sequenceDiagram
    participant User
    participant FileIngestion as File Ingestion Engine
    participant SchemaRegistry as Schema Registry
    participant Seman<PERSON><PERSON><PERSON><PERSON> as Semantic Mapping Engine
    participant OntologyLayer as Ontology Layer
    participant DriftDetection as Drift Detection Service
    participant UILayer as User Interface Layer
    participant TransformEngine as Data Transformation Engine
    participant DictionaryService as Dictionary Generation Service

    Note over User, DictionaryService: Phase 1: File Ingestion & Initial Processing
    
    User->>FileIngestion: Upload RAW CSV file(s)
    FileIngestion->>FileIngestion: Extract sample rows
    FileIngestion->>FileIngestion: Analyze filename patterns
    FileIngestion->>DictionaryService: Generate initial schema information
    DictionaryService-->>FileIngestion: Schema metadata
    
    Note over User, DictionaryService: Phase 2: Schema Management & Registration
    
    FileIngestion->>SchemaRegistry: Check if file is registered
    alt File Not Registered
        SchemaRegistry->>SchemaRegistry: Store new schema in registry
        SchemaRegistry->>DictionaryService: Generate table-level dictionary
        DictionaryService-->>SchemaRegistry: Dictionary metadata
    else File Already Registered
        SchemaRegistry->>DriftDetection: Compare for schema drift
        alt Drift Detected
            DriftDetection->>SchemaRegistry: Store new version of schema
            SchemaRegistry-->>DriftDetection: Version stored
        else No Drift
            DriftDetection->>TransformEngine: Get mapping details from database
            TransformEngine-->>DriftDetection: Existing mappings
        end
    end

    Note over User, DictionaryService: Phase 3: Intelligent Mapping & Semantic Analysis
    
    SchemaRegistry->>DictionaryService: Generate text embeddings for dictionary
    DictionaryService->>SemanticEngine: Provide embeddings and metadata
    SemanticEngine->>OntologyLayer: Perform intelligent semantic mapping
    OntologyLayer-->>SemanticEngine: Target ontology mappings
    SemanticEngine->>SemanticEngine: Generate mapping results between source and target
    SemanticEngine->>UILayer: Display mapping results
    
    Note over User, DictionaryService: Phase 4: User Review & Iterative Refinement
    
    UILayer->>User: Present mapping results for review
    User->>UILayer: Capture user input/modifications
    
    loop User Refinement Loop
        alt User wants to modify results
            UILayer->>User: Show modification interface
            User->>UILayer: Submit modifications
            UILayer->>SemanticEngine: Validate mapping compatibility
            SemanticEngine->>SchemaRegistry: Check against existing mappings
            alt Mapping Compatible
                SchemaRegistry-->>SemanticEngine: Validation passed
                SemanticEngine->>UILayer: Update mapping results
                UILayer->>User: Display updated results
            else Mapping Incompatible
                SchemaRegistry-->>SemanticEngine: Validation failed
                SemanticEngine->>UILayer: Alert user of conflicts
                UILayer->>User: Show conflict resolution options
            end
        else User accepts current results
            UILayer->>DictionaryService: Generate column-level dictionary
            DictionaryService-->>UILayer: Final dictionary generated
        end
    end

    Note over User, DictionaryService: Phase 5: Finalization & Data Transformation
    
    UILayer->>SchemaRegistry: Store data dictionary for RAW file
    UILayer->>SchemaRegistry: Store source to target mapping details
    SchemaRegistry->>TransformEngine: Execute data transformation
    TransformEngine->>TransformEngine: Perform data transformation
    TransformEngine-->>User: Transformation completed
    
    Note over User, DictionaryService: Optional: Mapping Update Process
    
    opt Mapping needs update
        TransformEngine->>UILayer: Get user input for updates
        UILayer->>User: Request mapping updates
        User->>UILayer: Provide updated mappings
        alt Mapping updated successfully
            UILayer->>SchemaRegistry: Update mapping in registry
            SchemaRegistry-->>UILayer: Update confirmed
        else Update failed
            UILayer->>UILayer: Stop further processing
            UILayer->>User: Alert update failure
        end
    end