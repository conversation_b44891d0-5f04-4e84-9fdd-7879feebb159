graph LR
    subgraph "File Processing Layer"
        FIE[File Ingestion Engine]
        SPE[Sample Processing Engine]
        FPA[Filename Pattern Analyzer]
    end
    
    subgraph "Schema Management Layer"
        SR[Schema Registry]
        SDD[Schema Drift Detector]
        VCS[Version Control Service]
        SMC[Schema Metadata Cache]
    end
    
    subgraph "Semantic Processing Layer"
        TES[Text Embedding Service]
        SME[Semantic Mapping Engine]
        OL[Ontology Layer]
        MRG[Mapping Results Generator]
    end
    
    subgraph "User Interface Layer"
        MUI[Mapping UI Interface]
        UIC[User Input Collector]
        CRH[Conflict Resolution Handler]
        PRV[Progress & Results Viewer]
    end
    
    subgraph "Validation & Quality Layer"
        MVS[Mapping Validation Service]
        QAS[Quality Assessment Service]
        CCS[Consistency Checker Service]
        BRV[Business Rules Validator]
    end
    
    subgraph "Data Management Layer"
        DGS[Dictionary Generation Service]
        MPS[Mapping Persistence Service]
        ALS[Audit Logging Service]
        MDS[Metadata Storage Service]
    end
    
    subgraph "Transformation Layer"
        DTE[Data Transformation Engine]
        ETL[ETL Pipeline Manager]
        QVS[Quality Validation Service]
        OPG[Output Generator]
    end
    
    %% File Processing Interactions
    FIE --> SPE
    FIE --> FPA
    SPE --> SR
    FPA --> SR
    
    %% Schema Management Interactions
    SR --> SDD
    SR --> VCS
    SR --> SMC
    SDD --> VCS
    
    %% Semantic Processing Interactions
    SMC --> TES
    TES --> SME
    SME --> OL
    SME --> MRG
    OL --> MRG
    
    %% UI Interactions
    MRG --> MUI
    MUI --> UIC
    UIC --> CRH
    MUI --> PRV
    
    %% Validation Interactions
    UIC --> MVS
    MVS --> QAS
    MVS --> CCS
    MVS --> BRV
    CCS --> SR
    BRV --> OL
    
    %% Data Management Interactions
    CRH --> DGS
    DGS --> MDS
    MPS --> MDS
    MVS --> ALS
    UIC --> ALS
    
    %% Transformation Interactions
    MPS --> DTE
    DTE --> ETL
    ETL --> QVS
    QVS --> OPG
    
    %% Cross-layer Interactions
    QAS --> DGS
    CRH --> MPS
    MVS --> MRG
    PRV --> ALS
    
    %% Feedback Loops
    CRH --> MVS
    QVS --> MVS
    OPG --> ALS
    
    %% Data Flow Labels
    FIE -.->|Raw Files| SPE
    SPE -.->|Sample Data| SR
    SR -.->|Schema Info| TES
    TES -.->|Embeddings| SME
    SME -.->|Mappings| MUI
    UIC -.->|User Feedback| MVS
    MVS -.->|Validation Results| CRH
    DGS -.->|Dictionaries| MPS
    MPS -.->|Final Mappings| DTE
    DTE -.->|Transformed Data| OPG
    
    %% Component Styling
    classDef fileLayer fill:#e8eaf6
    classDef schemaLayer fill:#e0f2f1
    classDef semanticLayer fill:#fff3e0
    classDef uiLayer fill:#fce4ec
    classDef validationLayer fill:#f3e5f5
    classDef dataLayer fill:#e1f5fe
    classDef transformLayer fill:#f1f8e9
    
    class FIE,SPE,FPA fileLayer
    class SR,SDD,VCS,SMC schemaLayer
    class TES,SME,OL,MRG semanticLayer
    class MUI,UIC,CRH,PRV uiLayer
    class MVS,QAS,CCS,BRV validationLayer
    class DGS,MPS,ALS,MDS dataLayer
    class DTE,ETL,QVS,OPG transformLayer