# CPG Agentic File Classification System

**AI-Powered Multi-Agent System for CPG Domain File Classification**

An advanced agentic framework that uses multiple specialized AI agents, voting mechanisms, and Azure OpenAI integration to automatically classify incoming raw files in the Consumer Packaged Goods (CPG) domain.

## Key Features

- **Multi-Agent Architecture**: Specialized agents for schema analysis, content analysis, pattern recognition, domain expertise, and ML classification
- **Voting System**: Intelligent consensus-building with multiple voting strategies
- **Azure OpenAI Integration**: Enhanced reasoning and insights using GPT models
- **CPG Domain Expertise**: Built-in knowledge of syndicated data, POS, product attributes, and more
- **Learning Capabilities**: Continuous improvement through user feedback
- **RAG Implementation**: Vector database integration with ChromaDB
- **Real-time Processing**: Async architecture for high-performance classification

## Supported CPG Data Types

| Data Type | Description | Use Cases |
|-----------|-------------|-----------|
| **Syndicated** | Nielsen/IRI market research data | Market share analysis, brand performance |
| **POS** | Point-of-sale transaction data | Sales analytics, customer behavior |
| **Product Attribute** | Product master data and hierarchies | Product management, category analysis |
| **Depletion Data** | Distributor shipment information | Supply chain optimization |
| **Margin Data** | Cost and pricing analysis | Revenue growth management (RGM) |
| **Numerator Intel** | Promotional intelligence | Marketing effectiveness |
| **Trace Data** | Supply chain traceability | Quality control, recalls |
| **Product Mapping** | Product hierarchy mappings | Data integration |
| **Geography Mapping** | Territory and market definitions | Market analysis |
| **PVP Mapping** | Price-Volume-Pack configurations | Pricing strategy |
| **National Accounts** | Key customer analytics | Account management |
| **POS Fact** | Aggregated transaction facts | Business intelligence |
| **Dimension Data** | Reference data for analytics | Data warehouse |

## Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    Supervisor Agent                         │
│  ┌─────────────────────────────────────────────────────┐    │
│  │              Voting Coordinator                     │    │
│  └─────────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────┬─────────────┬─────────────┬─────────────┬─────────────┐
│   Schema    │  Content    │  Pattern    │   Domain    │     ML      │
│  Analyzer   │  Analyzer   │ Recognizer  │   Expert    │ Classifier  │
│   Agent     │   Agent     │   Agent     │   Agent     │   Agent     │
└─────────────┴─────────────┴─────────────┴─────────────┴─────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                 Knowledge Base Layer                        │
│  ┌─────────────┬─────────────┬─────────────────────────┐    │
│  │  Vector DB  │  Learning   │     Azure OpenAI        │    │
│  │ (ChromaDB)  │   Engine    │    (GPT Integration)    │    │
│  └─────────────┴─────────────┴─────────────────────────┘    │
└─────────────────────────────────────────────────────────────┘
```

##  Quick Start


### 1. Configuration

Edit `config/config.json` with your Azure OpenAI credentials:

```json
{
  "azure_openai": {
    "api_key": "your_azure_openai_api_key",
    "endpoint": "https://your-resource.openai.azure.com/",
    "deployment": "your_deployment_name",
    "api_version": "2024-02-15-preview"
  }
}
```

### 2. Run Interactive CLI

```bash
python agents/agentic_cli.py
```

### 3. Classify Your First File

```bash
agentic> classify data/sample.csv
```

## Usage Examples

### Interactive CLI

```bash
# Start interactive mode
python agents/agentic_cli.py

# Available commands:
agentic> classify data/nielsen_data.csv    # Classify single file
agentic> batch data/incoming/              # Batch process directory
agentic> upload training.csv syndicated    # Add training example
agentic> stats                             # Show system statistics
agentic> history 10                        # Show recent classifications
agentic> help                              # Show all commands
```

### Programmatic Usage

```python
import asyncio
from agents.agentic_system import AgenticClassificationSystem, ClassificationRequest

async def classify_file():
    # Initialize system
    system = AgenticClassificationSystem()
    
    # Create classification request
    request = ClassificationRequest(
        file_path="data/sample.csv",
        user_context={
            "source": "api_call",
            "business_unit": "rgm_analytics"
        },
        processing_options={
            "include_ai_enhancement": True,
            "detailed_reasoning": True
        }
    )
    
    # Classify file
    response = await system.classify_file(request)
    
    # Process results
    print(f"Classification: {response.classification}")
    print(f"Confidence: {response.confidence:.2%}")
    print(f"Reasoning: {response.reasoning}")
    
    # Show agent details
    agent_details = response.agent_details
    print(f"Consensus: {agent_details['consensus_level']:.2%}")
    print(f"Agents: {len(agent_details['participating_agents'])}")
    
    # Shutdown
    system.shutdown()

# Run classification
asyncio.run(classify_file())
```

### Batch Processing

```python
import asyncio
from pathlib import Path
from agents.agentic_system import AgenticClassificationSystem, ClassificationRequest

async def batch_classify():
    system = AgenticClassificationSystem()
    
    # Find all CSV files
    files = list(Path("data/incoming").glob("*.csv"))
    
    results = []
    for file_path in files:
        request = ClassificationRequest(file_path=str(file_path))
        response = await system.classify_file(request)
        
        results.append({
            "file": file_path.name,
            "classification": response.classification,
            "confidence": response.confidence,
            "success": response.success
        })
    
    # Summary
    successful = sum(1 for r in results if r["success"])
    print(f"Processed {len(results)} files, {successful} successful")
    
    system.shutdown()

asyncio.run(batch_classify())
```

## 🔧 System Components

### Agents

#### 1. **Schema Analyzer Agent**
- Analyzes file structure and column patterns
- Detects data types and relationships
- Identifies key columns and hierarchies

#### 2. **Content Analyzer Agent**
- Processes textual content and business terms
- Extracts keywords and domain-specific indicators
- Performs text-based pattern matching

#### 3. **Pattern Recognizer Agent**
- Recognizes complex data patterns and relationships
- Detects temporal patterns and business processes
- Analyzes structural patterns (fact/dimension tables)

#### 4. **Domain Expert Agent**
- Applies CPG domain knowledge and business rules
- Understands industry-specific patterns
- Provides contextual business insights

#### 5. **ML Classifier Agent**
- Uses machine learning algorithms for classification
- Implements ensemble methods and neural networks
- Provides confidence scoring and model predictions

#### 6. **Voting Coordinator Agent**
- Orchestrates decision-making across agents
- Implements multiple voting strategies
- Builds consensus and resolves conflicts

#### 7. **Feedback Collector Agent**
- Collects and processes user feedback
- Learns from classification corrections
- Improves system performance over time

### Voting Strategies

1. **Weighted Average**: Combines agent decisions with performance-based weights
2. **Confidence Weighted**: Prioritizes high-confidence decisions
3. **Expert Priority**: Gives precedence to domain expert opinions
4. **Ensemble Learning**: Uses ML techniques for decision aggregation

### Azure OpenAI Integration

- **Content Analysis**: Enhanced file content understanding
- **Reasoning Enhancement**: Improved classification explanations
- **Quality Assessment**: Data quality evaluation
- **Business Context**: CPG domain-specific insights

## 📈 Performance Monitoring

### System Statistics

```bash
agentic> stats
```

Shows:
- Classification success rate
- Average confidence scores
- Processing times
- Agent performance metrics
- Knowledge base statistics

### Learning Analytics

- User feedback collection
- Classification correction patterns
- Model accuracy trends
- Continuous improvement metrics

## 🎛️ Configuration Options

### Agentic System Settings

```json
{
  "agentic_system": {
    "min_consensus_threshold": 0.4,     // Minimum agent agreement
    "min_confidence_threshold": 0.3,    // Minimum classification confidence
    "min_participating_agents": 2,      // Minimum agents required
    "max_processing_time": 60.0,        // Timeout in seconds
    "enable_azure_openai": true,        // Enable AI enhancement
    "enable_learning": true,             // Enable feedback learning
    "parallel_processing": true          // Enable parallel agent processing
  }
}
```

### Agent Weights

```json
{
  "agent_weights": {
    "schema_analyzer": 0.25,     // Structure analysis weight
    "content_analyzer": 0.20,    // Content analysis weight
    "pattern_recognizer": 0.20,  // Pattern recognition weight
    "domain_expert": 0.35,       // Domain expertise weight
    "ml_classifier": 0.30        // ML classification weight
  }
}
```

### Quality Thresholds

```json
{
  "quality_thresholds": {
    "high_quality": 0.8,     // High confidence threshold
    "medium_quality": 0.6,   // Medium confidence threshold
    "low_quality": 0.4       // Low confidence threshold
  }
}
```

## 🔄 Training and Learning

### Adding Training Examples

```bash
# Upload training file with correct classification
agentic> upload data/training/nielsen.csv syndicated

# Provide feedback on classification results
agentic> classify data/test.csv
# System will prompt for feedback after showing results
```

### Programmatic Training

```python
# Add training example
result = await system.add_training_example(
    file_path="data/training/pos_data.csv",
    correct_classification="pos",
    description="Sample POS transaction data from retail stores"
)

# Collect feedback on classification
feedback_data = {
    "correct_classification": "syndicated",
    "reasoning": "Contains Nielsen TDP and ACV metrics",
    "satisfaction": 4.0
}

await feedback_agent.collect_user_feedback(
    classification_result, feedback_data
)
```

