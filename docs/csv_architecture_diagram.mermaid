graph TB
    subgraph "User Interface Layer"
        UI[Interactive Mapping Interface]
        Dashboard[Monitoring Dashboard]
        API[REST API Gateway]
    end

    subgraph "Core Processing Layer"
        FileIngestion[File Ingestion Engine]
        SemanticEngine[Semantic Mapping Engine]
        TransformEngine[Data Transformation Engine]
        ValidationEngine[Mapping Validation Service]
    end

    subgraph "Intelligence Layer"
        MLModels[ML/NLP Models]
        EmbeddingService[Text Embedding Service]
        OntologyLayer[Target Ontology Layer]
        DriftDetection[Schema Drift Detection]
    end

    subgraph "Data Management Layer"
        SchemaRegistry[(Schema Registry)]
        MappingRepo[(Mapping Repository)]
        MetadataStore[(Metadata Store)]
        AuditLog[(Audit Log)]
    end

    subgraph "Supporting Services"
        DictionaryService[Dictionary Generation Service]
        VersionControl[Version Control Service]
        QualityService[Data Quality Service]
        SecurityService[Security & Access Control]
    end

    subgraph "External Systems"
        SourceSystems[Source Data Systems]
        TargetSystems[Target Data Systems]
        CloudStorage[Cloud Storage]
        EnterpriseSystems[Enterprise Systems]
    end

    %% User interactions
    UI --> FileIngestion
    UI --> SemanticEngine
    UI --> ValidationEngine
    Dashboard --> AuditLog
    API --> FileIngestion

    %% Core processing flows
    FileIngestion --> SchemaRegistry
    FileIngestion --> DictionaryService
    SemanticEngine --> OntologyLayer
    SemanticEngine --> EmbeddingService
    SemanticEngine --> MappingRepo
    TransformEngine --> TargetSystems
    ValidationEngine --> SchemaRegistry
    ValidationEngine --> MappingRepo

    %% Intelligence layer connections
    MLModels --> SemanticEngine
    EmbeddingService --> MLModels
    DriftDetection --> SchemaRegistry
    OntologyLayer --> EnterpriseSystems

    %% Data management connections
    SchemaRegistry --> VersionControl
    MappingRepo --> VersionControl
    MetadataStore --> DictionaryService
    AuditLog --> SecurityService

    %% Supporting services
    DictionaryService --> MetadataStore
    QualityService --> TransformEngine
    SecurityService --> UI
    SecurityService --> API

    %% External connections
    SourceSystems --> FileIngestion
    CloudStorage --> FileIngestion
    EnterpriseSystems --> OntologyLayer

    %% Styling
    classDef ui fill:#e1f5fe
    classDef core fill:#f3e5f5
    classDef intelligence fill:#e8f5e8
    classDef data fill:#fff3e0
    classDef support fill:#fce4ec
    classDef external fill:#f1f8e9

    class UI,Dashboard,API ui
    class FileIngestion,SemanticEngine,TransformEngine,ValidationEngine core
    class MLModels,EmbeddingService,OntologyLayer,DriftDetection intelligence
    class SchemaRegistry,MappingRepo,MetadataStore,AuditLog data
    class DictionaryService,VersionControl,QualityService,SecurityService support
    class SourceSystems,TargetSystems,CloudStorage,EnterpriseSystems external