# CSV Input Handling Flow - Technical Documentation

## Overview

This document describes a comprehensive data processing workflow for intelligent CSV file handling, featuring automated schema detection, semantic mapping, drift detection, and iterative user refinement capabilities. The system employs machine learning techniques for semantic understanding and provides enterprise-level data governance features.

## System Architecture

### High-Level Architecture Overview

The CSV processing system is built on a microservices architecture with distinct layers for different functional responsibilities. The system consists of six primary architectural layers, each containing multiple specialized services:

**User Interface Layer:**
- **Interactive Mapping Interface**: Web-based UI for mapping review and modification
- **Monitoring Dashboard**: Real-time system health and processing metrics dashboard
- **REST API Gateway**: Centralized API access point with authentication and rate limiting

**Core Processing Layer:**
- **File Ingestion Engine**: Handles raw file processing and initial data extraction
- **Semantic Mapping Engine**: AI-powered component for intelligent data mapping
- **Data Transformation Engine**: Final processing and output generation
- **Mapping Validation Service**: Consistency and conflict resolution engine

**Intelligence Layer:**
- **ML/NLP Models**: Advanced semantic understanding and pattern recognition models
- **Text Embedding Service**: NLP-based semantic analysis using transformer models
- **Target Ontology Layer**: Enterprise schema and semantic understanding framework
- **Schema Drift Detection**: Schema evolution monitoring and management service

**Data Management Layer:**
- **Schema Registry**: Centralized repository for file schemas and metadata
- **Mapping Repository**: Storage for mapping configurations and version history
- **Metadata Store**: Comprehensive metadata and documentation storage
- **Audit Log**: Complete audit trail for compliance and troubleshooting

**Supporting Services Layer:**
- **Dictionary Generation Service**: Automated metadata and documentation generation
- **Version Control Service**: Schema and mapping version management
- **Data Quality Service**: Continuous data quality monitoring and validation
- **Security Service**: Authentication, authorization, and data protection

**External Systems Integration:**
- **Source Data Systems**: Integration with various data source systems
- **Target Data Systems**: Output delivery to downstream systems
- **Cloud Storage**: Scalable file storage and retrieval
- **Enterprise Systems**: Integration with existing enterprise infrastructure

### Service Interaction Patterns

The architecture follows event-driven patterns with synchronous APIs for real-time interactions and asynchronous messaging for background processing. Services communicate through:

- **REST APIs**: For synchronous request-response operations
- **Apache Kafka**: For event streaming and asynchronous processing
- **Database Connections**: Direct database access for data persistence
- **Redis Cache**: For shared session and temporary data storage

## Detailed Process Flow Analysis

### Complete Data Processing Workflow

The CSV processing system follows a five-phase workflow with multiple decision points and feedback loops:

**Phase 1: File Ingestion & Initial Processing**
1. **File Upload**: Users upload CSV files through the web interface or API
2. **Sample Row Extraction**: System extracts representative data samples for analysis
3. **Filename Pattern Analysis**: Automated extraction of semantic information from file names
4. **Schema Information Generation**: Initial data type inference and structure analysis

**Phase 2: Schema Management & Registration**
1. **Registration Check**: System queries the schema registry to determine if the file type exists
2. **New File Processing**: If unregistered, the system stores a new schema definition
3. **Existing File Processing**: If registered, the system performs drift detection analysis
4. **Version Management**: Schema versions are created when significant drift is detected

**Phase 3: Intelligent Mapping & Semantic Analysis**
1. **Dictionary Generation**: Creation of table-level and column-level metadata
2. **Text Embedding Generation**: NLP processing for semantic understanding
3. **Semantic Mapping**: AI-powered mapping between source and target schemas
4. **Mapping Results Generation**: Production of confidence-scored mapping suggestions

**Phase 4: User Review & Iterative Refinement**
1. **Mapping Presentation**: Display of proposed mappings to users for review
2. **User Input Capture**: Collection of user feedback and modifications
3. **Validation Processing**: Real-time validation of user changes
4. **Conflict Resolution**: Guided resolution of mapping conflicts and inconsistencies
5. **Iterative Refinement**: Multiple rounds of user feedback and system updates

**Phase 5: Finalization & Data Transformation**
1. **Final Dictionary Generation**: Incorporation of all user feedback into final documentation
2. **Mapping Persistence**: Storage of finalized mappings in enterprise repository
3. **Data Transformation**: Execution of ETL processes based on approved mappings
4. **Quality Validation**: Post-transformation data quality assessment

### Decision Points and Business Logic

**File Registration Decision**: The system uses a composite scoring algorithm combining filename patterns (30% weight), column signatures (40% weight), and data patterns (30% weight) to determine if a file is already registered.

**Schema Drift Detection**: Multi-dimensional analysis including structural changes, data type evolution, constraint modifications, and statistical distribution shifts. Configurable sensitivity levels from low (>20% change) to high (>5% change).

**Mapping Compatibility Validation**: Cross-reference checks against existing mappings, business rule compliance verification, and impact analysis on downstream systems.

**User Modification Handling**: Real-time validation of user changes with immediate feedback on conflicts, consistency issues, and business rule violations.

## Process Phases Detailed Analysis

### Phase 1: File Ingestion & Initial Processing

#### File Upload and Validation
The system accepts CSV files up to 10GB through multiple channels including web interface, REST API, and batch upload mechanisms. File validation includes:

- **Format Verification**: MIME type validation and file header analysis
- **Security Scanning**: Virus scanning and malicious content detection
- **Size Validation**: Configurable file size limits with appropriate error handling
- **Encoding Detection**: Automatic character encoding detection and normalization

#### Sample Data Extraction Strategy
The system employs sophisticated sampling techniques to ensure representative data analysis:

- **Header Analysis**: First 10 rows for column structure understanding
- **Statistical Sampling**: Random sampling across the entire file (default 1000 rows)
- **Stratified Sampling**: Ensures representation across different data patterns
- **Null Value Analysis**: Specific sampling to understand missing data patterns

#### Filename Pattern Recognition
Advanced pattern matching algorithms extract semantic information:

- **Date Pattern Extraction**: Recognition of date formats in filenames
- **Department/Source Identification**: Matching against organizational naming conventions
- **Version Indicators**: Detection of file version numbers and incremental identifiers
- **Content Type Classification**: Automatic categorization based on naming patterns

#### Schema Generation Process
Automated schema inference using multiple analytical techniques:

- **Data Type Detection**: Statistical analysis of column values for type inference
- **Constraint Discovery**: Identification of unique constraints, null patterns, and value ranges
- **Pattern Recognition**: Detection of common business patterns (emails, phone numbers, IDs)
- **Confidence Scoring**: Probabilistic confidence assessment for each schema element

### Phase 2: Schema Management & Registration

#### Schema Registry Architecture
The schema registry serves as the central repository for all file schema definitions:

- **Versioned Storage**: Semantic versioning (MAJOR.MINOR.PATCH) for schema evolution
- **Fingerprint-based Indexing**: SHA-256 hashing for rapid schema comparison
- **Metadata Management**: Comprehensive documentation and lineage tracking
- **Retention Policies**: Configurable retention for deprecated schema versions

#### Drift Detection Algorithms
Multi-dimensional schema comparison with configurable sensitivity:

**Structural Analysis:**
- Column additions, removals, and reordering detection
- Data type changes and precision modifications
- Constraint evolution (nullability, uniqueness, relationships)

**Statistical Analysis:**
- Mean and variance changes in numerical columns
- Categorical value distribution shifts
- Outlier pattern evolution and anomaly detection

**Threshold Configuration:**
- Low Sensitivity (20%+): Major structural changes only
- Medium Sensitivity (10-20%): Moderate changes requiring review
- High Sensitivity (5-10%): Minor changes with impact assessment
- Critical Sensitivity (<5%): All changes requiring manual approval

#### Version Management Strategy
Comprehensive version control with automated and manual triggers:

- **Automatic Versioning**: Triggered by drift detection thresholds
- **Manual Versioning**: User-initiated version creation for business reasons
- **Rollback Capabilities**: Complete rollback to previous schema versions
- **Migration Support**: Automated migration scripts for version upgrades

### Phase 3: Intelligent Mapping & Semantic Analysis

#### Dictionary Generation Framework
Automated documentation generation with business context:

**Table-Level Dictionary:**
- Purpose and business context documentation
- Source system identification and update frequency
- Data quality indicators and completeness metrics
- Regulatory and compliance classification

**Column-Level Dictionary:**
- Business meaning and domain-specific definitions
- Data type specifications with format examples
- Valid value ranges and constraint documentation
- Relationship mappings and foreign key references

#### Text Embedding Technology Stack
Advanced NLP processing for semantic understanding:

**Primary Models:**
- BERT-based transformers fine-tuned for business terminology
- GPT-based models for context-aware description generation
- Domain-specific word embeddings for industry terminology

**Embedding Process:**
- Column name tokenization with business context preservation
- Sample value analysis for pattern-based embedding
- Description generation using AI language models
- Semantic similarity computation using cosine distance

**Performance Optimization:**
- Pre-computed embedding cache for common business terms
- Batch processing for large-scale embedding generation
- Incremental updates to minimize recomputation overhead

#### Semantic Mapping Algorithm
Multi-modal similarity computation with confidence scoring:

**Similarity Metrics:**
1. **Lexical Similarity**: String matching with fuzzy logic and Levenshtein distance
2. **Semantic Similarity**: Cosine similarity between neural embeddings
3. **Structural Similarity**: Data type compatibility and constraint alignment
4. **Historical Pattern Matching**: Learning from previous successful mappings

**Confidence Classification:**
- **High Confidence (90-100%)**: Direct matches with strong alignment across all metrics
- **Medium Confidence (70-89%)**: Good matches requiring minimal validation
- **Low Confidence (50-69%)**: Possible matches needing careful review
- **No Match (<50%)**: Manual mapping required with expert intervention

**Business Rule Integration:**
- Mandatory mapping rules for regulatory compliance
- Prohibited mapping rules for data governance
- Conditional mapping rules based on business context

### Phase 4: User Review & Iterative Refinement

#### Interactive User Interface Design
Comprehensive mapping review environment with advanced visualization:

**Mapping Visualization:**
- Source-to-target field relationship diagrams
- Confidence-based color coding for immediate assessment
- Conflict highlighting with detailed explanation
- Progress tracking with completion metrics

**User Interaction Capabilities:**
- Accept/reject workflow for proposed mappings
- Custom field-to-field mapping modifications
- Transformation rule definition with validation
- Business rule application and custom logic

#### Iterative Refinement Process
Continuous improvement through user feedback integration:

**Real-time Validation:**
- Immediate consistency checking during user modifications
- Business rule validation with explanatory feedback
- Impact analysis on downstream systems and processes
- Conflict detection with guided resolution workflows

**Learning Integration:**
- Pattern recognition from user decision history
- Organizational learning across multiple users and projects
- Preference storage for user-specific mapping styles
- Continuous model improvement based on feedback patterns

#### Compatibility Validation Engine
Comprehensive validation framework ensuring data integrity:

**Cross-Reference Validation:**
- Consistency checks against existing source system mappings
- Duplicate mapping detection and resolution
- Circular dependency identification and prevention

**Business Rule Compliance:**
- Enterprise data governance policy enforcement
- Regulatory requirement validation (GDPR, SOX, industry-specific)
- Data classification and sensitivity level compliance

**Impact Analysis:**
- Downstream system compatibility assessment
- Performance impact estimation for large-scale transformations
- Data quality prediction based on mapping decisions

### Phase 5: Finalization & Data Transformation

#### Final Documentation Generation
Comprehensive documentation creation with audit trail:

- **Complete Mapping Documentation**: Source-to-target mappings with transformation logic
- **Data Lineage Tracking**: End-to-end data flow documentation
- **Quality Metrics**: Mapping confidence scores and validation results
- **Change History**: Complete audit trail of user decisions and system actions

#### Mapping Persistence Strategy
Enterprise-grade storage with version control:

- **Atomic Updates**: Transactional updates to prevent partial state issues
- **Version History**: Complete evolution tracking with rollback capabilities
- **Backup Strategy**: Automated backup with configurable retention policies
- **Access Control**: Role-based access to mapping configurations

#### Data Transformation Execution
High-performance ETL processing with quality assurance:

**Processing Engine:**
- Apache Spark-based distributed processing for scalability
- Streaming processing capabilities for real-time data flows
- Memory-optimized processing for large file handling

**Quality Validation:**
- Real-time data quality monitoring during transformation
- Configurable quality thresholds with automatic alerts
- Data profiling and statistical analysis of transformation results

**Error Handling:**
- Comprehensive error recovery with exponential backoff
- Dead letter queues for failed record handling
- Detailed error reporting with resolution guidance

## Technical Specifications

### Performance Requirements and SLAs

#### Throughput Specifications
- **File Processing**: 1M rows per minute for standard CSV transformations
- **Concurrent Users**: Support for 500+ simultaneous users with sub-second response
- **File Upload**: 100+ concurrent file uploads with intelligent queuing
- **Mapping Generation**: Real-time mapping suggestions within 5 seconds for files up to 1GB

#### Latency Requirements
- **User Interface**: 95th percentile response time under 500ms for all user interactions
- **API Endpoints**: Sub-second response for all synchronous API calls
- **Background Processing**: Progress updates every 5 seconds for long-running operations
- **Database Operations**: Query response time under 100ms for 99% of operations

#### Scalability Targets
- **Horizontal Scaling**: Linear scaling up to 100 processing nodes
- **Storage Scaling**: Petabyte-scale storage with distributed architecture
- **Memory Management**: Configurable memory pools with automatic optimization
- **Network Bandwidth**: 10Gbps+ network capacity for large file transfers

#### Availability and Reliability
- **System Uptime**: 99.9% availability SLA with planned maintenance windows
- **Disaster Recovery**: RTO of 4 hours, RPO of 1 hour for critical data
- **Fault Tolerance**: Automatic failover with zero data loss guarantee
- **Backup Strategy**: Continuous backup with point-in-time recovery capabilities

### Technology Stack Specifications

#### Backend Application Framework
**Primary Framework**: Spring Boot 3.x with Java 17/21
- Reactive programming support with WebFlux for high concurrency
- Microservices architecture with service discovery (Eureka/Consul)
- Configuration management with Spring Cloud Config
- Circuit breaker pattern implementation with Resilience4j

#### Database Technologies
**Primary Database**: PostgreSQL 15+
- JSONB support for flexible schema storage
- Full-text search capabilities with GIN indexing
- Table partitioning for large-scale data management
- Read replicas for load distribution

**Caching Layer**: Redis Cluster
- Distributed caching with automatic failover
- Session management with configurable TTL
- Pub/sub messaging for real-time updates
- Memory optimization with compression

**Search and Analytics**: Elasticsearch 8.x
- Full-text search across all metadata
- Real-time analytics and aggregations
- Log analysis and monitoring integration
- Machine learning features for anomaly detection

#### Message Queue and Event Streaming
**Primary Messaging**: Apache Kafka 3.x
- Event-driven architecture with topic-based routing
- Stream processing with Kafka Streams
- Schema registry for message format management
- Dead letter queue handling for failed messages

#### Machine Learning and AI Infrastructure
**ML Framework**: Python ecosystem with Flask/FastAPI
- scikit-learn for traditional machine learning algorithms
- spaCy and NLTK for natural language processing
- transformers library for BERT/GPT model integration
- PyTorch/TensorFlow for custom model development

**Model Serving**: TensorFlow Serving / TorchServe
- REST API endpoints for model inference
- Model versioning and A/B testing capabilities
- GPU acceleration for intensive computations
- Batch prediction support for large datasets

#### Container Orchestration and DevOps
**Container Platform**: Kubernetes 1.25+
- Helm charts for application deployment
- Horizontal Pod Autoscaler for dynamic scaling
- Ingress controllers for traffic management
- Service mesh with Istio for advanced networking

**CI/CD Pipeline**: GitLab CI / GitHub Actions
- Automated testing with unit, integration, and end-to-end tests
- Security scanning with SAST/DAST tools
- Container image scanning for vulnerabilities
- Blue-green deployment for zero-downtime releases

**Monitoring and Observability**: Prometheus + Grafana + Jaeger
- Metrics collection and alerting with Prometheus
- Visualization dashboards with Grafana
- Distributed tracing with Jaeger
- Log aggregation with ELK stack

### Security Implementation

#### Data Protection Framework
**Encryption Standards:**
- AES-256 encryption for data at rest
- TLS 1.3 for data in transit
- Field-level encryption for sensitive data
- Key management with HashiCorp Vault

**PII Detection and Protection:**
- Automated PII detection using regex patterns and ML models
- Data masking and tokenization for non-production environments
- Data classification with automatic sensitivity labeling
- GDPR compliance with right to be forgotten implementation

#### Authentication and Authorization
**Identity Management:**
- OAuth 2.0 / OpenID Connect integration with enterprise identity providers
- Multi-factor authentication support
- Single sign-on (SSO) with SAML 2.0
- JWT token management with configurable expiration

**Access Control:**
- Role-based access control (RBAC) with fine-grained permissions
- Attribute-based access control (ABAC) for complex scenarios
- API rate limiting with user-based quotas
- Resource-level access control with data governance integration

#### Compliance and Audit
**Regulatory Compliance:**
- GDPR compliance with data lineage tracking and consent management
- SOX compliance with immutable audit trails and segregation of duties
- HIPAA compliance for healthcare data processing
- Industry-specific compliance frameworks (PCI DSS, ISO 27001)

**Audit and Monitoring:**
- Comprehensive audit logging with tamper-proof storage
- Real-time security monitoring with SIEM integration
- Anomaly detection for suspicious activities
- Automated compliance reporting and alerting

### API Specifications and Integration

#### REST API Design Standards
All APIs follow RESTful design principles with consistent patterns:

**Authentication**: All endpoints require Bearer token authentication
**Rate Limiting**: 1000 requests per minute per user with burst allowance
**Versioning**: URL-based versioning (/api/v1/) with backward compatibility
**Error Handling**: Consistent error response format with detailed error codes

#### Core API Endpoints

**File Management APIs:**

```
POST /api/v1/files/upload
Content-Type: multipart/form-data
Authorization: Bearer {jwt_token}

Request Parameters:
- file: CSV file (required, max 10GB)
- metadata: JSON object with source, domain, description (optional)
- processing_options: JSON object with sampling_size, auto_process flags (optional)

Response:
{
  "file_id": "uuid",
  "status": "uploaded|processing|completed|failed",
  "processing_job_id": "uuid",
  "estimated_completion": "ISO8601 timestamp",
  "file_metadata": {
    "size": "bytes",
    "rows_estimated": "number",
    "columns_detected": "number"
  }
}

Error Responses:
400 Bad Request - Invalid file format or size
401 Unauthorized - Invalid or expired token
413 Payload Too Large - File exceeds size limit
500 Internal Server Error - Processing failure
```

```
GET /api/v1/files/{file_id}/status
Authorization: Bearer {jwt_token}

Response:
{
  "file_id": "uuid",
  "status": "processing|completed|failed",
  "progress": {
    "phase": "ingestion|schema_analysis|mapping|transformation",
    "percentage": "0-100",
    "current_step": "descriptive string",
    "estimated_time_remaining": "seconds"
  },
  "results": {
    "schema_id": "uuid",
    "mapping_id": "uuid",
    "transformation_results": {}
  },
  "errors": [
    {
      "code": "error_code",
      "message": "human readable message",
      "details": "technical details"
    }
  ]
}
```

**Schema Management APIs:**

```
POST /api/v1/schemas/register
Content-Type: application/json
Authorization: Bearer {jwt_token}

Request:
{
  "file_id": "uuid",
  "schema_definition": {
    "name": "schema name",
    "description": "schema description",
    "columns": [
      {
        "name": "column_name",
        "data_type": "string|number|date|boolean|text",
        "nullable": "boolean",
        "primary_key": "boolean",
        "unique": "boolean",
        "constraints": {
          "min_length": "number",
          "max_length": "number",
          "pattern": "regex string",
          "enum_values": ["array of valid values"],
          "min_value": "number",
          "max_value": "number"
        },
        "description": "column description",
        "business_rules": ["array of rule descriptions"]
      }
    ],
    "metadata": {
      "source_system": "string",
      "domain": "string",
      "sensitivity_level": "public|internal|confidential|restricted",
      "retention_policy": "string",
      "update_frequency": "string"
    }
  }
}

Response:
{
  "schema_id": "uuid",
  "version": "semantic version string",
  "fingerprint": "sha256 hash",
  "registration_timestamp": "ISO8601",
  "validation_results": {
    "is_valid": "boolean",
    "warnings": ["array of warning messages"],
    "recommendations": ["array of improvement suggestions"]
  }
}
```

```
POST /api/v1/schemas/{schema_id}/drift-check
Content-Type: application/json
Authorization: Bearer {jwt_token}

Request:
{
  "new_schema_definition": "schema object",
  "comparison_options": {
    "sensitivity_level": "low|medium|high|critical",
    "ignore_field_order": "boolean",
    "tolerance_threshold": "0.0-1.0",
    "include_statistical_analysis": "boolean"
  }
}

Response:
{
  "drift_detected": "boolean",
  "drift_severity": "none|low|medium|high|critical",
  "drift_score": "0.0-1.0",
  "changes": [
    {
      "change_type": "added|removed|modified|renamed",
      "field_name": "string",
      "old_value": "any",
      "new_value": "any",
      "impact_level": "low|medium|high",
      "description": "human readable description"
    }
  ],
  "statistical_analysis": {
    "distribution_changes": [],
    "outlier_pattern_changes": [],
    "correlation_changes": []
  },
  "recommendation": "accept|review|reject",
  "required_actions": ["array of recommended actions"]
}
```

**Semantic Mapping APIs:**

```
POST /api/v1/mappings/generate
Content-Type: application/json
Authorization: Bearer {jwt_token}

Request:
{
  "source_schema_id": "uuid",
  "target_ontology_id": "uuid",
  "mapping_options": {
    "confidence_threshold": "0.0-1.0",
    "semantic_similarity_weight": "0.0-1.0",
    "structural_similarity_weight": "0.0-1.0",
    "historical_pattern_weight": "0.0-1.0",
    "include_transformations": "boolean",
    "max_suggestions_per_field": "number"
  }
}

Response:
{
  "mapping_id": "uuid",
  "generation_timestamp": "ISO8601",
  "mappings": [
    {
      "source_field": "string",
      "target_field": "string",
      "confidence": "0.0-1.0",
      "mapping_type": "direct|transformation|calculated|conditional",
      "transformation_rule": {
        "type": "function|expression|lookup|custom",
        "rule": "string representation",
        "parameters": "key-value pairs"
      },
      "reasoning": "explanation of mapping logic",
      "alternative_suggestions": [
        {
          "target_field": "string",
          "confidence": "0.0-1.0",
          "reasoning": "string"
        }
      ]
    }
  ],
  "overall_confidence": "0.0-1.0",
  "conflicts_detected": [
    {
      "conflict_type": "duplicate_target|business_rule_violation|data_type_mismatch",
      "description": "human readable description",
      "affected_fields": ["array of field names"],
      "suggested_resolution": "string"
    }
  ],
  "coverage_analysis": {
    "mapped_fields": "number",
    "unmapped_fields": "number",
    "coverage_percentage": "0.0-100.0"
  }
}
```

#### Error Handling and Response Codes

**Standard HTTP Status Codes:**
- 200 OK: Successful operation
- 201 Created: Resource created successfully
- 400 Bad Request: Invalid request parameters or data
- 401 Unauthorized: Authentication required or failed
- 403 Forbidden: Insufficient permissions
- 404 Not Found: Resource not found
- 409 Conflict: Resource conflict (duplicate, constraint violation)
- 413 Payload Too Large: Request entity too large
- 422 Unprocessable Entity: Valid request but processing failed
- 429 Too Many Requests: Rate limit exceeded
- 500 Internal Server Error: Server processing error
- 503 Service Unavailable: Service temporarily unavailable

**Error Response Format:**
```json
{
  "error": {
    "code": "SPECIFIC_ERROR_CODE",
    "message": "Human readable error message",
    "details": "Technical details for debugging",
    "timestamp": "ISO8601 timestamp",
    "request_id": "uuid for tracing",
    "suggested_actions": [
      "Array of suggested resolution steps"
    ]
  }
}
```

### Data Models and Schema Definitions

#### Schema Definition Data Model
```json
{
  "schema_id": "uuid",
  "version": "semantic version (e.g., 1.2.3)",
  "name": "human readable schema name",
  "description": "detailed schema description",
  "fingerprint": "sha256 hash for quick comparison",
  "created_at": "ISO8601 timestamp",
  "updated_at": "ISO8601 timestamp",
  "created_by": "user identifier",
  "status": "draft|active|deprecated",
  "columns": [
    {
      "name": "column name",
      "data_type": "string|number|date|boolean|text|json|binary",
      "nullable": "boolean",
      "primary_key": "boolean",
      "unique": "boolean",
      "indexed": "boolean",
      "constraints": {
        "min_length": "minimum string length",
        "max_length": "maximum string length",
        "pattern": "regex pattern for validation",
        "enum_values": ["array", "of", "valid", "values"],
        "min_value": "minimum numeric value",
        "max_value": "maximum numeric value",
        "precision": "decimal precision",
        "scale": "decimal scale"
      },
      "description": "business description of column",
      "business_rules": [
        "Array of business rule descriptions"
      ],
      "semantic_tags": [
        "Array of semantic classification tags"
      ],
      "data_quality_rules": [
        {
          "rule_type": "completeness|accuracy|consistency|validity",
          "rule_definition": "rule specification",
          "threshold": "quality threshold"
        }
      ],
      "business_glossary_id": "uuid reference to business glossary",
      "sample_values": [
        "Array of representative sample values"
      ]
    }
  ],
  "relationships": [
    {
      "type": "foreign_key|reference|dependency",
      "source_column": "column name",
      "target_schema_id": "uuid",
      "target_column": "column name",
      "description": "relationship description"
    }
  ],
  "metadata": {
    "source_system": "originating system identifier",
    "domain": "business domain classification",
    "sensitivity_level": "public|internal|confidential|restricted",
    "retention_policy": "data retention requirements",
    "update_frequency": "how often data is updated",
    "owner": "data owner identifier",
    "steward": "data steward identifier",
    "compliance_tags": [
      "Array of compliance framework tags"
    ]
  },
  "quality_metrics": {
    "completeness_score": "0.0-1.0",
    "accuracy_score": "0.0-1.0",
    "consistency_score": "0.0-1.0",
    "validity_score": "0.0-1.0"
  }
}
```

#### Mapping Configuration Data Model
```json
{
  "mapping_id": "uuid",
  "source_schema_id": "uuid",
  "target_schema_id": "uuid",
  "version": "semantic version",
  "status": "draft|review|approved|active|deprecated",
  "created_by": "user identifier",
  "created_at": "ISO8601 timestamp",
  "updated_at": "ISO8601 timestamp",
  "approved_by": "approver user identifier",
  "approved_at": "ISO8601 timestamp",
  "mappings": [
    {
      "source_field": "source column name",
      "target_field": "target column name",
      "mapping_type": "direct|transformation|calculated|conditional|composite",
      "transformation_rule": {
        "type": "function|expression|lookup|custom|ml_model",
        "rule": "transformation logic representation",
        "parameters": {
          "key": "value pairs for parameterization"
        },
        "validation_rules": [
          "Array of validation rule specifications"
        ]
      },
      "confidence": "0.0-1.0 confidence score",
      "user_verified": "boolean indicating manual verification",
      "business_justification": "business reason for mapping",
      "data_quality_impact": {
        "expected_accuracy": "0.0-1.0",
        "data_loss_risk": "low|medium|high",
        "transformation_complexity": "simple|moderate|complex"
      },
      "performance_impact": {
        "processing_overhead": "low|medium|high",
        "memory_requirements": "bytes",
        "estimated_duration": "seconds"
      }
    }
  ],
  "business_rules": [
    {
      "rule_id": "uuid",
      "rule_name": "human readable rule name",
      "condition": "rule condition specification",
      "action": "action to take when rule triggers",
      "priority": "1-10 priority level",
      "enabled": "boolean"
    }
  ],
  "quality_rules": [
    {
      "rule_id": "uuid",
      "rule_type": "completeness|accuracy|consistency|validity",
      "rule_definition": "quality rule specification",
      "threshold": "quality threshold",
      "action_on_failure": "warn|reject|transform"
    }
  ],
  "validation_results": {
    "is_valid": "boolean",
    "validation_errors": [
      "Array of validation error messages"
    ],
    "warnings": [
      "Array of warning messages"
    ],
    "test_results": [
      {
        "test_name": "test identifier",
        "status": "passed|failed|warning",
        "details": "test execution details"
      }
    ]
  },
  "metadata": {
    "purpose": "mapping purpose description",
    "impact_analysis": "impact assessment",
    "rollback_plan": "rollback procedure",
    "monitoring_requirements": "monitoring specifications"
  }
}
```

## DuckDB Schema Registry Database Design

### Overview

The schema registry utilizes DuckDB as the primary analytical database for storing and querying schema definitions, mappings, and metadata. DuckDB provides excellent performance for analytical workloads and complex queries required for schema comparison and drift detection.

### Core Tables Schema

#### 1. Schema Definitions Table

```sql
CREATE TABLE schema_definitions (
    schema_id UUID PRIMARY KEY,
    schema_name VARCHAR(255) NOT NULL,
    schema_description TEXT,
    version VARCHAR(50) NOT NULL,
    fingerprint VARCHAR(64) NOT NULL, -- SHA-256 hash
    status VARCHAR(20) NOT NULL CHECK (status IN ('draft', 'active', 'deprecated')),
    source_system VARCHAR(100),
    domain VARCHAR(100),
    sensitivity_level VARCHAR(20) CHECK (sensitivity_level IN ('public', 'internal', 'confidential', 'restricted')),
    retention_policy VARCHAR(255),
    update_frequency VARCHAR(100),
    owner_id VARCHAR(100),
    steward_id VARCHAR(100),
    created_by VARCHAR(100) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    approved_by VARCHAR(100),
    approved_at TIMESTAMP,
    -- Quality metrics stored as JSON for flexibility
    quality_metrics JSON,
    -- Compliance tags as array
    compliance_tags VARCHAR[],
    -- Additional metadata as JSON
    metadata JSON
);

-- Create indexes for performance
CREATE INDEX idx_schema_fingerprint ON schema_definitions(fingerprint);
CREATE INDEX idx_schema_status ON schema_definitions(status);
CREATE INDEX idx_schema_domain ON schema_definitions(domain);
CREATE INDEX idx_schema_created_at ON schema_definitions(created_at);
CREATE INDEX idx_schema_version ON schema_definitions(schema_name, version);
```

#### 2. Column Definitions Table

```sql
CREATE TABLE column_definitions (
    column_id UUID PRIMARY KEY,
    schema_id UUID NOT NULL,
    column_name VARCHAR(255) NOT NULL,
    ordinal_position INTEGER NOT NULL,
    data_type VARCHAR(50) NOT NULL,
    is_nullable BOOLEAN NOT NULL DEFAULT true,
    is_primary_key BOOLEAN NOT NULL DEFAULT false,
    is_unique BOOLEAN NOT NULL DEFAULT false,
    is_indexed BOOLEAN NOT NULL DEFAULT false,
    column_description TEXT,
    business_rules TEXT[],
    semantic_tags VARCHAR[],
    sample_values JSON, -- Array of sample values
    -- Constraints stored as JSON for flexibility
    constraints JSON,
    -- Data quality rules as JSON array
    data_quality_rules JSON,
    business_glossary_id UUID,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (schema_id) REFERENCES schema_definitions(schema_id) ON DELETE CASCADE
);

-- Create indexes
CREATE INDEX idx_column_schema_id ON column_definitions(schema_id);
CREATE INDEX idx_column_name ON column_definitions(column_name);
CREATE INDEX idx_column_data_type ON column_definitions(data_type);
CREATE INDEX idx_column_ordinal ON column_definitions(schema_id, ordinal_position);
```

#### 3. Schema Relationships Table

```sql
CREATE TABLE schema_relationships (
    relationship_id UUID PRIMARY KEY,
    source_schema_id UUID NOT NULL,
    source_column VARCHAR(255) NOT NULL,
    target_schema_id UUID NOT NULL,
    target_column VARCHAR(255) NOT NULL,
    relationship_type VARCHAR(50) NOT NULL CHECK (relationship_type IN ('foreign_key', 'reference', 'dependency', 'composition')),
    relationship_description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100) NOT NULL,
    
    FOREIGN KEY (source_schema_id) REFERENCES schema_definitions(schema_id),
    FOREIGN KEY (target_schema_id) REFERENCES schema_definitions(schema_id)
);

-- Create indexes
CREATE INDEX idx_relationship_source ON schema_relationships(source_schema_id);
CREATE INDEX idx_relationship_target ON schema_relationships(target_schema_id);
CREATE INDEX idx_relationship_type ON schema_relationships(relationship_type);
```

#### 4. Schema Versions Table

```sql
CREATE TABLE schema_versions (
    version_id UUID PRIMARY KEY,
    schema_id UUID NOT NULL,
    version_number VARCHAR(50) NOT NULL,
    previous_version_id UUID,
    change_type VARCHAR(50) NOT NULL CHECK (change_type IN ('major', 'minor', 'patch', 'hotfix')),
    change_summary TEXT NOT NULL,
    change_details JSON, -- Detailed change information
    drift_score DECIMAL(5,4), -- 0.0000 to 1.0000
    compatibility_level VARCHAR(20) CHECK (compatibility_level IN ('backward', 'forward', 'full', 'breaking')),
    migration_required BOOLEAN NOT NULL DEFAULT false,
    migration_script TEXT,
    rollback_script TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100) NOT NULL,
    approved_at TIMESTAMP,
    approved_by VARCHAR(100),
    
    FOREIGN KEY (schema_id) REFERENCES schema_definitions(schema_id),
    FOREIGN KEY (previous_version_id) REFERENCES schema_versions(version_id)
);

-- Create indexes
CREATE INDEX idx_version_schema_id ON schema_versions(schema_id);
CREATE INDEX idx_version_number ON schema_versions(schema_id, version_number);
CREATE INDEX idx_version_created_at ON schema_versions(created_at);
```

#### 5. Mapping Configurations Table

```sql
CREATE TABLE mapping_configurations (
    mapping_id UUID PRIMARY KEY,
    mapping_name VARCHAR(255) NOT NULL,
    source_schema_id UUID NOT NULL,
    target_schema_id UUID NOT NULL,
    version VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('draft', 'review', 'approved', 'active', 'deprecated')),
    purpose TEXT,
    impact_analysis TEXT,
    rollback_plan TEXT,
    monitoring_requirements TEXT,
    created_by VARCHAR(100) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    approved_by VARCHAR(100),
    approved_at TIMESTAMP,
    -- Overall mapping confidence score
    overall_confidence DECIMAL(3,2) CHECK (overall_confidence >= 0 AND overall_confidence <= 1),
    -- Business rules as JSON array
    business_rules JSON,
    -- Quality rules as JSON array
    quality_rules JSON,
    -- Validation results as JSON
    validation_results JSON,
    
    FOREIGN KEY (source_schema_id) REFERENCES schema_definitions(schema_id),
    FOREIGN KEY (target_schema_id) REFERENCES schema_definitions(schema_id)
);

-- Create indexes
CREATE INDEX idx_mapping_source_schema ON mapping_configurations(source_schema_id);
CREATE INDEX idx_mapping_target_schema ON mapping_configurations(target_schema_id);
CREATE INDEX idx_mapping_status ON mapping_configurations(status);
CREATE INDEX idx_mapping_created_at ON mapping_configurations(created_at);
```

#### 6. Field Mappings Table

```sql
CREATE TABLE field_mappings (
    mapping_detail_id UUID PRIMARY KEY,
    mapping_id UUID NOT NULL,
    source_field VARCHAR(255) NOT NULL,
    target_field VARCHAR(255) NOT NULL,
    mapping_type VARCHAR(50) NOT NULL CHECK (mapping_type IN ('direct', 'transformation', 'calculated', 'conditional', 'composite')),
    confidence DECIMAL(3,2) NOT NULL CHECK (confidence >= 0 AND confidence <= 1),
    is_user_verified BOOLEAN NOT NULL DEFAULT false,
    business_justification TEXT,
    -- Transformation rule stored as JSON
    transformation_rule JSON,
    -- Data quality impact assessment
    data_quality_impact JSON,
    -- Performance impact metrics
    performance_impact JSON,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    verified_by VARCHAR(100),
    verified_at TIMESTAMP,
    
    FOREIGN KEY (mapping_id) REFERENCES mapping_configurations(mapping_id) ON DELETE CASCADE
);

-- Create indexes
CREATE INDEX idx_field_mapping_id ON field_mappings(mapping_id);
CREATE INDEX idx_field_source ON field_mappings(source_field);
CREATE INDEX idx_field_target ON field_mappings(target_field);
CREATE INDEX idx_field_confidence ON field_mappings(confidence);
CREATE INDEX idx_field_mapping_type ON field_mappings(mapping_type);
```

#### 7. Schema Drift Detection Log

```sql
CREATE TABLE schema_drift_log (
    drift_log_id UUID PRIMARY KEY,
    schema_id UUID NOT NULL,
    comparison_schema_id UUID NOT NULL,
    drift_detected BOOLEAN NOT NULL,
    drift_severity VARCHAR(20) CHECK (drift_severity IN ('none', 'low', 'medium', 'high', 'critical')),
    drift_score DECIMAL(5,4) NOT NULL CHECK (drift_score >= 0 AND drift_score <= 1),
    -- Changes detected stored as JSON array
    changes_detected JSON,
    -- Statistical analysis results
    statistical_analysis JSON,
    recommendation VARCHAR(20) CHECK (recommendation IN ('accept', 'review', 'reject')),
    required_actions TEXT[],
    detection_algorithm VARCHAR(100),
    sensitivity_level VARCHAR(20) CHECK (sensitivity_level IN ('low', 'medium', 'high', 'critical')),
    tolerance_threshold DECIMAL(3,2),
    detected_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    detected_by VARCHAR(100),
    reviewed_at TIMESTAMP,
    reviewed_by VARCHAR(100),
    resolution_action VARCHAR(50),
    resolution_notes TEXT,
    
    FOREIGN KEY (schema_id) REFERENCES schema_definitions(schema_id),
    FOREIGN KEY (comparison_schema_id) REFERENCES schema_definitions(schema_id)
);

-- Create indexes
CREATE INDEX idx_drift_schema_id ON schema_drift_log(schema_id);
CREATE INDEX idx_drift_severity ON schema_drift_log(drift_severity);
CREATE INDEX idx_drift_detected_at ON schema_drift_log(detected_at);
CREATE INDEX idx_drift_score ON schema_drift_log(drift_score);
```

#### 8. File Processing Log

```sql
CREATE TABLE file_processing_log (
    processing_id UUID PRIMARY KEY,
    file_id UUID NOT NULL,
    schema_id UUID,
    mapping_id UUID,
    file_name VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    file_hash VARCHAR(64), -- SHA-256 of file content
    processing_status VARCHAR(50) NOT NULL CHECK (processing_status IN ('uploaded', 'processing', 'completed', 'failed', 'cancelled')),
    processing_phase VARCHAR(50) CHECK (processing_phase IN ('ingestion', 'schema_analysis', 'mapping', 'transformation', 'validation')),
    -- Processing metrics as JSON
    processing_metrics JSON,
    -- Error details if processing failed
    error_details JSON,
    started_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    processed_by VARCHAR(100),
    -- Estimated vs actual processing time
    estimated_duration INTEGER, -- seconds
    actual_duration INTEGER, -- seconds
    
    FOREIGN KEY (schema_id) REFERENCES schema_definitions(schema_id),
    FOREIGN KEY (mapping_id) REFERENCES mapping_configurations(mapping_id)
);

-- Create indexes
CREATE INDEX idx_file_processing_status ON file_processing_log(processing_status);
CREATE INDEX idx_file_processing_started ON file_processing_log(started_at);
CREATE INDEX idx_file_id ON file_processing_log(file_id);
CREATE INDEX idx_file_schema_id ON file_processing_log(schema_id);
```

#### 9. Audit Trail Table

```sql
CREATE TABLE audit_trail (
    audit_id UUID PRIMARY KEY,
    entity_type VARCHAR(100) NOT NULL, -- 'schema', 'mapping', 'column', etc.
    entity_id UUID NOT NULL,
    action VARCHAR(50) NOT NULL, -- 'create', 'update', 'delete', 'approve', 'reject'
    user_id VARCHAR(100) NOT NULL,
    user_role VARCHAR(100),
    timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    -- Previous state of the entity (JSON)
    old_values JSON,
    -- New state of the entity (JSON)
    new_values JSON,
    -- Changes made (JSON array of field changes)
    changes JSON,
    reason TEXT,
    ip_address VARCHAR(45), -- IPv6 compatible
    user_agent VARCHAR(500),
    session_id VARCHAR(100),
    -- Digital signature for tamper detection
    digital_signature VARCHAR(512)
);

-- Create indexes for audit queries
CREATE INDEX idx_audit_entity ON audit_trail(entity_type, entity_id);
CREATE INDEX idx_audit_user ON audit_trail(user_id);
CREATE INDEX idx_audit_timestamp ON audit_trail(timestamp);
CREATE INDEX idx_audit_action ON audit_trail(action);
```

#### 10. Business Glossary Table

```sql
CREATE TABLE business_glossary (
    glossary_id UUID PRIMARY KEY,
    term VARCHAR(255) NOT NULL UNIQUE,
    definition TEXT NOT NULL,
    category VARCHAR(100),
    domain VARCHAR(100),
    synonyms VARCHAR[],
    related_terms VARCHAR[],
    business_rules TEXT[],
    data_steward VARCHAR(100),
    approved_by VARCHAR(100),
    approved_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    -- Tags for classification
    tags VARCHAR[],
    -- Source of definition
    source VARCHAR(255),
    -- Usage examples
    examples TEXT[]
);

-- Create indexes
CREATE INDEX idx_glossary_term ON business_glossary(term);
CREATE INDEX idx_glossary_category ON business_glossary(category);
CREATE INDEX idx_glossary_domain ON business_glossary(domain);
```

### Advanced DuckDB Features Implementation

#### 1. Materialized Views for Performance

```sql
-- Materialized view for schema summary statistics
CREATE MATERIALIZED VIEW schema_summary_stats AS
SELECT 
    s.schema_id,
    s.schema_name,
    s.version,
    s.status,
    COUNT(c.column_id) as column_count,
    COUNT(CASE WHEN c.is_primary_key THEN 1 END) as primary_key_count,
    COUNT(CASE WHEN c.is_nullable = false THEN 1 END) as required_fields_count,
    AVG(LENGTH(c.column_description)) as avg_description_length,
    s.created_at,
    s.updated_at
FROM schema_definitions s
LEFT JOIN column_definitions c ON s.schema_id = c.schema_id
GROUP BY s.schema_id, s.schema_name, s.version, s.status, s.created_at, s.updated_at;

-- Materialized view for mapping effectiveness
CREATE MATERIALIZED VIEW mapping_effectiveness_stats AS
SELECT 
    m.mapping_id,
    m.mapping_name,
    m.status,
    COUNT(f.mapping_detail_id) as total_mappings,
    COUNT(CASE WHEN f.confidence >= 0.9 THEN 1 END) as high_confidence_mappings,
    COUNT(CASE WHEN f.confidence >= 0.7 AND f.confidence < 0.9 THEN 1 END) as medium_confidence_mappings,
    COUNT(CASE WHEN f.confidence < 0.7 THEN 1 END) as low_confidence_mappings,
    AVG(f.confidence) as average_confidence,
    COUNT(CASE WHEN f.is_user_verified THEN 1 END) as verified_mappings,
    m.created_at
FROM mapping_configurations m
LEFT JOIN field_mappings f ON m.mapping_id = f.mapping_id
GROUP BY m.mapping_id, m.mapping_name, m.status, m.created_at;
```

#### 2. Common Query Patterns

```sql
-- Find schemas with recent drift detection
CREATE VIEW recent_drift_activity AS
SELECT 
    s.schema_name,
    s.version,
    d.drift_severity,
    d.drift_score,
    d.detected_at,
    d.recommendation
FROM schema_drift_log d
JOIN schema_definitions s ON d.schema_id = s.schema_id
WHERE d.detected_at >= CURRENT_DATE - INTERVAL '30 days'
ORDER BY d.detected_at DESC;

-- Get mapping coverage analysis
CREATE VIEW mapping_coverage_analysis AS
SELECT 
    s.schema_name as source_schema,
    t.schema_name as target_schema,
    COUNT(DISTINCT c.column_name) as source_columns,
    COUNT(DISTINCT f.source_field) as mapped_columns,
    ROUND(
        (COUNT(DISTINCT f.source_field)::DECIMAL / COUNT(DISTINCT c.column_name)) * 100, 2
    ) as coverage_percentage
FROM schema_definitions s
JOIN column_definitions c ON s.schema_id = c.schema_id
LEFT JOIN mapping_configurations m ON s.schema_id = m.source_schema_id
LEFT JOIN schema_definitions t ON m.target_schema_id = t.schema_id
LEFT JOIN field_mappings f ON m.mapping_id = f.mapping_id AND c.column_name = f.source_field
WHERE s.status = 'active'
GROUP BY s.schema_id, s.schema_name, t.schema_name;
```

#### 3. Data Partitioning Strategy

```sql
-- Partition audit trail by month for performance
CREATE TABLE audit_trail_partitioned (
    audit_id UUID PRIMARY KEY,
    entity_type VARCHAR(100) NOT NULL,
    entity_id UUID NOT NULL,
    action VARCHAR(50) NOT NULL,
    user_id VARCHAR(100) NOT NULL,
    user_role VARCHAR(100),
    timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    old_values JSON,
    new_values JSON,
    changes JSON,
    reason TEXT,
    ip_address VARCHAR(45),
    user_agent VARCHAR(500),
    session_id VARCHAR(100),
    digital_signature VARCHAR(512),
    partition_month DATE GENERATED ALWAYS AS (DATE_TRUNC('month', timestamp)) STORED
) PARTITION BY RANGE (partition_month);

-- Create monthly partitions (example for 2024)
CREATE TABLE audit_trail_2024_01 PARTITION OF audit_trail_partitioned
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
CREATE TABLE audit_trail_2024_02 PARTITION OF audit_trail_partitioned
    FOR VALUES FROM ('2024-02-01') TO ('2024-03-01');
-- Continue for other months...
```

#### 4. Advanced Analytics Functions

```sql
-- Schema evolution analysis function
CREATE OR REPLACE FUNCTION analyze_schema_evolution(p_schema_id UUID, p_days INTEGER DEFAULT 90)
RETURNS TABLE (
    version_number VARCHAR,
    change_type VARCHAR,
    drift_score DECIMAL,
    changes_count INTEGER,
    created_at TIMESTAMP
) AS $
BEGIN
    RETURN QUERY
    SELECT 
        sv.version_number,
        sv.change_type,
        COALESCE(sdl.drift_score, 0.0) as drift_score,
        JSON_ARRAY_LENGTH(sv.change_details) as changes_count,
        sv.created_at
    FROM schema_versions sv
    LEFT JOIN schema_drift_log sdl ON sv.schema_id = sdl.schema_id 
        AND sdl.detected_at >= sv.created_at 
        AND sdl.detected_at < sv.created_at + INTERVAL '1 day'
    WHERE sv.schema_id = p_schema_id
        AND sv.created_at >= CURRENT_DATE - INTERVAL (p_days || ' days')
    ORDER BY sv.created_at DESC;
END;
$ LANGUAGE plpgsql;

-- Mapping quality assessment function
CREATE OR REPLACE FUNCTION assess_mapping_quality(p_mapping_id UUID)
RETURNS TABLE (
    quality_score DECIMAL,
    coverage_percentage DECIMAL,
    confidence_distribution JSON,
    recommendations TEXT[]
) AS $
DECLARE
    total_fields INTEGER;
    mapped_fields INTEGER;
    high_conf_count INTEGER;
    medium_conf_count INTEGER;
    low_conf_count INTEGER;
    avg_confidence DECIMAL;
    quality_score_val DECIMAL;
    recommendations_array TEXT[];
BEGIN
    -- Get mapping statistics
    SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN fm.confidence >= 0.9 THEN 1 END) as high_conf,
        COUNT(CASE WHEN fm.confidence >= 0.7 AND fm.confidence < 0.9 THEN 1 END) as medium_conf,
        COUNT(CASE WHEN fm.confidence < 0.7 THEN 1 END) as low_conf,
        AVG(fm.confidence)
    INTO total_fields, high_conf_count, medium_conf_count, low_conf_count, avg_confidence
    FROM field_mappings fm
    WHERE fm.mapping_id = p_mapping_id;
    
    -- Calculate quality score (weighted average)
    quality_score_val := (
        (high_conf_count * 1.0) + 
        (medium_conf_count * 0.7) + 
        (low_conf_count * 0.3)
    ) / NULLIF(total_fields, 0);
    
    -- Generate recommendations
    recommendations_array := ARRAY[]::TEXT[];
    
    IF low_conf_count > total_fields * 0.2 THEN
        recommendations_array := array_append(recommendations_array, 
            'Consider manual review of low-confidence mappings');
    END IF;
    
    IF avg_confidence < 0.8 THEN
        recommendations_array := array_append(recommendations_array, 
            'Overall confidence is low - consider additional data samples');
    END IF;
    
    RETURN QUERY SELECT 
        quality_score_val,
        (total_fields::DECIMAL / NULLIF(
            (SELECT COUNT(*) FROM column_definitions cd 
             JOIN mapping_configurations mc ON cd.schema_id = mc.source_schema_id 
             WHERE mc.mapping_id = p_mapping_id), 0
        )) * 100 as coverage_pct,
        JSON_OBJECT(
            'high_confidence', high_conf_count,
            'medium_confidence', medium_conf_count,
            'low_confidence', low_conf_count
        ) as conf_dist,
        recommendations_array;
END;
$ LANGUAGE plpgsql;
```

#### 5. Sample Data and Usage Examples

```sql
-- Insert sample schema definition
INSERT INTO schema_definitions (
    schema_id, schema_name, schema_description, version, fingerprint, 
    status, source_system, domain, sensitivity_level, created_by
) VALUES (
    'a1b2c3d4-e5f6-7890-1234-567890abcdef',
    'customer_data_v1',
    'Customer master data schema for CRM system',
    '1.0.0',
    'abc123def456789...',
    'active',
    'CRM_PROD',
    'customer_management',
    'confidential',
    'data_engineer_001'
);

-- Insert sample column definitions
INSERT INTO column_definitions (
    column_id, schema_id, column_name, ordinal_position, data_type,
    is_nullable, is_primary_key, column_description, constraints
) VALUES 
(
    '11111111-2222-3333-4444-555555555555',
    'a1b2c3d4-e5f6-7890-1234-567890abcdef',
    'customer_id',
    1,
    'string',
    false,
    true,
    'Unique customer identifier',
    '{"max_length": 20, "pattern": "^CUST[0-9]{10}$"}'
),
(
    '22222222-3333-4444-5555-666666666666',
    'a1b2c3d4-e5f6-7890-1234-567890abcdef',
    'email_address',
    2,
    'string',
    false,
    false,
    'Customer email address',
    '{"max_length": 255, "pattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"}'
);

-- Query examples
-- 1. Find all schemas with high drift in last 30 days
SELECT s.schema_name, d.drift_severity, d.drift_score, d.detected_at
FROM schema_definitions s
JOIN schema_drift_log d ON s.schema_id = d.schema_id
WHERE d.detected_at >= CURRENT_DATE - INTERVAL '30 days'
  AND d.drift_severity IN ('high', 'critical')
ORDER BY d.drift_score DESC;

-- 2. Get mapping effectiveness for active mappings
SELECT * FROM mapping_effectiveness_stats 
WHERE status = 'active' 
ORDER BY average_confidence DESC;

-- 3. Find unmapped columns across all schemas
SELECT s.schema_name, c.column_name, c.data_type
FROM schema_definitions s
JOIN column_definitions c ON s.schema_id = c.schema_id
WHERE s.status = 'active'
  AND NOT EXISTS (
    SELECT 1 FROM mapping_configurations m
    JOIN field_mappings f ON m.mapping_id = f.mapping_id
    WHERE m.source_schema_id = s.schema_id
      AND f.source_field = c.column_name
      AND m.status = 'active'
  )
ORDER BY s.schema_name, c.ordinal_position;
```
