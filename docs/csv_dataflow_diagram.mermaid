flowchart TD
    A[Incoming RAW CSV Files] --> B[Extract Sample Rows]
    B --> C[Extract Filename Patterns]
    C --> D[Generate Schema Information]
    
    D --> E{Is File Registered?}
    
    E -->|NO| F[Store Schema in Registry]
    E -->|YES| G[Compare for Schema Drift]
    
    F --> H[Generate Table-Level Dictionary]
    G --> I{Drift Detected?}
    
    I -->|YES| J[Store New Schema Version]
    I -->|NO| K[Get Existing Mapping Details]
    
    J --> H
    H --> L[Generate Text Embeddings]
    L --> M[Intelligent Semantic Mapping]
    M --> N[Generate Mapping Results]
    N --> O[Display Mapping Results]
    
    K --> P[Perform Data Transformation]
    
    O --> Q[Capture User Input]
    Q --> R{User Wants to Modify?}
    
    R -->|YES| S[Show Modification Interface]
    R -->|NO| T[Generate Column-Level Dictionary]
    
    S --> U[Validate Mapping Compatibility]
    U --> V{Mapping Compatible?}
    
    V -->|YES| W[Update Mapping Results]
    V -->|NO| X[Alert User & Show Conflicts]
    
    W --> O
    X --> S
    
    T --> Y[Store Data Dictionary]
    Y --> Z[Store Source-to-Target Mappings]
    Z --> P
    
    P --> AA[Transformation Complete]
    
    %% Additional feedback loop for mapping updates
    AA --> BB{Mapping Update Needed?}
    BB -->|YES| CC[Get User Input for Updates]
    BB -->|NO| DD[Process Complete]
    
    CC --> EE{Update Successful?}
    EE -->|YES| FF[Update Mapping Registry]
    EE -->|NO| GG[Stop Processing & Alert]
    
    FF --> DD
    GG --> HH[Manual Intervention Required]
    
    %% Data stores (shown as cylinders)
    I1[(Schema Registry)]
    I2[(Mapping Repository)]
    I3[(Metadata Store)]
    I4[(Audit Log)]
    I5[(Target Ontology)]
    
    %% Connect processes to data stores
    F -.-> I1
    J -.-> I1
    G -.-> I1
    K -.-> I2
    M -.-> I5
    H -.-> I3
    T -.-> I3
    Y -.-> I3
    Z -.-> I2
    FF -.-> I2
    
    %% Log all activities
    B -.-> I4
    F -.-> I4
    G -.-> I4
    Q -.-> I4
    U -.-> I4
    P -.-> I4
    
    %% Styling
    classDef process fill:#e3f2fd
    classDef decision fill:#fff3e0
    classDef datastore fill:#f1f8e9
    classDef userinteraction fill:#fce4ec
    classDef error fill:#ffebee
    
    class B,C,D,F,H,L,M,N,P,T,Y,Z,U,W,FF process
    class E,I,R,V,BB,EE decision
    class I1,I2,I3,I4,I5 datastore
    class O,Q,S,CC userinteraction
    class X,GG,HH error