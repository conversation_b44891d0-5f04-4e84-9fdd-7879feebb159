<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15" version="27.2.0">
  <diagram name="Page-1" id="KqqrPm0jcX5qdrquBsme">
    <mxGraphModel dx="2068" dy="1793" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-102" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-27" target="8HI1ljiHsv9H1I4Mly7g-34">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-27" value="Incoming RAW file(s)" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="340" y="-170" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-111" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-29" target="8HI1ljiHsv9H1I4Mly7g-36">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-29" value="&lt;span style=&quot;font-size: 10px;&quot;&gt;Extract the file_name and pattern to normalise the filename&lt;/span&gt;" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="340" y="30" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-113" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-31" target="8HI1ljiHsv9H1I4Mly7g-38">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-114" value="&lt;b&gt;NO&lt;/b&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="8HI1ljiHsv9H1I4Mly7g-113">
          <mxGeometry x="-0.1643" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-115" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-31" target="8HI1ljiHsv9H1I4Mly7g-85">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-117" value="&lt;b&gt;YES&lt;/b&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="8HI1ljiHsv9H1I4Mly7g-115">
          <mxGeometry x="-0.4211" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-31" value="Is the file&amp;nbsp;&lt;div&gt;registered ?&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="345" y="220" width="110" height="90" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-103" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-34" target="8HI1ljiHsv9H1I4Mly7g-29">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-34" value="Extract few rows of the file" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="340" y="-70" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-112" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-36" target="8HI1ljiHsv9H1I4Mly7g-31">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-36" value="Generate schema information for the file" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="340" y="120" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-109" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-38" target="8HI1ljiHsv9H1I4Mly7g-40">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-38" value="Store the schema for the file in &lt;b&gt;schema registry&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="630" y="310" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-46" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-40" target="8HI1ljiHsv9H1I4Mly7g-44">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-40" value="Generate table level dictionary for the file" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="630" y="430" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-47" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-44" target="8HI1ljiHsv9H1I4Mly7g-45">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-44" value="Generate text embeddings for the table dictionary" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="630" y="525" width="120" height="55" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-49" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-45" target="8HI1ljiHsv9H1I4Mly7g-48">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-45" value="Intelligent Semantic mapping with target &lt;b&gt;ontology &lt;/b&gt;layer" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="630" y="630" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-52" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-48" target="8HI1ljiHsv9H1I4Mly7g-51">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-48" value="Generate mapping results between source and target table" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="630" y="730" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-54" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-51" target="8HI1ljiHsv9H1I4Mly7g-53">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-51" value="Display the mapping results" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="630" y="830" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-53" value="User wants to modify&lt;div&gt;&amp;nbsp;the results ?&amp;nbsp;&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="620" y="930" width="140" height="140" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-59" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-56" target="8HI1ljiHsv9H1I4Mly7g-58">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-56" value="Generate column level dictionary" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="790" y="1020" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-57" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.558;entryY=0.017;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-53" target="8HI1ljiHsv9H1I4Mly7g-56">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-68" value="&lt;b&gt;NO&lt;/b&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="8HI1ljiHsv9H1I4Mly7g-57">
          <mxGeometry x="-0.2713" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-61" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-58" target="8HI1ljiHsv9H1I4Mly7g-60">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-58" value="Generate text embeddings for column dictionary" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="790" y="1130" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-64" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-60" target="8HI1ljiHsv9H1I4Mly7g-62">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-60" value="Intelligent Semantic mapping with target &lt;b&gt;ontology &lt;/b&gt;layer" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="790" y="1240" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-65" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-62" target="8HI1ljiHsv9H1I4Mly7g-63">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-62" value="Generate mapping results between source and target columns" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="790" y="1350" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-67" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-63" target="8HI1ljiHsv9H1I4Mly7g-66">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-63" value="Display the mapping results" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="790" y="1450" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-70" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-66" target="8HI1ljiHsv9H1I4Mly7g-69">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-73" value="&lt;b&gt;NO&lt;/b&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="8HI1ljiHsv9H1I4Mly7g-70">
          <mxGeometry x="-0.3429" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-75" value="&lt;b&gt;YES&lt;/b&gt;" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-66" target="8HI1ljiHsv9H1I4Mly7g-74">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-66" value="User wants to modify&lt;div&gt;&amp;nbsp;the results ?&amp;nbsp;&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="780" y="1560" width="140" height="140" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-72" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-69" target="8HI1ljiHsv9H1I4Mly7g-71">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-69" value="Store data dictionary for the RAW file" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="990" y="1600" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-120" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-71" target="8HI1ljiHsv9H1I4Mly7g-119">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-71" value="Store the source to target mapping details" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="990" y="1700" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-76" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-74" target="8HI1ljiHsv9H1I4Mly7g-69">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="660" y="1530" />
              <mxPoint x="1050" y="1530" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-74" value="Capture the user input" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="590" y="1600" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-78" value="Capture the user input" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="440" y="970" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-79" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-53" target="8HI1ljiHsv9H1I4Mly7g-78">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-81" value="&lt;b&gt;YES&lt;/b&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="8HI1ljiHsv9H1I4Mly7g-79">
          <mxGeometry x="-0.2333" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-80" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.683;entryY=-0.017;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-78" target="8HI1ljiHsv9H1I4Mly7g-56">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="500" y="910" />
              <mxPoint x="872" y="910" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-90" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-85" target="8HI1ljiHsv9H1I4Mly7g-89">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-85" value="Compare if there is schema drift" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="150" y="320" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-92" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-89" target="8HI1ljiHsv9H1I4Mly7g-91">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-93" value="&lt;b&gt;YES&lt;/b&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="8HI1ljiHsv9H1I4Mly7g-92">
          <mxGeometry x="-0.2353" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-95" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-89" target="8HI1ljiHsv9H1I4Mly7g-94">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-118" value="&lt;b&gt;NO&lt;/b&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="8HI1ljiHsv9H1I4Mly7g-95">
          <mxGeometry x="-0.3867" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-89" value="Drift detected ?" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="130" y="430" width="160" height="100" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-91" value="Store new version of schema in &lt;b&gt;schema registry&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-60" y="520" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-97" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-94" target="8HI1ljiHsv9H1I4Mly7g-96">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-94" value="Get the mapping details from database" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="340" y="520" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-96" value="Perform data transformation" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="340" y="620" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-119" value="Perform data transformation" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="990" y="1810" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-122" value="Generate both table and column level dictionary for the file" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-60" y="630" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-127" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-123" target="8HI1ljiHsv9H1I4Mly7g-124">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-123" value="Generate text embeddings for the dictionary" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-60" y="730" width="120" height="55" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-129" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-124" target="8HI1ljiHsv9H1I4Mly7g-128">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-124" value="Intelligent Semantic mapping with target &lt;b&gt;ontology &lt;/b&gt;layer" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-60" y="830" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-125" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-91" target="8HI1ljiHsv9H1I4Mly7g-122">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-126" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-122" target="8HI1ljiHsv9H1I4Mly7g-123">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-135" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-128" target="8HI1ljiHsv9H1I4Mly7g-132">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-128" value="Fetch the existing mapping details from the &lt;b&gt;database&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-60" y="930" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-139" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-132" target="8HI1ljiHsv9H1I4Mly7g-137">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-141" value="&lt;b&gt;YES&lt;/b&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="8HI1ljiHsv9H1I4Mly7g-139">
          <mxGeometry x="-0.3183" y="3" relative="1" as="geometry">
            <mxPoint y="-3" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-148" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-132" target="8HI1ljiHsv9H1I4Mly7g-147">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-162" value="&lt;b&gt;NO&lt;/b&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="8HI1ljiHsv9H1I4Mly7g-148">
          <mxGeometry x="-0.4582" y="2" relative="1" as="geometry">
            <mxPoint y="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-132" value="Is the new mapping&amp;nbsp;&lt;div&gt;compatible with&amp;nbsp;&lt;/div&gt;&lt;div&gt;old mapping ?&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-83.75" y="1038" width="167.5" height="131" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-140" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-137" target="8HI1ljiHsv9H1I4Mly7g-138">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-137" value="Store the source to target mapping details" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-230" y="1150" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-138" value="Perform data transformation" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-230" y="1260" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-150" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-147" target="8HI1ljiHsv9H1I4Mly7g-149">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-147" value="Alert the user" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="100" y="1150" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-152" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-149" target="8HI1ljiHsv9H1I4Mly7g-151">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-149" value="&lt;br&gt;&lt;div&gt;Ge the user input&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="100" y="1260" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-155" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-151">
          <mxGeometry relative="1" as="geometry">
            <mxPoint y="1010" as="targetPoint" />
            <mxPoint x="-165" y="1425" as="sourcePoint" />
            <Array as="points">
              <mxPoint x="-259" y="1415" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-157" value="&lt;b&gt;YES&lt;/b&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="8HI1ljiHsv9H1I4Mly7g-155">
          <mxGeometry x="-0.7525" y="2" relative="1" as="geometry">
            <mxPoint y="-2" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-160" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="8HI1ljiHsv9H1I4Mly7g-151" target="8HI1ljiHsv9H1I4Mly7g-159">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-161" value="&lt;b&gt;NO&lt;/b&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="8HI1ljiHsv9H1I4Mly7g-160">
          <mxGeometry x="-0.5077" y="-4" relative="1" as="geometry">
            <mxPoint x="4" y="-4" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-151" value="Mapping updated ?" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="95" y="1360" width="130" height="110" as="geometry" />
        </mxCell>
        <mxCell id="8HI1ljiHsv9H1I4Mly7g-159" value="Stop further processing" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="310" y="1385" width="120" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
