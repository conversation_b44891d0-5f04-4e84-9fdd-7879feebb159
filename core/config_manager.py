"""
Configuration Manager for Schema Registry
Handles reading and validation of config.json
"""

import json
import yaml
import os
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from pathlib import Path
import logging

@dataclass
class AzureOpenAIConfig:
    api_key: str
    endpoint: str
    deployment: str
    api_version: str
    timeout: int
    max_retries: int
    temperature: Optional[float] = None
    max_tokens: Optional[int] = None
    batch_size: Optional[int] = None

@dataclass
class DuckDBConfig:
    database_path: str
    connection_options: Dict[str, Any]


@dataclass
class SchemaRegistryConfig:
    version_strategy: str  # "auto_increment", "semantic", "timestamp"
    similarity_threshold: float
    default_compatibility_mode: str  # "strict", "graceful", "permissive"
    enable_recommendations: bool
    enable_migration_suggestions: bool
    column_order_matters: bool
    retention_policy: Dict[str, int]


@dataclass
class DatabaseConfig:
    type: str
    backends: Dict[str, Dict[str, Any]]

@dataclass
class Neo4jConfig:
    uri: str
    username: str
    password: str
    database: str


@dataclass
class OpenAIEmbeddingConfig:
    provider: str
    model: str
    api_key: str
    dimensions: int
    batch_size: int


@dataclass
class SentenceTransformerConfig:
    provider: str
    model: str
    dimensions: int
    device: str
    batch_size: int


@dataclass
class DualEmbeddingConfig:
    openai: OpenAIEmbeddingConfig
    sentence_transformer: SentenceTransformerConfig


class ConfigManager:
    
    def __init__(self, config_filename: str = "config.json",
                 config_dir: str = "config",
                 project_markers: list = None):
        
        if project_markers is None:
            project_markers = ['.git', 'setup.py', 'pyproject.toml', 'requirements.txt', '.gitignore']
        
        self.config_filename = config_filename
        self.config_dir = config_dir
        self.project_markers = project_markers
        self.project_root = self._find_project_root()
        self.config_path = self._get_config_path()
        self._config = None
        self._duckdb_config = None
        self._schema_registry_config = None
        self.logger = logging.getLogger(__name__)
    
    def _find_project_root(self) -> Path:
        """
        Find project root by looking for marker files/directories.
        
        Returns:
            Path: Project root directory
        """
        # Method 1: Start from current file's directory and traverse up
        current_path = Path(__file__).resolve().parent
        
        # Traverse up the directory tree
        for path in [current_path] + list(current_path.parents):
            # Check if any project markers exist in this directory
            if any((path / marker).exists() for marker in self.project_markers):
                return path
        
        # Method 2: If no markers found, assume current file's parent is project root
        # This assumes config_manager.py is in core/ at project root level
        return current_path.parent
    
    def _get_config_path(self) -> Path:
        """
        Construct the full path to the config file.
        
        Returns:
            Path: Full path to config file
        """
        if self.config_dir:
            # Config file is in a subdirectory (e.g., config/config.yaml)
            config_path = self.project_root / self.config_dir / self.config_filename
        else:
            # Config file is in project root
            config_path = self.project_root / self.config_filename
        
        return config_path
    
    def load_config(self) -> Dict[str, Any]:
        """Load and validate configuration from file."""
        if self._config is not None:
            return self._config
        
        try:
            if not self.config_path.exists():
                raise FileNotFoundError(f"Config file not found: {self.config_path}")
            
            path = Path(self.config_path)
            with open(self.config_path, 'r', encoding='utf-8') as file:
                if path.name.lower().endswith(('.yaml', '.yml')):
                    self._config = yaml.safe_load(file) or {}
                elif path.name.lower().endswith('.json'):
                    self._config = json.load(file) or {}
                else:
                    # Assume it's a simple key=value format
                    config = {}
                    for line in file:
                        line = line.strip()
                        if line and not line.startswith('#'):
                            if '=' in line:
                                key, value = line.split('=', 1)
                                config[key.strip()] = value.strip()
                    self._config = config
            
            self._validate_config()
            return self._config
                    
        except Exception as e:
            self.logger.error(f"Could not load config from {self.config_path}: {e}")
            raise e

    def get(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value by key.
        
        Args:
            key: Configuration key (supports dot notation like 'database.host')
            default: Default value if key not found
            
        Returns:
            Configuration value or default
        """
        if self._config is None:
            self.load_config()

        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """
        Set configuration value by key.
        
        Args:
            key: Configuration key (supports dot notation)
            value: Value to set
        """
        if self._config is None:
            self.load_config()

        keys = key.split('.')
        config = self._config
        
        # Navigate to the parent of the target key
        for k in keys[:-1]:
            config = config.setdefault(k, {})
        
        # Set the value
        config[keys[-1]] = value
    
    def save(self) -> None:
        """Save current configuration back to file."""
        if self._config is None:
            return

        try:
            # Ensure directory exists
            self.config_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_path, 'w', encoding='utf-8') as file:
                if self.config_filename.endswith(('.yaml', '.yml')):
                    yaml.dump(self._config, file, default_flow_style=False, indent=2)
                elif self.config_filename.endswith('.json'):
                    json.dump(self._config, file, indent=2)
                else:
                    # Simple key=value format
                    for key, value in self._config.items():
                        file.write(f"{key}={value}\n")
                        
        except Exception as e:
            self.logger.error(f"Error saving config to {self.config_path}: {e}")
    
    def reload(self) -> None:
        """Reload configuration from file."""
        self._config = None
        self.load_config()
    
    @property
    def project_root_path(self) -> str:
        """Get project root path as string."""
        return str(self.project_root)
    
    def __repr__(self) -> str:
        return f"ConfigManager(project_root='{self.project_root}', config_path='{self.config_path}')"

    def _validate_config(self) -> None:
        """Validate configuration structure and values"""
        if not self._config:
            raise ValueError("Configuration not loaded")
        
        # Validate database configuration
        database_config = self._config.get("database", {})
        if database_config.get("type") != "duckdb":
            raise ValueError(f"Unsupported database type: {database_config.get('type')}. Only 'duckdb' is supported")
        
        # Validate DuckDB backend exists
        backends = database_config.get("backends", {})
        if "duckdb" not in backends:
            raise ValueError("DuckDB backend configuration not found")
        
        # Validate schema registry configuration
        schema_registry = self._config.get("schema_registry", {})
        if not schema_registry:
            raise ValueError("Schema registry configuration not found")
        
        # Validate version strategy
        valid_strategies = ["auto_increment", "semantic", "timestamp"]
        version_strategy = schema_registry.get("version_strategy", "semantic")
        if version_strategy not in valid_strategies:
            raise ValueError(f"Invalid version strategy: {version_strategy}. Must be one of {valid_strategies}")
        
        # Validate compatibility mode
        valid_modes = ["strict", "graceful", "permissive"]
        compatibility_mode = schema_registry.get("default_compatibility_mode", "graceful")
        if compatibility_mode not in valid_modes:
            raise ValueError(f"Invalid compatibility mode: {compatibility_mode}. Must be one of {valid_modes}")
        
        # Validate similarity threshold
        similarity_threshold = schema_registry.get("similarity_threshold", 0.8)
        if not 0.0 <= similarity_threshold <= 1.0:
            raise ValueError(f"Similarity threshold must be between 0.0 and 1.0, got {similarity_threshold}")
        
        # Validate neo4j and embeddings config
        self._validate_neo4j_and_embedding_config()
    
    def get_duckdb_config(self) -> DuckDBConfig:
        """Get DuckDB configuration"""
        if self._config is None:
            self.load_config()
        
        if not self._duckdb_config:
            duckdb_backend = self._config["database"]["backends"]["duckdb"]
            defined_path = duckdb_backend['connection'].get("path","./data/schema_registry.duckdb")
            database_path = str(self.project_root)+ "/" + defined_path
            self.logger.info(f"database path: {database_path}")
            self._duckdb_config = DuckDBConfig(
                database_path=database_path,
                connection_options=duckdb_backend.get("connection_options", {})
            )
        
        return self._duckdb_config
    
    def get_schema_registry_config(self) -> SchemaRegistryConfig:
        """Get schema registry configuration"""
        if self._config is None:
            self.load_config()
        
        if not self._schema_registry_config:
            registry_config = self._config["schema_registry"]
            self._schema_registry_config = SchemaRegistryConfig(
                version_strategy=registry_config.get("version_strategy", "semantic"),
                similarity_threshold=registry_config.get("similarity_threshold", 0.8),
                default_compatibility_mode=registry_config.get("default_compatibility_mode", "graceful"),
                enable_recommendations=registry_config.get("enable_recommendations", True),
                enable_migration_suggestions=registry_config.get("enable_migration_suggestions", True),
                column_order_matters=registry_config.get("column_order_matters", False),
                retention_policy=registry_config.get("retention_policy", {"max_versions": 10, "max_age_days": 365})
            )
        
        return self._schema_registry_config
    
    def get_database_config(self) -> DatabaseConfig:
        """Get database configuration"""
        if self._config is None:
            self.load_config()
        
        database_config = self._config["database"]
        return DatabaseConfig(
            type=database_config["type"],
            backends=database_config["backends"]
        )
    
    def update_config(self, section: str, key: str, value: Any, changed_by: Optional[str] = None) -> None:
        """Update configuration value and audit the change"""
        if self._config is None:
            self.load_config()
        
        # Get old value for audit
        old_value = self.get(f"{section}.{key}")
        
        # Update config
        self.set(f"{section}.{key}", value)
        
        # Save config
        self.save()
        
        # TODO: Log audit trail to database
        self._log_config_change(f"{section}.{key}", old_value, value, changed_by)
    
    def _save_config(self) -> None:
        """Save configuration back to file"""
        try:
            # Create backup
            backup_path = f"{self.config_path}.backup"
            if os.path.exists(self.config_path):
                os.rename(self.config_path, backup_path)
            
            # Save new config
            with open(self.config_path, 'w') as f:
                json.dump(self._config, f, indent=2)
            
            # Remove backup if successful
            if os.path.exists(backup_path):
                os.remove(backup_path)
                
        except Exception as e:
            # Restore backup if save failed
            backup_path = f"{self.config_path}.backup"
            if os.path.exists(backup_path):
                os.rename(backup_path, self.config_path)
            raise e
    
    def _log_config_change(self, config_type: str, old_value: Any, new_value: Any, changed_by: Optional[str]) -> None:
        """Log configuration changes for audit trail"""
        # This will be implemented to log to the config_audit table
        # For now, just print the change
        self.logger.info(f"Config change: {config_type} changed from {old_value} to {new_value} by {changed_by or 'system'}")
    
    def create_default_config(self) -> None:
        """Create default configuration file"""
        default_config = {
            "database": {
                "type": "duckdb",
                "backends": {
                    "duckdb": {
                        "database_path": "./data/schema_registry.duckdb",
                        "connection_options": {
                            "read_only": False,
                            "access_mode": "automatic"
                        }
                    }
                }
            },
            "schema_registry": {
                "version_strategy": "semantic",
                "similarity_threshold": 0.8,
                "default_compatibility_mode": "graceful",
                "enable_recommendations": True,
                "enable_migration_suggestions": True,
                "column_order_matters": False,
                "retention_policy": {
                    "max_versions": 10,
                    "max_age_days": 365
                }
            }
        }
        
        # Create data directory if it doesn't exist
        data_dir = self.project_root / "data"
        data_dir.mkdir(exist_ok=True)
        
        # Save default config
        with open(self.config_path, 'w') as f:
            json.dump(default_config, f, indent=2)
        
        self.logger.info(f"Default configuration created at {self.config_path}")
    
    def reload_config(self) -> Dict[str, Any]:
        """Reload configuration from file"""
        self._config = None
        self._duckdb_config = None
        self._schema_registry_config = None
        return self.load_config()

    def get_azure_openai_config(self) -> AzureOpenAIConfig:
        """Get Azure OpenAI configuration"""
        if self._config is None:
            self.load_config()
        
        # Get azure_openai section from config
        azure_openai_config = self.get("azure_openai", {})
        
        if not azure_openai_config:
            raise ValueError("Azure OpenAI configuration not found in config file")
        
        # Validate required fields
        required_fields = ["api_key", "endpoint", "deployment", "api_version"]
        missing_fields = [field for field in required_fields if not azure_openai_config.get(field)]
        
        if missing_fields:
            raise ValueError(f"Missing required Azure OpenAI config fields: {missing_fields}")
        
        # Validate endpoint format
        endpoint = azure_openai_config["endpoint"]
        if not endpoint.startswith(("https://", "http://")):
            raise ValueError(f"Invalid Azure OpenAI endpoint format: {endpoint}")
        
        # Validate timeout and max_retries are positive integers
        timeout = azure_openai_config.get("timeout", 180)
        max_retries = azure_openai_config.get("max_retries", 3)
        
        try:
            timeout = int(timeout)
            max_retries = int(max_retries)
            
            if timeout <= 0:
                raise ValueError("Timeout must be a positive integer")
            if max_retries < 0:
                raise ValueError("Max retries must be a non-negative integer")
                
        except (ValueError, TypeError) as e:
            raise ValueError(f"Invalid timeout or max_retries values: {e}")
        
        # Create and return AzureOpenAIConfig object
        return AzureOpenAIConfig(
            api_key=azure_openai_config["api_key"],
            endpoint=azure_openai_config["endpoint"],
            deployment=azure_openai_config["deployment"],
            api_version=azure_openai_config["api_version"],
            timeout=timeout,
            max_retries=max_retries,
            temperature=azure_openai_config.get("temperature"),
            max_tokens=azure_openai_config.get("max_tokens"),
            batch_size=azure_openai_config.get("batch_size")
        )

    def _validate_azure_openai_config(self) -> None:
        """Validate Azure OpenAI configuration section"""
        azure_openai_config = self.get("azure_openai", {})
        
        if not azure_openai_config:
            return  # Azure OpenAI config is optional
        
        # Check required fields
        required_fields = ["api_key", "endpoint", "deployment", "api_version"]
        for field in required_fields:
            if not azure_openai_config.get(field):
                raise ValueError(f"Azure OpenAI config missing required field: {field}")
        
        # Validate API version format
        api_version = azure_openai_config["api_version"]
        if not api_version.count("-") >= 2:  # Expected format: YYYY-MM-DD-preview or YYYY-MM-DD
            raise ValueError(f"Invalid Azure OpenAI API version format: {api_version}")
        
        # Validate timeout and max_retries
        timeout = azure_openai_config.get("timeout", 180)
        max_retries = azure_openai_config.get("max_retries", 3)
        
        if not isinstance(timeout, int) or timeout <= 0:
            raise ValueError(f"Azure OpenAI timeout must be a positive integer, got: {timeout}")
        
        if not isinstance(max_retries, int) or max_retries < 0:
            raise ValueError(f"Azure OpenAI max_retries must be a non-negative integer, got: {max_retries}")
        
        # Validate optional fields if present
        temperature = azure_openai_config.get("temperature")
        if temperature is not None:
            if not isinstance(temperature, (int, float)) or not 0.0 <= temperature <= 2.0:
                raise ValueError(f"Azure OpenAI temperature must be between 0.0 and 2.0, got: {temperature}")
        
        max_tokens = azure_openai_config.get("max_tokens")
        if max_tokens is not None:
            if not isinstance(max_tokens, int) or max_tokens <= 0:
                raise ValueError(f"Azure OpenAI max_tokens must be a positive integer, got: {max_tokens}")
        
        batch_size = azure_openai_config.get("batch_size")
        if batch_size is not None:
            if not isinstance(batch_size, int) or batch_size <= 0:
                raise ValueError(f"Azure OpenAI batch_size must be a positive integer, got: {batch_size}")

    def get_neo4j_config(self) -> Neo4jConfig:
        """Get Neo4j configuration"""
        if self._config is None:
            self.load_config()
        
        neo4j_config = self.get("neo4j", {})
        
        if not neo4j_config:
            raise ValueError("Neo4j configuration not found in config file")
        
        # Validate required fields
        required_fields = ["uri", "username", "password", "database"]
        missing_fields = [field for field in required_fields if not neo4j_config.get(field)]
        
        if missing_fields:
            raise ValueError(f"Missing required Neo4j config fields: {missing_fields}")
        
        # Validate URI format
        uri = neo4j_config["uri"]
        if not uri.startswith(("bolt://", "neo4j://", "bolt+s://", "neo4j+s://")):
            raise ValueError(f"Invalid Neo4j URI format: {uri}")
        
        return Neo4jConfig(
            uri=neo4j_config["uri"],
            username=neo4j_config["username"],
            password=neo4j_config["password"],
            database=neo4j_config["database"]
        )


    def get_openai_embedding_config(self) -> OpenAIEmbeddingConfig:
        """Get OpenAI embedding configuration"""
        if self._config is None:
            self.load_config()
        
        openai_config = self.get("embeddings.openai", {})
        
        if not openai_config:
            raise ValueError("OpenAI embedding configuration not found in config file")
        
        # Validate required fields
        required_fields = ["provider", "model", "api_key", "dimensions", "batch_size"]
        missing_fields = [field for field in required_fields if not openai_config.get(field)]
        
        if missing_fields:
            raise ValueError(f"Missing required OpenAI embedding config fields: {missing_fields}")
        
        # Validate dimensions and batch_size are positive integers
        dimensions = openai_config.get("dimensions", 1536)
        batch_size = openai_config.get("batch_size", 100)
        
        try:
            dimensions = int(dimensions)
            batch_size = int(batch_size)
            
            if dimensions <= 0:
                raise ValueError("Dimensions must be a positive integer")
            if batch_size <= 0:
                raise ValueError("Batch size must be a positive integer")
                
        except (ValueError, TypeError) as e:
            raise ValueError(f"Invalid dimensions or batch_size values: {e}")
        
        return OpenAIEmbeddingConfig(
            provider=openai_config["provider"],
            model=openai_config["model"],
            api_key=openai_config["api_key"],
            dimensions=dimensions,
            batch_size=batch_size
        )


    def get_sentence_transformer_config(self) -> SentenceTransformerConfig:
        """Get Sentence Transformer configuration"""
        if self._config is None:
            self.load_config()
        
        st_config = self.get("embeddings.sentence_transformer", {})
        
        if not st_config:
            raise ValueError("Sentence Transformer configuration not found in config file")
        
        # Validate required fields
        required_fields = ["provider", "model", "dimensions", "device", "batch_size"]
        missing_fields = [field for field in required_fields if not st_config.get(field)]
        
        if missing_fields:
            raise ValueError(f"Missing required Sentence Transformer config fields: {missing_fields}")
        
        # Validate dimensions and batch_size are positive integers
        dimensions = st_config.get("dimensions", 768)
        batch_size = st_config.get("batch_size", 32)
        
        try:
            dimensions = int(dimensions)
            batch_size = int(batch_size)
            
            if dimensions <= 0:
                raise ValueError("Dimensions must be a positive integer")
            if batch_size <= 0:
                raise ValueError("Batch size must be a positive integer")
                
        except (ValueError, TypeError) as e:
            raise ValueError(f"Invalid dimensions or batch_size values: {e}")
        
        # Validate device
        device = st_config.get("device", "cpu")
        if device not in ["cpu", "cuda", "mps"]:
            raise ValueError(f"Invalid device: {device}. Must be one of: cpu, cuda, mps")
        
        return SentenceTransformerConfig(
            provider=st_config["provider"],
            model=st_config["model"],
            dimensions=dimensions,
            device=device,
            batch_size=batch_size
        )


    def get_dual_embedding_config(self) -> DualEmbeddingConfig:
        """Get combined dual embedding configuration"""
        if self._config is None:
            self.load_config()
        
        openai_config = self.get_openai_embedding_config()
        st_config = self.get_sentence_transformer_config()
        
        return DualEmbeddingConfig(
            openai=openai_config,
            sentence_transformer=st_config
        )


    def _validate_neo4j_and_embedding_config(self) -> None:
        """Validate Neo4j and embedding configurations"""
        # Validate Neo4j configuration (optional)
        neo4j_config = self.get("neo4j", {})
        if neo4j_config:
            required_fields = ["uri", "username", "password", "database"]
            for field in required_fields:
                if not neo4j_config.get(field):
                    raise ValueError(f"Neo4j config missing required field: {field}")
            
            uri = neo4j_config["uri"]
            if not uri.startswith(("bolt://", "neo4j://", "bolt+s://", "neo4j+s://")):
                raise ValueError(f"Invalid Neo4j URI format: {uri}")
        
        # Validate embeddings configuration (optional)
        embeddings_config = self.get("embeddings", {})
        if embeddings_config:
            # Validate OpenAI embedding config
            openai_config = embeddings_config.get("openai", {})
            if openai_config:
                required_fields = ["provider", "model", "api_key", "dimensions", "batch_size"]
                for field in required_fields:
                    if not openai_config.get(field):
                        raise ValueError(f"OpenAI embedding config missing required field: {field}")
                
                dimensions = openai_config.get("dimensions")
                batch_size = openai_config.get("batch_size")
                
                if not isinstance(dimensions, int) or dimensions <= 0:
                    raise ValueError(f"OpenAI dimensions must be a positive integer, got: {dimensions}")
                if not isinstance(batch_size, int) or batch_size <= 0:
                    raise ValueError(f"OpenAI batch_size must be a positive integer, got: {batch_size}")
            
            # Validate Sentence Transformer config
            st_config = embeddings_config.get("sentence_transformer", {})
            if st_config:
                required_fields = ["provider", "model", "dimensions", "device", "batch_size"]
                for field in required_fields:
                    if not st_config.get(field):
                        raise ValueError(f"Sentence Transformer config missing required field: {field}")
                
                dimensions = st_config.get("dimensions")
                batch_size = st_config.get("batch_size")
                
                if not isinstance(dimensions, int) or dimensions <= 0:
                    raise ValueError(f"Sentence Transformer dimensions must be a positive integer, got: {dimensions}")
                if not isinstance(batch_size, int) or batch_size <= 0:
                    raise ValueError(f"Sentence Transformer batch_size must be a positive integer, got: {batch_size}")
                
                device = st_config.get("device")
                if device not in ["cpu", "cuda", "mps"]:
                    raise ValueError(f"Invalid device: {device}. Must be one of: cpu, cuda, mps")


    def get_vector_db_config(self) -> Dict[str, Any]:
        """Get Vector DB configuration"""
        if self._config is None:
            self.load_config()
        
        vector_db_config = self.get("vector_db", {})
        
        if not vector_db_config:
            # Return default configuration if not found
            vector_db_config = {
                'persist_directory': str(self.project_root / 'data' / 'vector_db'),
                'embedding_model': 'all-MiniLM-L6-v2',
                'collection_prefix': 'cpg_',
                'similarity_threshold': 0.6,
                'max_results': 10,
                'chromadb_settings': {
                    'anonymized_telemetry': False,
                    'allow_reset': True
                }
            }
            self.logger.warning("Vector DB config not found, using defaults")
        
        # Ensure persist_directory is absolute path
        persist_dir = vector_db_config.get('persist_directory', 'data/vector_db')
        if not os.path.isabs(persist_dir):
            vector_db_config['persist_directory'] = str(self.project_root / persist_dir)
        
        return vector_db_config


    def get_knowledge_base_paths(self) -> Dict[str, Path]:
        """Get all knowledge base related paths"""
        try:
            config = self.load_config()
            kb_config = config.get('knowledgebase', {})
            file_config = kb_config.get('file_processing', {})
            
            paths = {}
            base_paths = {
                'vector_db': kb_config.get('vector_db', {}).get('persist_directory', 'data/vector_db'),
                'uploads': file_config.get('upload_directory', 'data/uploads'),
                'temp': file_config.get('temp_directory', 'data/temp'),
                'learning': file_config.get('learning_directory', 'data/learning')
            }
            
            # Convert to absolute paths
            for key, path in base_paths.items():
                if not os.path.isabs(path):
                    path = os.path.join(self.project_root, path)
                paths[key] = Path(path)
                # Create directory if it doesn't exist
                paths[key].mkdir(parents=True, exist_ok=True)
            
            return paths
        except Exception as e:
            logging.error(f"Failed to get knowledge base paths: {e}")
            return {}

    def get_embedding_config(self) -> Dict[str, Any]:
        """Get embedding model and processing configuration"""
        try:
            config = self.load_config()
            kb_config = config.get('knowledgebase', {})
            return kb_config.get('vector_db', {})
        except Exception as e:
            logging.error(f"Failed to get embedding config: {e}")
            return {}

    def get_classification_config(self) -> Dict[str, Any]:
        """Get classification configuration"""
        if self._config is None:
            self.load_config()
        
        classification_config = self.get("knowledgebase.classification", {})
        
        if not classification_config:
            # Return default configuration
            classification_config = {
                'confidence_threshold': 0.6,
                'min_similarity_score': 0.5,
                'data_types': ['syndicated', 'pos', 'promotion', 'product_attribute', 'margin_data'],
                'enable_learning': True
            }
            self.logger.warning("Classification config not found, using defaults")
        
        return classification_config

    def get_file_processing_config(self) -> Dict[str, Any]:
        """Get file processing configuration"""
        if self._config is None:
            self.load_config()
        
        file_processing_config = self.get("file_processing", {})
        
        if not file_processing_config:
            # Return default configuration
            file_processing_config = {
                'max_file_size': 100 * 1024 * 1024,  # 100MB
                'supported_formats': ['.csv', '.xlsx', '.xls', '.txt', '.pdf'],
                'parallel_processing': True,
                'max_workers': 4,
                'batch_size': 10,
                'timeout_seconds': 300,
                'upload_directory': str(self.project_root / 'data' / 'uploads'),
                'temp_directory': str(self.project_root / 'data' / 'temp')
            }
            self.logger.warning("File processing config not found, using defaults")
        
        # Ensure directories are absolute paths
        for dir_key in ['upload_directory', 'temp_directory']:
            if dir_key in file_processing_config:
                dir_path = file_processing_config[dir_key]
                if not os.path.isabs(dir_path):
                    file_processing_config[dir_key] = str(self.project_root / dir_path)
        
        return file_processing_config

    def get_agentic_system_config(self) -> Dict[str, Any]:
        """Get agentic system configuration"""
        if self._config is None:
            self.load_config()
        
        agentic_config = self.get("agentic_system", {})
        
        if not agentic_config:
            # Return default configuration
            agentic_config = {
                "min_consensus_threshold": 0.4,
                "min_confidence_threshold": 0.3,
                "min_participating_agents": 2,
                "max_processing_time": 60.0,
                "enable_azure_openai": True,
                "enable_learning": True,
                "auto_store_results": True,
                "enable_vector_search": True,
                "vector_similarity_threshold": 0.6,
                "quality_thresholds": {
                    "high_quality": 0.8,
                    "medium_quality": 0.6,
                    "low_quality": 0.4
                }
            }
            self.logger.warning("Agentic system config not found, using defaults")
        
        return agentic_config

    def validate_vector_db_config(self) -> Dict[str, Any]:
        """Validate vector DB configuration and return status"""
        try:
            vector_config = self.get_vector_db_config()
            validation_results = {
                'valid': True,
                'errors': [],
                'warnings': []
            }
            
            # Check required fields
            required_fields = ['persist_directory', 'embedding_model', 'collection_prefix']
            for field in required_fields:
                if field not in vector_config:
                    validation_results['errors'].append(f"Missing required field: {field}")
                    validation_results['valid'] = False
            
            # Check persist directory
            persist_dir = vector_config.get('persist_directory')
            if persist_dir:
                persist_path = Path(persist_dir)
                if not persist_path.parent.exists():
                    validation_results['warnings'].append(f"Parent directory does not exist: {persist_path.parent}")
            
            # Check similarity threshold
            similarity_threshold = vector_config.get('similarity_threshold', 0.6)
            if not 0.0 <= similarity_threshold <= 1.0:
                validation_results['errors'].append(f"Similarity threshold must be between 0.0 and 1.0, got {similarity_threshold}")
                validation_results['valid'] = False
            
            return validation_results
            
        except Exception as e:
            return {
                'valid': False,
                'errors': [f"Configuration validation failed: {str(e)}"],
                'warnings': []
            }

    def get_learning_config(self) -> Dict[str, Any]:
        """Get learning configuration"""
        if self._config is None:
            self.load_config()
        
        learning_config = self.get("knowledgebase.learning", {})
        
        if not learning_config:
            # Return default configuration
            learning_config = {
                'learning_directory': str(self.project_root / 'data' / 'learning'),
                'auto_ask_feedback': True,
                'min_confidence_for_feedback': 0.8,
                'store_corrections': True,
                'learning_batch_size': 10,
                'retrain_threshold': 50
            }
            self.logger.warning("Learning config not found, using defaults")
        
        # Ensure learning_directory is absolute path
        learning_dir = learning_config.get('learning_directory', 'data/learning')
        if not os.path.isabs(learning_dir):
            learning_config['learning_directory'] = str(self.project_root / learning_dir)
        
        return learning_config


    def get_document_types(self) -> Dict[str, List[str]]:
        """Get supported document types for upload classification"""
        try:
            config = self.load_config()
            kb_config = config.get('knowledgebase', {})
            return kb_config.get('document_types', {})
        except Exception as e:
            logging.error(f"Failed to get document types: {e}")
            return {}



# Example usage and testing
if __name__ == "__main__":
    # Create default config if it doesn't exist
    config_manager = ConfigManager()
    
    if not os.path.exists(config_manager.config_path):
        config_manager.create_default_config()
    
    # Load and test configuration
    try:
        config = config_manager.load_config()
        print("Configuration loaded successfully")
        
        # Test getting specific configs
        duckdb_config = config_manager.get_duckdb_config()
        registry_config = config_manager.get_schema_registry_config()
        
        print(f"DuckDB Path: {duckdb_config.database_path}")
        print(f"Version Strategy: {registry_config.version_strategy}")
        print(f"Compatibility Mode: {registry_config.default_compatibility_mode}")
        
    except Exception as e:
        print(f"Error loading configuration: {e}")