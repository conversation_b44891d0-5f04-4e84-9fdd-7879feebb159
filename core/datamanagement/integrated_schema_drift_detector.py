import json
import os
import logging
from datetime import datetime
from typing import Dict, List, Optional, Union
from pathlib import Path
import sys

# Add project paths
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Import CSV Parser
from utils.csv_extractor import CSVParser, CSVParserError

# Import Schema Drift components
from db_utils.database_factory import DatabaseFactory
from schema_detector import SchemaFingerprinter, DriftAnalyzer, SchemaDrift
from utils.alert_handlers import create_alert_handler

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class IntegratedSchemaDriftDetector:
    """
    Integrated Schema Drift Detector that combines CSV parsing with schema drift detection
    """
    
    def __init__(self, config_path: str):
        """
        Initialize the integrated detector with configuration
        
        Args:
            config_path: Path to configuration file
        """
        # Load configuration
        with open(config_path, 'r') as f:
            self.config = json.load(f)
        
        # Initialize CSV Parser
        self.csv_parser = CSVParser(config_path)
        
        # Initialize database
        self.db_manager = DatabaseFactory.create_database(self.config)
        
        # Initialize schema components
        self.fingerprinter = SchemaFingerprinter(self.config)
        self.drift_analyzer = DriftAnalyzer(self.config)
        self.alert_handler = create_alert_handler(self.config)
        
        logger.info(f"Initialized Integrated Schema Drift Detector")
    
    def parse_and_register_schema(self, file_path: str, version_id: str,
                                 rows_to_sample: Optional[int] = None,
                                 tags: List[str] = None, 
                                 metadata: Dict = None) -> Dict:
        """
        Parse CSV file and register its schema
        
        Args:
            file_path: Path to CSV file
            version_id: Version identifier for the schema
            rows_to_sample: Number of rows to sample for schema extraction
            tags: Optional tags for the schema version
            metadata: Optional metadata for the schema version
            
        Returns:
            Dictionary with parsing results and registration status
        """
        try:
            # Step 1: Parse the CSV file
            logger.info(f"Parsing CSV file: {file_path}")
            parsed_result = self.csv_parser.parse_csv_file(file_path, rows_to_sample)
            
            # Step 2: Generate fingerprint from parsed data
            logger.info("Generating schema fingerprint from parsed data")
            fingerprint, schema_details = self.fingerprinter.generate_fingerprint(parsed_result)
            
            # Check if schema already exists
            existing = self.db_manager.get_schema_version(fingerprint=fingerprint)
            if existing:
                logger.info(f"Schema already registered as version: {existing['version_id']}")
                return {
                    'status': 'already_exists',
                    'version_id': existing['version_id'],
                    'fingerprint': fingerprint,
                    'parsing_result': parsed_result
                }
            
            # Step 3: Register the schema
            version_data = {
                'version_id': version_id,
                'fingerprint': fingerprint,
                'schema_details': schema_details,
                'format_type': 'csv',
                'created_at': datetime.now(),
                'file_example': file_path,
                'tags': tags or [],
                'metadata': {
                    **(metadata or {}),
                    'parser_info': parsed_result['metadata']
                }
            }
            
            if self.db_manager.save_schema_version(version_data):
                logger.info(f"Successfully registered schema version: {version_id}")
                return {
                    'status': 'registered',
                    'version_id': version_id,
                    'fingerprint': fingerprint,
                    'schema_details': schema_details,
                    'parsing_result': parsed_result
                }
            else:
                raise Exception("Failed to save schema version")
                
        except CSVParserError as e:
            logger.error(f"CSV parsing error: {e}")
            return {
                'status': 'error',
                'error_type': 'parsing_error',
                'error_message': str(e)
            }
        except Exception as e:
            logger.error(f"Error in parse_and_register_schema: {e}")
            return {
                'status': 'error',
                'error_type': 'general_error',
                'error_message': str(e)
            }
    
    def parse_and_check_drift(self, file_path: str,
                             expected_version_id: Optional[str] = None,
                             rows_to_sample: Optional[int] = None) -> Dict:
        """
        Parse CSV file and check for schema drift
        
        Args:
            file_path: Path to CSV file to check
            expected_version_id: Expected schema version (None for latest)
            rows_to_sample: Number of rows to sample for schema extraction
            
        Returns:
            Dictionary with parsing results and drift detection results
        """
        start_time = datetime.now()
        
        try:
            # Step 1: Parse the CSV file
            logger.info(f"Parsing CSV file for drift detection: {file_path}")
            parsed_result = self.csv_parser.parse_csv_file(file_path, rows_to_sample)
            
            # Step 2: Generate fingerprint from parsed data
            current_fingerprint, current_schema = self.fingerprinter.generate_fingerprint(parsed_result)
            
            # Step 3: Get expected schema
            if expected_version_id:
                expected = self.db_manager.get_schema_version(version_id=expected_version_id)
            else:
                # Get latest schema for CSV format
                expected = self.db_manager.get_schema_version(format_type='csv')
            
            if not expected:
                logger.warning("No baseline schema found")
                return {
                    'status': 'no_baseline',
                    'message': 'No baseline schema found for comparison',
                    'current_fingerprint': current_fingerprint,
                    'parsing_result': parsed_result
                }
            
            # Step 4: Check for drift
            drifts = []
            if current_fingerprint != expected['fingerprint']:
                # Analyze differences
                expected_schema = json.loads(expected['schema_details'])
                drifts = self.drift_analyzer.analyze_drift(
                    expected_schema,
                    current_schema,
                    file_path
                )
                
                # Log drifts
                for drift in drifts:
                    drift_data = drift.to_dict()
                    drift_data['expected_version_id'] = expected['version_id']
                    drift_data['actual_fingerprint'] = current_fingerprint
                    
                    drift_id = self.db_manager.log_drift(drift_data)
                    logger.info(
                        f"Logged drift {drift_id}: {drift.drift_type} "
                        f"({drift.severity}) in {file_path}"
                    )
            
            # Step 5: Log file processing
            processing_time = int((datetime.now() - start_time).total_seconds() * 1000)
            file_stats = {
                'size': os.path.getsize(file_path),
                'rows': parsed_result['metadata'].get('rows_extracted', 0)
            }
            
            file_data = {
                'file_path': file_path,
                'fingerprint': current_fingerprint,
                'processed_at': datetime.now(),
                'schema_version_id': expected['version_id'] if not drifts else None,
                'file_size_bytes': file_stats['size'],
                'row_count': file_stats['rows'],
                'processing_time_ms': processing_time,
                'processing_status': 'drift_detected' if drifts else 'success',
                'metadata': {
                    'parser_info': parsed_result['metadata']
                }
            }
            
            self.db_manager.log_file_processing(file_data)
            
            # Step 6: Send alerts if configured
            if drifts and self.alert_handler:
                drift_dicts = [drift.to_dict() for drift in drifts]
                alert_success = self.alert_handler.send_alert(drift_dicts)
                
                if alert_success:
                    self.db_manager.log_alert({
                        'drift_log_ids': [d.drift_type for d in drifts],
                        'alert_type': 'composite',
                        'channel': 'multiple',
                        'sent_at': datetime.now(),
                        'success': True
                    })
            
            # Prepare result
            result = {
                'status': 'success',
                'drifts_detected': len(drifts) > 0,
                'drift_count': len(drifts),
                'drifts': [drift.to_dict() for drift in drifts],
                'current_fingerprint': current_fingerprint,
                'expected_version': expected['version_id'],
                'parsing_result': parsed_result,
                'processing_time_ms': processing_time
            }
            
            return result
            
        except CSVParserError as e:
            logger.error(f"CSV parsing error: {e}")
            
            # Log error
            self.db_manager.log_file_processing({
                'file_path': file_path,
                'fingerprint': '',
                'processed_at': datetime.now(),
                'processing_status': 'error',
                'error_message': f"CSV parsing error: {str(e)}",
                'processing_time_ms': int(
                    (datetime.now() - start_time).total_seconds() * 1000
                )
            })
            
            return {
                'status': 'error',
                'error_type': 'parsing_error',
                'error_message': str(e)
            }
            
        except Exception as e:
            logger.error(f"Error in parse_and_check_drift: {e}")
            
            # Log error
            self.db_manager.log_file_processing({
                'file_path': file_path,
                'fingerprint': '',
                'processed_at': datetime.now(),
                'processing_status': 'error',
                'error_message': str(e),
                'processing_time_ms': int(
                    (datetime.now() - start_time).total_seconds() * 1000
                )
            })
            
            return {
                'status': 'error',
                'error_type': 'general_error',
                'error_message': str(e)
            }
    
    def check_parsed_data_drift(self, parsed_data: Dict,
                               expected_version_id: Optional[str] = None) -> Dict:
        """
        Check for schema drift using already parsed data
        
        Args:
            parsed_data: Parsed data dictionary from CSVParser
            expected_version_id: Expected schema version (None for latest)
            
        Returns:
            Dictionary with drift detection results
        """
        try:
            # Generate fingerprint from parsed data
            current_fingerprint, current_schema = self.fingerprinter.generate_fingerprint(parsed_data)
            
            # Get expected schema
            if expected_version_id:
                expected = self.db_manager.get_schema_version(version_id=expected_version_id)
            else:
                expected = self.db_manager.get_schema_version(format_type='csv')
            
            if not expected:
                return {
                    'status': 'no_baseline',
                    'message': 'No baseline schema found for comparison',
                    'current_fingerprint': current_fingerprint
                }
            
            # Check for drift
            drifts = []
            if current_fingerprint != expected['fingerprint']:
                expected_schema = json.loads(expected['schema_details'])
                source_id = parsed_data.get('metadata', {}).get('file_path', 'parsed_data')
                drifts = self.drift_analyzer.analyze_drift(
                    expected_schema,
                    current_schema,
                    source_id
                )
            
            return {
                'status': 'success',
                'drifts_detected': len(drifts) > 0,
                'drift_count': len(drifts),
                'drifts': [drift.to_dict() for drift in drifts],
                'current_fingerprint': current_fingerprint,
                'expected_version': expected['version_id']
            }
            
        except Exception as e:
            logger.error(f"Error checking parsed data drift: {e}")
            return {
                'status': 'error',
                'error_message': str(e)
            }
    
    def batch_parse_and_check(self, file_paths: List[str],
                             expected_version_id: Optional[str] = None,
                             rows_to_sample: Optional[int] = None) -> Dict[str, Dict]:
        """
        Parse and check multiple CSV files for drift
        
        Args:
            file_paths: List of file paths to check
            expected_version_id: Expected schema version
            rows_to_sample: Number of rows to sample
            
        Returns:
            Dictionary mapping file paths to their results
        """
        results = {}
        
        for file_path in file_paths:
            logger.info(f"Processing file: {file_path}")
            try:
                result = self.parse_and_check_drift(
                    file_path, 
                    expected_version_id, 
                    rows_to_sample
                )
                results[file_path] = result
            except Exception as e:
                logger.error(f"Error processing {file_path}: {e}")
                results[file_path] = {
                    'status': 'error',
                    'error_message': str(e)
                }
        
        return results
    
    def get_schema_summary(self, version_id: str) -> Optional[Dict]:
        """
        Get a summary of a registered schema
        
        Args:
            version_id: Schema version ID
            
        Returns:
            Schema summary or None if not found
        """
        schema = self.db_manager.get_schema_version(version_id=version_id)
        if schema:
            schema_details = json.loads(schema['schema_details'])
            return {
                'version_id': schema['version_id'],
                'fingerprint': schema['fingerprint'],
                'format': schema_details.get('format'),
                'columns': schema_details.get('columns', []),
                'column_count': schema_details.get('column_count', 0),
                'created_at': schema['created_at'],
                'source': schema_details.get('source', schema['file_example'])
            }
        return None


def main():
    """Example usage of the integrated detector"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Integrated Schema Drift Detection System')
    parser.add_argument('--config', default='../../config/config.json', help='Configuration file path')
    
    subparsers = parser.add_subparsers(dest='command', help='Commands')
    
    # Parse and register command
    parse_register = subparsers.add_parser('parse-register', 
                                          help='Parse CSV and register schema')
    parse_register.add_argument('file', help='CSV file path')
    parse_register.add_argument('version', help='Version ID')
    parse_register.add_argument('--rows', type=int, help='Rows to sample')
    parse_register.add_argument('--tags', nargs='+', help='Tags')
    
    # Parse and check command
    parse_check = subparsers.add_parser('parse-check', 
                                       help='Parse CSV and check for drift')
    parse_check.add_argument('file', help='CSV file path')
    parse_check.add_argument('--version', help='Expected version ID')
    parse_check.add_argument('--rows', type=int, help='Rows to sample')
    
    # Batch check command
    batch_check = subparsers.add_parser('batch-check', 
                                       help='Check multiple CSV files')
    batch_check.add_argument('files', nargs='+', help='CSV file paths')
    batch_check.add_argument('--version', help='Expected version ID')
    batch_check.add_argument('--rows', type=int, help='Rows to sample')
    
    args = parser.parse_args()
    
    # Initialize detector
    detector = IntegratedSchemaDriftDetector(args.config)
    
    # Execute commands
    if args.command == 'parse-register':
        result = detector.parse_and_register_schema(
            args.file, 
            args.version,
            args.rows,
            args.tags
        )
        print(json.dumps(result, indent=2, default=str))
        
    elif args.command == 'parse-check':
        result = detector.parse_and_check_drift(
            args.file,
            args.version,
            args.rows
        )
        
        if result['status'] == 'success':
            print(f"File: {args.file}")
            print(f"Drifts detected: {result['drifts_detected']}")
            if result['drifts_detected']:
                print(f"Number of drifts: {result['drift_count']}")
                for drift in result['drifts']:
                    print(f"  - {drift['drift_type']} ({drift['severity']}): {drift['details']}")
            else:
                print("No schema drift detected")
        else:
            print(f"Error: {result.get('error_message', 'Unknown error')}")
            
    elif args.command == 'batch-check':
        results = detector.batch_parse_and_check(
            args.files,
            args.version,
            args.rows
        )
        
        for file_path, result in results.items():
            print(f"\nFile: {file_path}")
            if result['status'] == 'success':
                print(f"  Drifts: {result['drift_count']}")
            else:
                print(f"  Error: {result.get('error_message', 'Unknown error')}")
    
    else:
        parser.print_help()


if __name__ == '__main__':
    main()