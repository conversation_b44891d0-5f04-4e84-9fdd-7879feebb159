import hashlib
import json
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass
from pathlib import Path
import re
from abc import ABC, abstractmethod
import logging
import warnings

logger = logging.getLogger(__name__)

@dataclass
class SchemaDrift:
    file_path: str
    timestamp: datetime
    drift_type: str
    details: Dict
    severity: str
    
    def to_dict(self) -> Dict:
        return {
            'file_path': self.file_path,
            'timestamp': self.timestamp,
            'drift_type': self.drift_type,
            'details': self.details,
            'severity': self.severity
        }

@dataclass
class SchemaInfo:
    fingerprint: str
    format_type: str
    schema_details: Dict

class SchemaExtractor(ABC):
    """Abstract base class for schema extraction"""
    
    @abstractmethod
    def extract_schema(self, source: Union[str, Dict]) -> Tuple[str, Dict]:
        """Extract schema and return fingerprint and details"""
        pass
    
    def _calculate_fingerprint(self, schema_dict: Dict, algorithm: str = 'sha256') -> str:
        """Calculate fingerprint from schema dictionary"""
        # Convert numpy types to native Python types
        schema_dict = self._convert_numpy_types(schema_dict)
        schema_str = json.dumps(schema_dict, sort_keys=True)
        
        if algorithm == 'sha256':
            return hashlib.sha256(schema_str.encode()).hexdigest()
        elif algorithm == 'md5':
            return hashlib.md5(schema_str.encode()).hexdigest()
        else:
            raise ValueError(f"Unsupported hash algorithm: {algorithm}")
    
    def _convert_numpy_types(self, obj):
        """Recursively convert numpy types to native Python types"""
        if isinstance(obj, dict):
            return {key: self._convert_numpy_types(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_numpy_types(item) for item in obj]
        elif isinstance(obj, np.bool_):
            return bool(obj)
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif pd.isna(obj):
            return None
        else:
            return obj

class CSVSchemaExtractor(SchemaExtractor):
    """Extract schema from CSV files or parsed CSV data"""
    
    def __init__(self, config: Dict):
        self.sample_size = config.get('sample_size', 1000)
        self.type_inference = config.get('type_inference', {})
    
    def extract_schema(self, source: Union[str, Dict]) -> Tuple[str, Dict]:
        """
        Extract schema from CSV file path or parsed data dictionary
        
        Args:
            source: Either a file path (str) or parsed data dict from CSVParser
            
        Returns:
            Tuple of (fingerprint, schema_details)
        """
        if isinstance(source, str):
            # Original file-based extraction
            return self._extract_from_file(source)
        elif isinstance(source, dict):
            # New: Extract from parsed data dictionary
            return self._extract_from_parsed_data(source)
        else:
            raise ValueError(f"Unsupported source type: {type(source)}")
    
    def _extract_from_file(self, file_path: str) -> Tuple[str, Dict]:
        """Original method to extract schema from file"""
        df = pd.read_csv(file_path, nrows=self.sample_size)
        return self._analyze_dataframe(df, file_path)
    
    def _extract_from_parsed_data(self, parsed_data: Dict) -> Tuple[str, Dict]:
        """Extract schema from parsed CSV data dictionary"""
        data = parsed_data.get('data', [])
        metadata = parsed_data.get('metadata', {})
        
        if not data:
            raise ValueError("No data found in parsed data dictionary")
        
        # Convert list of dicts to DataFrame for analysis
        df = pd.DataFrame(data)
        
        # Use file path from metadata or create a placeholder
        file_path = metadata.get('file_path', 'parsed_data')
        
        # Extract schema with additional metadata
        fingerprint, schema_info = self._analyze_dataframe(df, file_path)
        
        # Enhance schema info with parser metadata
        schema_info['parser_metadata'] = {
            'detected_encoding': metadata.get('detected_encoding'),
            'detected_delimiter': metadata.get('detected_delimiter'),
            'parsing_method': metadata.get('parsing_method'),
            'original_headers': metadata.get('headers', [])
        }
        
        return fingerprint, schema_info
    
    def _analyze_dataframe(self, df: pd.DataFrame, source_identifier: str) -> Tuple[str, Dict]:
        """Analyze DataFrame to extract schema information"""
        schema_info = {
            'format': 'csv',
            'source': source_identifier,
            'columns': list(df.columns),
            'column_count': len(df.columns),
            'column_types': {},
            'column_properties': {},
            'statistics': {}
        }
        
        # Analyze each column
        for col in df.columns:
            # Type inference
            dtype = str(df[col].dtype)
            inferred_type = self._infer_column_type(df[col])
            
            schema_info['column_types'][col] = inferred_type
            
            # Column properties
            props = {
                'nullable': bool(df[col].isnull().any()),  # Convert to native bool
                'unique_count': int(df[col].nunique()),    # Convert to native int
                'null_count': int(df[col].isnull().sum()),
                'null_percentage': float(df[col].isnull().sum() / len(df) * 100)
            }
            
            # Additional properties for numeric columns
            if df[col].dtype in [np.int64, np.float64]:
                non_null_values = df[col].dropna()
                if len(non_null_values) > 0:
                    props.update({
                        'min': float(non_null_values.min()),
                        'max': float(non_null_values.max()),
                        'mean': float(non_null_values.mean()),
                        'std': float(non_null_values.std()) if len(non_null_values) > 1 else 0.0,
                        'is_sequential': bool(self._is_sequential(df[col]))  # Convert to native bool
                    })
            
            # String column properties
            elif df[col].dtype == 'object':
                non_null_values = df[col].dropna()
                if len(non_null_values) > 0:
                    str_lengths = non_null_values.astype(str).str.len()
                    props.update({
                        'min_length': int(str_lengths.min()),
                        'max_length': int(str_lengths.max()),
                        'avg_length': float(str_lengths.mean())
                    })
            
            schema_info['column_properties'][col] = props
        
        # Global statistics
        schema_info['statistics'] = {
            'total_rows': int(len(df)),
            'total_columns': int(len(df.columns)),
            'memory_usage_bytes': int(df.memory_usage(deep=True).sum())
        }
        
        fingerprint = self._calculate_fingerprint(schema_info)
        return fingerprint, schema_info
    
    def _infer_column_type(self, series: pd.Series) -> str:
        """Infer the semantic type of a column"""
        if series.dtype in [np.int64, np.int32]:
            return 'integer'
        elif series.dtype in [np.float64, np.float32]:
            return 'float'
        elif series.dtype == 'bool':
            return 'boolean'
        elif series.dtype == 'object':
            # Check for specific string patterns
            non_null_values = series.dropna()
            if len(non_null_values) == 0:
                return 'string'
            
            # Sample for pattern matching
            sample = non_null_values.head(100).astype(str)
            
            if self.type_inference.get('check_datetime', True):
                # Suppress warnings and try datetime detection more efficiently
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")
                    try:
                        # Try with common date formats first
                        common_formats = [
                            '%Y-%m-%d',
                            '%Y/%m/%d',
                            '%d-%m-%Y',
                            '%d/%m/%Y',
                            '%Y-%m-%d %H:%M:%S',
                            '%Y/%m/%d %H:%M:%S',
                            '%d-%m-%Y %H:%M:%S',
                            '%d/%m/%Y %H:%M:%S'
                        ]
                        
                        # Try each format
                        for fmt in common_formats:
                            try:
                                pd.to_datetime(sample, format=fmt)
                                return 'datetime'
                            except:
                                continue
                        
                        # If no specific format works, try general parsing on a smaller sample
                        try:
                            pd.to_datetime(sample.head(10), infer_datetime_format=True)
                            return 'datetime'
                        except:
                            pass
                    except:
                        pass
            
            if self.type_inference.get('check_email', True):
                if self._is_email_column(sample):
                    return 'email'
            
            if self.type_inference.get('check_url', True):
                if self._is_url_column(sample):
                    return 'url'
            
            if self.type_inference.get('check_phone', True):
                if self._is_phone_column(sample):
                    return 'phone'
            
            # Check if numeric string
            try:
                pd.to_numeric(sample)
                return 'numeric_string'
            except:
                pass
            
            return 'string'
        else:
            return str(series.dtype)
    
    def _is_email_column(self, series: pd.Series) -> bool:
        """Check if column contains emails"""
        email_pattern = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        matches = series.apply(lambda x: bool(email_pattern.match(str(x))))
        return matches.sum() > len(series) * 0.8
    
    def _is_url_column(self, series: pd.Series) -> bool:
        """Check if column contains URLs"""
        url_pattern = re.compile(r'^https?://[^\s]+$')
        matches = series.apply(lambda x: bool(url_pattern.match(str(x))))
        return matches.sum() > len(series) * 0.8
    
    def _is_phone_column(self, series: pd.Series) -> bool:
        """Check if column contains phone numbers"""
        phone_pattern = re.compile(r'^[\d\s\-\(\)\+]+$')
        matches = series.apply(lambda x: bool(phone_pattern.match(str(x))) and len(str(x)) >= 7)
        return matches.sum() > len(series) * 0.8
    
    def _is_sequential(self, series: pd.Series) -> bool:
        """Check if numeric column is sequential (likely an ID)"""
        if len(series) < 2:
            return False
        sorted_vals = series.dropna().sort_values()
        if len(sorted_vals) < 2:
            return False
        diffs = sorted_vals.diff().dropna()
        return bool((diffs == 1).all() or (diffs == -1).all())  # Convert to native bool

class JSONSchemaExtractor(SchemaExtractor):
    """Extract schema from JSON files"""
    
    def __init__(self, config: Dict):
        self.sample_size = config.get('sample_size', 10)
    
    def extract_schema(self, source: Union[str, Dict]) -> Tuple[str, Dict]:
        """Extract schema from JSON file or data"""
        if isinstance(source, str):
            with open(source, 'r') as f:
                data = json.load(f)
        else:
            data = source.get('data', {})
        
        schema_structure = self._extract_structure(data)
        
        schema_info = {
            'format': 'json',
            'root_type': 'array' if isinstance(data, list) else 'object',
            'schema': schema_structure,
            'statistics': self._gather_statistics(data)
        }
        
        fingerprint = self._calculate_fingerprint(schema_info)
        return fingerprint, schema_info
    
    def _extract_structure(self, obj: Any, path: str = 'root') -> Dict:
        """Recursively extract JSON structure"""
        if isinstance(obj, dict):
            properties = {}
            for key in sorted(obj.keys()):
                properties[key] = self._extract_structure(obj[key], f"{path}.{key}")
            
            return {
                'type': 'object',
                'properties': properties,
                'required': sorted(obj.keys())
            }
        
        elif isinstance(obj, list):
            if not obj:
                return {'type': 'array', 'items': {'type': 'null'}}
            
            # Sample items to check homogeneity
            sample_size = min(self.sample_size, len(obj))
            sample_schemas = [
                self._extract_structure(obj[i], f"{path}[{i}]") 
                for i in range(sample_size)
            ]
            
            # Check if all sampled items have same schema
            first_schema = json.dumps(sample_schemas[0], sort_keys=True)
            homogeneous = all(
                json.dumps(s, sort_keys=True) == first_schema 
                for s in sample_schemas
            )
            
            if homogeneous:
                return {
                    'type': 'array',
                    'items': sample_schemas[0],
                    'homogeneous': True,
                    'length': len(obj)
                }
            else:
                return {
                    'type': 'array',
                    'items': {'type': 'mixed', 'schemas': sample_schemas},
                    'homogeneous': False,
                    'length': len(obj)
                }
        
        elif obj is None:
            return {'type': 'null'}
        elif isinstance(obj, bool):
            return {'type': 'boolean'}
        elif isinstance(obj, int):
            return {'type': 'integer'}
        elif isinstance(obj, float):
            return {'type': 'float'}
        elif isinstance(obj, str):
            return {'type': 'string'}
        else:
            return {'type': 'unknown'}
    
    def _gather_statistics(self, data: Any) -> Dict:
        """Gather statistics about JSON data"""
        stats = {
            'total_keys': 0,
            'max_depth': 0,
            'array_count': 0,
            'object_count': 0
        }
        
        def traverse(obj, depth=0):
            stats['max_depth'] = max(stats['max_depth'], depth)
            
            if isinstance(obj, dict):
                stats['object_count'] += 1
                stats['total_keys'] += len(obj)
                for value in obj.values():
                    traverse(value, depth + 1)
            elif isinstance(obj, list):
                stats['array_count'] += 1
                for item in obj:
                    traverse(item, depth + 1)
        
        traverse(data)
        return stats

class ParquetSchemaExtractor(SchemaExtractor):
    """Extract schema from Parquet files"""
    
    def __init__(self, config: Dict):
        self.config = config
    
    def extract_schema(self, source: Union[str, Dict]) -> Tuple[str, Dict]:
        """Extract schema from Parquet file"""
        if isinstance(source, dict):
            raise ValueError("Parquet extraction from parsed data not implemented")
            
        df = pd.read_parquet(source, engine='pyarrow')
        
        # Get Parquet metadata
        import pyarrow.parquet as pq
        parquet_file = pq.ParquetFile(source)
        metadata = parquet_file.metadata
        
        schema_info = {
            'format': 'parquet',
            'columns': list(df.columns),
            'column_count': len(df.columns),
            'column_types': {},
            'parquet_metadata': {
                'num_rows': metadata.num_rows,
                'num_row_groups': metadata.num_row_groups,
                'format_version': metadata.format_version,
                'created_by': metadata.created_by
            }
        }
        
        # Extract column information
        for col in df.columns:
            schema_info['column_types'][col] = str(df[col].dtype)
        
        # Convert numpy types before calculating fingerprint
        schema_info = self._convert_numpy_types(schema_info)
        fingerprint = self._calculate_fingerprint(schema_info)
        return fingerprint, schema_info

class SchemaFingerprinter:
    """Main class for schema fingerprinting"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.extractors = {
            'csv': CSVSchemaExtractor(config['schema_fingerprinting']),
            'tsv': CSVSchemaExtractor(config['schema_fingerprinting']),
            'json': JSONSchemaExtractor(config['schema_fingerprinting']),
            'parquet': ParquetSchemaExtractor(config['schema_fingerprinting'])
        }
    
    def generate_fingerprint(self, source: Union[str, Dict]) -> Tuple[str, Dict]:
        """
        Generate fingerprint for a file or parsed data
        
        Args:
            source: File path (str) or parsed data dictionary
            
        Returns:
            Tuple of (fingerprint, schema_details)
        """
        if isinstance(source, str):
            # File-based fingerprinting
            file_ext = Path(source).suffix[1:].lower()
            
            if file_ext not in self.extractors:
                raise ValueError(f"Unsupported file format: {file_ext}")
            
            extractor = self.extractors[file_ext]
            return extractor.extract_schema(source)
            
        elif isinstance(source, dict):
            # Parsed data fingerprinting
            # Determine format from metadata or default to CSV
            metadata = source.get('metadata', {})
            format_type = metadata.get('format', 'csv')
            
            extractor = self.extractors.get(format_type)
            if extractor is None:
                raise ValueError(f"Extractor not implemented for format: {format_type}")
                
            return extractor.extract_schema(source)
            
        else:
            raise ValueError(f"Unsupported source type: {type(source)}")

class DriftAnalyzer:
    """Analyze schema differences and identify drifts"""
    
    def __init__(self, config: Dict):
        self.config = config
    
    def analyze_drift(self, expected: Dict, current: Dict, source_identifier: str) -> List[SchemaDrift]:
        """
        Analyze differences between expected and current schema
        
        Args:
            expected: Expected schema dictionary
            current: Current schema dictionary
            source_identifier: File path or identifier for the source
            
        Returns:
            List of detected schema drifts
        """
        drifts = []
        timestamp = datetime.now()
        
        # Use source identifier from current schema if available
        if 'source' in current:
            source_identifier = current['source']
        
        # Check format compatibility
        if expected.get('format') != current.get('format'):
            drifts.append(SchemaDrift(
                file_path=source_identifier,
                timestamp=timestamp,
                drift_type='format_changed',
                details={
                    'expected': expected.get('format'),
                    'current': current.get('format')
                },
                severity='critical'
            ))
            return drifts
        
        # Route to specific analyzer
        format_type = expected.get('format')
        if format_type == 'csv':
            drifts.extend(self._analyze_csv_drift(expected, current, source_identifier, timestamp))
        elif format_type == 'json':
            drifts.extend(self._analyze_json_drift(expected, current, source_identifier, timestamp))
        elif format_type == 'parquet':
            drifts.extend(self._analyze_parquet_drift(expected, current, source_identifier, timestamp))
        
        return drifts
    

    def _analyze_csv_drift(self, expected: Dict, current: Dict, 
                          source_identifier: str, timestamp: datetime) -> List[SchemaDrift]:
        """Analyze CSV schema drift"""
        drifts = []
        
        expected_cols = set(expected['columns'])
        current_cols = set(current['columns'])
        
        # Column additions
        added_cols = current_cols - expected_cols
        if added_cols:
            drifts.append(SchemaDrift(
                file_path=source_identifier,
                timestamp=timestamp,
                drift_type='columns_added',
                details={
                    'columns': list(added_cols),
                    'count': len(added_cols),
                    'new_column_types': {
                        col: current['column_types'].get(col) 
                        for col in added_cols
                    }
                },
                severity='medium'
            ))
        
        # Column removals
        removed_cols = expected_cols - current_cols
        if removed_cols:
            drifts.append(SchemaDrift(
                file_path=source_identifier,
                timestamp=timestamp,
                drift_type='columns_removed',
                details={
                    'columns': list(removed_cols),
                    'count': len(removed_cols),
                    'removed_column_types': {
                        col: expected['column_types'].get(col) 
                        for col in removed_cols
                    }
                },
                severity='high'
            ))
        
        # Column order changes
        if expected['columns'] != current['columns']:
            common_cols = [col for col in expected['columns'] if col in current_cols]
            current_order = [col for col in current['columns'] if col in expected_cols]
            
            if common_cols != current_order:
                drifts.append(SchemaDrift(
                    file_path=source_identifier,
                    timestamp=timestamp,
                    drift_type='column_order_changed',
                    details={
                        'expected_order': common_cols,
                        'current_order': current_order,
                        'position_changes': self._calculate_position_changes(
                            common_cols, current_order
                        )
                    },
                    severity='low'
                ))
        
        # Type changes
        for col in expected_cols.intersection(current_cols):
            expected_type = expected['column_types'].get(col)
            current_type = current['column_types'].get(col)
            
            if expected_type != current_type:
                severity = self._get_type_change_severity(expected_type, current_type)
                
                drifts.append(SchemaDrift(
                    file_path=source_identifier,
                    timestamp=timestamp,
                    drift_type='column_type_changed',
                    details={
                        'column': col,
                        'expected_type': expected_type,
                        'current_type': current_type,
                        'compatible': severity != 'critical'
                    },
                    severity=severity
                ))
        
        # Property changes (nullability, uniqueness, etc.)
        for col in expected_cols.intersection(current_cols):
            expected_props = expected.get('column_properties', {}).get(col, {})
            current_props = current.get('column_properties', {}).get(col, {})
            
            prop_changes = []
            
            # Check significant property changes
            if expected_props.get('nullable') != current_props.get('nullable'):
                prop_changes.append({
                    'property': 'nullable',
                    'expected': expected_props.get('nullable'),
                    'current': current_props.get('nullable')
                })
            
            # Check for significant statistical changes
            if expected_props.get('unique_count') and current_props.get('unique_count'):
                expected_unique_ratio = expected_props['unique_count'] / expected.get('statistics', {}).get('total_rows', 1)
                current_unique_ratio = current_props['unique_count'] / current.get('statistics', {}).get('total_rows', 1)
                
                # If unique ratio changed significantly (e.g., from unique to non-unique)
                if abs(expected_unique_ratio - current_unique_ratio) > 0.5:
                    prop_changes.append({
                        'property': 'uniqueness',
                        'expected_ratio': expected_unique_ratio,
                        'current_ratio': current_unique_ratio
                    })
            
            if prop_changes:
                drifts.append(SchemaDrift(
                    file_path=source_identifier,
                    timestamp=timestamp,
                    drift_type='column_properties_changed',
                    details={
                        'column': col,
                        'changes': prop_changes
                    },
                    severity='medium'
                ))
        
        return drifts
    
    def _get_type_change_severity(self, old_type: str, new_type: str) -> str:
        """Determine severity of type change"""
        # Compatible changes (low severity)
        compatible_changes = {
            ('integer', 'float'),
            ('integer', 'numeric_string'),
            ('float', 'numeric_string'),
            ('string', 'email'),
            ('string', 'url'),
            ('string', 'phone')
        }
        
        if (old_type, new_type) in compatible_changes:
            return 'low'
        
        # Semi-compatible (medium severity)
        if old_type in ['integer', 'float'] and new_type == 'string':
            return 'medium'
        
        # Everything else is high severity
        return 'high'
    
    def _calculate_position_changes(self, expected: List[str], 
                                   current: List[str]) -> Dict[str, Dict[str, int]]:
        """Calculate position changes for columns"""
        position_changes = {}
        
        expected_positions = {col: i for i, col in enumerate(expected)}
        current_positions = {col: i for i, col in enumerate(current)}
        
        for col in expected:
            if col in current:
                old_pos = expected_positions[col]
                new_pos = current_positions[col]
                if old_pos != new_pos:
                    position_changes[col] = {
                        'from': old_pos,
                        'to': new_pos,
                        'change': new_pos - old_pos
                    }
        
        return position_changes
    
    # Placeholder methods for other formats
    def _analyze_json_drift(self, expected: Dict, current: Dict,
                           source_identifier: str, timestamp: datetime) -> List[SchemaDrift]:
        """Placeholder for JSON drift analysis"""
        return []
    
    def _analyze_parquet_drift(self, expected: Dict, current: Dict,
                              source_identifier: str, timestamp: datetime) -> List[SchemaDrift]:
        """Placeholder for Parquet drift analysis"""
        return []