import json
import os
import logging
from datetime import datetime
from typing import Dict, List, Optional
from pathlib import Path
import concurrent.futures
import argparse
import sys
import os
from pathlib import Path
import warnings
warnings.filterwarnings('ignore', message='urllib3 v2 only supports OpenSSL')


project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from db_utils.duckdb_impl import DuckDBManager
from schema_detector import SchemaFingerprinter, DriftAnalyzer, SchemaDrift
from utils.alert_handlers import create_alert_handler

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SchemaDriftDetector:
    """Main schema drift detection system"""
    
    def __init__(self, config_path: str):
        # Load configuration
        with open(config_path, 'r') as f:
            self.config = json.load(f)
        
        # Initialize components
        self.db_manager = DuckDBManager(self.config)
        self.fingerprinter = SchemaFingerprinter(self.config)
        self.drift_analyzer = DriftAnalyzer(self.config)
        self.alert_handler = create_alert_handler(self.config)
    
    def register_schema(self, file_path: str, version_id: str, 
                       tags: List[str] = None, metadata: Dict = None) -> str:
        """Register a new schema version"""
        try:
            # Generate fingerprint
            fingerprint, schema_details = self.fingerprinter.generate_fingerprint(file_path)
            
            # Check if already exists
            existing = self.db_manager.get_schema_version(fingerprint=fingerprint)
            if existing:
                logger.info(f"Schema already registered as version: {existing['version_id']}")
                return existing['version_id']
            
            # Prepare version data
            version_data = {
                'version_id': version_id,
                'fingerprint': fingerprint,
                'schema_details': schema_details,
                'format_type': schema_details.get('format', 'unknown'),
                'created_at': datetime.now(),
                'file_example': file_path,
                'tags': tags or [],
                'metadata': metadata or {}
            }
            
            # Save to database
            if self.db_manager.save_schema_version(version_data):
                logger.info(f"Successfully registered schema version: {version_id}")
                return fingerprint
            else:
                raise Exception("Failed to save schema version")
                
        except Exception as e:
            logger.error(f"Error registering schema: {e}")
            raise
    
    def check_file(self, file_path: str, 
                   expected_version_id: Optional[str] = None) -> List[SchemaDrift]:
        """Check a file for schema drift"""
        start_time = datetime.now()
        drifts = []
        
        try:
            # Generate current fingerprint
            current_fingerprint, current_schema = self.fingerprinter.generate_fingerprint(
                file_path
            )
            
            # Get expected schema
            if expected_version_id:
                expected = self.db_manager.get_schema_version(version_id=expected_version_id)
            else:
                # Get latest for this format
                format_type = current_schema.get('format', 'unknown')
                expected = self.db_manager.get_schema_version(format_type=format_type)
            
            if not expected:
                raise ValueError(
                    f"No baseline schema found for format: {current_schema.get('format')}"
                )
            
            # Check for drift
            if current_fingerprint != expected['fingerprint']:
                # Analyze differences
                expected_schema = json.loads(expected['schema_details'])
                drifts = self.drift_analyzer.analyze_drift(
                    expected_schema,
                    current_schema,
                    file_path
                )
                
                # Log drifts
                for drift in drifts:
                    drift_data = drift.to_dict()
                    drift_data['expected_version_id'] = expected['version_id']
                    drift_data['actual_fingerprint'] = current_fingerprint
                    
                    drift_id = self.db_manager.log_drift(drift_data)
                    logger.info(
                        f"Logged drift {drift_id}: {drift.drift_type} "
                        f"({drift.severity}) in {file_path}"
                    )
            
            # Log file processing
            processing_time = int((datetime.now() - start_time).total_seconds() * 1000)
            file_stats = self._get_file_stats(file_path)
            
            file_data = {
                'file_path': file_path,
                'fingerprint': current_fingerprint,
                'processed_at': datetime.now(),
                'schema_version_id': expected['version_id'] if not drifts else None,
                'file_size_bytes': file_stats['size'],
                'row_count': file_stats.get('rows'),
                'processing_time_ms': processing_time,
                'processing_status': 'drift_detected' if drifts else 'success'
            }
            
            self.db_manager.log_file_processing(file_data)
            
            # Send alerts if configured
            if drifts and self.alert_handler:
                drift_dicts = [drift.to_dict() for drift in drifts]
                alert_success = self.alert_handler.send_alert(drift_dicts)
                
                # Log alert status
                if alert_success:
                    drift_ids = [
                        self.db_manager.log_drift(d.to_dict()) 
                        for d in drifts
                    ]
                    self.db_manager.log_alert({
                        'drift_log_ids': drift_ids,
                        'alert_type': 'composite',
                        'channel': 'multiple',
                        'sent_at': datetime.now(),
                        'success': True
                    })
            
            return drifts
            
        except Exception as e:
            logger.error(f"Error checking file {file_path}: {e}")
            
            # Log error
            self.db_manager.log_file_processing({
                'file_path': file_path,
                'fingerprint': '',
                'processed_at': datetime.now(),
                'processing_status': 'error',
                'error_message': str(e),
                'processing_time_ms': int(
                    (datetime.now() - start_time).total_seconds() * 1000
                )
            })
            
            raise
    
    def check_directory(self, directory_path: str, pattern: str = "*",
                       expected_version_id: Optional[str] = None,
                       parallel: bool = True) -> Dict[str, List[SchemaDrift]]:
        """Check all files in a directory for drift"""
        results = {}
        
        # Find files
        path = Path(directory_path)
        files = list(path.glob(pattern))
        
        logger.info(f"Found {len(files)} files to check in {directory_path}")
        
        if parallel and len(files) > 1:
            # Process files in parallel
            max_workers = self.config['file_processing'].get('max_workers', 4)
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_file = {
                    executor.submit(
                        self.check_file, 
                        str(file), 
                        expected_version_id
                    ): file
                    for file in files
                }
                
                for future in concurrent.futures.as_completed(future_to_file):
                    file = future_to_file[future]
                    try:
                        drifts = future.result()
                        results[str(file)] = drifts
                    except Exception as e:
                        logger.error(f"Error processing {file}: {e}")
                        results[str(file)] = []
        else:
            # Process files sequentially
            for file in files:
                try:
                    drifts = self.check_file(str(file), expected_version_id)
                    results[str(file)] = drifts
                except Exception as e:
                    logger.error(f"Error processing {file}: {e}")
                    results[str(file)] = []
        
        return results
    
    def resolve_drifts(self, file_path: str, resolution_notes: str) -> int:
        """Mark drifts as resolved for a file"""
        count = self.db_manager.mark_drifts_resolved(file_path, resolution_notes)
        logger.info(f"Resolved {count} drifts for {file_path}")
        return count
    
    def get_drift_report(self, days: int = 7) -> Dict:
        """Get drift report for the specified period"""
        return self.db_manager.get_analytics_summary(days)
    
    def cleanup_old_data(self) -> Dict[str, int]:
        """Clean up old data based on retention policy"""
        retention_days = self.config['monitoring'].get('retention_days', 90)
        return self.db_manager.cleanup_old_data(retention_days)
    
    def _get_file_stats(self, file_path: str) -> Dict:
        """Get basic file statistics"""
        stats = {'size': os.path.getsize(file_path)}
        
        # Try to get row count for CSV/Parquet
        try:
            ext = Path(file_path).suffix[1:].lower()
            if ext in ['csv', 'tsv']:
                import pandas as pd
                df = pd.read_csv(file_path, nrows=1)
                # Count lines (rough estimate)
                with open(file_path, 'r') as f:
                    stats['rows'] = sum(1 for _ in f) - 1  # Subtract header
            elif ext == 'parquet':
                import pandas as pd
                df = pd.read_parquet(file_path)
                stats['rows'] = len(df)
        except:
            pass
        
        return stats

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='Schema Drift Detection System')
    parser.add_argument('--config', default='config.json', help='Configuration file path')
    
    subparsers = parser.add_subparsers(dest='command', help='Commands')
    
    # Register command
    register_parser = subparsers.add_parser('register', help='Register a schema')
    register_parser.add_argument('file', help='File path')
    register_parser.add_argument('version', help='Version ID')
    register_parser.add_argument('--tags', nargs='+', help='Tags')
    
    # Check command
    check_parser = subparsers.add_parser('check', help='Check for drift')
    check_parser.add_argument('path', help='File or directory path')
    check_parser.add_argument('--version', help='Expected version ID')
    check_parser.add_argument('--pattern', default='*', help='File pattern for directories')
    
    # Report command
    report_parser = subparsers.add_parser('report', help='Generate drift report')
    report_parser.add_argument('--days', type=int, default=7, help='Days to include')
    
    # Resolve command
    resolve_parser = subparsers.add_parser('resolve', help='Resolve drifts')
    resolve_parser.add_argument('file', help='File path')
    resolve_parser.add_argument('notes', help='Resolution notes')
    
    args = parser.parse_args()
    
    # Initialize detector
    detector = SchemaDriftDetector(args.config)
    
    # Execute command
    if args.command == 'register':
        fingerprint = detector.register_schema(args.file, args.version, args.tags)
        print(f"Schema registered successfully. Fingerprint: {fingerprint}")
    
    elif args.command == 'check':
        path = Path(args.path)
        if path.is_file():
            drifts = detector.check_file(str(path), args.version)
            if drifts:
                print(f"Found {len(drifts)} drifts:")
                for drift in drifts:
                    print(f"  - {drift.drift_type} ({drift.severity}): {drift.details}")
            else:
                print("No drift detected")
        else:
            results = detector.check_directory(str(path), args.pattern, args.version)
            total_drifts = sum(len(drifts) for drifts in results.values())
            print(f"Checked {len(results)} files, found {total_drifts} total drifts")
    
    elif args.command == 'report':
        report = detector.get_drift_report(args.days)
        print(json.dumps(report, indent=2, default=str))
    
    elif args.command == 'resolve':
        count = detector.resolve_drifts(args.file, args.notes)
        print(f"Resolved {count} drifts")
    
    else:
        parser.print_help()

if __name__ == '__main__':
    main()