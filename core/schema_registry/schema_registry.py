"""
Schema Registry Manager
Main orchestrator for schema registration, drift detection, and compatibility checking
"""

import json
import sys
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).resolve().parent.parent.parent
sys.path.insert(0, str(project_root))


from core.config_manager import ConfigManager

from db_utils.duckdb_manager import DuckDBManager
from core.schema_registry.compatibility_checker import Compatibility<PERSON><PERSON><PERSON>, CompatibilityMode, DriftAnalysis
from core.schema_registry.version_manager import (
    <PERSON><PERSON><PERSON><PERSON>, 
    <PERSON>hemaHasher, 
    RegistryKeyManager,
    SchemaComparator
)


@dataclass
class RegistrationResult:
    status: str  # "SUCCESS", "WARNING", "ERROR"
    registry_key: str
    schema_exists: bool
    versions: Dict[str, Optional[str]]
    drift_analysis: Optional[DriftAnalysis]
    recommendations: List[str]
    database_info: Dict[str, Any]
    message: str


class SchemaRegistryManager:
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.db_manager = DuckDBManager(config_manager)
        self.compatibility_checker = CompatibilityChecker(
            similarity_threshold=config_manager.get_schema_registry_config().similarity_threshold
        )
        self.registry_config = config_manager.get_schema_registry_config()
    
    def register_schema_with_drift_detection(self, pipeline_id: str, file_name: str, 
                                           new_schema: Dict[str, Any],
                                           created_by: Optional[str] = None,
                                           force_registration: bool = False) -> RegistrationResult:
        """
        Main entry point for schema registration with comprehensive drift detection
        """
        try:
            # Generate registry key
            registry_key = RegistryKeyManager.generate_registry_key(pipeline_id, file_name)
            
            # Check if schema already exists
            existing_registry = self.db_manager.get_schema_registry(registry_key)
            
            if existing_registry:
                return self._handle_existing_schema(
                    registry_key, new_schema, created_by, force_registration
                )
            else:
                return self._handle_new_schema(
                    registry_key, pipeline_id, file_name, new_schema, created_by
                )
                
        except Exception as e:
            return self._create_error_result(
                registry_key if 'registry_key' in locals() else f"{pipeline_id}::{file_name}",
                f"Error during schema registration: {str(e)}"
            )
    
    def _handle_existing_schema(self, registry_key: str, new_schema: Dict[str, Any],
                               created_by: Optional[str], force_registration: bool) -> RegistrationResult:
        """Handle registration when schema already exists"""
        
        # Get current schema
        current_version_info = self.db_manager.get_latest_schema_version(registry_key)
        if not current_version_info:
            return self._create_error_result(registry_key, "Could not retrieve current schema version")
        
        current_schema = json.loads(current_version_info["schema_json"])
        
        # Check if schemas are identical
        current_hash = SchemaHasher.calculate_schema_hash(current_schema)
        new_hash = SchemaHasher.calculate_schema_hash(new_schema)
        
        if current_hash == new_hash:
            return self._create_success_result(
                registry_key=registry_key,
                schema_exists=True,
                previous_version=current_version_info["version"],
                current_version=current_version_info["version"],
                drift_analysis=None,
                message="Schema is identical to current version. No changes required."
            )
        
        # Perform drift analysis
        compatibility_mode = CompatibilityMode(self.registry_config.default_compatibility_mode)
        drift_analysis = self.compatibility_checker.analyze_drift(
            current_schema, new_schema, compatibility_mode
        )
        
        # Determine if registration should proceed
        should_register, status_message = self._should_register_schema(
            drift_analysis, force_registration, compatibility_mode
        )
        
        if should_register:
            # Register new version
            new_version = self._register_new_version(
                registry_key, new_schema, current_version_info["version"], 
                drift_analysis, created_by
            )
            
            if new_version:
                return self._create_success_result(
                    registry_key=registry_key,
                    schema_exists=True,
                    previous_version=current_version_info["version"],
                    current_version=new_version,
                    drift_analysis=drift_analysis,
                    message=status_message
                )
            else:
                return self._create_error_result(registry_key, "Failed to register new schema version")
        else:
            # Registration blocked due to breaking changes
            return self._create_warning_result(
                registry_key=registry_key,
                schema_exists=True,
                current_version=current_version_info["version"],
                drift_analysis=drift_analysis,
                message=status_message
            )
    
    def _handle_new_schema(self, registry_key: str, pipeline_id: str, file_name: str,
                          new_schema: Dict[str, Any], created_by: Optional[str]) -> RegistrationResult:
        """Handle first-time schema registration"""
        
        # Generate initial version
        initial_version = VersionManager.generate_version(self.registry_config.version_strategy)
        
        # Register schema registry entry
        if not self.db_manager.insert_schema_registry(registry_key, pipeline_id, file_name, initial_version):
            return self._create_error_result(registry_key, "Failed to create schema registry entry")
        
        # Register initial schema version
        schema_json = json.dumps(new_schema, sort_keys=True)
        schema_hash = SchemaHasher.calculate_schema_hash(new_schema)
        
        if not self.db_manager.insert_schema_version(
            registry_key, initial_version, schema_json, schema_hash, None, created_by
        ):
            return self._create_error_result(registry_key, "Failed to register initial schema version")
        
        # Log initial registration
        self.db_manager.insert_drift_log(
            registry_key, None, initial_version, "COMPATIBLE", 
            json.dumps({"message": "Initial schema registration"}), None
        )
        
        return self._create_success_result(
            registry_key=registry_key,
            schema_exists=False,
            previous_version=None,
            current_version=initial_version,
            drift_analysis=None,
            message="Schema registered successfully for the first time."
        )
    
    def _should_register_schema(self, drift_analysis: DriftAnalysis, force_registration: bool,
                               compatibility_mode: CompatibilityMode) -> Tuple[bool, str]:
        """Determine if schema registration should proceed based on compatibility"""
        
        if not drift_analysis.drift_detected:
            return True, "No changes detected."
        
        result = drift_analysis.compatibility_result
        
        # Force registration overrides compatibility checks
        if force_registration:
            return True, "Registration forced despite compatibility issues."
        
        # Check based on compatibility mode
        if compatibility_mode == CompatibilityMode.STRICT:
            if result.breaking_changes:
                return False, "Registration blocked: Breaking changes detected in STRICT mode."
            if result.warnings:
                return False, "Registration blocked: Warnings detected in STRICT mode."
            return True, "Schema changes are compatible."
        
        elif compatibility_mode == CompatibilityMode.GRACEFUL:
            if result.breaking_changes:
                return False, "Registration blocked: Breaking changes detected in GRACEFUL mode."
            return True, "Schema changes accepted with warnings in GRACEFUL mode."
        
        else:  # PERMISSIVE
            return True, "Schema changes accepted in PERMISSIVE mode."
    
    def _register_new_version(self, registry_key: str, new_schema: Dict[str, Any],
                             current_version: str, drift_analysis: DriftAnalysis,
                             created_by: Optional[str]) -> Optional[str]:
        """Register a new schema version"""
        
        # Determine version increment based on change type
        change_type = "patch"
        if drift_analysis.compatibility_result.breaking_changes:
            change_type = "major"
        elif drift_analysis.change_summary.get("added_columns") or drift_analysis.change_summary.get("modified_columns"):
            change_type = "minor"
        
        # Generate new version
        new_version = VersionManager.generate_version(
            self.registry_config.version_strategy, current_version, change_type
        )
        
        # Prepare schema data
        schema_json = json.dumps(new_schema, sort_keys=True)
        schema_hash = SchemaHasher.calculate_schema_hash(new_schema)
        compatibility_info = json.dumps(self.compatibility_checker.generate_compatibility_report(drift_analysis))
        
        # Insert new version
        if not self.db_manager.insert_schema_version(
            registry_key, new_version, schema_json, schema_hash, compatibility_info, created_by
        ):
            return None
        
        # Update registry current version
        if not self.db_manager.update_schema_registry_version(registry_key, new_version):
            return None
        
        # Log drift information
        changes_json = json.dumps(drift_analysis.change_summary)
        compatibility_report = json.dumps(self.compatibility_checker.generate_compatibility_report(drift_analysis))
        
        self.db_manager.insert_drift_log(
            registry_key, current_version, new_version, 
            drift_analysis.compatibility_result.drift_type.value,
            changes_json, compatibility_report
        )
        
        return new_version
    
    def get_schema(self, pipeline_id: str, file_name: str, version: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Get schema by pipeline ID and file name"""
        registry_key = RegistryKeyManager.generate_registry_key(pipeline_id, file_name)
        
        if version:
            version_info = self.db_manager.get_schema_version(registry_key, version)
        else:
            version_info = self.db_manager.get_latest_schema_version(registry_key)
        
        if version_info:
            return {
                "registry_key": registry_key,
                "version": version_info["version"],
                "schema": json.loads(version_info["schema_json"]),
                "created_at": version_info["created_at"],
                "created_by": version_info["created_by"]
            }
        
        return None
    
    def list_schema_versions(self, pipeline_id: str, file_name: str) -> List[Dict[str, Any]]:
        """List all versions of a schema"""
        registry_key = RegistryKeyManager.generate_registry_key(pipeline_id, file_name)
        versions = self.db_manager.get_all_versions(registry_key)
        
        return [
            {
                "version": v["version"],
                "created_at": v["created_at"],
                "created_by": v["created_by"],
                "schema_hash": v["schema_hash"]
            }
            for v in versions
        ]
    
    def get_drift_history(self, pipeline_id: str, file_name: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get drift history for a schema"""
        registry_key = RegistryKeyManager.generate_registry_key(pipeline_id, file_name)
        return self.db_manager.get_drift_history(registry_key, limit)
    
    def compare_versions(self, pipeline_id: str, file_name: str, 
                        version1: str, version2: str) -> Optional[Dict[str, Any]]:
        """Compare two versions of a schema"""
        registry_key = RegistryKeyManager.generate_registry_key(pipeline_id, file_name)
        
        v1_info = self.db_manager.get_schema_version(registry_key, version1)
        v2_info = self.db_manager.get_schema_version(registry_key, version2)
        
        if not v1_info or not v2_info:
            return None
        
        schema1 = json.loads(v1_info["schema_json"])
        schema2 = json.loads(v2_info["schema_json"])
        
        changes = SchemaComparator.compare_schemas(schema1, schema2)
        
        return {
            "version1": version1,
            "version2": version2,
            "changes": changes,
            "created_at1": v1_info["created_at"],
            "created_at2": v2_info["created_at"]
        }
    
    def cleanup_old_versions(self) -> int:
        """Clean up old schema versions based on retention policy"""
        retention_policy = self.registry_config.retention_policy
        return self.db_manager.cleanup_old_versions(
            retention_policy["max_versions"],
            retention_policy["max_age_days"]
        )
    
    def get_registry_statistics(self) -> Dict[str, Any]:
        """Get comprehensive registry statistics"""
        db_stats = self.db_manager.get_registry_statistics()
        
        return {
            **db_stats,
            "config": {
                "version_strategy": self.registry_config.version_strategy,
                "compatibility_mode": self.registry_config.default_compatibility_mode,
                "similarity_threshold": self.registry_config.similarity_threshold
            }
        }
    
    def _create_success_result(self, registry_key: str, schema_exists: bool,
                              previous_version: Optional[str], current_version: str,
                              drift_analysis: Optional[DriftAnalysis], message: str) -> RegistrationResult:
        """Create a successful registration result"""
        
        recommendations = []
        if drift_analysis and self.registry_config.enable_recommendations:
            compatibility_report = self.compatibility_checker.generate_compatibility_report(drift_analysis)
            recommendations = compatibility_report.get("recommendations", [])
        
        return RegistrationResult(
            status="SUCCESS",
            registry_key=registry_key,
            schema_exists=schema_exists,
            versions={
                "previous": previous_version,
                "current": current_version
            },
            drift_analysis=drift_analysis,
            recommendations=recommendations,
            database_info=self._get_database_info(),
            message=message
        )
    
    def _create_warning_result(self, registry_key: str, schema_exists: bool,
                              current_version: str, drift_analysis: DriftAnalysis,
                              message: str) -> RegistrationResult:
        """Create a warning registration result"""
        
        recommendations = []
        if self.registry_config.enable_recommendations:
            compatibility_report = self.compatibility_checker.generate_compatibility_report(drift_analysis)
            recommendations = compatibility_report.get("recommendations", [])
        
        return RegistrationResult(
            status="WARNING",
            registry_key=registry_key,
            schema_exists=schema_exists,
            versions={
                "previous": current_version,
                "current": current_version  # No new version created
            },
            drift_analysis=drift_analysis,
            recommendations=recommendations,
            database_info=self._get_database_info(),
            message=message
        )
    
    def _create_error_result(self, registry_key: str, message: str) -> RegistrationResult:
        """Create an error registration result"""
        return RegistrationResult(
            status="ERROR",
            registry_key=registry_key,
            schema_exists=False,
            versions={"previous": None, "current": None},
            drift_analysis=None,
            recommendations=[],
            database_info=self._get_database_info(),
            message=message
        )
    
    def _get_database_info(self) -> Dict[str, Any]:
        """Get current database information"""
        duckdb_config = self.config_manager.get_duckdb_config()
        stats = self.db_manager.get_registry_statistics()
        
        return {
            "duckdb_path": duckdb_config.database_path,
            "total_schemas": stats.get("total_registries", 0),
            "total_versions": stats.get("total_versions", 0)
        }


# Example usage and testing
if __name__ == "__main__":
    import os
    
    # Initialize components
    config_manager = ConfigManager()
    if not os.path.exists(config_manager.config_path):
        config_manager.create_default_config()
    
    config_manager.load_config()
    registry_manager = SchemaRegistryManager(config_manager)
    
    # Test schema registration
    test_schema = {
        "file_name": "users.csv",
        "schema": {
            "id": "integer",
            "name": "string",
            "email": "email",
            "age": "integer"
        }
    }
    
    print("=== Schema Registration Test ===")
    
    # Register initial schema
    result = registry_manager.register_schema_with_drift_detection(
        "test_pipeline", "users.csv", test_schema, "test_user"
    )
    
    print(f"Registration result: {result.status}")
    print(f"Registry key: {result.registry_key}")
    print(f"Version: {result.versions['current']}")
    print(f"Message: {result.message}")
    
    # Register modified schema
    modified_schema = {
        "file_name": "users.csv",
        "schema": {
            "id": "integer",
            "full_name": "string",  # Renamed from 'name'
            "email": "email",
            "age": "string",        # Type changed
            "phone": "phone"        # New column
        }
    }
    
    print("\n=== Modified Schema Registration Test ===")
    
    result2 = registry_manager.register_schema_with_drift_detection(
        "test_pipeline", "users.csv", modified_schema, "test_user"
    )
    
    print(f"Registration result: {result2.status}")
    print(f"Previous version: {result2.versions['previous']}")
    print(f"Current version: {result2.versions['current']}")
    print(f"Message: {result2.message}")
    
    if result2.drift_analysis:
        print(f"Drift detected: {result2.drift_analysis.drift_detected}")
        print(f"Total changes: {result2.drift_analysis.change_summary['total_changes']}")
        print("Breaking changes:")
        for change in result2.drift_analysis.compatibility_result.breaking_changes:
            print(f"  - {change}")
    
    print("\nRecommendations:")
    for rec in result2.recommendations:
        print(f"  - {rec}")
    
    # Test getting schema
    print("\n=== Schema Retrieval Test ===")
    current_schema = registry_manager.get_schema("test_pipeline", "users.csv")
    if current_schema:
        print(f"Current version: {current_schema['version']}")
        print(f"Schema: {current_schema['schema']}")
    
    # Test statistics
    print("\n=== Registry Statistics ===")
    stats = registry_manager.get_registry_statistics()
    print(f"Total schemas: {stats['total_registries']}")
    print(f"Total versions: {stats['total_versions']}")
    print(f"Configuration: {stats['config']}")
    
    # Close database connection
    registry_manager.db_manager.close_connection()