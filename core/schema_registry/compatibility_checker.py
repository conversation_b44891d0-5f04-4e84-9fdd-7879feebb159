"""
Compatibility Checker for Schema Registry
Handles drift detection and compatibility analysis
"""

from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).resolve().parent.parent.parent
sys.path.insert(0, str(project_root))

from core.schema_registry.version_manager import (
    SchemaComparator, 
    SimilarityCalculator, 
    TypeCompatibilityChecker,
    MigrationSuggester,
    SchemaHasher
)


class CompatibilityMode(Enum):
    STRICT = "strict"
    GRACEFUL = "graceful"
    PERMISSIVE = "permissive"


class DriftType(Enum):
    COMPATIBLE = "COMPATIBLE"
    BREAKING = "BREAKING"
    WARNING = "WARNING"


@dataclass
class CompatibilityResult:
    forward_compatible: bool
    backward_compatible: bool
    breaking_changes: List[str]
    safe_changes: List[str]
    warnings: List[str]
    migration_required: bool
    drift_type: DriftType
    risk_level: str  # "LOW", "MEDIUM", "HIGH"


@dataclass
class DriftAnalysis:
    drift_detected: bool
    change_summary: Dict[str, Any]
    potential_renames: List[Dict[str, Any]]
    compatibility_result: CompatibilityResult
    migration_suggestions: List[str]


class CompatibilityChecker:
    
    def __init__(self, similarity_threshold: float = 0.8):
        self.similarity_threshold = similarity_threshold
    
    def analyze_drift(self, old_schema: Dict[str, Any], new_schema: Dict[str, Any],
                     compatibility_mode: CompatibilityMode = CompatibilityMode.GRACEFUL) -> DriftAnalysis:
        """Perform comprehensive drift analysis between two schemas"""
        
        # Check if schemas are identical
        old_hash = SchemaHasher.calculate_schema_hash(old_schema)
        new_hash = SchemaHasher.calculate_schema_hash(new_schema)
        
        if old_hash == new_hash:
            return self._create_no_drift_analysis()
        
        # Compare schemas to detect changes
        changes = SchemaComparator.compare_schemas(old_schema, new_schema)
        
        # Detect potential renames
        potential_renames = SimilarityCalculator.detect_potential_renames(
            old_schema, new_schema, self.similarity_threshold
        )
        
        # Analyze compatibility
        compatibility_result = self._analyze_compatibility(old_schema, new_schema, changes, compatibility_mode)
        
        # Generate migration suggestions
        migration_suggestions = []
        if compatibility_result.migration_required:
            migration_suggestions = MigrationSuggester.generate_migration_suggestions(
                changes, old_schema, new_schema
            )
        
        return DriftAnalysis(
            drift_detected=True,
            change_summary=self._create_change_summary(changes, potential_renames),
            potential_renames=potential_renames,
            compatibility_result=compatibility_result,
            migration_suggestions=migration_suggestions
        )
    
    def _create_no_drift_analysis(self) -> DriftAnalysis:
        """Create analysis result for no drift detected"""
        return DriftAnalysis(
            drift_detected=False,
            change_summary={
                "added_columns": [],
                "removed_columns": [],
                "modified_columns": [],
                "renamed_columns": []
            },
            potential_renames=[],
            compatibility_result=CompatibilityResult(
                forward_compatible=True,
                backward_compatible=True,
                breaking_changes=[],
                safe_changes=[],
                warnings=[],
                migration_required=False,
                drift_type=DriftType.COMPATIBLE,
                risk_level="NONE"
            ),
            migration_suggestions=[]
        )
    
    def _create_change_summary(self, changes: Dict[str, Any], 
                              potential_renames: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Create a comprehensive change summary"""
        return {
            "added_columns": changes.get("added_columns", []),
            "removed_columns": changes.get("removed_columns", []),
            "modified_columns": changes.get("modified_columns", []),
            "renamed_columns": potential_renames,
            "total_changes": changes.get("total_changes", 0),
            "unchanged_columns": changes.get("unchanged_columns", [])
        }
    
    def _analyze_compatibility(self, old_schema: Dict[str, Any], new_schema: Dict[str, Any],
                              changes: Dict[str, Any], mode: CompatibilityMode) -> CompatibilityResult:
        """Analyze forward and backward compatibility"""
        
        breaking_changes = []
        safe_changes = []
        warnings = []
        
        # Analyze added columns
        for column in changes.get("added_columns", []):
            safe_changes.append(f"Added new column: {column}")
        
        # Analyze removed columns
        for column in changes.get("removed_columns", []):
            breaking_changes.append(f"Removed column: {column}")
        
        # Analyze modified columns
        for mod in changes.get("modified_columns", []):
            column = mod["column"]
            old_type = mod["old_type"]
            new_type = mod["new_type"]
            
            risk = TypeCompatibilityChecker.get_type_compatibility_risk(old_type, new_type)
            
            if risk == "HIGH":
                breaking_changes.append(f"High-risk type change: {column} ({old_type} → {new_type})")
            elif risk == "MEDIUM":
                warnings.append(f"Medium-risk type change: {column} ({old_type} → {new_type})")
            else:
                safe_changes.append(f"Low-risk type change: {column} ({old_type} → {new_type})")
        
        # Determine compatibility
        forward_compatible = self._check_forward_compatibility(changes)
        backward_compatible = self._check_backward_compatibility(changes)
        
        # Determine if migration is required
        migration_required = len(breaking_changes) > 0
        
        # Determine drift type based on compatibility mode
        drift_type = self._determine_drift_type(breaking_changes, warnings, mode)
        
        # Determine overall risk level
        risk_level = self._determine_risk_level(breaking_changes, warnings)
        
        return CompatibilityResult(
            forward_compatible=forward_compatible,
            backward_compatible=backward_compatible,
            breaking_changes=breaking_changes,
            safe_changes=safe_changes,
            warnings=warnings,
            migration_required=migration_required,
            drift_type=drift_type,
            risk_level=risk_level
        )
    
    def _check_forward_compatibility(self, changes: Dict[str, Any]) -> bool:
        """Check if new schema can read old data (forward compatibility)"""
        # Forward compatibility is broken if:
        # 1. Required columns are removed
        # 2. Types are changed in incompatible ways
        
        removed_columns = changes.get("removed_columns", [])
        if removed_columns:
            return False  # Can't read old data if columns are missing
        
        # Check type changes
        for mod in changes.get("modified_columns", []):
            old_type = mod["old_type"]
            new_type = mod["new_type"]
            
            # If type change is not safe, forward compatibility is broken
            if not TypeCompatibilityChecker.is_type_transition_safe(old_type, new_type):
                return False
        
        return True
    
    def _check_backward_compatibility(self, changes: Dict[str, Any]) -> bool:
        """Check if old schema can read new data (backward compatibility)"""
        # Backward compatibility is broken if:
        # 1. New required columns are added
        # 2. Types are changed in ways that old schema can't handle
        
        # For simplicity, assume added columns don't break backward compatibility
        # (they would just be ignored by old schema)
        
        # Check type changes
        for mod in changes.get("modified_columns", []):
            old_type = mod["old_type"]
            new_type = mod["new_type"]
            
            # Backward compatibility is more permissive
            # Only break if new type can't be converted back to old type
            if old_type == "integer" and new_type == "string":
                return False  # String can't always be converted to integer
            elif old_type == "number" and new_type == "string":
                return False  # String can't always be converted to number
        
        return True
    
    def _determine_drift_type(self, breaking_changes: List[str], warnings: List[str], 
                             mode: CompatibilityMode) -> DriftType:
        """Determine drift type based on changes and compatibility mode"""
        
        if breaking_changes:
            if mode == CompatibilityMode.STRICT:
                return DriftType.BREAKING
            elif mode == CompatibilityMode.GRACEFUL:
                return DriftType.WARNING
            else:  # PERMISSIVE
                return DriftType.COMPATIBLE
        
        if warnings:
            return DriftType.WARNING
        
        return DriftType.COMPATIBLE
    
    def _determine_risk_level(self, breaking_changes: List[str], warnings: List[str]) -> str:
        """Determine overall risk level"""
        if breaking_changes:
            return "HIGH"
        elif warnings:
            return "MEDIUM"
        else:
            return "LOW"
    
    def check_forward_compatibility_only(self, old_schema: Dict[str, Any], 
                                        new_schema: Dict[str, Any]) -> bool:
        """Quick check for forward compatibility only"""
        changes = SchemaComparator.compare_schemas(old_schema, new_schema)
        return self._check_forward_compatibility(changes)
    
    def check_backward_compatibility_only(self, old_schema: Dict[str, Any], 
                                         new_schema: Dict[str, Any]) -> bool:
        """Quick check for backward compatibility only"""
        changes = SchemaComparator.compare_schemas(old_schema, new_schema)
        return self._check_backward_compatibility(changes)
    
    def generate_compatibility_report(self, drift_analysis: DriftAnalysis) -> Dict[str, Any]:
        """Generate a comprehensive compatibility report"""
        
        result = drift_analysis.compatibility_result
        
        report = {
            "summary": {
                "drift_detected": drift_analysis.drift_detected,
                "drift_type": result.drift_type.value,
                "risk_level": result.risk_level,
                "forward_compatible": result.forward_compatible,
                "backward_compatible": result.backward_compatible,
                "migration_required": result.migration_required
            },
            "changes": drift_analysis.change_summary,
            "potential_renames": drift_analysis.potential_renames,
            "breaking_changes": result.breaking_changes,
            "safe_changes": result.safe_changes,
            "warnings": result.warnings,
            "migration_suggestions": drift_analysis.migration_suggestions,
            "recommendations": self._generate_recommendations(drift_analysis)
        }
        
        return report
    
    def _generate_recommendations(self, drift_analysis: DriftAnalysis) -> List[str]:
        """Generate actionable recommendations based on drift analysis"""
        recommendations = []
        result = drift_analysis.compatibility_result
        
        if not drift_analysis.drift_detected:
            recommendations.append("No changes detected. Schema is identical.")
            return recommendations
        
        if result.drift_type == DriftType.BREAKING:
            recommendations.append("CRITICAL: Breaking changes detected. Review and plan migration carefully.")
            recommendations.append("Consider rolling back changes or implementing gradual migration strategy.")
        
        elif result.drift_type == DriftType.WARNING:
            recommendations.append("WARNING: Potentially problematic changes detected.")
            recommendations.append("Test thoroughly before deploying to production.")
        
        else:
            recommendations.append("Changes appear safe. Proceed with normal deployment process.")
        
        # Specific recommendations for renames
        if drift_analysis.potential_renames:
            recommendations.append("Potential column renames detected. Verify if these are intentional:")
            for rename in drift_analysis.potential_renames[:3]:  # Show top 3
                recommendations.append(f"  - {rename['from']} → {rename['to']} (confidence: {rename['confidence']})")
        
        # Recommendations for type changes
        type_changes = [mod for mod in drift_analysis.change_summary.get("modified_columns", []) 
                       if TypeCompatibilityChecker.get_type_compatibility_risk(mod["old_type"], mod["new_type"]) == "HIGH"]
        
        if type_changes:
            recommendations.append("High-risk type changes detected. Consider:")
            recommendations.append("  - Data validation scripts before migration")
            recommendations.append("  - Backup strategies for affected columns")
            recommendations.append("  - Rollback procedures if migration fails")
        
        return recommendations


# Example usage and testing
if __name__ == "__main__":
    # Test compatibility checker
    checker = CompatibilityChecker(similarity_threshold=0.8)
    
    # Test schemas
    old_schema = {
        "file_name": "users.csv",
        "schema": {
            "user_id": "integer",
            "name": "string",
            "age": "integer",
            "email": "email"
        }
    }
    
    new_schema = {
        "file_name": "users.csv", 
        "schema": {
            "user_id": "integer",
            "full_name": "string",  # Potential rename from 'name'
            "age": "string",        # Type change
            "email": "email",
            "phone": "phone"        # New column
        }
    }
    
    print("=== Drift Analysis Test ===")
    
    # Test different compatibility modes
    for mode in [CompatibilityMode.STRICT, CompatibilityMode.GRACEFUL, CompatibilityMode.PERMISSIVE]:
        print(f"\n--- {mode.value.upper()} MODE ---")
        
        drift_analysis = checker.analyze_drift(old_schema, new_schema, mode)
        report = checker.generate_compatibility_report(drift_analysis)
        
        print(f"Drift detected: {drift_analysis.drift_detected}")
        print(f"Drift type: {drift_analysis.compatibility_result.drift_type.value}")
        print(f"Risk level: {drift_analysis.compatibility_result.risk_level}")
        print(f"Forward compatible: {drift_analysis.compatibility_result.forward_compatible}")
        print(f"Backward compatible: {drift_analysis.compatibility_result.backward_compatible}")
        
        print("Breaking changes:")
        for change in drift_analysis.compatibility_result.breaking_changes:
            print(f"  - {change}")
        
        print("Potential renames:")
        for rename in drift_analysis.potential_renames:
            print(f"  - {rename['from']} → {rename['to']} (confidence: {rename['confidence']})")
        
        print("Recommendations:")
        for rec in report["recommendations"]:
            print(f"  - {rec}")
    
    # Test identical schemas
    print("\n=== Identical Schemas Test ===")
    identical_analysis = checker.analyze_drift(old_schema, old_schema)
    print(f"Drift detected: {identical_analysis.drift_detected}")
    print(f"Total changes: {identical_analysis.change_summary}")