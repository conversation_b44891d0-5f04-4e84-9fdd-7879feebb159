"""
Main API for Schema Registry with Drift Detection
Provides high-level interface for schema management operations
"""

import os
import sys
import json
import traceback
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).resolve().parent.parent.parent
sys.path.insert(0, str(project_root))

from core.config_manager import ConfigManager
from core.schema_registry.schema_registry  import SchemaRegistryManager

from core.schema_registry.models import (
    RegistrationRequest, RegistrationResponse, 
    SchemaQueryRequest, SchemaQueryResponse,
    VersionListResponse, ComparisonRequest, ComparisonResponse,
    ResponseStatus, CompatibilityMode, ModelValidator,
    create_success_response, create_error_response,
    ModelEncoder
)


class SchemaRegistryAPI:
    """
    Main API class for Schema Registry operations
    Provides high-level interface for all schema management functionality
    """
    
    def __init__(self, config_manager: ConfigManager):
        """Initialize the Schema Registry API"""
        self.config_path = config_manager._get_config_path()
        self._initialize_components()
    
    def _initialize_components(self):
        """Initialize all components"""
        try:
            # Initialize configuration
            self.config_manager = ConfigManager(self.config_path)
            
            # Create default config if it doesn't exist
            if not os.path.exists(self.config_path):
                print(f"Creating default configuration at {self.config_path}")
                self.config_manager.create_default_config()
            
            # Load configuration
            self.config_manager.load_config()
            
            # Initialize schema registry manager
            self.registry_manager = SchemaRegistryManager(self.config_manager)
            
            print("Schema Registry API initialized successfully")
            
        except Exception as e:
            print(f"Error initializing Schema Registry API: {e}")
            raise
    
    def register_schema(self, pipeline_id: str, file_name: str, schema: Dict[str, Any],
                       created_by: Optional[str] = None, force_registration: bool = False,
                       compatibility_mode: Optional[str] = None) -> Dict[str, Any]:
        """
        Register a schema with drift detection
        
        Args:
            pipeline_id: Unique identifier for the pipeline
            file_name: Name of the file (usually CSV)
            schema: Schema dictionary with column definitions
            created_by: User who is registering the schema
            force_registration: Force registration even if breaking changes detected
            compatibility_mode: Override default compatibility mode
            
        Returns:
            Dictionary containing registration result
        """
        try:
            # Create and validate request
            request = RegistrationRequest(
                pipeline_id=pipeline_id,
                file_name=file_name,
                schema=schema,
                created_by=created_by,
                force_registration=force_registration,
                compatibility_mode=CompatibilityMode(compatibility_mode) if compatibility_mode else None
            )
            
            # Validate request
            is_valid, error_message = ModelValidator.validate_registration_request(request)
            if not is_valid:
                return create_error_response(
                    registry_key=f"{pipeline_id}::{file_name}",
                    message=f"Invalid request: {error_message}"
                ).to_dict()
            
            # Perform registration
            result = self.registry_manager.register_schema_with_drift_detection(
                pipeline_id=request.pipeline_id,
                file_name=request.file_name,
                new_schema=request.schema,
                created_by=request.created_by,
                force_registration=request.force_registration
            )
            
            # Convert to response format
            response = RegistrationResponse(
                status=ResponseStatus(result.status),
                registry_key=result.registry_key,
                schema_exists=result.schema_exists,
                versions=result.versions,
                drift_analysis=result.drift_analysis,
                recommendations=result.recommendations,
                database_info=result.database_info,
                message=result.message,
                timestamp=datetime.now()
            )
            
            return response.to_dict()
            
        except Exception as e:
            return create_error_response(
                registry_key=f"{pipeline_id}::{file_name}",
                message=f"Registration failed: {str(e)}"
            ).to_dict()
    
    def get_schema(self, pipeline_id: str, file_name: str, version: Optional[str] = None) -> Dict[str, Any]:
        """
        Get a schema by pipeline ID and file name
        
        Args:
            pipeline_id: Unique identifier for the pipeline
            file_name: Name of the file
            version: Specific version to retrieve (optional, defaults to latest)
            
        Returns:
            Dictionary containing schema information
        """
        try:
            # Validate inputs
            if not ModelValidator.validate_pipeline_id(pipeline_id):
                return SchemaQueryResponse(
                    status=ResponseStatus.ERROR,
                    registry_key=f"{pipeline_id}::{file_name}",
                    version=None,
                    schema=None,
                    created_at=None,
                    created_by=None,
                    message="Invalid pipeline_id format"
                ).to_dict()
            
            if not ModelValidator.validate_file_name(file_name):
                return SchemaQueryResponse(
                    status=ResponseStatus.ERROR,
                    registry_key=f"{pipeline_id}::{file_name}",
                    version=None,
                    schema=None,
                    created_at=None,
                    created_by=None,
                    message="Invalid file_name format"
                ).to_dict()
            
            # Get schema
            schema_info = self.registry_manager.get_schema(pipeline_id, file_name, version)
            
            if schema_info:
                return SchemaQueryResponse(
                    status=ResponseStatus.SUCCESS,
                    registry_key=schema_info["registry_key"],
                    version=schema_info["version"],
                    schema=schema_info["schema"],
                    created_at=schema_info["created_at"],
                    created_by=schema_info["created_by"],
                    message="Schema retrieved successfully"
                ).to_dict()
            else:
                return SchemaQueryResponse(
                    status=ResponseStatus.ERROR,
                    registry_key=f"{pipeline_id}::{file_name}",
                    version=version,
                    schema=None,
                    created_at=None,
                    created_by=None,
                    message="Schema not found"
                ).to_dict()
                
        except Exception as e:
            return SchemaQueryResponse(
                status=ResponseStatus.ERROR,
                registry_key=f"{pipeline_id}::{file_name}",
                version=version,
                schema=None,
                created_at=None,
                created_by=None,
                message=f"Error retrieving schema: {str(e)}"
            ).to_dict()
    
    def list_versions(self, pipeline_id: str, file_name: str) -> Dict[str, Any]:
        """
        List all versions of a schema
        
        Args:
            pipeline_id: Unique identifier for the pipeline
            file_name: Name of the file
            
        Returns:
            Dictionary containing list of versions
        """
        try:
            versions = self.registry_manager.list_schema_versions(pipeline_id, file_name)
            
            return VersionListResponse(
                status=ResponseStatus.SUCCESS,
                registry_key=f"{pipeline_id}::{file_name}",
                versions=versions,
                total_count=len(versions),
                message=f"Found {len(versions)} versions"
            ).to_dict()
            
        except Exception as e:
            return VersionListResponse(
                status=ResponseStatus.ERROR,
                registry_key=f"{pipeline_id}::{file_name}",
                versions=[],
                total_count=0,
                message=f"Error listing versions: {str(e)}"
            ).to_dict()
    
    def compare_versions(self, pipeline_id: str, file_name: str, 
                        version1: str, version2: str) -> Dict[str, Any]:
        """
        Compare two versions of a schema
        
        Args:
            pipeline_id: Unique identifier for the pipeline
            file_name: Name of the file
            version1: First version to compare
            version2: Second version to compare
            
        Returns:
            Dictionary containing comparison results
        """
        try:
            comparison = self.registry_manager.compare_versions(
                pipeline_id, file_name, version1, version2
            )
            
            if comparison:
                return ComparisonResponse(
                    status=ResponseStatus.SUCCESS,
                    registry_key=f"{pipeline_id}::{file_name}",
                    version1=version1,
                    version2=version2,
                    changes=comparison["changes"],
                    drift_analysis=None,  # Could add full drift analysis here
                    message=f"Comparison completed between {version1} and {version2}"
                ).to_dict()
            else:
                return ComparisonResponse(
                    status=ResponseStatus.ERROR,
                    registry_key=f"{pipeline_id}::{file_name}",
                    version1=version1,
                    version2=version2,
                    changes={},
                    drift_analysis=None,
                    message="One or both versions not found"
                ).to_dict()
                
        except Exception as e:
            return ComparisonResponse(
                status=ResponseStatus.ERROR,
                registry_key=f"{pipeline_id}::{file_name}",
                version1=version1,
                version2=version2,
                changes={},
                drift_analysis=None,
                message=f"Error comparing versions: {str(e)}"
            ).to_dict()
    
    def get_drift_history(self, pipeline_id: str, file_name: str, 
                         limit: int = 50) -> Dict[str, Any]:
        """
        Get drift history for a schema
        
        Args:
            pipeline_id: Unique identifier for the pipeline
            file_name: Name of the file
            limit: Maximum number of history entries to return
            
        Returns:
            Dictionary containing drift history
        """
        try:
            history = self.registry_manager.get_drift_history(pipeline_id, file_name, limit)
            
            return {
                "status": ResponseStatus.SUCCESS.value,
                "registry_key": f"{pipeline_id}::{file_name}",
                "history": history,
                "total_count": len(history),
                "message": f"Retrieved {len(history)} drift history entries"
            }
            
        except Exception as e:
            return {
                "status": ResponseStatus.ERROR.value,
                "registry_key": f"{pipeline_id}::{file_name}",
                "history": [],
                "total_count": 0,
                "message": f"Error retrieving drift history: {str(e)}"
            }
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get registry statistics
        
        Returns:
            Dictionary containing registry statistics
        """
        try:
            stats = self.registry_manager.get_registry_statistics()
            
            return {
                "status": ResponseStatus.SUCCESS.value,
                "statistics": stats,
                "message": "Statistics retrieved successfully"
            }
            
        except Exception as e:
            return {
                "status": ResponseStatus.ERROR.value,
                "statistics": {},
                "message": f"Error retrieving statistics: {str(e)}"
            }
    
    def cleanup_old_versions(self) -> Dict[str, Any]:
        """
        Clean up old schema versions based on retention policy
        
        Returns:
            Dictionary containing cleanup results
        """
        try:
            cleaned_count = self.registry_manager.cleanup_old_versions()
            
            return {
                "status": ResponseStatus.SUCCESS.value,
                "cleaned_versions": cleaned_count,
                "message": f"Cleaned up {cleaned_count} old versions"
            }
            
        except Exception as e:
            return {
                "status": ResponseStatus.ERROR.value,
                "cleaned_versions": 0,
                "message": f"Error during cleanup: {str(e)}"
            }
    
    def update_config(self, section: str, key: str, value: Any, 
                     changed_by: Optional[str] = None) -> Dict[str, Any]:
        """
        Update configuration setting
        
        Args:
            section: Configuration section
            key: Configuration key
            value: New value
            changed_by: User making the change
            
        Returns:
            Dictionary containing update result
        """
        try:
            self.config_manager.update_config(section, key, value, changed_by)
            
            return {
                "status": ResponseStatus.SUCCESS.value,
                "section": section,
                "key": key,
                "new_value": value,
                "message": f"Configuration {section}.{key} updated successfully"
            }
            
        except Exception as e:
            return {
                "status": ResponseStatus.ERROR.value,
                "section": section,
                "key": key,
                "new_value": value,
                "message": f"Error updating configuration: {str(e)}"
            }
    
    def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on the registry
        
        Returns:
            Dictionary containing health status
        """
        try:
            # Check database connection
            stats = self.registry_manager.get_registry_statistics()
            
            # Check configuration
            config = self.config_manager.get_schema_registry_config()
            
            return {
                "status": "HEALTHY",
                "database_connection": "OK",
                "total_schemas": stats.get("total_registries", 0),
                "total_versions": stats.get("total_versions", 0),
                "configuration": {
                    "version_strategy": config.version_strategy,
                    "compatibility_mode": config.default_compatibility_mode,
                    "similarity_threshold": config.similarity_threshold
                },
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "status": "UNHEALTHY",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def close(self):
        """Close database connections and cleanup"""
        if hasattr(self, 'registry_manager'):
            self.registry_manager.db_manager.close_connection()


# Command Line Interface
def main():
    """Command line interface for testing the API"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Schema Registry with Drift Detection")
    parser.add_argument("--config", default="config.json", help="Configuration file path")
    parser.add_argument("--action", required=True, 
                       choices=["register", "get", "list", "compare", "stats", "health", "cleanup"],
                       help="Action to perform")
    parser.add_argument("--pipeline-id", help="Pipeline ID")
    parser.add_argument("--file-name", help="File name")
    parser.add_argument("--schema-file", help="JSON file containing schema")
    parser.add_argument("--version", help="Schema version")
    parser.add_argument("--version1", help="First version for comparison")
    parser.add_argument("--version2", help="Second version for comparison")
    parser.add_argument("--created-by", help="User creating the schema")
    parser.add_argument("--force", action="store_true", help="Force registration")
    
    args = parser.parse_args()
    
    # Initialize API
    api = SchemaRegistryAPI(args.config)
    
    try:
        if args.action == "register":
            if not args.pipeline_id or not args.file_name or not args.schema_file:
                print("Error: --pipeline-id, --file-name, and --schema-file are required for register")
                return
            
            # Load schema from file
            with open(args.schema_file, 'r') as f:
                schema = json.load(f)
            
            result = api.register_schema(
                pipeline_id=args.pipeline_id,
                file_name=args.file_name,
                schema=schema,
                created_by=args.created_by,
                force_registration=args.force
            )
            print(json.dumps(result, cls=ModelEncoder, indent=2))
        
        elif args.action == "get":
            if not args.pipeline_id or not args.file_name:
                print("Error: --pipeline-id and --file-name are required for get")
                return
            
            result = api.get_schema(args.pipeline_id, args.file_name, args.version)
            print(json.dumps(result, cls=ModelEncoder, indent=2))
        
        elif args.action == "list":
            if not args.pipeline_id or not args.file_name:
                print("Error: --pipeline-id and --file-name are required for list")
                return
            
            result = api.list_versions(args.pipeline_id, args.file_name)
            print(json.dumps(result, cls=ModelEncoder, indent=2))
        
        elif args.action == "compare":
            if not args.pipeline_id or not args.file_name or not args.version1 or not args.version2:
                print("Error: --pipeline-id, --file-name, --version1, and --version2 are required for compare")
                return
            
            result = api.compare_versions(args.pipeline_id, args.file_name, args.version1, args.version2)
            print(json.dumps(result, cls=ModelEncoder, indent=2))
        
        elif args.action == "stats":
            result = api.get_statistics()
            print(json.dumps(result, cls=ModelEncoder, indent=2))
        
        elif args.action == "health":
            result = api.health_check()
            print(json.dumps(result, cls=ModelEncoder, indent=2))
        
        elif args.action == "cleanup":
            result = api.cleanup_old_versions()
            print(json.dumps(result, cls=ModelEncoder, indent=2))
    
    finally:
        api.close()


# Example usage
if __name__ == "__main__":
    config_manager = ConfigManager()

    # If running directly, use command line interface
    if len(os.sys.argv) > 1:
        main()
    else:
        # Example usage for testing
        print("=== Schema Registry API Example ===")
        
        # Initialize API
        api = SchemaRegistryAPI(config_manager)
        
        # Example schema
        example_schema = {
            "file_name": "users.csv",
            "schema": {
                "user_id": "uuid",
                "email": "email",
                "name": "string",
                "age": "integer",
                "created_at": "date-time"
            }
        }
        
        try:
            # Register schema
            print("Registering schema...")
            result = api.register_schema(
                pipeline_id="example_pipeline",
                file_name="users.csv",
                schema=example_schema,
                created_by="api_test"
            )
            print(f"Registration result: {result['status']}")
            print(f"Message: {result['message']}")
            
            # Get schema
            print("\nRetrieving schema...")
            schema_result = api.get_schema("example_pipeline", "users.csv")
            print(f"Retrieved schema version: {schema_result.get('version')}")
            
            # Get statistics
            print("\nRegistry statistics...")
            stats = api.get_statistics()
            print(f"Total schemas: {stats['statistics'].get('total_registries', 0)}")
            
            # Health check
            print("\nHealth check...")
            health = api.health_check()
            print(f"Status: {health['status']}")
            
        except Exception as e:
            print(f"Error: {e}")
            traceback.print_exc()
        
        finally:
            api.close()