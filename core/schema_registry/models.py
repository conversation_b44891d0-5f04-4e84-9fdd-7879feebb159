"""
Data Models for Schema Registry
Defines data structures and response models
"""

from dataclasses import dataclass, asdict
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from enum import Enum
import json
from typing import Tuple, Optional


class ResponseStatus(Enum):
    SUCCESS = "SUCCESS"
    WARNING = "WARNING"
    ERROR = "ERROR"


class DriftType(Enum):
    COMPATIBLE = "COMPATIBLE"
    BREAKING = "BREAKING"
    WARNING = "WARNING"
    NONE = "NONE"


class RiskLevel(Enum):
    NONE = "NONE"
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"


class CompatibilityMode(Enum):
    STRICT = "strict"
    GRACEFUL = "graceful"
    PERMISSIVE = "permissive"


class VersionStrategy(Enum):
    AUTO_INCREMENT = "auto_increment"
    SEMANTIC = "semantic"
    TIMESTAMP = "timestamp"


@dataclass
class ColumnChange:
    """Represents a change to a column"""
    column: str
    change_type: str  # "added", "removed", "modified", "renamed"
    old_value: Optional[Any] = None
    new_value: Optional[Any] = None
    risk_level: Optional[RiskLevel] = None


@dataclass
class PotentialRename:
    """Represents a potential column rename"""
    from_column: str
    to_column: str
    confidence: float
    old_type: str
    new_type: str


@dataclass
class ChangeSummary:
    """Summary of changes between schemas"""
    added_columns: List[str]
    removed_columns: List[str]
    modified_columns: List[Dict[str, Any]]
    renamed_columns: List[PotentialRename]
    total_changes: int
    unchanged_columns: List[str]


@dataclass
class CompatibilityResult:
    """Result of compatibility analysis"""
    forward_compatible: bool
    backward_compatible: bool
    breaking_changes: List[str]
    safe_changes: List[str]
    warnings: List[str]
    migration_required: bool
    drift_type: DriftType
    risk_level: RiskLevel


@dataclass
class DriftAnalysis:
    """Complete drift analysis result"""
    drift_detected: bool
    change_summary: ChangeSummary
    potential_renames: List[PotentialRename]
    compatibility_result: CompatibilityResult
    migration_suggestions: List[str]


@dataclass
class SchemaVersion:
    """Represents a schema version"""
    registry_key: str
    version: str
    schema: Dict[str, Any]
    schema_hash: str
    created_at: datetime
    created_by: Optional[str] = None
    compatibility_info: Optional[Dict[str, Any]] = None


@dataclass
class SchemaRegistry:
    """Represents a schema registry entry"""
    registry_key: str
    pipeline_id: str
    file_name: str
    current_version: str
    created_at: datetime
    updated_at: datetime


@dataclass
class DriftLogEntry:
    """Represents a drift log entry"""
    registry_key: str
    from_version: Optional[str]
    to_version: str
    drift_type: DriftType
    changes: Dict[str, Any]
    compatibility_report: Optional[Dict[str, Any]]
    created_at: datetime


@dataclass
class DatabaseInfo:
    """Database connection and statistics info"""
    duckdb_path: str
    total_schemas: int
    total_versions: int
    total_drift_events: int = 0


@dataclass
class RegistrationRequest:
    """Schema registration request"""
    pipeline_id: str
    file_name: str
    schema: Dict[str, Any]
    created_by: Optional[str] = None
    force_registration: bool = False
    compatibility_mode: Optional[CompatibilityMode] = None


@dataclass
class RegistrationResponse:
    """Schema registration response"""
    status: ResponseStatus
    registry_key: str
    schema_exists: bool
    versions: Dict[str, Optional[str]]
    drift_analysis: Optional[DriftAnalysis]
    recommendations: List[str]
    database_info: DatabaseInfo
    message: str
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        def convert_value(value):
            if isinstance(value, Enum):
                return value.value
            elif isinstance(value, datetime):
                return value.isoformat()
            elif hasattr(value, '__dict__'):
                return asdict(value)
            return value
        
        return {
            k: convert_value(v) for k, v in asdict(self).items()
        }


@dataclass
class SchemaQueryRequest:
    """Schema query request"""
    pipeline_id: str
    file_name: str
    version: Optional[str] = None


@dataclass
class SchemaQueryResponse:
    """Schema query response"""
    status: ResponseStatus
    registry_key: str
    version: Optional[str]
    schema: Optional[Dict[str, Any]]
    created_at: Optional[datetime]
    created_by: Optional[str]
    message: str
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        result = asdict(self)
        result['status'] = self.status.value
        if self.created_at:
            result['created_at'] = self.created_at.isoformat()
        return result


@dataclass
class VersionListResponse:
    """Response for listing schema versions"""
    status: ResponseStatus
    registry_key: str
    versions: List[Dict[str, Any]]
    total_count: int
    message: str
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            'status': self.status.value,
            'registry_key': self.registry_key,
            'versions': self.versions,
            'total_count': self.total_count,
            'message': self.message
        }


@dataclass
class ComparisonRequest:
    """Schema comparison request"""
    pipeline_id: str
    file_name: str
    version1: str
    version2: str


@dataclass
class ComparisonResponse:
    """Schema comparison response"""
    status: ResponseStatus
    registry_key: str
    version1: str
    version2: str
    changes: Dict[str, Any]
    drift_analysis: Optional[DriftAnalysis]
    message: str
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        result = asdict(self)
        result['status'] = self.status.value
        return result


@dataclass
class RegistryStatistics:
    """Registry statistics"""
    total_registries: int
    total_versions: int
    total_drift_events: int
    most_active_schemas: List[Dict[str, Any]]
    compatibility_breakdown: Dict[str, int]
    version_strategy_usage: Dict[str, int]
    recent_activity: List[Dict[str, Any]]


@dataclass
class RegistryConfig:
    """Registry configuration"""
    version_strategy: VersionStrategy
    similarity_threshold: float
    default_compatibility_mode: CompatibilityMode
    enable_recommendations: bool
    enable_migration_suggestions: bool
    column_order_matters: bool
    retention_policy: Dict[str, int]


@dataclass
class ConfigUpdateRequest:
    """Configuration update request"""
    section: str
    key: str
    value: Any
    changed_by: Optional[str] = None


@dataclass
class ConfigUpdateResponse:
    """Configuration update response"""
    status: ResponseStatus
    old_value: Optional[Any]
    new_value: Any
    message: str
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            'status': self.status.value,
            'old_value': self.old_value,
            'new_value': self.new_value,
            'message': self.message,
            'timestamp': self.timestamp.isoformat()
        }


class ModelEncoder(json.JSONEncoder):
    """Custom JSON encoder for data models"""
    
    def default(self, obj):
        if isinstance(obj, Enum):
            return obj.value
        elif isinstance(obj, datetime):
            return obj.isoformat()
        elif hasattr(obj, 'to_dict'):
            return obj.to_dict()
        elif hasattr(obj, '__dict__'):
            return asdict(obj)
        return super().default(obj)


class ModelValidator:
    """Validator for data models"""
    
    @staticmethod
    def validate_pipeline_id(pipeline_id: str) -> bool:
        """Validate pipeline ID format"""
        if not pipeline_id or not isinstance(pipeline_id, str):
            return False
        return len(pipeline_id.strip()) > 0 and len(pipeline_id) <= 100
    
    @staticmethod
    def validate_file_name(file_name: str) -> bool:
        """Validate file name format"""
        if not file_name or not isinstance(file_name, str):
            return False
        return len(file_name.strip()) > 0 and len(file_name) <= 255
    
    @staticmethod
    def validate_schema(schema: Dict[str, Any]) -> bool:
        """Validate schema structure"""
        if not isinstance(schema, dict):
            return False
        
        # Check for required 'schema' key
        if 'schema' not in schema:
            return False
        
        # Check that schema contains column definitions
        schema_def = schema['schema']
        if not isinstance(schema_def, dict) or len(schema_def) == 0:
            return False
        
        # Validate column definitions
        for column_name, column_type in schema_def.items():
            if not isinstance(column_name, str) or not isinstance(column_type, str):
                return False
            if len(column_name.strip()) == 0:
                return False
        
        return True
    
    @staticmethod
    def validate_version(version: str) -> bool:
        """Validate version format"""
        if not version or not isinstance(version, str):
            return False
        
        # Should match version patterns: v1, v1.0.0, or timestamp format
        import re
        patterns = [
            r'^v\d+$',                      # v1, v2, etc.
            r'^v\d+\.\d+\.\d+$',           # v1.0.0, v1.2.3, etc.
            r'^\d{8}_\d{6}$'               # 20231201_143000
        ]
        
        return any(re.match(pattern, version) for pattern in patterns)
    
    @staticmethod
    def validate_registration_request(request: RegistrationRequest) -> Tuple[bool, Optional[str]]:
        """Validate registration request"""
        if not ModelValidator.validate_pipeline_id(request.pipeline_id):
            return False, "Invalid pipeline_id format"
        
        if not ModelValidator.validate_file_name(request.file_name):
            return False, "Invalid file_name format"
        
        if not ModelValidator.validate_schema(request.schema):
            return False, "Invalid schema structure"
        
        if request.created_by and not isinstance(request.created_by, str):
            return False, "Invalid created_by format"
        
        if not isinstance(request.force_registration, bool):
            return False, "Invalid force_registration format"
        
        return True, None


# Factory functions for creating models
def create_registration_response(status: ResponseStatus, registry_key: str, 
                               message: str, **kwargs) -> RegistrationResponse:
    """Factory function for creating registration responses"""
    return RegistrationResponse(
        status=status,
        registry_key=registry_key,
        schema_exists=kwargs.get('schema_exists', False),
        versions=kwargs.get('versions', {'previous': None, 'current': None}),
        drift_analysis=kwargs.get('drift_analysis'),
        recommendations=kwargs.get('recommendations', []),
        database_info=kwargs.get('database_info', DatabaseInfo("", 0, 0)),
        message=message,
        timestamp=datetime.now()
    )


def create_error_response(registry_key: str, message: str) -> RegistrationResponse:
    """Factory function for creating error responses"""
    return create_registration_response(
        status=ResponseStatus.ERROR,
        registry_key=registry_key,
        message=message
    )


def create_success_response(registry_key: str, message: str, **kwargs) -> RegistrationResponse:
    """Factory function for creating success responses"""
    return create_registration_response(
        status=ResponseStatus.SUCCESS,
        registry_key=registry_key,
        message=message,
        **kwargs
    )


# Example usage and testing
if __name__ == "__main__":
    # Test model creation and validation
    print("=== Model Testing ===")
    
    # Test registration request
    request = RegistrationRequest(
        pipeline_id="test_pipeline",
        file_name="users.csv",
        schema={
            "file_name": "users.csv",
            "schema": {
                "id": "integer",
                "name": "string",
                "email": "email"
            }
        },
        created_by="test_user"
    )
    
    is_valid, error = ModelValidator.validate_registration_request(request)
    print(f"Registration request valid: {is_valid}")
    if error:
        print(f"Validation error: {error}")
    
    # Test response creation
    response = create_success_response(
        registry_key="test_pipeline::users.csv",
        message="Schema registered successfully",
        schema_exists=False,
        versions={"previous": None, "current": "v1.0.0"}
    )
    
    print(f"Response status: {response.status.value}")
    print(f"Response message: {response.message}")
    
    # Test JSON serialization
    response_dict = response.to_dict()
    response_json = json.dumps(response_dict, cls=ModelEncoder, indent=2)
    print("JSON serialization:")
    print(response_json[:200] + "...")
    
    # Test enum validation
    print(f"Valid compatibility modes: {[mode.value for mode in CompatibilityMode]}")
    print(f"Valid version strategies: {[strategy.value for strategy in VersionStrategy]}")
    print(f"Valid risk levels: {[level.value for level in RiskLevel]}")