"""
Utility functions for Schema Registry
Includes similarity calculation, hashing, version management, etc.
"""

import hashlib
import json
from typing import Dict, Any, List, Tuple, Set, Optional
from datetime import datetime
import re
from difflib import SequenceMatcher


class VersionManager:
    """Handles different version strategies"""
    
    @staticmethod
    def generate_version(strategy: str, current_version: Optional[str] = None, 
                        change_type: str = "minor") -> str:
        """Generate next version based on strategy"""
        
        if strategy == "timestamp":
            return datetime.now().strftime("%Y%m%d_%H%M%S")
        
        elif strategy == "auto_increment":
            if current_version is None:
                return "v1"
            
            # Extract number from version like "v1", "v2", etc.
            match = re.search(r'v(\d+)', current_version)
            if match:
                current_num = int(match.group(1))
                return f"v{current_num + 1}"
            else:
                return "v1"
        
        elif strategy == "semantic":
            if current_version is None:
                return "v1.0.0"
            
            # Parse semantic version like "v1.2.3"
            match = re.search(r'v(\d+)\.(\d+)\.(\d+)', current_version)
            if match:
                major, minor, patch = map(int, match.groups())
                
                if change_type == "major":
                    return f"v{major + 1}.0.0"
                elif change_type == "minor":
                    return f"v{major}.{minor + 1}.0"
                else:  # patch
                    return f"v{major}.{minor}.{patch + 1}"
            else:
                return "v1.0.0"
        
        else:
            raise ValueError(f"Unknown version strategy: {strategy}")


class SchemaHasher:
    """Handles schema hashing for duplicate detection"""
    
    @staticmethod
    def calculate_schema_hash(schema: Dict[str, Any]) -> str:
        """Calculate consistent hash for schema"""
        # Normalize schema for consistent hashing
        normalized_schema = SchemaHasher._normalize_schema(schema)
        
        # Convert to JSON string with sorted keys
        schema_json = json.dumps(normalized_schema, sort_keys=True, separators=(',', ':'))
        
        # Calculate SHA-256 hash
        return hashlib.sha256(schema_json.encode('utf-8')).hexdigest()
    
    @staticmethod
    def _normalize_schema(schema: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize schema for consistent comparison"""
        if "file_name" in schema:
            # Remove file_name for comparison
            normalized = {k: v for k, v in schema.items() if k != "file_name"}
        else:
            normalized = schema.copy()
        
        # Sort schema properties
        if "schema" in normalized:
            normalized["schema"] = dict(sorted(normalized["schema"].items()))
        
        return normalized


class SimilarityCalculator:
    """Calculate similarity between column names for rename detection"""
    
    @staticmethod
    def calculate_similarity(str1: str, str2: str) -> float:
        """Calculate similarity ratio between two strings"""
        return SequenceMatcher(None, str1.lower(), str2.lower()).ratio()
    
    @staticmethod
    def find_similar_columns(old_columns: List[str], new_columns: List[str], 
                           threshold: float = 0.8) -> List[Tuple[str, str, float]]:
        """Find potentially renamed columns based on similarity"""
        similar_pairs = []
        
        for old_col in old_columns:
            for new_col in new_columns:
                similarity = SimilarityCalculator.calculate_similarity(old_col, new_col)
                if similarity >= threshold and old_col != new_col:
                    similar_pairs.append((old_col, new_col, similarity))
        
        # Sort by similarity score descending
        similar_pairs.sort(key=lambda x: x[2], reverse=True)
        
        return similar_pairs
    
    @staticmethod
    def detect_potential_renames(old_schema: Dict[str, Any], new_schema: Dict[str, Any],
                               similarity_threshold: float = 0.8) -> List[Dict[str, Any]]:
        """Detect potential column renames between schemas"""
        old_columns = set(old_schema.get("schema", {}).keys())
        new_columns = set(new_schema.get("schema", {}).keys())
        
        # Find removed and added columns
        removed_columns = old_columns - new_columns
        added_columns = new_columns - old_columns
        
        if not removed_columns or not added_columns:
            return []
        
        # Find similar column pairs
        similar_pairs = SimilarityCalculator.find_similar_columns(
            list(removed_columns), list(added_columns), similarity_threshold
        )
        
        # Convert to rename suggestions
        renames = []
        used_old = set()
        used_new = set()
        
        for old_col, new_col, similarity in similar_pairs:
            if old_col not in used_old and new_col not in used_new:
                renames.append({
                    "from": old_col,
                    "to": new_col,
                    "confidence": round(similarity, 3),
                    "old_type": old_schema["schema"].get(old_col),
                    "new_type": new_schema["schema"].get(new_col)
                })
                used_old.add(old_col)
                used_new.add(new_col)
        
        return renames


class SchemaComparator:
    """Compare schemas and detect changes"""
    
    @staticmethod
    def compare_schemas(old_schema: Dict[str, Any], new_schema: Dict[str, Any],
                       ignore_column_order: bool = True) -> Dict[str, Any]:
        """Compare two schemas and return detailed changes"""
        
        old_columns = old_schema.get("schema", {})
        new_columns = new_schema.get("schema", {})
        
        # Normalize column order if needed
        if ignore_column_order:
            old_columns = dict(sorted(old_columns.items()))
            new_columns = dict(sorted(new_columns.items()))
        
        old_column_names = set(old_columns.keys())
        new_column_names = set(new_columns.keys())
        
        # Detect changes
        added_columns = new_column_names - old_column_names
        removed_columns = old_column_names - new_column_names
        common_columns = old_column_names & new_column_names
        
        # Check for type changes in common columns
        modified_columns = []
        for col in common_columns:
            if old_columns[col] != new_columns[col]:
                modified_columns.append({
                    "column": col,
                    "old_type": old_columns[col],
                    "new_type": new_columns[col]
                })
        
        # Calculate change summary
        total_changes = len(added_columns) + len(removed_columns) + len(modified_columns)
        
        return {
            "added_columns": list(added_columns),
            "removed_columns": list(removed_columns),
            "modified_columns": modified_columns,
            "unchanged_columns": list(common_columns - {mc["column"] for mc in modified_columns}),
            "total_changes": total_changes,
            "has_changes": total_changes > 0,
            "schema_hash_old": SchemaHasher.calculate_schema_hash(old_schema),
            "schema_hash_new": SchemaHasher.calculate_schema_hash(new_schema)
        }


class TypeCompatibilityChecker:
    """Check compatibility between data types"""
    
    # Define safe type transitions
    SAFE_TYPE_TRANSITIONS = {
        "integer": ["number", "string"],  # int can become float or string
        "number": ["string"],             # float can become string
        "boolean": ["string"],            # bool can become string
        "string": [],                     # string can't safely become anything else
        "date": ["string"],               # date can become string
        "date-time": ["string"],          # datetime can become string
        "email": ["string"],              # email is essentially string
        "url": ["string"],                # url is essentially string
        "uuid": ["string"],               # uuid is essentially string
        "phone": ["string"],              # phone is essentially string
        "ipv4": ["string"],               # ip is essentially string
        "ipv6": ["string"],               # ip is essentially string
        "currency": ["string"],           # currency is essentially string
    }
    
    @staticmethod
    def is_type_transition_safe(old_type: str, new_type: str) -> bool:
        """Check if type transition is safe"""
        if old_type == new_type:
            return True
        
        safe_transitions = TypeCompatibilityChecker.SAFE_TYPE_TRANSITIONS.get(old_type, [])
        return new_type in safe_transitions
    
    @staticmethod
    def get_type_compatibility_risk(old_type: str, new_type: str) -> str:
        """Get risk level for type transition"""
        if old_type == new_type:
            return "NONE"
        
        if TypeCompatibilityChecker.is_type_transition_safe(old_type, new_type):
            return "LOW"
        
        # Special cases
        if old_type == "string" and new_type in ["integer", "number", "boolean", "date", "date-time"]:
            return "HIGH"  # String to typed data is risky
        
        return "MEDIUM"


class MigrationSuggester:
    """Generate migration suggestions for schema changes"""
    
    @staticmethod
    def generate_migration_suggestions(changes: Dict[str, Any], 
                                     old_schema: Dict[str, Any],
                                     new_schema: Dict[str, Any]) -> List[str]:
        """Generate migration suggestions based on detected changes"""
        suggestions = []
        
        # Handle added columns
        for column in changes.get("added_columns", []):
            column_type = new_schema["schema"].get(column)
            suggestions.append(f"ADD COLUMN: Consider adding default value for new column '{column}' of type '{column_type}'")
        
        # Handle removed columns
        for column in changes.get("removed_columns", []):
            suggestions.append(f"REMOVE COLUMN: Ensure data for column '{column}' is backed up before removal")
        
        # Handle modified columns
        for mod in changes.get("modified_columns", []):
            column = mod["column"]
            old_type = mod["old_type"]
            new_type = mod["new_type"]
            
            risk = TypeCompatibilityChecker.get_type_compatibility_risk(old_type, new_type)
            
            if risk == "HIGH":
                suggestions.append(f"TYPE CHANGE (HIGH RISK): Column '{column}' changing from '{old_type}' to '{new_type}' - validate all existing data")
            elif risk == "MEDIUM":
                suggestions.append(f"TYPE CHANGE (MEDIUM RISK): Column '{column}' changing from '{old_type}' to '{new_type}' - test conversion")
            else:
                suggestions.append(f"TYPE CHANGE (LOW RISK): Column '{column}' changing from '{old_type}' to '{new_type}'")
        
        return suggestions


class RegistryKeyManager:
    """Manage registry keys and validation"""
    
    @staticmethod
    def generate_registry_key(pipeline_id: str, file_name: str) -> str:
        """Generate registry key from pipeline ID and file name"""
        # Sanitize inputs
        clean_pipeline_id = re.sub(r'[^\w\-]', '_', pipeline_id)
        clean_file_name = re.sub(r'[^\w\-\.]', '_', file_name)
        
        return f"{clean_pipeline_id}::{clean_file_name}"
    
    @staticmethod
    def parse_registry_key(registry_key: str) -> Tuple[str, str]:
        """Parse registry key back to pipeline ID and file name"""
        if "::" not in registry_key:
            raise ValueError(f"Invalid registry key format: {registry_key}")
        
        parts = registry_key.split("::", 1)
        return parts[0], parts[1]
    
    @staticmethod
    def validate_registry_key(registry_key: str) -> bool:
        """Validate registry key format"""
        try:
            pipeline_id, file_name = RegistryKeyManager.parse_registry_key(registry_key)
            return len(pipeline_id) > 0 and len(file_name) > 0
        except ValueError:
            return False


# Example usage and testing
if __name__ == "__main__":
    # Test version generation
    version_manager = VersionManager()
    
    print("=== Version Generation Tests ===")
    print(f"Timestamp: {version_manager.generate_version('timestamp')}")
    print(f"Auto increment from v5: {version_manager.generate_version('auto_increment', 'v5')}")
    print(f"Semantic minor from v1.2.3: {version_manager.generate_version('semantic', 'v1.2.3', 'minor')}")
    
    # Test schema hashing
    print("\n=== Schema Hashing Tests ===")
    schema1 = {"schema": {"id": "integer", "name": "string"}}
    schema2 = {"schema": {"name": "string", "id": "integer"}}  # Different order
    
    hash1 = SchemaHasher.calculate_schema_hash(schema1)
    hash2 = SchemaHasher.calculate_schema_hash(schema2)
    
    print(f"Schema 1 hash: {hash1[:16]}...")
    print(f"Schema 2 hash: {hash2[:16]}...")
    print(f"Hashes equal (should be True): {hash1 == hash2}")
    
    # Test similarity calculation
    print("\n=== Similarity Tests ===")
    similarity = SimilarityCalculator.calculate_similarity("user_id", "userId")
    print(f"Similarity between 'user_id' and 'userId': {similarity}")
    
    # Test schema comparison
    print("\n=== Schema Comparison Tests ===")
    old_schema = {"schema": {"id": "integer", "name": "string", "age": "integer"}}
    new_schema = {"schema": {"id": "integer", "name": "string", "email": "email"}}
    
    changes = SchemaComparator.compare_schemas(old_schema, new_schema)
    print(f"Changes detected: {changes}")
    
    # Test registry key management
    print("\n=== Registry Key Tests ===")
    key = RegistryKeyManager.generate_registry_key("pipeline-123", "users.csv")
    print(f"Generated key: {key}")
    
    pipeline_id, file_name = RegistryKeyManager.parse_registry_key(key)
    print(f"Parsed back: pipeline_id='{pipeline_id}', file_name='{file_name}'")