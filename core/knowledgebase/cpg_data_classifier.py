import os
import sys
from pathlib import Path
from typing import Dict, List, Any
import time
import json

# Add project root to path for imports
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from core.config_manager import ConfigManager
from core.knowledgebase.classification_engine import ClassificationEngine


class CPGClassificationCLI:
    """Command line interface for CPG data classification system"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.classification_engine = None
        self._initialize_system()
    
    def _initialize_system(self):
        """Initialize the classification system"""
        try:
            print("Initializing CPG Data Classification System...")
            
            # Get configuration
            vector_db_config = self.config_manager.get_vector_db_config()
            kb_paths = self.config_manager.get_knowledge_base_paths()
            classification_config = self.config_manager.get_classification_config()
            file_processing_config = self.config_manager.get_file_processing_config()
            learning_config = self.config_manager.get_learning_config()
            
            # Combine configuration
            full_config = {
                'vector_db': vector_db_config,
                'paths': kb_paths,
                'classification': classification_config,
                'file_processing': file_processing_config,
                'learning': learning_config
            }
            
            # Initialize classification engine
            self.classification_engine = ClassificationEngine(full_config)
            
            print("System initialized successfully!")
            
        except Exception as e:
            print(f"Failed to initialize system: {e}")
            sys.exit(1)
    
    def run(self):
        """Main CLI loop"""
        while True:
            try:
                choice = self._show_main_menu()
                
                if choice == '1':
                    self._upload_files_menu()
                elif choice == '2':
                    self._classify_file_menu()
                elif choice == '3':
                    self._search_knowledge_base_menu()
                elif choice == '4':
                    self._learning_training_menu()
                elif choice == '5':
                    self._system_status_menu()
                elif choice == '6':
                    print("Goodbye!")
                    break
                else:
                    print("Invalid choice. Please try again.")
                
                input("\nPress Enter to continue...")
                
            except KeyboardInterrupt:
                print("\n\nGoodbye!")
                break
            except Exception as e:
                print(f"Error: {e}")
                input("Press Enter to continue...")
    
    def _show_main_menu(self) -> str:
        """Show main menu and get user choice"""
        print("\n" + "="*50)
        print("CPG Data Classification System")
        print("="*50)
        print("1. Upload Files to Knowledge Base")
        print("2. Classify File")
        print("3. Search Knowledge Base")
        print("4. Learning & Training")
        print("5. System Status")
        print("6. Exit")
        print("-"*50)
        
        return input("Enter your choice (1-6): ").strip()
    
    def _upload_files_menu(self):
        """Handle file upload menu"""
        while True:
            print("\n" + "="*40)
            print("Upload Files to Knowledge Base")
            print("="*40)
            print("1. Upload single file")
            print("2. Batch upload directory")
            print("3. View uploaded files")
            print("4. Delete uploaded files")
            print("5. Back to main menu")
            print("-"*40)
            
            choice = input("Enter your choice (1-5): ").strip()
            
            if choice == '1':
                self._upload_single_file()
            elif choice == '2':
                self._batch_upload_directory()
            elif choice == '3':
                self._view_uploaded_files()
            elif choice == '4':
                self._delete_uploaded_files()
            elif choice == '5':
                break
            else:
                print("Invalid choice. Please try again.")
    
    def _upload_single_file(self):
        """Upload a single file with metadata"""
        try:
            print("\nUpload Single File")
            print("-"*20)
            
            # Get file path
            file_path = input("Enter file path: ").strip()
            
            if not os.path.exists(file_path):
                print("File not found!")
                return
            
            # Show document types
            document_types = self.config_manager.get_document_types()
            self._show_document_types(document_types)
            
            # Get document type
            doc_type_choice = input("Enter document type number: ").strip()
            document_type = self._get_document_type_from_choice(doc_type_choice, document_types)
            
            if not document_type:
                print("Invalid document type choice.")
                return
            
            # Get description
            description = input("Optional description: ").strip()
            
            # Ask if user wants to perform classification on upload
            classify_on_upload = input("Perform automatic classification during upload? (y/n): ").strip().lower() == 'y'
            
            # Upload file
            print(f"\nProcessing file: {os.path.basename(file_path)}")
            result = self.classification_engine.upload_file_with_metadata(file_path, document_type, description)
            
            if result['success']:
                print("File uploaded successfully!")
                print(f"Document ID: {result['document_id']}")
                print(f"File Type: {result.get('file_type', 'unknown')}")
                print(f"Document Type: {document_type}")
                if description:
                    print(f"Description: {description}")
                
                patterns = result.get('extracted_patterns', [])
                if patterns:
                    print(f"Detected Patterns: {', '.join(patterns)}")
                
                # Perform classification if requested
                if classify_on_upload:
                    print(f"\nPerforming automatic classification...")
                    classify_result = self.classification_engine.classify_file_by_path(file_path, collect_feedback=False)
                    
                    if classify_result['success']:
                        print(f"Automatic Classification: {classify_result['classification'].upper()}")
                        print(f"Confidence: {classify_result['confidence']*100:.1f}%")
                        
                        reasoning = classify_result.get('reasoning', 'No reasoning provided')
                        if len(reasoning) > 100:
                            reasoning = reasoning[:100] + "..."
                        print(f"Reasoning: {reasoning}")
                    else:
                        print(f"Automatic classification failed: {classify_result.get('error')}")
            else:
                print(f"Upload failed: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"Error uploading file: {e}")
    
    def _show_document_types(self, document_types: Dict[str, List[str]]):
        """Show available document types"""
        print("\nDocument Types Available:")
        print("="*30)
        
        choice_num = 1
        self.doc_type_choices = {}
        
        for category, types in document_types.items():
            print(f"\n{category.replace('_', ' ').title()}:")
            for doc_type in types:
                print(f"  {choice_num}. {doc_type.replace('_', ' ').title()}")
                self.doc_type_choices[str(choice_num)] = doc_type
                choice_num += 1
        
        print(f"\n  {choice_num}. Other (specify custom type)")
        self.doc_type_choices[str(choice_num)] = "custom"
    
    def _get_document_type_from_choice(self, choice: str, document_types: Dict[str, List[str]]) -> str:
        """Get document type from user choice"""
        if choice in self.doc_type_choices:
            if self.doc_type_choices[choice] == "custom":
                return input("Enter custom document type: ").strip()
            else:
                return self.doc_type_choices[choice]
        return ""
    
    def _batch_upload_directory(self):
        """Batch upload files from directory"""
        try:
            print("\nBatch Upload Directory")
            print("-"*25)
            
            directory = input("Enter directory path: ").strip()
            
            if not os.path.isdir(directory):
                print("Directory not found!")
                return
            
            # Find supported files based on available processors
            supported_extensions = self.classification_engine.file_processor_factory.get_supported_extensions()
            files = []
            
            for ext in supported_extensions:
                files.extend(Path(directory).glob(f"*{ext}"))
            
            if not files:
                print("No supported files found in directory.")
                return
            
            print(f"Found {len(files)} supported files")
            
            # Get common document type for all files
            document_types = self.config_manager.get_document_types()
            self._show_document_types(document_types)
            
            doc_type_choice = input("Enter document type for all files: ").strip()
            document_type = self._get_document_type_from_choice(doc_type_choice, document_types)
            
            if not document_type:
                print("Invalid document type choice.")
                return
            
            description = input("Optional description for all files: ").strip()
            
            # Ask about classification
            classify_on_upload = input("Perform automatic classification during upload? (y/n): ").strip().lower() == 'y'
            
            # Upload files
            print(f"\nUploading {len(files)} files...")
            successful = 0
            
            for file_path in files:
                try:
                    result = self.classification_engine.upload_file_with_metadata(
                        str(file_path), document_type, description
                    )
                    
                    if result['success']:
                        print(f"  Uploaded: {file_path.name}")
                        successful += 1
                        
                        # Perform classification if requested
                        if classify_on_upload:
                            classify_result = self.classification_engine.classify_file_by_path(
                                str(file_path), collect_feedback=False
                            )
                            if classify_result['success']:
                                print(f"    -> Classified as: {classify_result['classification']} ({classify_result['confidence']*100:.1f}%)")
                            else:
                                print(f"    -> Classification failed: {classify_result.get('error', 'Unknown error')}")
                    else:
                        print(f"  Failed: {file_path.name}: {result.get('error', 'Failed')}")
                
                except Exception as e:
                    print(f"  Error: {file_path.name}: {e}")
            
            print(f"\nUpload complete: {successful}/{len(files)} files uploaded successfully")
            
        except Exception as e:
            print(f"Error in batch upload: {e}")
    
    def _view_uploaded_files(self):
        """View uploaded files"""
        try:
            print("\nUploaded Files")
            print("="*30)
            
            files = self.classification_engine.get_uploaded_files()
            
            if not files:
                print("No files uploaded yet.")
                return
            
            print(f"Total files: {len(files)}")
            print()
            
            for i, file_info in enumerate(files[:20], 1):  # Show first 20
                print(f"{i}. {file_info['filename']}")
                print(f"   Type: {file_info['document_type']}")
                print(f"   File Type: {file_info['file_type']}")
                print(f"   Size: {file_info['file_size']} bytes")
                if file_info['description']:
                    print(f"   Description: {file_info['description']}")
                print()
            
            if len(files) > 20:
                print(f"... and {len(files) - 20} more files")
                
        except Exception as e:
            print(f"Error viewing uploaded files: {e}")
    
    def _delete_uploaded_files(self):
        """Delete uploaded files (placeholder)"""
        print("\nDelete functionality not implemented yet.")
        print("Files can be managed through the vector database directly.")
    
    def _classify_file_menu(self):
        """Handle file classification - IMPROVED VERSION"""
        try:
            print("\nFile Classification")
            print("="*20)
            
            file_path = input("Enter file path: ").strip()
            
            if not os.path.exists(file_path):
                print("File not found!")
                return
            
            print(f"\nAnalyzing file: {os.path.basename(file_path)}")
            print("Please wait...")
            
            start_time = time.time()
            
            # STEP 1: Get classification results (without feedback collection)
            result = self.classification_engine.classify_file_by_path(file_path, collect_feedback=False)
            end_time = time.time()
            
            if not result['success']:
                print(f"Classification failed: {result.get('error', 'Unknown error')}")
                return
            
            # STEP 2: Display results FIRST
            self._display_classification_results(result, end_time - start_time)
            
            # STEP 3: THEN optionally collect feedback
            if self.classification_engine.classification_config.get('enable_learning', True):
                feedback_result = self.classification_engine.collect_feedback_on_result(result)
                if feedback_result:
                    print("Feedback collected and learning system updated.")
            
        except Exception as e:
            print(f"Error classifying file: {e}")
    
    def _search_knowledge_base_menu(self):
        """Handle knowledge base search"""
        while True:
            print("\n" + "="*30)
            print("Search Knowledge Base")
            print("="*30)
            print("1. Search all uploaded files")
            print("2. Search by document type")
            print("3. Search by file type")
            print("4. Search learning feedback")
            print("5. Advanced search")
            print("6. Back to main menu")
            print("-"*30)
            
            choice = input("Enter your choice (1-6): ").strip()
            
            if choice == '1':
                self._search_all_files()
            elif choice == '2':
                self._search_by_document_type()
            elif choice == '3':
                self._search_by_file_type()
            elif choice == '4':
                self._search_learning_feedback()
            elif choice == '5':
                self._advanced_search()
            elif choice == '6':
                break
            else:
                print("Invalid choice. Please try again.")
    
    def _search_all_files(self):
        """Search all uploaded files"""
        try:
            print("\nSearch All Uploaded Files")
            print("-"*25)
            
            query = input("Enter search query: ").strip()
            if not query:
                print("Please enter a search query.")
                return
            
            print(f"\nSearching for: '{query}'")
            results = self.classification_engine.vector_db.query_collection('cpg_uploaded_files', query, n_results=10)
            
            self._display_search_results(results, "Uploaded Files")
            
        except Exception as e:
            print(f"Error searching files: {e}")
    
    def _search_by_document_type(self):
        """Search files by document type"""
        try:
            print("\nSearch by Document Type")
            print("-"*25)
            
            # Show available document types
            document_types = self.config_manager.get_document_types()
            print("\nAvailable Document Types:")
            all_types = []
            for category, types in document_types.items():
                for doc_type in types:
                    all_types.append(doc_type)
                    print(f"- {doc_type}")
            
            doc_type = input("\nEnter document type: ").strip()
            query = input("Enter search query (optional): ").strip()
            
            if not query:
                query = f"document type {doc_type}"
            
            # Search with document type filter
            where_filter = {"user_document_type": doc_type} if doc_type in all_types else None
            results = self.classification_engine.vector_db.query_collection(
                'cpg_uploaded_files', query, n_results=10, where_filter=where_filter
            )
            
            self._display_search_results(results, f"Document Type: {doc_type}")
            
        except Exception as e:
            print(f"Error searching by document type: {e}")
    
    def _search_by_file_type(self):
        """Search files by file type"""
        try:
            print("\nSearch by File Type")
            print("-"*20)
            
            print("Available file types: csv, excel, pdf, text")
            file_type = input("Enter file type: ").strip().lower()
            query = input("Enter search query (optional): ").strip()
            
            if not query:
                query = f"file type {file_type}"
            
            # Search with file type filter
            where_filter = {"file_type": file_type}
            results = self.classification_engine.vector_db.query_collection(
                'cpg_uploaded_files', query, n_results=10, where_filter=where_filter
            )
            
            self._display_search_results(results, f"File Type: {file_type}")
            
        except Exception as e:
            print(f"Error searching by file type: {e}")
    
    def _search_learning_feedback(self):
        """Search learning feedback and corrections"""
        try:
            print("\nSearch Learning Feedback")
            print("-"*25)
            
            query = input("Enter search query: ").strip()
            if not query:
                print("Please enter a search query.")
                return
            
            results = self.classification_engine.vector_db.query_collection('cpg_learning_feedback', query, n_results=10)
            
            print(f"\nLearning Feedback Search Results for: '{query}'")
            print("="*50)
            
            if results['count'] == 0:
                print("No learning feedback found.")
                return
            
            for i, match in enumerate(results['matches'], 1):
                metadata = match['metadata']
                print(f"\n{i}. Learning Entry")
                print(f"   Predicted: {metadata.get('predicted_classification', 'unknown')}")
                print(f"   Correct: {metadata.get('correct_classification', 'unknown')}")
                print(f"   Confidence: {metadata.get('confidence_score', 0):.2f}")
                print(f"   Learning Type: {metadata.get('learning_type', 'unknown')}")
                print(f"   Similarity: {match['similarity_score']:.2f}")
                
                # Show document preview
                doc_preview = match['document'][:100]
                print(f"   Content: {doc_preview}...")
            
        except Exception as e:
            print(f"Error searching learning feedback: {e}")
    
    def _advanced_search(self):
        """Advanced search with multiple filters"""
        try:
            print("\nAdvanced Search")
            print("-"*15)
            
            query = input("Enter search query: ").strip()
            if not query:
                print("Please enter a search query.")
                return
            
            # Get optional filters
            doc_type = input("Document type filter (optional): ").strip()
            file_type = input("File type filter (optional): ").strip()
            min_similarity = input("Minimum similarity score (0.0-1.0, optional): ").strip()
            
            # Build filter
            where_filter = {}
            if doc_type:
                where_filter["user_document_type"] = doc_type
            if file_type:
                where_filter["file_type"] = file_type
            
            # Perform search
            results = self.classification_engine.vector_db.query_collection(
                'cpg_uploaded_files', query, n_results=15, where_filter=where_filter if where_filter else None
            )
            
            # Apply similarity filter if specified
            if min_similarity:
                try:
                    min_sim = float(min_similarity)
                    filtered_matches = [m for m in results['matches'] if m['similarity_score'] >= min_sim]
                    results['matches'] = filtered_matches
                    results['count'] = len(filtered_matches)
                except ValueError:
                    print("Invalid similarity score, ignoring filter.")
            
            self._display_search_results(results, "Advanced Search")
            
        except Exception as e:
            print(f"Error in advanced search: {e}")
    
    def _display_search_results(self, results: Dict[str, Any], search_type: str):
        """Display search results in a formatted way"""
        print(f"\n{search_type} Results:")
        print("="*40)
        
        if results['count'] == 0:
            print("No results found.")
            return
        
        print(f"Found {results['count']} matches:")
        
        for i, match in enumerate(results['matches'], 1):
            metadata = match['metadata']
            print(f"\n{i}. {metadata.get('source_file', 'Unknown file')}")
            print(f"   Document Type: {metadata.get('user_document_type', 'unknown')}")
            print(f"   File Type: {metadata.get('file_type', 'unknown')}")
            print(f"   Similarity: {match['similarity_score']:.2f}")
            
            description = metadata.get('user_description', '')
            if description:
                print(f"   Description: {description}")
            
            # Show content preview
            doc_preview = match['document'][:150]
            print(f"   Content: {doc_preview}...")
            
            # Show patterns if available
            if metadata.get('has_patterns'):
                print("   Has detected patterns: Yes")
        
        # Show summary statistics
        if results['count'] > 0:
            avg_similarity = sum(m['similarity_score'] for m in results['matches']) / len(results['matches'])
            print(f"\nSummary:")
            print(f"- Average similarity: {avg_similarity:.2f}")
            print(f"- Results shown: {len(results['matches'])}")
    
    def _display_classification_results(self, result: Dict[str, Any], processing_time: float):
        """Display classification results"""
        print("\nClassification Results:")
        print("="*25)
        
        filename = os.path.basename(result['file_path'])
        print(f"File: {filename}")
        print(f"Predicted Type: {result['classification'].upper()}")
        print(f"Confidence: {result['confidence']*100:.1f}%")
        print()
        
        print("Reasoning:")
        reasoning = result.get('reasoning', 'No reasoning provided')
        # Split long reasoning into multiple lines
        if len(reasoning) > 80:
            words = reasoning.split(' ')
            lines = []
            current_line = []
            current_length = 0
            
            for word in words:
                if current_length + len(word) + 1 > 80 and current_line:
                    lines.append(' '.join(current_line))
                    current_line = [word]
                    current_length = len(word)
                else:
                    current_line.append(word)
                    current_length += len(word) + 1
            
            if current_line:
                lines.append(' '.join(current_line))
            
            for line in lines:
                print(f"{line}")
        else:
            print(reasoning)
        
        # Show patterns found
        patterns = result.get('patterns_found', [])
        if patterns:
            print(f"\nPatterns Detected:")
            for pattern in patterns[:5]:
                print(f"- {pattern.replace('_', ' ').title()}")
        
        # Show similar files
        similar_files = result.get('similar_files', [])
        if similar_files:
            print(f"\nSimilar Files Found:")
            print("="*20)
            
            # Group by similarity ranges
            high_similarity = [f for f in similar_files if f['similarity'] >= 0.8]
            medium_similarity = [f for f in similar_files if 0.6 <= f['similarity'] < 0.8]
            low_similarity = [f for f in similar_files if 0.4 <= f['similarity'] < 0.6]
            
            if high_similarity:
                print("\nMost Similar (Similarity > 0.8):")
                for i, file_info in enumerate(high_similarity[:3], 1):
                    print(f"{i}. {file_info['filename']} - {file_info['document_type']} ({file_info['similarity']:.2f})")
            
            if medium_similarity:
                print("\nSimilar (Similarity 0.6-0.8):")
                for i, file_info in enumerate(medium_similarity[:3], 1):
                    print(f"{i}. {file_info['filename']} - {file_info['document_type']} ({file_info['similarity']:.2f})")
            
            if low_similarity:
                print("\nSomewhat Similar (Similarity 0.4-0.6):")
                for i, file_info in enumerate(low_similarity[:2], 1):
                    print(f"{i}. {file_info['filename']} - {file_info['document_type']} ({file_info['similarity']:.2f})")
        
        print(f"\nProcessing completed in {processing_time:.1f} seconds.")
    
    def _learning_training_menu(self):
        """Handle learning and training menu"""
        while True:
            print("\n" + "="*30)
            print("Learning & Training")
            print("="*30)
            print("1. View classification accuracy stats")
            print("2. Review recent corrections")
            print("3. Manually add training examples")
            print("4. Export learning data")
            print("5. Reset learning data")
            print("6. Back to main menu")
            print("-"*30)
            
            choice = input("Enter your choice (1-6): ").strip()
            
            if choice == '1':
                self._show_learning_statistics()
            elif choice == '2':
                self._review_recent_corrections()
            elif choice == '3':
                self._manually_add_training_example()
            elif choice == '4':
                self._export_learning_data()
            elif choice == '5':
                self._reset_learning_data()
            elif choice == '6':
                break
            else:
                print("Invalid choice. Please try again.")
    
    def _show_learning_statistics(self):
        """Show learning statistics"""
        try:
            self.classification_engine.learning_engine.show_learning_statistics()
        except Exception as e:
            print(f"Error showing learning statistics: {e}")
    
    def _review_recent_corrections(self):
        """Review recent corrections (placeholder)"""
        print("\nRecent Corrections")
        print("="*20)
        print("Recent corrections review functionality not implemented yet.")
        print("You can view learning statistics for overall correction information.")
    
    def _manually_add_training_example(self):
        """Manually add training example"""
        try:
            print("\nManually Add Training Example")
            print("="*35)
            
            file_path = input("Enter file path: ").strip()
            
            if not os.path.exists(file_path):
                print("File not found!")
                return
            
            print("\nSelect correct data type:")
            print("1. syndicated")
            print("2. pos")
            print("3. promotion")
            
            choice = input("Enter choice (1-3): ").strip()
            choice_map = {'1': 'syndicated', '2': 'pos', '3': 'promotion'}
            
            if choice not in choice_map:
                print("Invalid choice.")
                return
            
            data_type = choice_map[choice]
            description = input("Enter description: ").strip()
            
            # Upload as training example
            result = self.classification_engine.upload_file_with_metadata(
                file_path, f"{data_type}_data", description
            )
            
            if result['success']:
                print("Training example added successfully!")
            else:
                print(f"Failed to add training example: {result.get('error')}")
                
        except Exception as e:
            print(f"Error adding training example: {e}")
    
    def _export_learning_data(self):
        """Export learning data (placeholder)"""
        print("\nExport Learning Data")
        print("="*20)
        print("Export functionality not implemented yet.")
        print("Learning data is stored in the vector database.")
    
    def _reset_learning_data(self):
        """Reset learning data (placeholder)"""
        print("\nReset Learning Data")
        print("="*20)
        
        confirm = input("This will delete all learning data. Are you sure? (y/n): ").strip().lower()
        
        if confirm == 'y':
            print("Reset functionality not implemented yet.")
            print("Contact administrator to reset learning data.")
        else:
            print("Reset cancelled.")
    
    def _system_status_menu(self):
        """Show system status"""
        try:
            print("\nSystem Status")
            print("="*15)
            
            stats = self.classification_engine.get_knowledge_base_stats()
            
            print("\nVector Database:")
            print("-"*20)
            for collection, info in stats.get('vector_database', {}).items():
                print(f"{collection}: {info['document_count']} documents")
            
            print(f"\nTotal Documents: {stats.get('total_documents', 0)}")
            
            print("\nSupported File Types:")
            print("-"*25)
            supported_types = stats.get('supported_file_types', [])
            print(", ".join(supported_types))
            
            learning_stats = stats.get('learning_system', {})
            if learning_stats:
                print("\nLearning System:")
                print("-"*20)
                print(f"Total Classifications: {learning_stats.get('total_classifications', 0)}")
                print(f"Current Accuracy: {learning_stats.get('accuracy_percentage', 0):.1f}%")
                print(f"Total Corrections: {learning_stats.get('corrections_count', 0)}")
            
        except Exception as e:
            print(f"Error getting system status: {e}")

def main():
    """Main entry point for CLI"""
    cli = CPGClassificationCLI()
    cli.run()

if __name__ == "__main__":
    main()