import pandas as pd
import os
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import logging
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).resolve().parent.parent.parent
sys.path.insert(0, str(project_root))

from core.knowledgebase.document_models import FileDocument
from core.knowledgebase.file_processors import FileProcessorFactory
from core.knowledgebase.vector_db_manager import VectorDBManager
from core.knowledgebase.learning_engine import LearningEngine

class ClassificationEngine:
    """Main engine for file classification with learning capabilities"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.vector_db = VectorDBManager(config['vector_db'])
        self.learning_engine = LearningEngine(self.vector_db, config)
        self.file_processor_factory = FileProcessorFactory()
        
        # Classification configuration
        self.classification_config = config.get('classification', {})
        self.confidence_threshold = self.classification_config.get('confidence_threshold', 0.6)
        self.data_types = self.classification_config.get('data_types', ['syndicated', 'pos', 'promotion'])
    
    def classify_file_by_path(self, file_path: str, collect_feedback: bool = True) -> Dict[str, Any]:
        """Classify a file by its path with optional learning feedback - IMPROVED VERSION"""
        try:
            if not os.path.exists(file_path):
                return {
                    'success': False,
                    'error': f'File not found: {file_path}',
                    'file_path': file_path
                }
            
            # Process file to extract features
            processor = self.file_processor_factory.get_processor(file_path)
            if not processor:
                return {
                    'success': False,
                    'error': f'Unsupported file type: {file_path}',
                    'file_path': file_path
                }
            
            # Extract content and features
            extracted_content = processor.extract_content(file_path)
            if 'error' in extracted_content:
                return {
                    'success': False,
                    'error': extracted_content['error'],
                    'file_path': file_path
                }
            
            # Perform classification analysis
            classification_result = self._analyze_and_classify(file_path, extracted_content)
            
            # IMPORTANT: Results are returned immediately here
            # Feedback collection is handled by the CLI after showing results
            
            return classification_result
            
        except Exception as e:
            self.logger.error(f"Failed to classify file {file_path}: {e}")
            return {
                'success': False,
                'error': str(e),
                'file_path': file_path
            }
    
    def collect_feedback_on_result(self, classification_result: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Separate method to collect feedback on classification result"""
        try:
            if not classification_result.get('success', False):
                return None
                
            if self.classification_config.get('enable_learning', True):
                return self.learning_engine.collect_classification_feedback(classification_result)
            
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to collect feedback: {e}")
            return None
    
    def classify_dataframe(self, df: pd.DataFrame, filename: str = "unknown") -> Dict[str, Any]:
        """Classify a DataFrame directly for programmatic use"""
        try:
            # Extract features from DataFrame
            extracted_content = self._extract_dataframe_features(df)
            
            # Perform classification analysis
            classification_result = self._analyze_and_classify(filename, extracted_content)
            
            return classification_result
            
        except Exception as e:
            self.logger.error(f"Failed to classify DataFrame {filename}: {e}")
            return {
                'success': False,
                'error': str(e),
                'file_path': filename
            }
    
    def _extract_dataframe_features(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Extract features from a pandas DataFrame"""
        try:
            columns = df.columns.tolist()
            data_types = [str(dtype) for dtype in df.dtypes]
            
            # Analyze patterns similar to CSV processor
            from .file_processors import CSVProcessor
            csv_processor = CSVProcessor()
            
            # Generate content summary
            content_parts = [
                f"DataFrame with {len(columns)} columns and {len(df)} rows",
                f"Columns: {', '.join(columns[:10])}{'...' if len(columns) > 10 else ''}",
                f"Data types: {', '.join(set(data_types))}",
                f"Sample data patterns: {csv_processor._analyze_patterns(df)}"
            ]
            
            # Statistical summary
            stats = {}
            numeric_cols = df.select_dtypes(include=['number']).columns
            if len(numeric_cols) > 0:
                stats = df[numeric_cols].describe().to_dict()
            
            return {
                "content": " | ".join(content_parts),
                "schema": {
                    "columns": columns,
                    "data_types": data_types,
                    "row_count": len(df),
                    "column_count": len(columns)
                },
                "statistical_summary": stats,
                "data_patterns": csv_processor._detect_data_patterns(df),
                "sample_data": df.head(3).to_dict('records') if len(df) > 0 else []
            }
            
        except Exception as e:
            self.logger.error(f"Failed to extract DataFrame features: {e}")
            return {"content": "Error extracting DataFrame features", "error": str(e)}
    
    def _analyze_and_classify(self, file_path: str, extracted_content: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze extracted content and perform classification"""
        try:
            # Build analysis features
            analysis_features = self._build_analysis_features(extracted_content)
            
            # Search for similar patterns in knowledge base
            search_results = self._search_similar_patterns(analysis_features)
            
            # Make classification decision
            classification_decision = self._make_classification_decision(search_results, analysis_features)
            
            # Build comprehensive result
            result = {
                'success': True,
                'file_path': file_path,
                'classification': classification_decision['predicted_type'],
                'confidence': classification_decision['confidence'],
                'reasoning': classification_decision['reasoning'],
                'patterns_found': analysis_features.get('detected_patterns', []),
                'similar_files': search_results.get('similar_files', []),
                'search_results': search_results,
                'file_features': analysis_features,
                'evidence_scores': classification_decision.get('evidence_scores', {}),
                'processing_time': classification_decision.get('processing_time', 0)
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to analyze and classify: {e}")
            return {
                'success': False,
                'error': str(e),
                'file_path': file_path
            }
    
    def _build_analysis_features(self, extracted_content: Dict[str, Any]) -> Dict[str, Any]:
        """Build analysis features from extracted content"""
        features = {}
        
        # Schema features
        if 'schema' in extracted_content:
            schema = extracted_content['schema']
            features['schema_info'] = schema
            features['columns'] = schema.get('columns', [])
            features['data_types'] = schema.get('data_types', [])
            features['column_count'] = schema.get('column_count', 0)
            features['row_count'] = schema.get('row_count', 0)
        
        # Content features
        features['content_summary'] = extracted_content.get('content', '')
        features['detected_patterns'] = extracted_content.get('data_patterns', [])
        features['statistical_summary'] = extracted_content.get('statistical_summary', {})
        
        # Business context analysis
        features['business_indicators'] = self._detect_business_indicators(features)
        features['data_granularity'] = self._detect_data_granularity(features)
        features['temporal_patterns'] = self._detect_temporal_patterns(features)
        
        return features
    
    def _detect_business_indicators(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """Detect business-specific indicators"""
        indicators = {
            'syndicated_indicators': [],
            'pos_indicators': [],
            'promotion_indicators': []
        }
        
        columns = [col.lower() for col in features.get('columns', [])]
        patterns = [pattern.lower() for pattern in features.get('detected_patterns', [])]
        
        # Syndicated indicators
        syndicated_keywords = ['tdp', 'acv', 'velocity', 'market', 'category', 'brand']
        for keyword in syndicated_keywords:
            if any(keyword in col for col in columns):
                indicators['syndicated_indicators'].append(f"Column contains '{keyword}'")
        
        if 'syndicated_metrics' in patterns:
            indicators['syndicated_indicators'].append("Syndicated metrics pattern detected")
        
        # POS indicators
        pos_keywords = ['store', 'upc', 'sku', 'transaction', 'scan', 'register']
        for keyword in pos_keywords:
            if any(keyword in col for col in columns):
                indicators['pos_indicators'].append(f"Column contains '{keyword}'")
        
        if 'pos_transaction' in patterns:
            indicators['pos_indicators'].append("POS transaction pattern detected")
        
        # Promotion indicators
        promo_keywords = ['campaign', 'promotion', 'promo', 'discount', 'offer']
        for keyword in promo_keywords:
            if any(keyword in col for col in columns):
                indicators['promotion_indicators'].append(f"Column contains '{keyword}'")
        
        if 'promotion_data' in patterns:
            indicators['promotion_indicators'].append("Promotion data pattern detected")
        
        return indicators
    
    def _detect_data_granularity(self, features: Dict[str, Any]) -> str:
        """Detect the granularity level of the data"""
        columns = [col.lower() for col in features.get('columns', [])]
        
        if any('store' in col for col in columns):
            return 'store_level'
        elif any('market' in col for col in columns):
            return 'market_level'
        elif any('transaction' in col for col in columns):
            return 'transaction_level'
        elif any('campaign' in col for col in columns):
            return 'campaign_level'
        else:
            return 'unknown'
    
    def _detect_temporal_patterns(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """Detect temporal patterns in the data"""
        columns = [col.lower() for col in features.get('columns', [])]
        temporal_info = {}
        
        # Look for date/time columns
        date_keywords = ['date', 'week', 'month', 'period', 'time']
        date_columns = []
        
        for col in columns:
            if any(keyword in col for keyword in date_keywords):
                date_columns.append(col)
        
        temporal_info['date_columns'] = date_columns
        temporal_info['has_temporal_data'] = len(date_columns) > 0
        
        # Infer frequency
        if any('week' in col for col in date_columns):
            temporal_info['likely_frequency'] = 'weekly'
        elif any('month' in col for col in date_columns):
            temporal_info['likely_frequency'] = 'monthly'
        elif any('day' in col or 'date' in col for col in date_columns):
            temporal_info['likely_frequency'] = 'daily'
        else:
            temporal_info['likely_frequency'] = 'unknown'
        
        return temporal_info
    
    def _search_similar_patterns(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """Search for similar patterns in the knowledge base"""
        try:
            # Build search query from features
            search_query = self._build_search_query(features)
            
            # Search uploaded files
            uploaded_files_results = self.vector_db.query_collection(
                'cpg_uploaded_files', 
                search_query, 
                n_results=10
            )
            
            # Search learning feedback for corrections
            learning_results = self.vector_db.query_collection(
                'cpg_learning_feedback',
                search_query,
                n_results=5
            )
            
            # Format results
            similar_files = []
            for match in uploaded_files_results.get('matches', []):
                similar_files.append({
                    'filename': match['metadata'].get('source_file', 'unknown'),
                    'document_type': match['metadata'].get('user_document_type', 'unknown'),
                    'similarity': match['similarity_score'],
                    'file_type': match['metadata'].get('file_type', 'unknown')
                })
            
            return {
                'uploaded_files_matches': uploaded_files_results,
                'learning_matches': learning_results,
                'similar_files': similar_files,
                'total_matches': uploaded_files_results.get('count', 0)
            }
            
        except Exception as e:
            self.logger.error(f"Failed to search similar patterns: {e}")
            return {'similar_files': [], 'total_matches': 0}
    
    def _build_search_query(self, features: Dict[str, Any]) -> str:
        """Build search query from analysis features"""
        query_parts = []
        
        # Add column information
        columns = features.get('columns', [])
        if columns:
            query_parts.append(f"columns: {', '.join(columns[:5])}")
        
        # Add patterns
        patterns = features.get('detected_patterns', [])
        if patterns:
            query_parts.append(f"patterns: {', '.join(patterns)}")
        
        # Add business indicators
        business_indicators = features.get('business_indicators', {})
        for indicator_type, indicators in business_indicators.items():
            if indicators:
                query_parts.append(f"{indicator_type}: {', '.join(indicators[:2])}")
        
        # Add granularity
        granularity = features.get('data_granularity', '')
        if granularity != 'unknown':
            query_parts.append(f"granularity: {granularity}")
        
        return " | ".join(query_parts)
    
    def _make_classification_decision(self, search_results: Dict[str, Any], features: Dict[str, Any]) -> Dict[str, Any]:
        """Make final classification decision based on analysis"""
        try:
            import time
            start_time = time.time()
            
            # Initialize evidence scores
            evidence = {data_type: 0.0 for data_type in self.data_types}
            reasoning_parts = []
            
            # Analyze business indicators (highest weight - works even without existing data)
            business_indicators = features.get('business_indicators', {})
            for indicator_type, indicators in business_indicators.items():
                if indicators:
                    data_type = indicator_type.replace('_indicators', '')
                    if data_type in evidence:
                        score = len(indicators) * 0.3  # 0.3 per indicator
                        evidence[data_type] += score
                        reasoning_parts.append(f"{data_type.title()} indicators: {', '.join(indicators[:2])}")
            
            # Analyze data granularity (works without existing data)
            granularity = features.get('data_granularity', '')
            if granularity == 'store_level':
                evidence['pos'] += 0.2
                reasoning_parts.append("Store-level granularity suggests POS data")
            elif granularity == 'market_level':
                evidence['syndicated'] += 0.2
                reasoning_parts.append("Market-level granularity suggests syndicated data")
            elif granularity == 'campaign_level':
                evidence['promotion'] += 0.2
                reasoning_parts.append("Campaign-level granularity suggests promotion data")
            
            # Analyze temporal patterns (works without existing data)
            temporal = features.get('temporal_patterns', {})
            frequency = temporal.get('likely_frequency', '')
            if frequency == 'weekly':
                evidence['syndicated'] += 0.15
                reasoning_parts.append("Weekly frequency typical of syndicated data")
            elif frequency == 'daily':
                evidence['pos'] += 0.15
                reasoning_parts.append("Daily frequency typical of POS data")
            
            # Analyze column patterns directly (works without existing data)
            columns = [col.lower() for col in features.get('columns', [])]
            
            # Direct column analysis for syndicated data
            syndicated_columns = ['tdp', 'acv', 'velocity', 'market', 'category', 'brand', 'total_distribution', 'all_commodity']
            syndicated_matches = sum(1 for col in columns for syn_col in syndicated_columns if syn_col in col)
            if syndicated_matches > 0:
                evidence['syndicated'] += syndicated_matches * 0.1
                reasoning_parts.append(f"Contains {syndicated_matches} syndicated-specific column patterns")
            
            # Direct column analysis for POS data
            pos_columns = ['store', 'upc', 'sku', 'transaction', 'scan', 'register', 'checkout', 'receipt']
            pos_matches = sum(1 for col in columns for pos_col in pos_columns if pos_col in col)
            if pos_matches > 0:
                evidence['pos'] += pos_matches * 0.1
                reasoning_parts.append(f"Contains {pos_matches} POS-specific column patterns")
            
            # Direct column analysis for promotion data
            promo_columns = ['campaign', 'promotion', 'promo', 'discount', 'offer', 'deal', 'mechanic']
            promo_matches = sum(1 for col in columns for promo_col in promo_columns if promo_col in col)
            if promo_matches > 0:
                evidence['promotion'] += promo_matches * 0.1
                reasoning_parts.append(f"Contains {promo_matches} promotion-specific column patterns")
            
            # Analyze similar files (only if they exist)
            similar_files = search_results.get('similar_files', [])
            if similar_files:
                for similar_file in similar_files[:5]:  # Top 5 similar files
                    doc_type = similar_file.get('document_type', '')
                    similarity = similar_file.get('similarity', 0)
                    
                    # Map document types to data types
                    if any(keyword in doc_type.lower() for keyword in ['syndicated']):
                        evidence['syndicated'] += similarity * 0.15
                        reasoning_parts.append(f"Similar to {similar_file['filename']} (syndicated, {similarity:.2f})")
                    elif any(keyword in doc_type.lower() for keyword in ['pos']):
                        evidence['pos'] += similarity * 0.15
                        reasoning_parts.append(f"Similar to {similar_file['filename']} (POS, {similarity:.2f})")
                    elif any(keyword in doc_type.lower() for keyword in ['promotion', 'promo']):
                        evidence['promotion'] += similarity * 0.15
                        reasoning_parts.append(f"Similar to {similar_file['filename']} (promotion, {similarity:.2f})")
            
            # Additional pattern-based analysis
            detected_patterns = features.get('detected_patterns', [])
            for pattern in detected_patterns:
                if 'syndicated' in pattern.lower():
                    evidence['syndicated'] += 0.1
                    reasoning_parts.append(f"Detected syndicated pattern: {pattern}")
                elif 'pos' in pattern.lower() or 'transaction' in pattern.lower():
                    evidence['pos'] += 0.1
                    reasoning_parts.append(f"Detected POS pattern: {pattern}")
                elif 'promotion' in pattern.lower():
                    evidence['promotion'] += 0.1
                    reasoning_parts.append(f"Detected promotion pattern: {pattern}")
            
            # Make final decision
            if sum(evidence.values()) == 0:
                # If no evidence found, try to make an educated guess based on column count and names
                column_count = len(columns)
                
                if column_count <= 5 and any('campaign' in col or 'promo' in col for col in columns):
                    predicted_type = 'promotion'
                    confidence = 0.3
                    reasoning = "Few columns with promotion-related terms suggests promotion data"
                elif column_count >= 10 and any('market' in col for col in columns):
                    predicted_type = 'syndicated'
                    confidence = 0.4
                    reasoning = "Many columns with market references suggests syndicated data"
                elif any('store' in col or 'upc' in col for col in columns):
                    predicted_type = 'pos'
                    confidence = 0.4
                    reasoning = "Store or UPC references suggest POS data"
                else:
                    predicted_type = 'unknown'
                    confidence = 0.0
                    reasoning = "Insufficient indicators for classification - need more data in knowledge base"
            else:
                predicted_type = max(evidence, key=evidence.get)
                confidence = evidence[predicted_type] / sum(evidence.values())
                reasoning = "; ".join(reasoning_parts[:5])  # Top 5 reasons
            
            processing_time = time.time() - start_time
            
            return {
                'predicted_type': predicted_type,
                'confidence': confidence,
                'evidence_scores': evidence,
                'reasoning': reasoning,
                'processing_time': processing_time
            }
            
        except Exception as e:
            self.logger.error(f"Failed to make classification decision: {e}")
            return {
                'predicted_type': 'unknown',
                'confidence': 0.0,
                'reasoning': f"Classification error: {str(e)}",
                'evidence_scores': {},
                'processing_time': 0
            }
    
    def upload_file_with_metadata(self, file_path: str, document_type: str, description: str = "") -> Dict[str, Any]:
        """Upload file to knowledge base with metadata"""
        try:
            if not os.path.exists(file_path):
                return {
                    'success': False,
                    'error': f'File not found: {file_path}'
                }
            
            # Process file
            processor = self.file_processor_factory.get_processor(file_path)
            if not processor:
                return {
                    'success': False,
                    'error': f'Unsupported file type: {file_path}'
                }
            
            # Extract content
            extracted_content = processor.extract_content(file_path)
            if 'error' in extracted_content:
                return {
                    'success': False,
                    'error': extracted_content['error']
                }
            
            # Get file info
            file_size = os.path.getsize(file_path)
            file_type = self._get_file_type(file_path)
            
            # Create document
            file_doc = FileDocument(
                file_path=file_path,
                file_type=file_type,
                file_size=file_size,
                extracted_content=extracted_content,
                user_document_type=document_type,
                user_description=description,
                processing_metadata={
                    'processor_type': processor.__class__.__name__,
                    'processing_timestamp': pd.Timestamp.now().isoformat(),
                    'file_size_mb': round(file_size / (1024 * 1024), 2)
                }
            )
            
            # Add to vector database
            success = self.vector_db.add_documents('cpg_uploaded_files', [file_doc])
            
            if success:
                return {
                    'success': True,
                    'document_id': file_doc.id,
                    'file_path': file_path,
                    'document_type': document_type,
                    'description': description,
                    'file_size': file_size,
                    'extracted_patterns': extracted_content.get('data_patterns', [])
                }
            else:
                return {
                    'success': False,
                    'error': 'Failed to add document to vector database'
                }
                
        except Exception as e:
            self.logger.error(f"Failed to upload file {file_path}: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _get_file_type(self, file_path: str) -> str:
        """Get file type from extension"""
        extension = Path(file_path).suffix.lower()
        type_mapping = {
            '.csv': 'csv',
            '.tsv': 'csv',
            '.xlsx': 'excel',
            '.xls': 'excel',
            '.pdf': 'pdf',
            '.txt': 'text',
            '.log': 'text',
            '.md': 'text'
        }
        return type_mapping.get(extension, 'unknown')
    
    def get_uploaded_files(self, document_type: str = None) -> List[Dict[str, Any]]:
        """Get list of uploaded files with optional type filter"""
        try:
            where_filter = {"user_document_type": document_type} if document_type else None
            results = self.vector_db.query_collection('cpg_uploaded_files', "", n_results=100, where_filter=where_filter)
            
            files = []
            for match in results.get('matches', []):
                files.append({
                    'id': match['metadata'].get('id', ''),
                    'filename': match['metadata'].get('source_file', ''),
                    'document_type': match['metadata'].get('user_document_type', ''),
                    'description': match['metadata'].get('user_description', ''),
                    'file_type': match['metadata'].get('file_type', ''),
                    'file_size': match['metadata'].get('file_size', 0),
                    'upload_date': match['metadata'].get('created_at', '')
                })
            
            return files
            
        except Exception as e:
            self.logger.error(f"Failed to get uploaded files: {e}")
            return []
    
    def get_knowledge_base_stats(self) -> Dict[str, Any]:
        """Get comprehensive knowledge base statistics"""
        try:
            # Get vector database stats
            db_stats = self.vector_db.get_collection_stats()
            
            # Get learning stats
            learning_stats = self.learning_engine.get_learning_statistics()
            
            # Combine stats
            combined_stats = {
                'vector_database': db_stats,
                'learning_system': learning_stats,
                'total_documents': sum(collection['document_count'] for collection in db_stats.values()),
                'supported_file_types': self.file_processor_factory.get_supported_extensions()
            }
            
            return combined_stats
            
        except Exception as e:
            self.logger.error(f"Failed to get knowledge base stats: {e}")
            return {}