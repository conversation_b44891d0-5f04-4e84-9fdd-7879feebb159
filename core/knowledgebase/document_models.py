from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional
from datetime import datetime
import hashlib
import json


@dataclass
class BaseDocument:
    """Base class for all document types"""
    id: str = ""
    content: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)
    document_type: str = ""
    created_at: str = ""
    source_file: Optional[str] = None
    
    def __post_init__(self):
        if not self.id:
            self.id = self._generate_id()
        if not self.created_at:
            self.created_at = datetime.now().isoformat()
    
    def _generate_id(self) -> str:
        """Generate unique ID based on content hash"""
        content_hash = hashlib.md5(self.content.encode()).hexdigest()
        return f"{self.document_type}_{content_hash[:12]}"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert document to dictionary"""
        return {
            "id": self.id,
            "content": self.content,
            "metadata": self.metadata,
            "document_type": self.document_type,
            "created_at": self.created_at,
            "source_file": self.source_file
        }

@dataclass
class FileDocument(BaseDocument):
    """Document representing an uploaded file with metadata"""
    file_path: str = ""
    file_type: str = ""
    file_size: int = 0
    extracted_content: Dict[str, Any] = field(default_factory=dict)
    processing_metadata: Dict[str, Any] = field(default_factory=dict)
    user_document_type: str = ""
    user_description: str = ""
    
    def __init__(self, file_path: str = "", file_type: str = "", file_size: int = 0,
                 extracted_content: Dict[str, Any] = None, processing_metadata: Dict[str, Any] = None,
                 user_document_type: str = "", user_description: str = "",
                 id: str = "", metadata: Dict[str, Any] = None):
        
        if extracted_content is None:
            extracted_content = {}
        if processing_metadata is None:
            processing_metadata = {}
        
        content = self._build_content(file_path, file_type, extracted_content, user_document_type, user_description)
        metadata = metadata or {}
        metadata.update({
            "file_type": file_type,
            "file_size": file_size,
            "source_file": file_path.split('/')[-1] if file_path else "",
            "user_document_type": user_document_type,
            "user_description": user_description,
            "has_schema": "schema" in extracted_content,
            "has_patterns": "data_patterns" in extracted_content
        })
        
        super().__init__(
            id=id,
            content=content,
            metadata=metadata,
            document_type="uploaded_file",
            created_at="",
            source_file=file_path.split('/')[-1] if file_path else ""
        )
        
        self.file_path = file_path
        self.file_type = file_type
        self.file_size = file_size
        self.extracted_content = extracted_content
        self.processing_metadata = processing_metadata
        self.user_document_type = user_document_type
        self.user_description = user_description
    
    def _build_content(self, file_path: str, file_type: str, extracted_content: Dict[str, Any],
                      user_document_type: str, user_description: str) -> str:
        """Build searchable content from file information"""
        filename = file_path.split('/')[-1] if file_path else "unknown_file"
        content_parts = [
            f"File: {filename}",
            f"Type: {file_type}",
            f"Document Type: {user_document_type}",
            f"Description: {user_description}",
            f"Content: {extracted_content.get('content', 'No content extracted')}"
        ]
        
        # Add schema information if available
        if "schema" in extracted_content:
            schema = extracted_content["schema"]
            if isinstance(schema, dict) and schema:
                if "columns" in schema:
                    content_parts.append(f"Columns: {', '.join(schema['columns'][:5])}")
        
        # Add patterns if available
        if "data_patterns" in extracted_content:
            patterns = extracted_content["data_patterns"]
            if patterns:
                content_parts.append(f"Patterns: {', '.join(patterns[:5])}")
        
        return " | ".join(content_parts)

@dataclass 
class LearningDocument(BaseDocument):
    """Document representing learning feedback and corrections"""
    original_file_path: str = ""
    predicted_classification: str = ""
    correct_classification: str = ""
    confidence_score: float = 0.0
    user_feedback: str = ""
    learning_type: str = ""  # correction, training_example, both
    file_features: Dict[str, Any] = field(default_factory=dict)
    
    def __init__(self, original_file_path: str = "", predicted_classification: str = "",
                 correct_classification: str = "", confidence_score: float = 0.0,
                 user_feedback: str = "", learning_type: str = "",
                 file_features: Dict[str, Any] = None,
                 id: str = "", metadata: Dict[str, Any] = None):
        
        if file_features is None:
            file_features = {}
        
        content = self._build_content(original_file_path, predicted_classification, 
                                    correct_classification, user_feedback)
        metadata = metadata or {}
        metadata.update({
            "predicted_classification": predicted_classification,
            "correct_classification": correct_classification,
            "confidence_score": confidence_score,
            "learning_type": learning_type,
            "is_correction": predicted_classification != correct_classification
        })
        
        super().__init__(
            id=id,
            content=content,
            metadata=metadata,
            document_type="cpg_learning_feedback",
            created_at="",
            source_file=original_file_path.split('/')[-1] if original_file_path else ""
        )
        
        self.original_file_path = original_file_path
        self.predicted_classification = predicted_classification
        self.correct_classification = correct_classification
        self.confidence_score = confidence_score
        self.user_feedback = user_feedback
        self.learning_type = learning_type
        self.file_features = file_features
    
    def _build_content(self, file_path: str, predicted: str, correct: str, feedback: str) -> str:
        """Build searchable content from learning information"""
        filename = file_path.split('/')[-1] if file_path else "unknown_file"
        content_parts = [
            f"File: {filename}",
            f"Predicted: {predicted}",
            f"Correct: {correct}",
            f"Feedback: {feedback}"
        ]
        return " | ".join(content_parts)