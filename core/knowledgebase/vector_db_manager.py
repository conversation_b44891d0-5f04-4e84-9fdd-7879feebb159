import chromadb
from chromadb.config import Settings
from sentence_transformers import SentenceTransformer
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).resolve().parent.parent.parent
sys.path.insert(0, str(project_root))

from core.knowledgebase.document_models import BaseDocument

class EmbeddingService:
    """Service for generating vector embeddings"""
    
    def __init__(self, model_name: str = "all-MiniLM-L6-v2"):
        self.model_name = model_name
        self.model = None
        self.logger = logging.getLogger(__name__)
        self._load_model()
    
    def _load_model(self):
        """Load the embedding model"""
        try:
            self.model = SentenceTransformer(self.model_name)
            self.logger.info(f"Embedding model {self.model_name} loaded successfully")
        except Exception as e:
            self.logger.error(f"Failed to load embedding model: {e}")
            raise
    
    def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for a list of texts"""
        try:
            if not texts:
                return []
            
            # Clean and truncate texts
            cleaned_texts = []
            for text in texts:
                cleaned_text = ' '.join(text.split())[:2000]
                cleaned_texts.append(cleaned_text)
            
            embeddings = self.model.encode(cleaned_texts, convert_to_tensor=False, show_progress_bar=False)
            return embeddings.tolist()
        except Exception as e:
            self.logger.error(f"Failed to generate embeddings: {e}")
            raise
    
    def generate_single_embedding(self, text: str) -> List[float]:
        """Generate embedding for a single text"""
        return self.generate_embeddings([text])[0]

class VectorDBManager:
    """Manager for ChromaDB operations"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.embedding_service = EmbeddingService(config.get('embedding_model', 'all-MiniLM-L6-v2'))
        self.client = None
        self.collections = {}
        self.logger = logging.getLogger(__name__)
        self._initialize_client()
        self._setup_collections()
    
    def _initialize_client(self):
        """Initialize ChromaDB client"""
        try:
            persist_dir = self.config['persist_directory']
            Path(persist_dir).mkdir(parents=True, exist_ok=True)
            
            self.client = chromadb.PersistentClient(
                path=persist_dir,
                settings=Settings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )
            self.logger.info(f"ChromaDB client initialized at {persist_dir}")
        except Exception as e:
            self.logger.error(f"Failed to initialize ChromaDB client: {e}")
            raise
    
    def _setup_collections(self):
        """Setup collections for different document types"""
        collection_configs = {
            "cpg_uploaded_files": "User uploaded files with metadata and content",
            "cpg_learning_feedback": "Learning feedback and classification corrections",
            "cpg_schemas": "Database schema patterns and structures", 
            "cpg_content": "Data content patterns and statistical summaries",
            "cpg_domain_knowledge": "CPG domain expertise and business rules"
        }
        
        prefix = self.config.get('collection_prefix', '')
        
        for collection_name, description in collection_configs.items():
            try:
                full_name = f"{prefix}{collection_name}"
                collection = self.client.get_or_create_collection(
                    name=full_name,
                    metadata={"description": description}
                )
                self.collections[collection_name] = collection
                self.logger.info(f"Collection '{full_name}' ready")
            except Exception as e:
                self.logger.error(f"Failed to setup collection {collection_name}: {e}")
    
    def add_documents(self, collection_name: str, documents: List[BaseDocument]) -> bool:
        """Add documents to a specific collection"""
        try:
            if collection_name not in self.collections:
                raise ValueError(f"Collection {collection_name} not found")
            
            if not documents:
                self.logger.warning("No documents provided")
                return True
            
            # Prepare data for ChromaDB
            doc_ids = [doc.id for doc in documents]
            doc_contents = [doc.content for doc in documents]
            doc_metadatas = [doc.metadata for doc in documents]
            
            # Generate embeddings
            embeddings = self.embedding_service.generate_embeddings(doc_contents)
            
            # Add to collection
            collection = self.collections[collection_name]
            collection.add(
                ids=doc_ids,
                documents=doc_contents,
                embeddings=embeddings,
                metadatas=doc_metadatas
            )
            
            self.logger.info(f"Added {len(documents)} documents to {collection_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to add documents to {collection_name}: {e}")
            return False
    
    def query_collection(self, collection_name: str, query_text: str,
                        n_results: int = 10, where_filter: Dict = None) -> Dict[str, Any]:
        """Query a specific collection"""
        try:
            if collection_name not in self.collections:
                raise ValueError(f"Collection {collection_name} not found")
            
            # Generate query embedding
            query_embedding = self.embedding_service.generate_single_embedding(query_text)
            
            # Query collection
            collection = self.collections[collection_name]
            results = collection.query(
                query_embeddings=[query_embedding],
                n_results=n_results,
                where=where_filter,
                include=["documents", "metadatas", "distances"]
            )
            
            return self._format_query_results(results)
            
        except Exception as e:
            self.logger.error(f"Failed to query collection {collection_name}: {e}")
            return {"matches": [], "count": 0}
    
    def get_collection_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics for all collections"""
        stats = {}
        try:
            for name, collection in self.collections.items():
                count = collection.count()
                stats[name] = {
                    "document_count": count,
                    "collection_name": collection.name
                }
            return stats
        except Exception as e:
            self.logger.error(f"Failed to get collection stats: {e}")
            return {}
    
    def delete_documents(self, collection_name: str, document_ids: List[str]) -> bool:
        """Delete documents from a collection"""
        try:
            if collection_name not in self.collections:
                raise ValueError(f"Collection {collection_name} not found")
            
            collection = self.collections[collection_name]
            collection.delete(ids=document_ids)
            
            self.logger.info(f"Deleted {len(document_ids)} documents from {collection_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to delete documents from {collection_name}: {e}")
            return False
    
    def _format_query_results(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Format query results for consistent output"""
        if not results or not results.get("documents"):
            return {"matches": [], "count": 0}
        
        formatted_results = []
        documents = results["documents"][0] if results["documents"] else []
        metadatas = results["metadatas"][0] if results["metadatas"] else []
        distances = results["distances"][0] if results["distances"] else []
        
        for doc, metadata, distance in zip(documents, metadatas, distances):
            formatted_results.append({
                "document": doc,
                "metadata": metadata,
                "similarity_score": 1 - distance,
                "distance": distance
            })
        
        return {
            "matches": formatted_results,
            "count": len(formatted_results)
        }
