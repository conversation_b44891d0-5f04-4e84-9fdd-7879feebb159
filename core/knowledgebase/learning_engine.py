import json
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging

import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).resolve().parent.parent.parent
sys.path.insert(0, str(project_root))

from core.knowledgebase.document_models import LearningDocument
from core.knowledgebase.vector_db_manager import VectorDBManager


class LearningEngine:
    """Engine for handling learning feedback and model improvement"""
    
    def __init__(self, vector_db: VectorDBManager, config: Dict[str, Any]):
        self.vector_db = vector_db
        self.config = config
        self.learning_dir = Path(config.get('learning_directory', project_root / 'data/learning'))
        self.learning_dir.mkdir(parents=True, exist_ok=True)
        self.logger = logging.getLogger(__name__)
        
        # Initialize learning statistics
        self.stats_file = self.learning_dir / 'learning_stats.json'
        self.stats = self._load_learning_stats()
    
    def _load_learning_stats(self) -> Dict[str, Any]:
        """Load learning statistics from file"""
        try:
            if self.stats_file.exists():
                with open(self.stats_file, 'r') as f:
                    return json.load(f)
            else:
                return {
                    'total_classifications': 0,
                    'correct_predictions': 0,
                    'accuracy': 0.0,
                    'corrections_count': 0,
                    'last_training_date': None,
                    'corrections_since_training': 0,
                    'classification_history': []
                }
        except Exception as e:
            self.logger.error(f"Failed to load learning stats: {e}")
            return {}
    
    def _save_learning_stats(self):
        """Save learning statistics to file"""
        try:
            with open(self.stats_file, 'w') as f:
                json.dump(self.stats, f, indent=2)
        except Exception as e:
            self.logger.error(f"Failed to save learning stats: {e}")
    
    def collect_classification_feedback(self, classification_result: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Collect feedback on classification result - IMPROVED VERSION"""
        try:
            file_path = classification_result.get('file_path', '')
            predicted = classification_result.get('classification', '')
            confidence = classification_result.get('confidence', 0.0)
            
            # FIRST: Ask if user wants to provide feedback
            print("\nWould you like to provide feedback on this classification? (y/n): ", end='')
            wants_feedback = input().strip().lower()
            
            if wants_feedback != 'y':
                # User doesn't want to provide feedback - just update stats as correct (assumed)
                self._update_classification_stats(predicted, predicted, confidence, correct=True)
                return None
            
            # User wants to provide feedback
            print("\nLearning & Feedback:")
            print("===================")
            print("Is this classification correct? (y/n): ", end='')
            is_correct = input().strip().lower()
            
            if is_correct == 'y':
                # Correct classification - update stats
                self._update_classification_stats(predicted, predicted, confidence, correct=True)
                print("Thank you for the feedback!")
                return None
            
            elif is_correct == 'n':
                # Incorrect classification - get correct answer
                print("\nWhat is the correct classification?")
                print("1. syndicated")
                print("2. pos")  
                print("3. promotion")
                print("Enter choice (1-3): ", end='')
                
                choice = input().strip()
                choice_map = {'1': 'syndicated', '2': 'pos', '3': 'promotion'}
                
                if choice not in choice_map:
                    print("Invalid choice. Feedback not recorded.")
                    return None
                
                correct_classification = choice_map[choice]
                print(f"\nThe correct classification is: {correct_classification}")
                
                # Ask about learning options
                print("\nWould you like to add this correction to the learning system? (y/n): ", end='')
                add_to_learning = input().strip().lower()
                
                if add_to_learning == 'y':
                    print("\nLearning options:")
                    print("1. Store as correction only (improve future classifications)")
                    print("2. Store as training example (add to knowledge base)")
                    print("3. Both correction and training example")
                    print("Enter choice (1-3): ", end='')
                    
                    learning_choice = input().strip()
                    learning_types = {'1': 'correction', '2': 'training_example', '3': 'both'}
                    
                    if learning_choice not in learning_types:
                        print("Invalid choice. Using correction only.")
                        learning_type = 'correction'
                    else:
                        learning_type = learning_types[learning_choice]
                    
                    # Get optional description
                    print("\nOptional: Add description for this learning example:")
                    print("Description: ", end='')
                    description = input().strip()
                    
                    # Store the learning feedback
                    feedback_data = {
                        'file_path': file_path,
                        'predicted': predicted,
                        'correct': correct_classification,
                        'confidence': confidence,
                        'learning_type': learning_type,
                        'description': description,
                        'file_features': classification_result.get('file_features', {})
                    }
                    
                    success = self.store_learning_feedback(feedback_data)
                    
                    if success:
                        print("\nLearning Example Stored Successfully!")
                        print("- Correction logged for future improvements")
                        if learning_type in ['training_example', 'both']:
                            print("- File added to knowledge base as training example")
                        print("- Learning database updated")
                        
                        # Show learning stats
                        print(f"\nClassification learning stats updated:")
                        print(f"- Total corrections: {self.stats['corrections_count']}")
                        accuracy_improvement = self._calculate_recent_accuracy_improvement()
                        if accuracy_improvement > 0:
                            print(f"- Accuracy improvement: +{accuracy_improvement:.1f}% over last 50 classifications")
                    else:
                        print("Failed to store learning example.")
                
                # Update classification stats
                self._update_classification_stats(predicted, correct_classification, confidence, correct=False)
                
                return feedback_data
            
            else:
                print("Please answer 'y' or 'n'. Feedback not recorded.")
                return None
            
        except Exception as e:
            self.logger.error(f"Failed to collect feedback: {e}")
            print(f"Error collecting feedback: {e}")
            return None
    
    def store_learning_feedback(self, feedback_data: Dict[str, Any]) -> bool:
        """Store learning feedback in the vector database"""
        try:
            # Create learning document
            learning_doc = LearningDocument(
                original_file_path=feedback_data['file_path'],
                predicted_classification=feedback_data['predicted'],
                correct_classification=feedback_data['correct'],
                confidence_score=feedback_data['confidence'],
                user_feedback=feedback_data.get('description', ''),
                learning_type=feedback_data['learning_type'],
                file_features=feedback_data.get('file_features', {})
            )
            
            # Add to vector database
            success = self.vector_db.add_documents('cpg_learning_feedback', [learning_doc])
            
            if success:
                # Update learning statistics
                self.stats['corrections_count'] += 1
                self.stats['corrections_since_training'] += 1
                self._save_learning_stats()
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to store learning feedback: {e}")
            return False
    
    def _update_classification_stats(self, predicted: str, actual: str, confidence: float, correct: bool):
        """Update classification statistics"""
        try:
            self.stats['total_classifications'] += 1
            if correct:
                self.stats['correct_predictions'] += 1
            
            # Update accuracy
            if self.stats['total_classifications'] > 0:
                self.stats['accuracy'] = self.stats['correct_predictions'] / self.stats['total_classifications']
            
            # Add to classification history (keep last 100)
            classification_entry = {
                'predicted': predicted,
                'actual': actual,
                'confidence': confidence,
                'correct': correct,
                'timestamp': datetime.now().isoformat()
            }
            
            if 'classification_history' not in self.stats:
                self.stats['classification_history'] = []
            
            self.stats['classification_history'].append(classification_entry)
            self.stats['classification_history'] = self.stats['classification_history'][-100:]  # Keep last 100
            
            self._save_learning_stats()
            
        except Exception as e:
            self.logger.error(f"Failed to update classification stats: {e}")
    
    def _calculate_recent_accuracy_improvement(self) -> float:
        """Calculate accuracy improvement over recent classifications"""
        try:
            history = self.stats.get('classification_history', [])
            if len(history) < 20:
                return 0.0
            
            # Compare last 25 vs previous 25
            recent_25 = history[-25:]
            previous_25 = history[-50:-25] if len(history) >= 50 else history[:-25]
            
            if not previous_25:
                return 0.0
            
            recent_accuracy = sum(1 for item in recent_25 if item['correct']) / len(recent_25)
            previous_accuracy = sum(1 for item in previous_25 if item['correct']) / len(previous_25)
            
            return (recent_accuracy - previous_accuracy) * 100
            
        except Exception as e:
            self.logger.error(f"Failed to calculate accuracy improvement: {e}")
            return 0.0
    
    def get_learning_statistics(self) -> Dict[str, Any]:
        """Get comprehensive learning statistics"""
        try:
            stats = self.stats.copy()
            
            # Add additional calculated metrics
            stats['accuracy_percentage'] = stats['accuracy'] * 100
            stats['recent_accuracy_improvement'] = self._calculate_recent_accuracy_improvement()
            
            # Get common misclassifications
            stats['common_misclassifications'] = self._get_common_misclassifications()
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Failed to get learning statistics: {e}")
            return {}
    
    def _get_common_misclassifications(self) -> List[Dict[str, Any]]:
        """Get common misclassification patterns"""
        try:
            history = self.stats.get('classification_history', [])
            incorrect_classifications = [item for item in history if not item['correct']]
            
            # Count misclassification patterns
            patterns = {}
            for item in incorrect_classifications:
                pattern = f"{item['predicted']} -> {item['actual']}"
                if pattern not in patterns:
                    patterns[pattern] = {'count': 0, 'avg_confidence': 0.0, 'confidences': []}
                patterns[pattern]['count'] += 1
                patterns[pattern]['confidences'].append(item['confidence'])
            
            # Calculate average confidence for each pattern
            for pattern in patterns:
                confidences = patterns[pattern]['confidences']
                patterns[pattern]['avg_confidence'] = sum(confidences) / len(confidences)
            
            # Sort by count and return top patterns
            sorted_patterns = sorted(patterns.items(), key=lambda x: x[1]['count'], reverse=True)
            
            result = []
            for pattern, data in sorted_patterns[:5]:  # Top 5 patterns
                result.append({
                    'pattern': pattern,
                    'count': data['count'],
                    'avg_confidence': data['avg_confidence']
                })
            
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to get common misclassifications: {e}")
            return []
    
    def show_learning_statistics(self):
        """Display learning statistics in CLI"""
        try:
            stats = self.get_learning_statistics()
            
            print("\nLearning & Training Statistics")
            print("=============================")
            print()
            print("Classification Performance:")
            print("---------------------------")
            print(f"Total Classifications: {stats.get('total_classifications', 0)}")
            print(f"Correct Predictions: {stats.get('correct_predictions', 0)}")
            print(f"Current Accuracy: {stats.get('accuracy_percentage', 0):.1f}%")
            
            improvement = stats.get('recent_accuracy_improvement', 0)
            if improvement > 0:
                print(f"Accuracy Trend: +{improvement:.1f}% (recent improvement)")
            elif improvement < 0:
                print(f"Accuracy Trend: {improvement:.1f}% (recent decline)")
            else:
                print("Accuracy Trend: Stable")
            
            print()
            print("Learning Data:")
            print("-------------")
            print(f"User Corrections: {stats.get('corrections_count', 0)}")
            print(f"Corrections Since Last Training: {stats.get('corrections_since_training', 0)}")
            
            # Show common misclassifications
            misclassifications = stats.get('common_misclassifications', [])
            if misclassifications:
                print()
                print("Common Misclassifications:")
                print("-------------------------")
                for i, item in enumerate(misclassifications, 1):
                    print(f"{i}. {item['pattern']} ({item['count']} occurrences, avg confidence: {item['avg_confidence']:.2f})")
            
            # Suggestions
            corrections_count = stats.get('corrections_since_training', 0)
            if corrections_count >= 10:
                print()
                print("Suggested Actions:")
                print("-----------------")
                print(f"Consider retraining model ({corrections_count} corrections accumulated)")
                
                if misclassifications:
                    top_pattern = misclassifications[0]
                    print(f"Focus on improving: {top_pattern['pattern']} classification")
            
        except Exception as e:
            self.logger.error(f"Failed to show learning statistics: {e}")
            print(f"Error displaying learning statistics: {e}")