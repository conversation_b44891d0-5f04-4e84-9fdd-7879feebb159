import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Optional
from abc import ABC, abstractmethod
import logging
import csv
import io

# Check for optional dependencies
OPENPYXL_AVAILABLE = True
try:
    import openpyxl
    from openpyxl import load_workbook
except ImportError:
    OPENPYXL_AVAILABLE = False

PDF_LIBRARIES_AVAILABLE = True
try:
    import PyPDF2
    import pdfplumber
except ImportError:
    PDF_LIBRARIES_AVAILABLE = False

class FileProcessor(ABC):
    """Abstract base class for file processors"""
    
    @abstractmethod
    def can_process(self, file_path: str) -> bool:
        """Check if this processor can handle the file"""
        pass
    
    @abstractmethod
    def extract_content(self, file_path: str) -> Dict[str, Any]:
        """Extract content from the file"""
        pass

class CSVProcessor(FileProcessor):
    """Processor for CSV files with robust error handling"""
    
    def can_process(self, file_path: str) -> bool:
        return file_path.lower().endswith(('.csv', '.tsv'))
    
    def extract_content(self, file_path: str) -> Dict[str, Any]:
        """Extract content from CSV file with enhanced error handling"""
        try:
            # Try different approaches to read the CSV
            df = None
            parsing_method = None
            
            # Method 1: Standard pandas read_csv with error handling
            df, parsing_method = self._try_standard_parsing(file_path)
            
            # Method 2: If standard parsing fails, try with flexible parameters
            if df is None:
                df, parsing_method = self._try_flexible_parsing(file_path)
            
            # Method 3: If still failing, try manual CSV parsing
            if df is None:
                df, parsing_method = self._try_manual_parsing(file_path)
            
            # Method 4: Last resort - read with maximum flexibility
            if df is None:
                df, parsing_method = self._try_fallback_parsing(file_path)
            
            if df is None:
                raise ValueError("Could not parse CSV file with any method")
            
            # Extract schema information
            columns = df.columns.tolist()
            data_types = [str(dtype) for dtype in df.dtypes]
            
            # Generate content summary
            content_parts = [
                f"CSV file with {len(columns)} columns and {len(df)} rows",
                f"Parsing method: {parsing_method}",
                f"Columns: {', '.join(columns[:10])}{'...' if len(columns) > 10 else ''}",
                f"Data types: {', '.join(set(data_types))}",
                f"Sample data patterns: {self._analyze_patterns(df)}"
            ]
            
            # Statistical summary
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            stats = {}
            if len(numeric_cols) > 0:
                stats = df[numeric_cols].describe().to_dict()
            
            return {
                "content": " | ".join(content_parts),
                "schema": {
                    "columns": columns,
                    "data_types": data_types,
                    "row_count": len(df),
                    "column_count": len(columns)
                },
                "statistical_summary": stats,
                "data_patterns": self._detect_data_patterns(df),
                "sample_data": df.head(3).to_dict('records') if len(df) > 0 else [],
                "parsing_info": {
                    "method_used": parsing_method,
                    "success": True
                }
            }
            
        except Exception as e:
            logging.error(f"Error processing CSV file {file_path}: {e}")
            return {
                "content": f"Error processing CSV file: {str(e)}",
                "schema": {},
                "error": str(e),
                "parsing_info": {
                    "method_used": "none",
                    "success": False
                }
            }
    
    def _try_standard_parsing(self, file_path: str) -> tuple[Optional[pd.DataFrame], Optional[str]]:
        """Try standard pandas parsing with different encodings"""
        encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
        
        for encoding in encodings:
            try:
                df = pd.read_csv(file_path, encoding=encoding, nrows=1000)
                return df, f"standard_parsing_encoding_{encoding}"
            except (UnicodeDecodeError, pd.errors.EmptyDataError):
                continue
            except Exception as e:
                logging.debug(f"Standard parsing failed with {encoding}: {e}")
                continue
        
        return None, None
    
    def _try_flexible_parsing(self, file_path: str) -> tuple[Optional[pd.DataFrame], Optional[str]]:
        """Try parsing with flexible parameters to handle malformed CSV"""
        encodings = ['utf-8', 'latin-1', 'cp1252']
        separators = [',', ';', '\t', '|']
        
        for encoding in encodings:
            for sep in separators:
                try:
                    # More flexible parsing parameters
                    df = pd.read_csv(
                        file_path,
                        encoding=encoding,
                        sep=sep,
                        nrows=1000,
                        on_bad_lines='skip',  # Skip bad lines instead of failing
                        engine='python',      # Python engine is more flexible
                        quoting=csv.QUOTE_MINIMAL,
                        skipinitialspace=True,
                        na_values=['', 'NULL', 'null', 'N/A', 'n/a', 'NA']
                    )
                    if not df.empty and len(df.columns) > 1:
                        return df, f"flexible_parsing_encoding_{encoding}_sep_{repr(sep)}"
                except Exception as e:
                    logging.debug(f"Flexible parsing failed with {encoding}, sep {sep}: {e}")
                    continue
        
        return None, None
    
    def _try_manual_parsing(self, file_path: str) -> tuple[Optional[pd.DataFrame], Optional[str]]:
        """Try manual CSV parsing to handle problematic files"""
        encodings = ['utf-8', 'latin-1', 'cp1252']
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding, errors='ignore') as file:
                    # Read first few lines to analyze structure
                    lines = []
                    for i, line in enumerate(file):
                        if i >= 1000:  # Limit to first 1000 lines
                            break
                        lines.append(line.strip())
                
                if not lines:
                    continue
                
                # Try to detect delimiter
                delimiter = self._detect_delimiter(lines[:10])
                
                # Parse manually with detected delimiter
                csv_data = []
                max_cols = 0
                
                for line_num, line in enumerate(lines):
                    if not line:
                        continue
                    
                    try:
                        # Use csv.reader for proper quote handling
                        reader = csv.reader([line], delimiter=delimiter, quotechar='"')
                        row = next(reader)
                        csv_data.append(row)
                        max_cols = max(max_cols, len(row))
                    except Exception as e:
                        logging.debug(f"Skipping malformed line {line_num + 1}: {e}")
                        continue
                
                if csv_data:
                    # Pad rows to have consistent column count
                    padded_data = []
                    for row in csv_data:
                        padded_row = row + [''] * (max_cols - len(row))
                        padded_data.append(padded_row[:max_cols])  # Truncate if too long
                    
                    # Create DataFrame
                    if len(padded_data) > 1:
                        df = pd.DataFrame(padded_data[1:], columns=padded_data[0])
                    else:
                        df = pd.DataFrame(padded_data)
                    
                    return df, f"manual_parsing_encoding_{encoding}_delimiter_{repr(delimiter)}"
                
            except Exception as e:
                logging.debug(f"Manual parsing failed with {encoding}: {e}")
                continue
        
        return None, None
    
    def _try_fallback_parsing(self, file_path: str) -> tuple[Optional[pd.DataFrame], Optional[str]]:
        """Fallback parsing method - very permissive"""
        encodings = ['utf-8', 'latin-1', 'cp1252']
        
        for encoding in encodings:
            try:
                # Read as text and try to create a basic structure
                with open(file_path, 'r', encoding=encoding, errors='ignore') as file:
                    content = file.read()
                
                # Split into lines and try different approaches
                lines = content.split('\n')[:1000]  # First 1000 lines
                non_empty_lines = [line.strip() for line in lines if line.strip()]
                
                if not non_empty_lines:
                    continue
                
                # Try comma-separated parsing with maximum flexibility
                csv_content = '\n'.join(non_empty_lines)
                
                try:
                    df = pd.read_csv(
                        io.StringIO(csv_content),
                        sep=None,  # Let pandas detect separator
                        engine='python',
                        on_bad_lines='skip',
                        header=0,
                        encoding=encoding,
                        skipinitialspace=True,
                        na_values=[''],
                        keep_default_na=False
                    )
                    
                    if not df.empty:
                        return df, f"fallback_parsing_encoding_{encoding}"
                
                except Exception:
                    # Last resort: create single column DataFrame
                    df = pd.DataFrame({'content': non_empty_lines})
                    return df, f"single_column_fallback_encoding_{encoding}"
                    
            except Exception as e:
                logging.debug(f"Fallback parsing failed with {encoding}: {e}")
                continue
        
        return None, None
    
    def _detect_delimiter(self, sample_lines: List[str]) -> str:
        """Detect the most likely delimiter"""
        delimiters = [',', ';', '\t', '|']
        delimiter_scores = {}
        
        for delimiter in delimiters:
            score = 0
            consistent_count = True
            col_counts = []
            
            for line in sample_lines:
                if line.strip():
                    count = len(line.split(delimiter))
                    col_counts.append(count)
                    score += count
            
            # Check consistency
            if col_counts and len(set(col_counts)) == 1:
                score *= 2  # Bonus for consistency
            
            delimiter_scores[delimiter] = score
        
        # Return delimiter with highest score
        return max(delimiter_scores, key=delimiter_scores.get)
    
    def _analyze_patterns(self, df: pd.DataFrame) -> str:
        """Analyze data patterns in the DataFrame"""
        patterns = []
        
        # Check for date columns
        date_cols = []
        for col in df.columns:
            if df[col].dtype == 'object':
                try:
                    import warnings
                    with warnings.catch_warnings():
                        warnings.simplefilter("ignore")
                        sample_data = df[col].dropna().head(10)
                        if len(sample_data) > 0:
                            parsed_dates = pd.to_datetime(sample_data, errors='coerce')
                            if parsed_dates.notna().sum() > len(sample_data) * 0.5:
                                date_cols.append(col)
                except:
                    pass
        
        if date_cols:
            patterns.append(f"temporal data in {len(date_cols)} columns")
        
        # Check for ID patterns
        id_cols = [col for col in df.columns if 'id' in col.lower() or 'code' in col.lower()]
        if id_cols:
            patterns.append(f"identifier columns: {', '.join(id_cols[:3])}")
        
        # Check for numeric patterns
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 0:
            patterns.append(f"{len(numeric_cols)} numeric columns")
        
        return ", ".join(patterns) if patterns else "mixed data types"
    
    def _detect_data_patterns(self, df: pd.DataFrame) -> List[str]:
        """Detect common CPG data patterns"""
        patterns = []
        
        # Check column naming patterns
        columns = [col.lower() for col in df.columns]
        
        # CPG-specific patterns
        if any('product' in col for col in columns):
            patterns.append("product_data")
        if any('sales' in col or 'revenue' in col for col in columns):
            patterns.append("sales_data")
        if any('store' in col for col in columns):
            patterns.append("store_data")
        if any('market' in col for col in columns):
            patterns.append("market_data")
        if any('promo' in col or 'campaign' in col for col in columns):
            patterns.append("promotion_data")
        
        # Syndicated data patterns
        if any(col in ['tdp', 'acv', 'velocity'] for col in columns):
            patterns.append("syndicated_metrics")
        
        # POS patterns
        if any(col in ['upc', 'sku', 'transaction'] for col in columns):
            patterns.append("pos_transaction")
        
        return patterns
    
class ExcelProcessor(FileProcessor):
    """Processor for Excel files"""
    
    def can_process(self, file_path: str) -> bool:
        return OPENPYXL_AVAILABLE and file_path.lower().endswith(('.xlsx', '.xls'))
    
    def extract_content(self, file_path: str) -> Dict[str, Any]:
        """Extract content from Excel file"""
        if not OPENPYXL_AVAILABLE:
            return {
                "content": "Excel processing not available - openpyxl not installed",
                "error": "Missing openpyxl dependency"
            }
        
        try:
            # Load workbook to get sheet information
            workbook = load_workbook(file_path, read_only=True)
            sheet_names = workbook.sheetnames
            
            content_parts = [f"Excel file with {len(sheet_names)} sheets: {', '.join(sheet_names)}"]
            all_schemas = {}
            all_patterns = []
            
            # Process each sheet (limit to first 5 sheets)
            for sheet_name in sheet_names[:5]:
                try:
                    df = pd.read_excel(file_path, sheet_name=sheet_name, nrows=1000)
                    
                    if not df.empty:
                        columns = df.columns.tolist()
                        data_types = [str(dtype) for dtype in df.dtypes]
                        
                        all_schemas[sheet_name] = {
                            "columns": columns,
                            "data_types": data_types,
                            "row_count": len(df),
                            "column_count": len(columns)
                        }
                        
                        # Detect patterns for this sheet using CSV processor logic
                        csv_processor = CSVProcessor()
                        sheet_patterns = csv_processor._detect_data_patterns(df)
                        all_patterns.extend(sheet_patterns)
                        
                        content_parts.append(
                            f"Sheet '{sheet_name}': {len(columns)} columns, {len(df)} rows"
                        )
                
                except Exception as e:
                    logging.warning(f"Could not process sheet {sheet_name}: {e}")
                    content_parts.append(f"Sheet '{sheet_name}': processing error")
            
            return {
                "content": " | ".join(content_parts),
                "schema": all_schemas,
                "data_patterns": list(set(all_patterns)),
                "sheet_count": len(sheet_names),
                "sheet_names": sheet_names
            }
            
        except Exception as e:
            logging.error(f"Error processing Excel file {file_path}: {e}")
            return {
                "content": f"Error processing Excel file: {str(e)}",
                "schema": {},
                "error": str(e)
            }

class PDFProcessor(FileProcessor):
    """Processor for PDF files"""
    
    def can_process(self, file_path: str) -> bool:
        return PDF_LIBRARIES_AVAILABLE and file_path.lower().endswith('.pdf')
    
    def extract_content(self, file_path: str) -> Dict[str, Any]:
        """Extract content from PDF file"""
        if not PDF_LIBRARIES_AVAILABLE:
            return {
                "content": "PDF processing not available - PDF libraries not installed",
                "error": "Missing PyPDF2 and pdfplumber dependencies"
            }
        
        try:
            content_text = ""
            tables_found = 0
            
            try:
                with pdfplumber.open(file_path) as pdf:
                    pages_processed = min(len(pdf.pages), 10)
                    
                    for i, page in enumerate(pdf.pages[:pages_processed]):
                        page_text = page.extract_text()
                        if page_text:
                            content_text += f"Page {i+1}: {page_text[:500]}...\n"
                        
                        tables = page.extract_tables()
                        if tables:
                            tables_found += len(tables)
                            content_text += f"[{len(tables)} table(s) found on page {i+1}]\n"
            
            except Exception as e:
                logging.warning(f"pdfplumber failed, trying PyPDF2: {e}")
                
                with open(file_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    pages_processed = min(len(pdf_reader.pages), 10)
                    
                    for i, page in enumerate(pdf_reader.pages[:pages_processed]):
                        page_text = page.extract_text()
                        if page_text:
                            content_text += f"Page {i+1}: {page_text[:500]}...\n"
            
            patterns = self._detect_pdf_patterns(content_text)
            
            content_summary = f"PDF document with {pages_processed} pages processed"
            if tables_found > 0:
                content_summary += f", {tables_found} tables found"
            
            return {
                "content": f"{content_summary} | Content preview: {content_text[:1000]}",
                "full_text": content_text,
                "data_patterns": patterns,
                "pages_processed": pages_processed,
                "tables_found": tables_found,
                "document_type": "pdf"
            }
            
        except Exception as e:
            logging.error(f"Error processing PDF file {file_path}: {e}")
            return {
                "content": f"Error processing PDF file: {str(e)}",
                "error": str(e)
            }
    
    def _detect_pdf_patterns(self, text: str) -> List[str]:
        """Detect patterns in PDF content"""
        patterns = []
        text_lower = text.lower()
        
        # Document type patterns
        if any(keyword in text_lower for keyword in ['report', 'analysis', 'summary']):
            patterns.append("business_report")
        if any(keyword in text_lower for keyword in ['manual', 'guide', 'documentation']):
            patterns.append("documentation")
        if any(keyword in text_lower for keyword in ['presentation', 'slide']):
            patterns.append("presentation")
        
        # CPG-specific patterns
        if any(keyword in text_lower for keyword in ['nielsen', 'iri', 'syndicated']):
            patterns.append("syndicated_report")
        if any(keyword in text_lower for keyword in ['pos', 'point of sale', 'transaction']):
            patterns.append("pos_documentation")
        if any(keyword in text_lower for keyword in ['promotion', 'campaign', 'trade']):
            patterns.append("promotion_material")
        
        return patterns

class TextProcessor(FileProcessor):
    """Processor for text files"""
    
    def can_process(self, file_path: str) -> bool:
        return file_path.lower().endswith(('.txt', '.log', '.md'))
    
    def extract_content(self, file_path: str) -> Dict[str, Any]:
        """Extract content from text file"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            if len(content) > 5000:
                content_preview = content[:5000] + "..."
            else:
                content_preview = content
            
            patterns = self._detect_text_patterns(content)
            
            return {
                "content": f"Text file content: {content_preview}",
                "full_text": content,
                "data_patterns": patterns,
                "character_count": len(content),
                "line_count": len(content.split('\n'))
            }
            
        except Exception as e:
            logging.error(f"Error processing text file {file_path}: {e}")
            return {
                "content": f"Error processing text file: {str(e)}",
                "error": str(e)
            }
    
    def _detect_text_patterns(self, text: str) -> List[str]:
        """Detect patterns in text content"""
        patterns = []
        text_lower = text.lower()
        
        # CPG-specific keywords
        if any(keyword in text_lower for keyword in ['nielsen', 'iri', 'syndicated']):
            patterns.append("syndicated_reference")
        if any(keyword in text_lower for keyword in ['pos', 'point of sale', 'transaction']):
            patterns.append("pos_reference")
        if any(keyword in text_lower for keyword in ['promotion', 'campaign', 'trade']):
            patterns.append("promotion_reference")
        
        # Document types
        if any(keyword in text_lower for keyword in ['guide', 'manual', 'documentation']):
            patterns.append("documentation")
        if any(keyword in text_lower for keyword in ['classification', 'taxonomy', 'category']):
            patterns.append("classification_guide")
        
        return patterns

# File processor factory
class FileProcessorFactory:
    """Factory for creating appropriate file processors"""
    
    def __init__(self):
        self.processors = [
            CSVProcessor(),
            ExcelProcessor(),
            PDFProcessor(),
            TextProcessor()
        ]
    
    def get_processor(self, file_path: str) -> Optional[FileProcessor]:
        """Get appropriate processor for file"""
        for processor in self.processors:
            if processor.can_process(file_path):
                return processor
        return None
    
    def get_supported_extensions(self) -> List[str]:
        """Get list of supported file extensions"""
        extensions = ['.csv', '.tsv', '.txt', '.log', '.md']
        if OPENPYXL_AVAILABLE:
            extensions.extend(['.xlsx', '.xls'])
        if PDF_LIBRARIES_AVAILABLE:
            extensions.extend(['.pdf'])
        return extensions
