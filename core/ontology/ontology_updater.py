"""
Ontology Updater for Neo4j Knowledge Graph
Updates Neo4j with table and column nodes including dual vector embeddings
"""

import json
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Optional
import numpy as np

# Neo4j imports
from neo4j import GraphDatabase

import sys
from pathlib import Path


project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Project imports
from utils.logger import Logger


class OntologyUpdater:
    """Updater for Neo4j ontology with dual vector embeddings"""
    
    def __init__(self, config_manager):
        """Initialize the Ontology Updater with Neo4j connection"""
        self.config_manager = config_manager
        self.logger = Logger.get_logger("ontology_updater")
        
        # Get Neo4j configuration
        self.neo4j_config = config_manager.get_neo4j_config()
        
        # Initialize Neo4j driver
        self._init_neo4j_driver()
        
        self.logger.info("Ontology Updater initialized with Neo4j connection")
    
    def _init_neo4j_driver(self):
        """Initialize Neo4j driver with connection"""
        try:
            self.driver = GraphDatabase.driver(
                self.neo4j_config.uri,
                auth=(self.neo4j_config.username, self.neo4j_config.password)
            )
            
            # Test connection
            with self.driver.session(database=self.neo4j_config.database) as session:
                result = session.run("RETURN 1 as test")
                test_value = result.single()["test"]
                if test_value == 1:
                    self.logger.info(f"Successfully connected to Neo4j database: {self.neo4j_config.database}")
                
        except Exception as e:
            self.logger.error(f"Failed to initialize Neo4j driver: {str(e)}")
            raise
    
    def close(self):
        """Close Neo4j driver connection"""
        if hasattr(self, 'driver'):
            self.driver.close()
            self.logger.info("Neo4j driver connection closed")
    
    def update_table_ontology(self, table_name: str, enriched_schema: pd.DataFrame):
        """Main method to update/create table and column nodes with dual embeddings"""
        try:
            self.logger.info(f"Updating ontology for table: {table_name}")
            
            with self.driver.session(database=self.neo4j_config.database) as session:
                # Start transaction
                with session.begin_transaction() as tx:
                    # Step 1: Delete existing table structure if it exists
                    self._delete_existing_table_structure(tx, table_name)
                    
                    # Step 2: Create table node
                    self._create_table_node(tx, table_name, len(enriched_schema))
                    
                    # Step 3: Create column nodes with dual embeddings
                    self._create_column_nodes_with_dual_embeddings(tx, table_name, enriched_schema)
                    
                    # Commit transaction
                    tx.commit()
            
            self.logger.info(f"Successfully updated ontology for table: {table_name} with {len(enriched_schema)} columns")
            
        except Exception as e:
            self.logger.error(f"Failed to update table ontology: {str(e)}")
            raise
    
    def _delete_existing_table_structure(self, tx, table_name: str):
        """Remove existing table and its columns without affecting other data"""
        try:
            # Delete all columns associated with the table
            delete_columns_query = """
            MATCH (t:Table {name: $table_name})-[:HAS_COLUMN]->(c:Column)
            DETACH DELETE c
            """
            tx.run(delete_columns_query, table_name=table_name)
            
            # Delete the table node itself
            delete_table_query = """
            MATCH (t:Table {name: $table_name})
            DETACH DELETE t
            """
            tx.run(delete_table_query, table_name=table_name)
            
            self.logger.debug(f"Deleted existing structure for table: {table_name}")
            
        except Exception as e:
            self.logger.error(f"Failed to delete existing table structure: {str(e)}")
            raise
    
    def _create_table_node(self, tx, table_name: str, total_columns: int):
        """Create or update table node"""
        try:
            current_time = datetime.now().isoformat()
            
            create_table_query = """
            CREATE (t:Table {
                name: $table_name,
                total_columns: $total_columns,
                created_at: $created_at,
                updated_at: $updated_at
            })
            RETURN t
            """
            
            result = tx.run(create_table_query,
                          table_name=table_name,
                          total_columns=total_columns,
                          created_at=current_time,
                          updated_at=current_time)
            
            self.logger.debug(f"Created table node: {table_name}")
            
        except Exception as e:
            self.logger.error(f"Failed to create table node: {str(e)}")
            raise
    
    def _create_column_nodes_with_dual_embeddings(self, tx, table_name: str, schema_data: pd.DataFrame):
        """Create column nodes with both embedding properties"""
        try:
            current_time = datetime.now().isoformat()
            
            for idx, row in schema_data.iterrows():
                # Prepare column data
                column_data = self._prepare_column_data(row, current_time)
                
                # Create column node
                create_column_query = """
                MATCH (t:Table {name: $table_name})
                CREATE (c:Column {
                    name: $name,
                    description: $description,
                    category: $category,
                    table_type: $table_type,
                    synonyms: $synonyms,
                    applicable_domains: $applicable_domains,
                    azure_openai_embedding: $azure_openai_embedding,
                    sentence_transformer_embedding: $sentence_transformer_embedding,
                    created_at: $created_at,
                    updated_at: $updated_at
                })
                CREATE (t)-[:HAS_COLUMN]->(c)
                RETURN c
                """
                
                tx.run(create_column_query,
                      table_name=table_name,
                      **column_data)
            
            self.logger.debug(f"Created {len(schema_data)} column nodes for table: {table_name}")
            
        except Exception as e:
            self.logger.error(f"Failed to create column nodes: {str(e)}")
            raise
    
    def _prepare_column_data(self, row: pd.Series, current_time: str) -> Dict[str, Any]:
        """Prepare column data for Neo4j insertion"""
        try:
            # Handle synonyms
            synonyms = row.get('synonyms', [])
            if isinstance(synonyms, list):
                synonyms_json = json.dumps(synonyms)
            else:
                synonyms_json = str(synonyms) if synonyms else "[]"
            
            # Handle applicable domains
            domains = row.get('applicable_domain', [])
            if isinstance(domains, list):
                domains_json = json.dumps(domains)
            else:
                domains_json = str(domains) if domains else "[]"
            
            # Handle embeddings
            azure_openai_embedding = row.get('azure_openai_embedding', [])
            if not isinstance(azure_openai_embedding, list):
                azure_openai_embedding = []
            
            st_embedding = row.get('sentence_transformer_embedding', [])
            if not isinstance(st_embedding, list):
                st_embedding = []
            
            column_data = {
                'name': str(row['column_name']),
                'description': str(row.get('description', '')),
                'category': str(row.get('column_category', '')),
                'table_type': str(row.get('table_type', '')),
                'synonyms': synonyms_json,
                'applicable_domains': domains_json,
                'azure_openai_embedding': azure_openai_embedding,
                'sentence_transformer_embedding': st_embedding,
                'created_at': current_time,
                'updated_at': current_time
            }
            
            return column_data
            
        except Exception as e:
            self.logger.error(f"Failed to prepare column data: {str(e)}")
            raise
    
    def get_table_details(self, table_name: str) -> Dict[str, Any]:
        """Fetch complete table structure from Neo4j"""
        try:
            with self.driver.session(database=self.neo4j_config.database) as session:
                query = """
                MATCH (t:Table {name: $table_name})-[:HAS_COLUMN]->(c:Column)
                RETURN t, collect(c) as columns
                """
                
                result = session.run(query, table_name=table_name)
                record = result.single()
                
                if not record:
                    raise ValueError(f"Table '{table_name}' not found in the database")
                
                table_node = dict(record['t'])
                column_nodes = [dict(col) for col in record['columns']]
                
                # Parse JSON strings in columns
                for col in column_nodes:
                    if 'synonyms' in col:
                        try:
                            col['synonyms'] = json.loads(col['synonyms'])
                        except:
                            col['synonyms'] = []
                    
                    if 'applicable_domains' in col:
                        try:
                            col['applicable_domains'] = json.loads(col['applicable_domains'])
                        except:
                            col['applicable_domains'] = []
                
                return {
                    'table': table_node,
                    'columns': column_nodes,
                    'created_at': table_node.get('created_at'),
                    'updated_at': table_node.get('updated_at'),
                    'total_columns': len(column_nodes)
                }
                
        except Exception as e:
            self.logger.error(f"Failed to get table details: {str(e)}")
            raise
    
    def get_all_tables(self) -> List[Dict[str, Any]]:
        """Fetch all tables in the database"""
        try:
            with self.driver.session(database=self.neo4j_config.database) as session:
                query = """
                MATCH (t:Table)
                OPTIONAL MATCH (t)-[:HAS_COLUMN]->(c:Column)
                RETURN t.name as table_name, 
                       t.created_at as created_at,
                       t.updated_at as updated_at,
                       count(c) as column_count
                ORDER BY t.name
                """
                
                result = session.run(query)
                tables = []
                
                for record in result:
                    tables.append({
                        'table_name': record['table_name'],
                        'created_at': record['created_at'],
                        'updated_at': record['updated_at'],
                        'column_count': record['column_count']
                    })
                
                return tables
                
        except Exception as e:
            self.logger.error(f"Failed to get all tables: {str(e)}")
            raise
    
    def query_columns_by_similarity(self, query_text: str, embedding_type: str = "both", 
                                   similarity_threshold: float = 0.8, limit: int = 10) -> List[Dict[str, Any]]:
        """Search columns using vector similarity (placeholder for future implementation)"""
        try:
            # Note: This is a placeholder for vector similarity search
            # In a real implementation, you would:
            # 1. Generate embedding for query_text
            # 2. Use Neo4j vector index or GDS library for similarity search
            # 3. Return ranked results
            
            self.logger.warning("Vector similarity search not implemented yet. Returning columns by text match.")
            
            with self.driver.session(database=self.neo4j_config.database) as session:
                # Simple text-based search as placeholder
                query = """
                MATCH (c:Column)
                WHERE toLower(c.name) CONTAINS toLower($query_text) 
                   OR toLower(c.description) CONTAINS toLower($query_text)
                RETURN c.name as name, 
                       c.description as description,
                       c.synonyms as synonyms,
                       c.applicable_domains as applicable_domains
                LIMIT $limit
                """
                
                result = session.run(query, query_text=query_text, limit=limit)
                columns = []
                
                for record in result:
                    try:
                        synonyms = json.loads(record['synonyms']) if record['synonyms'] else []
                        domains = json.loads(record['applicable_domains']) if record['applicable_domains'] else []
                    except:
                        synonyms = []
                        domains = []
                    
                    columns.append({
                        'name': record['name'],
                        'description': record['description'],
                        'synonyms': synonyms,
                        'applicable_domains': domains,
                        'similarity': 1.0  # Placeholder similarity score
                    })
                
                return columns
                
        except Exception as e:
            self.logger.error(f"Failed to query columns by similarity: {str(e)}")
            raise
    
    def create_indexes(self):
        """Create indexes for better query performance"""
        try:
            with self.driver.session(database=self.neo4j_config.database) as session:
                # Create index on table name
                session.run("CREATE INDEX table_name_index IF NOT EXISTS FOR (t:Table) ON (t.name)")
                
                # Create index on column name
                session.run("CREATE INDEX column_name_index IF NOT EXISTS FOR (c:Column) ON (c.name)")
                
                # Create index on column category
                session.run("CREATE INDEX column_category_index IF NOT EXISTS FOR (c:Column) ON (c.category)")
                
                self.logger.info("Created Neo4j indexes for better performance")
                
        except Exception as e:
            self.logger.warning(f"Failed to create indexes: {str(e)}")
    
    def get_database_stats(self) -> Dict[str, Any]:
        """Get statistics about the ontology database"""
        try:
            with self.driver.session(database=self.neo4j_config.database) as session:
                stats_query = """
                MATCH (t:Table)
                OPTIONAL MATCH (t)-[:HAS_COLUMN]->(c:Column)
                WITH t, COUNT(c) as column_count
                RETURN count(DISTINCT t) as total_tables,
                       sum(column_count) as total_columns,
                       avg(column_count) as avg_columns_per_table
                """
                
                result = session.run(stats_query)
                record = result.single()
                
                if record:
                    return {
                        'total_tables': record['total_tables'],
                        'total_columns': record['total_columns'] or 0,
                        'avg_columns_per_table': round(record['avg_columns_per_table'] or 0, 2)
                    }
                else:
                    return {'total_tables': 0, 'total_columns': 0, 'avg_columns_per_table': 0}
                
        except Exception as e:
            self.logger.error(f"Failed to get database stats: {str(e)}")
            return {}
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.close()