"""
Vector Generator for Schema Columns
Generates dual embeddings (Azure OpenAI + Sentence Transformers) for database columns
"""

import json
import numpy as np
import pandas as pd
from typing import List, Dict, Union, Any
from tqdm import tqdm
import time

# Azure OpenAI imports
from openai import AzureOpenAI

# Sentence Transformers imports
from sentence_transformers import SentenceTransformer

import sys
from pathlib import Path


project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Project imports
from utils.logger import Logger


class VectorGenerator:
    """Generator for dual vector embeddings using Azure OpenAI and Sentence Transformers"""
    
    def __init__(self, config_manager):
        """Initialize the Vector Generator with dual embedding models"""
        self.config_manager = config_manager
        self.logger = Logger.get_logger("vector_generator")
        
        # Get embedding configurations
        self.azure_openai_config = config_manager.get_azure_openai_config()
        self.st_config = config_manager.get_sentence_transformer_config()
        
        # Initialize Azure OpenAI client
        self._init_azure_openai_client()
        
        # Initialize Sentence Transformer model
        self._init_sentence_transformer()
        
        self.logger.info("Vector Generator initialized with Azure OpenAI and Sentence Transformer models")
    
    def _init_azure_openai_client(self):
        """Initialize Azure OpenAI client"""
        try:
            self.azure_openai_client = AzureOpenAI(
                api_key=self.azure_openai_config.api_key,
                api_version=self.azure_openai_config.api_version,
                azure_endpoint=self.azure_openai_config.endpoint
            )
            self.logger.info(f"Azure OpenAI client initialized with deployment: {self.azure_openai_config.deployment}")
        except Exception as e:
            self.logger.error(f"Failed to initialize Azure OpenAI client: {str(e)}")
            raise
    
    def _init_sentence_transformer(self):
        """Initialize Sentence Transformer model"""
        try:
            self.st_model = SentenceTransformer(
                self.st_config.model,
                device=self.st_config.device
            )
            self.logger.info(f"Sentence Transformer initialized: {self.st_config.model} on {self.st_config.device}")
        except Exception as e:
            self.logger.error(f"Failed to initialize Sentence Transformer: {str(e)}")
            raise
    
    def generate_azure_openai_embedding(self, text: str) -> List[float]:
        """Generate Azure OpenAI embedding for given text"""
        try:
            response = self.azure_openai_client.embeddings.create(
                model=self.azure_openai_config.deployment,
                input=text
            )
            embedding = response.data[0].embedding
            
            # Use dimensions from max_tokens if available, otherwise default
            expected_dimensions = self.azure_openai_config.max_tokens or 1536
            
            # Validate embedding dimensions
            if len(embedding) != expected_dimensions:
                self.logger.warning(f"Expected {expected_dimensions} dimensions, got {len(embedding)}")
            
            return embedding
            
        except Exception as e:
            self.logger.error(f"Failed to generate Azure OpenAI embedding: {str(e)}")
            return [0.0] * (self.azure_openai_config.max_tokens or 1536)  # Return zero embedding as fallback
    
    def generate_sentence_transformer_embedding(self, text: str) -> List[float]:
        """Generate Sentence Transformer embedding for given text"""
        try:
            embedding = self.st_model.encode(text, convert_to_numpy=True)
            
            # Validate embedding dimensions
            if len(embedding) != self.st_config.dimensions:
                raise ValueError(f"Expected {self.st_config.dimensions} dimensions, got {len(embedding)}")
            
            return embedding.tolist()
            
        except Exception as e:
            self.logger.error(f"Failed to generate Sentence Transformer embedding: {str(e)}")
            return [0.0] * self.st_config.dimensions  # Return zero embedding as fallback
    
    def _combine_text_for_embedding(self, column_name: str, description: str, 
                                   synonyms: list, applicable_domains: list) -> str:
        """Combine column information into optimized text for embedding"""
        # Handle None values
        description = description or "No description provided"
        synonyms = synonyms or []
        applicable_domains = applicable_domains or []
        
        # Parse JSON strings if needed
        if isinstance(synonyms, str):
            try:
                synonyms = json.loads(synonyms)
            except:
                synonyms = []
        
        if isinstance(applicable_domains, str):
            try:
                applicable_domains = json.loads(applicable_domains)
            except:
                applicable_domains = []
        
        # Create comprehensive text representation
        text = f"Database column '{column_name}': {description}. "
        
        if synonyms:
            text += f"Alternative names: {', '.join(synonyms)}. "
        
        if applicable_domains:
            text += f"Used in: {', '.join(applicable_domains)} industries."
        
        return text
    
    def generate_dual_column_embeddings(self, column_name: str, description: str, 
                                       synonyms: list, applicable_domains: list) -> Dict[str, List[float]]:
        """Generate both Azure OpenAI and Sentence Transformer embeddings for a column"""
        try:
            # Combine text for embedding
            combined_text = self._combine_text_for_embedding(
                column_name, description, synonyms, applicable_domains
            )
            
            # Generate both embeddings
            azure_openai_embedding = self.generate_azure_openai_embedding(combined_text)
            st_embedding = self.generate_sentence_transformer_embedding(combined_text)
            
            self.logger.debug(f"Generated dual embeddings for column: {column_name}")
            
            return {
                "azure_openai_embedding": azure_openai_embedding,
                "sentence_transformer_embedding": st_embedding
            }
            
        except Exception as e:
            self.logger.error(f"Failed to generate dual embeddings for {column_name}: {str(e)}")
            return {
                "azure_openai_embedding": [0.0] * (self.azure_openai_config.max_tokens or 1536),
                "sentence_transformer_embedding": [0.0] * self.st_config.dimensions
            }
    
    def batch_generate_dual_embeddings(self, schema_data: pd.DataFrame) -> pd.DataFrame:
        """Process entire schema dataframe and add dual embedding columns"""
        try:
            # Create a copy to avoid modifying original
            enriched_df = schema_data.copy()
            
            # Initialize embedding columns
            # enriched_df['azure_openai_embedding'] = [[] for _ in range(len(enriched_df))]
            enriched_df['sentence_transformer_embedding'] = [[] for _ in range(len(enriched_df))]
            
            # Process Azure OpenAI embeddings in batches
            # self._batch_generate_azure_openai_embeddings(enriched_df)
            
            # Process Sentence Transformer embeddings in batches
            self._batch_generate_sentence_transformer_embeddings(enriched_df)
            
            self.logger.info(f"Generated dual embeddings for {len(enriched_df)} columns")
            return enriched_df
            
        except Exception as e:
            self.logger.error(f"Failed to batch generate dual embeddings: {str(e)}")
            raise
    
    def _batch_generate_azure_openai_embeddings(self, df: pd.DataFrame):
        """Generate Azure OpenAI embeddings in batches with rate limiting"""
        print("Generating Azure OpenAI embeddings...")
        
        batch_size = self.azure_openai_config.batch_size or 5
        total_rows = len(df)
        
        for start_idx in tqdm(range(0, total_rows, batch_size), desc="Azure OpenAI embeddings"):
            end_idx = min(start_idx + batch_size, total_rows)
            batch_df = df.iloc[start_idx:end_idx]
            
            # Prepare batch texts
            batch_texts = []
            for _, row in batch_df.iterrows():
                text = self._combine_text_for_embedding(
                    row['column_name'],
                    row['description'],
                    row.get('synonyms', []),
                    row.get('applicable_domain', [])
                )
                batch_texts.append(text)
            
            # Generate embeddings for batch
            try:
                response = self.azure_openai_client.embeddings.create(
                    model=self.azure_openai_config.deployment,
                    input=batch_texts
                )
                
                # Store embeddings
                for i, embedding_data in enumerate(response.data):
                    df_idx = start_idx + i
                    df.at[df_idx, 'azure_openai_embedding'] = embedding_data.embedding
                
                # Rate limiting - small delay between batches
                time.sleep(0.5)
                
            except Exception as e:
                self.logger.warning(f"Failed to generate Azure OpenAI embeddings for batch {start_idx}-{end_idx}: {str(e)}")
                # Fill with zero embeddings
                for i in range(len(batch_df)):
                    df_idx = start_idx + i
                    df.at[df_idx, 'azure_openai_embedding'] = [0.0] * (self.azure_openai_config.max_tokens or 1536)
    
    def _batch_generate_sentence_transformer_embeddings(self, df: pd.DataFrame):
        """Generate Sentence Transformer embeddings in batches"""
        print("Generating Sentence Transformer embeddings...")
        
        batch_size = self.st_config.batch_size
        total_rows = len(df)
        
        for start_idx in tqdm(range(0, total_rows, batch_size), desc="ST embeddings"):
            end_idx = min(start_idx + batch_size, total_rows)
            batch_df = df.iloc[start_idx:end_idx]
            
            # Prepare batch texts
            batch_texts = []
            for _, row in batch_df.iterrows():
                text = self._combine_text_for_embedding(
                    row['column_name'],
                    row['description'],
                    row.get('synonyms', []),
                    row.get('applicable_domain', [])
                )
                batch_texts.append(text)
            
            # Generate embeddings for batch
            try:
                embeddings = self.st_model.encode(
                    batch_texts,
                    convert_to_numpy=True,
                    show_progress_bar=False
                )
                
                # Store embeddings
                for i, embedding in enumerate(embeddings):
                    df_idx = start_idx + i
                    df.at[df_idx, 'sentence_transformer_embedding'] = embedding.tolist()
                
            except Exception as e:
                self.logger.warning(f"Failed to generate ST embeddings for batch {start_idx}-{end_idx}: {str(e)}")
                # Fill with zero embeddings
                for i in range(len(batch_df)):
                    df_idx = start_idx + i
                    df.at[df_idx, 'sentence_transformer_embedding'] = [0.0] * self.st_config.dimensions
    
    def get_embedding_summary(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Get summary statistics of embedding generation"""
        try:
            total_rows = len(df)
            
            # Get expected dimensions
            azure_openai_dimensions = self.azure_openai_config.max_tokens or 1536
            
            # Count successful embeddings
            azure_openai_success = sum(1 for _, row in df.iterrows() 
                                     if isinstance(row.get('azure_openai_embedding'), list) 
                                     and len(row['azure_openai_embedding']) == azure_openai_dimensions
                                     and sum(row['azure_openai_embedding']) != 0.0)
            
            st_success = sum(1 for _, row in df.iterrows() 
                           if isinstance(row.get('sentence_transformer_embedding'), list) 
                           and len(row['sentence_transformer_embedding']) == self.st_config.dimensions
                           and sum(row['sentence_transformer_embedding']) != 0.0)
            
            summary = {
                'total_rows': total_rows,
                # 'azure_openai_embeddings_generated': azure_openai_success,
                'st_embeddings_generated': st_success,
                'azure_openai_success_rate': round(azure_openai_success / total_rows * 100, 2) if total_rows > 0 else 0,
                'st_success_rate': round(st_success / total_rows * 100, 2) if total_rows > 0 else 0,
                # 'azure_openai_dimensions': azure_openai_dimensions,
                'st_dimensions': self.st_config.dimensions,
                'azure_openai_deployment': self.azure_openai_config.deployment,
                'st_model': self.st_config.model
            }
            
            return summary
            
        except Exception as e:
            self.logger.error(f"Failed to generate embedding summary: {str(e)}")
            return {}