"""
Ontology Usage - End-to-End Workflow Orchestrator
Coordinates schema enrichment, vector generation, and ontology updates
"""

import json
import pandas as pd
from typing import Dict, List, Any

import sys
from pathlib import Path


project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Project imports
from agents.schema_enricher_agent import SchemaEnricherAgent
from core.ontology.vector_generator import VectorGenerator
from core.ontology.ontology_updater import OntologyUpdater
from utils.logger import Logger


class OntologyUsage:
    """Orchestrator for complete schema-to-ontology workflow"""
    
    def __init__(self, config_manager):
        """Initialize with all required components"""
        self.config_manager = config_manager
        self.logger = Logger.get_logger("ontology_usage")
        
        # Initialize components
        self.schema_enricher = SchemaEnricherAgent(config_manager)
        self.vector_generator = VectorGenerator(config_manager)
        self.ontology_updater = OntologyUpdater(config_manager)
        
        self.logger.info("Ontology Usage orchestrator initialized")
    
    def process_schema_file(self, schema_file_path: str) -> str:
        """Complete end-to-end workflow from schema file to Neo4j ontology"""
        try:
            self.logger.info(f"Starting complete workflow for: {schema_file_path}")
            
            # Step 1: Extract table name from filename
            table_name = self._extract_table_name_from_file(schema_file_path)
            print(f"Extracted table name: {table_name}")
            
            # Step 2: Schema Enrichment
            print("Step 1: Enriching schema with synonyms and domains...")
            enriched_df = self.schema_enricher.enrich_schema(schema_file_path)
            print(f"Enriched {len(enriched_df)} columns with synonyms and domains")
            
            # Step 3: Vector Generation
            print("Step 2: Generating dual vector embeddings...")
            enriched_with_vectors = self.vector_generator.batch_generate_dual_embeddings(enriched_df)
            
            # Get embedding summary
            embedding_summary = self.vector_generator.get_embedding_summary(enriched_with_vectors)
            print(f"Generated embeddings - Azure OpenAI: {embedding_summary.get('azure_openai_success_rate', 0)}% success, ST: {embedding_summary.get('st_success_rate', 0)}% success")
            
            # Step 4: Ontology Update
            print("Step 3: Updating Neo4j knowledge graph...")
            self.ontology_updater.update_table_ontology(table_name, enriched_with_vectors)
            print(f"Updated Neo4j ontology for table: {table_name}")
            
            # Step 5: Create indexes for performance
            try:
                self.ontology_updater.create_indexes()
                print("Created/verified Neo4j indexes")
            except Exception as e:
                print(f"Index creation warning: {str(e)}")
            
            print("Complete workflow finished successfully!")
            return table_name
            
        except Exception as e:
            self.logger.error(f"Workflow failed: {str(e)}")
            print(f"❌ Workflow failed: {str(e)}")
            raise
    
    def _extract_table_name_from_file(self, file_path: str) -> str:
        """Extract table name from filename"""
        try:
            # Get filename without extension
            filename = Path(file_path).stem
            
            # Remove common suffixes
            suffixes_to_remove = ['_schema', '_data', '_table', '_dict', '_dictionary', '_columns']
            
            for suffix in suffixes_to_remove:
                if filename.lower().endswith(suffix.lower()):
                    filename = filename[:-len(suffix)]
                    break
            
            # Clean the filename (remove special characters, replace spaces/hyphens with underscores)
            clean_name = filename.replace(' ', '_').replace('-', '_')
            clean_name = ''.join(c for c in clean_name if c.isalnum() or c == '_')
            
            # Ensure it starts with a letter (valid identifier)
            if clean_name and clean_name[0].isdigit():
                clean_name = 'table_' + clean_name
            
            return clean_name if clean_name else 'unknown_table'
            
        except Exception as e:
            self.logger.warning(f"Failed to extract table name from {file_path}: {str(e)}")
            return 'unknown_table'
    
    def display_processed_results(self, table_name: str):
        """Display comprehensive results from Neo4j in formatted table way"""
        try:
            # Get table details from Neo4j
            table_data = self.ontology_updater.get_table_details(table_name)
            
            # Display header
            print(f"\n{'='*100}")
            print(f"TABLE: {table_name.upper()}")
            print(f"{'='*100}")
            print(f"Created: {table_data.get('created_at', 'Unknown')}")
            print(f"Updated: {table_data.get('updated_at', 'Unknown')}")
            print(f"Total Columns: {table_data.get('total_columns', 0)}")
            
            # Get columns data
            columns = table_data.get('columns', [])
            
            if not columns:
                print("No columns found for this table.")
                return
            
            # Display columns in table format
            print(f"\nCOLUMNS OVERVIEW:")
            print(f"{'-'*100}")
            
            # Table header
            header = f"{'No.':<4} {'Column Name':<25} {'Category':<15} {'Synonyms':<8} {'Domains':<8} {'Azure OpenAI':<12} {'ST Embedding':<12}"
            print(header)
            print("-" * len(header))
            
            # Table rows
            for i, col in enumerate(columns, 1):
                # Handle synonyms
                synonyms = col.get('synonyms', [])
                if isinstance(synonyms, str):
                    try:
                        synonyms = json.loads(synonyms)
                    except:
                        synonyms = []
                
                # Handle domains
                domains = col.get('applicable_domains', [])
                if isinstance(domains, str):
                    try:
                        domains = json.loads(domains)
                    except:
                        domains = []
                
                # Handle embeddings
                azure_openai_embedding = col.get('azure_openai_embedding', [])
                st_embedding = col.get('sentence_transformer_embedding', [])
                
                azure_openai_dim = len(azure_openai_embedding) if isinstance(azure_openai_embedding, list) else 0
                st_dim = len(st_embedding) if isinstance(st_embedding, list) else 0
                
                # Truncate column name if too long
                col_name = col.get('name', 'Unknown')
                if len(col_name) > 24:
                    col_name = col_name[:21] + "..."
                
                row = f"{i:<4} {col_name:<25} {col.get('category', 'Unknown'):<15} {len(synonyms):<8} {len(domains):<8} {azure_openai_dim:<12} {st_dim:<12}"
                print(row)
            
            # Display detailed column information
            print(f"\nDETAILED COLUMN INFORMATION:")
            print(f"{'-'*100}")
            
            for i, col in enumerate(columns, 1):
                print(f"\n[{i}] {col.get('name', 'Unknown')}")
                print(f"    Description: {col.get('description', 'No description')}")
                print(f"    Category: {col.get('category', 'Unknown')}")
                print(f"    Table Type: {col.get('table_type', 'Unknown')}")
                
                # Handle synonyms
                synonyms = col.get('synonyms', [])
                if isinstance(synonyms, str):
                    try:
                        synonyms = json.loads(synonyms)
                    except:
                        synonyms = []
                
                # Handle domains
                domains = col.get('applicable_domains', [])
                if isinstance(domains, str):
                    try:
                        domains = json.loads(domains)
                    except:
                        domains = []
                
                # Display synonyms (truncate if too many)
                if len(synonyms) <= 5:
                    print(f"    Synonyms: {synonyms}")
                else:
                    print(f"    Synonyms: {synonyms[:5]} ... (+{len(synonyms)-5} more)")
                
                # Display domains (truncate if too many)
                if len(domains) <= 3:
                    print(f"    Domains: {domains}")
                else:
                    print(f"    Domains: {domains[:3]} ... (+{len(domains)-3} more)")
                
                # Handle embeddings
                azure_openai_embedding = col.get('azure_openai_embedding', [])
                st_embedding = col.get('sentence_transformer_embedding', [])
                
                azure_openai_dim = len(azure_openai_embedding) if isinstance(azure_openai_embedding, list) else 0
                st_dim = len(st_embedding) if isinstance(st_embedding, list) else 0
                
                print(f"    Azure OpenAI Embedding: {azure_openai_dim} dimensions")
                print(f"    Sentence Transformer Embedding: {st_dim} dimensions")
            
            # Display summary statistics
            print(f"\n{'='*100}")
            print("DATABASE SUMMARY:")
            print(f"{'='*100}")
            
            # Get database stats
            try:
                db_stats = self.ontology_updater.get_database_stats()
                
                # Create summary table
                summary_data = [
                    ["Total Tables in Database", db_stats.get('total_tables', 0)],
                    ["Total Columns in Database", db_stats.get('total_columns', 0)],
                    ["Average Columns per Table", db_stats.get('avg_columns_per_table', 0)],
                    ["Current Table Columns", len(columns)],
                    ["Columns with Synonyms", sum(1 for col in columns if len(json.loads(col.get('synonyms', '[]')) if isinstance(col.get('synonyms'), str) else col.get('synonyms', [])) > 0)],
                    ["Columns with Domains", sum(1 for col in columns if len(json.loads(col.get('applicable_domains', '[]')) if isinstance(col.get('applicable_domains'), str) else col.get('applicable_domains', [])) > 0)],
                    ["Columns with Azure OpenAI Embeddings", sum(1 for col in columns if len(col.get('azure_openai_embedding', [])) > 0)],
                    ["Columns with ST Embeddings", sum(1 for col in columns if len(col.get('sentence_transformer_embedding', [])) > 0)]
                ]
                
                for item, value in summary_data:
                    print(f"{item:<40}: {value}")
                    
            except Exception as e:
                print(f"Could not retrieve database statistics: {str(e)}")
            
            print(f"\nAll results displayed successfully!")
            
        except Exception as e:
            self.logger.error(f"Failed to display results: {str(e)}")
            print(f"Error displaying results: {str(e)}")
            raise
    
    def display_all_tables(self):
        """Display all tables in the knowledge graph"""
        try:
            tables = self.ontology_updater.get_all_tables()
            
            print(f"\n{'='*80}")
            print("ALL TABLES IN KNOWLEDGE GRAPH")
            print(f"{'='*80}")
            
            if not tables:
                print("No tables found in the database.")
                return
            
            print(f"Found {len(tables)} table(s):")
            print(f"{'-'*80}")
            
            for i, table in enumerate(tables, 1):
                print(f"{i}. Table: {table['table_name']}")
                print(f"   Columns: {table['column_count']}")
                print(f"   Created: {table.get('created_at', 'Unknown')}")
                print(f"   Updated: {table.get('updated_at', 'Unknown')}")
                print()
            
        except Exception as e:
            self.logger.error(f"Failed to display all tables: {str(e)}")
            print(f"❌ Error displaying tables: {str(e)}")
    
    def search_similar_columns(self, query: str, embedding_type: str = "both", limit: int = 10):
        """Search and display similar columns using embeddings"""
        try:
            print(f"\n🔍 Searching for columns similar to: '{query}'")
            print(f"Embedding type: {embedding_type}, Limit: {limit}")
            print(f"{'-'*60}")
            
            results = self.ontology_updater.query_columns_by_similarity(
                query_text=query,
                embedding_type=embedding_type,
                limit=limit
            )
            
            if not results:
                print("No similar columns found.")
                return
            
            print(f"Found {len(results)} similar column(s):")
            print(f"{'-'*60}")
            
            for i, result in enumerate(results, 1):
                print(f"{i}. {result['name']} (similarity: {result.get('similarity', 'N/A')})")
                print(f"   Description: {result['description']}")
                print(f"   Domains: {result['applicable_domains'][:3]}{'...' if len(result['applicable_domains']) > 3 else ''}")
                print()
            
        except Exception as e:
            self.logger.error(f"Failed to search similar columns: {str(e)}")
            print(f"❌ Error searching columns: {str(e)}")
    
    def get_workflow_summary(self, table_name: str) -> Dict[str, Any]:
        """Get summary of the complete workflow results"""
        try:
            table_data = self.ontology_updater.get_table_details(table_name)
            db_stats = self.ontology_updater.get_database_stats()
            
            # Count embeddings
            columns = table_data.get('columns', [])
            azure_openai_embeddings = sum(1 for col in columns 
                                        if isinstance(col.get('azure_openai_embedding'), list) 
                                        and len(col['azure_openai_embedding']) > 0)
            
            st_embeddings = sum(1 for col in columns 
                              if isinstance(col.get('sentence_transformer_embedding'), list) 
                              and len(col['sentence_transformer_embedding']) > 0)
            
            summary = {
                'table_name': table_name,
                'total_columns': len(columns),
                'azure_openai_embeddings_created': azure_openai_embeddings,
                'st_embeddings_created': st_embeddings,
                'created_at': table_data.get('created_at'),
                'updated_at': table_data.get('updated_at'),
                'database_stats': db_stats
            }
            
            return summary
            
        except Exception as e:
            self.logger.error(f"Failed to get workflow summary: {str(e)}")
            return {}
    
    def cleanup(self):
        """Clean up resources"""
        try:
            if hasattr(self, 'ontology_updater'):
                self.ontology_updater.close()
            self.logger.info("Cleanup completed")
        except Exception as e:
            self.logger.warning(f"Cleanup warning: {str(e)}")
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.cleanup()