"""
Quality Analyzer for CSV Quality Analyzer
Performs comprehensive data quality analysis including completeness, uniqueness, 
validity, consistency, and outlier detection.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Tuple, Optional
from scipy import stats
import re
from datetime import datetime
import warnings
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).resolve().parent.parent.parent
sys.path.insert(0, str(project_root))


from core.config_manager import ConfigManager

warnings.filterwarnings('ignore')


class QualityAnalyzer:
    """
    Performs comprehensive data quality analysis on CSV data.
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize QualityAnalyzer with configuration.
        
        Args:
            config_manager (ConfigManager): Configuration manager instance
        """
        self.config = config_manager
        self.completeness_threshold = config_manager.get('data_quality.completeness_threshold', 0.95)
        self.uniqueness_threshold = config_manager.get('data_quality.uniqueness_threshold', 0.99)
        self.outlier_method = config_manager.get('data_quality.outlier_method', 'iqr')
        self.outlier_threshold = config_manager.get('data_quality.outlier_threshold', 1.5)
        self.enable_outlier_detection = config_manager.get('data_quality.enable_outlier_detection', True)
        self.enable_pattern_analysis = config_manager.get('data_quality.enable_pattern_analysis', True)
        
        # Validation patterns
        self.email_pattern = config_manager.get('validation_rules.email_pattern')
        self.phone_pattern = config_manager.get('validation_rules.phone_pattern')
        self.url_pattern = config_manager.get('validation_rules.url_pattern')
        
        # Custom validation rules
        self.numeric_ranges = config_manager.get('validation_rules.numeric_ranges', {})
        self.required_columns = config_manager.get('validation_rules.required_columns', [])
        self.categorical_columns = config_manager.get('validation_rules.categorical_columns', {})
    
    def analyze_quality(self, df: pd.DataFrame, column_types: Dict[str, str]) -> Dict[str, Any]:
        """
        Perform comprehensive quality analysis on the DataFrame.
        
        Args:
            df (pd.DataFrame): DataFrame to analyze
            column_types (Dict[str, str]): Column type mappings
            
        Returns:
            Dict[str, Any]: Comprehensive quality metrics
        """
        print("Performing quality analysis...")
        
        quality_metrics = {
            'overall_score': 0.0,
            'completeness': self._analyze_completeness(df),
            'uniqueness': self._analyze_uniqueness(df),
            'validity': self._analyze_validity(df, column_types),
            'consistency': self._analyze_consistency(df, column_types),
            'accuracy': self._analyze_accuracy(df, column_types),
            'duplicates': self._analyze_duplicates(df),
            'column_quality': {},
            'data_freshness': self._analyze_data_freshness(df, column_types),
            'referential_integrity': self._analyze_referential_integrity(df, column_types)
        }
        
        # Add outlier analysis if enabled
        if self.enable_outlier_detection:
            quality_metrics['outliers'] = self._detect_outliers(df, column_types)
        
        # Calculate column-level quality metrics
        print("Analyzing individual column quality...")
        for col in df.columns:
            quality_metrics['column_quality'][col] = self._analyze_column_quality(
                df[col], column_types.get(col, 'unknown'), df
            )
        
        # Calculate overall quality score
        quality_metrics['overall_score'] = self._calculate_overall_score(quality_metrics)
        
        # Add quality issues summary
        quality_metrics['issues_summary'] = self._summarize_quality_issues(quality_metrics)
        
        return quality_metrics
    
    def _analyze_completeness(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Analyze data completeness across the dataset.
        
        Args:
            df (pd.DataFrame): DataFrame to analyze
            
        Returns:
            Dict[str, Any]: Completeness analysis results
        """
        total_cells = df.size
        missing_cells = self._safe_sum(df.isna().sum())
        completeness_ratio = 1 - (missing_cells / total_cells) if total_cells > 0 else 1.0
        
        # Analyze completeness by column
        column_completeness = {}
        for col in df.columns:
            col_missing = self._safe_sum(df[col].isna())
            col_total = len(df)
            col_completeness = 1 - (col_missing / col_total) if col_total > 0 else 1.0
            
            column_completeness[col] = {
                'completeness_ratio': round(col_completeness, 4),
                'missing_count': col_missing,
                'total_count': col_total,
                'meets_threshold': col_completeness >= self.completeness_threshold
            }
        
        # Find patterns in missing data
        missing_patterns = self._analyze_missing_patterns(df)
        
        return {
            'ratio': round(completeness_ratio, 4),
            'missing_cells': missing_cells,
            'total_cells': int(total_cells),
            'meets_threshold': completeness_ratio >= self.completeness_threshold,
            'column_completeness': column_completeness,
            'missing_patterns': missing_patterns,
            'completely_empty_columns': [col for col in df.columns if df[col].isna().all()],
            'mostly_empty_columns': [col for col in df.columns if df[col].isna().sum() / len(df) > 0.9]
        }
    
    def _analyze_missing_patterns(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze patterns in missing data."""
        try:
            # Check for columns that are missing together
            missing_df = df.isna()
            
            # Find common missing patterns
            missing_combinations = missing_df.value_counts().head(10)
            
            # Calculate correlation between missing values
            missing_corr = missing_df.corr()
            high_missing_corr = []
            
            for i in range(len(missing_corr.columns)):
                for j in range(i+1, len(missing_corr.columns)):
                    corr_value = missing_corr.iloc[i, j]
                    if pd.notna(corr_value) and abs(corr_value) > 0.5:
                        high_missing_corr.append({
                            'column1': missing_corr.columns[i],
                            'column2': missing_corr.columns[j],
                            'correlation': round(float(corr_value), 3)
                        })
            
            # Safe counting with null checks
            rows_with_any_missing = self._safe_sum(missing_df.any(axis=1))
            rows_completely_missing = self._safe_sum(missing_df.all(axis=1))
            
            return {
                'common_patterns': missing_combinations.to_dict(),
                'missing_correlations': high_missing_corr,
                'rows_with_any_missing': rows_with_any_missing,
                'rows_completely_missing': rows_completely_missing
            }
            
        except Exception as e:
            return {'error': f'Missing pattern analysis failed: {str(e)}'}
    
    def _analyze_uniqueness(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Analyze data uniqueness across the dataset.
        
        Args:
            df (pd.DataFrame): DataFrame to analyze
            
        Returns:
            Dict[str, Any]: Uniqueness analysis results
        """
        total_rows = len(df)
        unique_rows = len(df.drop_duplicates())
        uniqueness_ratio = unique_rows / total_rows if total_rows > 0 else 1.0
        
        # Analyze uniqueness by column
        column_uniqueness = {}
        for col in df.columns:
            col_unique = self._safe_int_convert(df[col].nunique())
            col_total = self._safe_sum(df[col].notna())  # Only count non-null values
            col_uniqueness_ratio = col_unique / col_total if col_total > 0 else 1.0
            
            column_uniqueness[col] = {
                'uniqueness_ratio': round(col_uniqueness_ratio, 4),
                'unique_count': col_unique,
                'total_non_null': col_total,
                'is_primary_key_candidate': col_uniqueness_ratio == 1.0 and col_total > 0,
                'duplicate_values': self._find_duplicate_values(df[col])
            }
        
        # Find potential composite keys
        composite_keys = self._find_composite_keys(df)
        
        return {
            'ratio': round(uniqueness_ratio, 4),
            'unique_rows': unique_rows,
            'duplicate_rows': total_rows - unique_rows,
            'duplicate_percentage': round(((total_rows - unique_rows) / total_rows) * 100, 2) if total_rows > 0 else 0,
            'meets_threshold': uniqueness_ratio >= self.uniqueness_threshold,
            'column_uniqueness': column_uniqueness,
            'potential_primary_keys': [col for col, info in column_uniqueness.items() 
                                     if info['is_primary_key_candidate']],
            'composite_key_candidates': composite_keys
        }
    
    def _find_duplicate_values(self, series: pd.Series, top_n: int = 5) -> Dict[str, int]:
        """Find the most common duplicate values in a series."""
        duplicates = series.value_counts()
        duplicates = duplicates[duplicates > 1]
        return duplicates.head(top_n).to_dict()
    
    def _find_composite_keys(self, df: pd.DataFrame, max_combinations: int = 5) -> List[Dict[str, Any]]:
        """Find potential composite key combinations."""
        composite_keys = []
        columns = df.columns.tolist()
        
        # Check 2-column combinations
        for i in range(len(columns)):
            for j in range(i+1, min(len(columns), i+max_combinations)):
                col1, col2 = columns[i], columns[j]
                combo_df = df[[col1, col2]].dropna()
                
                if len(combo_df) > 0:
                    unique_combos = len(combo_df.drop_duplicates())
                    total_combos = len(combo_df)
                    uniqueness = unique_combos / total_combos
                    
                    if uniqueness > 0.98:  # High uniqueness
                        composite_keys.append({
                            'columns': [col1, col2],
                            'uniqueness_ratio': round(uniqueness, 4),
                            'duplicate_count': total_combos - unique_combos
                        })
        
        return sorted(composite_keys, key=lambda x: x['uniqueness_ratio'], reverse=True)[:5]
    
    def _analyze_validity(self, df: pd.DataFrame, column_types: Dict[str, str]) -> Dict[str, Any]:
        """
        Analyze data validity based on expected formats and business rules.
        
        Args:
            df (pd.DataFrame): DataFrame to analyze
            column_types (Dict[str, str]): Column type mappings
            
        Returns:
            Dict[str, Any]: Validity analysis results
        """
        validity_results = {
            'overall_validity_ratio': 0.0,
            'valid_records': 0,
            'invalid_records': 0,
            'column_validity': {},
            'validation_errors': []
        }
        
        total_valid_fields = 0
        total_fields = 0
        
        try:
            for col, col_type in column_types.items():
                if col not in df.columns:
                    continue
                
                col_validity = self._validate_column(df[col], col_type, col)
                validity_results['column_validity'][col] = col_validity
                
                # Safe aggregation of counts
                valid_count = self._safe_int_convert(col_validity.get('valid_count', 0))
                total_count = self._safe_int_convert(col_validity.get('total_count', 0))
                
                total_valid_fields += valid_count
                total_fields += total_count
            
            # Calculate overall validity
            if total_fields > 0:
                validity_results['overall_validity_ratio'] = round(total_valid_fields / total_fields, 4)
                validity_results['valid_records'] = total_valid_fields
                validity_results['invalid_records'] = total_fields - total_valid_fields
            
            # Check business rules
            business_rule_results = self._check_business_rules(df, column_types)
            validity_results['business_rules'] = business_rule_results
            
        except Exception as e:
            validity_results['validation_errors'].append(f'Validity analysis failed: {str(e)}')
        
        return validity_results
    
    def _validate_column(self, series: pd.Series, col_type: str, col_name: str) -> Dict[str, Any]:
        """
        Validate individual column based on its type and business rules.
        
        Args:
            series (pd.Series): Column to validate
            col_type (str): Expected column type
            col_name (str): Column name for rule lookup
            
        Returns:
            Dict[str, Any]: Column validation results
        """
        clean_series = series.dropna()
        total_count = len(clean_series)
        valid_count = 0
        validation_errors = []
        
        if total_count == 0:
            return {
                'valid_count': 0,
                'total_count': 0,
                'validity_ratio': 1.0,
                'validation_errors': ['Column is empty']
            }
        
        # Type-specific validation with safe counting
        try:
            if col_type == 'email' and self.email_pattern:
                matches = clean_series.astype(str).str.match(self.email_pattern, na=False)
                valid_count = self._safe_sum(matches)
                
                if valid_count < total_count:
                    invalid_samples = clean_series[~matches].head(3).tolist()
                    validation_errors.append(f"Invalid email formats found: {invalid_samples}")
            
            elif col_type == 'phone' and self.phone_pattern:
                matches = clean_series.astype(str).str.match(self.phone_pattern, na=False)
                valid_count = self._safe_sum(matches)
                
                if valid_count < total_count:
                    invalid_samples = clean_series[~matches].head(3).tolist()
                    validation_errors.append(f"Invalid phone formats found: {invalid_samples}")
            
            elif col_type == 'url' and self.url_pattern:
                matches = clean_series.astype(str).str.match(self.url_pattern, na=False)
                valid_count = self._safe_sum(matches)
                
                if valid_count < total_count:
                    invalid_samples = clean_series[~matches].head(3).tolist()
                    validation_errors.append(f"Invalid URL formats found: {invalid_samples}")
            
            elif col_type in ['numeric', 'integer', 'float']:
                try:
                    numeric_series = pd.to_numeric(clean_series, errors='coerce')
                    valid_count = self._safe_sum(numeric_series.notna())
                    
                    # Check numeric ranges if specified
                    if col_name in self.numeric_ranges:
                        range_config = self.numeric_ranges[col_name]
                        min_val = range_config.get('min')
                        max_val = range_config.get('max')
                        
                        if min_val is not None:
                            out_of_range = self._safe_sum(numeric_series < min_val)
                            if out_of_range > 0:
                                validation_errors.append(f"{out_of_range} values below minimum {min_val}")
                        
                        if max_val is not None:
                            out_of_range = self._safe_sum(numeric_series > max_val)
                            if out_of_range > 0:
                                validation_errors.append(f"{out_of_range} values above maximum {max_val}")
                    
                except Exception as e:
                    validation_errors.append(f"Numeric validation failed: {str(e)}")
                    valid_count = 0
            
            elif col_type == 'datetime':
                try:
                    datetime_series = pd.to_datetime(clean_series, errors='coerce')
                    valid_count = self._safe_sum(datetime_series.notna())
                    
                    if valid_count < total_count:
                        invalid_samples = clean_series[datetime_series.isna()].head(3).tolist()
                        validation_errors.append(f"Invalid datetime formats found: {invalid_samples}")
                        
                except Exception as e:
                    validation_errors.append(f"Datetime validation failed: {str(e)}")
                    valid_count = 0
            
            elif col_type == 'categorical':
                # Check if values are in expected categories
                if col_name in self.categorical_columns:
                    expected_categories = set(self.categorical_columns[col_name])
                    actual_categories = set(clean_series.unique())
                    invalid_categories = actual_categories - expected_categories
                    
                    if invalid_categories:
                        validation_errors.append(f"Unexpected categories found: {list(invalid_categories)}")
                        valid_matches = clean_series.isin(expected_categories)
                        valid_count = self._safe_sum(valid_matches)
                    else:
                        valid_count = total_count
                else:
                    valid_count = total_count  # No specific validation rules
            
            else:
                # For other types, assume all non-null values are valid
                valid_count = total_count
            
        except Exception as e:
            validation_errors.append(f"Validation error: {str(e)}")
            valid_count = 0
        
        validity_ratio = valid_count / total_count if total_count > 0 else 1.0
        
        return {
            'valid_count': valid_count,
            'total_count': total_count,
            'validity_ratio': round(validity_ratio, 4),
            'validation_errors': validation_errors
        }
    
    def _check_business_rules(self, df: pd.DataFrame, column_types: Dict[str, str]) -> Dict[str, Any]:
        """Check business-specific validation rules."""
        business_rule_results = {
            'required_columns_check': self._check_required_columns(df),
            'data_consistency_checks': self._check_data_consistency(df),
            'cross_column_validation': self._check_cross_column_rules(df)
        }
        
        return business_rule_results
    
    def _check_required_columns(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Check if required columns are present and not empty."""
        results = {
            'missing_required_columns': [],
            'empty_required_columns': [],
            'all_required_present': True
        }
        
        for required_col in self.required_columns:
            if required_col not in df.columns:
                results['missing_required_columns'].append(required_col)
                results['all_required_present'] = False
            elif df[required_col].isna().all():
                results['empty_required_columns'].append(required_col)
                results['all_required_present'] = False
        
        return results
    
    def _check_data_consistency(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Check for data consistency issues."""
        consistency_issues = []
        
        # Check for mixed case in text columns
        text_columns = df.select_dtypes(include=['object']).columns
        for col in text_columns:
            if df[col].dtype == 'object':
                text_series = df[col].dropna().astype(str)
                if len(text_series) > 0:
                    case_consistency = self._check_case_consistency(text_series)
                    if case_consistency['inconsistent']:
                        consistency_issues.append({
                            'type': 'case_inconsistency',
                            'column': col,
                            'details': case_consistency
                        })
        
        return consistency_issues
    
    def _check_case_consistency(self, series: pd.Series) -> Dict[str, Any]:
        """Check case consistency in text series."""
        total = len(series)
        upper_count = series.str.isupper().sum()
        lower_count = series.str.islower().sum()
        title_count = series.str.istitle().sum()
        
        # Consider inconsistent if no single case pattern dominates
        max_case_ratio = max(upper_count, lower_count, title_count) / total
        
        return {
            'inconsistent': max_case_ratio < 0.8,
            'upper_count': int(upper_count),
            'lower_count': int(lower_count),
            'title_count': int(title_count),
            'mixed_count': int(total - upper_count - lower_count - title_count),
            'dominant_case_ratio': round(max_case_ratio, 3)
        }
    
    def _check_cross_column_rules(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Check cross-column validation rules."""
        cross_column_issues = []
        
        # Example: Check if start_date <= end_date
        date_columns = [col for col in df.columns if 'date' in col.lower()]
        if len(date_columns) >= 2:
            for i, start_col in enumerate(date_columns):
                for end_col in date_columns[i+1:]:
                    if 'start' in start_col.lower() and 'end' in end_col.lower():
                        try:
                            start_dates = pd.to_datetime(df[start_col], errors='coerce')
                            end_dates = pd.to_datetime(df[end_col], errors='coerce')
                            
                            invalid_ranges = ((start_dates > end_dates) & 
                                            start_dates.notna() & 
                                            end_dates.notna()).sum()
                            
                            if invalid_ranges > 0:
                                cross_column_issues.append({
                                    'type': 'invalid_date_range',
                                    'columns': [start_col, end_col],
                                    'invalid_count': int(invalid_ranges),
                                    'description': f"{start_col} should be <= {end_col}"
                                })
                        except Exception:
                            pass
        
        return cross_column_issues
    
    def _analyze_consistency(self, df: pd.DataFrame, column_types: Dict[str, str]) -> Dict[str, Any]:
        """
        Analyze data consistency across the dataset.
        
        Args:
            df (pd.DataFrame): DataFrame to analyze
            column_types (Dict[str, str]): Column type mappings
            
        Returns:
            Dict[str, Any]: Consistency analysis results
        """
        consistency_results = {
            'format_consistency': {},
            'pattern_consistency': {},
            'encoding_consistency': {},
            'structural_consistency': self._check_structural_consistency(df)
        }
        
        for col in df.columns:
            col_type = column_types.get(col, 'unknown')
            
            if col_type == 'text':
                consistency_results['format_consistency'][col] = self._check_format_consistency(df[col])
            
            if self.enable_pattern_analysis and col_type in ['text', 'categorical']:
                consistency_results['pattern_consistency'][col] = self._check_pattern_consistency(df[col])
        
        return consistency_results
    
    def _check_format_consistency(self, series: pd.Series) -> Dict[str, Any]:
        """Check format consistency within a column."""
        text_series = series.dropna().astype(str)
        
        if len(text_series) == 0:
            return {'consistent': True, 'reason': 'Empty column'}
        
        # Check length consistency
        lengths = text_series.str.len()
        length_variance = lengths.var()
        
        # Check character pattern consistency
        patterns = text_series.str.extract(r'([A-Za-z]+)', expand=False).dropna()
        pattern_consistency = len(patterns.unique()) / len(patterns) if len(patterns) > 0 else 1.0
        
        return {
            'length_variance': round(length_variance, 2),
            'length_consistency_score': round(1 / (1 + length_variance), 3),
            'pattern_consistency_score': round(1 - pattern_consistency, 3),
            'consistent': length_variance < 100 and pattern_consistency < 0.5
        }
    
    def _check_pattern_consistency(self, series: pd.Series) -> Dict[str, Any]:
        """Check pattern consistency in text data."""
        text_series = series.dropna().astype(str)
        
        if len(text_series) == 0:
            return {'patterns': [], 'consistency_score': 1.0}
        
        # Extract common patterns
        patterns = {}
        for text in text_series.head(100):  # Sample for performance
            # Extract pattern (letters as 'A', digits as '9', others as symbols)
            pattern = re.sub(r'[A-Za-z]', 'A', text)
            pattern = re.sub(r'\d', '9', pattern)
            patterns[pattern] = patterns.get(pattern, 0) + 1
        
        # Calculate consistency score
        if patterns:
            most_common_count = max(patterns.values())
            consistency_score = most_common_count / len(text_series.head(100))
        else:
            consistency_score = 1.0
        
        return {
            'patterns': dict(sorted(patterns.items(), key=lambda x: x[1], reverse=True)[:5]),
            'consistency_score': round(consistency_score, 3),
            'unique_patterns': len(patterns)
        }
    
    def _check_structural_consistency(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Check structural consistency of the dataset."""
        return {
            'column_count_consistency': True,  # Always true for single file
            'data_type_consistency': self._check_data_type_consistency(df),
            'row_structure_consistency': self._check_row_structure_consistency(df)
        }
    
    def _check_data_type_consistency(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Check if data types are consistent throughout the dataset."""
        type_issues = []
        
        for col in df.columns:
            if df[col].dtype == 'object':
                # Check if column contains mixed types
                sample = df[col].dropna().head(100)
                type_counts = {}
                
                for value in sample:
                    if isinstance(value, str):
                        try:
                            float(value)
                            type_counts['numeric_string'] = type_counts.get('numeric_string', 0) + 1
                        except ValueError:
                            type_counts['text'] = type_counts.get('text', 0) + 1
                    elif isinstance(value, (int, float)):
                        type_counts['numeric'] = type_counts.get('numeric', 0) + 1
                    else:
                        type_counts['other'] = type_counts.get('other', 0) + 1
                
                if len(type_counts) > 1:
                    type_issues.append({
                        'column': col,
                        'mixed_types': type_counts
                    })
        
        return {
            'has_mixed_types': len(type_issues) > 0,
            'mixed_type_columns': type_issues
        }
    
    def _check_row_structure_consistency(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Check if rows have consistent structure."""
        # Check for rows with unusual number of non-null values
        non_null_counts = df.count(axis=1)
        mean_non_null = non_null_counts.mean()
        std_non_null = non_null_counts.std()
        
        # Identify rows with significantly different structure
        threshold = mean_non_null - 2 * std_non_null
        inconsistent_rows = non_null_counts[non_null_counts < threshold]
        
        return {
            'mean_fields_per_row': round(mean_non_null, 2),
            'std_fields_per_row': round(std_non_null, 2),
            'inconsistent_row_count': len(inconsistent_rows),
            'inconsistent_row_indices': inconsistent_rows.index.tolist()[:10]  # First 10
        }
    
    def _analyze_accuracy(self, df: pd.DataFrame, column_types: Dict[str, str]) -> Dict[str, Any]:
        """
        Analyze data accuracy through various checks.
        
        Args:
            df (pd.DataFrame): DataFrame to analyze
            column_types (Dict[str, str]): Column type mappings
            
        Returns:
            Dict[str, Any]: Accuracy analysis results
        """
        accuracy_results = {
            'statistical_accuracy': {},
            'domain_accuracy': {},
            'referential_accuracy': {}
        }
        
        # Statistical accuracy checks (outliers, distributions)
        numeric_columns = [col for col, dtype in column_types.items() 
                          if dtype in ['numeric', 'integer', 'float'] and col in df.columns]
        
        for col in numeric_columns:
            accuracy_results['statistical_accuracy'][col] = self._check_statistical_accuracy(df[col])
        
        # Domain accuracy checks (realistic value ranges)
        accuracy_results['domain_accuracy'] = self._check_domain_accuracy(df, column_types)
        
        return accuracy_results
    
    def _check_statistical_accuracy(self, series: pd.Series) -> Dict[str, Any]:
        """Check statistical accuracy of numeric data."""
        numeric_series = pd.to_numeric(series, errors='coerce').dropna()
        
        if len(numeric_series) == 0:
            return {'error': 'No valid numeric data'}
        
        # Calculate statistical measures
        mean_val = numeric_series.mean()
        std_val = numeric_series.std()
        skewness = numeric_series.skew()
        kurtosis = numeric_series.kurtosis()
        
        # Check for suspicious patterns
        suspicious_patterns = []
        
        # Check for too many repeated values
        value_counts = numeric_series.value_counts()
        most_common_ratio = value_counts.iloc[0] / len(numeric_series) if len(value_counts) > 0 else 0
        if most_common_ratio > 0.5:
            suspicious_patterns.append(f"Single value ({value_counts.index[0]}) represents {most_common_ratio:.1%} of data")
        
        # Check for suspicious round numbers
        round_numbers = numeric_series[numeric_series % 1 == 0]
        if len(round_numbers) / len(numeric_series) > 0.8 and std_val > 0:
            suspicious_patterns.append("Unusually high proportion of round numbers")
        
        return {
            'mean': round(mean_val, 3),
            'std': round(std_val, 3),
            'skewness': round(skewness, 3),
            'kurtosis': round(kurtosis, 3),
            'suspicious_patterns': suspicious_patterns,
            'accuracy_score': self._calculate_accuracy_score(numeric_series)
        }
    
    def _calculate_accuracy_score(self, series: pd.Series) -> float:
        """Calculate accuracy score based on statistical properties."""
        # Simple heuristic scoring
        score = 1.0
        
        # Penalize for extreme skewness
        skewness = abs(series.skew())
        if skewness > 2:
            score -= 0.2
        
        # Penalize for extreme kurtosis
        kurtosis = abs(series.kurtosis())
        if kurtosis > 3:
            score -= 0.1
        
        # Penalize for too many identical values
        value_counts = series.value_counts()
        if len(value_counts) > 0:
            most_common_ratio = value_counts.iloc[0] / len(series)
            if most_common_ratio > 0.5:
                score -= 0.3
        
        return max(0.0, score)
    
    def _check_domain_accuracy(self, df: pd.DataFrame, column_types: Dict[str, str]) -> Dict[str, Any]:
        """Check if values are reasonable for their domain."""
        domain_issues = []
        
        # Check age columns
        age_columns = [col for col in df.columns if 'age' in col.lower()]
        for col in age_columns:
            if col in df.columns and column_types.get(col) in ['numeric', 'integer']:
                numeric_series = pd.to_numeric(df[col], errors='coerce').dropna()
                if len(numeric_series) > 0:
                    unrealistic_ages = numeric_series[(numeric_series < 0) | (numeric_series > 150)]
                    if len(unrealistic_ages) > 0:
                        domain_issues.append({
                            'column': col,
                            'issue': 'unrealistic_age_values',
                            'count': len(unrealistic_ages),
                            'examples': unrealistic_ages.head(5).tolist()
                        })
        
        # Check percentage columns
        pct_columns = [col for col in df.columns if 'percent' in col.lower() or 'pct' in col.lower() or col.endswith('%')]
        for col in pct_columns:
            if col in df.columns and column_types.get(col) in ['numeric', 'float']:
                numeric_series = pd.to_numeric(df[col], errors='coerce').dropna()
                if len(numeric_series) > 0:
                    invalid_pcts = numeric_series[(numeric_series < 0) | (numeric_series > 100)]
                    if len(invalid_pcts) > 0:
                        domain_issues.append({
                            'column': col,
                            'issue': 'invalid_percentage_values',
                            'count': len(invalid_pcts),
                            'examples': invalid_pcts.head(5).tolist()
                        })
        
        return {
            'domain_issues': domain_issues,
            'domain_accuracy_score': 1.0 - (len(domain_issues) / max(len(df.columns), 1))
        }
    
    def _analyze_data_freshness(self, df: pd.DataFrame, column_types: Dict[str, str]) -> Dict[str, Any]:
        """Analyze data freshness based on date columns."""
        freshness_results = {
            'has_date_columns': False,
            'date_analysis': {},
            'freshness_score': 1.0
        }
        
        date_columns = [col for col, dtype in column_types.items() 
                       if dtype == 'datetime' and col in df.columns]
        
        if date_columns:
            freshness_results['has_date_columns'] = True
            current_date = datetime.now()
            
            for col in date_columns:
                try:
                    date_series = pd.to_datetime(df[col], errors='coerce').dropna()
                    if len(date_series) > 0:
                        min_date = date_series.min()
                        max_date = date_series.max()
                        days_since_latest = (current_date - max_date).days
                        
                        freshness_results['date_analysis'][col] = {
                            'min_date': min_date.isoformat(),
                            'max_date': max_date.isoformat(),
                            'days_since_latest': days_since_latest,
                            'data_span_days': (max_date - min_date).days,
                            'freshness_category': self._categorize_freshness(days_since_latest)
                        }
                except Exception as e:
                    freshness_results['date_analysis'][col] = {'error': str(e)}
        
        return freshness_results
    
    def _categorize_freshness(self, days_since_latest: int) -> str:
        """Categorize data freshness based on days since latest record."""
        if days_since_latest <= 1:
            return 'very_fresh'
        elif days_since_latest <= 7:
            return 'fresh'
        elif days_since_latest <= 30:
            return 'moderately_fresh'
        elif days_since_latest <= 90:
            return 'stale'
        else:
            return 'very_stale'
    
    def _analyze_referential_integrity(self, df: pd.DataFrame, column_types: Dict[str, str]) -> Dict[str, Any]:
        """Analyze referential integrity within the dataset."""
        integrity_results = {
            'foreign_key_candidates': [],
            'orphaned_records': {},
            'integrity_score': 1.0
        }
        
        # Identify potential foreign key relationships
        id_columns = [col for col, dtype in column_types.items() 
                     if 'id' in col.lower() and col in df.columns]
        
        for col in id_columns:
            # Check if this column references another column
            for other_col in id_columns:
                if col != other_col:
                    col_values = set(df[col].dropna())
                    other_values = set(df[other_col].dropna())
                    
                    # Check if col values are subset of other_col values
                    if col_values.issubset(other_values) and len(col_values) > 0:
                        orphaned = col_values - other_values
                        if len(orphaned) == 0:
                            integrity_results['foreign_key_candidates'].append({
                                'foreign_key': col,
                                'references': other_col,
                                'integrity': 'perfect'
                            })
                        else:
                            integrity_results['foreign_key_candidates'].append({
                                'foreign_key': col,
                                'references': other_col,
                                'integrity': 'partial',
                                'orphaned_count': len(orphaned),
                                'orphaned_samples': list(orphaned)[:5]
                            })
        
        return integrity_results
    
    def _detect_outliers(self, df: pd.DataFrame, column_types: Dict[str, str]) -> Dict[str, Any]:
        """
        Detect outliers in numeric columns.
        
        Args:
            df (pd.DataFrame): DataFrame to analyze
            column_types (Dict[str, str]): Column type mappings
            
        Returns:
            Dict[str, Any]: Outlier detection results
        """
        outliers = {}
        
        numeric_columns = [col for col, dtype in column_types.items() 
                          if dtype in ['numeric', 'integer', 'float'] and col in df.columns]
        
        for col in numeric_columns:
            outliers[col] = self._detect_column_outliers(df[col])
        
        return outliers
    
    def _detect_column_outliers(self, series: pd.Series) -> Dict[str, Any]:
        """Detect outliers in a single numeric column."""
        numeric_series = pd.to_numeric(series, errors='coerce').dropna()
        
        if len(numeric_series) < 4:  # Need minimum data points
            return {
                'method': self.outlier_method,
                'outliers': [],
                'count': 0,
                'percentage': 0.0,
                'bounds': None
            }
        
        outlier_indices = []
        bounds = None
        
        try:
            if self.outlier_method == 'iqr':
                Q1 = numeric_series.quantile(0.25)
                Q3 = numeric_series.quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - self.outlier_threshold * IQR
                upper_bound = Q3 + self.outlier_threshold * IQR
                bounds = {'lower': float(lower_bound), 'upper': float(upper_bound)}
                
                outlier_mask = (numeric_series < lower_bound) | (numeric_series > upper_bound)
                outlier_indices = numeric_series[outlier_mask].index.tolist()
            
            elif self.outlier_method == 'zscore':
                z_scores = np.abs(stats.zscore(numeric_series))
                outlier_mask = z_scores > self.outlier_threshold
                outlier_indices = numeric_series[outlier_mask].index.tolist()
            
            elif self.outlier_method == 'modified_zscore':
                median = numeric_series.median()
                mad = np.median(np.abs(numeric_series - median))
                if mad != 0:
                    modified_z_scores = 0.6745 * (numeric_series - median) / mad
                    outlier_mask = np.abs(modified_z_scores) > self.outlier_threshold
                    outlier_indices = numeric_series[outlier_mask].index.tolist()
                else:
                    outlier_indices = []
            
            outlier_values = numeric_series.loc[outlier_indices].tolist() if outlier_indices else []
            
            # Safe conversions
            outlier_count = len(outlier_indices)
            outlier_percentage = (outlier_count / len(numeric_series)) * 100 if len(numeric_series) > 0 else 0
            
            return {
                'method': self.outlier_method,
                'outliers': outlier_values[:20],  # Limit to first 20
                'outlier_indices': outlier_indices[:20],
                'count': outlier_count,
                'percentage': round(outlier_percentage, 2),
                'bounds': bounds,
                'outlier_summary': {
                    'min_outlier': float(min(outlier_values)) if outlier_values else None,
                    'max_outlier': float(max(outlier_values)) if outlier_values else None,
                    'mean_outlier': float(np.mean(outlier_values)) if outlier_values else None
                }
            }
            
        except Exception as e:
            return {
                'method': self.outlier_method,
                'outliers': [],
                'count': 0,
                'percentage': 0.0,
                'bounds': None,
                'error': f'Outlier detection failed: {str(e)}'
            }
    
    def _analyze_duplicates(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze duplicate records in detail."""
        try:
            duplicate_mask = df.duplicated(keep=False)
            duplicate_rows = df[duplicate_mask]
            
            # Safe counting
            duplicate_count = self._safe_sum(duplicate_mask)
            total_rows = len(df)
            
            # Group duplicates
            duplicate_groups = {}
            if not duplicate_rows.empty:
                for idx, row in duplicate_rows.iterrows():
                    row_tuple = tuple(row.values)
                    if row_tuple not in duplicate_groups:
                        duplicate_groups[row_tuple] = []
                    duplicate_groups[row_tuple].append(idx)
            
            # Analyze partial duplicates (same values in subset of columns)
            partial_duplicates = self._find_partial_duplicates(df)
            
            return {
                'exact_duplicate_count': duplicate_count,
                'exact_duplicate_percentage': round((duplicate_count / total_rows) * 100, 2) if total_rows > 0 else 0,
                'unique_duplicate_groups': len(duplicate_groups),
                'duplicate_groups_sample': dict(list(duplicate_groups.items())[:5]),
                'partial_duplicates': partial_duplicates
            }
        except Exception as e:
            return {
                'error': f'Duplicate analysis failed: {str(e)}',
                'exact_duplicate_count': 0,
                'exact_duplicate_percentage': 0,
                'unique_duplicate_groups': 0,
                'duplicate_groups_sample': {},
                'partial_duplicates': {}
            }
    
    def _find_partial_duplicates(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Find records that are duplicates in subset of columns."""
        partial_duplicates = {}
        
        try:
            # Check key columns for partial duplicates
            key_columns = [col for col in df.columns if 'id' in col.lower() or 'key' in col.lower()]
            
            for col in key_columns[:3]:  # Limit to first 3 to avoid performance issues
                if col in df.columns:
                    col_duplicates = df[df[col].duplicated(keep=False) & df[col].notna()]
                    if not col_duplicates.empty:
                        col_duplicate_count = len(col_duplicates)
                        col_unique_count = self._safe_int_convert(col_duplicates[col].nunique())
                        
                        partial_duplicates[col] = {
                            'count': col_duplicate_count,
                            'unique_values': col_unique_count,
                            'sample_indices': col_duplicates.index.tolist()[:10]
                        }
            
            return partial_duplicates
            
        except Exception as e:
            return {'error': f'Partial duplicate analysis failed: {str(e)}'}
    
    def _safe_int_convert(self, value) -> int:
        """Safely convert a value to integer."""
        if value is None:
            return 0
        if pd.isna(value):
            return 0
        try:
            if isinstance(value, str) and value.strip() == '':
                return 0
            return int(float(value))  # Convert through float first to handle decimal strings
        except (ValueError, TypeError, OverflowError):
            return 0
    
    def _safe_float_convert(self, value) -> float:
        """Safely convert a value to float."""
        if value is None:
            return 0.0
        if pd.isna(value):
            return 0.0
        try:
            if isinstance(value, str) and value.strip() == '':
                return 0.0
            return float(value)
        except (ValueError, TypeError, OverflowError):
            return 0.0
    
    def _safe_sum(self, series_or_value):
        """Safely sum a pandas Series or convert a value."""
        try:
            if hasattr(series_or_value, 'sum'):
                result = series_or_value.sum()
                return self._safe_int_convert(result)
            else:
                return self._safe_int_convert(series_or_value)
        except Exception:
            return 0
    
    def _analyze_column_quality(self, series: pd.Series, col_type: str, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze comprehensive quality metrics for individual column."""
        # Basic quality metrics
        completeness = 1 - (series.isna().sum() / len(series)) if len(series) > 0 else 1.0
        uniqueness = series.nunique() / len(series.dropna()) if len(series.dropna()) > 0 else 1.0
        
        # Validity score (simplified)
        validity = 1.0  # Would be calculated based on type-specific validation
        
        # Consistency score (simplified)
        consistency = 1.0  # Would be calculated based on format/pattern consistency
        
        # Calculate weighted quality score
        weights = {'completeness': 0.3, 'uniqueness': 0.3, 'validity': 0.25, 'consistency': 0.15}
        quality_score = (
            weights['completeness'] * completeness +
            weights['uniqueness'] * uniqueness +
            weights['validity'] * validity +
            weights['consistency'] * consistency
        )
        
        return {
            'completeness': round(completeness, 4),
            'uniqueness': round(uniqueness, 4),
            'validity': round(validity, 4),
            'consistency': round(consistency, 4),
            'quality_score': round(quality_score, 4),
            'data_type': col_type,
            'quality_grade': self._assign_quality_grade(quality_score)
        }
    
    def _assign_quality_grade(self, score: float) -> str:
        """Assign quality grade based on score."""
        if score >= 0.9:
            return 'A'
        elif score >= 0.8:
            return 'B'
        elif score >= 0.7:
            return 'C'
        elif score >= 0.6:
            return 'D'
        else:
            return 'F'
    
    def _calculate_overall_score(self, quality_metrics: Dict[str, Any]) -> float:
        """Calculate overall quality score from individual metrics."""
        weights = {
            'completeness': 0.25,
            'uniqueness': 0.20,
            'validity': 0.25,
            'consistency': 0.15,
            'accuracy': 0.15
        }
        
        score = 0.0
        
        # Completeness score
        completeness = quality_metrics.get('completeness', {}).get('ratio', 1.0)
        score += weights['completeness'] * completeness
        
        # Uniqueness score
        uniqueness = quality_metrics.get('uniqueness', {}).get('ratio', 1.0)
        score += weights['uniqueness'] * uniqueness
        
        # Validity score
        validity = quality_metrics.get('validity', {}).get('overall_validity_ratio', 1.0)
        score += weights['validity'] * validity
        
        # Consistency score (simplified)
        consistency = 0.8  # Placeholder - would calculate from consistency metrics
        score += weights['consistency'] * consistency
        
        # Accuracy score
        accuracy = quality_metrics.get('accuracy', {}).get('domain_accuracy', {}).get('domain_accuracy_score', 1.0)
        score += weights['accuracy'] * accuracy
        
        return round(min(score, 1.0), 4)
    
    def _summarize_quality_issues(self, quality_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Summarize key quality issues found."""
        issues = {
            'critical_issues': [],
            'major_issues': [],
            'minor_issues': [],
            'recommendations': []
        }
        
        # Check completeness issues
        completeness = quality_metrics.get('completeness', {})
        if completeness.get('ratio', 1.0) < 0.8:
            issues['critical_issues'].append(f"Low data completeness: {completeness.get('ratio', 0):.1%}")
        
        # Check uniqueness issues
        uniqueness = quality_metrics.get('uniqueness', {})
        if uniqueness.get('duplicate_rows', 0) > 0:
            duplicate_pct = uniqueness.get('duplicate_percentage', 0)
            if duplicate_pct > 10:
                issues['major_issues'].append(f"High duplicate rate: {duplicate_pct:.1f}%")
            else:
                issues['minor_issues'].append(f"Some duplicates found: {duplicate_pct:.1f}%")
        
        # Check outliers
        outliers = quality_metrics.get('outliers', {})
        high_outlier_columns = [col for col, info in outliers.items() 
                               if info.get('percentage', 0) > 5]
        if high_outlier_columns:
            issues['major_issues'].append(f"High outlier percentage in columns: {high_outlier_columns}")
        
        # Generate recommendations
        if completeness.get('ratio', 1.0) < 0.9:
            issues['recommendations'].append("Investigate missing data patterns and implement data validation")
        
        if uniqueness.get('duplicate_rows', 0) > 0:
            issues['recommendations'].append("Implement deduplication processes")
        
        return issues


if __name__ == "__main__":
    # Example usage
    from core.config_manager import ConfigManager
    import pandas as pd
    import numpy as np
    
    # Create sample data for testing
    np.random.seed(42)
    sample_data = {
        'id': range(1, 101),
        'name': [f'Person {i}' for i in range(1, 101)],
        'email': [f'person{i}@example.com' for i in range(1, 101)],
        'age': np.random.randint(18, 65, 100),
        'salary': np.random.normal(50000, 15000, 100),
        'is_active': np.random.choice([True, False], 100)
    }
    
    # Add some quality issues
    sample_data['age'] = sample_data['age'].astype(float)
    sample_data['age'][5] = np.nan  # Missing value
    sample_data['age'][10] = 150  # Outlier
    sample_data['email'][7] = 'invalid-email'  # Invalid format
    
    df = pd.DataFrame(sample_data)
    
    config = ConfigManager()
    analyzer = QualityAnalyzer(config)
    
    column_types = {
        'id': 'integer',
        'name': 'text',
        'email': 'email',
        'age': 'integer',
        'salary': 'numeric',
        'is_active': 'boolean'
    }
    
    quality_results = analyzer.analyze_quality(df, column_types)
    print(f"Overall Quality Score: {quality_results['overall_score']:.2%}")
    print(f"Completeness: {quality_results['completeness']['ratio']:.2%}")
    print(f"Uniqueness: {quality_results['uniqueness']['ratio']:.2%}")