"""
Utility Functions for CSV Quality Analyzer
Contains logging, progress tracking, memory monitoring, and error handling utilities.
"""

import os
import sys
import psutil
import logging
import traceback
from datetime import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path
import json
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).resolve().parent.parent.parent
sys.path.insert(0, str(project_root))



def setup_logging(config_manager) -> logging.Logger:
    """
    Set up logging configuration based on config manager settings.
    
    Args:
        config_manager: ConfigManager instance
        
    Returns:
        logging.Logger: Configured logger instance
    """
    log_level = config_manager.get('logging.level', 'INFO')
    log_to_file = config_manager.get('logging.log_to_file', True)
    log_file = config_manager.get('logging.log_file', 'csv_analyzer.log')
    log_format = config_manager.get('logging.log_format', 
                                   '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # Create logger
    logger = logging.getLogger('CSVQualityAnalyzer')
    logger.setLevel(getattr(logging, log_level.upper(), logging.INFO))
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Create formatters
    formatter = logging.Formatter(log_format)
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler
    if log_to_file and log_file:
        try:
            # Ensure log directory exists
            log_dir = str(project_root)+"/logs"

            if not os.path.exists(log_dir):
                os.makedirs(log_dir, exist_ok=True) 
            
            file_handler = logging.FileHandler(log_dir+"/" + log_file, encoding='utf-8')
            file_handler.setLevel(getattr(logging, log_level.upper(), logging.INFO))
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
            
        except Exception as e:
            logger.warning(f"Could not set up file logging: {e}")
    
    return logger


class ProgressTracker:
    """
    Tracks and reports progress of analysis operations.
    """
    
    def __init__(self):
        self.current_file = None
        self.start_time = None
        self.progress_history = []
        self.current_progress = 0
        self.current_stage = "Initializing"
        self.statistics = {
            'total_analyses': 0,
            'successful_analyses': 0,
            'failed_analyses': 0,
            'total_processing_time': 0.0,
            'average_processing_time': 0.0
        }
    
    def start_analysis(self, file_path: str) -> None:
        """Start tracking analysis for a new file."""
        self.current_file = file_path
        self.start_time = datetime.now()
        self.current_progress = 0
        self.current_stage = "Starting analysis"
        self.progress_history = []
        
        print(f"\nStarting analysis: {os.path.basename(file_path)}")
        print("Progress: [" + " " * 40 + "] 0%")
    
    def update_progress(self, stage: str, progress: int) -> None:
        """
        Update current progress.
        
        Args:
            stage (str): Current stage description
            progress (int): Progress percentage (0-100)
        """
        self.current_stage = stage
        self.current_progress = min(100, max(0, progress))
        
        # Record progress history
        self.progress_history.append({
            'timestamp': datetime.now(),
            'stage': stage,
            'progress': progress
        })
        
        # Display progress bar
        filled = int(40 * progress / 100)
        bar = "█" * filled + "░" * (40 - filled)
        
        # Move cursor up and overwrite previous line
        print(f"\r{stage}")
        print(f"Progress: [{bar}] {progress}%")
        
        # Move cursor up to overwrite on next update
        if progress < 100:
            print("\033[2A", end="")
    
    def complete_analysis(self) -> None:
        """Complete the current analysis tracking."""
        if self.start_time:
            end_time = datetime.now()
            duration = (end_time - self.start_time).total_seconds()
            
            self.statistics['total_analyses'] += 1
            self.statistics['total_processing_time'] += duration
            self.statistics['average_processing_time'] = (
                self.statistics['total_processing_time'] / self.statistics['total_analyses']
            )
            
            if self.current_progress >= 100:
                self.statistics['successful_analyses'] += 1
                print(f"\n Analysis completed in {duration:.1f} seconds")
            else:
                self.statistics['failed_analyses'] += 1
                print(f"\n Analysis incomplete")
        
        # Reset state
        self.current_file = None
        self.start_time = None
        self.current_progress = 0
        self.current_stage = "Idle"
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get progress tracking statistics."""
        return self.statistics.copy()
    
    def get_current_status(self) -> Dict[str, Any]:
        """Get current progress status."""
        return {
            'current_file': self.current_file,
            'current_stage': self.current_stage,
            'current_progress': self.current_progress,
            'elapsed_time': (datetime.now() - self.start_time).total_seconds() if self.start_time else 0
        }


class MemoryMonitor:
    """
    Monitors memory usage during analysis operations.
    """
    
    def __init__(self, max_memory_mb: int = 1024):
        self.max_memory_mb = max_memory_mb
        self.process = psutil.Process()
        self.memory_history = []
        self.peak_memory_mb = 0
        self.warnings_issued = []
    
    def check_memory(self, checkpoint: str = "unknown") -> Dict[str, Any]:
        """
        Check current memory usage and issue warnings if needed.
        
        Args:
            checkpoint (str): Description of current checkpoint
            
        Returns:
            Dict[str, Any]: Memory usage information
        """
        try:
            memory_info = self.process.memory_info()
            memory_mb = memory_info.rss / (1024 * 1024)  # Convert to MB
            memory_percent = self.process.memory_percent()
            
            # Update peak memory
            self.peak_memory_mb = max(self.peak_memory_mb, memory_mb)
            
            # Record memory usage
            memory_record = {
                'timestamp': datetime.now(),
                'checkpoint': checkpoint,
                'memory_mb': round(memory_mb, 2),
                'memory_percent': round(memory_percent, 2),
                'virtual_memory_mb': round(memory_info.vms / (1024 * 1024), 2)
            }
            
            self.memory_history.append(memory_record)
            
            # Issue warnings if needed
            if memory_mb > self.max_memory_mb * 0.8:  # 80% threshold
                warning_msg = f"High memory usage at {checkpoint}: {memory_mb:.1f}MB"
                if warning_msg not in self.warnings_issued:
                    logging.getLogger('CSVQualityAnalyzer').warning(warning_msg)
                    self.warnings_issued.append(warning_msg)
            
            if memory_mb > self.max_memory_mb:  # Exceeded limit
                raise MemoryError(f"Memory limit exceeded at {checkpoint}: {memory_mb:.1f}MB > {self.max_memory_mb}MB")
            
            return memory_record
            
        except Exception as e:
            logging.getLogger('CSVQualityAnalyzer').error(f"Error checking memory: {e}")
            return {'error': str(e), 'checkpoint': checkpoint}
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get memory usage statistics."""
        if not self.memory_history:
            return {'no_data': True}
        
        memory_values = [record['memory_mb'] for record in self.memory_history if 'memory_mb' in record]
        
        if not memory_values:
            return {'no_valid_data': True}
        
        return {
            'peak_memory_mb': round(self.peak_memory_mb, 2),
            'average_memory_mb': round(sum(memory_values) / len(memory_values), 2),
            'min_memory_mb': round(min(memory_values), 2),
            'max_memory_mb': round(max(memory_values), 2),
            'memory_limit_mb': self.max_memory_mb,
            'checkpoints_monitored': len(self.memory_history),
            'warnings_issued': len(self.warnings_issued),
            'limit_exceeded': self.peak_memory_mb > self.max_memory_mb
        }
    
    def optimize_memory(self) -> None:
        """Perform garbage collection and memory optimization."""
        import gc
        gc.collect()
        
        # Force garbage collection multiple times for better cleanup
        for _ in range(3):
            gc.collect()


class ErrorHandler:
    """
    Handles and logs errors with context information.
    """
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.error_history = []
        self.error_counts = {}
    
    def handle_error(self, error: Exception, context: str = None, 
                    raise_error: bool = False) -> Dict[str, Any]:
        """
        Handle an error with logging and tracking.
        
        Args:
            error (Exception): The error that occurred
            context (str): Additional context information
            raise_error (bool): Whether to re-raise the error
            
        Returns:
            Dict[str, Any]: Error information
        """
        error_type = type(error).__name__
        error_message = str(error)
        
        # Track error counts
        self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1
        
        # Create error record
        error_record = {
            'timestamp': datetime.now().isoformat(),
            'error_type': error_type,
            'error_message': error_message,
            'context': context,
            'traceback': traceback.format_exc(),
            'occurrence_count': self.error_counts[error_type]
        }
        
        self.error_history.append(error_record)
        
        # Log error
        log_message = f"Error in {context}: {error_type}: {error_message}" if context else f"{error_type}: {error_message}"
        self.logger.error(log_message)
        self.logger.debug(f"Full traceback: {error_record['traceback']}")
        
        # Re-raise if requested
        if raise_error:
            raise error
        
        return error_record
    
    def get_error_summary(self) -> Dict[str, Any]:
        """Get summary of all errors encountered."""
        return {
            'total_errors': len(self.error_history),
            'error_types': dict(self.error_counts),
            'recent_errors': self.error_history[-5:] if self.error_history else [],
            'most_common_error': max(self.error_counts.items(), key=lambda x: x[1])[0] if self.error_counts else None
        }
    
    def clear_history(self) -> None:
        """Clear error history."""
        self.error_history.clear()
        self.error_counts.clear()


class FileValidator:
    """
    Validates files and file paths for analysis.
    """
    
    @staticmethod
    def validate_csv_file(file_path: str) -> Dict[str, Any]:
        """
        Validate a CSV file for analysis readiness.
        
        Args:
            file_path (str): Path to the file
            
        Returns:
            Dict[str, Any]: Validation results
        """
        validation_results = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'file_info': {}
        }
        
        try:
            # Check if file exists
            if not os.path.exists(file_path):
                validation_results['is_valid'] = False
                validation_results['errors'].append(f"File does not exist: {file_path}")
                return validation_results
            
            # Check if it's a file (not directory)
            if not os.path.isfile(file_path):
                validation_results['is_valid'] = False
                validation_results['errors'].append(f"Path is not a file: {file_path}")
                return validation_results
            
            # Check file permissions
            if not os.access(file_path, os.R_OK):
                validation_results['is_valid'] = False
                validation_results['errors'].append(f"File is not readable: {file_path}")
                return validation_results
            
            # Get file information
            file_stats = os.stat(file_path)
            file_size = file_stats.st_size
            
            validation_results['file_info'] = {
                'file_path': file_path,
                'file_name': os.path.basename(file_path),
                'file_size_bytes': file_size,
                'file_size_mb': round(file_size / (1024 * 1024), 2),
                'last_modified': datetime.fromtimestamp(file_stats.st_mtime).isoformat(),
                'is_readable': True
            }
            
            # Check file size
            if file_size == 0:
                validation_results['is_valid'] = False
                validation_results['errors'].append("File is empty")
                return validation_results
            
            # Check file extension
            file_ext = os.path.splitext(file_path)[1].lower()
            valid_extensions = ['.csv', '.txt', '.tsv']
            
            if file_ext not in valid_extensions:
                validation_results['warnings'].append(
                    f"File extension '{file_ext}' may not be a CSV file. Expected: {valid_extensions}"
                )
            
            # Check file size warnings
            if file_size > 100 * 1024 * 1024:  # 100MB
                validation_results['warnings'].append(
                    f"Large file detected ({validation_results['file_info']['file_size_mb']:.1f}MB). "
                    "Consider using chunked processing."
                )
            
            # Try to read first few bytes to check if it's binary
            try:
                with open(file_path, 'rb') as f:
                    first_bytes = f.read(1024)
                    
                # Check for null bytes (indicator of binary file)
                if b'\x00' in first_bytes:
                    validation_results['warnings'].append("File may be binary (contains null bytes)")
                
                # Check for common text encoding
                try:
                    first_bytes.decode('utf-8')
                except UnicodeDecodeError:
                    validation_results['warnings'].append("File may not be UTF-8 encoded")
                    
            except Exception as e:
                validation_results['warnings'].append(f"Could not read file content: {e}")
            
        except Exception as e:
            validation_results['is_valid'] = False
            validation_results['errors'].append(f"Validation error: {str(e)}")
        
        return validation_results
    
    @staticmethod
    def validate_output_directory(output_dir: str) -> Dict[str, Any]:
        """
        Validate output directory for report generation.
        
        Args:
            output_dir (str): Output directory path
            
        Returns:
            Dict[str, Any]: Validation results
        """
        validation_results = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'created_directory': False
        }
        
        try:
            # Try to create directory if it doesn't exist
            if not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)
                validation_results['created_directory'] = True
            
            # Check if it's a directory
            if not os.path.isdir(output_dir):
                validation_results['is_valid'] = False
                validation_results['errors'].append(f"Path is not a directory: {output_dir}")
                return validation_results
            
            # Check write permissions
            if not os.access(output_dir, os.W_OK):
                validation_results['is_valid'] = False
                validation_results['errors'].append(f"Directory is not writable: {output_dir}")
                return validation_results
            
            # Check available space
            try:
                statvfs = os.statvfs(output_dir)
                available_bytes = statvfs.f_frsize * statvfs.f_bavail
                available_mb = available_bytes / (1024 * 1024)
                
                if available_mb < 100:  # Less than 100MB
                    validation_results['warnings'].append(
                        f"Low disk space in output directory: {available_mb:.1f}MB available"
                    )
                    
            except Exception:
                validation_results['warnings'].append("Could not check available disk space")
            
        except Exception as e:
            validation_results['is_valid'] = False
            validation_results['errors'].append(f"Directory validation error: {str(e)}")
        
        return validation_results


class ConfigValidator:
    """
    Validates configuration settings.
    """
    
    @staticmethod
    def validate_config(config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate configuration dictionary.
        
        Args:
            config (Dict[str, Any]): Configuration to validate
            
        Returns:
            Dict[str, Any]: Validation results
        """
        validation_results = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'recommendations': []
        }
        
        # Required sections
        required_sections = ['processing', 'data_quality', 'output', 'validation_rules']
        for section in required_sections:
            if section not in config:
                validation_results['errors'].append(f"Missing required section: {section}")
                validation_results['is_valid'] = False
        
        # Validate processing section
        if 'processing' in config:
            processing = config['processing']
            
            # Chunk size validation
            chunk_size = processing.get('chunk_size', 10000)
            if not isinstance(chunk_size, int) or chunk_size <= 0:
                validation_results['errors'].append("chunk_size must be a positive integer")
                validation_results['is_valid'] = False
            elif chunk_size < 1000:
                validation_results['warnings'].append("chunk_size is very small, may impact performance")
            elif chunk_size > 100000:
                validation_results['warnings'].append("chunk_size is very large, may cause memory issues")
            
            # Sample size validation
            sample_size = processing.get('sample_size', 100000)
            if not isinstance(sample_size, int) or sample_size <= 0:
                validation_results['errors'].append("sample_size must be a positive integer")
                validation_results['is_valid'] = False
            
            # Memory limit validation
            max_memory = processing.get('max_memory_mb', 1024)
            if not isinstance(max_memory, (int, float)) or max_memory <= 0:
                validation_results['errors'].append("max_memory_mb must be a positive number")
                validation_results['is_valid'] = False
        
        # Validate data quality section
        if 'data_quality' in config:
            dq = config['data_quality']
            
            # Threshold validations
            for threshold_name in ['completeness_threshold', 'uniqueness_threshold']:
                threshold = dq.get(threshold_name, 0.95)
                if not isinstance(threshold, (int, float)) or not 0 <= threshold <= 1:
                    validation_results['errors'].append(f"{threshold_name} must be between 0 and 1")
                    validation_results['is_valid'] = False
            
            # Outlier method validation
            outlier_method = dq.get('outlier_method', 'iqr')
            valid_methods = ['iqr', 'zscore', 'modified_zscore']
            if outlier_method not in valid_methods:
                validation_results['errors'].append(f"outlier_method must be one of: {valid_methods}")
                validation_results['is_valid'] = False
        
        # Validate output section
        if 'output' in config:
            output = config['output']
            
            # Report format validation
            report_format = output.get('report_format', 'json')
            valid_formats = ['json', 'html', 'csv', 'all']
            if report_format not in valid_formats:
                validation_results['errors'].append(f"report_format must be one of: {valid_formats}")
                validation_results['is_valid'] = False
            
            # Output directory validation
            output_dir = output.get('output_directory', './reports')
            if not isinstance(output_dir, str):
                validation_results['errors'].append("output_directory must be a string")
                validation_results['is_valid'] = False
        
        # Performance recommendations
        if validation_results['is_valid']:
            # Memory vs chunk size recommendation
            processing = config.get('processing', {})
            chunk_size = processing.get('chunk_size', 10000)
            max_memory = processing.get('max_memory_mb', 1024)
            
            estimated_chunk_memory = chunk_size * 0.01  # Rough estimate: 0.01MB per 1000 rows
            if estimated_chunk_memory > max_memory * 0.5:
                validation_results['recommendations'].append(
                    f"Consider reducing chunk_size ({chunk_size}) for max_memory_mb ({max_memory})"
                )
        
        return validation_results


def format_bytes(bytes_value: int) -> str:
    """
    Format bytes into human readable format.
    
    Args:
        bytes_value (int): Number of bytes
        
    Returns:
        str: Formatted string (e.g., "1.5 GB")
    """
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if bytes_value < 1024.0:
            return f"{bytes_value:.1f} {unit}"
        bytes_value /= 1024.0
    return f"{bytes_value:.1f} PB"


def format_duration(seconds: float) -> str:
    """
    Format duration in seconds into human readable format.
    
    Args:
        seconds (float): Duration in seconds
        
    Returns:
        str: Formatted string (e.g., "2m 30s")
    """
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        remaining_seconds = seconds % 60
        return f"{minutes}m {remaining_seconds:.0f}s"
    else:
        hours = int(seconds // 3600)
        remaining_minutes = int((seconds % 3600) // 60)
        return f"{hours}h {remaining_minutes}m"


def create_timestamp() -> str:
    """Create a timestamp string for filenames."""
    return datetime.now().strftime("%Y%m%d_%H%M%S")


def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """
    Safely divide two numbers, returning default if denominator is zero.
    
    Args:
        numerator (float): Numerator
        denominator (float): Denominator
        default (float): Default value if division by zero
        
    Returns:
        float: Result of division or default value
    """
    return numerator / denominator if denominator != 0 else default


def truncate_text(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """
    Truncate text to specified length with suffix.
    
    Args:
        text (str): Text to truncate
        max_length (int): Maximum length
        suffix (str): Suffix to add if truncated
        
    Returns:
        str: Truncated text
    """
    if len(text) <= max_length:
        return text
    return text[:max_length - len(suffix)] + suffix


def ensure_directory(directory_path: str) -> bool:
    """
    Ensure directory exists, create if necessary.
    
    Args:
        directory_path (str): Directory path
        
    Returns:
        bool: True if directory exists or was created successfully
    """
    try:
        os.makedirs(directory_path, exist_ok=True)
        return True
    except Exception:
        return False


if __name__ == "__main__":
    # Test utilities
    print("Testing utility functions...")
    
    # Test progress tracker
    tracker = ProgressTracker()
    tracker.start_analysis("test.csv")
    tracker.update_progress("Loading data", 25)
    tracker.update_progress("Analyzing", 50)
    tracker.update_progress("Generating report", 75)
    tracker.update_progress("Complete", 100)
    tracker.complete_analysis()
    
    print(f"Statistics: {tracker.get_statistics()}")
    
    # Test memory monitor
    monitor = MemoryMonitor()
    memory_info = monitor.check_memory("test")
    print(f"Memory info: {memory_info}")
    
    # Test file validator
    validation = FileValidator.validate_csv_file("test.csv")
    print(f"File validation: {validation}")
    
    print("Utility tests completed!")