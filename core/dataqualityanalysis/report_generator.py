"""
Report Generator for CSV Quality Analyzer
Generates comprehensive quality analysis reports in multiple formats.
"""

import json
import os
import csv
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path
import pandas as pd
import numpy as np
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).resolve().parent.parent.parent
sys.path.insert(0, str(project_root))


from core.config_manager import ConfigManager


class ReportGenerator:
    """
    Generates comprehensive quality analysis reports in various formats.
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize ReportGenerator with configuration.
        
        Args:
            config_manager (ConfigManager): Configuration manager instance
        """
        self.config = config_manager
        self.output_dir = config_manager.get('output.output_directory', os.path.join(project_root,"reports"))
        self.report_format = config_manager.get('output.report_format', 'json')
        self.include_sample_data = config_manager.get('output.include_sample_data', False)
        self.detailed_metrics = config_manager.get('output.detailed_metrics', True)
        self.report_sections = config_manager.get('output.report_sections', {})
        
        # Ensure output directory exists
        # os.makedirs(self.output_dir, exist_ok=True)
    
    def generate_report(self, file_info: Dict[str, Any], profile: Dict[str, Any], 
                       quality_metrics: Dict[str, Any], file_path: str,
                       sample_data: Optional[pd.DataFrame] = None) -> str:
        """
        Generate comprehensive quality report.
        
        Args:
            file_info (Dict[str, Any]): File information and detection results
            profile (Dict[str, Any]): Data profile results
            quality_metrics (Dict[str, Any]): Quality analysis results
            file_path (str): Original file path
            sample_data (pd.DataFrame, optional): Sample data for inclusion in report
            
        Returns:
            str: Path to generated report file
        """
        print("Generating comprehensive quality report...")
        
        # Ensure quality_metrics has complete missing patterns
        quality_metrics = self._ensure_complete_quality_metrics(quality_metrics, profile)
        
        # Build complete report structure
        report = self._build_report_structure(file_info, profile, quality_metrics, file_path, sample_data)
        
        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_filename = f"quality_report_{timestamp}"
        
        # Generate report in specified format(s)
        report_paths = []
        
        if self.report_format == 'json' or self.report_format == 'all':
            json_path = self._generate_json_report(report, base_filename)
            if json_path:
                report_paths.append(json_path)
        
        if self.report_format == 'html' or self.report_format == 'all':
            html_path = self._generate_html_report(report, base_filename)
            if html_path:
                report_paths.append(html_path)
        
        if self.report_format == 'csv' or self.report_format == 'all':
            csv_path = self._generate_csv_summary(report, base_filename)
            if csv_path:
                report_paths.append(csv_path)
        
        # Return primary report path (first generated)
        return report_paths[0] if report_paths else None
    
    def _ensure_complete_quality_metrics(self, quality_metrics: Dict[str, Any], profile: Dict[str, Any]) -> Dict[str, Any]:
        """Ensure quality_metrics has complete missing patterns data."""
        
        # If completeness section exists but missing_patterns is incomplete, fix it
        if 'completeness' in quality_metrics:
            completeness = quality_metrics['completeness']
            
            # Ensure missing_patterns exists and is complete
            if 'missing_patterns' not in completeness or not completeness.get('missing_patterns'):
                completeness['missing_patterns'] = self._generate_completeness_missing_patterns(profile)
        
        return quality_metrics
    
    def _generate_completeness_missing_patterns(self, profile: Dict[str, Any]) -> Dict[str, Any]:
        """Generate missing patterns for the completeness section."""
        
        columns_info = profile.get('columns', {})
        
        # Common missing patterns analysis
        common_patterns = {}
        row_patterns = {}
        column_patterns = {}
        
        # Analyze patterns by missing percentage ranges
        missing_ranges = {
            'complete': [],           # 0% missing
            'minimal': [],           # 0-5% missing  
            'low': [],               # 5-15% missing
            'moderate': [],          # 15-30% missing
            'high': [],              # 30-60% missing
            'severe': [],            # 60-100% missing
            'empty': []              # 100% missing
        }
        
        for col_name, col_info in columns_info.items():
            missing_pct = col_info.get('null_percentage', 0)
            
            if missing_pct == 0:
                missing_ranges['complete'].append(col_name)
            elif missing_pct <= 5:
                missing_ranges['minimal'].append(col_name)
            elif missing_pct <= 15:
                missing_ranges['low'].append(col_name)
            elif missing_pct <= 30:
                missing_ranges['moderate'].append(col_name)
            elif missing_pct <= 60:
                missing_ranges['high'].append(col_name)
            elif missing_pct < 100:
                missing_ranges['severe'].append(col_name)
            else:
                missing_ranges['empty'].append(col_name)
        
        # Build common patterns
        common_patterns = {
            'by_missing_level': missing_ranges,
            'total_patterns_found': len([col for cols in missing_ranges.values() for col in cols]),
            'most_problematic_columns': missing_ranges['severe'] + missing_ranges['empty'],
            'analysis_summary': {
                'complete_data_columns': len(missing_ranges['complete']),
                'problematic_columns': len(missing_ranges['high']) + len(missing_ranges['severe']) + len(missing_ranges['empty']),
                'acceptable_missing_columns': len(missing_ranges['minimal']) + len(missing_ranges['low']),
                'requires_attention_columns': len(missing_ranges['moderate'])
            }
        }
        
        # Row-level patterns (estimated)
        dataset_info = profile.get('dataset_info', {})
        total_rows = dataset_info.get('row_count', 0)
        total_missing = dataset_info.get('missing_cells', 0)
        total_cells = dataset_info.get('total_cells', 1)
        
        if total_rows > 0:
            avg_missing_per_row = total_missing / total_rows if total_rows > 0 else 0
            
            row_patterns = {
                'estimated_complete_rows': max(0, int(total_rows * 0.7)) if avg_missing_per_row < 1 else max(0, int(total_rows * 0.3)),
                'estimated_partial_missing_rows': max(0, int(total_rows * 0.25)) if avg_missing_per_row < 1 else max(0, int(total_rows * 0.5)),
                'estimated_high_missing_rows': max(0, int(total_rows * 0.05)) if avg_missing_per_row < 1 else max(0, int(total_rows * 0.2)),
                'average_missing_per_row': round(avg_missing_per_row, 2),
                'total_rows_analyzed': total_rows
            }
        
        # Column-specific patterns
        for col_name, col_info in columns_info.items():
            missing_count = col_info.get('null_count', 0)
            total_count = col_info.get('non_null_count', 0) + missing_count
            missing_pct = col_info.get('null_percentage', 0)
            
            if missing_count > 0:  # Only include columns with missing data
                column_patterns[col_name] = {
                    'missing_count': missing_count,
                    'missing_percentage': missing_pct,
                    'total_values': total_count,
                    'data_type': profile.get('column_types', {}).get(col_name, 'unknown'),
                    'severity': self._categorize_missing_level(missing_pct)
                }
        
        return {
            'common_patterns': common_patterns,
            'row_patterns': row_patterns,
            'column_patterns': column_patterns,
            'pattern_summary': {
                'columns_with_missing_data': len(column_patterns),
                'columns_with_no_missing': len(missing_ranges['complete']),
                'most_severe_missing_column': max(column_patterns.keys(), 
                                                 key=lambda x: column_patterns[x]['missing_percentage']) if column_patterns else None,
                'average_missing_percentage': sum(col_info.get('null_percentage', 0) for col_info in columns_info.values()) / len(columns_info) if columns_info else 0
            }
        }
    
    def _build_report_structure(self, file_info: Dict[str, Any], profile: Dict[str, Any],
                               quality_metrics: Dict[str, Any], file_path: str,
                               sample_data: Optional[pd.DataFrame] = None) -> Dict[str, Any]:
        """Build the complete report structure."""
        
        report = {
            'metadata': self._generate_metadata(file_path),
            'executive_summary': self._generate_executive_summary(quality_metrics, profile),
            'file_information': file_info if self.report_sections.get('file_info', True) else None,
            'data_profile': profile if self.report_sections.get('data_profile', True) else None,
            'quality_assessment': quality_metrics if self.report_sections.get('quality_metrics', True) else None,
            'missing_patterns_analysis': self._generate_missing_patterns_analysis(profile, quality_metrics),
            'recommendations': self._generate_recommendations(quality_metrics, profile) if self.report_sections.get('recommendations', True) else None,
            'data_dictionary': self._generate_data_dictionary(profile),
            'quality_scorecard': self._generate_quality_scorecard(quality_metrics),
            'action_plan': self._generate_action_plan(quality_metrics, profile)
        }
        
        # Add sample data if requested and available
        if self.include_sample_data and sample_data is not None:
            report['sample_data'] = self._prepare_sample_data(sample_data)
        
        # Remove None sections
        report = {k: v for k, v in report.items() if v is not None}
        
        return report
    
    def _generate_missing_patterns_analysis(self, profile: Dict[str, Any], 
                                           quality_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive missing patterns analysis."""
        
        missing_patterns = {
            'overall_missing_summary': {},
            'column_missing_patterns': {},
            'row_missing_patterns': {},
            'missing_value_correlations': {},
            'temporal_missing_patterns': {},
            'business_impact_analysis': {},
            'insights': [],
            'recommendations': []
        }
        
        # Overall missing data summary
        dataset_info = profile.get('dataset_info', {})
        total_cells = dataset_info.get('total_cells', 0)
        total_missing = dataset_info.get('missing_cells', 0)
        
        missing_patterns['overall_missing_summary'] = {
            'total_cells': total_cells,
            'total_missing_cells': total_missing,
            'overall_missing_percentage': round((total_missing / total_cells * 100) if total_cells > 0 else 0, 2),
            'completely_empty_columns': [],
            'mostly_missing_columns': [],  # >50% missing
            'sparse_columns': [],  # 10-50% missing
            'complete_columns': []  # <10% missing
        }
        
        # Column-wise missing patterns
        columns_info = profile.get('columns', {})
        for col_name, col_info in columns_info.items():
            null_count = col_info.get('null_count', 0)
            total_count = col_info.get('non_null_count', 0) + null_count
            missing_pct = (null_count / total_count * 100) if total_count > 0 else 0
            
            col_missing_info = {
                'column_name': col_name,
                'missing_count': null_count,
                'total_count': total_count,
                'missing_percentage': round(missing_pct, 2),
                'missing_category': self._categorize_missing_level(missing_pct),
                'data_type': profile.get('column_types', {}).get(col_name, 'unknown'),
                'potential_causes': self._identify_missing_causes(col_name, col_info, missing_pct),
                'impact_level': self._assess_missing_impact(missing_pct, col_name),
                'recommended_action': self._recommend_missing_action(missing_pct, col_name)
            }
            
            missing_patterns['column_missing_patterns'][col_name] = col_missing_info
            
            # Categorize columns by missing level
            if missing_pct == 100:
                missing_patterns['overall_missing_summary']['completely_empty_columns'].append(col_name)
            elif missing_pct > 50:
                missing_patterns['overall_missing_summary']['mostly_missing_columns'].append(col_name)
            elif missing_pct > 10:
                missing_patterns['overall_missing_summary']['sparse_columns'].append(col_name)
            else:
                missing_patterns['overall_missing_summary']['complete_columns'].append(col_name)
        
        # Row-level missing patterns analysis
        missing_patterns['row_missing_patterns'] = self._analyze_row_missing_patterns(profile)
        
        # Business impact analysis
        missing_patterns['business_impact_analysis'] = self._analyze_business_impact(missing_patterns)
        
        # Generate missing data insights
        missing_patterns['insights'] = self._generate_missing_data_insights(missing_patterns)
        
        # Generate missing data recommendations
        missing_patterns['recommendations'] = self._generate_missing_data_recommendations(missing_patterns)
        
        return missing_patterns

    def _categorize_missing_level(self, missing_percentage: float) -> str:
        """Categorize the level of missingness."""
        if missing_percentage == 0:
            return 'complete'
        elif missing_percentage < 5:
            return 'minimal'
        elif missing_percentage < 15:
            return 'low'
        elif missing_percentage < 30:
            return 'moderate'
        elif missing_percentage < 60:
            return 'high'
        elif missing_percentage < 100:
            return 'severe'
        else:
            return 'empty'

    def _identify_missing_causes(self, col_name: str, col_info: Dict[str, Any], 
                               missing_pct: float) -> List[str]:
        """Identify potential causes for missing data in a column."""
        causes = []
        
        # Data type specific causes
        data_type = str(col_info.get('dtype', '')).lower()
        
        if 'float' in data_type and missing_pct > 20:
            causes.append("Possible data type conversion issues")
        
        if 'object' in data_type and missing_pct > 30:
            causes.append("Possible inconsistent text formatting")
        
        # Column name based inference
        col_name_lower = col_name.lower()
        
        if any(word in col_name_lower for word in ['optional', 'comment', 'note', 'remark']):
            causes.append("Optional field - missing by design")
        
        if any(word in col_name_lower for word in ['phone', 'email', 'address', 'contact']):
            causes.append("Contact information - privacy or availability issues")
        
        if any(word in col_name_lower for word in ['date', 'time', 'created', 'updated', 'timestamp']):
            causes.append("Timestamp issues - system or process problems")
        
        if any(word in col_name_lower for word in ['id', 'key', 'identifier']):
            causes.append("Primary key issues - data integrity problems")
        
        if any(word in col_name_lower for word in ['description', 'desc', 'details']):
            causes.append("Descriptive field - may be optional")
        
        # Missing percentage based causes
        if missing_pct > 80:
            causes.append("Column may be newly added or deprecated")
        elif missing_pct > 50:
            causes.append("Systematic data collection issue")
        elif missing_pct > 20:
            causes.append("Inconsistent data entry practices")
        elif missing_pct > 5:
            causes.append("Minor data collection gaps")
        
        return causes if causes else ["Unknown - requires investigation"]

    def _assess_missing_impact(self, missing_pct: float, col_name: str) -> str:
        """Assess the impact level of missing data."""
        col_name_lower = col_name.lower()
        
        # Critical fields
        if any(word in col_name_lower for word in ['id', 'key', 'primary']):
            return 'critical' if missing_pct > 0 else 'minimal'
        
        # Important business fields
        if any(word in col_name_lower for word in ['revenue', 'sales', 'amount', 'quantity', 'value']):
            if missing_pct > 10:
                return 'high'
            elif missing_pct > 5:
                return 'medium'
            else:
                return 'low'
        
        # General impact based on percentage
        if missing_pct > 50:
            return 'high'
        elif missing_pct > 20:
            return 'medium'
        elif missing_pct > 5:
            return 'low'
        else:
            return 'minimal'

    def _recommend_missing_action(self, missing_pct: float, col_name: str) -> str:
        """Recommend action for missing data."""
        col_name_lower = col_name.lower()
        
        if missing_pct == 100:
            return "Consider removing column or investigate data source"
        elif missing_pct > 80:
            return "Investigate data collection process immediately"
        elif missing_pct > 50:
            return "Review data validation rules and collection process"
        elif missing_pct > 20:
            return "Consider imputation or flag missing values"
        elif missing_pct > 5:
            return "Monitor missing data trends"
        else:
            return "No action required - acceptable missing rate"

    def _analyze_row_missing_patterns(self, profile: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze missing data patterns at the row level."""
        dataset_info = profile.get('dataset_info', {})
        total_rows = dataset_info.get('row_count', 0)
        
        if total_rows == 0:
            return {'error': 'No row data available for analysis'}
        
        # Estimate row-level missing patterns
        columns_info = profile.get('columns', {})
        total_columns = len(columns_info)
        
        # Calculate estimated row patterns
        row_patterns = {
            'total_rows': total_rows,
            'total_columns': total_columns,
            'estimated_complete_rows': 0,
            'estimated_partially_missing_rows': 0,
            'estimated_mostly_missing_rows': 0,
            'average_missing_per_row': 0,
            'pattern_analysis': {
                'rows_with_no_missing': 'estimated',
                'rows_with_few_missing': 'estimated (1-25% fields missing)',
                'rows_with_many_missing': 'estimated (25-75% fields missing)',
                'rows_with_most_missing': 'estimated (75%+ fields missing)'
            }
        }
        
        # Estimate based on column-level statistics
        total_missing_cells = sum(col.get('null_count', 0) for col in columns_info.values())
        if total_rows > 0 and total_columns > 0:
            avg_missing_per_row = total_missing_cells / total_rows
            row_patterns['average_missing_per_row'] = round(avg_missing_per_row, 2)
            
            # Rough estimation of row completeness patterns
            if avg_missing_per_row < 0.5:
                row_patterns['estimated_complete_rows'] = int(total_rows * 0.8)
                row_patterns['estimated_partially_missing_rows'] = int(total_rows * 0.2)
            elif avg_missing_per_row < 2:
                row_patterns['estimated_complete_rows'] = int(total_rows * 0.6)
                row_patterns['estimated_partially_missing_rows'] = int(total_rows * 0.4)
            else:
                row_patterns['estimated_complete_rows'] = int(total_rows * 0.3)
                row_patterns['estimated_partially_missing_rows'] = int(total_rows * 0.5)
                row_patterns['estimated_mostly_missing_rows'] = int(total_rows * 0.2)
        
        return row_patterns

    def _analyze_business_impact(self, missing_patterns: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze business impact of missing data patterns."""
        
        column_patterns = missing_patterns.get('column_missing_patterns', {})
        overall_summary = missing_patterns.get('overall_missing_summary', {})
        
        # Categorize columns by business impact
        critical_impact_columns = []
        high_impact_columns = []
        medium_impact_columns = []
        low_impact_columns = []
        
        for col_name, col_info in column_patterns.items():
            impact = col_info.get('impact_level', 'low')
            if impact == 'critical':
                critical_impact_columns.append(col_name)
            elif impact == 'high':
                high_impact_columns.append(col_name)
            elif impact == 'medium':
                medium_impact_columns.append(col_name)
            else:
                low_impact_columns.append(col_name)
        
        # Calculate business risk score
        total_columns = len(column_patterns)
        if total_columns > 0:
            risk_score = (
                len(critical_impact_columns) * 4 +
                len(high_impact_columns) * 3 +
                len(medium_impact_columns) * 2 +
                len(low_impact_columns) * 1
            ) / (total_columns * 4) * 100
        else:
            risk_score = 0
        
        return {
            'business_risk_score': round(risk_score, 1),
            'risk_level': 'High' if risk_score > 70 else 'Medium' if risk_score > 40 else 'Low',
            'critical_impact_columns': critical_impact_columns,
            'high_impact_columns': high_impact_columns,
            'medium_impact_columns': medium_impact_columns,
            'low_impact_columns': low_impact_columns,
            'impact_summary': {
                'total_columns_analyzed': total_columns,
                'columns_requiring_immediate_attention': len(critical_impact_columns) + len(high_impact_columns),
                'business_continuity_risk': 'High' if len(critical_impact_columns) > 0 else 'Medium' if len(high_impact_columns) > 3 else 'Low'
            }
        }

    def _generate_missing_data_insights(self, missing_patterns: Dict[str, Any]) -> List[str]:
        """Generate insights about missing data patterns."""
        insights = []
        
        overall = missing_patterns['overall_missing_summary']
        business_impact = missing_patterns.get('business_impact_analysis', {})
        
        # Overall insights
        overall_missing_pct = overall.get('overall_missing_percentage', 0)
        if overall_missing_pct > 20:
            insights.append(f"High overall missing data rate ({overall_missing_pct:.1f}%) indicates systematic data quality issues")
        elif overall_missing_pct > 10:
            insights.append(f"Moderate missing data rate ({overall_missing_pct:.1f}%) requires attention")
        elif overall_missing_pct > 5:
            insights.append(f"Low-moderate missing data rate ({overall_missing_pct:.1f}%) - monitor trends")
        else:
            insights.append(f"Low missing data rate ({overall_missing_pct:.1f}%) indicates good data collection practices")
        
        # Column-specific insights
        empty_cols = len(overall.get('completely_empty_columns', []))
        if empty_cols > 0:
            insights.append(f"{empty_cols} completely empty columns detected - consider removal or investigation")
        
        mostly_missing = len(overall.get('mostly_missing_columns', []))
        if mostly_missing > 0:
            insights.append(f"{mostly_missing} columns have >50% missing data - high impact on analysis")
        
        sparse_cols = len(overall.get('sparse_columns', []))
        if sparse_cols > 0:
            insights.append(f"{sparse_cols} columns have 10-50% missing data - moderate impact on analysis")
        
        complete_cols = len(overall.get('complete_columns', []))
        if complete_cols > 0:
            insights.append(f"{complete_cols} columns have <10% missing data - good quality for analysis")
        
        # Business impact insights
        risk_level = business_impact.get('risk_level', 'Unknown')
        if risk_level == 'High':
            insights.append("High business risk due to missing data in critical columns")
        elif risk_level == 'Medium':
            insights.append("Medium business risk - some important data missing")
        
        # Pattern insights
        column_patterns = missing_patterns.get('column_missing_patterns', {})
        optional_fields = [col for col, info in column_patterns.items() 
                          if 'Optional field' in str(info.get('potential_causes', []))]
        
        if optional_fields:
            insights.append(f"{len(optional_fields)} fields appear to be optional by design")
        
        # System issues
        system_issues = [col for col, info in column_patterns.items() 
                        if 'system' in str(info.get('potential_causes', [])).lower()]
        if system_issues:
            insights.append(f"{len(system_issues)} columns show signs of system-related missing data issues")
        
        return insights

    def _generate_missing_data_recommendations(self, missing_patterns: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate specific recommendations for handling missing data."""
        recommendations = []
        
        overall = missing_patterns['overall_missing_summary']
        column_patterns = missing_patterns.get('column_missing_patterns', {})
        business_impact = missing_patterns.get('business_impact_analysis', {})
        
        # Critical impact columns
        critical_cols = business_impact.get('critical_impact_columns', [])
        if critical_cols:
            recommendations.append({
                'priority': 'Critical',
                'category': 'Data Integrity',
                'issue': f"Missing data in {len(critical_cols)} critical business columns",
                'recommendation': "Immediate action required - investigate data source and implement fixes",
                'affected_columns': critical_cols,
                'action': "Stop analysis until critical missing data is resolved",
                'estimated_effort': 'High',
                'timeline': 'Immediate (1-3 days)'
            })
        
        # Completely empty columns
        empty_cols = overall.get('completely_empty_columns', [])
        if empty_cols:
            recommendations.append({
                'priority': 'High',
                'category': 'Data Cleanup',
                'issue': f"{len(empty_cols)} completely empty columns",
                'recommendation': "Remove empty columns or investigate data source issues",
                'affected_columns': empty_cols,
                'action': "Consider dropping these columns unless they serve a future purpose",
                'estimated_effort': 'Low',
                'timeline': '1-2 days'
            })
        
        # Mostly missing columns
        mostly_missing = overall.get('mostly_missing_columns', [])
        if mostly_missing:
            recommendations.append({
                'priority': 'High',
                'category': 'Data Quality',
                'issue': f"{len(mostly_missing)} columns with >50% missing data",
                'recommendation': "Investigate root causes and implement data collection improvements",
                'affected_columns': mostly_missing,
                'action': "Review data collection processes for these critical fields",
                'estimated_effort': 'High',
                'timeline': '1-2 weeks'
            })
        
        # High impact missing data
        high_impact_cols = business_impact.get('high_impact_columns', [])
        if high_impact_cols:
            recommendations.append({
                'priority': 'High',
                'category': 'Business Impact',
                'issue': f"High business impact missing data in {len(high_impact_cols)} columns",
                'recommendation': "Prioritize data collection for business-critical fields",
                'affected_columns': high_impact_cols,
                'action': "Implement immediate data collection improvements for critical business fields",
                'estimated_effort': 'High',
                'timeline': '1-2 weeks'
            })
        
        # Moderate missing data
        sparse_cols = overall.get('sparse_columns', [])
        if sparse_cols:
            recommendations.append({
                'priority': 'Medium',
                'category': 'Data Imputation',
                'issue': f"{len(sparse_cols)} columns with 10-50% missing data",
                'recommendation': "Consider imputation strategies or flag missing values",
                'affected_columns': sparse_cols,
                'action': "Evaluate business rules for handling missing values in these fields",
                'estimated_effort': 'Medium',
                'timeline': '2-4 weeks'
            })
        
        # Contact information specific
        contact_cols = [col for col, info in column_patterns.items() 
                       if any('Contact information' in str(cause) for cause in info.get('potential_causes', []))]
        if contact_cols:
            recommendations.append({
                'priority': 'Medium',
                'category': 'Business Process',
                'issue': f"Missing contact information in {len(contact_cols)} columns",
                'recommendation': "Implement data collection incentives or make fields required",
                'affected_columns': contact_cols,
                'action': "Review privacy policies and data collection practices",
                'estimated_effort': 'Medium',
                'timeline': '2-3 weeks'
            })
        
        # System/timestamp issues
        timestamp_cols = [col for col, info in column_patterns.items() 
                         if any('Timestamp issues' in str(cause) for cause in info.get('potential_causes', []))]
        if timestamp_cols:
            recommendations.append({
                'priority': 'Medium',
                'category': 'System Issues',
                'issue': f"Timestamp/system issues in {len(timestamp_cols)} columns",
                'recommendation': "Review automated timestamp generation and system processes",
                'affected_columns': timestamp_cols,
                'action': "Audit system processes and implement automatic timestamp generation",
                'estimated_effort': 'Medium',
                'timeline': '2-4 weeks'
            })
        
        # Data type issues
        dtype_cols = [col for col, info in column_patterns.items() 
                     if any('data type conversion' in str(cause).lower() for cause in info.get('potential_causes', []))]
        if dtype_cols:
            recommendations.append({
                'priority': 'Medium',
                'category': 'Data Processing',
                'issue': f"Data type conversion issues in {len(dtype_cols)} columns",
                'recommendation': "Review data parsing and type conversion processes",
                'affected_columns': dtype_cols,
                'action': "Implement robust data type validation and conversion",
                'estimated_effort': 'Medium',
                'timeline': '1-3 weeks'
            })
        
        return recommendations
    
    def _generate_metadata(self, file_path: str) -> Dict[str, Any]:
        """Generate report metadata."""
        return {
            'report_generated_at': datetime.now().isoformat(),
            'analyzer_version': '1.0.0',
            'source_file': file_path,
            'source_file_name': os.path.basename(file_path),
            'report_configuration': {
                'detailed_metrics': self.detailed_metrics,
                'include_sample_data': self.include_sample_data,
                'report_format': self.report_format,
                'sections_included': self.report_sections
            }
        }
    
    def _generate_executive_summary(self, quality_metrics: Dict[str, Any], 
                                   profile: Dict[str, Any]) -> Dict[str, Any]:
        """Generate executive summary of the analysis."""
        overall_score = quality_metrics.get('overall_score', 0)
        dataset_info = profile.get('dataset_info', {})
        
        # Determine overall quality level
        if overall_score >= 0.9:
            quality_level = 'Excellent'
            quality_description = 'Data quality is excellent with minimal issues.'
        elif overall_score >= 0.8:
            quality_level = 'Good'
            quality_description = 'Data quality is good with some minor issues to address.'
        elif overall_score >= 0.7:
            quality_level = 'Fair'
            quality_description = 'Data quality is fair with several issues requiring attention.'
        elif overall_score >= 0.6:
            quality_level = 'Poor'
            quality_description = 'Data quality is poor with significant issues that need immediate attention.'
        else:
            quality_level = 'Critical'
            quality_description = 'Data quality is critical with severe issues that may impact analysis reliability.'
        
        # Key findings
        key_findings = []
        
        # Completeness findings
        completeness = quality_metrics.get('completeness', {})
        if completeness.get('ratio', 1.0) < 0.9:
            key_findings.append(f"Data completeness is {completeness.get('ratio', 0):.1%} - below recommended threshold")
        
        # Uniqueness findings
        uniqueness = quality_metrics.get('uniqueness', {})
        duplicate_count = uniqueness.get('duplicate_rows', 0)
        if duplicate_count > 0:
            key_findings.append(f"{duplicate_count:,} duplicate records found ({uniqueness.get('duplicate_percentage', 0):.1f}%)")
        
        # Outlier findings
        outliers = quality_metrics.get('outliers', {})
        if isinstance(outliers, dict):
            high_outlier_cols = [col for col, info in outliers.items() 
                               if isinstance(info, dict) and info.get('percentage', 0) > 5]
            if high_outlier_cols:
                key_findings.append(f"High outlier rates detected in {len(high_outlier_cols)} columns")
        
        # Validity findings
        validity = quality_metrics.get('validity', {})
        if validity.get('overall_validity_ratio', 1.0) < 0.95:
            key_findings.append(f"Data validity is {validity.get('overall_validity_ratio', 0):.1%} - some format/type issues detected")
        
        return {
            'overall_quality_score': round(overall_score * 100, 1),
            'quality_level': quality_level,
            'quality_description': quality_description,
            'dataset_overview': {
                'total_records': dataset_info.get('row_count', 0),
                'total_fields': dataset_info.get('column_count', 0),
                'missing_data_percentage': round(dataset_info.get('missing_percentage', 0), 2),
                'memory_usage_mb': round(dataset_info.get('memory_usage_mb', 0), 2)
            },
            'key_findings': key_findings,
            'priority_actions_needed': len(key_findings) > 2
        }
    
    def _generate_recommendations(self, quality_metrics: Dict[str, Any], 
                                 profile: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate actionable recommendations based on analysis."""
        recommendations = []
        
        # Completeness recommendations
        completeness = quality_metrics.get('completeness', {})
        if completeness.get('ratio', 1.0) < self.config.get('data_quality.completeness_threshold', 0.95):
            missing_pct = (1 - completeness.get('ratio', 1.0)) * 100
            recommendations.append({
                'category': 'Data Completeness',
                'priority': 'High' if missing_pct > 20 else 'Medium',
                'issue': f"Dataset has {missing_pct:.1f}% missing data",
                'recommendation': "Investigate missing data patterns and implement data validation at source",
                'actions': [
                    "Review data collection processes",
                    "Implement required field validation",
                    "Consider imputation strategies for critical fields",
                    "Set up monitoring for data completeness trends"
                ],
                'impact': 'Improves analysis reliability and reduces bias from missing data'
            })
        
        # Uniqueness recommendations
        uniqueness = quality_metrics.get('uniqueness', {})
        duplicate_pct = uniqueness.get('duplicate_percentage', 0)
        if duplicate_pct > 1:
            recommendations.append({
                'category': 'Data Uniqueness',
                'priority': 'High' if duplicate_pct > 10 else 'Medium',
                'issue': f"Found {uniqueness.get('duplicate_rows', 0):,} duplicate records ({duplicate_pct:.1f}%)",
                'recommendation': "Implement deduplication processes and prevent duplicate entry",
                'actions': [
                    "Identify root cause of duplicates",
                    "Implement unique constraints at database level",
                    "Create deduplication procedures",
                    "Train users on proper data entry"
                ],
                'impact': 'Reduces data redundancy and improves storage efficiency'
            })
        
        # Outlier recommendations
        outliers = quality_metrics.get('outliers', {})
        if isinstance(outliers, dict):
            for col, outlier_info in outliers.items():
                if isinstance(outlier_info, dict):
                    outlier_pct = outlier_info.get('percentage', 0)
                    if outlier_pct > 5:
                        recommendations.append({
                            'category': 'Data Accuracy',
                            'priority': 'Medium',
                            'issue': f"Column '{col}' has {outlier_info.get('count', 0)} outliers ({outlier_pct:.1f}%)",
                            'recommendation': f"Review and validate outlier values in '{col}' column",
                            'actions': [
                                f"Manually review outlier values in {col}",
                                "Implement business rules for acceptable value ranges",
                                "Set up alerts for extreme values",
                                "Consider data entry training if errors are systematic"
                            ],
                            'impact': 'Improves data accuracy and analysis reliability'
                        })
        
        # Validity recommendations
        validity = quality_metrics.get('validity', {})
        column_validity = validity.get('column_validity', {})
        if isinstance(column_validity, dict):
            for col, col_validity in column_validity.items():
                if isinstance(col_validity, dict):
                    validity_ratio = col_validity.get('validity_ratio', 1.0)
                    if validity_ratio < 0.95:
                        recommendations.append({
                            'category': 'Data Validity',
                            'priority': 'High' if validity_ratio < 0.8 else 'Medium',
                            'issue': f"Column '{col}' has {((1-validity_ratio)*100):.1f}% invalid formats",
                            'recommendation': f"Fix format issues in '{col}' column",
                            'actions': [
                                f"Standardize format for {col} field",
                                "Implement input validation",
                                "Clean existing invalid data",
                                "Document expected formats"
                            ],
                            'impact': 'Ensures data consistency and prevents processing errors'
                        })
        
        # Column-specific recommendations
        column_quality = quality_metrics.get('column_quality', {})
        if isinstance(column_quality, dict):
            for col, col_metrics in column_quality.items():
                if isinstance(col_metrics, dict):
                    quality_score = col_metrics.get('quality_score', 1.0)
                    if quality_score < 0.7:
                        recommendations.append({
                            'category': 'Column Quality',
                            'priority': 'Medium',
                            'issue': f"Column '{col}' has overall quality score of {quality_score:.1%}",
                            'recommendation': f"Comprehensive review needed for '{col}' column",
                            'actions': [
                                f"Analyze specific issues in {col}",
                                "Review business requirements for this field",
                                "Implement targeted improvements",
                                "Monitor quality trends"
                            ],
                            'impact': 'Improves overall dataset quality'
                        })
        
        # Data freshness recommendations
        freshness = quality_metrics.get('data_freshness', {})
        if isinstance(freshness, dict) and freshness.get('has_date_columns', False):
            date_analysis = freshness.get('date_analysis', {})
            if isinstance(date_analysis, dict):
                for col, date_info in date_analysis.items():
                    if isinstance(date_info, dict):
                        freshness_cat = date_info.get('freshness_category', 'unknown')
                        if freshness_cat in ['stale', 'very_stale']:
                            days_old = date_info.get('days_since_latest', 0)
                            recommendations.append({
                                'category': 'Data Freshness',
                                'priority': 'Medium',
                                'issue': f"Data in '{col}' is {days_old} days old",
                                'recommendation': "Update data refresh schedule to maintain currency",
                                'actions': [
                                    "Review data refresh frequency",
                                    "Implement automated data updates",
                                    "Set up freshness monitoring",
                                    "Document data currency requirements"
                                ],
                                'impact': 'Ensures analysis is based on current information'
                            })
        
        # Sort recommendations by priority
        priority_order = {'Critical': 0, 'High': 1, 'Medium': 2, 'Low': 3}
        recommendations.sort(key=lambda x: priority_order.get(x.get('priority', 'Low'), 4))
        
        return recommendations[:10]  # Limit to top 10 recommendations
    
    def _generate_data_dictionary(self, profile: Dict[str, Any]) -> Dict[str, Any]:
        """Generate data dictionary from profile information."""
        data_dictionary = {
            'columns': {},
            'summary': {
                'total_columns': profile.get('dataset_info', {}).get('column_count', 0),
                'column_types': profile.get('column_types', {}),
                'type_distribution': {}
            }
        }
        
        # Count type distribution
        column_types = profile.get('column_types', {})
        type_counts = {}
        for col_type in column_types.values():
            type_counts[col_type] = type_counts.get(col_type, 0) + 1
        data_dictionary['summary']['type_distribution'] = type_counts
        
        # Generate column details
        columns_info = profile.get('columns', {})
        for col_name, col_info in columns_info.items():
            col_type = column_types.get(col_name, 'unknown')
            
            column_detail = {
                'name': col_name,
                'data_type': col_type,
                'pandas_dtype': str(col_info.get('dtype', 'unknown')),
                'completeness': f"{100 - col_info.get('null_percentage', 0):.1f}%",
                'uniqueness': f"{col_info.get('unique_percentage', 0):.1f}%",
                'null_count': col_info.get('null_count', 0),
                'unique_count': col_info.get('unique_count', 0),
                'memory_usage': col_info.get('memory_usage', 0)
            }
            
            # Add type-specific information
            if col_type in ['integer', 'float', 'numeric']:
                stats = {}
                for stat in ['min', 'max', 'mean', 'median', 'std']:
                    val = col_info.get(stat)
                    if val is not None and not (isinstance(val, float) and (np.isnan(val) or np.isinf(val))):
                        stats[stat] = val
                column_detail['statistics'] = stats
                
            elif col_type in ['text', 'categorical']:
                text_stats = {}
                for stat in ['min_length', 'max_length', 'mean_length']:
                    val = col_info.get(stat)
                    if val is not None:
                        text_stats[stat] = val
                most_common = col_info.get('most_common', {})
                if most_common:
                    text_stats['most_common'] = most_common
                column_detail['text_stats'] = text_stats
                
            elif col_type == 'datetime':
                date_stats = {}
                for stat in ['min_date', 'max_date', 'date_range_days']:
                    val = col_info.get(stat)
                    if val is not None:
                        date_stats[stat] = val
                column_detail['date_stats'] = date_stats
            
            data_dictionary['columns'][col_name] = column_detail
        
        return data_dictionary
    
    def _generate_quality_scorecard(self, quality_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Generate quality scorecard with grades and scores."""
        scorecard = {
            'overall_grade': self._score_to_grade(quality_metrics.get('overall_score', 0)),
            'dimension_scores': {},
            'column_scores': {},
            'improvement_areas': []
        }
        
        # Quality dimension scores
        dimensions = {
            'Completeness': quality_metrics.get('completeness', {}).get('ratio', 1.0),
            'Uniqueness': quality_metrics.get('uniqueness', {}).get('ratio', 1.0),
            'Validity': quality_metrics.get('validity', {}).get('overall_validity_ratio', 1.0),
            'Consistency': 0.85,  # Placeholder - would calculate from consistency metrics
            'Accuracy': quality_metrics.get('accuracy', {}).get('domain_accuracy', {}).get('domain_accuracy_score', 1.0) if isinstance(quality_metrics.get('accuracy', {}), dict) else 1.0
        }
        
        for dimension, score in dimensions.items():
            if score is not None and not (isinstance(score, float) and (np.isnan(score) or np.isinf(score))):
                scorecard['dimension_scores'][dimension] = {
                    'score': round(score * 100, 1),
                    'grade': self._score_to_grade(score),
                    'status': 'Pass' if score >= 0.8 else 'Fail'
                }
                
                if score < 0.8:
                    scorecard['improvement_areas'].append(dimension)
        
        # Column-level scores
        column_quality = quality_metrics.get('column_quality', {})
        if isinstance(column_quality, dict):
            for col, col_metrics in column_quality.items():
                if isinstance(col_metrics, dict):
                    quality_score = col_metrics.get('quality_score', 1.0)
                    if quality_score is not None and not (isinstance(quality_score, float) and (np.isnan(quality_score) or np.isinf(quality_score))):
                        scorecard['column_scores'][col] = {
                            'score': round(quality_score * 100, 1),
                            'grade': col_metrics.get('quality_grade', self._score_to_grade(quality_score)),
                            'completeness': round(col_metrics.get('completeness', 1.0) * 100, 1),
                            'uniqueness': round(col_metrics.get('uniqueness', 1.0) * 100, 1)
                        }
        
        return scorecard
    
    def _score_to_grade(self, score: float) -> str:
        """Convert numeric score to letter grade."""
        if score is None or (isinstance(score, float) and (np.isnan(score) or np.isinf(score))):
            return 'F'
        
        if score >= 0.9:
            return 'A'
        elif score >= 0.8:
            return 'B'
        elif score >= 0.7:
            return 'C'
        elif score >= 0.6:
            return 'D'
        else:
            return 'F'
    
    def _generate_action_plan(self, quality_metrics: Dict[str, Any], 
                             profile: Dict[str, Any]) -> Dict[str, Any]:
        """Generate prioritized action plan for quality improvement."""
        action_plan = {
            'immediate_actions': [],
            'short_term_actions': [],
            'long_term_actions': [],
            'estimated_effort': {}
        }
        
        overall_score = quality_metrics.get('overall_score', 0)
        
        # Immediate actions (critical issues)
        if overall_score < 0.6:
            action_plan['immediate_actions'].extend([
                "Halt any critical analysis using this dataset",
                "Perform emergency data quality assessment",
                "Identify and fix critical data issues"
            ])
        
        completeness = quality_metrics.get('completeness', {})
        if completeness.get('ratio', 1.0) < 0.7:
            action_plan['immediate_actions'].append("Address critical missing data issues")
        
        # Short-term actions (1-4 weeks)
        uniqueness = quality_metrics.get('uniqueness', {})
        if uniqueness.get('duplicate_percentage', 0) > 5:
            action_plan['short_term_actions'].append("Implement deduplication process")
        
        validity = quality_metrics.get('validity', {})
        if validity.get('overall_validity_ratio', 1.0) < 0.9:
            action_plan['short_term_actions'].append("Fix data format and validation issues")
        
        action_plan['short_term_actions'].extend([
            "Implement data quality monitoring",
            "Create data quality dashboard",
            "Train team on data quality best practices"
        ])
        
        # Long-term actions (1-6 months)
        action_plan['long_term_actions'].extend([
            "Establish data governance framework",
            "Implement automated data quality checks",
            "Create data quality SLAs",
            "Develop data quality metrics and KPIs",
            "Regular data quality audits"
        ])
        
        # Effort estimation
        action_plan['estimated_effort'] = {
            'immediate_actions': "1-2 weeks",
            'short_term_actions': "2-4 weeks",
            'long_term_actions': "3-6 months",
            'total_estimated_effort': "4-8 months for complete implementation"
        }
        
        return action_plan
    
    def _prepare_sample_data(self, sample_data: pd.DataFrame, max_rows: int = 20) -> Dict[str, Any]:
        """Prepare sample data for inclusion in report."""
        if sample_data is None or sample_data.empty:
            return None
        
        sample_subset = sample_data.head(max_rows)
        
        return {
            'sample_size': len(sample_subset),
            'total_available': len(sample_data),
            'columns': list(sample_subset.columns),
            'data': sample_subset.to_dict('records')
        }
    
    def _generate_json_report(self, report: Dict[str, Any], base_filename: str) -> str:
        """Generate JSON format report with enhanced NaN/infinity handling."""
        filename = f"{base_filename}.json"
        filedir = os.path.join(project_root,self.output_dir)
        filepath = os.path.join(filedir, filename)

        if not os.path.exists(filedir):
            os.makedirs(filedir, exist_ok=True)
        
        try:
            # Clean the report data to be JSON serializable
            clean_report = self._clean_for_json_serialization(report)
            
            # Use custom JSON encoder for better handling
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(clean_report, f, indent=2, default=self._json_serializer, ensure_ascii=False)
            
            print(f"JSON report generated: {filepath}")
            return filepath
        except Exception as e:
            print(f"Error generating JSON report: {e}")
            # Try to save a simplified version
            try:
                simplified_report = {
                    'metadata': report.get('metadata', {}),
                    'executive_summary': report.get('executive_summary', {}),
                    'error': f"Full report generation failed: {str(e)}",
                    'available_sections': list(report.keys())
                }
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(simplified_report, f, indent=2, ensure_ascii=False)
                print(f"Simplified JSON report generated: {filepath}")
                return filepath
            except Exception as e2:
                print(f"Error generating simplified JSON report: {e2}")
                return None
    
    def _clean_for_json_serialization(self, obj: Any) -> Any:
        """Recursively clean data for JSON serialization, handling NaN and infinity values."""
        if obj is None:
            return None
        elif isinstance(obj, dict):
            cleaned = {}
            for key, value in obj.items():
                try:
                    cleaned[str(key)] = self._clean_for_json_serialization(value)
                except Exception:
                    cleaned[str(key)] = str(value)
            return cleaned
        elif isinstance(obj, (list, tuple)):
            cleaned = []
            for item in obj:
                try:
                    cleaned.append(self._clean_for_json_serialization(item))
                except Exception:
                    cleaned.append(str(item))
            return cleaned
        elif pd.isna(obj):
            return None
        elif isinstance(obj, (np.integer, np.int64, np.int32, np.int8, np.int16)):
            return int(obj)
        elif isinstance(obj, (np.floating, np.float64, np.float32, np.float16)):
            if np.isnan(obj):
                return None
            elif np.isinf(obj):
                return "Infinity" if obj > 0 else "-Infinity"
            else:
                return float(obj)
        elif isinstance(obj, np.bool_):
            return bool(obj)
        elif isinstance(obj, np.ndarray):
            return [self._clean_for_json_serialization(item) for item in obj.tolist()]
        elif hasattr(obj, 'isoformat'):  # datetime objects
            return obj.isoformat()
        elif isinstance(obj, (np.complexfloating, complex)):
            return str(obj)
        elif isinstance(obj, set):
            return list(obj)
        elif isinstance(obj, (int, float, str, bool)):
            if isinstance(obj, float) and (np.isnan(obj) or np.isinf(obj)):
                return None if np.isnan(obj) else ("Infinity" if obj > 0 else "-Infinity")
            return obj
        else:
            try:
                # Try to convert to string as fallback
                return str(obj)
            except Exception:
                return None
    
    def _json_serializer(self, obj: Any) -> Any:
        """Custom JSON serializer for numpy and pandas objects."""
        if obj is None:
            return None
        elif pd.isna(obj):
            return None
        elif isinstance(obj, (np.integer, np.int64, np.int32, np.int8, np.int16)):
            return int(obj)
        elif isinstance(obj, (np.floating, np.float64, np.float32, np.float16)):
            if np.isnan(obj):
                return None
            elif np.isinf(obj):
                return "Infinity" if obj > 0 else "-Infinity"
            else:
                return float(obj)
        elif isinstance(obj, np.bool_):
            return bool(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif hasattr(obj, 'isoformat'):  # datetime objects
            return obj.isoformat()
        elif isinstance(obj, (datetime, pd.Timestamp)):
            return obj.isoformat()
        elif isinstance(obj, set):
            return list(obj)
        elif isinstance(obj, (np.complexfloating, complex)):
            return str(obj)
        else:
            return str(obj)
    
    def _generate_html_report(self, report: Dict[str, Any], base_filename: str) -> str:
        """Generate HTML format report."""
        filename = f"{base_filename}.html"
        filedir = os.path.join(project_root,self.output_dir)
        filepath = os.path.join(filedir, filename)

        if not os.path.exists(filedir):
            os.makedirs(filedir, exist_ok=True)
        
        try:
            html_content = self._build_html_content(report)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            print(f"HTML report generated: {filepath}")
            return filepath
        except Exception as e:
            print(f"Error generating HTML report: {e}")
            return None
    
    def _build_html_content(self, report: Dict[str, Any]) -> str:
        """Build HTML content for the report."""
        metadata = report.get('metadata', {})
        summary = report.get('executive_summary', {})
        scorecard = report.get('quality_scorecard', {})
        missing_patterns = report.get('missing_patterns_analysis', {})
        
        html = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Data Quality Report - {metadata.get('source_file_name', 'Unknown')}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }}
                .header {{ background: #2c3e50; color: white; padding: 20px; border-radius: 5px; }}
                .summary {{ background: #ecf0f1; padding: 20px; margin: 20px 0; border-radius: 5px; }}
                .quality-score {{ font-size: 2em; font-weight: bold; text-align: center; margin: 20px 0; }}
                .grade-A {{ color: #27ae60; }}
                .grade-B {{ color: #f39c12; }}
                .grade-C {{ color: #e67e22; }}
                .grade-D {{ color: #e74c3c; }}
                .grade-F {{ color: #c0392b; }}
                .metrics {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }}
                .metric-card {{ background: white; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
                .metric-value {{ font-size: 1.5em; font-weight: bold; }}
                .recommendations {{ margin: 20px 0; }}
                .recommendation {{ background: #fff3cd; padding: 15px; margin: 10px 0; border-left: 4px solid #ffc107; }}
                .high-priority {{ border-left-color: #dc3545; background: #f8d7da; }}
                .critical-priority {{ border-left-color: #721c24; background: #f5c6cb; }}
                .table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                .table th, .table td {{ padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }}
                .table th {{ background-color: #f8f9fa; }}
                .missing-patterns {{ background: #e8f4f8; padding: 20px; margin: 20px 0; border-radius: 5px; }}
                .insights {{ background: #f8f9fa; padding: 15px; margin: 15px 0; border-left: 4px solid #6c757d; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Data Quality Report</h1>
                <p>File: {metadata.get('source_file_name', 'Unknown')}</p>
                <p>Generated: {metadata.get('report_generated_at', 'Unknown')}</p>
            </div>
            
            <div class="summary">
                <h2>Executive Summary</h2>
                <div class="quality-score grade-{scorecard.get('overall_grade', 'F')}">
                    Overall Quality Score: {summary.get('overall_quality_score', 0):.1f}% (Grade {scorecard.get('overall_grade', 'F')})
                </div>
                <p><strong>Quality Level:</strong> {summary.get('quality_level', 'Unknown')}</p>
                <p>{summary.get('quality_description', 'No description available')}</p>
            </div>
            
            <div class="metrics">
                <div class="metric-card">
                    <h3>Dataset Overview</h3>
                    <div class="metric-value">{summary.get('dataset_overview', {}).get('total_records', 0):,}</div>
                    <p>Total Records</p>
                </div>
                <div class="metric-card">
                    <h3>Fields</h3>
                    <div class="metric-value">{summary.get('dataset_overview', {}).get('total_fields', 0)}</div>
                    <p>Total Fields</p>
                </div>
                <div class="metric-card">
                    <h3>Missing Data</h3>
                    <div class="metric-value">{summary.get('dataset_overview', {}).get('missing_data_percentage', 0):.1f}%</div>
                    <p>Missing Data</p>
                </div>
                <div class="metric-card">
                    <h3>Memory Usage</h3>
                    <div class="metric-value">{summary.get('dataset_overview', {}).get('memory_usage_mb', 0):.1f}</div>
                    <p>MB</p>
                </div>
            </div>
            
            <div class="missing-patterns">
                <h2>Missing Data Analysis</h2>
                <div class="metrics">
                    <div class="metric-card">
                        <h3>Overall Missing</h3>
                        <div class="metric-value">{missing_patterns.get('overall_missing_summary', {}).get('overall_missing_percentage', 0):.1f}%</div>
                        <p>Total Missing Data</p>
                    </div>
                    <div class="metric-card">
                        <h3>Empty Columns</h3>
                        <div class="metric-value">{len(missing_patterns.get('overall_missing_summary', {}).get('completely_empty_columns', []))}</div>
                        <p>Completely Empty</p>
                    </div>
                    <div class="metric-card">
                        <h3>Mostly Missing</h3>
                        <div class="metric-value">{len(missing_patterns.get('overall_missing_summary', {}).get('mostly_missing_columns', []))}</div>
                        <p>>50% Missing</p>
                    </div>
                    <div class="metric-card">
                        <h3>Complete Columns</h3>
                        <div class="metric-value">{len(missing_patterns.get('overall_missing_summary', {}).get('complete_columns', []))}</div>
                        <p><10% Missing</p>
                    </div>
                </div>
                
                <div class="insights">
                    <h3>Missing Data Insights</h3>
                    <ul>
        """
        
        # Add missing data insights
        for insight in missing_patterns.get('insights', []):
            html += f"<li>{insight}</li>"
        
        html += """
                    </ul>
                </div>
            </div>
            
            <h2>Quality Dimensions</h2>
            <table class="table">
                <tr>
                    <th>Dimension</th>
                    <th>Score</th>
                    <th>Grade</th>
                    <th>Status</th>
                </tr>
        """
        
        # Add dimension scores
        for dimension, scores in scorecard.get('dimension_scores', {}).items():
            status_class = 'style="color: green;"' if scores.get('status') == 'Pass' else 'style="color: red;"'
            html += f"""
                <tr>
                    <td>{dimension}</td>
                    <td>{scores.get('score', 0):.1f}%</td>
                    <td class="grade-{scores.get('grade', 'F')}">{scores.get('grade', 'F')}</td>
                    <td {status_class}>{scores.get('status', 'Unknown')}</td>
                </tr>
            """
        
        html += """
            </table>
            
            <h2>Key Findings</h2>
            <ul>
        """
        
        # Add key findings
        for finding in summary.get('key_findings', []):
            html += f"<li>{finding}</li>"
        
        html += """
            </ul>
            
            <div class="recommendations">
                <h2>Priority Recommendations</h2>
        """
        
        # Add recommendations
        recommendations = report.get('recommendations', [])[:5]  # Top 5 recommendations
        for rec in recommendations:
            priority = rec.get('priority', 'Medium')
            if priority == 'Critical':
                priority_class = 'critical-priority'
            elif priority == 'High':
                priority_class = 'high-priority'
            else:
                priority_class = 'recommendation'
                
            html += f"""
                <div class="{priority_class}">
                    <h3>{rec.get('category', 'General')} - {priority} Priority</h3>
                    <p><strong>Issue:</strong> {rec.get('issue', 'No issue description')}</p>
                    <p><strong>Recommendation:</strong> {rec.get('recommendation', 'No recommendation')}</p>
                    <p><strong>Impact:</strong> {rec.get('impact', 'No impact description')}</p>
                </div>
            """
        
        html += """
            </div>
            
            <footer style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; color: #666;">
                <p>Report generated by CSV Quality Analyzer v1.0.0</p>
            </footer>
        </body>
        </html>
        """
        
        return html
    
    def _generate_csv_summary(self, report: Dict[str, Any], base_filename: str) -> str:
        """Generate CSV summary report."""
        filename = f"{base_filename}_summary.csv"
        filedir = os.path.join(project_root,self.output_dir)
        filepath = os.path.join(filedir, filename)

        if not os.path.exists(filedir):
            os.makedirs(filedir, exist_ok=True)
        
        try:
            # Create summary data for CSV
            summary_data = []
            
            # Overall metrics
            summary = report.get('executive_summary', {})
            missing_patterns = report.get('missing_patterns_analysis', {})
            
            summary_data.append(['Metric', 'Value', 'Category'])
            summary_data.append(['Overall Quality Score', f"{summary.get('overall_quality_score', 0):.1f}%", 'Overall'])
            summary_data.append(['Quality Level', summary.get('quality_level', 'Unknown'), 'Overall'])
            summary_data.append(['Total Records', summary.get('dataset_overview', {}).get('total_records', 0), 'Dataset'])
            summary_data.append(['Total Fields', summary.get('dataset_overview', {}).get('total_fields', 0), 'Dataset'])
            summary_data.append(['Missing Data %', f"{summary.get('dataset_overview', {}).get('missing_data_percentage', 0):.1f}%", 'Dataset'])
            
            # Missing data summary
            overall_missing = missing_patterns.get('overall_missing_summary', {})
            summary_data.append(['Overall Missing %', f"{overall_missing.get('overall_missing_percentage', 0):.1f}%", 'Missing Data'])
            summary_data.append(['Empty Columns', len(overall_missing.get('completely_empty_columns', [])), 'Missing Data'])
            summary_data.append(['Mostly Missing Columns', len(overall_missing.get('mostly_missing_columns', [])), 'Missing Data'])
            summary_data.append(['Complete Columns', len(overall_missing.get('complete_columns', [])), 'Missing Data'])
            
            # Quality dimensions
            scorecard = report.get('quality_scorecard', {})
            for dimension, scores in scorecard.get('dimension_scores', {}).items():
                summary_data.append([f'{dimension} Score', f"{scores.get('score', 0):.1f}%", 'Quality Dimension'])
                summary_data.append([f'{dimension} Grade', scores.get('grade', 'F'), 'Quality Dimension'])
            
            # Column quality summary (top 10)
            column_scores = list(scorecard.get('column_scores', {}).items())[:10]
            for col, col_scores in column_scores:
                summary_data.append([f'{col} Quality Score', f"{col_scores.get('score', 0):.1f}%", 'Column Quality'])
                summary_data.append([f'{col} Grade', col_scores.get('grade', 'F'), 'Column Quality'])
            
            # Write CSV file
            with open(filepath, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerows(summary_data)
            
            print(f"CSV summary generated: {filepath}")
            return filepath
        except Exception as e:
            print(f"Error generating CSV summary: {e}")
            return None
    def generate_consolidated_report(self, chunk_reports: List[Dict[str, Any]], 
                                   overall_file_info: Dict[str, Any],
                                   consolidated_quality_metrics: Dict[str, Any],
                                   file_path: str) -> str:
        """Generate a consolidated report that summarizes results from multiple chunk analyses."""
        print("Generating consolidated report from chunked analysis...")
        
        # Ensure complete quality metrics
        consolidated_quality_metrics = self._ensure_complete_quality_metrics(consolidated_quality_metrics, {})
        
        # Build consolidated report structure
        consolidated_report = {
            'metadata': {
                'report_type': 'consolidated_large_file_analysis',
                'file_path': file_path,
                'analysis_timestamp': datetime.now().isoformat(),
                'analyzer_version': '1.0.0',
                'processing_method': 'chunked_analysis',
                'total_chunks_processed': len(chunk_reports)
            },
            'executive_summary': self._generate_consolidated_executive_summary(
                consolidated_quality_metrics, overall_file_info, len(chunk_reports)
            ),
            'file_information': overall_file_info,
            'consolidated_quality_metrics': consolidated_quality_metrics,
            'recommendations': self._generate_recommendations(consolidated_quality_metrics, {}),
            'quality_scorecard': self._generate_quality_scorecard(consolidated_quality_metrics)
        }
        
        # Generate filename with consolidated marker
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_filename = f"consolidated_quality_report_{timestamp}"
        
        # Generate report in specified format(s)
        report_paths = []
        
        if self.report_format == 'json' or self.report_format == 'all':
            json_path = self._generate_json_report(consolidated_report, base_filename)
            if json_path:
                report_paths.append(json_path)
        
        if self.report_format == 'html' or self.report_format == 'all':
            html_path = self._generate_html_report(consolidated_report, base_filename)
            if html_path:
                report_paths.append(html_path)
        
        if self.report_format == 'csv' or self.report_format == 'all':
            csv_path = self._generate_csv_summary(consolidated_report, base_filename)
            if csv_path:
                report_paths.append(csv_path)
        
        print(f"Consolidated report generated with {len(report_paths)} files")
        return report_paths[0] if report_paths else None
    
    def _generate_consolidated_executive_summary(self, quality_metrics: Dict[str, Any], 
                                                file_info: Dict[str, Any], 
                                                chunks_processed: int) -> Dict[str, Any]:
        """Generate executive summary for consolidated report."""
        overall_score = quality_metrics.get('overall_score', 0)
        
        # Determine overall quality level
        if overall_score >= 0.9:
            quality_level = 'Excellent'
            quality_description = 'Data quality is excellent across all processed chunks.'
        elif overall_score >= 0.8:
            quality_level = 'Good'
            quality_description = 'Data quality is good with consistent patterns across chunks.'
        elif overall_score >= 0.7:
            quality_level = 'Fair'
            quality_description = 'Data quality is fair with some inconsistencies between chunks.'
        elif overall_score >= 0.6:
            quality_level = 'Poor'
            quality_description = 'Data quality is poor with significant issues across multiple chunks.'
        else:
            quality_level = 'Critical'
            quality_description = 'Data quality is critical with severe issues throughout the dataset.'
        
        # Key findings from chunked analysis
        key_findings = []
        
        # Completeness findings
        completeness = quality_metrics.get('completeness', {})
        if completeness.get('ratio', 1.0) < 0.9:
            key_findings.append(f"Data completeness is {completeness.get('ratio', 0):.1%} across all chunks")
        
        # Processing findings
        key_findings.append(f"Processed data across {chunks_processed} chunks")
        
        return {
            'overall_quality_score': round(overall_score * 100, 1),
            'quality_level': quality_level,
            'quality_description': quality_description,
            'processing_summary': {
                'chunks_processed': chunks_processed,
                'processing_method': 'chunked_analysis'
            },
            'dataset_overview': {
                'file_size_mb': file_info.get('structure', {}).get('file_size_mb', 0),
                'encoding': file_info.get('encoding', 'unknown'),
                'delimiter': file_info.get('delimiter', 'unknown')
            },
            'key_findings': key_findings,
            'requires_immediate_attention': len(key_findings) > 2 or overall_score < 0.6
        }


    def _summarize_chunk_processing(self, chunk_reports: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Summarize the chunk processing statistics."""
        if not chunk_reports:
            return {'error': 'No chunk reports provided'}
        
        chunk_summary = {
            'total_chunks': len(chunk_reports),
            'chunk_quality_scores': [],
            'chunk_sizes': [],
            'quality_consistency': {},
            'processing_times': []
        }
        
        # Extract quality scores and sizes from each chunk
        for i, chunk_report in enumerate(chunk_reports):
            quality_metrics = chunk_report.get('quality_assessment', {})
            overall_score = quality_metrics.get('overall_score', 0)
            chunk_summary['chunk_quality_scores'].append(overall_score)
            
            # Extract chunk size if available
            dataset_info = chunk_report.get('data_profile', {}).get('dataset_info', {})
            chunk_size = dataset_info.get('row_count', 0)
            chunk_summary['chunk_sizes'].append(chunk_size)
        
        # Calculate quality consistency metrics
        if chunk_summary['chunk_quality_scores']:
            scores = chunk_summary['chunk_quality_scores']
            chunk_summary['quality_consistency'] = {
                'min_quality_score': min(scores),
                'max_quality_score': max(scores),
                'average_quality_score': sum(scores) / len(scores),
                'quality_std_deviation': np.std(scores) if len(scores) > 1 else 0,
                'consistent_quality': np.std(scores) < 0.1 if len(scores) > 1 else True
            }
        
        # Processing efficiency
        if chunk_summary['chunk_sizes']:
            sizes = chunk_summary['chunk_sizes']
            chunk_summary['processing_efficiency'] = {
                'total_rows_processed': sum(sizes),
                'average_chunk_size': sum(sizes) / len(sizes),
                'min_chunk_size': min(sizes),
                'max_chunk_size': max(sizes),
                'size_consistency': np.std(sizes) / np.mean(sizes) if np.mean(sizes) > 0 else 0
            }
        
        return chunk_summary
    
    def _aggregate_data_profiles(self, chunk_reports: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Aggregate data profiles from all chunks."""
        if not chunk_reports:
            return {'error': 'No chunk reports provided'}
        
        aggregated_profile = {
            'column_types': {},
            'aggregated_statistics': {},
            'consistency_across_chunks': {}
        }
        
        # Collect column types from all chunks
        all_column_types = {}
        for chunk_report in chunk_reports:
            data_profile = chunk_report.get('data_profile', {})
            column_types = data_profile.get('column_types', {})
            
            for col, col_type in column_types.items():
                if col not in all_column_types:
                    all_column_types[col] = []
                all_column_types[col].append(col_type)
        
        # Determine consistent column types
        for col, types in all_column_types.items():
            unique_types = list(set(types))
            if len(unique_types) == 1:
                aggregated_profile['column_types'][col] = unique_types[0]
            else:
                # Type inconsistency across chunks
                aggregated_profile['column_types'][col] = f"inconsistent: {unique_types}"
        
        # Check for type consistency
        type_inconsistencies = [col for col, col_type in aggregated_profile['column_types'].items() 
                              if col_type.startswith('inconsistent:')]
        
        aggregated_profile['consistency_across_chunks'] = {
            'consistent_column_types': len(type_inconsistencies) == 0,
            'inconsistent_columns': type_inconsistencies,
            'total_columns_analyzed': len(aggregated_profile['column_types'])
        }
        
        return aggregated_profile
    
    def _generate_processing_statistics(self, chunk_reports: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate detailed processing statistics."""
        processing_stats = {
            'memory_usage': {
                'peak_memory_per_chunk': [],
                'average_memory_usage': 0,
                'memory_efficiency': 'good'
            },
            'performance_metrics': {
                'total_processing_time': 0,
                'average_time_per_chunk': 0,
                'throughput_rows_per_second': 0
            },
            'quality_trends': {
                'quality_by_chunk': [],
                'improving_trend': False,
                'stable_quality': True
            }
        }
        
        # Extract performance data if available
        chunk_scores = []
        total_rows = 0
        
        for i, chunk_report in enumerate(chunk_reports):
            # Quality score
            quality_score = chunk_report.get('quality_assessment', {}).get('overall_score', 0)
            chunk_scores.append(quality_score)
            
            # Row count
            dataset_info = chunk_report.get('data_profile', {}).get('dataset_info', {})
            rows = dataset_info.get('row_count', 0)
            total_rows += rows
        
        # Analyze quality trends
        if len(chunk_scores) > 1:
            # Check if quality is improving, declining, or stable
            first_half_avg = np.mean(chunk_scores[:len(chunk_scores)//2])
            second_half_avg = np.mean(chunk_scores[len(chunk_scores)//2:])
            
            processing_stats['quality_trends'] = {
                'quality_by_chunk': chunk_scores,
                'improving_trend': second_half_avg > first_half_avg + 0.05,
                'declining_trend': second_half_avg < first_half_avg - 0.05,
                'stable_quality': abs(second_half_avg - first_half_avg) <= 0.05,
                'quality_variance': round(np.var(chunk_scores), 4)
            }
        
        processing_stats['summary'] = {
            'total_chunks_processed': len(chunk_reports),
            'total_rows_processed': total_rows,
            'average_quality_score': round(np.mean(chunk_scores), 4) if chunk_scores else 0,
            'quality_standard_deviation': round(np.std(chunk_scores), 4) if len(chunk_scores) > 1 else 0
        }
        
        return processing_stats
    
    def _generate_chunk_detail_report(self, chunk_reports: List[Dict[str, Any]], base_filename: str) -> str:
        """Generate detailed chunk-by-chunk analysis report."""
        filename = f"{base_filename}_chunk_details.json"
        filedir = os.path.join(project_root,self.output_dir)
        filepath = os.path.join(filedir, filename)

        if not os.path.exists(filedir):
            os.makedirs(filedir, exist_ok=True)
        
        try:
            chunk_details = {
                'metadata': {
                    'report_type': 'chunk_detail_analysis',
                    'generated_at': datetime.now().isoformat(),
                    'total_chunks': len(chunk_reports)
                },
                'chunk_summaries': [],
                'chunk_comparison': self._compare_chunks(chunk_reports)
            }
            
            # Summarize each chunk
            for i, chunk_report in enumerate(chunk_reports):
                chunk_summary = {
                    'chunk_index': i + 1,
                    'quality_score': chunk_report.get('quality_assessment', {}).get('overall_score', 0),
                    'row_count': chunk_report.get('data_profile', {}).get('dataset_info', {}).get('row_count', 0),
                    'completeness': chunk_report.get('quality_assessment', {}).get('completeness', {}).get('ratio', 0),
                    'uniqueness': chunk_report.get('quality_assessment', {}).get('uniqueness', {}).get('ratio', 0),
                    'validity': chunk_report.get('quality_assessment', {}).get('validity', {}).get('overall_validity_ratio', 0),
                    'issues_found': len(chunk_report.get('recommendations', []))
                }
                chunk_details['chunk_summaries'].append(chunk_summary)
            
            # Clean and serialize
            clean_chunk_details = self._clean_for_json_serialization(chunk_details)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(clean_chunk_details, f, indent=2, default=self._json_serializer, ensure_ascii=False)
            
            print(f"Chunk detail report generated: {filepath}")
            return filepath
            
        except Exception as e:
            print(f"Error generating chunk detail report: {e}")
            return None
    
    def _compare_chunks(self, chunk_reports: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Compare quality metrics across chunks to identify patterns."""
        if len(chunk_reports) < 2:
            return {'note': 'Need at least 2 chunks for comparison'}
        
        comparison = {
            'quality_variance': {},
            'outlier_chunks': [],
            'best_quality_chunk': None,
            'worst_quality_chunk': None,
            'patterns_detected': []
        }
        
        # Extract quality scores
        chunk_scores = []
        for i, chunk_report in enumerate(chunk_reports):
            score = chunk_report.get('quality_assessment', {}).get('overall_score', 0)
            chunk_scores.append({'chunk_index': i + 1, 'quality_score': score})
        
        # Find best and worst chunks
        if chunk_scores:
            chunk_scores_sorted = sorted(chunk_scores, key=lambda x: x['quality_score'])
            comparison['worst_quality_chunk'] = chunk_scores_sorted[0]
            comparison['best_quality_chunk'] = chunk_scores_sorted[-1]
            
            # Identify outliers (chunks with quality significantly different from mean)
            scores = [item['quality_score'] for item in chunk_scores]
            mean_score = np.mean(scores)
            std_score = np.std(scores)
            
            for chunk_info in chunk_scores:
                if abs(chunk_info['quality_score'] - mean_score) > 2 * std_score:
                    comparison['outlier_chunks'].append(chunk_info)
            
            # Detect patterns
            if len(scores) >= 3:
                # Check for declining quality
                if scores[-1] < scores[0] - 0.1:
                    comparison['patterns_detected'].append('Quality declines towards end of file')
                
                # Check for improving quality
                if scores[-1] > scores[0] + 0.1:
                    comparison['patterns_detected'].append('Quality improves towards end of file')
                
                # Check for consistent quality
                if std_score < 0.05:
                    comparison['patterns_detected'].append('Consistent quality throughout file')
        
        return comparison
    
    def generate_column_report(self, profile: Dict[str, Any], quality_metrics: Dict[str, Any]) -> str:
        """Generate detailed column-by-column analysis report."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"column_analysis_{timestamp}.json"
        filedir = os.path.join(project_root,self.output_dir)
        filepath = os.path.join(filedir, filename)
        
        if not os.path.exists(filedir):
            os.makedirs(filedir, exist_ok=True)

        column_report = {
            'metadata': {
                'report_type': 'column_analysis',
                'generated_at': datetime.now().isoformat()
            },
            'columns': {}
        }
        
        columns_info = profile.get('columns', {})
        column_types = profile.get('column_types', {})
        column_quality = quality_metrics.get('column_quality', {})
        
        for col_name, col_info in columns_info.items():
            column_report['columns'][col_name] = {
                'basic_info': {
                    'name': col_name,
                    'data_type': column_types.get(col_name, 'unknown'),
                    'pandas_dtype': col_info.get('dtype', 'unknown'),
                    'total_values': col_info.get('non_null_count', 0) + col_info.get('null_count', 0),
                    'non_null_values': col_info.get('non_null_count', 0),
                    'null_values': col_info.get('null_count', 0),
                    'unique_values': col_info.get('unique_count', 0)
                },
                'quality_metrics': column_quality.get(col_name, {}),
                'detailed_profile': col_info
            }
        
        try:
            # Clean and serialize the column report
            clean_column_report = self._clean_for_json_serialization(column_report)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(clean_column_report, f, indent=2, default=self._json_serializer, ensure_ascii=False)
            
            print(f"Column analysis report generated: {filepath}")
            return filepath
        except Exception as e:
            print(f"Error generating column report: {e}")
            return None


if __name__ == "__main__":
    # Example usage
    from core.config_manager import ConfigManager
    
    # Sample data for testing
    sample_report_data = {
        'metadata': {'source_file': 'test.csv'},
        'executive_summary': {
            'overall_quality_score': 85.5,
            'quality_level': 'Good',
            'dataset_overview': {
                'total_records': 1000,
                'total_fields': 10,
                'missing_data_percentage': 5.2
            },
            'key_findings': ['Some missing data', 'Few duplicates found']
        },
        'quality_scorecard': {
            'overall_grade': 'B',
            'dimension_scores': {
                'Completeness': {'score': 94.8, 'grade': 'A', 'status': 'Pass'},
                'Uniqueness': {'score': 98.5, 'grade': 'A', 'status': 'Pass'}
            }
        }
    }
    
    config = ConfigManager()
    generator = ReportGenerator(config)
    
    # This would normally be called with real data
    # report_path = generator.generate_report({}, {}, {}, "test.csv")
    print("Report generator initialized successfully!")