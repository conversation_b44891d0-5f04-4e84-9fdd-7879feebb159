"""
Main CSV Quality Analyzer
Orchestrates the complete data quality analysis workflow for CSV files.
"""

import pandas as pd
import os
import gc
import sys
import traceback
from typing import Dict, Any, Iterator, Optional, Tuple
import logging
from datetime import datetime
import warnings
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).resolve().parent.parent.parent
sys.path.insert(0, str(project_root))


# Import custom modules
from core.config_manager import ConfigManager
from core.dataqualityanalysis.file_detector import FileDetector
from core.dataqualityanalysis.data_profiler import DataProfiler
from core.dataqualityanalysis.quality_analyzer import QualityAnalyzer
from core.dataqualityanalysis.report_generator import ReportGenerator
from core.dataqualityanalysis.utils import setup_logging, ProgressTracker, MemoryMonitor, ErrorHandler

warnings.filterwarnings('ignore')


class CSVQualityAnalyzer:
    """
    Main orchestrator for comprehensive CSV quality analysis.
    Coordinates file detection, profiling, quality analysis, and reporting.
    """
    
    def __init__(self, config_path: str = "config.json"):
        """
        Initialize the CSV Quality Analyzer.
        
        Args:
            config_path (str): Path to configuration file
        """
        self.config_manager = ConfigManager(config_path)
        self.logger = setup_logging(self.config_manager)
        
        # Initialize components
        self.file_detector = FileDetector(self.config_manager)
        self.data_profiler = DataProfiler(self.config_manager)
        self.quality_analyzer = QualityAnalyzer(self.config_manager)
        self.report_generator = ReportGenerator(self.config_manager)
        
        # Analysis parameters
        self.chunk_size = self.config_manager.get('processing.chunk_size', 10000)
        self.sample_size = self.config_manager.get('processing.sample_size', 100000)
        self.max_memory_mb = self.config_manager.get('processing.max_memory_mb', 1024)
        
        # Initialize utilities
        self.progress_tracker = ProgressTracker()
        self.memory_monitor = MemoryMonitor(max_memory_mb=self.max_memory_mb)
        self.error_handler = ErrorHandler(self.logger)
        
        self.logger.info("CSV Quality Analyzer initialized successfully")
    
    def analyze_file(self, file_path: str, output_dir: Optional[str] = None) -> Dict[str, Any]:
        """
        Perform complete analysis of a CSV file.
        
        Args:
            file_path (str): Path to the CSV file to analyze
            output_dir (str, optional): Custom output directory for reports
            
        Returns:
            Dict[str, Any]: Analysis results including file paths to generated reports
        """
        analysis_start_time = datetime.now()
        
        try:
            self.logger.info(f"Starting analysis of: {file_path}")
            self.progress_tracker.start_analysis(file_path)
            
            # Validate input file
            self._validate_input_file(file_path)
            
            # Step 1: Detect file characteristics
            self.logger.info("Step 1: Detecting file encoding and delimiter...")
            self.progress_tracker.update_progress("Detecting file characteristics", 10)
            
            file_info = self.file_detector.get_file_info(file_path)
            self.logger.info(f"Detected encoding: {file_info['encoding']} "
                           f"(confidence: {file_info['encoding_confidence']:.2f})")
            self.logger.info(f"Detected delimiter: '{file_info['delimiter']}'")
            self.logger.info(f"Estimated rows: {file_info['structure']['estimated_rows']:,}")
            
            # Validate file readability
            validation_results = self.file_detector.validate_file_readability(
                file_path, file_info['encoding'], file_info['delimiter']
            )
            
            if not validation_results['is_readable']:
                raise ValueError(f"File cannot be read: {validation_results['errors']}")
            
            # Step 2: Load sample for initial profiling
            self.logger.info("Step 2: Loading sample data for profiling...")
            self.progress_tracker.update_progress("Loading sample data", 20)
            
            sample_df = self._load_sample(file_path, file_info)
            self.logger.info(f"Loaded sample: {len(sample_df):,} rows, {len(sample_df.columns)} columns")
            
            # Step 3: Generate data profile
            self.logger.info("Step 3: Generating data profile...")
            self.progress_tracker.update_progress("Profiling data", 40)
            
            profile = self.data_profiler.generate_profile(sample_df)
            column_types = profile['column_types']
            
            self.logger.info(f"Identified column types: {len(set(column_types.values()))} unique types")
            
            # Step 4: Perform quality analysis
            self.logger.info("Step 4: Performing quality analysis...")
            self.progress_tracker.update_progress("Analyzing quality", 60)
            
            # Determine if we need chunked processing
            estimated_rows = file_info['structure']['estimated_rows']
            estimated_memory = file_info['structure'].get('estimated_memory_usage_mb', 0)
            
            if estimated_rows <= self.sample_size or estimated_memory <= self.max_memory_mb:
                # Small file - analyze entirely in memory
                self.logger.info("Processing as small file (in-memory analysis)")
                quality_metrics = self.quality_analyzer.analyze_quality(sample_df, column_types)
            else:
                # Large file - analyze in chunks
                self.logger.info("Processing as large file (chunked analysis)")
                quality_metrics = self._analyze_large_file(file_path, file_info, column_types)
            
            # Step 5: Generate reports
            self.logger.info("Step 5: Generating reports...")
            self.progress_tracker.update_progress("Generating reports", 80)
            
            # Set custom output directory if provided
            if output_dir:
                original_output_dir = self.config_manager.get('output.output_directory')
                self.config_manager.set('output.output_directory', output_dir)
                self.report_generator = ReportGenerator(self.config_manager)
            
            # Generate main report
            main_report_path = self.report_generator.generate_report(
                file_info, profile, quality_metrics, file_path, sample_df
            )
            
            # Generate additional reports if configured
            additional_reports = {}
            if self.config_manager.get('output.detailed_metrics', True):
                column_report_path = self.report_generator.generate_column_report(profile, quality_metrics)
                additional_reports['column_report'] = column_report_path
            
            # Restore original output directory
            if output_dir:
                self.config_manager.set('output.output_directory', original_output_dir)
            
            # Step 6: Finalize results
            self.progress_tracker.update_progress("Finalizing results", 100)
            
            analysis_end_time = datetime.now()
            analysis_duration = (analysis_end_time - analysis_start_time).total_seconds()
            
            # Compile final results
            results = {
                'success': True,
                'analysis_summary': {
                    'file_path': file_path,
                    'analysis_start_time': analysis_start_time.isoformat(),
                    'analysis_end_time': analysis_end_time.isoformat(),
                    'analysis_duration_seconds': round(analysis_duration, 2),
                    'rows_analyzed': len(sample_df) if estimated_rows <= self.sample_size else estimated_rows,
                    'columns_analyzed': len(sample_df.columns),
                    'overall_quality_score': quality_metrics.get('overall_score', 0),
                    'quality_grade': self._score_to_grade(quality_metrics.get('overall_score', 0))
                },
                'file_info': file_info,
                'data_profile': profile,
                'quality_metrics': quality_metrics,
                'reports': {
                    'main_report': main_report_path,
                    **additional_reports
                },
                'recommendations_count': len(quality_metrics.get('issues_summary', {}).get('recommendations', [])),
                'critical_issues_count': len(quality_metrics.get('issues_summary', {}).get('critical_issues', []))
            }
            
            # Log completion
            self.logger.info(f"Analysis completed successfully in {analysis_duration:.1f} seconds")
            self.logger.info(f"Overall Quality Score: {quality_metrics.get('overall_score', 0):.2%}")
            self.logger.info(f"Main report saved to: {main_report_path}")
            
            # Print summary to console
            self._print_analysis_summary(results)
            
            return results
            
        except Exception as e:
            self.error_handler.handle_error(e, f"Analysis failed for file: {file_path}")
            
            return {
                'success': False,
                'error': str(e),
                'error_details': traceback.format_exc(),
                'file_path': file_path,
                'analysis_start_time': analysis_start_time.isoformat(),
                'analysis_end_time': datetime.now().isoformat()
            }
        
        finally:
            # Cleanup
            self.progress_tracker.complete_analysis()
            gc.collect()
    
    def analyze_multiple_files(self, file_paths: list, output_dir: Optional[str] = None) -> Dict[str, Any]:
        """
        Analyze multiple CSV files in batch.
        
        Args:
            file_paths (list): List of file paths to analyze
            output_dir (str, optional): Custom output directory for reports
            
        Returns:
            Dict[str, Any]: Batch analysis results
        """
        batch_start_time = datetime.now()
        
        self.logger.info(f"Starting batch analysis of {len(file_paths)} files")
        
        batch_results = {
            'batch_summary': {
                'total_files': len(file_paths),
                'successful_analyses': 0,
                'failed_analyses': 0,
                'batch_start_time': batch_start_time.isoformat(),
                'average_quality_score': 0.0
            },
            'file_results': {},
            'batch_statistics': {}
        }
        
        quality_scores = []
        
        for i, file_path in enumerate(file_paths, 1):
            try:
                self.logger.info(f"Processing file {i}/{len(file_paths)}: {os.path.basename(file_path)}")
                
                # Analyze individual file
                file_result = self.analyze_file(file_path, output_dir)
                
                if file_result['success']:
                    batch_results['batch_summary']['successful_analyses'] += 1
                    quality_score = file_result['analysis_summary']['overall_quality_score']
                    quality_scores.append(quality_score)
                else:
                    batch_results['batch_summary']['failed_analyses'] += 1
                
                batch_results['file_results'][file_path] = file_result
                
            except Exception as e:
                self.logger.error(f"Failed to analyze {file_path}: {str(e)}")
                batch_results['file_results'][file_path] = {
                    'success': False,
                    'error': str(e),
                    'file_path': file_path
                }
                batch_results['batch_summary']['failed_analyses'] += 1
        
        # Calculate batch statistics
        if quality_scores:
            batch_results['batch_summary']['average_quality_score'] = sum(quality_scores) / len(quality_scores)
            batch_results['batch_statistics'] = {
                'min_quality_score': min(quality_scores),
                'max_quality_score': max(quality_scores),
                'median_quality_score': sorted(quality_scores)[len(quality_scores)//2],
                'quality_distribution': self._calculate_quality_distribution(quality_scores)
            }
        
        batch_end_time = datetime.now()
        batch_duration = (batch_end_time - batch_start_time).total_seconds()
        
        batch_results['batch_summary']['batch_end_time'] = batch_end_time.isoformat()
        batch_results['batch_summary']['batch_duration_seconds'] = round(batch_duration, 2)
        
        self.logger.info(f"Batch analysis completed in {batch_duration:.1f} seconds")
        self.logger.info(f"Success rate: {batch_results['batch_summary']['successful_analyses']}/{len(file_paths)}")
        
        # Generate batch report
        if output_dir:
            batch_report_path = self._generate_batch_report(batch_results, output_dir)
            batch_results['batch_report_path'] = batch_report_path
        
        return batch_results
    
    def _validate_input_file(self, file_path: str) -> None:
        """Validate that the input file exists and is accessible."""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        if not os.path.isfile(file_path):
            raise ValueError(f"Path is not a file: {file_path}")
        
        if not os.access(file_path, os.R_OK):
            raise PermissionError(f"File is not readable: {file_path}")
        
        # Check file size
        file_size = os.path.getsize(file_path)
        if file_size == 0:
            raise ValueError(f"File is empty: {file_path}")
        
        # Check file extension (optional warning)
        if not file_path.lower().endswith(('.csv', '.txt', '.tsv')):
            self.logger.warning(f"File extension may not be CSV: {file_path}")
    
    def _load_sample(self, file_path: str, file_info: Dict[str, Any]) -> pd.DataFrame:
        """Load a sample of the data for analysis."""
        try:
            # Monitor memory usage
            self.memory_monitor.check_memory("before loading sample")
            
            df = pd.read_csv(
                file_path,
                encoding=file_info['encoding'],
                delimiter=file_info['delimiter'],
                nrows=self.sample_size,
                low_memory=self.config_manager.get('processing.low_memory', True),
                skipinitialspace=True,
                skip_blank_lines=True
            )
            
            if df.empty:
                raise ValueError("No data could be loaded from the file")
            
            self.memory_monitor.check_memory("after loading sample")
            
            return df
            
        except pd.errors.EmptyDataError:
            raise ValueError("File appears to be empty or contains no data")
        except pd.errors.ParserError as e:
            raise ValueError(f"Error parsing CSV file: {str(e)}")
        except Exception as e:
            raise RuntimeError(f"Error loading sample data: {str(e)}")
    
    def _analyze_large_file(self, file_path: str, file_info: Dict[str, Any], 
                          column_types: Dict[str, str]) -> Dict[str, Any]:
        """Analyze large files using chunked processing."""
        self.logger.info("Processing large file in chunks...")
        
        # Initialize aggregated metrics
        aggregated_metrics = {
            'total_rows_processed': 0,
            'chunks_processed': 0,
            'completeness': {'missing_cells': 0, 'total_cells': 0},
            'uniqueness': {'seen_combinations': set(), 'duplicate_count': 0},
            'outliers': {},
            'column_stats': {},
            'validity_errors': []
        }
        
        chunk_count = 0
        progress_interval = max(1, self.chunk_size // 1000)  # Update progress every ~1000 chunks
        
        try:
            for chunk in self._read_chunks(file_path, file_info):
                chunk_count += 1
                
                # Update progress periodically
                if chunk_count % progress_interval == 0:
                    progress = min(95, 60 + (chunk_count * 30 // 100))  # Progress between 60-95%
                    self.progress_tracker.update_progress(f"Processing chunk {chunk_count}", progress)
                
                self.logger.debug(f"Processing chunk {chunk_count} with {len(chunk)} rows")
                
                # Monitor memory usage
                self.memory_monitor.check_memory(f"chunk {chunk_count}")
                
                # Analyze chunk
                chunk_metrics = self.quality_analyzer.analyze_quality(chunk, column_types)
                
                # Aggregate results
                self._aggregate_chunk_metrics(aggregated_metrics, chunk_metrics, len(chunk))
                
                # Memory cleanup
                del chunk
                if chunk_count % 10 == 0:  # Garbage collect every 10 chunks
                    gc.collect()
            
            self.logger.info(f"Processed {chunk_count} chunks totaling {aggregated_metrics['total_rows_processed']:,} rows")
            
            # Finalize aggregated metrics
            final_metrics = self._finalize_aggregated_metrics(aggregated_metrics)
            
            return final_metrics
            
        except Exception as e:
            self.logger.error(f"Error during chunked analysis: {str(e)}")
            raise
    
    def _read_chunks(self, file_path: str, file_info: Dict[str, Any]) -> Iterator[pd.DataFrame]:
        """Read file in chunks with error handling."""
        try:
            chunk_reader = pd.read_csv(
                file_path,
                encoding=file_info['encoding'],
                delimiter=file_info['delimiter'],
                chunksize=self.chunk_size,
                low_memory=self.config_manager.get('processing.low_memory', True),
                skipinitialspace=True,
                skip_blank_lines=True
            )
            
            for chunk in chunk_reader:
                if not chunk.empty:
                    yield chunk
                
        except Exception as e:
            self.logger.error(f"Error reading chunks: {str(e)}")
            raise
    
    def _aggregate_chunk_metrics(self, aggregated: Dict[str, Any], 
                                chunk_metrics: Dict[str, Any], chunk_size: int) -> None:
        """Aggregate metrics from individual chunk analysis."""
        aggregated['total_rows_processed'] += chunk_size
        aggregated['chunks_processed'] += 1
        
        # Aggregate completeness
        completeness = chunk_metrics.get('completeness', {})
        aggregated['completeness']['missing_cells'] += completeness.get('missing_cells', 0)
        aggregated['completeness']['total_cells'] += completeness.get('total_cells', 0)
        
        # Aggregate duplicates (simplified - exact duplicates only)
        duplicates = chunk_metrics.get('duplicates', {})
        aggregated['uniqueness']['duplicate_count'] += duplicates.get('exact_duplicate_count', 0)
        
        # Aggregate outliers
        outliers = chunk_metrics.get('outliers', {})
        for col, outlier_info in outliers.items():
            if col not in aggregated['outliers']:
                aggregated['outliers'][col] = {'count': 0, 'total_values': 0, 'outliers': []}
            
            aggregated['outliers'][col]['count'] += outlier_info.get('count', 0)
            aggregated['outliers'][col]['total_values'] += chunk_size
            aggregated['outliers'][col]['outliers'].extend(outlier_info.get('outliers', [])[:5])  # Keep sample
        
        # Aggregate column statistics (simplified)
        column_quality = chunk_metrics.get('column_quality', {})
        for col, col_metrics in column_quality.items():
            if col not in aggregated['column_stats']:
                aggregated['column_stats'][col] = {
                    'total_completeness': 0,
                    'total_uniqueness': 0,
                    'chunk_count': 0
                }
            
            stats = aggregated['column_stats'][col]
            stats['total_completeness'] += col_metrics.get('completeness', 0) * chunk_size
            stats['total_uniqueness'] += col_metrics.get('uniqueness', 0) * chunk_size
            stats['chunk_count'] += 1
    
    def _finalize_aggregated_metrics(self, aggregated: Dict[str, Any]) -> Dict[str, Any]:
        """Finalize aggregated metrics calculations."""
        total_rows = aggregated['total_rows_processed']
        
        # Calculate final ratios
        completeness = aggregated['completeness']
        if completeness['total_cells'] > 0:
            completeness_ratio = 1 - (completeness['missing_cells'] / completeness['total_cells'])
        else:
            completeness_ratio = 1.0
        
        # Calculate uniqueness ratio
        duplicate_count = aggregated['uniqueness']['duplicate_count']
        uniqueness_ratio = 1 - (duplicate_count / total_rows) if total_rows > 0 else 1.0
        
        # Calculate outlier percentages
        for col, outlier_info in aggregated['outliers'].items():
            if outlier_info['total_values'] > 0:
                outlier_info['percentage'] = (outlier_info['count'] / outlier_info['total_values']) * 100
            else:
                outlier_info['percentage'] = 0.0
        
        # Calculate column quality averages
        column_quality = {}
        for col, stats in aggregated['column_stats'].items():
            if total_rows > 0:
                avg_completeness = stats['total_completeness'] / total_rows
                avg_uniqueness = stats['total_uniqueness'] / total_rows
                quality_score = (avg_completeness + avg_uniqueness) / 2
                
                column_quality[col] = {
                    'completeness': round(avg_completeness, 4),
                    'uniqueness': round(avg_uniqueness, 4),
                    'quality_score': round(quality_score, 4),
                    'quality_grade': self._score_to_grade(quality_score)
                }
        
        # Build final metrics structure
        final_metrics = {
            'overall_score': self._calculate_overall_score_from_aggregated(aggregated),
            'completeness': {
                'ratio': round(completeness_ratio, 4),
                'missing_cells': completeness['missing_cells'],
                'total_cells': completeness['total_cells'],
                'meets_threshold': completeness_ratio >= self.config_manager.get('data_quality.completeness_threshold', 0.95)
            },
            'uniqueness': {
                'ratio': round(uniqueness_ratio, 4),
                'duplicate_rows': duplicate_count,
                'unique_rows': total_rows - duplicate_count,
                'meets_threshold': uniqueness_ratio >= self.config_manager.get('data_quality.uniqueness_threshold', 0.99)
            },
            'outliers': aggregated['outliers'],
            'column_quality': column_quality,
            'processing_info': {
                'total_rows_processed': total_rows,
                'chunks_processed': aggregated['chunks_processed'],
                'processing_method': 'chunked_analysis'
            },
            'validity': {'overall_validity_ratio': 0.95},  # Placeholder
            'consistency': {'structural_consistency': True},  # Placeholder
            'accuracy': {'domain_accuracy_score': 0.90},  # Placeholder
            'duplicates': {'exact_duplicate_count': duplicate_count},
            'data_freshness': {'has_date_columns': False},  # Placeholder
            'referential_integrity': {'integrity_score': 1.0},  # Placeholder
            'issues_summary': {'recommendations': [], 'critical_issues': [], 'major_issues': []}
        }
        
        return final_metrics
    
    def _calculate_overall_score_from_aggregated(self, aggregated: Dict[str, Any]) -> float:
        """Calculate overall quality score from aggregated metrics."""
        completeness_ratio = 1 - (aggregated['completeness']['missing_cells'] / 
                                 max(aggregated['completeness']['total_cells'], 1))
        
        duplicate_ratio = aggregated['uniqueness']['duplicate_count'] / max(aggregated['total_rows_processed'], 1)
        uniqueness_ratio = 1 - duplicate_ratio
        
        # Weighted average (simplified)
        overall_score = (completeness_ratio * 0.4 + uniqueness_ratio * 0.3 + 0.3)  # 0.3 for other metrics
        
        return round(min(overall_score, 1.0), 4)
    
    def _score_to_grade(self, score: float) -> str:
        """Convert numeric score to letter grade."""
        if score >= 0.9:
            return 'A'
        elif score >= 0.8:
            return 'B'
        elif score >= 0.7:
            return 'C'
        elif score >= 0.6:
            return 'D'
        else:
            return 'F'
    
    def _print_analysis_summary(self, results: Dict[str, Any]) -> None:
        """Print analysis summary to console."""
        if not results['success']:
            print(f"\n❌ Analysis Failed: {results.get('error', 'Unknown error')}")
            return
        
        summary = results['analysis_summary']
        quality_score = summary['overall_quality_score']
        grade = summary['quality_grade']
        
        print("\n" + "="*60)
        print("📊 CSV QUALITY ANALYSIS SUMMARY")
        print("="*60)
        print(f"📁 File: {os.path.basename(summary['file_path'])}")
        print(f"📏 Size: {summary['rows_analyzed']:,} rows × {summary['columns_analyzed']} columns")
        print(f"⏱️  Duration: {summary['analysis_duration_seconds']:.1f} seconds")
        print(f"🎯 Overall Quality: {quality_score:.1f}% (Grade {grade})")
        
        # Quality indicators
        if grade in ['A', 'B']:
            print("✅ Quality Status: Good - Ready for analysis")
        elif grade == 'C':
            print("⚠️  Quality Status: Fair - Some issues to address")
        else:
            print("❌ Quality Status: Poor - Significant issues found")
        
        # Issue summary
        critical_issues = results.get('critical_issues_count', 0)
        recommendations = results.get('recommendations_count', 0)
        
        if critical_issues > 0:
            print(f"🚨 Critical Issues: {critical_issues}")
        if recommendations > 0:
            print(f"💡 Recommendations: {recommendations}")
        
        print(f"📋 Report: {results['reports']['main_report']}")
        print("="*60)
    
    def _calculate_quality_distribution(self, scores: list) -> Dict[str, int]:
        """Calculate distribution of quality scores by grade."""
        distribution = {'A': 0, 'B': 0, 'C': 0, 'D': 0, 'F': 0}
        
        for score in scores:
            grade = self._score_to_grade(score)
            distribution[grade] += 1
        
        return distribution
    
    def _generate_batch_report(self, batch_results: Dict[str, Any], output_dir: str) -> str:
        """Generate summary report for batch analysis."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = os.path.join(output_dir, f"batch_analysis_summary_{timestamp}.json")
        
        try:
            import json
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(batch_results, f, indent=2, default=str, ensure_ascii=False)
            
            self.logger.info(f"Batch report generated: {report_path}")
            return report_path
        except Exception as e:
            self.logger.error(f"Error generating batch report: {e}")
            return None
    
    def get_analysis_statistics(self) -> Dict[str, Any]:
        """Get statistics about analyzer performance and usage."""
        return {
            'analyzer_info': {
                'version': '1.0.0',
                'config_file': self.config_manager.config_path,
                'chunk_size': self.chunk_size,
                'sample_size': self.sample_size,
                'max_memory_mb': self.max_memory_mb
            },
            'performance_stats': self.progress_tracker.get_statistics(),
            'memory_stats': self.memory_monitor.get_statistics()
        }


def main():
    """Main entry point for command-line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description='CSV Quality Analyzer - Comprehensive data quality analysis')
    parser.add_argument('input_file', help='Path to CSV file to analyze')
    parser.add_argument('--config', '-c', default='config.json', help='Path to configuration file')
    parser.add_argument('--output', '-o', help='Output directory for reports')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose logging')
    parser.add_argument('--batch', '-b', action='store_true', help='Treat input as file list for batch processing')
    
    args = parser.parse_args()
    
    try:
        # Initialize analyzer
        analyzer = CSVQualityAnalyzer(args.config)
        
        if args.verbose:
            logging.getLogger().setLevel(logging.DEBUG)
        
        if args.batch:
            # Batch processing
            if not os.path.exists(args.input_file):
                print(f"Error: Batch file list not found: {args.input_file}")
                sys.exit(1)
            
            with open(args.input_file, 'r') as f:
                file_paths = [line.strip() for line in f if line.strip()]
            
            print(f"Starting batch analysis of {len(file_paths)} files...")
            results = analyzer.analyze_multiple_files(file_paths, args.output)
            
            print(f"\nBatch Analysis Complete!")
            print(f"Success rate: {results['batch_summary']['successful_analyses']}/{results['batch_summary']['total_files']}")
            print(f"Average quality score: {results['batch_summary']['average_quality_score']:.1f}%")
            
        else:
            # Single file analysis
            if not os.path.exists(args.input_file):
                print(f"Error: File not found: {args.input_file}")
                sys.exit(1)
            
            results = analyzer.analyze_file(args.input_file, args.output)
            
            if results['success']:
                print("\n Analysis completed successfully!")
                sys.exit(0)
            else:
                print(f"\n Analysis failed: {results.get('error', 'Unknown error')}")
                sys.exit(1)
                
    except KeyboardInterrupt:
        print("\n Analysis interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n Unexpected error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()