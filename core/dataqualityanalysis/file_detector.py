"""
File Detector for CSV Quality Analyzer
Handles automatic detection of file encoding, delimiter, and basic file characteristics.
"""

import chardet
import csv
import pandas as pd
import os
import sys
import mmap
import json
from typing import Dict, Any, Tuple, List, Optional
from pathlib import Path
import re

# Add project root to Python path
project_root = Path(__file__).resolve().parent.parent.parent
sys.path.insert(0, str(project_root))


from core.config_manager import ConfigManager


class FileDetector:
    """
    Handles encoding and delimiter detection for CSV files with comprehensive analysis.
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize FileDetector with configuration.
        
        Args:
            config_manager (ConfigManager): Configuration manager instance
        """
        self.config = config_manager
        self.sample_size = config_manager.get('processing.encoding_detection_sample', 10000)
        self.common_delimiters = [',', ';', '\t', '|', ':', ' ']
        self.common_encodings = ['utf-8', 'latin-1', 'cp1252', 'ascii', 'utf-16']
    
    def detect_encoding(self, file_path: str, sample_size: Optional[int] = None) -> Tuple[str, float, Dict[str, Any]]:
        """
        Detect file encoding using multiple methods for better accuracy.
        
        Args:
            file_path (str): Path to the CSV file
            sample_size (int, optional): Number of bytes to sample for detection
            
        Returns:
            Tuple[str, float, Dict[str, Any]]: (encoding, confidence, detection_details)
        """
        sample_size = sample_size or self.sample_size
        
        try:
            # Method 1: Use chardet for initial detection
            with open(file_path, 'rb') as f:
                raw_data = f.read(sample_size)
            
            chardet_result = chardet.detect(raw_data)
            primary_encoding = chardet_result.get('encoding', 'utf-8')
            primary_confidence = chardet_result.get('confidence', 0.0)
            
            # Method 2: Try common encodings and validate
            encoding_scores = {}
            
            for encoding in self.common_encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        test_data = f.read(min(sample_size, 1000))
                    
                    # Score based on successful decoding and character distribution
                    score = self._score_encoding(test_data, encoding)
                    encoding_scores[encoding] = score
                    
                except (UnicodeDecodeError, UnicodeError):
                    encoding_scores[encoding] = 0.0
            
            # Select best encoding
            best_encoding = max(encoding_scores, key=encoding_scores.get)
            best_score = encoding_scores[best_encoding]
            
            # Use chardet result if it has higher confidence
            if primary_confidence > 0.8 and primary_confidence > best_score:
                final_encoding = primary_encoding
                final_confidence = primary_confidence
            else:
                final_encoding = best_encoding
                final_confidence = best_score
            
            detection_details = {
                'chardet_result': chardet_result,
                'encoding_scores': encoding_scores,
                'method_used': 'chardet' if final_encoding == primary_encoding else 'validation',
                'sample_size_bytes': len(raw_data)
            }
            
            return final_encoding, final_confidence, detection_details
            
        except Exception as e:
            print(f"Warning: Encoding detection failed, using UTF-8: {e}")
            return 'utf-8', 0.5, {'error': str(e), 'fallback': True}
    
    def _score_encoding(self, text: str, encoding: str) -> float:
        """
        Score an encoding based on text characteristics.
        
        Args:
            text (str): Decoded text sample
            encoding (str): Encoding used
            
        Returns:
            float: Score between 0 and 1
        """
        score = 0.0
        
        # Base score for successful decoding
        score += 0.3
        
        # Score for printable characters
        printable_ratio = sum(1 for c in text if c.isprintable()) / len(text) if text else 0
        score += printable_ratio * 0.4
        
        # Score for ASCII characters (bonus for compatibility)
        ascii_ratio = sum(1 for c in text if ord(c) < 128) / len(text) if text else 0
        score += ascii_ratio * 0.2
        
        # Penalty for control characters
        control_ratio = sum(1 for c in text if ord(c) < 32 and c not in '\n\r\t') / len(text) if text else 0
        score -= control_ratio * 0.3
        
        # Bonus for UTF-8
        if encoding.lower() == 'utf-8':
            score += 0.1
        
        return max(0.0, min(1.0, score))
    
    def detect_delimiter(self, file_path: str, encoding: str, sample_lines: int = 10) -> Tuple[str, Dict[str, Any]]:
        """
        Detect CSV delimiter using multiple methods.
        
        Args:
            file_path (str): Path to the CSV file
            encoding (str): File encoding
            sample_lines (int): Number of lines to analyze
            
        Returns:
            Tuple[str, Dict[str, Any]]: (delimiter, detection_details)
        """
        try:
            # Read sample lines
            with open(file_path, 'r', encoding=encoding) as f:
                sample_lines_data = [f.readline() for _ in range(sample_lines)]
            
            sample_text = ''.join(sample_lines_data)
            
            # Method 1: Use csv.Sniffer
            sniffer_delimiter = None
            try:
                sniffer = csv.Sniffer()
                dialect = sniffer.sniff(sample_text, delimiters=',;\t|')
                sniffer_delimiter = dialect.delimiter
            except Exception as e:
                print(f"CSV Sniffer failed: {e}")
            
            # Method 2: Count delimiter occurrences
            delimiter_counts = {}
            delimiter_consistency = {}
            
            for delimiter in self.common_delimiters:
                counts_per_line = []
                for line in sample_lines_data:
                    if line.strip():  # Skip empty lines
                        counts_per_line.append(line.count(delimiter))
                
                if counts_per_line:
                    total_count = sum(counts_per_line)
                    delimiter_counts[delimiter] = total_count
                    
                    # Check consistency (same count per line indicates structure)
                    if len(set(counts_per_line)) == 1 and counts_per_line[0] > 0:
                        delimiter_consistency[delimiter] = 1.0
                    elif total_count > 0:
                        avg_count = total_count / len(counts_per_line)
                        variance = sum((x - avg_count) ** 2 for x in counts_per_line) / len(counts_per_line)
                        delimiter_consistency[delimiter] = max(0, 1 - (variance / (avg_count + 1)))
                    else:
                        delimiter_consistency[delimiter] = 0.0
            
            # Select best delimiter
            if sniffer_delimiter and sniffer_delimiter in delimiter_consistency:
                if delimiter_consistency[sniffer_delimiter] > 0.7:
                    best_delimiter = sniffer_delimiter
                    method = 'sniffer_validated'
                else:
                    # Sniffer result not consistent, use count method
                    best_delimiter = self._select_best_delimiter(delimiter_counts, delimiter_consistency)
                    method = 'count_analysis'
            else:
                best_delimiter = self._select_best_delimiter(delimiter_counts, delimiter_consistency)
                method = 'count_analysis'
            
            detection_details = {
                'sniffer_result': sniffer_delimiter,
                'delimiter_counts': delimiter_counts,
                'delimiter_consistency': delimiter_consistency,
                'method_used': method,
                'sample_lines_analyzed': len([line for line in sample_lines_data if line.strip()])
            }
            
            return best_delimiter, detection_details
            
        except Exception as e:
            print(f"Warning: Delimiter detection failed, using comma: {e}")
            return ',', {'error': str(e), 'fallback': True}
    
    def _select_best_delimiter(self, counts: Dict[str, int], consistency: Dict[str, float]) -> str:
        """
        Select the best delimiter based on count and consistency.
        
        Args:
            counts (Dict[str, int]): Delimiter occurrence counts
            consistency (Dict[str, float]): Consistency scores
            
        Returns:
            str: Best delimiter
        """
        # Score each delimiter
        delimiter_scores = {}
        
        for delimiter in self.common_delimiters:
            count = counts.get(delimiter, 0)
            consist = consistency.get(delimiter, 0)
            
            # Weighted score: consistency is more important than raw count
            score = (consist * 0.7) + (min(count / 100, 1.0) * 0.3)
            delimiter_scores[delimiter] = score
        
        # Return delimiter with highest score
        best_delimiter = max(delimiter_scores, key=delimiter_scores.get)
        
        # Fallback to comma if no clear winner
        if delimiter_scores[best_delimiter] < 0.1:
            return ','
        
        return best_delimiter
    
    def analyze_file_structure(self, file_path: str, encoding: str, delimiter: str) -> Dict[str, Any]:
        """
        Analyze the structure of the CSV file.
        
        Args:
            file_path (str): Path to the CSV file
            encoding (str): File encoding
            delimiter (str): CSV delimiter
            
        Returns:
            Dict[str, Any]: File structure analysis
        """
        try:
            # Basic file information
            file_stats = os.stat(file_path)
            file_size = file_stats.st_size
            
            # Read first few rows to analyze structure
            with open(file_path, 'r', encoding=encoding) as f:
                # Check for header
                first_line = f.readline().strip()
                second_line = f.readline().strip()
                
                # Reset and read with pandas for structure analysis
                f.seek(0)
                try:
                    sample_df = pd.read_csv(f, delimiter=delimiter, nrows=5)
                    columns = list(sample_df.columns)
                    column_count = len(columns)
                    has_header = self._detect_header(first_line, second_line, delimiter)
                except Exception:
                    # Fallback analysis
                    columns = first_line.split(delimiter) if first_line else []
                    column_count = len(columns)
                    has_header = True
            
            # Estimate row count efficiently
            estimated_rows = self._estimate_row_count(file_path, encoding)
            
            return {
                'file_size_bytes': file_size,
                'file_size_mb': round(file_size / (1024 * 1024), 2),
                'estimated_rows': estimated_rows,
                'column_count': column_count,
                'columns': columns,
                'has_header': has_header,
                'estimated_memory_usage_mb': self._estimate_memory_usage(file_size, column_count)
            }
            
        except Exception as e:
            print(f"Warning: File structure analysis failed: {e}")
            return {
                'file_size_bytes': os.path.getsize(file_path),
                'error': str(e)
            }
    
    def _detect_header(self, first_line: str, second_line: str, delimiter: str) -> bool:
        """
        Detect if the first line is a header.
        
        Args:
            first_line (str): First line of the file
            second_line (str): Second line of the file
            delimiter (str): CSV delimiter
            
        Returns:
            bool: True if first line appears to be a header
        """
        if not first_line or not second_line:
            return True  # Default assumption
        
        first_fields = first_line.split(delimiter)
        second_fields = second_line.split(delimiter)
        
        if len(first_fields) != len(second_fields):
            return True  # Inconsistent field count suggests header
        
        # Check if first line contains more text than numbers
        first_line_numeric = sum(1 for field in first_fields if self._is_numeric(field.strip()))
        second_line_numeric = sum(1 for field in second_fields if self._is_numeric(field.strip()))
        
        # If second line has more numeric fields, first line is likely header
        return second_line_numeric > first_line_numeric
    
    def _is_numeric(self, value: str) -> bool:
        """Check if a string represents a numeric value."""
        try:
            float(value)
            return True
        except ValueError:
            return False
    
    def _estimate_row_count(self, file_path: str, encoding: str) -> int:
        """
        Efficiently estimate the number of rows in the file.
        
        Args:
            file_path (str): Path to the CSV file
            encoding (str): File encoding
            
        Returns:
            int: Estimated number of rows
        """
        try:
            # For small files, count exactly
            file_size = os.path.getsize(file_path)
            if file_size < 10 * 1024 * 1024:  # 10MB
                with open(file_path, 'r', encoding=encoding) as f:
                    return sum(1 for _ in f)
            
            # For large files, estimate based on sample
            with open(file_path, 'r', encoding=encoding) as f:
                # Read first chunk
                chunk_size = min(file_size, 1024 * 1024)  # 1MB sample
                chunk = f.read(chunk_size)
                lines_in_chunk = chunk.count('\n')
                
                if lines_in_chunk == 0:
                    return 1
                
                # Estimate total lines
                estimated_lines = int((file_size / len(chunk.encode(encoding))) * lines_in_chunk)
                return max(1, estimated_lines)
                
        except Exception:
            return 1000  # Conservative fallback estimate
    
    def _estimate_memory_usage(self, file_size: int, column_count: int) -> float:
        """
        Estimate memory usage for loading the file.
        
        Args:
            file_size (int): File size in bytes
            column_count (int): Number of columns
            
        Returns:
            float: Estimated memory usage in MB
        """
        # Rough estimation: pandas typically uses 2-4x file size
        # More columns increase overhead
        base_multiplier = 2.5
        column_overhead = max(1.0, column_count / 10)
        
        estimated_mb = (file_size * base_multiplier * column_overhead) / (1024 * 1024)
        return round(estimated_mb, 2)
    
    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """
        Get comprehensive file information including encoding, delimiter, and structure.
        
        Args:
            file_path (str): Path to the CSV file
            
        Returns:
            Dict[str, Any]: Comprehensive file information
        """
        print(f"Analyzing file: {file_path}")
        
        # Validate file exists
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        if not os.path.isfile(file_path):
            raise ValueError(f"Path is not a file: {file_path}")
        
        # Detect encoding
        print("Detecting encoding...")
        encoding, confidence, encoding_details = self.detect_encoding(file_path)
        print(f"Detected encoding: {encoding} (confidence: {confidence:.2f})")
        
        # Detect delimiter
        print("Detecting delimiter...")
        delimiter, delimiter_details = self.detect_delimiter(file_path, encoding)
        print(f"Detected delimiter: '{delimiter}'")
        
        # Analyze file structure
        print("Analyzing file structure...")
        structure_info = self.analyze_file_structure(file_path, encoding, delimiter)
        
        # Compile comprehensive information
        file_info = {
            'file_path': file_path,
            'file_name': os.path.basename(file_path),
            'encoding': encoding,
            'encoding_confidence': confidence,
            'encoding_details': encoding_details,
            'delimiter': delimiter,
            'delimiter_details': delimiter_details,
            'structure': structure_info,
            'analysis_timestamp': pd.Timestamp.now().isoformat()
        }
        
        return file_info
    
    def validate_file_readability(self, file_path: str, encoding: str, delimiter: str) -> Dict[str, Any]:
        """
        Validate that the file can be read properly with detected parameters.
        
        Args:
            file_path (str): Path to the CSV file
            encoding (str): Detected encoding
            delimiter (str): Detected delimiter
            
        Returns:
            Dict[str, Any]: Validation results
        """
        validation_results = {
            'is_readable': False,
            'can_parse_structure': False,
            'sample_rows_read': 0,
            'errors': [],
            'warnings': []
        }
        
        try:
            # Test reading with detected parameters
            with open(file_path, 'r', encoding=encoding) as f:
                sample = f.read(1024)
                validation_results['is_readable'] = True
            
            # Test pandas parsing
            try:
                sample_df = pd.read_csv(file_path, encoding=encoding, delimiter=delimiter, nrows=10)
                validation_results['can_parse_structure'] = True
                validation_results['sample_rows_read'] = len(sample_df)
                
                # Check for potential issues
                if sample_df.empty:
                    validation_results['warnings'].append("File appears to be empty")
                
                if sample_df.columns.duplicated().any():
                    validation_results['warnings'].append("Duplicate column names detected")
                    
            except Exception as e:
                validation_results['errors'].append(f"Pandas parsing failed: {e}")
            
        except Exception as e:
            validation_results['errors'].append(f"File reading failed: {e}")
        
        return validation_results


if __name__ == "__main__":
    # Example usage
    from core.config_manager import ConfigManager
    
    config = ConfigManager()
    detector = FileDetector(config)
    
    # Test with a sample file 
    file_info = detector.get_file_info(str(project_root) + "/data/sample.csv")
    print(json.dumps(file_info, indent=2, default=str))