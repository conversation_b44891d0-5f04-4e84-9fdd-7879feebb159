"""
Data Profiler for CSV Quality Analyzer
Profiles data to understand schema, data types, and basic statistics.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Union
import re
import sys
from pathlib import Path
from datetime import datetime
import warnings
import json


# Add project root to Python path
project_root = Path(__file__).resolve().parent.parent.parent
sys.path.insert(0, str(project_root))


from core.config_manager import ConfigManager

warnings.filterwarnings('ignore')


class DataProfiler:
    """
    Profiles CSV data to understand schema, data types, and generate comprehensive statistics.
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize DataProfiler with configuration.
        
        Args:
            config_manager (ConfigManager): Configuration manager instance
        """
        self.config = config_manager
        self.date_formats = config_manager.get('data_quality.date_formats', [])
        self.categorical_threshold = config_manager.get('data_quality.categorical_threshold', 0.05)
        self.max_categorical_values = config_manager.get('data_quality.max_categorical_values', 50)
        self.email_pattern = config_manager.get('validation_rules.email_pattern')
        self.phone_pattern = config_manager.get('validation_rules.phone_pattern')
        self.url_pattern = config_manager.get('validation_rules.url_pattern')
    
    def generate_profile(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Generate comprehensive data profile for the DataFrame.
        
        Args:
            df (pd.DataFrame): DataFrame to profile
            
        Returns:
            Dict[str, Any]: Comprehensive data profile
        """
        print("Generating data profile...")
        
        # Basic dataset information
        profile = {
            'dataset_info': self._get_dataset_info(df),
            'column_types': self._infer_column_types(df),
            'columns': {},
            'correlations': self._calculate_correlations(df),
            'summary_statistics': self._get_summary_statistics(df)
        }
        
        # Profile each column individually
        print("Profiling individual columns...")
        for col in df.columns:
            profile['columns'][col] = self._profile_column(df[col], profile['column_types'].get(col, 'unknown'))
        
        return profile
    
    def _get_dataset_info(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Get basic dataset information.
        
        Args:
            df (pd.DataFrame): DataFrame to analyze
            
        Returns:
            Dict[str, Any]: Basic dataset information
        """
        return {
            'row_count': len(df),
            'column_count': len(df.columns),
            'memory_usage_mb': round(df.memory_usage(deep=True).sum() / (1024 * 1024), 2),
            'total_cells': df.size,
            'missing_cells': df.isna().sum().sum(),
            'missing_percentage': round((df.isna().sum().sum() / df.size) * 100, 2),
            'duplicate_rows': df.duplicated().sum(),
            'shape': df.shape
        }
    
    def _infer_column_types(self, df: pd.DataFrame) -> Dict[str, str]:
        """
        Infer semantic data types for each column.
        
        Args:
            df (pd.DataFrame): DataFrame to analyze
            
        Returns:
            Dict[str, str]: Column name to inferred type mapping
        """
        column_types = {}
        
        for col in df.columns:
            series = df[col].dropna()
            
            if len(series) == 0:
                column_types[col] = 'empty'
                continue
            
            # Check for boolean types first (before numeric check)
            if pd.api.types.is_bool_dtype(series) or series.dtype == 'bool':
                column_types[col] = 'boolean'
                continue
            
            # Check if it's a boolean column with string representations
            if self._is_boolean_column(series.astype(str) if series.dtype == 'object' else series):
                column_types[col] = 'boolean'
                continue
            
            # Check for numeric types
            if pd.api.types.is_numeric_dtype(series):
                # Additional check to ensure it's not actually boolean data stored as 0/1
                unique_vals = set(series.unique())
                if unique_vals.issubset({0, 1, 0.0, 1.0}) and len(unique_vals) <= 2:
                    column_types[col] = 'boolean'
                elif pd.api.types.is_integer_dtype(series):
                    column_types[col] = 'integer'
                elif pd.api.types.is_float_dtype(series):
                    column_types[col] = 'float'
                else:
                    column_types[col] = 'numeric'
                continue
            
            # Convert to string for pattern analysis
            string_series = series.astype(str)
            
            # Check for datetime
            if self._is_datetime_column(string_series):
                column_types[col] = 'datetime'
                continue
            
            # Check for email
            if self._is_email_column(string_series):
                column_types[col] = 'email'
                continue
            
            # Check for phone numbers
            if self._is_phone_column(string_series):
                column_types[col] = 'phone'
                continue
            
            # Check for URLs
            if self._is_url_column(string_series):
                column_types[col] = 'url'
                continue
            
            # Check for categorical (limited unique values)
            unique_ratio = len(series.unique()) / len(series)
            unique_count = len(series.unique())
            
            if (unique_ratio < self.categorical_threshold and 
                unique_count < self.max_categorical_values and 
                unique_count > 1):
                column_types[col] = 'categorical'
                continue
            
            # Check for ID-like columns
            if self._is_id_column(string_series, col):
                column_types[col] = 'identifier'
                continue
            
            # Check if it's numeric but stored as string
            if self._is_numeric_string(string_series):
                column_types[col] = 'numeric_string'
                continue
            
            # Default to text
            column_types[col] = 'text'
        
        return column_types
    
    def _is_datetime_column(self, series: pd.Series) -> bool:
        """Check if column contains datetime values."""
        # Take a sample for testing
        sample_size = min(100, len(series))
        sample = series.head(sample_size)
        
        # Try predefined date formats
        for date_format in self.date_formats:
            try:
                pd.to_datetime(sample, format=date_format, errors='raise')
                return True
            except (ValueError, TypeError):
                continue
        
        # Try automatic parsing
        try:
            parsed = pd.to_datetime(sample, errors='coerce', infer_datetime_format=True)
            success_rate = parsed.notna().sum() / len(sample)
            return success_rate > 0.8
        except (ValueError, TypeError):
            return False
    
    def _is_boolean_column(self, series: pd.Series) -> bool:
        """Check if column contains boolean values."""
        # Handle empty series
        if len(series) == 0:
            return False
            
        # If it's already boolean dtype, return True
        if series.dtype == 'bool':
            return True
            
        # Convert to string and get unique values
        try:
            unique_vals = set(str(val).lower().strip() for val in series.unique())
            
            # Remove nan/null values
            unique_vals.discard('nan')
            unique_vals.discard('none')
            unique_vals.discard('')
            
            if len(unique_vals) == 0:
                return False
            
            boolean_patterns = [
                {'true', 'false'},
                {'yes', 'no'},
                {'1', '0'},
                {'y', 'n'},
                {'t', 'f'},
                {'on', 'off'},
                {'enabled', 'disabled'},
                {'active', 'inactive'},
                {'1.0', '0.0'}
            ]
            
            # Check if unique values match any boolean pattern
            for pattern in boolean_patterns:
                if unique_vals.issubset(pattern) and len(unique_vals) > 0:
                    return True
                    
            return False
            
        except Exception:
            return False
    
    def _is_email_column(self, series: pd.Series) -> bool:
        """Check if column contains email addresses."""
        if not self.email_pattern:
            return False
        
        sample_size = min(50, len(series))
        sample = series.head(sample_size)
        
        matches = sum(1 for val in sample if re.match(self.email_pattern, str(val)))
        return (matches / len(sample)) > 0.8
    
    def _is_phone_column(self, series: pd.Series) -> bool:
        """Check if column contains phone numbers."""
        if not self.phone_pattern:
            return False
        
        sample_size = min(50, len(series))
        sample = series.head(sample_size)
        
        matches = sum(1 for val in sample if re.match(self.phone_pattern, str(val)))
        return (matches / len(sample)) > 0.8
    
    def _is_url_column(self, series: pd.Series) -> bool:
        """Check if column contains URLs."""
        if not self.url_pattern:
            return False
        
        sample_size = min(50, len(series))
        sample = series.head(sample_size)
        
        matches = sum(1 for val in sample if re.match(self.url_pattern, str(val)))
        return (matches / len(sample)) > 0.8
    
    def _is_id_column(self, series: pd.Series, column_name: str) -> bool:
        """Check if column appears to be an identifier."""
        # Check column name patterns
        id_name_patterns = ['id', 'key', 'identifier', 'uuid', 'guid']
        if any(pattern in column_name.lower() for pattern in id_name_patterns):
            return True
        
        # Check uniqueness (IDs should be mostly unique)
        unique_ratio = len(series.unique()) / len(series)
        if unique_ratio > 0.95:
            # Check if values look like IDs (alphanumeric, specific patterns)
            sample = series.head(min(20, len(series)))
            id_like_count = 0
            
            for val in sample:
                val_str = str(val)
                # Check for common ID patterns
                if (val_str.isalnum() or 
                    '-' in val_str or 
                    '_' in val_str or 
                    len(val_str) > 8):
                    id_like_count += 1
            
            return (id_like_count / len(sample)) > 0.8
        
        return False
    
    def _is_numeric_string(self, series: pd.Series) -> bool:
        """Check if column contains numeric values stored as strings."""
        sample = series.head(min(100, len(series)))
        
        numeric_count = 0
        for val in sample:
            try:
                # Try to convert to float
                float(str(val).replace(',', '').replace('$', '').replace('%', ''))
                numeric_count += 1
            except (ValueError, TypeError):
                pass
        
        return (numeric_count / len(sample)) > 0.8
    
    def _profile_column(self, series: pd.Series, col_type: str) -> Dict[str, Any]:
        """
        Generate comprehensive profile for a single column.
        
        Args:
            series (pd.Series): Column to profile
            col_type (str): Inferred column type
            
        Returns:
            Dict[str, Any]: Column profile
        """
        profile = {
            'name': series.name,
            'type': col_type,
            'dtype': str(series.dtype),
            'non_null_count': int(series.notna().sum()),
            'null_count': int(series.isna().sum()),
            'null_percentage': round((series.isna().sum() / len(series)) * 100, 2),
            'unique_count': int(series.nunique()),
            'unique_percentage': round((series.nunique() / len(series)) * 100, 2),
            'memory_usage': series.memory_usage(deep=True)
        }
        
        # Add type-specific profiling with safety checks
        try:
            # Check actual dtype vs inferred type for safety
            actual_dtype = series.dtype
            
            # Handle boolean data types specifically
            if actual_dtype == 'bool' or col_type == 'boolean':
                profile.update(self._profile_boolean_column(series))
            elif col_type in ['integer', 'float', 'numeric', 'numeric_string'] and actual_dtype != 'bool':
                profile.update(self._profile_numeric_column(series))
            elif col_type in ['text', 'categorical', 'identifier']:
                profile.update(self._profile_text_column(series))
            elif col_type == 'datetime':
                profile.update(self._profile_datetime_column(series))
            elif col_type in ['email', 'phone', 'url']:
                profile.update(self._profile_text_column(series))
                profile.update(self._profile_pattern_column(series, col_type))
            else:
                # Default to text profiling for unknown types
                profile.update(self._profile_text_column(series))
                
        except Exception as e:
            profile['profiling_error'] = f"Error profiling column: {str(e)}"
            # Add basic text profiling as fallback
            try:
                profile.update(self._profile_text_column(series))
            except Exception:
                pass
        
        return profile
    
    def _profile_numeric_column(self, series: pd.Series) -> Dict[str, Any]:
        """Profile numeric column with statistical measures."""
        # Handle boolean columns that might be misclassified as numeric
        if series.dtype == 'bool':
            return self._profile_boolean_column(series)
        
        # Convert to numeric if it's stored as string
        if series.dtype == 'object':
            numeric_series = pd.to_numeric(series, errors='coerce')
        else:
            # Ensure we're working with numeric data
            try:
                numeric_series = pd.to_numeric(series, errors='coerce')
            except (ValueError, TypeError):
                numeric_series = series
        
        # Remove NaN values for calculations
        clean_series = numeric_series.dropna()
        
        if len(clean_series) == 0:
            return {'error': 'No valid numeric values'}
        
        # Additional check to ensure we have numeric data
        if clean_series.dtype == 'bool':
            return self._profile_boolean_column(series)
        
        # Ensure we have at least one numeric value
        try:
            test_val = float(clean_series.iloc[0])
        except (ValueError, TypeError):
            return {'error': 'Data cannot be converted to numeric'}
        
        try:
            min_val = clean_series.min()
            max_val = clean_series.max()
            mean_val = clean_series.mean()
            median_val = clean_series.median()
            
            profile = {
                'min': float(min_val) if pd.notna(min_val) else None,
                'max': float(max_val) if pd.notna(max_val) else None,
                'mean': float(mean_val) if pd.notna(mean_val) else None,
                'median': float(median_val) if pd.notna(median_val) else None,
                'std': float(clean_series.std()) if len(clean_series) > 1 and pd.notna(clean_series.std()) else 0.0,
                'variance': float(clean_series.var()) if len(clean_series) > 1 and pd.notna(clean_series.var()) else 0.0,
                'skewness': float(clean_series.skew()) if len(clean_series) > 1 and pd.notna(clean_series.skew()) else 0.0,
                'kurtosis': float(clean_series.kurtosis()) if len(clean_series) > 1 and pd.notna(clean_series.kurtosis()) else 0.0,
                'q25': float(clean_series.quantile(0.25)) if pd.notna(clean_series.quantile(0.25)) else None,
                'q75': float(clean_series.quantile(0.75)) if pd.notna(clean_series.quantile(0.75)) else None,
                'zeros_count': int((clean_series == 0).sum()),
                'negative_count': int((clean_series < 0).sum()),
                'positive_count': int((clean_series > 0).sum())
            }
            
            # Calculate IQR and range safely
            if profile['q75'] is not None and profile['q25'] is not None:
                profile['iqr'] = float(profile['q75'] - profile['q25'])
            else:
                profile['iqr'] = None
                
            if profile['max'] is not None and profile['min'] is not None:
                profile['range'] = float(profile['max'] - profile['min'])
            else:
                profile['range'] = None
                
        except Exception as e:
            return {'error': f'Error calculating numeric statistics: {str(e)}'}
        
        # Add percentiles
        percentiles = [1, 5, 10, 90, 95, 99]
        for p in percentiles:
            profile[f'p{p}'] = float(clean_series.quantile(p/100))
        
        return profile
    
    def _profile_text_column(self, series: pd.Series) -> Dict[str, Any]:
        """Profile text column with string-specific measures."""
        text_series = series.astype(str)
        
        # Remove NaN values for text analysis
        clean_series = series.dropna()
        clean_text_series = clean_series.astype(str)
        
        if len(clean_text_series) == 0:
            return {'error': 'No valid text values'}
        
        # Calculate length statistics
        lengths = clean_text_series.str.len()
        
        profile = {
            'min_length': int(lengths.min()),
            'max_length': int(lengths.max()),
            'mean_length': round(lengths.mean(), 2),
            'median_length': int(lengths.median()),
            'std_length': round(lengths.std(), 2) if len(lengths) > 1 else 0.0,
            'empty_strings': int((clean_text_series == '').sum()),
            'whitespace_only': int(clean_text_series.str.strip().eq('').sum()),
            'most_common': clean_series.value_counts().head(5).to_dict(),
            'least_common': clean_series.value_counts().tail(5).to_dict()
        }
        
        # Character analysis
        profile.update(self._analyze_text_patterns(clean_text_series))
        
        return profile
    
    def _profile_datetime_column(self, series: pd.Series) -> Dict[str, Any]:
        """Profile datetime column."""
        # Convert to datetime
        try:
            dt_series = pd.to_datetime(series, errors='coerce')
            clean_dt_series = dt_series.dropna()
            
            if len(clean_dt_series) == 0:
                return {'error': 'No valid datetime values'}
            
            # Calculate date range safely
            min_date = clean_dt_series.min()
            max_date = clean_dt_series.max()
            
            # Calculate date range in days
            date_range_days = 0
            if len(clean_dt_series) > 1 and pd.notna(min_date) and pd.notna(max_date):
                try:
                    date_range_days = (max_date - min_date).days
                except (TypeError, AttributeError):
                    date_range_days = 0
            
            profile = {
                'min_date': min_date.isoformat() if pd.notna(min_date) else None,
                'max_date': max_date.isoformat() if pd.notna(max_date) else None,
                'date_range_days': date_range_days,
                'unique_dates': int(clean_dt_series.nunique()),
                'year_range': list(clean_dt_series.dt.year.unique()) if len(clean_dt_series) > 0 else [],
                'month_distribution': clean_dt_series.dt.month.value_counts().to_dict(),
                'day_of_week_distribution': clean_dt_series.dt.dayofweek.value_counts().to_dict()
            }
            
            # Add hour distribution only if we have time components
            if clean_dt_series.dt.hour.nunique() > 1:
                profile['hour_distribution'] = clean_dt_series.dt.hour.value_counts().to_dict()
            else:
                profile['hour_distribution'] = {}
            
            return profile
            
        except Exception as e:
            return {'error': f'Datetime analysis failed: {str(e)}'}
    
    def _profile_boolean_column(self, series: pd.Series) -> Dict[str, Any]:
        """Profile boolean column."""
        clean_series = series.dropna()
        
        if len(clean_series) == 0:
            return {'error': 'No valid boolean values'}
        
        try:
            # Handle different boolean representations
            if clean_series.dtype == 'bool':
                # Native boolean type
                value_counts = clean_series.value_counts()
                true_count = int(value_counts.get(True, 0))
                false_count = int(value_counts.get(False, 0))
            else:
                # String or numeric boolean representations
                clean_series_str = clean_series.astype(str).str.lower().str.strip()
                
                # Map common boolean representations to True/False
                true_values = {'true', 'yes', '1', 'y', 't', 'on', 'enabled', 'active', '1.0'}
                false_values = {'false', 'no', '0', 'n', 'f', 'off', 'disabled', 'inactive', '0.0'}
                
                true_count = sum(1 for val in clean_series_str if val in true_values)
                false_count = sum(1 for val in clean_series_str if val in false_values)
                
                # Handle cases where values don't match expected boolean patterns
                other_count = len(clean_series_str) - true_count - false_count
                if other_count > 0:
                    # Treat as text column instead
                    return self._profile_text_column(series)
            
            total_count = true_count + false_count
            
            if total_count == 0:
                return {'error': 'No recognizable boolean values'}
            
            return {
                'true_count': true_count,
                'false_count': false_count,
                'true_percentage': round((true_count / total_count) * 100, 2) if total_count > 0 else 0,
                'false_percentage': round((false_count / total_count) * 100, 2) if total_count > 0 else 0,
                'value_distribution': {
                    'true_values': true_count,
                    'false_values': false_count
                }
            }
            
        except Exception as e:
            return {'error': f'Boolean profiling failed: {str(e)}'}
    
    def _profile_pattern_column(self, series: pd.Series, pattern_type: str) -> Dict[str, Any]:
        """Profile column with specific patterns (email, phone, url)."""
        clean_series = series.dropna().astype(str)
        
        if len(clean_series) == 0:
            return {'error': 'No valid values for pattern analysis'}
        
        profile = {
            'pattern_type': pattern_type,
            'pattern_compliance': self._check_pattern_compliance(clean_series, pattern_type)
        }
        
        if pattern_type == 'email':
            profile.update(self._analyze_email_patterns(clean_series))
        elif pattern_type == 'phone':
            profile.update(self._analyze_phone_patterns(clean_series))
        elif pattern_type == 'url':
            profile.update(self._analyze_url_patterns(clean_series))
        
        return profile
    
    def _analyze_text_patterns(self, series: pd.Series) -> Dict[str, Any]:
        """Analyze patterns in text data."""
        if len(series) == 0:
            return {}
        
        # Character type analysis
        alpha_count = series.str.count(r'[a-zA-Z]').sum()
        digit_count = series.str.count(r'\d').sum()
        space_count = series.str.count(r'\s').sum()
        special_count = series.str.count(r'[^a-zA-Z0-9\s]').sum()
        
        total_chars = alpha_count + digit_count + space_count + special_count
        
        if total_chars == 0:
            return {}
        
        return {
            'character_distribution': {
                'alphabetic_percentage': round((alpha_count / total_chars) * 100, 2),
                'numeric_percentage': round((digit_count / total_chars) * 100, 2),
                'whitespace_percentage': round((space_count / total_chars) * 100, 2),
                'special_char_percentage': round((special_count / total_chars) * 100, 2)
            },
            'case_analysis': {
                'uppercase_count': int(series.str.isupper().sum()),
                'lowercase_count': int(series.str.islower().sum()),
                'titlecase_count': int(series.str.istitle().sum()),
                'mixed_case_count': int((~series.str.isupper() & ~series.str.islower() & ~series.str.istitle()).sum())
            },
            'common_words': self._get_common_words(series),
            'starts_with_digit': int(series.str.match(r'^\d').sum()),
            'contains_special_chars': int(series.str.contains(r'[^a-zA-Z0-9\s]').sum())
        }
    
    def _get_common_words(self, series: pd.Series, top_n: int = 10) -> Dict[str, int]:
        """Get most common words across all text values."""
        try:
            # Combine all text and split into words
            all_text = ' '.join(series.astype(str).tolist())
            words = re.findall(r'\b[a-zA-Z]+\b', all_text.lower())
            
            if not words:
                return {}
            
            from collections import Counter
            word_counts = Counter(words)
            return dict(word_counts.most_common(top_n))
        except Exception:
            return {}
    
    def _check_pattern_compliance(self, series: pd.Series, pattern_type: str) -> Dict[str, Any]:
        """Check how well the series matches the expected pattern."""
        pattern_map = {
            'email': self.email_pattern,
            'phone': self.phone_pattern,
            'url': self.url_pattern
        }
        
        pattern = pattern_map.get(pattern_type)
        if not pattern:
            return {'error': f'No pattern defined for {pattern_type}'}
        
        matches = series.str.match(pattern, na=False).sum()
        total = len(series)
        
        return {
            'matches': int(matches),
            'total': total,
            'compliance_percentage': round((matches / total) * 100, 2) if total > 0 else 0,
            'non_compliant_samples': series[~series.str.match(pattern, na=False)].head(5).tolist()
        }
    
    def _analyze_email_patterns(self, series: pd.Series) -> Dict[str, Any]:
        """Analyze email-specific patterns."""
        # Extract domains
        domains = series.str.extract(r'@([^.]+\..*)', expand=False).dropna()
        
        return {
            'unique_domains': int(domains.nunique()),
            'top_domains': domains.value_counts().head(10).to_dict(),
            'domain_distribution': domains.value_counts().to_dict()
        }
    
    def _analyze_phone_patterns(self, series: pd.Series) -> Dict[str, Any]:
        """Analyze phone number patterns."""
        # Extract digits only
        digits_only = series.str.replace(r'[^\d]', '', regex=True)
        lengths = digits_only.str.len()
        
        return {
            'digit_length_distribution': lengths.value_counts().to_dict(),
            'most_common_length': int(lengths.mode().iloc[0]) if len(lengths.mode()) > 0 else None,
            'has_country_code': int(series.str.contains(r'^\+').sum()),
            'formatting_patterns': series.str.extract(r'(\D+)', expand=False).value_counts().head(5).to_dict()
        }
    
    def _analyze_url_patterns(self, series: pd.Series) -> Dict[str, Any]:
        """Analyze URL patterns."""
        # Extract protocols and domains
        protocols = series.str.extract(r'^(https?)', expand=False).dropna()
        domains = series.str.extract(r'://([^/]+)', expand=False).dropna()
        
        return {
            'protocol_distribution': protocols.value_counts().to_dict(),
            'unique_domains': int(domains.nunique()),
            'top_domains': domains.value_counts().head(10).to_dict(),
            'has_query_params': int(series.str.contains(r'\?').sum()),
            'has_fragments': int(series.str.contains(r'#').sum())
        }
    
    def _calculate_correlations(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate correlations between numeric columns."""
        try:
            # Select only numeric columns
            numeric_df = df.select_dtypes(include=[np.number])
            
            if numeric_df.empty or len(numeric_df.columns) < 2:
                return {'message': 'Insufficient numeric columns for correlation analysis'}
            
            # Calculate correlation matrix
            corr_matrix = numeric_df.corr()
            
            # Find high correlations (> 0.7 or < -0.7)
            high_correlations = []
            for i in range(len(corr_matrix.columns)):
                for j in range(i+1, len(corr_matrix.columns)):
                    corr_value = corr_matrix.iloc[i, j]
                    if abs(corr_value) > 0.7:
                        high_correlations.append({
                            'column1': corr_matrix.columns[i],
                            'column2': corr_matrix.columns[j],
                            'correlation': round(corr_value, 3)
                        })
            
            return {
                'correlation_matrix': corr_matrix.round(3).to_dict(),
                'high_correlations': high_correlations,
                'numeric_columns_analyzed': list(numeric_df.columns)
            }
            
        except Exception as e:
            return {'error': f'Correlation analysis failed: {str(e)}'}
    
    def _get_summary_statistics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Get summary statistics for the entire dataset."""
        try:
            summary = {
                'numeric_summary': {},
                'text_summary': {},
                'missing_data_summary': {}
            }
            
            # Numeric summary
            numeric_df = df.select_dtypes(include=[np.number])
            if not numeric_df.empty:
                summary['numeric_summary'] = {
                    'column_count': len(numeric_df.columns),
                    'total_values': numeric_df.size,
                    'non_null_values': numeric_df.count().sum(),
                    'mean_of_means': round(numeric_df.mean().mean(), 3),
                    'columns': list(numeric_df.columns)
                }
            
            # Text summary
            text_df = df.select_dtypes(include=['object'])
            if not text_df.empty:
                total_length = text_df.astype(str).apply(lambda x: x.str.len().sum()).sum()
                summary['text_summary'] = {
                    'column_count': len(text_df.columns),
                    'total_characters': int(total_length),
                    'average_length_per_field': round(total_length / text_df.size, 2) if text_df.size > 0 else 0,
                    'columns': list(text_df.columns)
                }
            
            # Missing data summary
            missing_by_column = df.isnull().sum()
            summary['missing_data_summary'] = {
                'columns_with_missing': int((missing_by_column > 0).sum()),
                'total_missing_values': int(missing_by_column.sum()),
                'worst_columns': missing_by_column[missing_by_column > 0].sort_values(ascending=False).head(5).to_dict()
            }
            
            return summary
            
        except Exception as e:
            return {'error': f'Summary statistics failed: {str(e)}'}


if __name__ == "__main__":
    # Example usage
    from core.config_manager import ConfigManager
    import pandas as pd
    
    # Create sample data for testing
    sample_data = {
        'id': range(1, 101),
        'name': [f'Person {i}' for i in range(1, 101)],
        'email': [f'person{i}@example.com' for i in range(1, 101)],
        'age': np.random.randint(18, 65, 100),
        'salary': np.random.normal(50000, 15000, 100),
        'is_active': np.random.choice([True, False], 100),
        'date_joined': pd.date_range('2020-01-01', periods=100, freq='D')
    }
    
    df = pd.DataFrame(sample_data)
    
    config = ConfigManager()
    profiler = DataProfiler(config)
    
    profile = profiler.generate_profile(df)
    print("Profile generated successfully!")
    print(f"Dataset has {profile['dataset_info']['row_count']} rows and {profile['dataset_info']['column_count']} columns")