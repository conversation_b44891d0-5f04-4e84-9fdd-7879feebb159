"""
CSV Rule Loader for Validation Rules
Handles parsing and loading validation rules from CSV files
"""

import csv
import json
import re
from typing import Dict, List, Any, Optional
from pathlib import Path


class CSVRuleLoader:
    """Loads and parses validation rules from CSV files"""
    
    def __init__(self):
        self.supported_rule_types = {
            'not_null', 'range', 'regex', 'unique', 'data_type', 
            'length', 'enum', 'custom'
        }
        self.required_columns = {'column_name', 'rule_type', 'rule_config'}
        
    def load_rules_from_csv(self, csv_file_path: str, schema_registry_id: str) -> List[Dict[str, Any]]:
        """
        Load validation rules from CSV file
        
        Args:
            csv_file_path: Path to CSV file containing validation rules
            schema_registry_id: ID from schema_registry table to associate rules with
            
        Returns:
            List of validation rule dictionaries
        """
        if not Path(csv_file_path).exists():
            raise FileNotFoundError(f"CSV file not found: {csv_file_path}")
        
        rules = []
        
        with open(csv_file_path, 'r', newline='', encoding='utf-8') as csvfile:
            # Detect delimiter using the dedicated method
            delimiter = self._detect_delimiter(csvfile)
            
            reader = csv.DictReader(csvfile, delimiter=delimiter)
            
            # Validate CSV headers
            headers = set(reader.fieldnames)
            missing_columns = self.required_columns - headers
            if missing_columns:
                raise ValueError(f"Missing required columns: {missing_columns}")
            
            for row_num, row in enumerate(reader, start=2):  # Start at 2 for header row
                try:
                    rule = self._parse_rule_row(row, schema_registry_id, row_num)
                    if rule:
                        rules.append(rule)
                except Exception as e:
                    print(f"Warning: Skipping row {row_num} due to error: {e}")
        
        return rules
 
    def _parse_rule_row(self, row: Dict[str, str], schema_registry_id: str, row_num: int) -> Optional[Dict[str, Any]]:
        """Parse a single CSV row into a validation rule"""
        
        # Clean and validate required fields
        column_name = row['column_name'].strip()
        rule_type = row['rule_type'].strip().lower()
        rule_config_str = row['rule_config'].strip()
        
        if not column_name:
            raise ValueError(f"Column name cannot be empty")
        
        if rule_type not in self.supported_rule_types:
            raise ValueError(f"Unsupported rule type: {rule_type}. Supported types: {self.supported_rule_types}")
        
        # Parse rule configuration JSON
        try:
            if rule_config_str == '{}' or not rule_config_str:
                rule_config = {}
            else:
                rule_config = json.loads(rule_config_str)
                if not isinstance(rule_config, dict):
                    raise ValueError("Rule config must be a JSON object")
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in rule_config: {e}")
        
        # Validate rule configuration
        self._validate_rule_config(rule_type, rule_config)
        
        # Parse optional fields
        rule_description = row.get('rule_description', '').strip()
        is_active = self._parse_boolean(row.get('is_active', 'true'), default=True)
        
        return {
            'schema_registry_id': schema_registry_id,
            'column_name': column_name,
            'rule_type': rule_type,
            'rule_config': rule_config,
            'rule_description': rule_description,
            'is_active': is_active
        }
    
    def _validate_rule_config(self, rule_type: str, rule_config: Dict[str, Any]) -> None:
        """Validate rule configuration based on rule type"""
        
        if rule_type == 'range':
            if 'min' not in rule_config and 'max' not in rule_config:
                raise ValueError("Range rule must specify at least 'min' or 'max'")
            
            if 'min' in rule_config and 'max' in rule_config:
                if rule_config['min'] >= rule_config['max']:
                    raise ValueError("Range min must be less than max")
        
        elif rule_type == 'regex':
            if 'pattern' not in rule_config:
                raise ValueError("Regex rule must specify 'pattern'")
            
            # Validate regex pattern
            try:
                re.compile(rule_config['pattern'])
            except re.error as e:
                raise ValueError(f"Invalid regex pattern: {e}")
        
        elif rule_type == 'length':
            if 'min' not in rule_config and 'max' not in rule_config:
                raise ValueError("Length rule must specify at least 'min' or 'max'")
            
            if 'min' in rule_config and rule_config['min'] < 0:
                raise ValueError("Length min cannot be negative")
            
            if 'max' in rule_config and rule_config['max'] < 0:
                raise ValueError("Length max cannot be negative")
            
            if 'min' in rule_config and 'max' in rule_config:
                if rule_config['min'] > rule_config['max']:
                    raise ValueError("Length min cannot be greater than max")
        
        elif rule_type == 'enum':
            if 'values' not in rule_config:
                raise ValueError("Enum rule must specify 'values' list")
            
            if not isinstance(rule_config['values'], list):
                raise ValueError("Enum 'values' must be a list")
            
            if len(rule_config['values']) == 0:
                raise ValueError("Enum 'values' cannot be empty")
        
        elif rule_type == 'data_type':
            valid_types = {'string', 'integer', 'float', 'boolean', 'date', 'datetime'}
            if 'type' not in rule_config:
                raise ValueError("Data type rule must specify 'type'")
            
            if rule_config['type'] not in valid_types:
                raise ValueError(f"Invalid data type. Must be one of: {valid_types}")
        
        elif rule_type == 'custom':
            if 'expression' not in rule_config:
                raise ValueError("Custom rule must specify 'expression'")
    
    def _parse_boolean(self, value: str, default: bool = False) -> bool:
        """Parse string to boolean"""
        if not value:
            return default
        
        value = value.strip().lower()
        if value in ('true', '1', 'yes', 'y', 'on'):
            return True
        elif value in ('false', '0', 'no', 'n', 'off'):
            return False
        else:
            return default
    
    def validate_csv_format(self, csv_file_path: str) -> Dict[str, Any]:
        """
        Validate CSV file format without loading rules
        
        Returns:
            Dictionary with validation results
        """
        result = {
            'valid': False,
            'errors': [],
            'warnings': [],
            'row_count': 0,
            'columns': []
        }
        
        try:
            if not Path(csv_file_path).exists():
                result['errors'].append(f"File not found: {csv_file_path}")
                return result
            
            with open(csv_file_path, 'r', newline='', encoding='utf-8') as csvfile:
                # Detect delimiter with fallbacks
                delimiter = self._detect_delimiter(csvfile)
                
                reader = csv.DictReader(csvfile, delimiter=delimiter)
                
                # Check headers
                headers = set(reader.fieldnames) if reader.fieldnames else set()
                result['columns'] = list(headers)
                
                missing_columns = self.required_columns - headers
                if missing_columns:
                    result['errors'].append(f"Missing required columns: {missing_columns}")
                
                # Validate rows
                for row_num, row in enumerate(reader, start=2):
                    result['row_count'] += 1
                    try:
                        self._validate_row_basic(row, row_num)
                    except Exception as e:
                        result['errors'].append(f"Row {row_num}: {str(e)}")
                
                if not result['errors']:
                    result['valid'] = True
                
        except Exception as e:
            result['errors'].append(f"File processing error: {str(e)}")
        
        return result
    
    def _validate_row_basic(self, row: Dict[str, str], row_num: int) -> None:
        """Basic validation of a CSV row"""
        column_name = row.get('column_name', '').strip()
        rule_type = row.get('rule_type', '').strip().lower()
        rule_config_str = row.get('rule_config', '').strip()
        
        if not column_name:
            raise ValueError("Column name cannot be empty")
        
        if rule_type not in self.supported_rule_types:
            raise ValueError(f"Unsupported rule type: {rule_type}")
        
        # Basic JSON validation
        if rule_config_str and rule_config_str != '{}':
            try:
                json.loads(rule_config_str)
            except json.JSONDecodeError:
                raise ValueError("Invalid JSON in rule_config")
    
    def _detect_delimiter(self, csvfile) -> str:
        """
        Detect CSV delimiter with fallbacks
        
        Args:
            csvfile: Open file object positioned at start
            
        Returns:
            Detected delimiter character
        """
        try:
            # Read sample for delimiter detection
            sample = csvfile.read(1024)
            csvfile.seek(0)  # Reset file position
            
            # Use csv.Sniffer to detect delimiter
            sniffer = csv.Sniffer()
            delimiter = sniffer.sniff(sample).delimiter
            
            return delimiter
            
        except Exception:
            # Fallback to common delimiters if sniffing fails
            csvfile.seek(0)
            sample = csvfile.read(1024)
            csvfile.seek(0)
            
            # Count occurrences of common delimiters
            delimiters = [',', ';', '\t', '|']
            delimiter_counts = {}
            
            for delimiter in delimiters:
                delimiter_counts[delimiter] = sample.count(delimiter)
            
            # Return delimiter with highest count, default to comma
            if any(count > 0 for count in delimiter_counts.values()):
                return max(delimiter_counts, key=delimiter_counts.get)
            else:
                return ','  # Default fallback
        
    def generate_sample_csv(self, output_path: str) -> None:
        """Generate a sample CSV file with validation rules examples"""
        
        sample_rules = [
            {
                'column_name': 'customer_id',
                'rule_type': 'not_null',
                'rule_config': '{}',
                'rule_description': 'Customer ID cannot be null',
                'is_active': 'true'
            },
            {
                'column_name': 'age',
                'rule_type': 'range',
                'rule_config': '{"min": 18, "max": 120}',
                'rule_description': 'Age must be between 18 and 120',
                'is_active': 'true'
            },
            {
                'column_name': 'email',
                'rule_type': 'regex',
                'rule_config': '{"pattern": "^[\\\\w\\\\.-]+@[\\\\w\\\\.-]+\\\\.[a-zA-Z]{2,}$"}',
                'rule_description': 'Valid email format required',
                'is_active': 'true'
            },
            {
                'column_name': 'phone',
                'rule_type': 'length',
                'rule_config': '{"min": 10, "max": 15}',
                'rule_description': 'Phone number must be 10-15 characters',
                'is_active': 'true'
            },
            {
                'column_name': 'status',
                'rule_type': 'enum',
                'rule_config': '{"values": ["active", "inactive", "pending"]}',
                'rule_description': 'Status must be one of the allowed values',
                'is_active': 'true'
            },
            {
                'column_name': 'salary',
                'rule_type': 'data_type',
                'rule_config': '{"type": "float"}',
                'rule_description': 'Salary must be a valid float',
                'is_active': 'true'
            },
            {
                'column_name': 'employee_id',
                'rule_type': 'unique',
                'rule_config': '{}',
                'rule_description': 'Employee ID must be unique',
                'is_active': 'true'
            }
        ]
        
        with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=[
                'column_name', 'rule_type', 'rule_config', 
                'rule_description', 'is_active'
            ])
            
            writer.writeheader()
            writer.writerows(sample_rules)
        
        print(f"Sample CSV file generated: {output_path}")


# Example usage
if __name__ == "__main__":
    loader = CSVRuleLoader()
    
    # Generate sample CSV
    sample_path = "sample_validation_rules.csv"
    loader.generate_sample_csv(sample_path)
    
    # Validate the sample CSV
    validation_result = loader.validate_csv_format(sample_path)
    print(f"Validation result: {validation_result}")
    
    # Load rules (assuming we have a schema_registry_id)
    if validation_result['valid']:
        try:
            rules = loader.load_rules_from_csv(sample_path, "sample-schema-id")
            print(f"Loaded {len(rules)} validation rules")
            for rule in rules:
                print(f"  - {rule['column_name']}: {rule['rule_type']}")
        except Exception as e:
            print(f"Error loading rules: {e}")