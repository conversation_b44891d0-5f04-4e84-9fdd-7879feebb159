"""
Data Validation System

A comprehensive data validation system that allows users to define custom validation rules
and execute them against incoming data files.

Main Components:
- ValidationRuleManager: Main orchestrator for validation operations
- ValidationEngine: Executes validation rules against data files
- ReportGenerator: Generates validation reports in multiple formats
- CSVRuleLoader: Loads validation rules from CSV files
- DuckDBManager: Extended database manager with validation functionality

Usage:
    from validation import ValidationRuleManager
    
    # Initialize manager
    manager = ValidationRuleManager()
    
    # Define validation rules from CSV
    result = manager.define_validation_rules(
        csv_file_path="rules.csv",
        pipeline_id="PIPELINE_001",
        normalized_file_name="data_normalized.csv"
    )
    
    # Validate a data file
    result = manager.validate_file(
        pipeline_id="PIPELINE_001",
        file_path="data.csv",
        normalized_file_name="data_normalized.csv"
    )
    
    # Generate validation report
    report = manager.generate_validation_report(
        execution_id=result['execution_id'],
        format_type='html',
        output_path='report.html'
    )
    
    # Cleanup
    manager.close()
"""


# Import main classes for easy access
try:
    from .validation_rule_manager import ValidationRuleManager
    from .validation_engine import ValidationEngine
    from .report_generator import ReportGenerator
    from .csv_rule_loader import CSVRuleLoader
    
    __all__ = [
        'ValidationRuleManager',
        'ValidationEngine', 
        'ReportGenerator',
        'CSVRuleLoader'
    ]
    
except ImportError as e:
    # Handle import errors gracefully during development
    import warnings
    warnings.warn(f"Could not import validation components: {e}")
    __all__ = []

# Supported validation rule types
SUPPORTED_RULE_TYPES = {
    'not_null',
    'range', 
    'regex',
    'unique',
    'data_type',
    'length',
    'enum',
    'custom'
}

# Supported report formats
SUPPORTED_REPORT_FORMATS = {
    'json',
    'csv', 
    'html'
}

# Supported data file formats
SUPPORTED_DATA_FORMATS = {
    '.csv',
    '.xlsx',
    '.xls', 
    '.json',
    '.parquet'
}