"""
Validation Engine for Data File Validation
Executes validation rules against incoming data files
"""

import pandas as pd
import re
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path
import numpy as np


class ValidationEngine:
    """Executes validation rules against data files"""
    
    def __init__(self):
        self.validation_functions = {
            'not_null': self._validate_not_null,
            'range': self._validate_range,
            'regex': self._validate_regex,
            'unique': self._validate_unique,
            'data_type': self._validate_data_type,
            'length': self._validate_length,
            'enum': self._validate_enum,
            'custom': self._validate_custom
        }
    
    def validate_file(self, file_path: str, validation_rules: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Validate a data file against validation rules
        
        Args:
            file_path: Path to the data file to validate
            validation_rules: List of validation rules to apply
            
        Returns:
            Dictionary containing validation results
        """
        start_time = datetime.now()
        
        try:
            # Load data file
            df = self._load_data_file(file_path)
            total_records = len(df)
            
            # Initialize results
            validation_results = {
                'file_path': file_path,
                'total_records': total_records,
                'total_rules_executed': 0,
                'overall_status': 'PASSED',
                'execution_duration_ms': 0,
                'rule_results': [],
                'summary': {
                    'passed_rules': 0,
                    'failed_rules': 0,
                    'warning_rules': 0
                }
            }
            
            # Validate each rule
            for rule in validation_rules:
                if not rule.get('is_active', True):
                    continue
                
                rule_result = self._execute_rule(df, rule)
                validation_results['rule_results'].append(rule_result)
                validation_results['total_rules_executed'] += 1
                
                # Update summary
                status = rule_result['status']
                if status == 'PASSED':
                    validation_results['summary']['passed_rules'] += 1
                elif status == 'FAILED':
                    validation_results['summary']['failed_rules'] += 1
                    validation_results['overall_status'] = 'FAILED'
                elif status == 'WARNING':
                    validation_results['summary']['warning_rules'] += 1
                    if validation_results['overall_status'] == 'PASSED':
                        validation_results['overall_status'] = 'WARNING'
            
            # Calculate execution time
            end_time = datetime.now()
            validation_results['execution_duration_ms'] = int((end_time - start_time).total_seconds() * 1000)
            
            return validation_results
            
        except Exception as e:
            end_time = datetime.now()
            return {
                'file_path': file_path,
                'total_records': 0,
                'total_rules_executed': 0,
                'overall_status': 'ERROR',
                'execution_duration_ms': int((end_time - start_time).total_seconds() * 1000),
                'error_message': str(e),
                'rule_results': [],
                'summary': {'passed_rules': 0, 'failed_rules': 0, 'warning_rules': 0}
            }
    
    def _load_data_file(self, file_path: str) -> pd.DataFrame:
        """Load data file into pandas DataFrame"""
        file_extension = Path(file_path).suffix.lower()
        
        if file_extension == '.csv':
            # Try to detect encoding and delimiter
            try:
                df = pd.read_csv(file_path, encoding='utf-8')
            except UnicodeDecodeError:
                df = pd.read_csv(file_path, encoding='latin-1')
        elif file_extension in ['.xlsx', '.xls']:
            df = pd.read_excel(file_path)
        elif file_extension == '.json':
            df = pd.read_json(file_path)
        elif file_extension == '.parquet':
            df = pd.read_parquet(file_path)
        else:
            raise ValueError(f"Unsupported file format: {file_extension}")
        
        return df
    
    def _execute_rule(self, df: pd.DataFrame, rule: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a single validation rule"""
        column_name = rule['column_name']
        rule_type = rule['rule_type']
        rule_config = rule['rule_config']
        
        # Check if column exists
        if column_name not in df.columns:
            return {
                'rule_id': rule.get('id', ''),
                'column_name': column_name,
                'rule_type': rule_type,
                'status': 'FAILED',
                'passed_records_count': 0,
                'failed_records_count': len(df),
                'failure_details': {
                    'error': f"Column '{column_name}' not found in data file",
                    'available_columns': list(df.columns)
                }
            }
        
        # Get validation function
        validation_func = self.validation_functions.get(rule_type)
        if not validation_func:
            return {
                'rule_id': rule.get('id', ''),
                'column_name': column_name,
                'rule_type': rule_type,
                'status': 'FAILED',
                'passed_records_count': 0,
                'failed_records_count': len(df),
                'failure_details': {
                    'error': f"Unsupported rule type: {rule_type}"
                }
            }
        
        # Execute validation
        try:
            return validation_func(df[column_name], rule, len(df))
        except Exception as e:
            return {
                'rule_id': rule.get('id', ''),
                'column_name': column_name,
                'rule_type': rule_type,
                'status': 'FAILED',
                'passed_records_count': 0,
                'failed_records_count': len(df),
                'failure_details': {
                    'error': f"Validation execution error: {str(e)}"
                }
            }
    
    def _validate_not_null(self, series: pd.Series, rule: Dict[str, Any], total_records: int) -> Dict[str, Any]:
        """Validate not null constraint"""
        null_mask = series.isnull() | (series == '') | (series == ' ')
        failed_count = null_mask.sum()
        passed_count = total_records - failed_count
        
        failure_details = {}
        if failed_count > 0:
            null_indices = series[null_mask].index.tolist()[:10]  # Show first 10 failures
            failure_details = {
                'null_record_indices': null_indices,
                'total_null_records': int(failed_count)
            }
        
        return {
            'rule_id': rule.get('id', ''),
            'column_name': rule['column_name'],
            'rule_type': rule['rule_type'],
            'status': 'PASSED' if failed_count == 0 else 'FAILED',
            'passed_records_count': int(passed_count),
            'failed_records_count': int(failed_count),
            'failure_details': failure_details
        }
    
    def _validate_range(self, series: pd.Series, rule: Dict[str, Any], total_records: int) -> Dict[str, Any]:
        """Validate numeric range constraint"""
        rule_config = rule['rule_config']
        min_val = rule_config.get('min')
        max_val = rule_config.get('max')
        
        # Convert to numeric, errors will be marked as failed
        numeric_series = pd.to_numeric(series, errors='coerce')
        
        # Check for conversion failures
        conversion_failures = numeric_series.isnull() & series.notnull()
        
        # Apply range validation
        if min_val is not None and max_val is not None:
            range_failures = (numeric_series < min_val) | (numeric_series > max_val)
        elif min_val is not None:
            range_failures = numeric_series < min_val
        elif max_val is not None:
            range_failures = numeric_series > max_val
        else:
            range_failures = pd.Series([False] * len(series), index=series.index)
        
        # Combine all failures
        all_failures = conversion_failures | range_failures
        failed_count = all_failures.sum()
        passed_count = total_records - failed_count
        
        failure_details = {}
        if failed_count > 0:
            failed_indices = series[all_failures].index.tolist()[:10]
            failure_details = {
                'failed_record_indices': failed_indices,
                'failed_values': series[all_failures].head(10).tolist(),
                'range_constraint': {'min': min_val, 'max': max_val},
                'conversion_failures': int(conversion_failures.sum()),
                'range_violations': int(range_failures.sum())
            }
        
        return {
            'rule_id': rule.get('id', ''),
            'column_name': rule['column_name'],
            'rule_type': rule['rule_type'],
            'status': 'PASSED' if failed_count == 0 else 'FAILED',
            'passed_records_count': int(passed_count),
            'failed_records_count': int(failed_count),
            'failure_details': failure_details
        }
    
    def _validate_regex(self, series: pd.Series, rule: Dict[str, Any], total_records: int) -> Dict[str, Any]:
        """Validate regex pattern constraint"""
        pattern = rule['rule_config']['pattern']
        
        try:
            compiled_pattern = re.compile(pattern)
        except re.error as e:
            return {
                'rule_id': rule.get('id', ''),
                'column_name': rule['column_name'],
                'rule_type': rule['rule_type'],
                'status': 'FAILED',
                'passed_records_count': 0,
                'failed_records_count': total_records,
                'failure_details': {'error': f"Invalid regex pattern: {e}"}
            }
        
        # Convert to string and check pattern
        string_series = series.astype(str)
        matches = string_series.str.match(compiled_pattern, na=False)
        failed_count = (~matches).sum()
        passed_count = total_records - failed_count
        
        failure_details = {}
        if failed_count > 0:
            failed_mask = ~matches
            failed_indices = series[failed_mask].index.tolist()[:10]
            failure_details = {
                'failed_record_indices': failed_indices,
                'failed_values': series[failed_mask].head(10).tolist(),
                'pattern': pattern
            }
        
        return {
            'rule_id': rule.get('id', ''),
            'column_name': rule['column_name'],
            'rule_type': rule['rule_type'],
            'status': 'PASSED' if failed_count == 0 else 'FAILED',
            'passed_records_count': int(passed_count),
            'failed_records_count': int(failed_count),
            'failure_details': failure_details
        }
    
    def _validate_unique(self, series: pd.Series, rule: Dict[str, Any], total_records: int) -> Dict[str, Any]:
        """Validate uniqueness constraint"""
        # Find duplicates
        duplicates = series.duplicated(keep=False)
        failed_count = duplicates.sum()
        passed_count = total_records - failed_count
        
        failure_details = {}
        if failed_count > 0:
            duplicate_values = series[duplicates].value_counts().head(10)
            failure_details = {
                'duplicate_values': duplicate_values.to_dict(),
                'total_duplicate_records': int(failed_count),
                'unique_duplicate_values': len(duplicate_values)
            }
        
        return {
            'rule_id': rule.get('id', ''),
            'column_name': rule['column_name'],
            'rule_type': rule['rule_type'],
            'status': 'PASSED' if failed_count == 0 else 'FAILED',
            'passed_records_count': int(passed_count),
            'failed_records_count': int(failed_count),
            'failure_details': failure_details
        }
    
    def _validate_data_type(self, series: pd.Series, rule: Dict[str, Any], total_records: int) -> Dict[str, Any]:
        """Validate data type constraint"""
        expected_type = rule['rule_config']['type']
        
        validation_functions = {
            'string': lambda x: pd.api.types.is_string_dtype(x) or x.dtype == 'object',
            'integer': self._is_integer_convertible,
            'float': self._is_float_convertible,
            'boolean': self._is_boolean_convertible,
            'date': self._is_date_convertible,
            'datetime': self._is_datetime_convertible
        }
        
        validation_func = validation_functions.get(expected_type)
        if not validation_func:
            return {
                'rule_id': rule.get('id', ''),
                'column_name': rule['column_name'],
                'rule_type': rule['rule_type'],
                'status': 'FAILED',
                'passed_records_count': 0,
                'failed_records_count': total_records,
                'failure_details': {'error': f"Unsupported data type: {expected_type}"}
            }
        
        # For string type, just check if all values can be converted to string
        if expected_type == 'string':
            failed_count = 0
            passed_count = total_records
        else:
            valid_mask = validation_func(series)
            failed_count = (~valid_mask).sum()
            passed_count = total_records - failed_count
        
        failure_details = {}
        if failed_count > 0:
            failed_mask = ~valid_mask if expected_type != 'string' else pd.Series([False] * len(series))
            failed_indices = series[failed_mask].index.tolist()[:10]
            failure_details = {
                'failed_record_indices': failed_indices,
                'failed_values': series[failed_mask].head(10).tolist(),
                'expected_type': expected_type
            }
        
        return {
            'rule_id': rule.get('id', ''),
            'column_name': rule['column_name'],
            'rule_type': rule['rule_type'],
            'status': 'PASSED' if failed_count == 0 else 'FAILED',
            'passed_records_count': int(passed_count),
            'failed_records_count': int(failed_count),
            'failure_details': failure_details
        }
    
    def _validate_length(self, series: pd.Series, rule: Dict[str, Any], total_records: int) -> Dict[str, Any]:
        """Validate string length constraint"""
        rule_config = rule['rule_config']
        min_length = rule_config.get('min')
        max_length = rule_config.get('max')
        
        # Convert to string and get lengths
        string_series = series.astype(str)
        lengths = string_series.str.len()
        
        # Apply length validation
        if min_length is not None and max_length is not None:
            length_failures = (lengths < min_length) | (lengths > max_length)
        elif min_length is not None:
            length_failures = lengths < min_length
        elif max_length is not None:
            length_failures = lengths > max_length
        else:
            length_failures = pd.Series([False] * len(series), index=series.index)
        
        failed_count = length_failures.sum()
        passed_count = total_records - failed_count
        
        failure_details = {}
        if failed_count > 0:
            failed_indices = series[length_failures].index.tolist()[:10]
            failed_lengths = lengths[length_failures].head(10).tolist()
            failure_details = {
                'failed_record_indices': failed_indices,
                'failed_values': series[length_failures].head(10).tolist(),
                'failed_lengths': failed_lengths,
                'length_constraint': {'min': min_length, 'max': max_length}
            }
        
        return {
            'rule_id': rule.get('id', ''),
            'column_name': rule['column_name'],
            'rule_type': rule['rule_type'],
            'status': 'PASSED' if failed_count == 0 else 'FAILED',
            'passed_records_count': int(passed_count),
            'failed_records_count': int(failed_count),
            'failure_details': failure_details
        }
    
    def _validate_enum(self, series: pd.Series, rule: Dict[str, Any], total_records: int) -> Dict[str, Any]:
        """Validate enumeration constraint"""
        allowed_values = set(rule['rule_config']['values'])
        
        # Check if values are in allowed set
        valid_mask = series.isin(allowed_values)
        failed_count = (~valid_mask).sum()
        passed_count = total_records - failed_count
        
        failure_details = {}
        if failed_count > 0:
            failed_mask = ~valid_mask
            failed_indices = series[failed_mask].index.tolist()[:10]
            invalid_values = series[failed_mask].value_counts().head(10)
            failure_details = {
                'failed_record_indices': failed_indices,
                'invalid_values': invalid_values.to_dict(),
                'allowed_values': list(allowed_values)
            }
        
        return {
            'rule_id': rule.get('id', ''),
            'column_name': rule['column_name'],
            'rule_type': rule['rule_type'],
            'status': 'PASSED' if failed_count == 0 else 'FAILED',
            'passed_records_count': int(passed_count),
            'failed_records_count': int(failed_count),
            'failure_details': failure_details
        }
    
    def _validate_custom(self, series: pd.Series, rule: Dict[str, Any], total_records: int) -> Dict[str, Any]:
        """Validate custom expression"""
        expression = rule['rule_config']['expression']
        
        try:
            # Simple eval-based validation (be cautious with this in production)
            # For safety, you might want to use a more restricted expression evaluator
            namespace = {'series': series, 'pd': pd, 'np': np}
            valid_mask = eval(expression, {"__builtins__": {}}, namespace)
            
            if not isinstance(valid_mask, pd.Series):
                valid_mask = pd.Series([bool(valid_mask)] * len(series), index=series.index)
            
            failed_count = (~valid_mask).sum()
            passed_count = total_records - failed_count
            
            failure_details = {}
            if failed_count > 0:
                failed_mask = ~valid_mask
                failed_indices = series[failed_mask].index.tolist()[:10]
                failure_details = {
                    'failed_record_indices': failed_indices,
                    'failed_values': series[failed_mask].head(10).tolist(),
                    'expression': expression
                }
            
            return {
                'rule_id': rule.get('id', ''),
                'column_name': rule['column_name'],
                'rule_type': rule['rule_type'],
                'status': 'PASSED' if failed_count == 0 else 'FAILED',
                'passed_records_count': int(passed_count),
                'failed_records_count': int(failed_count),
                'failure_details': failure_details
            }
            
        except Exception as e:
            return {
                'rule_id': rule.get('id', ''),
                'column_name': rule['column_name'],
                'rule_type': rule['rule_type'],
                'status': 'FAILED',
                'passed_records_count': 0,
                'failed_records_count': total_records,
                'failure_details': {'error': f"Custom expression error: {str(e)}"}
            }
    
    def _is_integer_convertible(self, series: pd.Series) -> pd.Series:
        """Check if series values can be converted to integer"""
        try:
            pd.to_numeric(series, errors='raise')
            numeric_series = pd.to_numeric(series, errors='coerce')
            return numeric_series.notna() & (numeric_series % 1 == 0)
        except:
            numeric_series = pd.to_numeric(series, errors='coerce')
            return numeric_series.notna() & (numeric_series % 1 == 0)
    
    def _is_float_convertible(self, series: pd.Series) -> pd.Series:
        """Check if series values can be converted to float"""
        numeric_series = pd.to_numeric(series, errors='coerce')
        return numeric_series.notna()
    
    def _is_boolean_convertible(self, series: pd.Series) -> pd.Series:
        """Check if series values can be converted to boolean"""
        boolean_values = {'true', 'false', '1', '0', 'yes', 'no', 'y', 'n'}
        string_series = series.astype(str).str.lower()
        return string_series.isin(boolean_values) | pd.api.types.is_bool_dtype(series)
    
    def _is_date_convertible(self, series: pd.Series) -> pd.Series:
        """Check if series values can be converted to date"""
        try:
            pd.to_datetime(series, errors='raise')
            return pd.Series([True] * len(series), index=series.index)
        except:
            date_series = pd.to_datetime(series, errors='coerce')
            return date_series.notna()
    
    def _is_datetime_convertible(self, series: pd.Series) -> pd.Series:
        """Check if series values can be converted to datetime"""
        return self._is_date_convertible(series)


# Example usage
if __name__ == "__main__":
    # Create sample data
    import tempfile
    import csv
    
    # Create sample CSV file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
        writer = csv.writer(f)
        writer.writerow(['customer_id', 'age', 'email', 'status'])
        writer.writerow(['1', '25', '<EMAIL>', 'active'])
        writer.writerow(['2', '30', '<EMAIL>', 'inactive'])
        writer.writerow(['3', '', 'invalid-email', 'active'])  # Missing age, invalid email
        writer.writerow(['1', '35', '<EMAIL>', 'pending'])  # Duplicate ID
        temp_file = f.name
    
    # Sample validation rules
    rules = [
        {
            'id': '1',
            'column_name': 'customer_id',
            'rule_type': 'not_null',
            'rule_config': {},
            'is_active': True
        },
        {
            'id': '2',
            'column_name': 'customer_id',
            'rule_type': 'unique',
            'rule_config': {},
            'is_active': True
        },
        {
            'id': '3',
            'column_name': 'age',
            'rule_type': 'range',
            'rule_config': {'min': 18, 'max': 120},
            'is_active': True
        },
        {
            'id': '4',
            'column_name': 'email',
            'rule_type': 'regex',
            'rule_config': {'pattern': r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'},
            'is_active': True
        }
    ]
    
    # Execute validation
    engine = ValidationEngine()
    results = engine.validate_file(temp_file, rules)
    
    print(f"Validation Results:")
    print(f"Overall Status: {results['overall_status']}")
    print(f"Total Records: {results['total_records']}")
    print(f"Rules Executed: {results['total_rules_executed']}")
    print(f"Summary: {results['summary']}")
    
    for rule_result in results['rule_results']:
        print(f"\nRule: {rule_result['column_name']} - {rule_result['rule_type']}")
        print(f"Status: {rule_result['status']}")
        print(f"Passed: {rule_result['passed_records_count']}, Failed: {rule_result['failed_records_count']}")
        if rule_result['failure_details']:
            print(f"Failures: {rule_result['failure_details']}")
    
    # Clean up
    Path(temp_file).unlink()
