"""
Report Generator for Validation Results
Generates comprehensive validation reports in multiple formats
"""

import json
import csv
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path
import pandas as pd
import sys
import os

# Add project root to Python path for imports
project_root = Path(__file__).resolve().parent.parent.parent
sys.path.insert(0, str(project_root))

from core.config_manager import ConfigManager


class ReportGenerator:
    """Generates validation reports in multiple formats"""
    
    def __init__(self):
        self.supported_formats = {'json', 'csv', 'html'}
        self.config_manager = ConfigManager()
    
    def generate_report(self, validation_data: Dict[str, Any], 
                       format_type: str = 'json',
                       output_path: Optional[str] = None) -> str:
        """
        Generate validation report in specified format
        
        Args:
            validation_data: Complete validation results from database
            format_type: Output format ('json', 'csv', 'html')
            output_path: Optional path to save report
            
        Returns:
            Report content as string or file path if saved
        """
        if format_type not in self.supported_formats:
            raise ValueError(f"Unsupported format: {format_type}. Supported: {self.supported_formats}")
        
        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_filename = f"validation_report_{timestamp}"
        
        # Generate report content
        if format_type == 'json':
            content = self._generate_json_report(validation_data)
            base_filename = base_filename + ".json"
        elif format_type == 'csv':
            content = self._generate_csv_report(validation_data)
            base_filename = base_filename + ".csv"
        elif format_type == 'html':
            content = self._generate_html_report(validation_data)
            base_filename = base_filename + ".html"

        
        
        # Save to file if path provided
        if output_path:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return output_path
        else:
            report_dir = self.config_manager.get('output.output_directory')
            report_path = os.path.join(report_dir,base_filename)
            if not os.path.exists(report_dir):
                os.makedirs(report_dir,exist_ok=True)
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(content)
        
        return content
    
    def _generate_json_report(self, validation_data: Dict[str, Any]) -> str:
        """Generate JSON format report"""
        execution_info = validation_data.get('execution', {})
        results = validation_data.get('results', [])
        
        # Calculate summary statistics
        summary = self._calculate_summary_stats(results)
        
        report = {
            'report_metadata': {
                'generated_at': datetime.now().isoformat(),
                'report_format': 'json',
                'report_version': '1.0'
            },
            'execution_summary': {
                'execution_id': execution_info.get('id'),
                'file_path': execution_info.get('file_path'),
                'normalized_file_name': execution_info.get('normalized_file_name'),
                'pipeline_id': execution_info.get('pipeline_id'),
                'execution_timestamp': execution_info.get('execution_timestamp'),
                'total_records': execution_info.get('total_records'),
                'total_rules_executed': execution_info.get('total_rules_executed'),
                'overall_status': execution_info.get('overall_status'),
                'execution_duration_ms': execution_info.get('execution_duration_ms')
            },
            'validation_summary': summary,
            'detailed_results': self._format_detailed_results(results),
            'recommendations': self._generate_recommendations(results, summary)
        }
        
        return json.dumps(report, indent=2, default=str)
    
    def _generate_csv_report(self, validation_data: Dict[str, Any]) -> str:
        """Generate CSV format report"""
        execution_info = validation_data.get('execution', {})
        results = validation_data.get('results', [])
        
        # Create CSV content
        output = []
        
        # Header information
        output.append("VALIDATION REPORT SUMMARY")
        output.append(f"Generated At,{datetime.now().isoformat()}")
        output.append(f"Execution ID,{execution_info.get('id', '')}")
        output.append(f"File Path,{execution_info.get('file_path', '')}")
        output.append(f"Pipeline ID,{execution_info.get('pipeline_id', '')}")
        output.append(f"Total Records,{execution_info.get('total_records', 0)}")
        output.append(f"Overall Status,{execution_info.get('overall_status', '')}")
        output.append(f"Execution Duration (ms),{execution_info.get('execution_duration_ms', 0)}")
        output.append("")
        
        # Summary statistics
        summary = self._calculate_summary_stats(results)
        output.append("SUMMARY STATISTICS")
        output.append(f"Total Rules,{summary['total_rules']}")
        output.append(f"Passed Rules,{summary['passed_rules']}")
        output.append(f"Failed Rules,{summary['failed_rules']}")
        output.append(f"Success Rate,%,{summary['success_rate']:.1f}")
        output.append("")
        
        # Detailed results
        output.append("DETAILED RESULTS")
        output.append("Column Name,Rule Type,Status,Passed Records,Failed Records,Rule Description,Failure Summary")
        
        for result in results:
            failure_summary = self._summarize_failures(result['failure_details'])
            output.append(f"{result['column_name']},{result['rule_type']},{result['status']},"
                         f"{result['passed_records_count']},{result['failed_records_count']},"
                         f"\"{result['rule_description']}\",\"{failure_summary}\"")
        
        return '\n'.join(output)
    
    def _generate_html_report(self, validation_data: Dict[str, Any]) -> str:
        """Generate HTML format report"""
        execution_info = validation_data.get('execution', {})
        results = validation_data.get('results', [])
        summary = self._calculate_summary_stats(results)
        
        # Determine status color
        status_color = {
            'PASSED': '#28a745',
            'FAILED': '#dc3545',
            'WARNING': '#ffc107',
            'ERROR': '#6c757d'
        }.get(execution_info.get('overall_status', 'ERROR'), '#6c757d')
        
        html = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Validation Report - {execution_info.get('normalized_file_name', 'Unknown File')}</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 28px;
            font-weight: 300;
        }}
        .header .subtitle {{
            margin-top: 10px;
            opacity: 0.9;
            font-size: 16px;
        }}
        .content {{
            padding: 30px;
        }}
        .summary-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        .summary-card {{
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            border-left: 4px solid #667eea;
        }}
        .summary-card h3 {{
            margin: 0 0 10px 0;
            color: #666;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }}
        .summary-card .value {{
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }}
        .status-badge {{
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            color: white;
            font-weight: bold;
            font-size: 14px;
            background-color: {status_color};
        }}
        .section {{
            margin-bottom: 30px;
        }}
        .section h2 {{
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
            color: #333;
        }}
        .results-table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }}
        .results-table th,
        .results-table td {{
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }}
        .results-table th {{
            background: #667eea;
            color: white;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 12px;
            letter-spacing: 1px;
        }}
        .results-table tbody tr:hover {{
            background-color: #f8f9fa;
        }}
        .status-cell {{
            font-weight: bold;
        }}
        .status-passed {{ color: #28a745; }}
        .status-failed {{ color: #dc3545; }}
        .status-warning {{ color: #ffc107; }}
        .progress-bar {{
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }}
        .progress-fill {{
            height: 100%;
            background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
            transition: width 0.3s ease;
        }}
        .metadata {{
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }}
        .metadata h3 {{
            margin-top: 0;
            color: #666;
        }}
        .metadata-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }}
        .metadata-item {{
            display: flex;
            justify-content: space-between;
        }}
        .metadata-label {{
            font-weight: 600;
            color: #666;
        }}
        .failure-details {{
            font-size: 12px;
            color: #666;
            max-width: 300px;
            word-wrap: break-word;
        }}
        .recommendations {{
            background: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 20px;
            border-radius: 0 8px 8px 0;
        }}
        .recommendations h3 {{
            color: #007bff;
            margin-top: 0;
        }}
        .recommendations ul {{
            margin: 0;
            padding-left: 20px;
        }}
        .recommendations li {{
            margin-bottom: 8px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Data Validation Report</h1>
            <div class="subtitle">
                {execution_info.get('normalized_file_name', 'Unknown File')} • 
                Pipeline: {execution_info.get('pipeline_id', 'Unknown')} • 
                <span class="status-badge">{execution_info.get('overall_status', 'UNKNOWN')}</span>
            </div>
        </div>
        
        <div class="content">
            <div class="summary-grid">
                <div class="summary-card">
                    <h3>Total Records</h3>
                    <div class="value">{execution_info.get('total_records', 0):,}</div>
                </div>
                <div class="summary-card">
                    <h3>Rules Executed</h3>
                    <div class="value">{execution_info.get('total_rules_executed', 0)}</div>
                </div>
                <div class="summary-card">
                    <h3>Success Rate</h3>
                    <div class="value">{summary['success_rate']:.1f}%</div>
                </div>
                <div class="summary-card">
                    <h3>Execution Time</h3>
                    <div class="value">{execution_info.get('execution_duration_ms', 0):,}ms</div>
                </div>
            </div>
            
            <div class="section">
                <h2>Validation Progress</h2>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: {summary['success_rate']}%"></div>
                </div>
                <p style="margin-top: 10px; text-align: center; color: #666;">
                    {summary['passed_rules']} of {summary['total_rules']} rules passed
                </p>
            </div>
            
            <div class="section">
                <h2>Detailed Results</h2>
                <table class="results-table">
                    <thead>
                        <tr>
                            <th>Column</th>
                            <th>Rule Type</th>
                            <th>Status</th>
                            <th>Passed</th>
                            <th>Failed</th>
                            <th>Description</th>
                            <th>Failure Details</th>
                        </tr>
                    </thead>
                    <tbody>
        """
        
        # Add results rows
        for result in results:
            status_class = f"status-{result['status'].lower()}"
            failure_summary = self._summarize_failures(result['failure_details'])
            
            html += f"""
                        <tr>
                            <td><strong>{result['column_name']}</strong></td>
                            <td>{result['rule_type'].replace('_', ' ').title()}</td>
                            <td class="status-cell {status_class}">{result['status']}</td>
                            <td>{result['passed_records_count']:,}</td>
                            <td>{result['failed_records_count']:,}</td>
                            <td>{result['rule_description']}</td>
                            <td class="failure-details">{failure_summary}</td>
                        </tr>
            """
        
        # Generate recommendations
        recommendations = self._generate_recommendations(results, summary)
        
        html += f"""
                    </tbody>
                </table>
            </div>
            
            {self._generate_recommendations_html(recommendations)}
            
            <div class="metadata">
                <h3>Execution Metadata</h3>
                <div class="metadata-grid">
                    <div class="metadata-item">
                        <span class="metadata-label">Execution ID:</span>
                        <span>{execution_info.get('id', 'N/A')}</span>
                    </div>
                    <div class="metadata-item">
                        <span class="metadata-label">File Path:</span>
                        <span>{execution_info.get('file_path', 'N/A')}</span>
                    </div>
                    <div class="metadata-item">
                        <span class="metadata-label">Execution Time:</span>
                        <span>{execution_info.get('execution_timestamp', 'N/A')}</span>
                    </div>
                    <div class="metadata-item">
                        <span class="metadata-label">Report Generated:</span>
                        <span>{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
        """
        
        return html
    
    def _calculate_summary_stats(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate summary statistics from validation results"""
        total_rules = len(results)
        passed_rules = sum(1 for r in results if r['status'] == 'PASSED')
        failed_rules = sum(1 for r in results if r['status'] == 'FAILED')
        warning_rules = sum(1 for r in results if r['status'] == 'WARNING')
        
        success_rate = (passed_rules / total_rules * 100) if total_rules > 0 else 0
        
        # Column-level statistics
        columns_with_failures = set(r['column_name'] for r in results if r['status'] != 'PASSED')
        
        # Total records processed (from first result)
        total_records = results[0]['passed_records_count'] + results[0]['failed_records_count'] if results else 0
        
        return {
            'total_rules': total_rules,
            'passed_rules': passed_rules,
            'failed_rules': failed_rules,
            'warning_rules': warning_rules,
            'success_rate': success_rate,
            'columns_with_failures': len(columns_with_failures),
            'total_records': total_records
        }
    
    def _format_detailed_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Format detailed results for JSON output"""
        formatted_results = []
        
        for result in results:
            formatted_result = {
                'rule_id': result['id'],
                'column_name': result['column_name'],
                'rule_type': result['rule_type'],
                'rule_description': result['rule_description'],
                'status': result['status'],
                'passed_records_count': result['passed_records_count'],
                'failed_records_count': result['failed_records_count'],
                'failure_summary': self._summarize_failures(result['failure_details']),
                'failure_details': result['failure_details']
            }
            formatted_results.append(formatted_result)
        
        return formatted_results
    
    def _summarize_failures(self, failure_details: Dict[str, Any]) -> str:
        """Create a concise summary of failure details"""
        if not failure_details:
            return "No failures"
        
        if 'error' in failure_details:
            return f"Error: {failure_details['error']}"
        
        summary_parts = []
        
        if 'total_null_records' in failure_details:
            summary_parts.append(f"{failure_details['total_null_records']} null values")
        
        if 'conversion_failures' in failure_details and 'range_violations' in failure_details:
            summary_parts.append(f"{failure_details['conversion_failures']} conversion errors")
            summary_parts.append(f"{failure_details['range_violations']} range violations")
        
        if 'duplicate_values' in failure_details:
            summary_parts.append(f"{len(failure_details['duplicate_values'])} duplicate values")
        
        if 'invalid_values' in failure_details:
            summary_parts.append(f"{len(failure_details['invalid_values'])} invalid values")
        
        if 'failed_values' in failure_details:
            summary_parts.append(f"Examples: {', '.join(map(str, failure_details['failed_values'][:3]))}")
        
        return '; '.join(summary_parts) if summary_parts else "Validation failures detected"
    
    def _generate_recommendations(self, results: List[Dict[str, Any]], 
                                summary: Dict[str, Any]) -> List[str]:
        """Generate actionable recommendations based on validation results"""
        recommendations = []
        
        # Overall performance recommendations
        if summary['success_rate'] < 50:
            recommendations.append("Critical: Less than 50% of validation rules passed. Review data quality processes.")
        elif summary['success_rate'] < 80:
            recommendations.append("Warning: Success rate is below 80%. Consider reviewing data validation rules and source data quality.")
        
        # Rule-specific recommendations
        failed_rules_by_type = {}
        for result in results:
            if result['status'] == 'FAILED':
                rule_type = result['rule_type']
                if rule_type not in failed_rules_by_type:
                    failed_rules_by_type[rule_type] = []
                failed_rules_by_type[rule_type].append(result)
        
        for rule_type, failed_results in failed_rules_by_type.items():
            if rule_type == 'not_null':
                recommendations.append(f"Address missing data in {len(failed_results)} columns. Consider data imputation or source system fixes.")
            elif rule_type == 'unique':
                recommendations.append(f"Resolve duplicate records in {len(failed_results)} columns. Implement deduplication processes.")
            elif rule_type == 'range':
                recommendations.append(f"Fix out-of-range values in {len(failed_results)} columns. Review data collection and validation at source.")
            elif rule_type == 'regex':
                recommendations.append(f"Correct format violations in {len(failed_results)} columns. Standardize data entry formats.")
            elif rule_type == 'data_type':
                recommendations.append(f"Fix data type mismatches in {len(failed_results)} columns. Review data parsing and conversion logic.")
        
        # Column-specific recommendations
        if summary['columns_with_failures'] > summary['total_rules'] * 0.3:
            recommendations.append("Multiple columns have validation failures. Consider reviewing overall data pipeline quality.")
        
        if not recommendations:
            recommendations.append("All validations passed successfully. Data quality is good.")
        
        return recommendations
    
    def _generate_recommendations_html(self, recommendations: List[str]) -> str:
        """Generate HTML section for recommendations"""
        if not recommendations:
            return ""
        
        recommendations_html = '<div class="section"><div class="recommendations"><h3>Recommendations</h3><ul>'
        for rec in recommendations:
            recommendations_html += f'<li>{rec}</li>'
        recommendations_html += '</ul></div></div>'
        
        return recommendations_html
    
    def export_failed_records(self, validation_data: Dict[str, Any], 
                             output_path: str) -> str:
        """Export details of failed records to CSV for investigation"""
        results = validation_data.get('results', [])
        
        failed_records_data = []
        
        for result in results:
            if result['status'] != 'PASSED' and result['failure_details']:
                failure_details = result['failure_details']
                
                # Extract failed record information
                if 'failed_record_indices' in failure_details:
                    indices = failure_details['failed_record_indices']
                    values = failure_details.get('failed_values', ['N/A'] * len(indices))
                    
                    for i, (idx, val) in enumerate(zip(indices, values)):
                        failed_records_data.append({
                            'record_index': idx,
                            'column_name': result['column_name'],
                            'rule_type': result['rule_type'],
                            'failed_value': val,
                            'rule_description': result['rule_description'],
                            'failure_reason': self._summarize_failures(failure_details)
                        })
        
        # Write to CSV
        if failed_records_data:
            df = pd.DataFrame(failed_records_data)
            df.to_csv(output_path, index=False)
        else:
            # Create empty file with headers
            with open(output_path, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(['record_index', 'column_name', 'rule_type', 'failed_value', 
                               'rule_description', 'failure_reason'])
        
        return output_path


# Example usage
if __name__ == "__main__":
    # Sample validation data
    sample_data = {
        'execution': {
            'id': 'exec-123',
            'file_path': '/data/customers.csv',
            'normalized_file_name': 'customers_normalized.csv',
            'pipeline_id': 'PIPELINE_001',
            'execution_timestamp': datetime.now().isoformat(),
            'total_records': 1000,
            'total_rules_executed': 5,
            'overall_status': 'FAILED',
            'execution_duration_ms': 1500
        },
        'results': [
            {
                'id': 'rule-1',
                'column_name': 'customer_id',
                'rule_type': 'not_null',
                'rule_description': 'Customer ID cannot be null',
                'status': 'PASSED',
                'passed_records_count': 1000,
                'failed_records_count': 0,
                'failure_details': {}
            },
            {
                'id': 'rule-2',
                'column_name': 'age',
                'rule_type': 'range',
                'rule_description': 'Age must be between 18 and 120',
                'status': 'FAILED',
                'passed_records_count': 950,
                'failed_records_count': 50,
                'failure_details': {
                    'failed_record_indices': [45, 67, 89],
                    'failed_values': [150, -5, 200],
                    'range_constraint': {'min': 18, 'max': 120},
                    'range_violations': 50
                }
            }
        ]
    }
    
    generator = ReportGenerator()
    
    # Generate reports in different formats
    json_report = generator.generate_report(sample_data, 'json')
    print("JSON Report generated")
    
    csv_report = generator.generate_report(sample_data, 'csv')
    print("CSV Report generated")
    
    html_report = generator.generate_report(sample_data, 'html')
    print(f"HTML Report generated")
