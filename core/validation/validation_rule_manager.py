"""
Validation Rule Manager
Main orchestrator for validation rule operations and file validation
"""

import os
import sys
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path
import time

# Add project root to Python path for imports
project_root = Path(__file__).resolve().parent.parent.parent
sys.path.insert(0, str(project_root))

from core.config_manager import ConfigManager
from db_utils.duckdb_manager import DuckDBManager
from core.validation.csv_rule_loader import CSVRuleLoader
from core.validation.validation_engine import ValidationEngine
from core.validation.report_generator import ReportGenerator


class ValidationRuleManager:
    """Main manager for validation rule operations and file validation"""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        # Initialize configuration
        if config_manager is None:
            self.config_manager = ConfigManager()
            if not os.path.exists(self.config_manager.config_path):
                self.config_manager.create_default_config()
            self.config_manager.load_config()
        else:
            self.config_manager = config_manager
        
        # Initialize components
        self.db_manager = DuckDBManager(self.config_manager)
        self.csv_loader = CSVRuleLoader()
        self.validation_engine = ValidationEngine()
        self.report_generator = ReportGenerator()
    
    def define_validation_rules(self, csv_file_path: str, pipeline_id: str, 
                               normalized_file_name: str) -> Dict[str, Any]:
        """
        Define validation rules from CSV file for a specific pipeline and file
        
        Args:
            csv_file_path: Path to CSV file containing validation rules
            pipeline_id: Pipeline identifier
            normalized_file_name: Normalized file name
            
        Returns:
            Dictionary with operation results
        """
        try:
            # Generate registry key and lookup schema_registry_id
            registry_key = self.db_manager.generate_registry_key(pipeline_id, normalized_file_name)
            schema_registry_id = self.db_manager.get_schema_registry_id_by_registry_key(registry_key)
            
            if not schema_registry_id:
                return {
                    'success': False,
                    'error': f"Schema registry not found for pipeline_id: {pipeline_id}, "
                            f"file_name: {normalized_file_name}. "
                            f"Please ensure the schema is registered first.",
                    'registry_key': registry_key
                }
            
            # Validate CSV format first
            validation_result = self.csv_loader.validate_csv_format(csv_file_path)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'error': f"CSV validation failed: {'; '.join(validation_result['errors'])}",
                    'validation_details': validation_result
                }
            
            # Load validation rules from CSV
            validation_rules = self.csv_loader.load_rules_from_csv(csv_file_path, schema_registry_id)
            
            if not validation_rules:
                return {
                    'success': False,
                    'error': "No valid validation rules found in CSV file"
                }
            
            # Check for existing rules and remove them
            existing_rules = self.db_manager.get_validation_rules_by_schema_id(schema_registry_id, active_only=False)
            if existing_rules:
                print(f"Warning: Found {len(existing_rules)} existing validation rules. They will be replaced.")
                # In a production system, you might want to deactivate rather than delete
                # For now, we'll proceed with inserting new rules
            
            # Insert validation rules into database
            inserted_count = self.db_manager.batch_insert_validation_rules(validation_rules)
            
            return {
                'success': True,
                'message': f"Successfully inserted {inserted_count} validation rules",
                'schema_registry_id': schema_registry_id,
                'registry_key': registry_key,
                'total_rules': len(validation_rules),
                'inserted_rules': inserted_count,
                'rules_summary': self._summarize_rules(validation_rules)
            }
            
        except FileNotFoundError as e:
            return {'success': False, 'error': f"File not found: {str(e)}"}
        except Exception as e:
            return {'success': False, 'error': f"Error defining validation rules: {str(e)}"}
    
    def validate_file(self, pipeline_id: str, file_path: str, 
                     normalized_file_name: str) -> Dict[str, Any]:
        """
        Validate a data file against defined validation rules
        
        Args:
            pipeline_id: Pipeline identifier
            file_path: Path to the data file to validate
            normalized_file_name: Normalized file name
            
        Returns:
            Dictionary with validation results
        """
        start_time = time.time()
        
        try:
            # Generate registry key and lookup schema_registry_id
            registry_key = self.db_manager.generate_registry_key(pipeline_id, normalized_file_name)
            schema_registry_id = self.db_manager.get_schema_registry_id_by_registry_key(registry_key)
            
            if not schema_registry_id:
                return {
                    'success': False,
                    'error': f"Schema registry not found for pipeline_id: {pipeline_id}, "
                            f"file_name: {normalized_file_name}",
                    'registry_key': registry_key
                }
            
            # Get validation rules
            validation_rules = self.db_manager.get_validation_rules_by_schema_id(schema_registry_id)
            
            if not validation_rules:
                return {
                    'success': False,
                    'error': f"No validation rules defined for this schema. "
                            f"Please define validation rules first using --define-rules.",
                    'schema_registry_id': schema_registry_id
                }
            
            # Execute validation
            validation_results = self.validation_engine.validate_file(file_path, validation_rules)
            
            # Calculate execution duration
            execution_duration_ms = int((time.time() - start_time) * 1000)
            
            # Store validation execution results
            execution_id = self.db_manager.insert_validation_execution(
                schema_registry_id=schema_registry_id,
                file_path=file_path,
                normalized_file_name=normalized_file_name,
                total_records=validation_results['total_records'],
                total_rules_executed=validation_results['total_rules_executed'],
                overall_status=validation_results['overall_status'],
                execution_duration_ms=execution_duration_ms
            )
            
            if not execution_id:
                print("Warning: Failed to store validation execution record")
            
            # Store detailed validation results
            for rule_result in validation_results['rule_results']:
                success = self.db_manager.insert_validation_results(
                    execution_id=execution_id,
                    rule_id=rule_result.get('rule_id', ''),
                    column_name=rule_result['column_name'],
                    rule_type=rule_result['rule_type'],
                    passed_records_count=rule_result['passed_records_count'],
                    failed_records_count=rule_result['failed_records_count'],
                    failure_details=rule_result['failure_details'],
                    status=rule_result['status']
                )
                
                if not success:
                    print(f"Warning: Failed to store validation result for rule {rule_result.get('rule_id')}")
            
            return {
                'success': True,
                'execution_id': execution_id,
                'validation_results': validation_results,
                'schema_registry_id': schema_registry_id,
                'registry_key': registry_key
            }
            
        except FileNotFoundError as e:
            return {'success': False, 'error': f"Data file not found: {str(e)}"}
        except Exception as e:
            return {'success': False, 'error': f"Error validating file: {str(e)}"}
    
    def generate_validation_report(self, execution_id: str, format_type: str = 'json',
                                  output_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate validation report for a specific execution
        
        Args:
            execution_id: Validation execution ID
            format_type: Report format ('json', 'csv', 'html')
            output_path: Optional path to save report
            
        Returns:
            Dictionary with report generation results
        """
        try:
            # Get validation execution results from database
            validation_data = self.db_manager.get_validation_execution_results(execution_id)
            
            if not validation_data:
                return {
                    'success': False,
                    'error': f"Validation execution not found: {execution_id}"
                }
            
            # Generate report
            report_content = self.report_generator.generate_report(
                validation_data, format_type, output_path
            )
            
            return {
                'success': True,
                'format': format_type,
                'content': report_content if not output_path else None,
                'file_path': output_path if output_path else None,
                'execution_id': execution_id
            }
            
        except Exception as e:
            return {'success': False, 'error': f"Error generating report: {str(e)}"}
    
    def export_failed_records(self, execution_id: str, output_path: str) -> Dict[str, Any]:
        """
        Export failed records details to CSV for investigation
        
        Args:
            execution_id: Validation execution ID
            output_path: Path to save CSV file
            
        Returns:
            Dictionary with export results
        """
        try:
            # Get validation execution results
            validation_data = self.db_manager.get_validation_execution_results(execution_id)
            
            if not validation_data:
                return {
                    'success': False,
                    'error': f"Validation execution not found: {execution_id}"
                }
            
            # Export failed records
            exported_file = self.report_generator.export_failed_records(validation_data, output_path)
            
            return {
                'success': True,
                'file_path': exported_file,
                'execution_id': execution_id
            }
            
        except Exception as e:
            return {'success': False, 'error': f"Error exporting failed records: {str(e)}"}
    
    def list_validation_rules(self, pipeline_id: str, normalized_file_name: str) -> Dict[str, Any]:
        """
        List existing validation rules for a pipeline and file
        
        Args:
            pipeline_id: Pipeline identifier
            normalized_file_name: Normalized file name
            
        Returns:
            Dictionary with validation rules
        """
        try:
            # Generate registry key and lookup schema_registry_id
            registry_key = self.db_manager.generate_registry_key(pipeline_id, normalized_file_name)
            schema_registry_id = self.db_manager.get_schema_registry_id_by_registry_key(registry_key)
            
            if not schema_registry_id:
                return {
                    'success': False,
                    'error': f"Schema registry not found for pipeline_id: {pipeline_id}, "
                            f"file_name: {normalized_file_name}",
                    'registry_key': registry_key
                }
            
            # Get validation rules
            validation_rules = self.db_manager.get_validation_rules_by_schema_id(schema_registry_id, active_only=False)
            
            return {
                'success': True,
                'schema_registry_id': schema_registry_id,
                'registry_key': registry_key,
                'total_rules': len(validation_rules),
                'validation_rules': validation_rules,
                'rules_summary': self._summarize_rules(validation_rules)
            }
            
        except Exception as e:
            return {'success': False, 'error': f"Error listing validation rules: {str(e)}"}
    
    def get_validation_history(self, pipeline_id: str, normalized_file_name: str,
                              limit: int = 10) -> Dict[str, Any]:
        """
        Get validation execution history for a pipeline and file
        
        Args:
            pipeline_id: Pipeline identifier
            normalized_file_name: Normalized file name
            limit: Maximum number of executions to return
            
        Returns:
            Dictionary with validation history
        """
        try:
            # Generate registry key and lookup schema_registry_id
            registry_key = self.db_manager.generate_registry_key(pipeline_id, normalized_file_name)
            schema_registry_id = self.db_manager.get_schema_registry_id_by_registry_key(registry_key)
            
            if not schema_registry_id:
                return {
                    'success': False,
                    'error': f"Schema registry not found for pipeline_id: {pipeline_id}, "
                            f"file_name: {normalized_file_name}",
                    'registry_key': registry_key
                }
            
            # Get validation history
            history = self.db_manager.get_validation_execution_history(schema_registry_id, limit)
            
            return {
                'success': True,
                'schema_registry_id': schema_registry_id,
                'registry_key': registry_key,
                'total_executions': len(history),
                'execution_history': history
            }
            
        except Exception as e:
            return {'success': False, 'error': f"Error getting validation history: {str(e)}"}
    
    def check_validation_rules_exist(self, pipeline_id: str, normalized_file_name: str) -> bool:
        """
        Check if validation rules exist for a pipeline and file
        
        Args:
            pipeline_id: Pipeline identifier
            normalized_file_name: Normalized file name
            
        Returns:
            True if validation rules exist, False otherwise
        """
        try:
            registry_key = self.db_manager.generate_registry_key(pipeline_id, normalized_file_name)
            schema_registry_id = self.db_manager.get_schema_registry_id_by_registry_key(registry_key)
            
            if not schema_registry_id:
                return False
            
            validation_rules = self.db_manager.get_validation_rules_by_schema_id(schema_registry_id)
            return len(validation_rules) > 0
            
        except Exception:
            return False
    
    def _summarize_rules(self, validation_rules: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Create a summary of validation rules"""
        if not validation_rules:
            return {}
        
        rule_types = {}
        columns = set()
        active_rules = 0
        
        for rule in validation_rules:
            rule_type = rule['rule_type']
            rule_types[rule_type] = rule_types.get(rule_type, 0) + 1
            columns.add(rule['column_name'])
            if rule.get('is_active', True):
                active_rules += 1
        
        return {
            'total_rules': len(validation_rules),
            'active_rules': active_rules,
            'inactive_rules': len(validation_rules) - active_rules,
            'columns_covered': len(columns),
            'rule_types': rule_types,
            'columns': sorted(list(columns))
        }
    
    def generate_sample_rules_csv(self, output_path: str) -> Dict[str, Any]:
        """
        Generate a sample CSV file with validation rules examples
        
        Args:
            output_path: Path to save the sample CSV file
            
        Returns:
            Dictionary with operation results
        """
        try:
            self.csv_loader.generate_sample_csv(output_path)
            return {
                'success': True,
                'message': f"Sample validation rules CSV generated at: {output_path}",
                'file_path': output_path
            }
        except Exception as e:
            return {'success': False, 'error': f"Error generating sample CSV: {str(e)}"}
    
    def close(self):
        """Close database connections and cleanup"""
        if self.db_manager:
            self.db_manager.close_connection()


# Example usage and testing
if __name__ == "__main__":
    # Initialize manager
    manager = ValidationRuleManager()
    
    try:
        # Test 1: Generate sample CSV
        print("1. Generating sample validation rules CSV...")
        result = manager.generate_sample_rules_csv("sample_validation_rules.csv")
        print(f"Result: {result}")
        
        # Test 2: Check if validation rules exist (should be False for non-existent pipeline)
        print("\n2. Checking if validation rules exist...")
        exists = manager.check_validation_rules_exist("TEST_PIPELINE", "test_file.csv")
        print(f"Rules exist: {exists}")
        
        # Test 3: List validation rules (should return error for non-existent schema)
        print("\n3. Attempting to list validation rules...")
        result = manager.list_validation_rules("TEST_PIPELINE", "test_file.csv")
        print(f"Result: {result}")
        
        print("\nValidation Rule Manager test completed successfully!")
        
    except Exception as e:
        print(f"Error during testing: {e}")
    
    finally:
        # Cleanup
        manager.close()
