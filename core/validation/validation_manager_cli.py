#!/usr/bin/env python3
"""
Validation Manager CLI
Command-line interface for validation rule management and file validation
"""

import argparse
import sys
import json
from pathlib import Path
from typing import Optional

# Add project root to Python path for imports
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from validation.validation_rule_manager import ValidationRuleManager


def print_success(message: str):
    """Print success message in green"""
    print(f"\033[92m✓ {message}\033[0m")


def print_error(message: str):
    """Print error message in red"""
    print(f"\033[91m✗ {message}\033[0m")


def print_warning(message: str):
    """Print warning message in yellow"""
    print(f"\033[93m⚠ {message}\033[0m")


def print_info(message: str):
    """Print info message in blue"""
    print(f"\033[94mℹ {message}\033[0m")


def define_rules_command(args, manager: ValidationRuleManager):
    """Handle --define-rules command"""
    print_info(f"Defining validation rules from CSV: {args.csv_file}")
    print_info(f"Pipeline ID: {args.pipeline_id}")
    print_info(f"Normalized file name: {args.file_name}")
    
    result = manager.define_validation_rules(
        csv_file_path=args.csv_file,
        pipeline_id=args.pipeline_id,
        normalized_file_name=args.file_name
    )
    
    if result['success']:
        print_success(result['message'])
        print_info(f"Schema Registry ID: {result['schema_registry_id']}")
        print_info(f"Registry Key: {result['registry_key']}")
        
        # Print rules summary
        summary = result['rules_summary']
        print("\nRules Summary:")
        print(f"  Total Rules: {summary['total_rules']}")
        print(f"  Active Rules: {summary['active_rules']}")
        print(f"  Columns Covered: {summary['columns_covered']}")
        print(f"  Rule Types: {', '.join(summary['rule_types'].keys())}")
        
        if args.verbose:
            print("\nRule Types Breakdown:")
            for rule_type, count in summary['rule_types'].items():
                print(f"  - {rule_type.replace('_', ' ').title()}: {count}")
    else:
        print_error(f"Failed to define validation rules: {result['error']}")
        return 1
    
    return 0


def validate_command(args, manager: ValidationRuleManager):
    """Handle --validate command"""
    print_info(f"Validating file: {args.file_path}")
    print_info(f"Pipeline ID: {args.pipeline_id}")
    print_info(f"Normalized file name: {args.file_name}")
    
    # Check if validation rules exist
    if not manager.check_validation_rules_exist(args.pipeline_id, args.file_name):
        print_error("No validation rules found for this pipeline and file.")
        print_info("Use --define-rules command to create validation rules first.")
        return 1
    
    result = manager.validate_file(
        pipeline_id=args.pipeline_id,
        file_path=args.file_path,
        normalized_file_name=args.file_name
    )
    
    if result['success']:
        validation_results = result['validation_results']
        execution_id = result['execution_id']
        
        print_success("File validation completed!")
        print_info(f"Execution ID: {execution_id}")
        
        # Print summary
        print(f"\nValidation Summary:")
        print(f"  Total Records: {validation_results['total_records']:,}")
        print(f"  Rules Executed: {validation_results['total_rules_executed']}")
        print(f"  Overall Status: {validation_results['overall_status']}")
        print(f"  Execution Time: {validation_results['execution_duration_ms']}ms")
        
        summary = validation_results['summary']
        print(f"\nResults Breakdown:")
        print(f"  Passed Rules: {summary['passed_rules']}")
        print(f"  Failed Rules: {summary['failed_rules']}")
        print(f"  Warning Rules: {summary['warning_rules']}")
        
        # Print rule results if verbose or if there are failures
        if args.verbose or summary['failed_rules'] > 0:
            print(f"\nDetailed Results:")
            for rule_result in validation_results['rule_results']:
                status_symbol = "✓" if rule_result['status'] == 'PASSED' else "✗"
                print(f"  {status_symbol} {rule_result['column_name']} ({rule_result['rule_type']}) - "
                      f"{rule_result['status']} - "
                      f"Passed: {rule_result['passed_records_count']}, "
                      f"Failed: {rule_result['failed_records_count']}")
                
                if rule_result['failure_details'] and args.verbose:
                    print(f"    Failures: {rule_result['failure_details']}")
        
        # Suggest generating report if there are failures
        if summary['failed_rules'] > 0:
            print_warning(f"\nValidation failures detected!")
            print_info(f"Generate detailed report: python validation_manager.py --report --execution-id {execution_id} --format html")
            print_info(f"Export failed records: python validation_manager.py --export-failures --execution-id {execution_id} --output failed_records.csv")
    else:
        print_error(f"Failed to validate file: {result['error']}")
        return 1
    
    return 0


def report_command(args, manager: ValidationRuleManager):
    """Handle --report command"""
    print_info(f"Generating validation report for execution: {args.execution_id}")
    print_info(f"Format: {args.format}")
    
    # Generate output path if not provided
    output_path = args.output
    if not output_path:
        timestamp = args.execution_id.replace('-', '_') if args.execution_id else "unknown"
        extensions = {'json': '.json', 'csv': '.csv', 'html': '.html'}
        output_path = f"validation_report_{timestamp}{extensions.get(args.format, '.txt')}"
    
    result = manager.generate_validation_report(
        execution_id=args.execution_id,
        format_type=args.format,
        output_path=output_path
    )
    
    if result['success']:
        if result['file_path']:
            print_success(f"Report generated: {result['file_path']}")
        else:
            print_success("Report generated successfully")
            if args.format == 'json' and not result['file_path']:
                # Print JSON to stdout if no output file specified
                print("\n" + result['content'])
    else:
        print_error(f"Failed to generate report: {result['error']}")
        return 1
    
    return 0


def export_failures_command(args, manager: ValidationRuleManager):
    """Handle --export-failures command"""
    print_info(f"Exporting failed records for execution: {args.execution_id}")
    
    result = manager.export_failed_records(
        execution_id=args.execution_id,
        output_path=args.output
    )
    
    if result['success']:
        print_success(f"Failed records exported: {result['file_path']}")
    else:
        print_error(f"Failed to export failed records: {result['error']}")
        return 1
    
    return 0


def list_rules_command(args, manager: ValidationRuleManager):
    """Handle --list-rules command"""
    print_info(f"Listing validation rules for pipeline: {args.pipeline_id}")
    print_info(f"Normalized file name: {args.file_name}")
    
    result = manager.list_validation_rules(
        pipeline_id=args.pipeline_id,
        normalized_file_name=args.file_name
    )
    
    if result['success']:
        summary = result['rules_summary']
        print_success(f"Found {result['total_rules']} validation rules")
        
        if result['total_rules'] > 0:
            print(f"\nRules Summary:")
            print(f"  Total Rules: {summary['total_rules']}")
            print(f"  Active Rules: {summary['active_rules']}")
            print(f"  Inactive Rules: {summary['inactive_rules']}")
            print(f"  Columns Covered: {summary['columns_covered']}")
            
            print(f"\nRule Types:")
            for rule_type, count in summary['rule_types'].items():
                print(f"  - {rule_type.replace('_', ' ').title()}: {count}")
            
            print(f"\nColumns: {', '.join(summary['columns'])}")
            
            if args.verbose:
                print(f"\nDetailed Rules:")
                for rule in result['validation_rules']:
                    status = "Active" if rule['is_active'] else "Inactive"
                    print(f"  - {rule['column_name']} ({rule['rule_type']}) - {status}")
                    if rule['rule_description']:
                        print(f"    Description: {rule['rule_description']}")
        else:
            print_info("No validation rules defined for this pipeline and file.")
            print_info("Use --define-rules command to create validation rules.")
    else:
        print_error(f"Failed to list validation rules: {result['error']}")
        return 1
    
    return 0


def history_command(args, manager: ValidationRuleManager):
    """Handle --history command"""
    print_info(f"Getting validation history for pipeline: {args.pipeline_id}")
    print_info(f"Normalized file name: {args.file_name}")
    
    result = manager.get_validation_history(
        pipeline_id=args.pipeline_id,
        normalized_file_name=args.file_name,
        limit=args.limit
    )
    
    if result['success']:
        print_success(f"Found {result['total_executions']} validation executions")
        
        if result['total_executions'] > 0:
            print(f"\nValidation History (last {args.limit} executions):")
            for execution in result['execution_history']:
                print(f"  - {execution['id']} ({execution['execution_timestamp']})")
                print(f"    Status: {execution['overall_status']}")
                print(f"    Records: {execution['total_records']:,}")
                print(f"    Rules: {execution['total_rules_executed']}")
                print(f"    Duration: {execution['execution_duration_ms']}ms")
                print()
        else:
            print_info("No validation executions found for this pipeline and file.")
    else:
        print_error(f"Failed to get validation history: {result['error']}")
        return 1
    
    return 0


def sample_csv_command(args, manager: ValidationRuleManager):
    """Handle --sample-csv command"""
    output_path = args.output or "sample_validation_rules.csv"
    print_info(f"Generating sample validation rules CSV: {output_path}")
    
    result = manager.generate_sample_rules_csv(output_path)
    
    if result['success']:
        print_success(f"Sample CSV generated: {result['file_path']}")
        print_info("Edit this file with your validation rules and use --define-rules to load them.")
    else:
        print_error(f"Failed to generate sample CSV: {result['error']}")
        return 1
    
    return 0


def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(
        description="Validation Manager - Define and execute data validation rules",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Generate sample validation rules CSV
  python validation_manager.py --sample-csv --output my_rules.csv
  
  # Define validation rules
  python validation_manager.py --define-rules --csv-file validation_rules.csv --pipeline-id PIPELINE_001 --file-name customers_normalized.csv
  
  # Validate a file
  python validation_manager.py --validate --file-path data.csv --pipeline-id PIPELINE_001 --file-name customers_normalized.csv
  
  # Generate validation report
  python validation_manager.py --report --execution-id exec-123 --format html --output report.html
  
  # List existing rules
  python validation_manager.py --list-rules --pipeline-id PIPELINE_001 --file-name customers_normalized.csv
  
  # Get validation history
  python validation_manager.py --history --pipeline-id PIPELINE_001 --file-name customers_normalized.csv --limit 10
        """
    )
    
    # Main action arguments (mutually exclusive)
    action_group = parser.add_mutually_exclusive_group(required=True)
    action_group.add_argument('--define-rules', action='store_true',
                             help='Define validation rules from CSV file')
    action_group.add_argument('--validate', action='store_true',
                             help='Validate a data file against defined rules')
    action_group.add_argument('--report', action='store_true',
                             help='Generate validation report')
    action_group.add_argument('--export-failures', action='store_true',
                             help='Export failed records to CSV')
    action_group.add_argument('--list-rules', action='store_true',
                             help='List existing validation rules')
    action_group.add_argument('--history', action='store_true',
                             help='Get validation execution history')
    action_group.add_argument('--sample-csv', action='store_true',
                             help='Generate sample validation rules CSV')
    
    # Common arguments
    parser.add_argument('--pipeline-id', type=str,
                       help='Pipeline identifier (required for most operations)')
    parser.add_argument('--file-name', type=str,
                       help='Normalized file name (required for most operations)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose output')
    
    # Arguments for specific commands
    parser.add_argument('--csv-file', type=str,
                       help='Path to CSV file with validation rules (for --define-rules)')
    parser.add_argument('--file-path', type=str,
                       help='Path to data file to validate (for --validate)')
    parser.add_argument('--execution-id', type=str,
                       help='Validation execution ID (for --report, --export-failures)')
    parser.add_argument('--format', choices=['json', 'csv', 'html'], default='json',
                       help='Report format (for --report)')
    parser.add_argument('--output', type=str,
                       help='Output file path')
    parser.add_argument('--limit', type=int, default=10,
                       help='Maximum number of records to return (for --history)')
    
    args = parser.parse_args()
    
    # Validate required arguments for each command
    if args.define_rules:
        if not all([args.csv_file, args.pipeline_id, args.file_name]):
            print_error("--define-rules requires --csv-file, --pipeline-id, and --file-name")
            return 1
    elif args.validate:
        if not all([args.file_path, args.pipeline_id, args.file_name]):
            print_error("--validate requires --file-path, --pipeline-id, and --file-name")
            return 1
    elif args.report:
        if not args.execution_id:
            print_error("--report requires --execution-id")
            return 1
    elif args.export_failures:
        if not all([args.execution_id, args.output]):
            print_error("--export-failures requires --execution-id and --output")
            return 1
    elif args.list_rules or args.history:
        if not all([args.pipeline_id, args.file_name]):
            print_error(f"--{args.list_rules and 'list-rules' or 'history'} requires --pipeline-id and --file-name")
            return 1
    
    # Initialize validation manager
    try:
        manager = ValidationRuleManager()
    except Exception as e:
        print_error(f"Failed to initialize validation manager: {e}")
        return 1
    
    try:
        # Execute appropriate command
        if args.define_rules:
            return define_rules_command(args, manager)
        elif args.validate:
            return validate_command(args, manager)
        elif args.report:
            return report_command(args, manager)
        elif args.export_failures:
            return export_failures_command(args, manager)
        elif args.list_rules:
            return list_rules_command(args, manager)
        elif args.history:
            return history_command(args, manager)
        elif args.sample_csv:
            return sample_csv_command(args, manager)
    
    except KeyboardInterrupt:
        print_warning("\nOperation cancelled by user")
        return 1
    except Exception as e:
        print_error(f"Unexpected error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1
    finally:
        # Cleanup
        manager.close()
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
