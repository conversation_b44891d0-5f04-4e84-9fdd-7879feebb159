{"azure_openai": {"api_key": "********************************", "endpoint": "https://asper-genai.openai.azure.com/", "deployment": "ddai-gpt-4o", "api_version": "2024-12-01-preview", "timeout": 180, "max_retries": 3}, "neo4j": {"uri": "neo4j+s://74946ed7.databases.neo4j.io", "username": "neo4j", "password": "K-hBeH6CHoKfonqLi9tg89bKA9vd_vymPNLcjMNlRbg", "database": "neo4j", "connection_timeout": 30, "max_retry_attempts": 3}, "embeddings": {"sentence_transformer": {"provider": "sentence_transformers", "model": "all-mpnet-base-v2", "dimensions": 768, "device": "cpu", "batch_size": 32}}, "csv_parser": {"default_rows_to_extract": 100, "max_file_size_mb": 500, "sample_size_for_detection": 8192, "encoding_detection": {"fallback_encodings": ["utf-8", "latin-1", "cp1252", "iso-8859-1"], "confidence_threshold": 0.7, "use_chardet": true}, "delimiter_detection": {"possible_delimiters": [",", ";", "\t", "|", ":", " "], "use_csv_sniffer": true, "fallback_delimiter": ",", "min_delimiter_frequency": 2}, "parsing_options": {"skip_blank_lines": true, "strip_whitespace": true, "handle_quotes": true, "quote_char": "\"", "escape_char": "\\", "max_field_size": 131072}, "performance": {"chunk_size": 1024, "use_pandas_for_large_files": true, "memory_limit_mb": 100, "enable_caching": false}, "output": {"include_metadata": true, "return_format": "list_of_dicts", "date_parsing": false, "numeric_conversion": false}, "logging": {"log_level": "INFO", "log_file": "csv_parser.log", "enable_file_logging": false, "log_detection_details": true}, "error_handling": {"strict_mode": false, "skip_bad_lines": true, "max_errors": 10, "continue_on_encoding_error": true}}, "database": {"type": "duckdb", "backends": {"duckdb": {"enabled": true, "connection": {"path": "data/operational_metadata.duckdb", "read_only": false, "threads": 4}}, "postgresql": {"enabled": false, "connection": {"host": "localhost", "port": 5432, "database": "schema_drift", "user": "${POSTGRES_USER}", "password": "${POSTGRES_PASSWORD}", "sslmode": "prefer", "pool_size": 10, "pool_timeout": 30}}, "mysql": {"enabled": false, "connection": {"host": "localhost", "port": 3306, "database": "schema_drift", "user": "${MYSQL_USER}", "password": "${MYSQL_PASSWORD}", "charset": "utf8mb4", "pool_size": 10, "pool_timeout": 30}}, "sqlite": {"enabled": false, "connection": {"path": "./schema_drift.sqlite", "check_same_thread": false, "timeout": 30}}, "mongodb": {"enabled": false, "connection": {"uri": "${MONGODB_URI}", "database": "schema_drift", "collections": {"schema_versions": "schema_versions", "drift_log": "drift_log", "file_history": "file_history", "alert_history": "alert_history"}}}, "bigquery": {"enabled": false, "connection": {"project_id": "${GCP_PROJECT_ID}", "dataset_id": "schema_drift", "credentials_path": "${GOOGLE_APPLICATION_CREDENTIALS}", "location": "US"}}, "snowflake": {"enabled": false, "connection": {"account": "${SNOWFLAKE_ACCOUNT}", "user": "${SNOWFLAKE_USER}", "password": "${SNOWFLAKE_PASSWORD}", "warehouse": "COMPUTE_WH", "database": "SCHEMA_DRIFT", "schema": "PUBLIC", "role": "SYSADMIN"}}}, "migration": {"auto_migrate": true, "migration_path": "./migrations"}}, "monitoring": {"default_severity_threshold": "medium", "auto_resolve_low_severity": false, "retention_days": 90}, "alerts": {"enabled": true, "channels": {"email": {"enabled": true, "smtp": {"server": "smtp.gmail.com", "port": 587, "use_tls": true, "username": "<EMAIL>", "password": "${SMTP_PASSWORD}", "from": "<EMAIL>", "to": ["<EMAIL>", "<EMAIL>"]}, "severity_threshold": "high"}, "slack": {"enabled": true, "webhook_url": "${SLACK_WEBHOOK_URL}", "channel": "#data-alerts", "severity_threshold": "medium"}, "pagerduty": {"enabled": false, "integration_key": "${PAGERDUTY_KEY}", "severity_threshold": "critical"}}}, "schema_fingerprinting": {"sample_size": 1000, "type_inference": {"check_email": true, "check_url": true, "check_phone": true, "check_datetime": true}, "hash_algorithm": "sha256"}, "file_processing": {"parallel_processing": true, "max_workers": 4, "batch_size": 10, "timeout_seconds": 300, "max_file_size": 9999999999, "upload_directory": "data/uploads", "temp_directory": "data/temp", "supported_formats": ["csv", "xlsx", "xls", "txt", "pdf"]}, "schema_registry": {"version_strategy": "semantic", "similarity_threshold": 0.8, "default_compatibility_mode": "graceful", "enable_recommendations": true, "enable_migration_suggestions": true, "column_order_matters": false, "retention_policy": {"max_versions": 10, "max_age_days": 365}}, "processing": {"_description": "Data processing and performance settings", "chunk_size": 10000, "_chunk_size_comment": "Number of rows to process in each chunk for large files", "sample_size": 100000, "_sample_size_comment": "Maximum number of rows to sample for initial profiling", "max_memory_mb": 1024, "_max_memory_mb_comment": "Maximum memory usage in MB before issuing warnings", "enable_parallel": false, "_enable_parallel_comment": "Enable parallel processing (experimental feature)", "encoding_detection_sample": 10000, "_encoding_detection_sample_comment": "Number of bytes to sample for encoding detection", "skip_rows": 0, "_skip_rows_comment": "Number of rows to skip at the beginning of the file", "max_columns": null, "_max_columns_comment": "Maximum number of columns to analyze (null = no limit)", "low_memory": true, "_low_memory_comment": "Use low memory mode for pandas operations"}, "data_quality": {"_description": "Quality analysis thresholds and settings", "completeness_threshold": 0.95, "_completeness_threshold_comment": "Minimum ratio of non-missing values required to pass completeness check", "uniqueness_threshold": 0.99, "_uniqueness_threshold_comment": "Minimum ratio of unique values required to pass uniqueness check", "outlier_method": "iqr", "_outlier_method_comment": "Method for outlier detection: 'iqr', 'zscore', or 'modified_zscore'", "outlier_threshold": 1.5, "_outlier_threshold_comment": "Threshold multiplier for outlier detection", "date_formats": ["%Y-%m-%d", "%d/%m/%Y", "%m-%d-%Y", "%Y/%m/%d", "%d-%m-%Y", "%m/%d/%Y", "%Y-%m-%d %H:%M:%S", "%d/%m/%Y %H:%M:%S", "%Y-%m-%dT%H:%M:%S", "%Y-%m-%d %H:%M:%S.%f"], "_date_formats_comment": "List of date formats to try when detecting datetime columns", "enable_advanced_validation": true, "_enable_advanced_validation_comment": "Enable advanced validation checks (cross-column, business rules)", "categorical_threshold": 0.05, "_categorical_threshold_comment": "Maximum ratio of unique values to consider a column categorical", "max_categorical_values": 50, "_max_categorical_values_comment": "Maximum number of unique values for a column to be considered categorical", "enable_outlier_detection": true, "_enable_outlier_detection_comment": "Enable statistical outlier detection", "enable_pattern_analysis": true, "_enable_pattern_analysis_comment": "Enable text pattern consistency analysis"}, "output": {"_description": "Report generation and output settings", "generate_report": true, "_generate_report_comment": "Whether to generate analysis reports", "report_format": "json", "_report_format_comment": "Report format: 'json', 'html', 'csv', or 'all'", "output_directory": "reports", "_output_directory_comment": "Directory where reports will be saved", "include_sample_data": false, "_include_sample_data_comment": "Include sample data rows in the reports", "detailed_metrics": true, "_detailed_metrics_comment": "Include detailed column-level metrics in reports", "save_profile": true, "_save_profile_comment": "Save separate data profile report", "export_cleaned_data": false, "_export_cleaned_data_comment": "Export cleaned/processed data (if applicable)", "report_sections": {"_description": "Control which sections to include in reports", "file_info": true, "data_profile": true, "quality_metrics": true, "recommendations": true, "visualizations": false}}, "validation_rules": {"_description": "Data validation patterns and business rules", "email_pattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", "_email_pattern_comment": "Regular expression for email validation", "phone_pattern": "^\\+?[\\d\\s\\-\\(\\)]{10,}$", "_phone_pattern_comment": "Regular expression for phone number validation", "url_pattern": "^https?://[^\\s/$.?#].[^\\s]*$", "_url_pattern_comment": "Regular expression for URL validation", "numeric_ranges": {"_description": "Valid numeric ranges for specific columns", "_example_age": {"min": 0, "max": 120, "_comment": "Example: age column should be between 0 and 120"}, "_example_salary": {"min": 0, "max": 1000000, "_comment": "Example: salary should be positive and reasonable"}, "_example_percentage": {"min": 0, "max": 100, "_comment": "Example: percentage values should be 0-100"}}, "required_columns": [], "_required_columns_comment": "List of column names that must be present and non-empty", "_required_columns_example": ["id", "email", "created_date"], "categorical_columns": {"_description": "Expected categories for categorical columns", "_example_status": ["active", "inactive", "pending"], "_example_priority": ["low", "medium", "high", "critical"]}, "date_columns": {"_description": "Expected date column configurations", "_example_birth_date": {"min_date": "1900-01-01", "max_date": "2010-12-31", "format": "%Y-%m-%d"}}, "text_length_limits": {"_description": "Text length constraints for string columns", "_example_name": {"min_length": 1, "max_length": 100}, "_example_description": {"min_length": 10, "max_length": 1000}}, "custom_patterns": {"_description": "Custom validation patterns for specific business rules", "_example_product_code": "^[A-Z]{2}\\d{4}$", "_example_customer_id": "^CUST\\d{6}$", "_example_postal_code_us": "^\\d{5}(-\\d{4})?$"}}, "logging": {"_description": "Logging configuration settings", "level": "DEBUG", "_level_comment": "Logging level: DEBUG, INFO, WARNING, ERROR, CRITICAL", "log_to_file": true, "_log_to_file_comment": "Whether to write logs to a file", "log_file": "csv_analyzer.log", "_log_file_comment": "Path to the log file", "log_format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "_log_format_comment": "Format string for log messages", "max_log_size_mb": 10, "_max_log_size_mb_comment": "Maximum log file size before rotation", "log_backup_count": 5, "_log_backup_count_comment": "Number of backup log files to keep"}, "advanced": {"_description": "Advanced configuration options", "memory_optimization": {"enable_gc_frequency": 10, "_enable_gc_frequency_comment": "Run garbage collection every N chunks", "chunk_overlap": 0, "_chunk_overlap_comment": "Number of overlapping rows between chunks (for continuity)", "use_memory_mapping": false, "_use_memory_mapping_comment": "Use memory mapping for very large files"}, "performance_tuning": {"enable_caching": true, "_enable_caching_comment": "Cache intermediate results", "cache_size_mb": 100, "_cache_size_mb_comment": "Maximum cache size in MB", "parallel_chunk_processing": false, "_parallel_chunk_processing_comment": "Process chunks in parallel (requires more memory)"}, "experimental_features": {"ml_type_detection": false, "_ml_type_detection_comment": "Use machine learning for improved type detection", "smart_sampling": false, "_smart_sampling_comment": "Use intelligent sampling strategies", "auto_repair": false, "_auto_repair_comment": "Attempt automatic data repair for common issues"}}, "integrations": {"_description": "External system integrations", "database": {"enable_db_export": false, "_enable_db_export_comment": "Export results to database", "connection_string": "", "_connection_string_comment": "Database connection string (if applicable)"}, "cloud_storage": {"enable_cloud_export": false, "_enable_cloud_export_comment": "Export reports to cloud storage", "provider": "s3", "_provider_comment": "Cloud provider: 's3', 'azure', 'gcp'", "bucket_name": "", "_bucket_name_comment": "Cloud storage bucket/container name"}, "notifications": {"enable_email_alerts": false, "_enable_email_alerts_comment": "Send email alerts for quality issues", "email_recipients": [], "_email_recipients_comment": "List of email addresses for notifications", "alert_thresholds": {"critical_quality_score": 0.5, "high_missing_data": 0.2, "high_duplicate_rate": 0.1}}}, "profiles": {"_description": "Predefined configuration profiles for different use cases", "development": {"processing.chunk_size": 1000, "processing.sample_size": 10000, "logging.level": "DEBUG", "output.detailed_metrics": true}, "production": {"processing.chunk_size": 50000, "processing.sample_size": 1000000, "logging.level": "INFO", "output.detailed_metrics": false, "advanced.performance_tuning.enable_caching": true}, "large_files": {"processing.chunk_size": 5000, "processing.max_memory_mb": 512, "processing.low_memory": true, "advanced.memory_optimization.enable_gc_frequency": 5}, "strict_validation": {"data_quality.completeness_threshold": 0.99, "data_quality.uniqueness_threshold": 0.995, "data_quality.enable_advanced_validation": true, "validation_rules.required_columns": ["id"]}}, "matcher_agent": {"weights": {"vector_embedding": 0.5, "synonym": 0.2, "description": 0.2, "column_name": 0.1}, "min_confidence_threshold": 0.3, "max_matches_per_source": 3, "enable_fuzzy_matching": true, "synonym_similarity_threshold": 0.1, "description_preprocessing": {"remove_stopwords": true, "use_stemming": false, "min_text_length": 3}, "vector_similarity": {"metric": "cosine", "normalize_embeddings": true}}, "knowledgebase": {"vector_db": {"_description": "Vector database configuration for semantic search", "persist_directory": "data/vector_db", "learning_directory": "data/learning", "embedding_model": "all-MiniLM-L6-v2", "collection_prefix": "", "similarity_threshold": 0.6, "max_results": 10, "chromadb_settings": {"anonymized_telemetry": false, "allow_reset": true, "enable_logging": true}, "collections": {"cpg_uploaded_files": {"description": "User uploaded files with metadata and content", "auto_create": true}, "cpg_learning_feedback": {"description": "Learning feedback and classification corrections", "auto_create": true}, "cpg_schemas": {"description": "Database schema patterns and structures", "auto_create": true}, "cpg_content": {"description": "Data content patterns and statistical summaries", "auto_create": true}, "cpg_domain_knowledge": {"description": "CPG domain expertise and business rules", "auto_create": true}}, "embedding_settings": {"batch_size": 100, "max_text_length": 2000, "normalize_embeddings": true}}, "file_processing": {"upload_directory": "data/uploads", "temp_directory": "data/temp", "learning_directory": "data/learning", "max_file_size_mb": 50, "supported_formats": ["csv", "xlsx", "xls", "txt", "pdf"]}, "classification": {"confidence_threshold": 0.6, "min_similarity_score": 0.5, "data_types": ["syndicated", "pos", "promotion"], "enable_learning": true}, "learning": {"auto_ask_feedback": true, "min_confidence_for_feedback": 0.8, "store_corrections": true, "learning_batch_size": 10, "retrain_threshold": 50}, "document_types": {"schema_examples": ["pos_schema", "syndicated_schema", "promotion_schema"], "data_samples": ["pos_data", "syndicated_data", "promotion_data"], "documentation": ["classification_guide", "vendor_documentation", "business_rules"], "reference_materials": ["data_dictionary", "field_mappings", "transformation_rules"]}}, "agentic_system": {"_description": "Enhanced agentic system with vector DB integration", "min_consensus_threshold": 0.4, "min_confidence_threshold": 0.3, "min_participating_agents": 2, "max_processing_time": 60.0, "enable_azure_openai": true, "enable_learning": true, "auto_store_results": true, "enable_vector_search": true, "parallel_processing": true, "vector_similarity_threshold": 0.6, "auto_store_threshold": 0.7, "vector_search_timeout": 10.0, "quality_thresholds": {"high_quality": 0.8, "medium_quality": 0.6, "low_quality": 0.4}, "agent_weights": {"schema_analyzer": 0.25, "content_analyzer": 0.2, "pattern_recognizer": 0.2, "domain_expert": 0.35, "ml_classifier": 0.3}, "voting_strategies": ["weighted_average", "confidence_weighted", "expert_priority", "ensemble_learning", "vector_enhanced"], "vector_enhancements": {"_description": "Vector DB enhancement settings", "confidence_boost_threshold": 0.7, "similarity_boost_factor": 0.2, "enable_collection_weighting": true, "collection_weights": {"cpg_learning_feedback": 0.35, "cpg_uploaded_files": 0.25, "cpg_domain_knowledge": 0.2, "cpg_schemas": 0.15, "cpg_content": 0.05}, "boost_thresholds": {"high_similarity": {"threshold": 0.8, "avg_threshold": 0.7, "boost": 0.25}, "medium_similarity": {"threshold": 0.7, "avg_threshold": 0.6, "boost": 0.15}, "low_similarity": {"threshold": 0.6, "boost": 0.08}}, "agent_vector_multipliers": {"schema_analyzer": 1.1, "content_analyzer": 1.2, "domain_expert": 1.3, "ml_classifier": 1.1, "pattern_recognizer": 1.0}}, "logging": {"enable_vector_logging": true, "log_similarity_scores": true, "log_search_queries": true, "log_boost_calculations": true}}, "vector_db": {"_description": "Vector database configuration for semantic search", "persist_directory": "data/vector_db", "learning_directory": "data/learning", "embedding_model": "all-MiniLM-L6-v2", "collection_prefix": "", "similarity_threshold": 0.6, "max_results": 10, "chromadb_settings": {"anonymized_telemetry": false, "allow_reset": true, "enable_logging": true}, "collections": {"cpg_uploaded_files": {"description": "User uploaded files with metadata and content", "auto_create": true}, "cpg_learning_feedback": {"description": "Learning feedback and classification corrections", "auto_create": true}, "cpg_schemas": {"description": "Database schema patterns and structures", "auto_create": true}, "cpg_content": {"description": "Data content patterns and statistical summaries", "auto_create": true}, "cpg_domain_knowledge": {"description": "CPG domain expertise and business rules", "auto_create": true}}, "embedding_settings": {"batch_size": 100, "max_text_length": 2000, "normalize_embeddings": true}}}