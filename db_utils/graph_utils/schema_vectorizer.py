import json
import os
from neo4j import GraphDatabase
from sentence_transformers import SentenceTransformer
import numpy as np

class SchemaVectorizer:
    def __init__(self, config_path="../../config/config.json"):
        self.config = self.load_config(config_path)
        self.driver = self.connect_to_neo4j()
        self.model = SentenceTransformer('all-MiniLM-L6-v2')
    
    def load_config(self, config_path):
        """Load configuration from JSON file"""
        with open(config_path, 'r') as f:
            return json.load(f)
    
    def connect_to_neo4j(self):
        """Establish connection to Neo4j database"""
        return GraphDatabase.driver(
            self.config['neo4j']['uri'],
            auth=(self.config['neo4j']['user'], self.config['neo4j']['password']),
            connection_timeout=self.config.get('connection_timeout', 30)
        )
    
    def extract_table_nodes(self):
        """Extract table nodes with business properties"""
        query = """
        MATCH (t:Table)
        RETURN t.table_name as table_name,
               t.business_name as business_name,
               t.business_description as business_description,
               t.business_domain as business_domain,
               t.business_subdomain as business_subdomain,
               t.applicable_domains as applicable_domains,
               t.business_concepts as business_concepts,
               t.business_synonyms as business_synonyms,
               elementId(t) as node_elementId
        """
        with self.driver.session() as session:
            return list(session.run(query))
    
    def extract_column_nodes(self):
        """Extract column nodes with business properties"""
        query = """
        MATCH (t:Table)-[:CONTAINS]->(c:Column)
        RETURN c.column_name as column_name,
               c.business_name as business_name,
               c.business_description as business_description,
               c.applicable_domains as applicable_domains,
               c.business_synonyms as business_synonyms,
               c.data_type as data_type,
               t.table_name as table_name,
               elementId(c) as node_elementId
        """
        with self.driver.session() as session:
            return list(session.run(query))
    
    def create_table_text_representation(self, table_record):
        """Create comprehensive text representation for table nodes"""
        parts = []
        
        if table_record['table_name']:
            parts.append(f"Table: {table_record['table_name']}")
        
        if table_record['business_name']:
            parts.append(f"Business Name: {table_record['business_name']}")
        
        if table_record['business_description']:
            parts.append(f"Description: {table_record['business_description']}")
        
        if table_record['business_domain']:
            parts.append(f"Domain: {table_record['business_domain']}")
        
        if table_record['business_subdomain']:
            parts.append(f"Subdomain: {table_record['business_subdomain']}")
        
        if table_record['applicable_domains']:
            # Handle list or string format
            domains = table_record['applicable_domains']
            if isinstance(domains, list):
                domains_str = ", ".join(domains)
            else:
                domains_str = str(domains)
            parts.append(f"Applicable Domains: {domains_str}")
        
        if table_record['business_concepts']:
            # Handle list or string format
            concepts = table_record['business_concepts']
            if isinstance(concepts, list):
                concepts_str = ", ".join(concepts)
            else:
                concepts_str = str(concepts)
            parts.append(f"Business Concepts: {concepts_str}")
        
        if table_record['business_synonyms']:
            # Handle list or string format
            synonyms = table_record['business_synonyms']
            if isinstance(synonyms, list):
                synonyms_str = ", ".join(synonyms)
            else:
                synonyms_str = str(synonyms)
            parts.append(f"Synonyms: {synonyms_str}")
        
        return " | ".join(parts) if parts else table_record['table_name'] or "Unknown Table"
    
    def create_column_text_representation(self, column_record):
        """Create comprehensive text representation for column nodes"""
        parts = []
        
        if column_record['column_name']:
            parts.append(f"Column: {column_record['column_name']}")
        
        if column_record['table_name']:
            parts.append(f"Table: {column_record['table_name']}")
        
        if column_record['business_name']:
            parts.append(f"Business Name: {column_record['business_name']}")
        
        if column_record['business_description']:
            parts.append(f"Description: {column_record['business_description']}")
        
        if column_record['data_type']:
            parts.append(f"Data Type: {column_record['data_type']}")
        
        if column_record['applicable_domains']:
            # Handle list or string format
            domains = column_record['applicable_domains']
            if isinstance(domains, list):
                domains_str = ", ".join(domains)
            else:
                domains_str = str(domains)
            parts.append(f"Applicable Domains: {domains_str}")
        
        if column_record['business_synonyms']:
            # Handle list or string format
            synonyms = column_record['business_synonyms']
            if isinstance(synonyms, list):
                synonyms_str = ", ".join(synonyms)
            else:
                synonyms_str = str(synonyms)
            parts.append(f"Synonyms: {synonyms_str}")
        
        return " | ".join(parts) if parts else column_record['column_name'] or "Unknown Column"
    
    def vectorize_text(self, text):
        """Generate vector embedding for text using pre-trained model"""
        return self.model.encode(text).tolist()
    
    def store_table_vector(self, node_elementId, vector):
        """Store vector embedding in table node"""
        query = """
        MATCH (t:Table)
        WHERE elementId(t) = $node_elementId
        SET t.embedding = $vector
        """
        with self.driver.session() as session:
            session.run(query, node_elementId=node_elementId, vector=vector)
    
    def store_column_vector(self, node_elementId, vector):
        """Store vector embedding in column node"""
        query = """
        MATCH (c:Column)
        WHERE elementId(c) = $node_elementId
        SET c.embedding = $vector
        """
        with self.driver.session() as session:
            session.run(query, node_elementId=node_elementId, vector=vector)
    
    def process_tables(self):
        """Process all table nodes: extract, vectorize, and store"""
        print("Processing table nodes...")
        tables = self.extract_table_nodes()
        
        for i, table in enumerate(tables):
            try:
                text_repr = self.create_table_text_representation(table)
                vector = self.vectorize_text(text_repr)
                self.store_table_vector(table['node_elementId'], vector)
                
                if (i + 1) % 10 == 0:
                    print(f"Processed {i + 1}/{len(tables)} tables")
                    
            except Exception as e:
                print(f"Error processing table {table.get('table_name', 'unknown')}: {e}")
                continue
        
        print(f"Completed processing {len(tables)} table nodes")
    
    def process_columns(self):
        """Process all column nodes: extract, vectorize, and store"""
        print("Processing column nodes...")
        columns = self.extract_column_nodes()
        
        for i, column in enumerate(columns):
            try:
                text_repr = self.create_column_text_representation(column)
                vector = self.vectorize_text(text_repr)
                self.store_column_vector(column['node_elementId'], vector)
                
                if (i + 1) % 50 == 0:
                    print(f"Processed {i + 1}/{len(columns)} columns")
                    
            except Exception as e:
                print(f"Error processing column {column.get('column_name', 'unknown')}: {e}")
                continue
        
        print(f"Completed processing {len(columns)} column nodes")
    
    def vectorize_schema(self):
        """Main method to vectorize entire schema"""
        try:
            print("Starting schema vectorization...")
            print(f"Using model: {self.model}")
            
            self.process_tables()
            self.process_columns()
            
            print("Schema vectorization completed successfully")
            
        except Exception as e:
            print(f"Error during vectorization: {e}")
            raise
        finally:
            self.driver.close()
    
    def test_connection(self):
        """Test Neo4j connection and basic queries"""
        try:
            with self.driver.session() as session:
                result = session.run("RETURN 1 as test")
                record = result.single()
                print(f"Connection test successful: {record['test']}")
                
                # Test table count
                table_count = session.run("MATCH (t:Table) RETURN count(t) as count").single()['count']
                print(f"Found {table_count} table nodes")
                
                # Test column count
                column_count = session.run("MATCH (c:Column) RETURN count(c) as count").single()['count']
                print(f"Found {column_count} column nodes")
                
                return True
        except Exception as e:
            print(f"Connection test failed: {e}")
            return False

def main():
    """Main execution function"""
    try:
        vectorizer = SchemaVectorizer()
        
        # Test connection first
        if not vectorizer.test_connection():
            print("Connection test failed. Please check your configuration.")
            return
        
        # Start vectorization
        vectorizer.vectorize_schema()
        
    except FileNotFoundError:
        print("Configuration file not found at ../../config/config.json")
        print("Please ensure the config file exists with proper Neo4j credentials.")
    except Exception as e:
        print(f"Unexpected error: {e}")

if __name__ == "__main__":
    main()