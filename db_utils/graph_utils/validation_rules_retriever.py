import json
import argparse
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from neo4j import GraphDatabase
import os

class ValidationRuleRetriever:
    def __init__(self, config_path: str = "../../config/config.json"):
        """Initialize with Neo4j connection from config file"""
        self.config = self._load_config(config_path)
        self.driver = None
        self._connect()
    
    def _load_config(self, config_path: str) -> Dict:
        """Load configuration from JSON file"""
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"Config file not found: {config_path}")
        
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        if 'neo4j' not in config:
            raise ValueError("Neo4j configuration not found in config file")
        
        return config['neo4j']
    
    def _connect(self):
        """Establish connection to Neo4j database"""
        try:
            self.driver = GraphDatabase.driver(
                self.config['uri'],
                auth=(self.config['user'], self.config['password']),
                connection_timeout=self.config.get('connection_timeout', 30)
            )
            # Test connection
            self.driver.verify_connectivity()
        except Exception as e:
            raise ConnectionError(f"Failed to connect to Neo4j: {str(e)}")
    
    def get_validations(self, 
                       table_spec: Union[str, Dict[str, Optional[List[str]]]], 
                       column_names: Optional[List[str]] = None,
                       applicable_domains: Optional[str] = None,
                       business_criticality: Optional[str] = None) -> Dict[str, Any]:
        """
        Retrieve validation rules for tables with optional column filters
        
        Args:
            table_spec: Either:
                - Single table name (str) - for backward compatibility
                - Dict mapping table names to column lists, e.g.:
                  {"DIM_PRODUCT_MASTER": ["PRODUCT_ID", "UPC_CODE"],
                   "DIM_CUSTOMER": ["CUSTOMER_ID", "EMAIL"],
                   "DIM_BRAND": None}  # None means get all columns
            column_names: Column list for single table mode (backward compatibility)
            applicable_domains: Optional domain filter (e.g., 'pharmaceuticals')
            business_criticality: Optional criticality filter (e.g., 'critical')
            
        Returns:
            Dictionary containing validation rules in JSON format
        """
        
        # Normalize input for backward compatibility
        if isinstance(table_spec, str):
            # Single table mode (backward compatible)
            table_column_map = {table_spec: column_names}
            single_table_mode = True
        else:
            # Multiple table mode with specific columns
            table_column_map = table_spec
            single_table_mode = False
        
        # For multiple tables, we'll use UNION to combine results
        if len(table_column_map) > 1:
            return self._get_validations_multiple_tables(
                table_column_map, applicable_domains, business_criticality, single_table_mode
            )
        
        # Single table query (existing logic)
        table_name = list(table_column_map.keys())[0]
        columns = table_column_map[table_name]
        
        params = {
            'table': table_name,
            'columns': columns,
            'domain': applicable_domains,
            'criticality': business_criticality
        }
        
        query = """
        MATCH (t:Table {table_id: $table})
        OPTIONAL MATCH (t)-[:CONTAINS]->(c:Column)
        WHERE $columns IS NULL OR c.column_name IN $columns
        OPTIONAL MATCH (c)-[:HAS_VALIDATION]->(v:ValidationRule)
        WHERE v.is_active = true
            AND ($domain IS NULL OR $domain IN v.applicable_domains OR 'ALL' IN v.applicable_domains)
            AND ($criticality IS NULL OR v.business_criticality = $criticality)
        WITH t, c, collect(DISTINCT {
            rule_id: v.rule_id,
            rule_name: v.rule_name,
            validation_type: v.validation_type,
            validation_rule: v.validation_rule,
            error_message: v.error_message,
            priority: v.priority,
            business_criticality: v.business_criticality,
            applicable_domains: v.applicable_domains
        }) as column_validations
        
        WITH t, 
             collect(DISTINCT {
                 column_name: c.column_name,
                 column_id: c.column_id,
                 validations: CASE WHEN size(column_validations) > 0 THEN column_validations ELSE [] END
             }) as columns_with_validations
        
        OPTIONAL MATCH (t)-[:HAS_CROSS_FIELD_VALIDATION]->(cv:ValidationRule)
        WHERE cv.is_active = true
            AND ($domain IS NULL OR $domain IN cv.applicable_domains OR 'ALL' IN cv.applicable_domains)
            AND ($criticality IS NULL OR cv.business_criticality = $criticality)
            AND ($columns IS NULL OR 
                 ANY(col IN split(cv.column_name, ',') WHERE trim(col) IN $columns))
        
        RETURN t.table_id as table_name,
               columns_with_validations,
               collect(DISTINCT {
                   rule_id: cv.rule_id,
                   rule_name: cv.rule_name,
                   columns: cv.column_name,
                   validation_type: cv.validation_type,
                   validation_rule: cv.validation_rule,
                   error_message: cv.error_message,
                   priority: cv.priority,
                   business_criticality: cv.business_criticality,
                   applicable_domains: cv.applicable_domains
               }) as cross_field_validations
        """
        
        with self.driver.session() as session:
            result = session.run(query, **params)
            record = result.single()
            
            if not record or not record['table_name']:
                return {
                    "error": f"Table '{table_name}' not found",
                    "query_parameters": {
                        "table_name": table_name,
                        "column_names": columns,
                        "applicable_domains": applicable_domains,
                        "business_criticality": business_criticality
                    },
                    "query_timestamp": datetime.now().isoformat()
                }
            
            # Process results
            columns_data = record['columns_with_validations']
            cross_field_data = record['cross_field_validations']
            
            # Build column validations dictionary
            column_validations = {}
            columns_with_rules = 0
            total_validations = 0
            total_columns_in_result = 0
            
            for col in columns_data:
                if col['column_name']:
                    total_columns_in_result += 1
                    # Filter out null validations
                    valid_rules = [v for v in col['validations'] if v['rule_id'] is not None]
                    if valid_rules:
                        column_validations[col['column_name']] = valid_rules
                        columns_with_rules += 1
                        total_validations += len(valid_rules)
            
            # Filter cross-field validations
            cross_field_validations = [v for v in cross_field_data if v['rule_id'] is not None]
            total_validations += len(cross_field_validations)
            
            # Build response
            response = {
                "table_name": table_name,
                "filters_applied": {
                    "column_names": columns,
                    "applicable_domains": applicable_domains,
                    "business_criticality": business_criticality
                },
                "column_validations": column_validations,
                "cross_field_validations": cross_field_validations,
                "metadata": {
                    "total_columns": total_columns_in_result,
                    "columns_with_validations": columns_with_rules,
                    "total_validations": total_validations,
                    "column_filter_applied": columns is not None,
                    "query_timestamp": datetime.now().isoformat()
                }
            }
            
            return response
    
    def _get_validations_multiple_tables(self, 
                                       table_column_map: Dict[str, Optional[List[str]]],
                                       applicable_domains: Optional[str],
                                       business_criticality: Optional[str],
                                       single_table_mode: bool) -> Dict[str, Any]:
        """Handle multiple table queries using separate queries for each table"""
        
        results = {}
        overall_total_validations = 0
        tables_found = []
        
        # Query each table separately
        for table_name, columns in table_column_map.items():
            params = {
                'table': table_name,
                'columns': columns,
                'domain': applicable_domains,
                'criticality': business_criticality
            }
            
            query = """
            MATCH (t:Table {table_id: $table})
            OPTIONAL MATCH (t)-[:CONTAINS]->(c:Column)
            WHERE $columns IS NULL OR c.column_name IN $columns
            OPTIONAL MATCH (c)-[:HAS_VALIDATION]->(v:ValidationRule)
            WHERE v.is_active = true
                AND ($domain IS NULL OR $domain IN v.applicable_domains OR 'ALL' IN v.applicable_domains)
                AND ($criticality IS NULL OR v.business_criticality = $criticality)
            WITH t, c, collect(DISTINCT {
                rule_id: v.rule_id,
                rule_name: v.rule_name,
                validation_type: v.validation_type,
                validation_rule: v.validation_rule,
                error_message: v.error_message,
                priority: v.priority,
                business_criticality: v.business_criticality,
                applicable_domains: v.applicable_domains
            }) as column_validations
            
            WITH t, 
                 collect(DISTINCT {
                     column_name: c.column_name,
                     column_id: c.column_id,
                     validations: CASE WHEN size(column_validations) > 0 THEN column_validations ELSE [] END
                 }) as columns_with_validations
            
            OPTIONAL MATCH (t)-[:HAS_CROSS_FIELD_VALIDATION]->(cv:ValidationRule)
            WHERE cv.is_active = true
                AND ($domain IS NULL OR $domain IN cv.applicable_domains OR 'ALL' IN cv.applicable_domains)
                AND ($criticality IS NULL OR cv.business_criticality = $criticality)
                AND ($columns IS NULL OR 
                     ANY(col IN split(cv.column_name, ',') WHERE trim(col) IN $columns))
            
            RETURN t.table_id as table_name,
                   columns_with_validations,
                   collect(DISTINCT {
                       rule_id: cv.rule_id,
                       rule_name: cv.rule_name,
                       columns: cv.column_name,
                       validation_type: cv.validation_type,
                       validation_rule: cv.validation_rule,
                       error_message: cv.error_message,
                       priority: cv.priority,
                       business_criticality: cv.business_criticality,
                       applicable_domains: cv.applicable_domains
                   }) as cross_field_validations
            """
            
            with self.driver.session() as session:
                result = session.run(query, **params)
                record = result.single()
                
                if record and record['table_name']:
                    tables_found.append(table_name)
                    columns_data = record['columns_with_validations']
                    cross_field_data = record['cross_field_validations']
                    
                    # Build column validations dictionary
                    column_validations = {}
                    columns_with_rules = 0
                    total_validations = 0
                    total_columns_in_result = 0
                    
                    for col in columns_data:
                        if col['column_name']:
                            total_columns_in_result += 1
                            # Filter out null validations
                            valid_rules = [v for v in col['validations'] if v['rule_id'] is not None]
                            if valid_rules:
                                column_validations[col['column_name']] = valid_rules
                                columns_with_rules += 1
                                total_validations += len(valid_rules)
                    
                    # Filter cross-field validations
                    cross_field_validations = [v for v in cross_field_data if v['rule_id'] is not None]
                    total_validations += len(cross_field_validations)
                    overall_total_validations += total_validations
                    
                    # Build table-specific response
                    results[table_name] = {
                        "column_validations": column_validations,
                        "cross_field_validations": cross_field_validations,
                        "metadata": {
                            "total_columns": total_columns_in_result,
                            "columns_with_validations": columns_with_rules,
                            "total_validations": total_validations,
                            "column_filter_applied": columns is not None
                        }
                    }
        
        # Build response
        requested_tables = list(table_column_map.keys())
        response = {
            "query_parameters": {
                "table_column_specifications": table_column_map,
                "applicable_domains": applicable_domains,
                "business_criticality": business_criticality
            },
            "results": results,
            "overall_metadata": {
                "total_tables_queried": len(requested_tables),
                "tables_found": len(tables_found),
                "tables_not_found": list(set(requested_tables) - set(tables_found)),
                "total_validations": overall_total_validations,
                "query_timestamp": datetime.now().isoformat()
            }
        }
        
        return response
    
    def close(self):
        """Close the Neo4j connection"""
        if self.driver:
            self.driver.close()


def main():
    """Main function to test the validation retriever"""
    parser = argparse.ArgumentParser(
        description='Retrieve validation rules from Neo4j',
        epilog='Examples:\n'
               '  python validation_retriever.py DIM_PRODUCT_MASTER\n'
               '  python validation_retriever.py DIM_PRODUCT_MASTER:PRODUCT_ID,UPC_CODE\n'
               '  python validation_retriever.py DIM_PRODUCT_MASTER:PRODUCT_ID DIM_CUSTOMER:CUSTOMER_ID,EMAIL\n',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    parser.add_argument('table_specs', nargs='+', 
                       help='Table specifications in format TABLE_NAME or TABLE_NAME:COL1,COL2,COL3')
    parser.add_argument('--domain', help='Filter by applicable domain (e.g., pharmaceuticals)')
    parser.add_argument('--criticality', help='Filter by business criticality (e.g., critical)')
    parser.add_argument('--config', default='../../config/config.json', help='Path to config file')
    
    args = parser.parse_args()
    
    try:
        # Create retriever instance
        retriever = ValidationRuleRetriever(config_path=args.config)
        
        # Parse table specifications
        if len(args.table_specs) == 1 and ':' not in args.table_specs[0]:
            # Single table without columns - backward compatible mode
            table_spec = args.table_specs[0]
        else:
            # Parse table:column specifications
            table_spec = {}
            for spec in args.table_specs:
                if ':' in spec:
                    table_name, columns_str = spec.split(':', 1)
                    columns = [col.strip() for col in columns_str.split(',')]
                    table_spec[table_name] = columns
                else:
                    # Table without specific columns - get all
                    table_spec[spec] = None
        
        # Get validations
        result = retriever.get_validations(
            table_spec=table_spec,
            applicable_domains=args.domain,
            business_criticality=args.criticality
        )
        
        # Print results
        print(json.dumps(result, indent=2))
        
        # Close connection
        retriever.close()
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return 1
    
    return 0


if __name__ == "__main__":
    main()