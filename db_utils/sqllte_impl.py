import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from contextlib import contextmanager
import logging
from .database_interface import DatabaseInterface

logger = logging.getLogger(__name__)

class SQLiteImplementation(DatabaseInterface):
    """SQLite implementation of database interface"""
    
    def __init__(self, config: Dict):
        self.config = config['database']['backends']['sqlite']['connection']
        self.db_path = self.config['path']
        self.initialize()
    
    @contextmanager
    def get_connection(self):
        """Context manager for SQLite connections"""
        conn = sqlite3.connect(
            self.db_path,
            check_same_thread=self.config.get('check_same_thread', False),
            timeout=self.config.get('timeout', 30)
        )
        conn.row_factory = sqlite3.Row
        try:
            yield conn
            conn.commit()
        except Exception:
            conn.rollback()
            raise
        finally:
            conn.close()
    
    def initialize(self) -> None:
        """Initialize database schema"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Schema versions table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS schema_versions (
                    version_id TEXT PRIMARY KEY,
                    fingerprint TEXT NOT NULL,
                    schema_details TEXT NOT NULL,
                    format_type TEXT NOT NULL,
                    created_at TIMESTAMP NOT NULL,
                    created_by TEXT DEFAULT CURRENT_USER,
                    file_example TEXT NOT NULL,
                    is_active INTEGER DEFAULT 1,
                    tags TEXT DEFAULT '[]',
                    metadata TEXT DEFAULT '{}'
                )
            ''')
            
            # Create indexes
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_schema_fingerprint 
                ON schema_versions(fingerprint)
            ''')
            
            # Drift log table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS drift_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    file_path TEXT NOT NULL,
                    timestamp TIMESTAMP NOT NULL,
                    drift_type TEXT NOT NULL,
                    details TEXT NOT NULL,
                    severity TEXT NOT NULL,
                    resolved INTEGER DEFAULT 0,
                    resolved_at TIMESTAMP,
                    resolution_notes TEXT,
                    expected_version_id TEXT,
                    actual_fingerprint TEXT,
                    alert_sent INTEGER DEFAULT 0,
                    alert_sent_at TIMESTAMP
                )
            ''')
            
            # File history table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS file_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    file_path TEXT NOT NULL,
                    fingerprint TEXT NOT NULL,
                    processed_at TIMESTAMP NOT NULL,
                    schema_version_id TEXT,
                    file_size_bytes INTEGER,
                    row_count INTEGER,
                    processing_time_ms INTEGER,
                    processing_status TEXT DEFAULT 'success',
                    error_message TEXT,
                    metadata TEXT DEFAULT '{}'
                )
            ''')
            
            # Alert history table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS alert_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    drift_log_ids TEXT NOT NULL,
                    alert_type TEXT NOT NULL,
                    channel TEXT NOT NULL,
                    sent_at TIMESTAMP NOT NULL,
                    success INTEGER NOT NULL,
                    error_message TEXT,
                    metadata TEXT DEFAULT '{}'
                )
            ''')
            
            cursor.close()
            logger.info("SQLite schema initialized successfully")
    
    def save_schema_version(self, version_data: Dict) -> bool:
        """Save a new schema version"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO schema_versions 
                    (version_id, fingerprint, schema_details, format_type, 
                     created_at, file_example, tags, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    version_data['version_id'],
                    version_data['fingerprint'],
                    json.dumps(version_data['schema_details']),
                    version_data['format_type'],
                    version_data['created_at'].isoformat(),
                    version_data['file_example'],
                    json.dumps(version_data.get('tags', [])),
                    json.dumps(version_data.get('metadata', {}))
                ))
                cursor.close()
                return True
        except Exception as e:
            logger.error(f"Failed to save schema version: {e}")
            return False
    
    # Implement other methods similarly...
    
    def close(self) -> None:
        """Close database connection (no-op for SQLite)"""
        pass