import duckdb
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from contextlib import contextmanager
import logging
from .database_interface import DatabaseInterface

logger = logging.getLogger(__name__)

class DuckDBImplementation(DatabaseInterface):
    """DuckDB implementation of database interface"""
    
    def __init__(self, config: Dict):
        self.config = config['database']['backends']['duckdb']['connection']
        self.db_path = self.config['path']
        self.connection_options = {
            k: v for k, v in self.config.items() 
            if k not in ['path']
        }
        self.initialize()
    
    @contextmanager
    def get_connection(self):
        """Context manager for DuckDB connections"""
        # Separate read_only from other config options
        read_only = self.connection_options.get('read_only', False)
        
        # Create config dict for DuckDB-specific options
        duckdb_config = {}
        if 'threads' in self.connection_options:
            duckdb_config['threads'] = self.connection_options['threads']
        
        # Connect with proper parameters
        conn = duckdb.connect(
            database=self.db_path,
            read_only=read_only,
            config=duckdb_config if duckdb_config else None
        )
        try:
            yield conn
        finally:
            conn.close()
    
    def initialize(self) -> None:
        """Initialize database schema"""
        with self.get_connection() as conn:
            # Create sequences FIRST
            conn.execute('CREATE SEQUENCE IF NOT EXISTS seq_drift_id START 1')
            conn.execute('CREATE SEQUENCE IF NOT EXISTS seq_file_history_id START 1')
            conn.execute('CREATE SEQUENCE IF NOT EXISTS seq_evolution_id START 1')
            conn.execute('CREATE SEQUENCE IF NOT EXISTS seq_alert_id START 1')
            
            # Now create tables that use these sequences
            
            # Schema versions table
            conn.execute('''
                CREATE TABLE IF NOT EXISTS schema_versions (
                    version_id VARCHAR PRIMARY KEY,
                    fingerprint VARCHAR NOT NULL,
                    schema_details JSON NOT NULL,
                    format_type VARCHAR NOT NULL,
                    created_at TIMESTAMP NOT NULL,
                    created_by VARCHAR DEFAULT CURRENT_USER,
                    file_example VARCHAR NOT NULL,
                    is_active BOOLEAN DEFAULT TRUE,
                    tags JSON DEFAULT '[]'::JSON,
                    metadata JSON DEFAULT '{}'::JSON
                )
            ''')
            
            # Create indexes
            conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_schema_fingerprint 
                ON schema_versions(fingerprint)
            ''')
            
            conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_schema_format_active 
                ON schema_versions(format_type, is_active, created_at DESC)
            ''')
            
            # Drift log table
            conn.execute('''
                CREATE TABLE IF NOT EXISTS drift_log (
                    id INTEGER PRIMARY KEY DEFAULT nextval('seq_drift_id'),
                    file_path VARCHAR NOT NULL,
                    timestamp TIMESTAMP NOT NULL,
                    drift_type VARCHAR NOT NULL,
                    details JSON NOT NULL,
                    severity VARCHAR NOT NULL,
                    resolved BOOLEAN DEFAULT FALSE,
                    resolved_at TIMESTAMP,
                    resolution_notes VARCHAR,
                    expected_version_id VARCHAR,
                    actual_fingerprint VARCHAR,
                    alert_sent BOOLEAN DEFAULT FALSE,
                    alert_sent_at TIMESTAMP
                )
            ''')
            
            # Create index for unresolved drifts
            conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_drift_unresolved 
                ON drift_log(resolved, severity, timestamp DESC)
            ''')
            
            # File processing history
            conn.execute('''
                CREATE TABLE IF NOT EXISTS file_history (
                    id INTEGER PRIMARY KEY DEFAULT nextval('seq_file_history_id'),
                    file_path VARCHAR NOT NULL,
                    fingerprint VARCHAR NOT NULL,
                    processed_at TIMESTAMP NOT NULL,
                    schema_version_id VARCHAR,
                    file_size_bytes BIGINT,
                    row_count BIGINT,
                    processing_time_ms INTEGER,
                    processing_status VARCHAR DEFAULT 'success',
                    error_message VARCHAR,
                    metadata JSON DEFAULT '{}'::JSON
                )
            ''')
            
            # Schema evolution tracking
            conn.execute('''
                CREATE TABLE IF NOT EXISTS schema_evolution (
                    id INTEGER PRIMARY KEY DEFAULT nextval('seq_evolution_id'),
                    from_version_id VARCHAR NOT NULL,
                    to_version_id VARCHAR NOT NULL,
                    evolution_type VARCHAR NOT NULL,
                    changes JSON NOT NULL,
                    evolved_at TIMESTAMP NOT NULL,
                    evolved_by VARCHAR DEFAULT CURRENT_USER,
                    approved BOOLEAN DEFAULT FALSE,
                    approved_by VARCHAR,
                    approved_at TIMESTAMP,
                    notes VARCHAR
                )
            ''')
            
            # Alert history
            conn.execute('''
                CREATE TABLE IF NOT EXISTS alert_history (
                    id INTEGER PRIMARY KEY DEFAULT nextval('seq_alert_id'),
                    drift_log_ids JSON NOT NULL,
                    alert_type VARCHAR NOT NULL,
                    channel VARCHAR NOT NULL,
                    sent_at TIMESTAMP NOT NULL,
                    success BOOLEAN NOT NULL,
                    error_message VARCHAR,
                    metadata JSON DEFAULT '{}'::JSON
                )
            ''')
            
            # Create views
            self._create_views(conn)
            
            logger.info("DuckDB schema initialized successfully")
            
    def save_schema_version(self, version_data: Dict) -> bool:
        """Save a new schema version"""
        try:
            with self.get_connection() as conn:
                conn.execute('''
                    INSERT INTO schema_versions 
                    (version_id, fingerprint, schema_details, format_type, 
                     created_at, file_example, tags, metadata)
                    VALUES (?, ?, ?::JSON, ?, ?, ?, ?::JSON, ?::JSON)
                ''', [
                    version_data['version_id'],
                    version_data['fingerprint'],
                    json.dumps(version_data['schema_details']),
                    version_data['format_type'],
                    version_data['created_at'],
                    version_data['file_example'],
                    json.dumps(version_data.get('tags', [])),
                    json.dumps(version_data.get('metadata', {}))
                ])
                return True
        except Exception as e:
            logger.error(f"Failed to save schema version: {e}")
            return False
    
    def get_schema_version(self, version_id: Optional[str] = None, 
                          fingerprint: Optional[str] = None,
                          format_type: Optional[str] = None) -> Optional[Dict]:
        """Retrieve schema version"""
        with self.get_connection() as conn:
            if version_id:
                result = conn.execute(
                    "SELECT * FROM schema_versions WHERE version_id = ?",
                    [version_id]
                ).fetchone()
            elif fingerprint:
                result = conn.execute(
                    "SELECT * FROM schema_versions WHERE fingerprint = ?",
                    [fingerprint]
                ).fetchone()
            elif format_type:
                result = conn.execute('''
                    SELECT * FROM schema_versions 
                    WHERE format_type = ? AND is_active = TRUE
                    ORDER BY created_at DESC 
                    LIMIT 1
                ''', [format_type]).fetchone()
            else:
                return None
            
            if result:
                columns = [desc[0] for desc in conn.description]
                return dict(zip(columns, result))
            return None
    
    def log_drift(self, drift_data: Dict) -> int:
        """Log a schema drift event"""
        with self.get_connection() as conn:
            result = conn.execute('''
                INSERT INTO drift_log 
                (file_path, timestamp, drift_type, details, severity,
                 expected_version_id, actual_fingerprint)
                VALUES (?, ?, ?, ?::JSON, ?, ?, ?)
                RETURNING id
            ''', [
                drift_data['file_path'],
                drift_data['timestamp'],
                drift_data['drift_type'],
                json.dumps(drift_data['details']),
                drift_data['severity'],
                drift_data.get('expected_version_id'),
                drift_data.get('actual_fingerprint')
            ]).fetchone()
            
            return result[0] if result else -1
    
    def log_file_processing(self, file_data: Dict) -> bool:
        """Log file processing history"""
        try:
            with self.get_connection() as conn:
                conn.execute('''
                    INSERT INTO file_history 
                    (file_path, fingerprint, processed_at, schema_version_id,
                     file_size_bytes, row_count, processing_time_ms, 
                     processing_status, error_message, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?::JSON)
                ''', [
                    file_data['file_path'],
                    file_data['fingerprint'],
                    file_data['processed_at'],
                    file_data.get('schema_version_id'),
                    file_data.get('file_size_bytes'),
                    file_data.get('row_count'),
                    file_data.get('processing_time_ms'),
                    file_data.get('processing_status', 'success'),
                    file_data.get('error_message'),
                    json.dumps(file_data.get('metadata', {}))
                ])
                return True
        except Exception as e:
            logger.error(f"Failed to log file processing: {e}")
            return False
    
    def get_unresolved_drifts(self, severity_threshold: Optional[str] = None,
                             days_back: int = 7) -> List[Dict]:
        """Get unresolved drifts"""
        severity_order = {'low': 1, 'medium': 2, 'high': 3, 'critical': 4}
        
        with self.get_connection() as conn:
            query = '''
                SELECT 
                    id, file_path, timestamp, drift_type, 
                    details, severity, expected_version_id
                FROM drift_log
                WHERE resolved = FALSE
                AND timestamp >= CURRENT_TIMESTAMP - INTERVAL ? DAY
            '''
            params = [days_back]
            
            if severity_threshold:
                threshold_value = severity_order.get(severity_threshold, 1)
                severities = [s for s, v in severity_order.items() if v >= threshold_value]
                placeholders = ','.join(['?' for _ in severities])
                query += f' AND severity IN ({placeholders})'
                params.extend(severities)
            
            query += ' ORDER BY timestamp DESC'
            
            results = conn.execute(query, params).fetchall()
            columns = [desc[0] for desc in conn.description]
            
            return [dict(zip(columns, row)) for row in results]
    
    def mark_drifts_resolved(self, file_path: str, resolution_notes: str) -> int:
        """Mark drifts as resolved"""
        with self.get_connection() as conn:
            result = conn.execute('''
                UPDATE drift_log    
                SET 
                    resolved = TRUE,
                    resolved_at = CURRENT_TIMESTAMP,
                    resolution_notes = ?
                WHERE 
                    file_path = ? AND 
                    resolved = FALSE
                RETURNING id
            ''', [resolution_notes, file_path]).fetchall()
            
            return len(result)
    
    def log_alert(self, alert_data: Dict) -> bool:
        """Log alert history"""
        try:
            with self.get_connection() as conn:
                conn.execute('''
                    INSERT INTO alert_history 
                    (drift_log_ids, alert_type, channel, sent_at, 
                     success, error_message, metadata)
                    VALUES (?::JSON, ?, ?, ?, ?, ?, ?::JSON)
                ''', [
                    json.dumps(alert_data['drift_log_ids']),
                    alert_data['alert_type'],
                    alert_data['channel'],
                    alert_data['sent_at'],
                    alert_data['success'],
                    alert_data.get('error_message'),
                    json.dumps(alert_data.get('metadata', {}))
                ])
                
                return True
        except Exception as e:
            logger.error(f"Failed to log alert: {e}")
            return False
    
    def get_analytics_summary(self, days: int = 30) -> Dict:
        """Get analytics summary"""
        with self.get_connection() as conn:
            since_date = datetime.now() - timedelta(days=days)
            
            # Overall statistics
            overall_stats = conn.execute('''
                SELECT 
                    COUNT(*) as total_drifts,
                    COUNT(DISTINCT file_path) as affected_files,
                    COUNT(CASE WHEN resolved THEN 1 END) as resolved_drifts,
                    COUNT(CASE WHEN severity = 'critical' THEN 1 END) as critical_count,
                    COUNT(CASE WHEN severity = 'high' THEN 1 END) as high_count
                FROM drift_log
                WHERE timestamp >= ?
            ''', [since_date]).fetchone()
            
            return {
                'period_days': days,
                'overall_stats': dict(zip(
                    ['total_drifts', 'affected_files', 'resolved_drifts', 
                     'critical_count', 'high_count'],
                    overall_stats
                ))
            }
    
    def cleanup_old_data(self, retention_days: int) -> Dict[str, int]:
        """Clean up old data"""
        cutoff_date = datetime.now() - timedelta(days=retention_days)
        
        with self.get_connection() as conn:
            # Delete old resolved drifts
            drift_result = conn.execute('''
                DELETE FROM drift_log
                WHERE resolved = TRUE AND timestamp < ?
                RETURNING id
            ''', [cutoff_date]).fetchall()
            
            # Delete old file history
            file_result = conn.execute('''
                DELETE FROM file_history
                WHERE processed_at < ?
                RETURNING id
            ''', [cutoff_date]).fetchall()
            
            return {
                'deleted_drifts': len(drift_result),
                'deleted_file_history': len(file_result)
            }
    
    def close(self) -> None:
        """Close database connection (no-op for DuckDB)"""
        pass
    def _create_views(self, conn):
        """Create analytical views"""
        # Drift summary view
        conn.execute('''
            CREATE OR REPLACE VIEW v_drift_summary AS
            SELECT 
                DATE_TRUNC('day', timestamp) as drift_date,
                drift_type,
                severity,
                COUNT(*) as count,
                COUNT(DISTINCT file_path) as unique_files,
                COUNT(CASE WHEN resolved THEN 1 END) as resolved_count
            FROM drift_log
            GROUP BY drift_date, drift_type, severity
        ''')
        
        # Schema usage view
        conn.execute('''
            CREATE OR REPLACE VIEW v_schema_usage AS
            SELECT 
                sv.version_id,
                sv.format_type,
                sv.created_at,
                sv.is_active,
                COUNT(DISTINCT fh.file_path) as unique_files_processed,
                COUNT(fh.id) as total_processings,
                MAX(fh.processed_at) as last_used,
                AVG(fh.processing_time_ms) as avg_processing_time_ms
            FROM schema_versions sv
            LEFT JOIN file_history fh ON sv.version_id = fh.schema_version_id
            GROUP BY sv.version_id, sv.format_type, sv.created_at, sv.is_active
        ''')
        
        # Recent drift activity
        conn.execute('''
            CREATE OR REPLACE VIEW v_recent_drift_activity AS
            SELECT 
                dl.file_path,
                dl.timestamp,
                dl.drift_type,
                dl.severity,
                dl.details,
                sv.version_id as expected_version,
                sv.format_type
            FROM drift_log dl
            LEFT JOIN schema_versions sv ON dl.expected_version_id = sv.version_id
            WHERE dl.timestamp >= CURRENT_TIMESTAMP - INTERVAL '7 days'
            ORDER BY dl.timestamp DESC
        ''')