"""
DuckDB Manager for Schema Registry
Handles database connections, table creation, and basic operations
"""

import duckdb
import os
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from pathlib import Path
import json
import sys

# Add project root to Python path for imports
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from core.config_manager import ConfigManager


class DuckDBManager:
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.duckdb_config = config_manager.get_duckdb_config()
        self._connection = None
        
    def get_connection(self) -> duckdb.DuckDBPyConnection:
        """Get or create DuckDB connection"""
        if self._connection is None:
            # Ensure database directory exists
            db_path = Path(self.duckdb_config.database_path)
            db_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Create connection
            self._connection = duckdb.connect(
                database=self.duckdb_config.database_path,
                **self.duckdb_config.connection_options
            )
            
            # Initialize tables if they don't exist
            self._initialize_tables()
        
        return self._connection
    
    def _initialize_tables(self) -> None:
        """Create schema registry tables if they don't exist"""
        conn = self._connection
        
        try:
            # Create schema_registry table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS schema_registry (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    registry_key VARCHAR NOT NULL,
                    pipeline_id VARCHAR NOT NULL,
                    file_name VARCHAR NOT NULL,
                    current_version VARCHAR NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(registry_key)
                )
            """)
            
            # Create schema_versions table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS schema_versions (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    registry_key VARCHAR NOT NULL,
                    version VARCHAR NOT NULL,
                    schema_json TEXT NOT NULL,
                    schema_hash VARCHAR NOT NULL,
                    compatibility_info TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_by VARCHAR,
                    UNIQUE(registry_key, version)
                )
            """)
            
            # Create schema_drift_log table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS schema_drift_log (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    registry_key VARCHAR NOT NULL,
                    from_version VARCHAR,
                    to_version VARCHAR NOT NULL,
                    drift_type VARCHAR NOT NULL,
                    changes_json TEXT NOT NULL,
                    compatibility_report TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create config_audit table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS config_audit (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    config_type VARCHAR NOT NULL,
                    old_value VARCHAR,
                    new_value VARCHAR NOT NULL,
                    changed_by VARCHAR,
                    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # === VALIDATION TABLES (NEW) ===
            # Create validation_rules table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS validation_rules (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    schema_registry_id UUID NOT NULL,
                    column_name VARCHAR(255) NOT NULL,
                    rule_type VARCHAR(50) NOT NULL,
                    rule_config JSON,
                    rule_description TEXT,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (schema_registry_id) REFERENCES schema_registry(id)
                )
            """)

            # Create validation_executions table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS validation_executions (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    schema_registry_id UUID NOT NULL,
                    file_path VARCHAR(500),
                    normalized_file_name VARCHAR(255),
                    execution_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    total_records INTEGER,
                    total_rules_executed INTEGER,
                    overall_status VARCHAR(20),
                    execution_duration_ms INTEGER,
                    FOREIGN KEY (schema_registry_id) REFERENCES schema_registry(id)
                )
            """)

            # Create validation_results table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS validation_results (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    execution_id UUID NOT NULL,
                    rule_id UUID NOT NULL,
                    column_name VARCHAR(255),
                    rule_type VARCHAR(50),
                    passed_records_count INTEGER DEFAULT 0,
                    failed_records_count INTEGER DEFAULT 0,
                    failure_details JSON,
                    status VARCHAR(20),
                    FOREIGN KEY (execution_id) REFERENCES validation_executions(id),
                    FOREIGN KEY (rule_id) REFERENCES validation_rules(id)
                )
            """)
            
            # Create indexes for better performance
            self._create_indexes()
            
            print("Schema registry tables initialized successfully")
            
        except Exception as e:
            print(f"Error initializing tables: {e}")
            raise
    
    def _create_indexes(self) -> None:
        """Create indexes for better query performance"""
        conn = self._connection
        
        try:
            # Original indexes
            conn.execute("CREATE INDEX IF NOT EXISTS idx_registry_key ON schema_registry(registry_key)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_versions_registry_key ON schema_versions(registry_key)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_drift_registry_key ON schema_drift_log(registry_key)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_schema_hash ON schema_versions(schema_hash)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_versions_created_at ON schema_versions(created_at)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_drift_created_at ON schema_drift_log(created_at)")
            
            # Validation indexes (NEW)
            conn.execute("CREATE INDEX IF NOT EXISTS idx_validation_rules_schema_id ON validation_rules(schema_registry_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_validation_rules_active ON validation_rules(is_active)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_validation_executions_schema_id ON validation_executions(schema_registry_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_validation_results_execution_id ON validation_results(execution_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_validation_results_rule_id ON validation_results(rule_id)")
            
        except Exception as e:
            print(f"Warning: Could not create some indexes: {e}")

    # === EXISTING METHODS (UNCHANGED) ===
    def insert_schema_registry(self, registry_key: str, pipeline_id: str, file_name: str, 
                              current_version: str) -> bool:
        """Insert new schema registry entry"""
        conn = self.get_connection()
        
        try:
            conn.execute("""
                INSERT INTO schema_registry (registry_key, pipeline_id, file_name, current_version)
                VALUES (?, ?, ?, ?)
            """, [registry_key, pipeline_id, file_name, current_version])
            return True
            
        except Exception as e:
            print(f"Error inserting schema registry: {e}")
            return False
    
    def update_schema_registry_version(self, registry_key: str, new_version: str) -> bool:
        """Update current version for existing schema registry"""
        conn = self.get_connection()
        
        try:
            conn.execute("""
                UPDATE schema_registry 
                SET current_version = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE registry_key = ?
            """, [new_version, registry_key])
            return True
            
        except Exception as e:
            print(f"Error updating schema registry version: {e}")
            return False
    
    def insert_schema_version(self, registry_key: str, version: str, schema_json: str, 
                             schema_hash: str, compatibility_info: Optional[str] = None,
                             created_by: Optional[str] = None) -> bool:
        """Insert new schema version"""
        conn = self.get_connection()
        
        try:
            conn.execute("""
                INSERT INTO schema_versions 
                (registry_key, version, schema_json, schema_hash, compatibility_info, created_by)
                VALUES (?, ?, ?, ?, ?, ?)
            """, [registry_key, version, schema_json, schema_hash, compatibility_info, created_by])
            return True
            
        except Exception as e:
            print(f"Error inserting schema version: {e}")
            return False
    
    def insert_drift_log(self, registry_key: str, from_version: Optional[str], to_version: str,
                        drift_type: str, changes_json: str, compatibility_report: Optional[str] = None) -> bool:
        """Insert schema drift log entry"""
        conn = self.get_connection()
        
        try:
            conn.execute("""
                INSERT INTO schema_drift_log 
                (registry_key, from_version, to_version, drift_type, changes_json, compatibility_report)
                VALUES (?, ?, ?, ?, ?, ?)
            """, [registry_key, from_version, to_version, drift_type, changes_json, compatibility_report])
            return True
            
        except Exception as e:
            print(f"Error inserting drift log: {e}")
            return False
    
    def insert_config_audit(self, config_type: str, old_value: Optional[str], new_value: str,
                           changed_by: Optional[str] = None) -> bool:
        """Insert configuration audit log"""
        conn = self.get_connection()
        
        try:
            conn.execute("""
                INSERT INTO config_audit (config_type, old_value, new_value, changed_by)
                VALUES (?, ?, ?, ?)
            """, [config_type, old_value, new_value, changed_by])
            return True
            
        except Exception as e:
            print(f"Error inserting config audit: {e}")
            return False
    
    def get_schema_registry(self, registry_key: str) -> Optional[Dict[str, Any]]:
        """Get schema registry by key"""
        conn = self.get_connection()
        
        try:
            result = conn.execute("""
                SELECT id, registry_key, pipeline_id, file_name, current_version, created_at, updated_at
                FROM schema_registry 
                WHERE registry_key = ?
            """, [registry_key]).fetchone()
            
            if result:
                return {
                    'id': result[0],
                    'registry_key': result[1],
                    'pipeline_id': result[2],
                    'file_name': result[3],
                    'current_version': result[4],
                    'created_at': result[5],
                    'updated_at': result[6]
                }
            return None
            
        except Exception as e:
            print(f"Error getting schema registry: {e}")
            return None
    
    def get_schema_version(self, registry_key: str, version: str) -> Optional[Dict[str, Any]]:
        """Get specific schema version"""
        conn = self.get_connection()
        
        try:
            result = conn.execute("""
                SELECT registry_key, version, schema_json, schema_hash, compatibility_info, created_at, created_by
                FROM schema_versions 
                WHERE registry_key = ? AND version = ?
            """, [registry_key, version]).fetchone()
            
            if result:
                return {
                    'registry_key': result[0],
                    'version': result[1],
                    'schema_json': result[2],
                    'schema_hash': result[3],
                    'compatibility_info': result[4],
                    'created_at': result[5],
                    'created_by': result[6]
                }
            return None
            
        except Exception as e:
            print(f"Error getting schema version: {e}")
            return None
    
    def get_latest_schema_version(self, registry_key: str) -> Optional[Dict[str, Any]]:
        """Get the latest schema version for a registry key"""
        conn = self.get_connection()
        
        try:
            result = conn.execute("""
                SELECT registry_key, version, schema_json, schema_hash, compatibility_info, created_at, created_by
                FROM schema_versions 
                WHERE registry_key = ? 
                ORDER BY created_at DESC 
                LIMIT 1
            """, [registry_key]).fetchone()
            
            if result:
                return {
                    'registry_key': result[0],
                    'version': result[1],
                    'schema_json': result[2],
                    'schema_hash': result[3],
                    'compatibility_info': result[4],
                    'created_at': result[5],
                    'created_by': result[6]
                }
            return None
            
        except Exception as e:
            print(f"Error getting latest schema version: {e}")
            return None
    
    def get_all_versions(self, registry_key: str) -> List[Dict[str, Any]]:
        """Get all versions for a schema registry"""
        conn = self.get_connection()
        
        try:
            results = conn.execute("""
                SELECT registry_key, version, schema_json, schema_hash, compatibility_info, created_at, created_by
                FROM schema_versions 
                WHERE registry_key = ? 
                ORDER BY created_at ASC
            """, [registry_key]).fetchall()
            
            versions = []
            for result in results:
                versions.append({
                    'registry_key': result[0],
                    'version': result[1],
                    'schema_json': result[2],
                    'schema_hash': result[3],
                    'compatibility_info': result[4],
                    'created_at': result[5],
                    'created_by': result[6]
                })
            
            return versions
            
        except Exception as e:
            print(f"Error getting all versions: {e}")
            return []
    
    def get_drift_history(self, registry_key: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get drift history for a schema registry"""
        conn = self.get_connection()
        
        try:
            results = conn.execute("""
                SELECT registry_key, from_version, to_version, drift_type, changes_json, 
                       compatibility_report, created_at
                FROM schema_drift_log 
                WHERE registry_key = ? 
                ORDER BY created_at DESC 
                LIMIT ?
            """, [registry_key, limit]).fetchall()
            
            history = []
            for result in results:
                history.append({
                    'registry_key': result[0],
                    'from_version': result[1],
                    'to_version': result[2],
                    'drift_type': result[3],
                    'changes_json': result[4],
                    'compatibility_report': result[5],
                    'created_at': result[6]
                })
            
            return history
            
        except Exception as e:
            print(f"Error getting drift history: {e}")
            return []
    
    def get_registry_statistics(self) -> Dict[str, Any]:
        """Get overall registry statistics"""
        conn = self.get_connection()
        
        try:
            # Total registries
            total_registries = conn.execute("SELECT COUNT(*) FROM schema_registry").fetchone()[0]
            
            # Total versions
            total_versions = conn.execute("SELECT COUNT(*) FROM schema_versions").fetchone()[0]
            
            # Total drift events
            total_drift_events = conn.execute("SELECT COUNT(*) FROM schema_drift_log").fetchone()[0]
            
            # Most active schemas
            most_active = conn.execute("""
                SELECT registry_key, COUNT(*) as version_count
                FROM schema_versions 
                GROUP BY registry_key 
                ORDER BY version_count DESC 
                LIMIT 5
            """).fetchall()
            
            return {
                'total_registries': total_registries,
                'total_versions': total_versions,
                'total_drift_events': total_drift_events,
                'most_active_schemas': [{'registry_key': row[0], 'version_count': row[1]} for row in most_active]
            }
            
        except Exception as e:
            print(f"Error getting registry statistics: {e}")
            return {}
    
    def cleanup_old_versions(self, max_versions: int, max_age_days: int) -> int:
        """Clean up old schema versions based on retention policy"""
        conn = self.get_connection()
        
        try:
            # Get schemas that exceed version limit
            registries_to_clean = conn.execute("""
                SELECT registry_key 
                FROM schema_versions 
                GROUP BY registry_key 
                HAVING COUNT(*) > ?
            """, [max_versions]).fetchall()
            
            cleaned_count = 0
            
            for (registry_key,) in registries_to_clean:
                # Keep only the latest N versions
                versions_to_delete = conn.execute("""
                    SELECT version 
                    FROM schema_versions 
                    WHERE registry_key = ? 
                    ORDER BY created_at DESC 
                    OFFSET ?
                """, [registry_key, max_versions]).fetchall()
                
                for (version,) in versions_to_delete:
                    conn.execute("""
                        DELETE FROM schema_versions 
                        WHERE registry_key = ? AND version = ?
                    """, [registry_key, version])
                    cleaned_count += 1
            
            # Clean up versions older than max_age_days
            old_versions_deleted = conn.execute("""
                DELETE FROM schema_versions 
                WHERE created_at < CURRENT_TIMESTAMP - INTERVAL ? DAYS
            """, [max_age_days]).rowcount
            
            cleaned_count += old_versions_deleted
            
            return cleaned_count
            
        except Exception as e:
            print(f"Error cleaning up old versions: {e}")
            return 0

    # === NEW VALIDATION METHODS ===
    
    def generate_registry_key(self, pipeline_id: str, normalized_file_name: str) -> str:
        """Generate registry key from pipeline_id and normalized_file_name"""
        return f"{pipeline_id}::{normalized_file_name}"
    
    def get_schema_registry_id_by_registry_key(self, registry_key: str) -> Optional[str]:
        """Get schema_registry.id using registry_key"""
        conn = self.get_connection()
        
        try:
            result = conn.execute("""
                SELECT id FROM schema_registry WHERE registry_key = ?
            """, [registry_key]).fetchone()
            
            return str(result[0]) if result else None
            
        except Exception as e:
            print(f"Error getting schema registry ID: {e}")
            return None
    
    def insert_validation_rule(self, schema_registry_id: str, column_name: str, rule_type: str,
                              rule_config: Dict[str, Any], rule_description: str = "",
                              is_active: bool = True) -> bool:
        """Insert single validation rule"""
        conn = self.get_connection()
        
        try:
            conn.execute("""
                INSERT INTO validation_rules 
                (schema_registry_id, column_name, rule_type, rule_config, rule_description, is_active)
                VALUES (?, ?, ?, ?::JSON, ?, ?)
            """, [schema_registry_id, column_name, rule_type, json.dumps(rule_config), 
                  rule_description, is_active])
            return True
            
        except Exception as e:
            print(f"Error inserting validation rule: {e}")
            return False
    
    def batch_insert_validation_rules(self, validation_rules: List[Dict[str, Any]]) -> int:
        """Bulk insert validation rules from CSV data"""
        conn = self.get_connection()
        inserted_count = 0
        
        try:
            for rule in validation_rules:
                success = self.insert_validation_rule(
                    schema_registry_id=rule['schema_registry_id'],
                    column_name=rule['column_name'],
                    rule_type=rule['rule_type'],
                    rule_config=rule['rule_config'],
                    rule_description=rule.get('rule_description', ''),
                    is_active=rule.get('is_active', True)
                )
                if success:
                    inserted_count += 1
                    
            return inserted_count
            
        except Exception as e:
            print(f"Error batch inserting validation rules: {e}")
            return inserted_count
    
    def get_validation_rules_by_schema_id(self, schema_registry_id: str, 
                                         active_only: bool = True) -> List[Dict[str, Any]]:
        """Retrieve validation rules for a schema_registry_id"""
        conn = self.get_connection()
        
        try:
            query = """
                SELECT id, schema_registry_id, column_name, rule_type, rule_config, 
                       rule_description, is_active, created_at, updated_at
                FROM validation_rules 
                WHERE schema_registry_id = ?
            """
            params = [schema_registry_id]
            
            if active_only:
                query += " AND is_active = TRUE"
            
            query += " ORDER BY column_name, rule_type"
            
            results = conn.execute(query, params).fetchall()
            
            rules = []
            for result in results:
                rule_config = json.loads(result[4]) if result[4] else {}
                rules.append({
                    'id': str(result[0]),
                    'schema_registry_id': str(result[1]),
                    'column_name': result[2],
                    'rule_type': result[3],
                    'rule_config': rule_config,
                    'rule_description': result[5],
                    'is_active': result[6],
                    'created_at': result[7],
                    'updated_at': result[8]
                })
            
            return rules
            
        except Exception as e:
            print(f"Error getting validation rules: {e}")
            return []
    
    def insert_validation_execution(self, schema_registry_id: str, file_path: str,
                                   normalized_file_name: str, total_records: int,
                                   total_rules_executed: int, overall_status: str,
                                   execution_duration_ms: int) -> Optional[str]:
        """Insert validation execution log and return execution_id"""
        conn = self.get_connection()
        
        try:
            result = conn.execute("""
                INSERT INTO validation_executions 
                (schema_registry_id, file_path, normalized_file_name, total_records,
                 total_rules_executed, overall_status, execution_duration_ms)
                VALUES (?, ?, ?, ?, ?, ?, ?)
                RETURNING id
            """, [schema_registry_id, file_path, normalized_file_name, total_records,
                  total_rules_executed, overall_status, execution_duration_ms]).fetchone()
            
            return str(result[0]) if result else None
            
        except Exception as e:
            print(f"Error inserting validation execution: {e}")
            return None
    
    def insert_validation_results(self, execution_id: str, rule_id: str, column_name: str,
                                 rule_type: str, passed_records_count: int,
                                 failed_records_count: int, failure_details: Dict[str, Any],
                                 status: str) -> bool:
        """Insert validation results for a specific rule"""
        conn = self.get_connection()
        
        try:
            conn.execute("""
                INSERT INTO validation_results 
                (execution_id, rule_id, column_name, rule_type, passed_records_count,
                 failed_records_count, failure_details, status)
                VALUES (?, ?, ?, ?, ?, ?, ?::JSON, ?)
            """, [execution_id, rule_id, column_name, rule_type, passed_records_count,
                  failed_records_count, json.dumps(failure_details), status])
            return True
            
        except Exception as e:
            print(f"Error inserting validation results: {e}")
            return False
    
    def get_validation_execution_results(self, execution_id: str) -> Dict[str, Any]:
        """Get complete validation execution results"""
        conn = self.get_connection()
        
        try:
            # Get execution summary
            execution = conn.execute("""
                SELECT ve.id, ve.schema_registry_id, ve.file_path, ve.normalized_file_name,
                       ve.execution_timestamp, ve.total_records, ve.total_rules_executed,
                       ve.overall_status, ve.execution_duration_ms,
                       sr.registry_key, sr.pipeline_id, sr.file_name
                FROM validation_executions ve
                JOIN schema_registry sr ON ve.schema_registry_id = sr.id
                WHERE ve.id = ?
            """, [execution_id]).fetchone()
            
            if not execution:
                return {}
            
            # Get detailed results
            results = conn.execute("""
                SELECT vr.id, vr.rule_id, vr.column_name, vr.rule_type,
                       vr.passed_records_count, vr.failed_records_count,
                       vr.failure_details, vr.status,
                       vru.rule_description
                FROM validation_results vr
                JOIN validation_rules vru ON vr.rule_id = vru.id
                WHERE vr.execution_id = ?
                ORDER BY vr.column_name, vr.rule_type
            """, [execution_id]).fetchall()
            
            detailed_results = []
            for result in results:
                failure_details = json.loads(result[6]) if result[6] else {}
                detailed_results.append({
                    'id': str(result[0]),
                    'rule_id': str(result[1]),
                    'column_name': result[2],
                    'rule_type': result[3],
                    'passed_records_count': result[4],
                    'failed_records_count': result[5],
                    'failure_details': failure_details,
                    'status': result[7],
                    'rule_description': result[8]
                })
            
            return {
                'execution': {
                    'id': str(execution[0]),
                    'schema_registry_id': str(execution[1]),
                    'file_path': execution[2],
                    'normalized_file_name': execution[3],
                    'execution_timestamp': execution[4],
                    'total_records': execution[5],
                    'total_rules_executed': execution[6],
                    'overall_status': execution[7],
                    'execution_duration_ms': execution[8],
                    'registry_key': execution[9],
                    'pipeline_id': execution[10],
                    'file_name': execution[11]
                },
                'results': detailed_results
            }
            
        except Exception as e:
            print(f"Error getting validation execution results: {e}")
            return {}
    
    def get_validation_execution_history(self, schema_registry_id: str, 
                                       limit: int = 50) -> List[Dict[str, Any]]:
        """Get validation execution history for a schema"""
        conn = self.get_connection()
        
        try:
            results = conn.execute("""
                SELECT id, file_path, normalized_file_name, execution_timestamp,
                       total_records, total_rules_executed, overall_status, execution_duration_ms
                FROM validation_executions
                WHERE schema_registry_id = ?
                ORDER BY execution_timestamp DESC
                LIMIT ?
            """, [schema_registry_id, limit]).fetchall()
            
            history = []
            for result in results:
                history.append({
                    'id': str(result[0]),
                    'file_path': result[1],
                    'normalized_file_name': result[2],
                    'execution_timestamp': result[3],
                    'total_records': result[4],
                    'total_rules_executed': result[5],
                    'overall_status': result[6],
                    'execution_duration_ms': result[7]
                })
            
            return history
            
        except Exception as e:
            print(f"Error getting validation execution history: {e}")
            return []
    
    def close_connection(self) -> None:
        """Close database connection"""
        if self._connection:
            self._connection.close()
            self._connection = None


# Example usage and testing
if __name__ == "__main__":
    from core.config_manager import ConfigManager
    
    # Initialize
    config_manager = ConfigManager()
    if not os.path.exists(config_manager.config_path):
        config_manager.create_default_config()
    
    config_manager.load_config()
    db_manager = DuckDBManager(config_manager)
    
    # Test database operations
    try:
        # Get connection and initialize
        conn = db_manager.get_connection()
        print("DuckDB connection established successfully")
        
        # Test registry key generation
        registry_key = db_manager.generate_registry_key("PIPELINE_001", "customers_normalized.csv")
        print(f"Generated registry key: {registry_key}")
        
        # Test statistics
        stats = db_manager.get_registry_statistics()
        print(f"Registry statistics: {stats}")
        
        # Close connection
        db_manager.close_connection()
        print("Connection closed successfully")
        
    except Exception as e:
        print(f"Error testing DuckDB operations: {e}")