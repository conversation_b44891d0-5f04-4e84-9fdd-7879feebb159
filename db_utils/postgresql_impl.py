import psycopg2
from psycopg2 import pool
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from contextlib import contextmanager
import logging
from .database_interface import DatabaseInterface

logger = logging.getLogger(__name__)

class PostgreSQLImplementation(DatabaseInterface):
    """PostgreSQL implementation of database interface"""
    
    def __init__(self, config: Dict):
        self.config = config['database']['backends']['postgresql']['connection']
        self._resolve_env_variables()
        self._create_connection_pool()
        self.initialize()
    
    def _resolve_env_variables(self):
        """Resolve environment variables in config"""
        for key, value in self.config.items():
            if isinstance(value, str) and value.startswith('${') and value.endswith('}'):
                env_var = value[2:-1]
                self.config[key] = os.environ.get(env_var, value)
    
    def _create_connection_pool(self):
        """Create connection pool"""
        self.connection_pool = psycopg2.pool.SimpleConnectionPool(
            1,  # Min connections
            self.config.get('pool_size', 10),  # Max connections
            host=self.config['host'],
            port=self.config['port'],
            database=self.config['database'],
            user=self.config['user'],
            password=self.config['password'],
            sslmode=self.config.get('sslmode', 'prefer')
        )
    
    @contextmanager
    def get_connection(self):
        """Get connection from pool"""
        conn = self.connection_pool.getconn()
        try:
            yield conn
            conn.commit()
        except Exception:
            conn.rollback()
            raise
        finally:
            self.connection_pool.putconn(conn)
    
    def initialize(self) -> None:
        """Initialize database schema"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Enable JSON support
            cursor.execute("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"")
            
            # Schema versions table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS schema_versions (
                    version_id VARCHAR PRIMARY KEY,
                    fingerprint VARCHAR NOT NULL,
                    schema_details JSONB NOT NULL,
                    format_type VARCHAR NOT NULL,
                    created_at TIMESTAMP NOT NULL,
                    created_by VARCHAR DEFAULT CURRENT_USER,
                    file_example VARCHAR NOT NULL,
                    is_active BOOLEAN DEFAULT TRUE,
                    tags JSONB DEFAULT '[]'::JSONB,
                    metadata JSONB DEFAULT '{}'::JSONB
                )
            ''')
            
            # Create indexes
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_schema_fingerprint 
                ON schema_versions(fingerprint)
            ''')
            
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_schema_format_active 
                ON schema_versions(format_type, is_active, created_at DESC)
            ''')
            
            # Drift log table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS drift_log (
                    id SERIAL PRIMARY KEY,
                    file_path VARCHAR NOT NULL,
                    timestamp TIMESTAMP NOT NULL,
                    drift_type VARCHAR NOT NULL,
                    details JSONB NOT NULL,
                    severity VARCHAR NOT NULL,
                    resolved BOOLEAN DEFAULT FALSE,
                    resolved_at TIMESTAMP,
                    resolution_notes TEXT,
                    expected_version_id VARCHAR,
                    actual_fingerprint VARCHAR,
                    alert_sent BOOLEAN DEFAULT FALSE,
                    alert_sent_at TIMESTAMP
                )
            ''')
            
            # Create indexes
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_drift_unresolved 
                ON drift_log(resolved, severity, timestamp DESC)
            ''')
            
            # File history table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS file_history (
                    id SERIAL PRIMARY KEY,
                    file_path VARCHAR NOT NULL,
                    fingerprint VARCHAR NOT NULL,
                    processed_at TIMESTAMP NOT NULL,
                    schema_version_id VARCHAR,
                    file_size_bytes BIGINT,
                    row_count BIGINT,
                    processing_time_ms INTEGER,
                    processing_status VARCHAR DEFAULT 'success',
                    error_message TEXT,
                    metadata JSONB DEFAULT '{}'::JSONB
                )
            ''')
            
            # Alert history table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS alert_history (
                    id SERIAL PRIMARY KEY,
                    drift_log_ids JSONB NOT NULL,
                    alert_type VARCHAR NOT NULL,
                    channel VARCHAR NOT NULL,
                    sent_at TIMESTAMP NOT NULL,
                    success BOOLEAN NOT NULL,
                    error_message TEXT,
                    metadata JSONB DEFAULT '{}'::JSONB
                )
            ''')
            
            cursor.close()
            logger.info("PostgreSQL schema initialized successfully")
    
    def save_schema_version(self, version_data: Dict) -> bool:
        """Save a new schema version"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO schema_versions 
                    (version_id, fingerprint, schema_details, format_type, 
                     created_at, file_example, tags, metadata)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                ''', (
                    version_data['version_id'],
                    version_data['fingerprint'],
                    json.dumps(version_data['schema_details']),
                    version_data['format_type'],
                    version_data['created_at'],
                    version_data['file_example'],
                    json.dumps(version_data.get('tags', [])),
                    json.dumps(version_data.get('metadata', {}))
                ))
                cursor.close()
                return True
        except Exception as e:
            logger.error(f"Failed to save schema version: {e}")
            return False
    
    def get_schema_version(self, version_id: Optional[str] = None, 
                          fingerprint: Optional[str] = None,
                          format_type: Optional[str] = None) -> Optional[Dict]:
        """Retrieve schema version"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            if version_id:
                cursor.execute(
                    "SELECT * FROM schema_versions WHERE version_id = %s",
                    (version_id,)
                )
            elif fingerprint:
                cursor.execute(
                    "SELECT * FROM schema_versions WHERE fingerprint = %s",
                    (fingerprint,)
                )
            elif format_type:
                cursor.execute('''
                    SELECT * FROM schema_versions 
                    WHERE format_type = %s AND is_active = TRUE
                    ORDER BY created_at DESC 
                    LIMIT 1
                ''', (format_type,))
            else:
                cursor.close()
                return None
            
            result = cursor.fetchone()
            if result:
                columns = [desc[0] for desc in cursor.description]
                cursor.close()
                return dict(zip(columns, result))
            
            cursor.close()
            return None
    
    def log_drift(self, drift_data: Dict) -> int:
        """Log a schema drift event"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO drift_log 
                (file_path, timestamp, drift_type, details, severity,
                 expected_version_id, actual_fingerprint)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                RETURNING id
            ''', (
                drift_data['file_path'],
                drift_data['timestamp'],
                drift_data['drift_type'],
                json.dumps(drift_data['details']),
                drift_data['severity'],
                drift_data.get('expected_version_id'),
                drift_data.get('actual_fingerprint')
            ))
            
            result = cursor.fetchone()
            cursor.close()
            return result[0] if result else -1
    
    def log_file_processing(self, file_data: Dict) -> bool:
        """Log file processing history"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO file_history 
                    (file_path, fingerprint, processed_at, schema_version_id,
                     file_size_bytes, row_count, processing_time_ms, 
                     processing_status, error_message, metadata)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ''', (
                    file_data['file_path'],
                    file_data['fingerprint'],
                    file_data['processed_at'],
                    file_data.get('schema_version_id'),
                    file_data.get('file_size_bytes'),
                    file_data.get('row_count'),
                    file_data.get('processing_time_ms'),
                    file_data.get('processing_status', 'success'),
                    file_data.get('error_message'),
                    json.dumps(file_data.get('metadata', {}))
                ))
                cursor.close()
                return True
        except Exception as e:
            logger.error(f"Failed to log file processing: {e}")
            return False
    
    def get_unresolved_drifts(self, severity_threshold: Optional[str] = None,
                             days_back: int = 7) -> List[Dict]:
        """Get unresolved drifts"""
        severity_order = {'low': 1, 'medium': 2, 'high': 3, 'critical': 4}
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            query = '''
                SELECT 
                    id, file_path, timestamp, drift_type, 
                    details, severity, expected_version_id
                FROM drift_log
                WHERE resolved = FALSE
                AND timestamp >= CURRENT_TIMESTAMP - INTERVAL '%s days'
            '''
            params = [days_back]
            
            if severity_threshold:
                threshold_value = severity_order.get(severity_threshold, 1)
                severities = [s for s, v in severity_order.items() if v >= threshold_value]
                placeholders = ','.join(['%s' for _ in severities])
                query += f' AND severity IN ({placeholders})'
                params.extend(severities)
            
            query += ' ORDER BY timestamp DESC'
            
            cursor.execute(query, params)
            results = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]
            cursor.close()
            
            return [dict(zip(columns, row)) for row in results]
    
    def mark_drifts_resolved(self, file_path: str, resolution_notes: str) -> int:
        """Mark drifts as resolved"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE drift_log
                SET 
                    resolved = TRUE,
                    resolved_at = CURRENT_TIMESTAMP,
                    resolution_notes = %s
                WHERE 
                    file_path = %s AND 
                    resolved = FALSE
                RETURNING id
            ''', (resolution_notes, file_path))
            
            result = cursor.fetchall()
            cursor.close()
            return len(result)
    
    def log_alert(self, alert_data: Dict) -> bool:
        """Log alert history"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO alert_history 
                    (drift_log_ids, alert_type, channel, sent_at, 
                     success, error_message, metadata)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                ''', (
                    json.dumps(alert_data['drift_log_ids']),
                    alert_data['alert_type'],
                    alert_data['channel'],
                    alert_data['sent_at'],
                    alert_data['success'],
                    alert_data.get('error_message'),
                    json.dumps(alert_data.get('metadata', {}))
                ))
                cursor.close()
                return True
        except Exception as e:
            logger.error(f"Failed to log alert: {e}")
            return False
    
    def get_analytics_summary(self, days: int = 30) -> Dict:
        """Get analytics summary"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Overall statistics
            cursor.execute('''
                SELECT 
                    COUNT(*) as total_drifts,
                    COUNT(DISTINCT file_path) as affected_files,
                    COUNT(CASE WHEN resolved THEN 1 END) as resolved_drifts,
                    COUNT(CASE WHEN severity = 'critical' THEN 1 END) as critical_count,
                    COUNT(CASE WHEN severity = 'high' THEN 1 END) as high_count
                FROM drift_log
                WHERE timestamp >= CURRENT_TIMESTAMP - INTERVAL '%s days'
            ''', (days,))
            
            overall_stats = cursor.fetchone()
            cursor.close()
            
            return {
                'period_days': days,
                'overall_stats': dict(zip(
                    ['total_drifts', 'affected_files', 'resolved_drifts', 
                     'critical_count', 'high_count'],
                    overall_stats
                ))
            }
    
    def cleanup_old_data(self, retention_days: int) -> Dict[str, int]:
        """Clean up old data"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Delete old resolved drifts
            cursor.execute('''
                DELETE FROM drift_log
                WHERE resolved = TRUE 
                AND timestamp < CURRENT_TIMESTAMP - INTERVAL '%s days'
                RETURNING id
            ''', (retention_days,))
            
            deleted_drifts = len(cursor.fetchall())
            
            # Delete old file history
            cursor.execute('''
                DELETE FROM file_history
                WHERE processed_at < CURRENT_TIMESTAMP - INTERVAL '%s days'
                RETURNING id
            ''', (retention_days,))
            
            deleted_files = len(cursor.fetchall())
            cursor.close()
            
            return {
                'deleted_drifts': deleted_drifts,
                'deleted_file_history': deleted_files
            }
    
    def close(self) -> None:
        """Close connection pool"""
        if hasattr(self, 'connection_pool'):
            self.connection_pool.closeall()