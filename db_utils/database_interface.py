from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import json
import logging

logger = logging.getLogger(__name__)

class DatabaseInterface(ABC):
    """Abstract interface for database operations"""
    
    @abstractmethod
    def initialize(self) -> None:
        """Initialize database schema"""
        pass
    
    @abstractmethod
    def save_schema_version(self, version_data: Dict) -> bool:
        """Save a new schema version"""
        pass
    
    @abstractmethod
    def get_schema_version(self, version_id: Optional[str] = None, 
                          fingerprint: Optional[str] = None,
                          format_type: Optional[str] = None) -> Optional[Dict]:
        """Retrieve schema version"""
        pass
    
    @abstractmethod
    def log_drift(self, drift_data: Dict) -> int:
        """Log a schema drift event"""
        pass
    
    @abstractmethod
    def log_file_processing(self, file_data: Dict) -> bool:
        """Log file processing history"""
        pass
    
    @abstractmethod
    def get_unresolved_drifts(self, severity_threshold: Optional[str] = None,
                             days_back: int = 7) -> List[Dict]:
        """Get unresolved drifts"""
        pass
    
    @abstractmethod
    def mark_drifts_resolved(self, file_path: str, resolution_notes: str) -> int:
        """Mark drifts as resolved"""
        pass
    
    @abstractmethod
    def log_alert(self, alert_data: Dict) -> bool:
        """Log alert history"""
        pass
    
    @abstractmethod
    def get_analytics_summary(self, days: int = 30) -> Dict:
        """Get analytics summary"""
        pass
    
    @abstractmethod
    def cleanup_old_data(self, retention_days: int) -> Dict[str, int]:
        """Clean up old data"""
        pass
    
    @abstractmethod
    def close(self) -> None:
        """Close database connection"""
        pass