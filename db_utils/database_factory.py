import logging
from typing import Dict
from .database_interface import DatabaseInterface
from .duckdb_impl import DuckDBImplementation
from .postgresql_impl import PostgreSQLImplementation


logger = logging.getLogger(__name__)

class DatabaseFactory:
    """Factory class to create database implementations"""
    
    # Registry of available implementations
    _implementations = {
        'duckdb': DuckDBImplementation,
        'postgresql': PostgreSQLImplementation,
        # 'mysql': MySQLImplementation,
        # 'sqlite': SQLiteImplementation,
        # 'mongodb': MongoDBImplementation,
        # 'bigquery': BigQueryImplementation,
        # 'snowflake': SnowflakeImplementation
    }
    
    @classmethod
    def create_database(cls, config: Dict) -> DatabaseInterface:
        """Create database instance based on configuration"""
        db_type = config['database']['type']
        
        # Check if the database type is enabled
        if not config['database']['backends'].get(db_type, {}).get('enabled', False):
            raise ValueError(f"Database type '{db_type}' is not enabled in configuration")
        
        # Get implementation class
        implementation_class = cls._implementations.get(db_type)
        if not implementation_class:
            raise ValueError(f"Unsupported database type: {db_type}")
        
        logger.info(f"Creating database implementation for: {db_type}")
        
        # Create and return instance
        return implementation_class(config)
    
    @classmethod
    def register_implementation(cls, db_type: str, implementation_class):
        """Register a new database implementation"""
        cls._implementations[db_type] = implementation_class
    
    @classmethod
    def get_available_backends(cls) -> list:
        """Get list of available database backends"""
        return list(cls._implementations.keys())