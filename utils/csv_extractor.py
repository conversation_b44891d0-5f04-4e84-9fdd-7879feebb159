"""
CSV Parser with Automatic Delimiter and Encoding Detection

This module provides a comprehensive CSV parsing solution that can:
- Automatically detect file encoding
- Automatically detect CSV delimiter
- Handle large files efficiently
- Extract specified number of rows
- Provide detailed metadata about the parsing process
"""

import csv
import json
import logging
import os
import sys
from typing import Dict, List, Optional, Tuple, Union, Any
from pathlib import Path
import io
from collections import Counter
from core.config_manager import ConfigManager

##########################################################
# Author  : <EMAIL>
# Date    : July 2025
# Version : v1.0
##########################################################

try:
    import chardet
    CHARDET_AVAILABLE = True
except ImportError:
    CHARDET_AVAILABLE = False
    print("Warning: chardet not available. Install with: pip install chardet")

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    print("Warning: pandas not available. Install with: pip install pandas")


class CSVParserError(Exception):
    """Custom exception for CSV parsing errors"""
    pass


class CSVParser:
    """
    CSV parser with automatic delimiter and encoding detection.
    
    Features:
    - Automatic encoding detection using chardet and fallback methods
    - Automatic delimiter detection using csv.Sniffer and frequency analysis
    - Memory-efficient processing for large files
    - Configurable extraction of rows
    - Comprehensive error handling and logging
    """
    
    def __init__(self, config_manager:ConfigManager):
        """
        Initialize the CSV parser with configuration.
        
        Args:
            config_path: Path to JSON configuration file
        """
        self.config = self._load_config(config_manager._get_config_path())
        self.logger = self._setup_logging()
        
    def _load_config(self, config_path: Optional[str]) -> Dict:
        """Load configuration from JSON file or use defaults"""
        default_config = {
            "csv_parser": {
                "default_rows_to_extract": 100,
                "max_file_size_mb": 500,
                "sample_size_for_detection": 8192,
                "encoding_detection": {
                    "fallback_encodings": ["utf-8", "latin-1", "cp1252", "iso-8859-1"],
                    "confidence_threshold": 0.7,
                    "use_chardet": True
                },
                "delimiter_detection": {
                    "possible_delimiters": [",", ";", "\t", "|", ":", " "],
                    "use_csv_sniffer": True,
                    "fallback_delimiter": ",",
                    "min_delimiter_frequency": 2
                },
                "parsing_options": {
                    "skip_blank_lines": True,
                    "strip_whitespace": True,
                    "handle_quotes": True,
                    "quote_char": "\"",
                    "escape_char": "\\",
                    "max_field_size": 131072
                },
                "performance": {
                    "chunk_size": 1024,
                    "use_pandas_for_large_files": True,
                    "memory_limit_mb": 100,
                    "enable_caching": False
                },
                "error_handling": {
                    "strict_mode": False,
                    "skip_bad_lines": True,
                    "max_errors": 10,
                    "continue_on_encoding_error": True
                }
            }
        }
        
        if config_path and os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    # Merge with defaults
                    default_config.update(loaded_config)
            except Exception as e:
                print(f"Warning: Could not load config from {config_path}: {e}")
        
        return default_config["csv_parser"]
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration"""
        logger = logging.getLogger(__name__)
        logger.setLevel(getattr(logging, self.config.get("logging", {}).get("log_level", "INFO")))
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def detect_encoding(self, file_path: str) -> Tuple[str, float]:
        """
        Detect file encoding using multiple methods.
        
        Args:
            file_path: Path to the CSV file
            
        Returns:
            Tuple of (encoding, confidence)
        """
        sample_size = self.config["sample_size_for_detection"]
        
        # Method 1: Use chardet if available
        if CHARDET_AVAILABLE and self.config["encoding_detection"]["use_chardet"]:
            try:
                with open(file_path, 'rb') as f:
                    sample = f.read(sample_size)
                    result = chardet.detect(sample)
                    
                if result and result["confidence"] >= self.config["encoding_detection"]["confidence_threshold"]:
                    encoding = result["encoding"]
                    confidence = result["confidence"]
                    self.logger.info(f"Chardet detected encoding: {encoding} (confidence: {confidence:.2f})")
                    return encoding, confidence
            except Exception as e:
                self.logger.warning(f"Chardet detection failed: {e}")
        
        # Method 2: Try fallback encodings
        fallback_encodings = self.config["encoding_detection"]["fallback_encodings"]
        
        for encoding in fallback_encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    f.read(sample_size)
                self.logger.info(f"Successfully opened with encoding: {encoding}")
                return encoding, 0.9  # High confidence for successful fallback
            except UnicodeDecodeError:
                continue
            except Exception as e:
                self.logger.warning(f"Error testing encoding {encoding}: {e}")
        
        # Method 3: Last resort - utf-8 with error handling
        self.logger.warning("Using utf-8 with error handling as last resort")
        return "utf-8", 0.5
    
    def detect_delimiter(self, file_path: str, encoding: str) -> str:
        """
        Detect CSV delimiter using multiple methods.
        
        Args:
            file_path: Path to the CSV file
            encoding: File encoding
            
        Returns:
            Detected delimiter
        """
        sample_size = self.config["sample_size_for_detection"]
        fallback_delimiter = self.config["delimiter_detection"]["fallback_delimiter"]
        
        try:
            with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
                sample = f.read(sample_size)
        except Exception as e:
            self.logger.error(f"Could not read file for delimiter detection: {e}")
            return fallback_delimiter
        
        if not sample.strip():
            self.logger.warning("File appears to be empty or whitespace only")
            return fallback_delimiter
        
        # Method 1: Use csv.Sniffer
        if self.config["delimiter_detection"]["use_csv_sniffer"]:
            try:
                sniffer = csv.Sniffer()
                # Use only common delimiters for better detection
                delimiters_to_try = ',;\t|'
                dialect = sniffer.sniff(sample, delimiters=delimiters_to_try)
                delimiter = dialect.delimiter
                
                # Validate the detected delimiter makes sense
                if delimiter in self.config["delimiter_detection"]["possible_delimiters"]:
                    self.logger.info(f"CSV Sniffer detected delimiter: '{delimiter}'")
                    return delimiter
                else:
                    self.logger.warning(f"CSV Sniffer detected unusual delimiter: '{delimiter}', continuing to frequency analysis")
            except Exception as e:
                self.logger.warning(f"CSV Sniffer failed: {e}")
        
        # Method 2: Frequency analysis
        possible_delimiters = self.config["delimiter_detection"]["possible_delimiters"]
        delimiter_counts = Counter()
        
        # Analyze multiple lines for better accuracy
        lines = sample.split('\n')[:20]  # Check more lines
        lines = [line.strip() for line in lines if line.strip()]  # Remove empty lines
        
        if not lines:
            self.logger.warning("No non-empty lines found for delimiter detection")
            return fallback_delimiter
        
        for delimiter in possible_delimiters:
            total_count = 0
            consistent_count = 0
            
            for line in lines:
                if delimiter in line:
                    count = line.count(delimiter)
                    total_count += count
                    if count > 0:
                        consistent_count += 1
            
            # Score based on total occurrences and consistency across lines
            if consistent_count > 0:
                consistency_ratio = consistent_count / len(lines)
                score = total_count * consistency_ratio
                delimiter_counts[delimiter] = score
        
        if delimiter_counts:
            # Find highest scoring delimiter
            best_delimiter = delimiter_counts.most_common(1)[0][0]
            best_score = delimiter_counts.most_common(1)[0][1]
            
            min_frequency = self.config["delimiter_detection"]["min_delimiter_frequency"]
            if best_score >= min_frequency:
                self.logger.info(f"Frequency analysis detected delimiter: '{best_delimiter}' (score: {best_score:.2f})")
                return best_delimiter
        
        # Method 3: Simple line-by-line analysis as final fallback
        self.logger.info("Trying simple delimiter detection as final method")
        for delimiter in [',', ';', '\t', '|']:
            if any(delimiter in line for line in lines[:5]):
                self.logger.info(f"Simple detection found delimiter: '{delimiter}'")
                return delimiter
        
        # Final fallback
        self.logger.warning(f"All delimiter detection methods failed, using fallback: '{fallback_delimiter}'")
        return fallback_delimiter
    
    def _get_file_size_mb(self, file_path: str) -> float:
        """Get file size in MB"""
        return os.path.getsize(file_path) / (1024 * 1024)
    
    def parse_csv_standard(self, file_path: str, encoding: str, delimiter: str, 
                          rows_to_extract: int) -> Tuple[List[Dict], Dict]:
        """
        Parse CSV using standard csv module (memory efficient for large files).
        
        Args:
            file_path: Path to CSV file
            encoding: File encoding
            delimiter: CSV delimiter
            rows_to_extract: Number of rows to extract
            
        Returns:
            Tuple of (data, metadata)
        """
        data = []
        metadata = {
            "total_rows_read": 0,
            "headers": [],
            "parsing_method": "standard_csv",
            "errors": []
        }
        
        try:
            csv.field_size_limit(self.config["parsing_options"]["max_field_size"])
            
            with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
                # Read a sample to detect headers and validate delimiter
                sample_lines = []
                f.seek(0)
                for i in range(min(5, rows_to_extract + 1)):  # Read a few lines for analysis
                    line = f.readline()
                    if not line:
                        break
                    sample_lines.append(line)
                
                if not sample_lines:
                    raise CSVParserError("File appears to be empty")
                
                # Validate delimiter works with the sample
                sample_text = ''.join(sample_lines)
                has_delimiter = delimiter in sample_text
                
                if not has_delimiter and delimiter != ' ':  # Space delimiter might not be obvious
                    self.logger.warning(f"Delimiter '{delimiter}' not found in sample, but proceeding with parsing")
                
                # Reset file position
                f.seek(0)
                
                # Try to detect if file has headers
                try:
                    sniffer = csv.Sniffer()
                    has_header = sniffer.has_header(sample_text)
                except Exception as e:
                    # Fallback heuristic: assume headers if first line has more text than numbers
                    first_line = sample_lines[0] if sample_lines else ""
                    has_header = any(not cell.strip().replace('.', '').replace('-', '').isdigit() 
                                   for cell in first_line.split(delimiter) if cell.strip())
                    self.logger.warning(f"Header detection fallback used: {has_header}")
                
                # Configure CSV reader
                csv_params = {
                    'delimiter': delimiter,
                    'quotechar': self.config["parsing_options"]["quote_char"],
                    'skipinitialspace': self.config["parsing_options"]["strip_whitespace"]
                }
                
                if has_header:
                    reader = csv.DictReader(f, **csv_params)
                    try:
                        # Try to get fieldnames
                        fieldnames = reader.fieldnames
                        if fieldnames:
                            metadata["headers"] = list(fieldnames)
                        else:
                            # If fieldnames failed, fall back to manual header reading
                            f.seek(0)
                            first_line = f.readline().strip()
                            if first_line:
                                headers = [col.strip() for col in first_line.split(delimiter)]
                                metadata["headers"] = headers
                                reader = csv.DictReader(f, fieldnames=headers, **csv_params)
                    except Exception as e:
                        self.logger.warning(f"Error reading headers: {e}, falling back to no headers")
                        has_header = False
                
                if not has_header:
                    # Reset and read without headers
                    f.seek(0)
                    reader = csv.reader(f, **csv_params)
                
                # Read the data
                error_count = 0
                max_errors = self.config["error_handling"]["max_errors"]
                
                for i, row in enumerate(reader):
                    if len(data) >= rows_to_extract:
                        break
                    
                    try:
                        if has_header and isinstance(row, dict):
                            # Handle DictReader
                            if self.config["parsing_options"]["strip_whitespace"]:
                                cleaned_row = {}
                                for k, v in row.items():
                                    if k is not None:  # Handle None keys
                                        cleaned_row[k] = v.strip() if isinstance(v, str) else v
                                row = cleaned_row
                            data.append(row)
                        else:
                            # Handle regular reader or fallback
                            if self.config["parsing_options"]["strip_whitespace"]:
                                row = [cell.strip() if isinstance(cell, str) else cell for cell in row]
                            
                            # Convert to dict with column indices or use headers
                            if metadata["headers"]:
                                row_dict = {}
                                for j, cell in enumerate(row):
                                    if j < len(metadata["headers"]):
                                        row_dict[metadata["headers"][j]] = cell
                                    else:
                                        row_dict[f"extra_column_{j}"] = cell
                            else:
                                row_dict = {f"column_{j}": cell for j, cell in enumerate(row)}
                                if not metadata["headers"] and i == 0:
                                    metadata["headers"] = list(row_dict.keys())
                            
                            data.append(row_dict)
                        
                        metadata["total_rows_read"] += 1
                        
                    except Exception as e:
                        error_count += 1
                        error_msg = f"Error parsing row {i}: {e}"
                        metadata["errors"].append(error_msg)
                        
                        if not self.config["error_handling"]["skip_bad_lines"]:
                            raise CSVParserError(error_msg)
                        
                        if error_count >= max_errors:
                            error_msg = f"Too many errors ({error_count}), stopping parse"
                            metadata["errors"].append(error_msg)
                            break
                            
                        self.logger.warning(f"Skipping bad row {i}: {e}")
                            
        except Exception as e:
            error_msg = f"Error parsing CSV with standard method: {e}"
            self.logger.error(error_msg)
            metadata["errors"].append(error_msg)
            raise CSVParserError(error_msg)
        
        return data, metadata
    
    def parse_csv_pandas(self, file_path: str, encoding: str, delimiter: str, 
                        rows_to_extract: int) -> Tuple[List[Dict], Dict]:
        """
        Parse CSV using pandas (efficient for complex files).
        
        Args:
            file_path: Path to CSV file
            encoding: File encoding
            delimiter: CSV delimiter
            rows_to_extract: Number of rows to extract
            
        Returns:
            Tuple of (data, metadata)
        """
        if not PANDAS_AVAILABLE:
            raise CSVParserError("Pandas not available for parsing")
        
        metadata = {
            "total_rows_read": 0,
            "headers": [],
            "parsing_method": "pandas",
            "errors": []
        }
        
        try:
            # Prepare pandas read_csv parameters
            read_params = {
                "filepath_or_buffer": file_path,
                "encoding": encoding,
                "delimiter": delimiter,
                "nrows": rows_to_extract
            }
            
            # Handle version compatibility for error handling parameters
            skip_bad_lines = self.config["error_handling"]["skip_bad_lines"]
            
            # Check pandas version for parameter compatibility
            try:
                import pandas as pd
                pandas_version = tuple(map(int, pd.__version__.split('.')[:2]))
                
                # pandas >= 1.3.0 uses on_bad_lines, older versions use error_bad_lines
                if pandas_version >= (1, 3):
                    read_params["on_bad_lines"] = 'skip' if skip_bad_lines else 'error'
                else:
                    read_params["error_bad_lines"] = not skip_bad_lines
                    read_params["warn_bad_lines"] = True
                    
            except (AttributeError, ValueError) as e:
                # If we can't determine version, try the newer parameter first
                self.logger.warning(f"Could not determine pandas version: {e}")
                try:
                    read_params["on_bad_lines"] = 'skip' if skip_bad_lines else 'error'
                except TypeError:
                    # Fall back to older parameter
                    read_params["error_bad_lines"] = not skip_bad_lines
                    read_params["warn_bad_lines"] = True
            
            # Read the CSV file
            df = pd.read_csv(**read_params)
            
            metadata["headers"] = df.columns.tolist()
            metadata["total_rows_read"] = len(df)
            
            # Convert to list of dictionaries
            data = df.to_dict('records')
            
            # Strip whitespace if configured
            if self.config["parsing_options"]["strip_whitespace"]:
                for row in data:
                    for key, value in row.items():
                        if isinstance(value, str):
                            row[key] = value.strip()
            
        except Exception as e:
            error_msg = f"Error parsing CSV with pandas: {e}"
            self.logger.error(error_msg)
            metadata["errors"].append(error_msg)
            raise CSVParserError(error_msg)
        
        return data, metadata
    
    def parse_csv_file(self, file_path: str, rows_to_extract: Optional[int] = None) -> Dict:
        """
        Main method to parse CSV file with automatic detection.
        
        Args:
            file_path: Path to the CSV file
            rows_to_extract: Number of rows to extract (None for default)
            
        Returns:
            Dictionary containing parsed data and metadata
        """
        if not os.path.exists(file_path):
            raise CSVParserError(f"File not found: {file_path}")
        
        if rows_to_extract is None:
            rows_to_extract = self.config["default_rows_to_extract"]
        
        file_size_mb = self._get_file_size_mb(file_path)
        max_size_mb = self.config["max_file_size_mb"]
        
        if file_size_mb > max_size_mb:
            raise CSVParserError(f"File size ({file_size_mb:.2f}MB) exceeds maximum allowed size ({max_size_mb}MB)")
        
        self.logger.info(f"Starting to parse CSV file: {file_path} ({file_size_mb:.2f}MB)")
        
        # Step 1: Detect encoding
        encoding, encoding_confidence = self.detect_encoding(file_path)
        
        # Step 2: Detect delimiter
        delimiter = self.detect_delimiter(file_path, encoding)
        
        # Step 3: Choose parsing method based on file size and configuration
        use_pandas = (
            PANDAS_AVAILABLE and 
            self.config["performance"]["use_pandas_for_large_files"] and
            file_size_mb > self.config["performance"]["memory_limit_mb"]
        )
        
        # Step 4: Parse the file
        primary_method = "pandas" if use_pandas else "standard"
        fallback_method = "standard" if use_pandas else "pandas"
        
        parsing_errors = []
        
        try:
            if use_pandas:
                data, parsing_metadata = self.parse_csv_pandas(file_path, encoding, delimiter, rows_to_extract)
            else:
                data, parsing_metadata = self.parse_csv_standard(file_path, encoding, delimiter, rows_to_extract)
                
        except Exception as e:
            parsing_errors.append(f"{primary_method} method failed: {e}")
            self.logger.warning(f"Primary parsing method ({primary_method}) failed: {e}")
            
            # Try fallback method
            try:
                self.logger.info(f"Attempting fallback to {fallback_method} method")
                if use_pandas:
                    data, parsing_metadata = self.parse_csv_standard(file_path, encoding, delimiter, rows_to_extract)
                else:
                    if PANDAS_AVAILABLE:
                        data, parsing_metadata = self.parse_csv_pandas(file_path, encoding, delimiter, rows_to_extract)
                    else:
                        raise CSVParserError("Pandas not available for fallback parsing")
                        
                self.logger.info(f"Successfully parsed using {fallback_method} method")
                
            except Exception as e2:
                parsing_errors.append(f"{fallback_method} method failed: {e2}")
                self.logger.error(f"Fallback parsing method ({fallback_method}) also failed: {e2}")
                
                # Try with different delimiters as final attempt
                self.logger.info("Attempting final fallback with common delimiters")
                for fallback_delimiter in [',', ';', '\t', '|']:
                    if fallback_delimiter != delimiter:
                        try:
                            self.logger.info(f"Trying delimiter: '{fallback_delimiter}'")
                            data, parsing_metadata = self.parse_csv_standard(file_path, encoding, fallback_delimiter, rows_to_extract)
                            self.logger.info(f"Success with delimiter: '{fallback_delimiter}'")
                            delimiter = fallback_delimiter  # Update for metadata
                            break
                        except Exception as e3:
                            parsing_errors.append(f"Delimiter '{fallback_delimiter}' failed: {e3}")
                            continue
                else:
                    # All methods failed
                    error_summary = "; ".join(parsing_errors)
                    raise CSVParserError(f"All parsing methods failed. Errors: {error_summary}")
        
        # Step 5: Compile results
        result = {
            "data": data,
            "metadata": {
                "file_path": file_path,
                "file_size_mb": round(file_size_mb, 2),
                "detected_encoding": encoding,
                "encoding_confidence": encoding_confidence,
                "detected_delimiter": delimiter,
                "rows_requested": rows_to_extract,
                "rows_extracted": len(data),
                "headers": parsing_metadata.get("headers", []),
                "parsing_method": parsing_metadata.get("parsing_method", "unknown"),
                "errors": parsing_metadata.get("errors", [])
            }
        }
        
        self.logger.info(f"Successfully parsed {len(data)} rows from {file_path}")
        return result



def main():
    """Example usage of the CSV parser"""
    config_manager = ConfigManager()
    parser = CSVParser(config_manager)
    
    # Example file path (replace with actual file)
    file_path = "../test_data/sample.csv"
    
    if not os.path.exists(file_path):
        print(f"Example file {file_path} not found. Please provide a valid CSV file path.")
        return
    
    try:
        result = parser.parse_csv_file(file_path, rows_to_extract=10)
        
        print("Parsing Results:")
        print(f"File: {result['metadata']['file_path']}")
        print(f"Encoding: {result['metadata']['detected_encoding']}")
        print(f"Delimiter: '{result['metadata']['detected_delimiter']}'")
        print(f"Rows extracted: {result['metadata']['rows_extracted']}")
        print(f"Headers: {result['metadata']['headers']}")
        
        print("\nFirst few rows:")
        for i, row in enumerate(result['data'][:3]):
            print(f"Row {i+1}: {row}")
            
    except CSVParserError as e:
        print(f"CSV parsing error: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")


if __name__ == "__main__":
    main()