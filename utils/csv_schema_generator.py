import json
import re
from typing import List, Dict, Any, Set, Optional, Tuple
from datetime import datetime
import uuid

class IntelligentSchemaGenerator:
    
    def __init__(self):
        # Regex patterns for format detection
        self.patterns = {
        }
        
        # Date patterns to try
        self.date_formats = [
            '%Y-%m-%d',           # 2023-12-01
            '%Y/%m/%d',           # 2023/12/01  
            '%d/%m/%Y',           # 01/12/2023
            '%m/%d/%Y',           # 12/01/2023
            '%d-%m-%Y',           # 01-12-2023
            '%m-%d-%Y',           # 12-01-2023
            '%Y-%m-%d %H:%M:%S',  # 2023-12-01 14:30:00
            '%Y-%m-%dT%H:%M:%S',  # 2023-12-01T14:30:00
            '%Y-%m-%dT%H:%M:%SZ', # 2023-12-01T14:30:00Z
            '%d %b %Y',           # 01 Dec 2023
            '%b %d, %Y',          # Dec 01, 2023
        ]

    def detect_format(self, values: List[Any]) -> Optional[str]:
        """Detect special formats like email, URL, UUID, etc."""
        non_null_string_values = [str(v).strip() for v in values if v is not None and str(v).strip()]
        
        if not non_null_string_values:
            return None
            
        total_values = len(non_null_string_values)
        
        # Check each pattern - need 80% match rate
        for format_name, pattern in self.patterns.items():
            matches = sum(1 for v in non_null_string_values if re.match(pattern, v, re.IGNORECASE))
            if matches / total_values >= 0.8:
                return format_name
                
        return None

    def detect_date_format(self, values: List[Any]) -> Optional[str]:
        """Detect if values are dates and return the format."""
        non_null_string_values = [str(v).strip() for v in values if v is not None and str(v).strip()]
        
        if not non_null_string_values:
            return None
            
        total_values = len(non_null_string_values)
        
        for date_format in self.date_formats:
            successful_parses = 0
            for value in non_null_string_values:
                try:
                    datetime.strptime(value, date_format)
                    successful_parses += 1
                except ValueError:
                    continue
            
            # If 80% of values match this format, consider it a date
            if successful_parses / total_values >= 0.8:
                return date_format
                
        return None

    def detect_base_type(self, values: List[Any]) -> str:
        """Detect basic JSON schema type."""
        non_null_values = [v for v in values if v is not None]
        
        if not non_null_values:
            return "null"
        
        # Check boolean first (since bool is subclass of int in Python)
        if all(isinstance(v, bool) for v in non_null_values):
            return "boolean"
        
        # Check integer
        # if all(isinstance(v, int) and not isinstance(v, bool) for v in non_null_values):
        # return "integer"
        
        # Check number (float)
        if all(isinstance(v, (int, float)) and not isinstance(v, bool) for v in non_null_values):
            return "number"
        
        # Try to parse as numbers
        try:
            parsed_numbers = []
            for v in non_null_values:
                if isinstance(v, (int, float)) and not isinstance(v, bool):
                    parsed_numbers.append(v)
                else:
                    # Try to parse string as number
                    str_val = str(v).strip().replace(',', '')
                    if '.' in str_val:
                        parsed_numbers.append(float(str_val))
                    else:
                        parsed_numbers.append(int(str_val))
            
                return "number"
                
        except (ValueError, TypeError):
            pass
        
        # Check boolean-like strings
        boolean_values = {'true', 'false', '1', '0', 'yes', 'no', 'y', 'n', 't', 'f'}
        if all(str(v).lower() in boolean_values for v in non_null_values):
            return "boolean"
        
        return "string"

    def detect_enum(self, values: List[Any], max_enum_size: int = 10) -> Optional[List[Any]]:
        """Detect if column should be an enum (limited distinct values)."""
        non_null_values = [v for v in values if v is not None]
        unique_values = list(set(non_null_values))
        
        # Consider enum if:
        # 1. Less than max_enum_size unique values
        # 2. At least 3 duplicate values per unique value on average
        if len(unique_values) <= max_enum_size and len(non_null_values) >= len(unique_values) * 3:
            return sorted(unique_values)
        
        return None

    def detect_constraints(self, values: List[Any], data_type: str) -> Dict[str, Any]:
        """Detect constraints like min/max, length, patterns."""
        constraints = {}
        non_null_values = [v for v in values if v is not None]
        
        if not non_null_values:
            return constraints
        
        if data_type in ["integer", "number"]:
            # Convert to numbers for analysis
            numbers = []
            for v in non_null_values:
                try:
                    if isinstance(v, (int, float)):
                        numbers.append(v)
                    else:
                        # Try parsing string numbers
                        str_val = str(v).strip().replace(',', '')
                        numbers.append(float(str_val) if '.' in str_val else int(str_val))
                except (ValueError, TypeError):
                    continue
            
            if numbers:
                constraints["minimum"] = min(numbers)
                constraints["maximum"] = max(numbers)
        
        elif data_type == "string":
            # String length constraints
            string_values = [str(v) for v in non_null_values]
            lengths = [len(s) for s in string_values]
            
            if lengths:
                constraints["minLength"] = min(lengths)
                constraints["maxLength"] = max(lengths)
        
        # Check uniqueness
        if len(set(non_null_values)) == len(non_null_values):
            constraints["uniqueItems"] = True
        
        return constraints

    def detect_id_field(self, column_name: str, values: List[Any]) -> bool:
        """Detect if this is likely an ID field."""
        name_lower = column_name.lower()
        id_indicators = ['id', '_id', 'key', 'pk', 'primary', 'identifier', 'uuid']
        
        # Check name patterns
        if any(indicator in name_lower for indicator in id_indicators):
            return True
        
        # Check if values look like IDs (unique, sequential integers or UUIDs)
        non_null_values = [v for v in values if v is not None]
        if not non_null_values:
            return False
        
        # Check uniqueness
        if len(set(non_null_values)) != len(non_null_values):
            return False
        
        # Check if sequential integers
        try:
            int_values = [int(v) for v in non_null_values]
            sorted_values = sorted(int_values)
            if sorted_values == list(range(min(sorted_values), max(sorted_values) + 1)):
                return True
        except (ValueError, TypeError):
            pass
        
        # Check if UUIDs
        try:
            for v in non_null_values:
                uuid.UUID(str(v))
            return True
        except (ValueError, TypeError):
            pass
        
        return False

    def generate_property_schema(self, column_name: str, values: List[Any]) -> str:
        """Generate data type for a single property/column."""
        # Detect base type
        base_type = self.detect_base_type(values)
        
        # For string types, check for special formats and return more specific type
        if base_type == "string":
            # Check for date format
            date_format = self.detect_date_format(values)
            if date_format:
                return "date-time" if any(char in date_format for char in ['H', 'M', 'S']) else "date"
            
            # Check for other formats
            detected_format = self.detect_format(values)
            if detected_format:
                return detected_format
        
        return base_type

    def generate_schema(self, normalized_file_name: str, sample_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate simplified JSON schema with only column names and data types."""
        if not sample_data:
            return {
                "file_name": normalized_file_name,
                "schema": {}
            }
        
        # Get all columns
        columns = list(sample_data[0].keys())
        
        # Generate schema for each property (just data type)
        schema_properties = {}
        
        for column in columns:
            column_values = [row.get(column) for row in sample_data]
            # Get just the data type
            data_type = self.generate_property_schema(column, column_values)
            schema_properties[column] = data_type
        
        # Build simplified schema
        schema = {
            "file_name": normalized_file_name,
            "schema": schema_properties
        }
        
        return schema

# Convenience functions
def generate_intelligent_schema(normalized_file_name: str, sample_data: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Generate intelligent schema using the IntelligentSchemaGenerator."""
    generator = IntelligentSchemaGenerator()
    return generator.generate_schema(normalized_file_name, sample_data)

def print_intelligent_schema(normalized_file_name: str, sample_data: List[Dict[str, Any]]) -> None:
    """Generate and print intelligent schema."""
    schema = generate_intelligent_schema(normalized_file_name, sample_data)
    print(json.dumps(schema, indent=2))

# Example usage
if __name__ == "__main__":
    # Comprehensive sample data showcasing intelligent detection
    sample_data = [
        {
            "user_id": "550e8400-e29b-41d4-a716-446655440000",
            "email": "<EMAIL>",
            "phone": "******-123-4567",
            "age": 25,
            "salary": "50,000.50",
            "status": "active",
            "registration_date": "2023-01-15",
            "last_login": "2023-12-01 14:30:00",
            "website": "https://johndoe.com",
            "ip_address": "***********",
            "is_premium": True,
            "country_code": "US"
        },
        {
            "user_id": "6ba7b810-9dad-11d1-80b4-00c04fd430c8",
            "email": "<EMAIL>",
            "phone": "******-987-6543",
            "age": 30,
            "salary": "75,000.00",
            "status": "active",
            "registration_date": "2023-02-20",
            "last_login": "2023-12-02 09:15:00",
            "website": "https://janesmith.net",
            "ip_address": "********",
            "is_premium": False,
            "country_code": "CA"
        },
        {
            "user_id": "6ba7b811-9dad-11d1-80b4-00c04fd430c9",
            "email": "<EMAIL>",
            "phone": "+44-20-7946-0958",
            "age": 35,
            "salary": "60,000.25",
            "status": "inactive",
            "registration_date": "2023-03-10",
            "last_login": "2023-11-15 16:45:00",
            "website": "https://bobwilson.info",
            "ip_address": "**********",
            "is_premium": True,
            "country_code": "UK"
        }
    ]
    
    # Generate and display simplified schema
    print("=== Simplified Schema Generation (Column Names + Data Types Only) ===")
    print_intelligent_schema("users.csv", sample_data)