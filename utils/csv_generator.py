import csv
import json
import os
from typing import Dict, List, Any, Tuple
import pandas as pd
from logger import Logger


class TargetCSVGenerator:
    """Generate target CSV format from mapped rules"""
    
    def __init__(self):
        self.logger = Logger.get_logger("csv_generator")
        self.target_columns = [
            'rule_name',
            'rule_type', 
            'source_file',
            'reference_file',
            'source_col',
            'reference_col',
            'rule_config',
            'severity',
            'is_active'
        ]
    
    def format_rule_to_csv_row(self, mapped_rule: Dict) -> Dict[str, Any]:
        """Format single rule to target CSV row format"""
        try:
            # Ensure all required columns are present
            csv_row = {}
            
            for column in self.target_columns:
                value = mapped_rule.get(column, '')
                
                # Special handling for rule_config - convert dict to JSON string
                if column == 'rule_config':
                    if isinstance(value, dict):
                        value = json.dumps(value)
                    elif value is None:
                        value = '{}'
                
                # Ensure string values for CSV
                csv_row[column] = str(value) if value is not None else ''
            
            return csv_row
            
        except Exception as e:
            self.logger.error(f"Failed to format rule to CSV row: {str(e)}")
            return self._create_empty_row()
    
    def generate_csv_content(self, mapped_rules: List[Dict]) -> str:
        """Generate complete CSV content from mapped rules"""
        try:
            if not mapped_rules:
                self.logger.warning("No mapped rules provided for CSV generation")
                return self._generate_empty_csv()
            
            # Format all rules to CSV rows
            csv_rows = []
            for i, rule in enumerate(mapped_rules):
                try:
                    csv_row = self.format_rule_to_csv_row(rule)
                    csv_rows.append(csv_row)
                except Exception as e:
                    self.logger.warning(f"Failed to format rule {i}: {str(e)}")
                    continue
            
            if not csv_rows:
                self.logger.warning("No valid CSV rows generated")
                return self._generate_empty_csv()
            
            # Generate CSV content
            csv_content = self._rows_to_csv_string(csv_rows)
            
            self.logger.info(f"Generated CSV content with {len(csv_rows)} rules")
            return csv_content
            
        except Exception as e:
            self.logger.error(f"Failed to generate CSV content: {str(e)}")
            raise
    
    def save_csv_file(self, csv_content: str, output_path: str) -> bool:
        """Save CSV content to file"""
        try:
            # Create directory if it doesn't exist
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)
            
            # Write CSV content to file
            with open(output_path, 'w', newline='', encoding='utf-8') as file:
                file.write(csv_content)
            
            self.logger.info(f"Successfully saved CSV file: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save CSV file {output_path}: {str(e)}")
            return False
    
    def validate_output_format(self, csv_content: str) -> Tuple[bool, List[str]]:
        """Validate generated CSV against target schema"""
        issues = []
        
        try:
            # Parse CSV content
            lines = csv_content.strip().split('\n')
            if not lines:
                issues.append("CSV content is empty")
                return False, issues
            
            # Check header
            header_line = lines[0]
            headers = [h.strip() for h in header_line.split(',')]
            
            # Validate headers
            missing_headers = [col for col in self.target_columns if col not in headers]
            if missing_headers:
                issues.append(f"Missing required headers: {missing_headers}")
            
            extra_headers = [h for h in headers if h not in self.target_columns]
            if extra_headers:
                issues.append(f"Unexpected headers found: {extra_headers}")
            
            # Validate data rows
            if len(lines) < 2:
                issues.append("No data rows found in CSV")
            else:
                # Check a sample of data rows
                sample_size = min(10, len(lines) - 1)
                for i in range(1, sample_size + 1):
                    row_issues = self._validate_csv_row(lines[i], headers, i)
                    issues.extend(row_issues)
            
            # Check for consistent column count
            expected_col_count = len(headers)
            for i, line in enumerate(lines[1:], 1):  # Skip header
                actual_col_count = len(line.split(','))
                if actual_col_count != expected_col_count:
                    issues.append(f"Row {i}: Expected {expected_col_count} columns, got {actual_col_count}")
                    if len(issues) > 20:  # Limit issues reported
                        issues.append("... (more issues truncated)")
                        break
            
        except Exception as e:
            issues.append(f"Failed to validate CSV format: {str(e)}")
        
        is_valid = len(issues) == 0
        return is_valid, issues
    
    def _validate_csv_row(self, row_line: str, headers: List[str], row_num: int) -> List[str]:
        """Validate individual CSV row"""
        issues = []
        
        try:
            values = [v.strip() for v in row_line.split(',')]
            
            if len(values) != len(headers):
                issues.append(f"Row {row_num}: Column count mismatch")
                return issues
            
            # Create row dict for validation
            row_dict = dict(zip(headers, values))
            
            # Validate required fields are not empty
            required_fields = ['rule_name', 'rule_type', 'source_file', 'severity', 'is_active']
            for field in required_fields:
                if field in row_dict and not row_dict[field].strip():
                    issues.append(f"Row {row_num}: Required field '{field}' is empty")
            
            # Validate rule_type values
            valid_rule_types = [
                'NOT_NULL', 'REGEX', 'CROSS_FILE_LEVEL', 'RANGE_CHECK',
                'DISTINCT_COUNT', 'FILE_LEVEL', 'PERCENTILE_CHECK', 
                'AGGREGATE_CHECK', 'CUSTOM_CHECK'
            ]
            if 'rule_type' in row_dict:
                if row_dict['rule_type'] not in valid_rule_types:
                    issues.append(f"Row {row_num}: Invalid rule_type '{row_dict['rule_type']}'")
            
            # Validate severity values
            valid_severities = ['Warning', 'Error']
            if 'severity' in row_dict:
                if row_dict['severity'] not in valid_severities:
                    issues.append(f"Row {row_num}: Invalid severity '{row_dict['severity']}'")
            
            # Validate is_active values
            valid_active_values = ['TRUE', 'FALSE']
            if 'is_active' in row_dict:
                if row_dict['is_active'] not in valid_active_values:
                    issues.append(f"Row {row_num}: Invalid is_active '{row_dict['is_active']}'")
            
            # Validate rule_config is valid JSON
            if 'rule_config' in row_dict and row_dict['rule_config'].strip():
                try:
                    json.loads(row_dict['rule_config'])
                except json.JSONDecodeError:
                    issues.append(f"Row {row_num}: rule_config is not valid JSON")
            
        except Exception as e:
            issues.append(f"Row {row_num}: Validation error - {str(e)}")
        
        return issues
    
    def _rows_to_csv_string(self, csv_rows: List[Dict]) -> str:
        """Convert list of row dictionaries to CSV string"""
        try:
            import io
            
            output = io.StringIO()
            writer = csv.DictWriter(output, fieldnames=self.target_columns)
            
            # Write header
            writer.writeheader()
            
            # Write data rows
            for row in csv_rows:
                writer.writerow(row)
            
            csv_content = output.getvalue()
            output.close()
            
            return csv_content
            
        except Exception as e:
            self.logger.error(f"Failed to convert rows to CSV string: {str(e)}")
            raise
    
    def _create_empty_row(self) -> Dict[str, str]:
        """Create empty row with all required columns"""
        return {column: '' for column in self.target_columns}
    
    def _generate_empty_csv(self) -> str:
        """Generate CSV with just headers"""
        return ','.join(self.target_columns) + '\n'
    
    def preview_csv_output(self, mapped_rules: List[Dict], num_rows: int = 5) -> str:
        """Generate preview of CSV output with limited rows"""
        try:
            preview_rules = mapped_rules[:num_rows]
            preview_content = self.generate_csv_content(preview_rules)
            
            lines = preview_content.split('\n')
            if len(lines) > num_rows + 1:  # +1 for header
                lines = lines[:num_rows + 1]
                lines.append(f"... ({len(mapped_rules) - num_rows} more rows)")
                preview_content = '\n'.join(lines)
            
            return preview_content
            
        except Exception as e:
            self.logger.error(f"Failed to generate CSV preview: {str(e)}")
            return self._generate_empty_csv()
    
    def export_to_dataframe(self, mapped_rules: List[Dict]) -> pd.DataFrame:
        """Export mapped rules to pandas DataFrame"""
        try:
            # Format rules to CSV rows
            csv_rows = [self.format_rule_to_csv_row(rule) for rule in mapped_rules]
            
            # Create DataFrame
            df = pd.DataFrame(csv_rows, columns=self.target_columns)
            
            self.logger.info(f"Created DataFrame with {len(df)} rows and {len(df.columns)} columns")
            return df
            
        except Exception as e:
            self.logger.error(f"Failed to export to DataFrame: {str(e)}")
            # Return empty DataFrame with correct columns
            return pd.DataFrame(columns=self.target_columns)
    
    def get_output_statistics(self, mapped_rules: List[Dict]) -> Dict[str, Any]:
        """Get statistics about the output that will be generated"""
        stats = {
            'total_rules': len(mapped_rules),
            'estimated_file_size_kb': 0,
            'rule_types_distribution': {},
            'severity_distribution': {},
            'files_referenced': set(),
            'columns_referenced': set(),
            'validation_summary': {'valid_rules': 0, 'invalid_rules': 0}
        }
        
        total_chars = len(','.join(self.target_columns)) + 1  # Header size
        
        for rule in mapped_rules:
            try:
                # Format rule and estimate size
                csv_row = self.format_rule_to_csv_row(rule)
                row_chars = sum(len(str(value)) for value in csv_row.values()) + len(csv_row) - 1  # +commas
                total_chars += row_chars + 1  # +newline
                
                # Count rule types
                rule_type = rule.get('rule_type', 'Unknown')
                stats['rule_types_distribution'][rule_type] = stats['rule_types_distribution'].get(rule_type, 0) + 1
                
                # Count severities
                severity = rule.get('severity', 'Unknown')
                stats['severity_distribution'][severity] = stats['severity_distribution'].get(severity, 0) + 1
                
                # Track referenced files and columns
                if rule.get('source_file'):
                    stats['files_referenced'].add(rule['source_file'])
                if rule.get('source_col'):
                    stats['columns_referenced'].add(rule['source_col'])
                
                # Validation check
                csv_content_sample = self._rows_to_csv_string([csv_row])
                is_valid, _ = self.validate_output_format(csv_content_sample)
                if is_valid:
                    stats['validation_summary']['valid_rules'] += 1
                else:
                    stats['validation_summary']['invalid_rules'] += 1
                    
            except Exception as e:
                self.logger.warning(f"Failed to process rule for statistics: {str(e)}")
                stats['validation_summary']['invalid_rules'] += 1
        
        # Calculate estimated file size
        stats['estimated_file_size_kb'] = round(total_chars / 1024, 2)
        
        # Convert sets to counts
        stats['unique_files_referenced'] = len(stats['files_referenced'])
        stats['unique_columns_referenced'] = len(stats['columns_referenced'])
        del stats['files_referenced']
        del stats['columns_referenced']
        
        return stats
