import json
from typing import Dict, List, Any, Optional
from logger import Logger


class ValidationRuleMapper:
    """Map input validation patterns to target rule types"""
    
    def __init__(self, llm_processor):
        self.llm_processor = llm_processor
        self.logger = Logger.get_logger("validation_rule_mapper")
        self.rule_patterns = self._initialize_rule_patterns()
    
    def _initialize_rule_patterns(self) -> Dict[str, Dict]:
        """Initialize rule pattern mappings"""
        return {
            'duplicate_checks': {
                'all_columns': {
                    'pattern': ['does the file have duplicates at all columns'],
                    'rule_type': 'FILE_LEVEL',
                    'config_template': {"check": "duplicates", "scope": "all_columns"}
                },
                'specific_column': {
                    'pattern': ['does the file have duplicates at specified column'],
                    'rule_type': 'NOT_NULL',
                    'config_template': {"check": "duplicates", "scope": "single_column"}
                }
            },
            'date_validations': {
                'min': {
                    'pattern': ['min'],
                    'rule_type': 'RANGE_CHECK',
                    'config_template': {"check": "min_date", "operator": ">="}
                },
                'max': {
                    'pattern': ['max'],
                    'rule_type': 'RANGE_CHECK',
                    'config_template': {"check": "max_date", "operator": "<="}
                }
            },
            'string_validations': {
                'distinct_count': {
                    'pattern': ['number of distinct value'],
                    'rule_type': 'DISTINCT_COUNT',
                    'config_template': {"check": "distinct_count", "min_threshold": 1}
                },
                'distinct_values': {
                    'pattern': ['distinct values'],
                    'rule_type': 'DISTINCT_COUNT',
                    'config_template': {"check": "distinct_values", "action": "list"}
                }
            },
            'numeric_validations': {
                'sum': {
                    'pattern': ['sum'],
                    'rule_type': 'AGGREGATE_CHECK',
                    'config_template': {"check": "sum", "aggregation": "sum"}
                },
                'average': {
                    'pattern': ['average'],
                    'rule_type': 'AGGREGATE_CHECK',
                    'config_template': {"check": "average", "aggregation": "mean"}
                },
                'min': {
                    'pattern': ['min'],
                    'rule_type': 'RANGE_CHECK',
                    'config_template': {"check": "min_value", "operator": ">="}
                },
                'max': {
                    'pattern': ['max'],
                    'rule_type': 'RANGE_CHECK',
                    'config_template': {"check": "max_value", "operator": "<="}
                },
                'percentile': {
                    'pattern': ['percentile'],
                    'rule_type': 'PERCENTILE_CHECK',
                    'config_template': {"check": "percentile", "percentile": None}
                }
            }
        }
    
    def map_validation_rule(self, input_rule: Dict) -> Dict[str, Any]:
        """Map input rule to target format using pattern matching and LLM"""
        try:
            # First try pattern-based mapping
            pattern_result = self._map_using_patterns(input_rule)
            
            if pattern_result:
                self.logger.info(f"Successfully mapped using patterns: {input_rule['attribute']}")
                return pattern_result
            
            # Fall back to LLM mapping for complex cases
            self.logger.info(f"Using LLM for complex rule mapping: {input_rule['attribute']}")
            return self.llm_processor.map_validation_rule(input_rule)
            
        except Exception as e:
            self.logger.error(f"Failed to map validation rule: {str(e)}")
            return self._create_default_mapping(input_rule)
    
    def _map_using_patterns(self, input_rule: Dict) -> Optional[Dict[str, Any]]:
        """Map rule using predefined patterns"""
        try:
            column_type = input_rule['column_type'].lower()
            attribute = input_rule['attribute'].lower()
            
            # Handle duplicate checks (applicable to 'All' column type)
            if column_type == 'all':
                return self._map_duplicate_check(input_rule)
            
            # Handle date validations
            elif column_type == 'date':
                return self._map_date_validation(input_rule)
            
            # Handle string validations
            elif column_type == 'string':
                return self._map_string_validation(input_rule)
            
            # Handle numeric validations
            elif column_type == 'numeric':
                return self._map_numeric_validation(input_rule)
            
            return None
            
        except Exception as e:
            self.logger.error(f"Pattern mapping failed: {str(e)}")
            return None
    
    def _map_duplicate_check(self, input_rule: Dict) -> Dict[str, Any]:
        """Map duplicate validation rules"""
        attribute = input_rule['attribute'].lower()
        
        if 'all columns' in attribute:
            rule_type = 'FILE_LEVEL'
            config = {"check": "duplicates", "scope": "all_columns"}
            severity = 'Error'  # Data integrity is critical
        else:
            rule_type = 'NOT_NULL'
            config = {"check": "duplicates", "scope": "single_column"}
            severity = 'Error'
        
        return self._build_mapped_rule(
            input_rule=input_rule,
            rule_type=rule_type,
            config=config,
            severity=severity
        )
    
    def _map_date_validation(self, input_rule: Dict) -> Dict[str, Any]:
        """Map date min/max validations"""
        attribute = input_rule['attribute'].lower()
        
        if 'min' in attribute:
            rule_type = 'RANGE_CHECK'
            config = {"check": "min_date", "operator": ">="}
        elif 'max' in attribute:
            rule_type = 'RANGE_CHECK'
            config = {"check": "max_date", "operator": "<="}
        else:
            return None
        
        return self._build_mapped_rule(
            input_rule=input_rule,
            rule_type=rule_type,
            config=config,
            severity='Warning'
        )
    
    def _map_string_validation(self, input_rule: Dict) -> Dict[str, Any]:
        """Map string validation rules"""
        attribute = input_rule['attribute'].lower()
        
        if 'number of distinct value' in attribute:
            rule_type = 'DISTINCT_COUNT'
            config = {"check": "distinct_count", "min_threshold": 1}
        elif 'distinct values' in attribute:
            rule_type = 'DISTINCT_COUNT'
            config = {"check": "distinct_values", "action": "list"}
        else:
            return None
        
        return self._build_mapped_rule(
            input_rule=input_rule,
            rule_type=rule_type,
            config=config,
            severity='Warning'
        )
    
    def _map_numeric_validation(self, input_rule: Dict) -> Dict[str, Any]:
        """Map numeric validation rules"""
        attribute = input_rule['attribute'].lower()
        
        if attribute == 'sum':
            rule_type = 'AGGREGATE_CHECK'
            config = {"check": "sum", "aggregation": "sum"}
        elif attribute == 'average':
            rule_type = 'AGGREGATE_CHECK'
            config = {"check": "average", "aggregation": "mean"}
        elif attribute == 'min':
            rule_type = 'RANGE_CHECK'
            config = {"check": "min_value", "operator": ">="}
        elif attribute == 'max':
            rule_type = 'RANGE_CHECK'
            config = {"check": "max_value", "operator": "<="}
        elif 'percentile' in attribute:
            # Extract percentile number
            percentile_num = self._extract_percentile_number(attribute)
            rule_type = 'PERCENTILE_CHECK'
            config = {"check": "percentile", "percentile": percentile_num}
        else:
            return None
        
        return self._build_mapped_rule(
            input_rule=input_rule,
            rule_type=rule_type,
            config=config,
            severity='Warning'  # Statistical validations are typically warnings
        )
    
    def _extract_percentile_number(self, attribute: str) -> Optional[int]:
        """Extract percentile number from attribute string"""
        try:
            # Look for pattern like "10th percentile", "25th percentile", etc.
            import re
            match = re.search(r'(\d+)(?:st|nd|rd|th)?\s*percentile', attribute)
            if match:
                return int(match.group(1))
        except:
            pass
        return None
    
    def _build_mapped_rule(self, input_rule: Dict, rule_type: str, config: Dict, severity: str) -> Dict[str, Any]:
        """Build complete mapped rule structure"""
        return {
            'rule_name': self.generate_rule_name(input_rule, rule_type),
            'rule_type': rule_type,
            'source_file': f"{input_rule['filename']}",
            'reference_file': '',  # Most rules don't need reference files
            'source_col': self._extract_primary_column(input_rule['column_name']),
            'reference_col': '',
            'rule_config': config,
            'severity': severity,
            'is_active': 'TRUE'
        }
    
    def generate_rule_name(self, input_rule: Dict, rule_type: str = None) -> str:
        """Generate meaningful rule names"""
        try:
            # Extract components
            column_type = input_rule['column_type'].upper()
            column_name = self._extract_primary_column(input_rule['column_name'])
            attribute = input_rule['attribute'].replace(' ', '_').upper()
            
            # Truncate attribute if too long
            if len(attribute) > 20:
                attribute = attribute[:20]
            
            # Create rule name
            rule_name = f"{column_type}_{column_name}_{attribute}"
            
            # Ensure it's not too long
            if len(rule_name) > 50:
                rule_name = rule_name[:50]
            
            return rule_name
            
        except Exception as e:
            self.logger.warning(f"Failed to generate rule name: {str(e)}")
            return f"RULE_{input_rule.get('row_index', 'UNKNOWN')}"
    
    def _extract_primary_column(self, column_name_str: str) -> str:
        """Extract primary column name from column string"""
        try:
            # Handle list format like "['Time', 'Geography', ...]"
            if column_name_str.startswith('[') and column_name_str.endswith(']'):
                # For multi-column rules, extract first meaningful column or use 'ALL_COLUMNS'
                cleaned = column_name_str.strip('[]').replace("'", "").replace('"', '')
                columns = [col.strip() for col in cleaned.split(',')]
                
                # Priority order for column selection
                priority_columns = ['Time', 'Geography', 'Product', 'UPC', 'Brand']
                for priority_col in priority_columns:
                    for col in columns:
                        if priority_col.lower() in col.lower():
                            return col.replace(' ', '_')
                
                # If no priority column found, return first column or ALL_COLUMNS
                if len(columns) > 5:  # Many columns
                    return 'ALL_COLUMNS'
                else:
                    return columns[0].replace(' ', '_') if columns else 'UNKNOWN_COLUMN'
            
            # Single column name
            return column_name_str.replace(' ', '_')
            
        except Exception as e:
            self.logger.warning(f"Failed to extract primary column: {str(e)}")
            return 'UNKNOWN_COLUMN'
    
    def _create_default_mapping(self, input_rule: Dict) -> Dict[str, Any]:
        """Create default mapping when all else fails"""
        return {
            'rule_name': self.generate_rule_name(input_rule),
            'rule_type': 'CUSTOM_CHECK',
            'source_file': f"{input_rule['filename']}",
            'reference_file': '',
            'source_col': self._extract_primary_column(input_rule['column_name']),
            'reference_col': '',
            'rule_config': {"check": "custom", "description": input_rule['attribute']},
            'severity': 'Warning',
            'is_active': 'TRUE'
        }
    
    def validate_mapped_rule(self, mapped_rule: Dict) -> tuple[bool, List[str]]:
        """Validate mapped rule structure"""
        required_fields = [
            'rule_name', 'rule_type', 'source_file', 'reference_file',
            'source_col', 'reference_col', 'rule_config', 'severity', 'is_active'
        ]
        
        issues = []
        
        # Check required fields
        for field in required_fields:
            if field not in mapped_rule:
                issues.append(f"Missing required field: {field}")
        
        # Validate rule_type
        valid_rule_types = [
            'NOT_NULL', 'REGEX', 'CROSS_FILE_LEVEL', 'RANGE_CHECK', 
            'DISTINCT_COUNT', 'FILE_LEVEL', 'PERCENTILE_CHECK', 
            'AGGREGATE_CHECK', 'CUSTOM_CHECK'
        ]
        if mapped_rule.get('rule_type') not in valid_rule_types:
            issues.append(f"Invalid rule_type: {mapped_rule.get('rule_type')}")
        
        # Validate severity
        valid_severities = ['Warning', 'Error']
        if mapped_rule.get('severity') not in valid_severities:
            issues.append(f"Invalid severity: {mapped_rule.get('severity')}")
        
        # Validate is_active
        valid_active_values = ['TRUE', 'FALSE']
        if mapped_rule.get('is_active') not in valid_active_values:
            issues.append(f"Invalid is_active: {mapped_rule.get('is_active')}")
        
        # Validate rule_config is dict or valid JSON string
        rule_config = mapped_rule.get('rule_config')
        if rule_config is not None:
            if isinstance(rule_config, str):
                try:
                    json.loads(rule_config)
                except json.JSONDecodeError:
                    issues.append("rule_config is not valid JSON")
            elif not isinstance(rule_config, dict):
                issues.append("rule_config must be dict or JSON string")
        
        is_valid = len(issues) == 0
        return is_valid, issues
    
    def get_mapping_statistics(self, mapped_rules: List[Dict]) -> Dict[str, Any]:
        """Get statistics about mapped rules"""
        stats = {
            'total_rules': len(mapped_rules),
            'rule_types': {},
            'severities': {},
            'validation_status': {'valid': 0, 'invalid': 0},
            'source_files': set(),
            'columns_covered': set()
        }
        
        for rule in mapped_rules:
            # Count rule types
            rule_type = rule.get('rule_type', 'Unknown')
            stats['rule_types'][rule_type] = stats['rule_types'].get(rule_type, 0) + 1
            
            # Count severities
            severity = rule.get('severity', 'Unknown')
            stats['severities'][severity] = stats['severities'].get(severity, 0) + 1
            
            # Validation status
            is_valid, _ = self.validate_mapped_rule(rule)
            if is_valid:
                stats['validation_status']['valid'] += 1
            else:
                stats['validation_status']['invalid'] += 1
            
            # Track source files and columns
            if rule.get('source_file'):
                stats['source_files'].add(rule['source_file'])
            if rule.get('source_col'):
                stats['columns_covered'].add(rule['source_col'])
        
        # Convert sets to counts
        stats['unique_source_files'] = len(stats['source_files'])
        stats['unique_columns'] = len(stats['columns_covered'])
        del stats['source_files']
        del stats['columns_covered']
        
        return stats
