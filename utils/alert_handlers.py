import json
import smtplib
import requests
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ip<PERSON>
from email.mime.base import MIMEBase
from email import encoders
from datetime import datetime
from typing import List, Dict, Any
from abc import ABC, abstractmethod
import logging
import os
from jinja2 import Template

logger = logging.getLogger(__name__)

class AlertHandler(ABC):
    """Abstract base class for alert handlers"""
    
    @abstractmethod
    def send_alert(self, drifts: List[Dict]) -> bool:
        """Send alert for detected drifts"""
        pass
    
    def filter_by_severity(self, drifts: List[Dict], min_severity: str) -> List[Dict]:
        """Filter drifts by minimum severity"""
        severity_order = {'low': 1, 'medium': 2, 'high': 3, 'critical': 4}
        min_level = severity_order.get(min_severity, 1)
        
        return [
            drift for drift in drifts
            if severity_order.get(drift.get('severity', 'low'), 1) >= min_level
        ]

class EmailAlertHandler(AlertHandler):
    """Send email alerts for schema drifts"""
    
    def __init__(self, config: Dict):
        self.smtp_config = config['alerts']['channels']['email']['smtp']
        self.severity_threshold = config['alerts']['channels']['email'].get(
            'severity_threshold', 'high'
        )
        self.template = self._load_email_template()
    
    def _load_email_template(self) -> Template:
        """Load email template"""
        template_str = """
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; }
                .header { background-color: #f44336; color: white; padding: 20px; }
                .content { padding: 20px; }
                .drift-table { border-collapse: collapse; width: 100%; }
                .drift-table th, .drift-table td { 
                    border: 1px solid #ddd; 
                    padding: 8px; 
                    text-align: left; 
                }
                .drift-table th { background-color: #4CAF50; color: white; }
                .severity-critical { color: #d32f2f; font-weight: bold; }
                .severity-high { color: #f57c00; font-weight: bold; }
                .severity-medium { color: #fbc02d; }
                .severity-low { color: #388e3c; }
                .details { 
                    background-color: #f5f5f5; 
                    padding: 5px; 
                    font-family: monospace; 
                    font-size: 12px;
                }
                .summary { 
                    background-color: #e3f2fd; 
                    padding: 15px; 
                    margin: 10px 0; 
                    border-radius: 5px;
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Schema Drift Alert</h1>
                <p>{{ timestamp }}</p>
            </div>
            <div class="content">
                <div class="summary">
                    <h2>Summary</h2>
                    <ul>
                        <li>Total Drifts: {{ total_drifts }}</li>
                        <li>Critical: {{ critical_count }}</li>
                        <li>High: {{ high_count }}</li>
                        <li>Files Affected: {{ affected_files }}</li>
                    </ul>
                </div>
                
                <h2>Drift Details</h2>
                <table class="drift-table">
                    <tr>
                        <th>File</th>
                        <th>Drift Type</th>
                        <th>Severity</th>
                        <th>Details</th>
                        <th>Timestamp</th>
                    </tr>
                    {% for drift in drifts %}
                    <tr>
                        <td>{{ drift.file_path }}</td>
                        <td>{{ drift.drift_type }}</td>
                        <td class="severity-{{ drift.severity }}">{{ drift.severity }}</td>
                        <td><pre class="details">{{ drift.details | tojson(indent=2) }}</pre></td>
                        <td>{{ drift.timestamp }}</td>
                    </tr>
                    {% endfor %}
                </table>
                
                <p style="margin-top: 20px; color: #666;">
                    This is an automated alert from the Schema Drift Detection System.
                </p>
            </div>
        </body>
        </html>
        """
        return Template(template_str)
    
    def send_alert(self, drifts: List[Dict]) -> bool:
        """Send email alert"""
        try:
            # Filter by severity
            filtered_drifts = self.filter_by_severity(drifts, self.severity_threshold)
            
            if not filtered_drifts:
                logger.info("No drifts meet severity threshold for email alert")
                return True
            
            # Prepare email content
            context = {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'total_drifts': len(filtered_drifts),
                'critical_count': len([d for d in filtered_drifts if d['severity'] == 'critical']),
                'high_count': len([d for d in filtered_drifts if d['severity'] == 'high']),
                'affected_files': len(set(d['file_path'] for d in filtered_drifts)),
                'drifts': filtered_drifts
            }
            
            html_content = self.template.render(**context)
            
            # Create message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = f"Schema Drift Alert: {context['total_drifts']} issues detected"
            msg['From'] = self.smtp_config['from']
            msg['To'] = ', '.join(self.smtp_config['to'])
            
            # Attach HTML content
            msg.attach(MIMEText(html_content, 'html'))
            
            # Attach drift details as JSON
            drift_json = json.dumps(filtered_drifts, indent=2, default=str)
            attachment = MIMEBase('application', 'json')
            attachment.set_payload(drift_json.encode())
            encoders.encode_base64(attachment)
            attachment.add_header(
                'Content-Disposition',
                f'attachment; filename="drift_details_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json"'
            )
            msg.attach(attachment)
            
            # Send email
            with smtplib.SMTP(self.smtp_config['server'], self.smtp_config['port']) as server:
                if self.smtp_config.get('use_tls'):
                    server.starttls()
                
                # Handle environment variable passwords
                username = self.smtp_config['username']
                password = self.smtp_config['password']
                if password.startswith('${') and password.endswith('}'):
                    password = os.environ.get(password[2:-1], '')
                
                if username and password:
                    server.login(username, password)
                
                server.send_message(msg)
            
            logger.info(f"Email alert sent successfully to {self.smtp_config['to']}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email alert: {e}")
            return False

class SlackAlertHandler(AlertHandler):
    """Send Slack alerts for schema drifts"""
    
    def __init__(self, config: Dict):
        webhook_url = config['alerts']['channels']['slack']['webhook_url']
        
        # Handle environment variable
        if webhook_url.startswith('${') and webhook_url.endswith('}'):
            webhook_url = os.environ.get(webhook_url[2:-1], '')
        
        self.webhook_url = webhook_url
        self.channel = config['alerts']['channels']['slack'].get('channel', '#alerts')
        self.severity_threshold = config['alerts']['channels']['slack'].get(
            'severity_threshold', 'medium'
        )
    
    def send_alert(self, drifts: List[Dict]) -> bool:
        """Send Slack alert"""
        try:
            # Filter by severity
            filtered_drifts = self.filter_by_severity(drifts, self.severity_threshold)
            
            if not filtered_drifts:
                logger.info("No drifts meet severity threshold for Slack alert")
                return True
            
            # Count by severity
            severity_counts = {
                'critical': len([d for d in filtered_drifts if d['severity'] == 'critical']),
                'high': len([d for d in filtered_drifts if d['severity'] == 'high']),
                'medium': len([d for d in filtered_drifts if d['severity'] == 'medium']),
                'low': len([d for d in filtered_drifts if d['severity'] == 'low'])
            }
            
            # Build message blocks
            blocks = [
                {
                    "type": "header",
                    "text": {
                        "type": "plain_text",
                        "text": f"🚨 Schema Drift Alert",
                        "emoji": True
                    }
                },
                {
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": f"*{len(filtered_drifts)} schema drifts detected*\n"
                                f"Critical: {severity_counts['critical']} | "
                                f"High: {severity_counts['high']} | "
                                f"Medium: {severity_counts['medium']} | "
                                f"Low: {severity_counts['low']}"
                    }
                },
                {
                    "type": "divider"
                }
            ]
            
            # Add top 5 drifts
            for drift in filtered_drifts[:5]:
                severity_emoji = {
                    'critical': '🔴',
                    'high': '🟠',
                    'medium': '🟡',
                    'low': '🟢'
                }.get(drift['severity'], '⚪')
                
                blocks.append({
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": f"{severity_emoji} *{drift['drift_type']}* ({drift['severity']})\n"
                                f"File: `{drift['file_path']}`\n"
                                f"```{json.dumps(drift['details'], indent=2)}```"
                    }
                })
            
            if len(filtered_drifts) > 5:
                blocks.append({
                    "type": "context",
                    "elements": [
                        {
                            "type": "mrkdwn",
                            "text": f"_And {len(filtered_drifts) - 5} more drifts..._"
                        }
                    ]
                })
            
            # Send to Slack
            message = {
                "channel": self.channel,
                "blocks": blocks,
                "attachments": [
                    {
                        "color": "danger" if severity_counts['critical'] > 0 else "warning",
                        "footer": "Schema Drift Detection System",
                        "ts": int(datetime.now().timestamp())
                    }
                ]
            }
            
            response = requests.post(self.webhook_url, json=message)
            response.raise_for_status()
            
            logger.info(f"Slack alert sent successfully to {self.channel}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send Slack alert: {e}")
            return False

class PagerDutyAlertHandler(AlertHandler):
    """Send PagerDuty alerts for critical schema drifts"""
    
    def __init__(self, config: Dict):
        integration_key = config['alerts']['channels']['pagerduty']['integration_key']
        
        # Handle environment variable
        if integration_key.startswith('${') and integration_key.endswith('}'):
            integration_key = os.environ.get(integration_key[2:-1], '')
        
        self.integration_key = integration_key
        self.severity_threshold = config['alerts']['channels']['pagerduty'].get(
            'severity_threshold', 'critical'
        )
        self.api_url = "https://events.pagerduty.com/v2/enqueue"
    
    def send_alert(self, drifts: List[Dict]) -> bool:
        """Send PagerDuty alert"""
        try:
            # Filter by severity
            filtered_drifts = self.filter_by_severity(drifts, self.severity_threshold)
            
            if not filtered_drifts:
                logger.info("No drifts meet severity threshold for PagerDuty alert")
                return True
            
            # Create incident
            payload = {
                "routing_key": self.integration_key,
                "event_action": "trigger",
                "payload": {
                    "summary": f"Schema Drift Alert: {len(filtered_drifts)} critical issues",
                    "severity": "error",
                    "source": "schema-drift-detector",
                    "timestamp": datetime.now().isoformat(),
                    "custom_details": {
                        "drift_count": len(filtered_drifts),
                        "affected_files": list(set(d['file_path'] for d in filtered_drifts)),
                        "drift_types": list(set(d['drift_type'] for d in filtered_drifts)),
                        "drifts": filtered_drifts[:10]  # Limit details
                    }
                },
                "dedup_key": f"schema-drift-{datetime.now().strftime('%Y%m%d')}"
            }
            
            response = requests.post(self.api_url, json=payload)
            response.raise_for_status()
            
            logger.info("PagerDuty alert sent successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send PagerDuty alert: {e}")
            return False

class CompositeAlertHandler(AlertHandler):
    """Composite handler that sends alerts to multiple channels"""
    
    def __init__(self, config: Dict):
        self.handlers = []
        
        # Initialize enabled handlers
        if config['alerts']['channels']['email']['enabled']:
            self.handlers.append(EmailAlertHandler(config))
        
        if config['alerts']['channels']['slack']['enabled']:
            self.handlers.append(SlackAlertHandler(config))
        
        if config['alerts']['channels']['pagerduty']['enabled']:
            self.handlers.append(PagerDutyAlertHandler(config))
    
    def send_alert(self, drifts: List[Dict]) -> bool:
        """Send alerts to all configured channels"""
        success_count = 0
        
        for handler in self.handlers:
            try:
                if handler.send_alert(drifts):
                    success_count += 1
            except Exception as e:
                logger.error(f"Handler {handler.__class__.__name__} failed: {e}")
        
        return success_count > 0

def create_alert_handler(config: Dict) -> AlertHandler:
    """Factory function to create appropriate alert handler"""
    if config['alerts']['enabled']:
        return CompositeAlertHandler(config)
    else:
        logger.info("Alerts are disabled in configuration")
        return None