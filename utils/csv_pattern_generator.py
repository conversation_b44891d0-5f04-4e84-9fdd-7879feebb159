import re
import os
from pathlib import Path
from typing import List, Union, Optional, Dict, Set, Tuple
from collections import Counter
from dataclasses import dataclass

@dataclass
class NormalizationResult:
    """Result of filename normalization"""
    original: str
    normalized: str
    extracted_components: Dict[str, List[str]]
    removed_patterns: Dict[str, List[str]]
    confidence: float
    status: str
    message: str

class AdvancedCSVNormalizer:
    def __init__(self, 
                 replacement_word: str = "TEMPLATE",
                 output_format: str = "auto",
                 max_components: int = 4,
                 preserve_order: bool = True):
        """
        Advanced CSV Filename Normalizer
        
        Args:
            replacement_word (str): Word to replace temporal/metadata patterns
            output_format (str): Output format ('auto', 'business_function', 'hierarchical', 'minimal')
            max_components (int): Maximum components in normalized name
            preserve_order (bool): Whether to preserve original component order
        """
        self.replacement_word = replacement_word
        self.output_format = output_format
        self.max_components = max_components
        self.preserve_order = preserve_order
        
        # Comprehensive pattern definitions
        self._init_patterns()
        
        # Business intelligence patterns
        self._init_business_patterns()
    
    def _init_patterns(self):
        """Initialize all detection patterns"""
        
        # Temporal patterns (dates, timestamps)
        self.temporal_patterns = {
            'dates_8digit': [
                r'\b\d{8}\b',                           # DDMMYYYY, YYYYMMDD
                r'\b\d{4}\d{2}\d{2}\b',                 # YYYYMMDD
                r'\b\d{2}\d{2}\d{4}\b',                 # DDMMYYYY, MMDDYYYY
            ],
            'dates_6digit': [
                r'\b\d{6}\b',                           # DDMMYY, YYMMDD
                r'\b\d{2}\d{2}\d{2}\b',                 # DDMMYY
            ],
            'dates_separated': [
                r'\b\d{4}[-/._ ]\d{2}[-/._ ]\d{2}\b',   # YYYY-MM-DD variants
                r'\b\d{2}[-/._ ]\d{2}[-/._ ]\d{4}\b',   # DD-MM-YYYY variants
                r'\b\d{2}[-/._ ]\d{2}[-/._ ]\d{2}\b',   # DD-MM-YY variants
            ],
            'timestamps': [
                r'\b\d{6}\b(?=\d{0})',                  # HHMMSS
                r'\b\d{4}(?:AM|PM)\b',                  # 1230AM, 0945PM
                r'\b\d{2}:\d{2}:\d{2}\b',               # HH:MM:SS
                r'\b\d{2}:\d{2}\b',                     # HH:MM
            ],
            'datetime_combined': [
                r'\b\d{8}_?\d{6}\b',                    # YYYYMMDD_HHMMSS
                r'\b\d{14}\b',                          # YYYYMMDDHHMMSS
            ]
        }
        
        # Version and sequence patterns
        self.version_patterns = {
            'version_v': [
                r'\bv\d+(?:\.\d+)*\b',                  # v1, v2.1, v1.0.3
                r'\bV\d+(?:\.\d+)*\b',                  # V1, V2.1
                r'\bver\d+(?:\.\d+)*\b',                # ver1, ver2.1
                r'\bversion\d+(?:\.\d+)*\b',            # version1
            ],
            'sequence_numbers': [
                r'\b\d{3,4}$',                          # 001, 1234 at end
                r'\bpart\d+\b',                         # part1, part2
                r'\bbatch\d+\b',                        # batch1, batch2
                r'\bseq\d+\b',                          # seq1, seq2
                r'\b#\d+\b',                            # #1, #2
                r'\b\(\d+\)\b',                         # (1), (2)
            ],
            'revisions': [
                r'\brev\d+\b',                          # rev1, rev2
                r'\br\d+\b',                            # r1, r2
                r'\bdraft\d*\b',                        # draft, draft1
            ]
        }
        
        # Status and metadata patterns
        self.status_patterns = {
            'status_final': [
                r'\bfinal\b', r'\bcomplete\b', r'\bdone\b', r'\bready\b',
                r'\bapproved\b', r'\bsigned\b', r'\bconfirmed\b'
            ],
            'status_temp': [
                r'\btemp\b', r'\btmp\b', r'\btemporary\b', r'\btest\b',
                r'\bdraft\b', r'\bwip\b', r'\bwork\b', r'\bpending\b'
            ],
            'status_backup': [
                r'\bbackup\b', r'\bbak\b', r'\bcopy\b', r'\barchive\b',
                r'\bold\b', r'\bprev\b', r'\bprevious\b'
            ],
            'frequency': [
                r'\bdaily\b', r'\bweekly\b', r'\bmonthly\b', r'\bquarterly\b',
                r'\bannual\b', r'\byearly\b', r'\bhourly\b'
            ]
        }
        
        # Business context patterns
        self.business_patterns = {
            'departments': [
                r'\bhr\b', r'\bfinance\b', r'\baccounting\b', r'\bsales\b',
                r'\bmarketing\b', r'\bit\b', r'\bops\b', r'\boperations\b',
                r'\blegal\b', r'\bcompliance\b', r'\baudit\b'
            ],
            'functions': [
                r'\breport\b', r'\banalysis\b', r'\bdata\b', r'\bexport\b',
                r'\bimport\b', r'\bsummary\b', r'\bdashboard\b', r'\bmetrics\b'
            ],
            'regions': [
                r'\bnorth\b', r'\bsouth\b', r'\beast\b', r'\bwest\b',
                r'\bapac\b', r'\bemea\b', r'\bamer\b', r'\beu\b', r'\bus\b',
                r'\bregion\b', r'\bzone\b', r'\barea\b'
            ]
        }
        
    def _init_business_patterns(self):
        """Initialize business intelligence for component classification"""
        
        # Common business prefixes and their priorities
        self.business_hierarchy = {
            'organization': ['corp', 'company', 'org', 'enterprise'],
            'division': ['div', 'business', 'bu', 'unit'],
            'department': ['dept', 'department', 'team', 'group'],
            'function': ['sales', 'marketing', 'finance', 'hr', 'it', 'ops'],
            'geography': ['global', 'region', 'country', 'state', 'city'],
            'product': ['product', 'service', 'offering', 'solution'],
            'process': ['process', 'workflow', 'procedure', 'operation']
        }
        
        # Component importance weights
        self.component_weights = {
            'organization': 10,
            'division': 9,
            'department': 8,
            'geography': 7,
            'function': 6,
            'product': 5,
            'process': 4,
            'generic': 1
        }
    
    def extract_temporal_patterns(self, filename: str) -> Tuple[str, Dict[str, List[str]]]:
        """Extract and remove temporal patterns from filename"""
        cleaned = filename
        extracted = {category: [] for category in self.temporal_patterns.keys()}
        
        for category, patterns in self.temporal_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, cleaned, re.IGNORECASE)
                if matches:
                    extracted[category].extend(matches)
                    cleaned = re.sub(pattern, '', cleaned, flags=re.IGNORECASE)
        
        return cleaned, extracted
    
    def extract_version_patterns(self, filename: str) -> Tuple[str, Dict[str, List[str]]]:
        """Extract and remove version/sequence patterns"""
        cleaned = filename
        extracted = {category: [] for category in self.version_patterns.keys()}
        
        for category, patterns in self.version_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, cleaned, re.IGNORECASE)
                if matches:
                    extracted[category].extend(matches)
                    cleaned = re.sub(pattern, '', cleaned, flags=re.IGNORECASE)
        
        return cleaned, extracted
    
    def extract_status_patterns(self, filename: str) -> Tuple[str, Dict[str, List[str]]]:
        """Extract and remove status/metadata patterns"""
        cleaned = filename
        extracted = {category: [] for category in self.status_patterns.keys()}
        
        for category, patterns in self.status_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, cleaned, re.IGNORECASE)
                if matches:
                    extracted[category].extend(matches)
                    cleaned = re.sub(pattern, '', cleaned, flags=re.IGNORECASE)
        
        return cleaned, extracted
    
    def classify_components(self, components: List[str]) -> Dict[str, List[str]]:
        """Classify remaining components by business context"""
        classified = {category: [] for category in self.business_hierarchy.keys()}
        classified['unclassified'] = []
        
        for component in components:
            component_lower = component.lower()
            classified_flag = False
            
            for category, keywords in self.business_hierarchy.items():
                if any(keyword in component_lower for keyword in keywords):
                    classified[category].append(component)
                    classified_flag = True
                    break
            
            if not classified_flag:
                classified['unclassified'].append(component)
        
        return classified
    
    def clean_and_split_components(self, text: str) -> List[str]:
        """Clean text and split into meaningful components"""
        # Remove file extension
        if text.lower().endswith('.csv'):
            text = text[:-4]
        
        # Clean up separators and split
        text = re.sub(r'[_\-\.\/\\]+', '_', text)
        text = re.sub(r'^_+|_+$', '', text)  # Remove leading/trailing underscores
        text = re.sub(r'_+', '_', text)      # Collapse multiple underscores
        
        components = [comp.strip() for comp in text.split('_') if comp.strip()]
        
        # Filter out very short or numeric-only components
        filtered_components = []
        for comp in components:
            if len(comp) > 1 and not comp.isdigit():
                filtered_components.append(comp)
        
        return filtered_components
    
    def remove_duplicates_preserve_order(self, components: List[str]) -> List[str]:
        """Remove duplicates while preserving order"""
        seen = set()
        result = []
        for comp in components:
            comp_lower = comp.lower()
            if comp_lower not in seen:
                seen.add(comp_lower)
                result.append(comp)
        return result
    
    def select_best_components(self, classified: Dict[str, List[str]]) -> List[str]:
        """Select the best components based on business hierarchy and importance"""
        selected = []
        
        # Process by hierarchy priority
        for category in ['organization', 'division', 'department', 'geography', 
                        'function', 'product', 'process']:
            if classified[category]:
                # Take the first (usually most important) component from each category
                selected.extend(classified[category][:1])
        
        # Add unclassified components if we need more
        if len(selected) < self.max_components:
            remaining_slots = self.max_components - len(selected)
            selected.extend(classified['unclassified'][:remaining_slots])
        
        # Ensure we don't exceed max components
        return selected[:self.max_components]
    
    def generate_normalized_filename(self, components: List[str]) -> str:
        """Generate the final normalized filename"""
        if not components:
            return f"{self.replacement_word}.csv"
        
        # Remove duplicates while preserving order
        unique_components = self.remove_duplicates_preserve_order(components)
        
        # Apply output format
        if self.output_format == "minimal":
            # Keep only the most important component
            final_components = unique_components[:1] + [self.replacement_word]
        elif self.output_format == "business_function":
            # Ensure business context + function + template
            if len(unique_components) >= 2:
                final_components = unique_components[:2] + [self.replacement_word]
            else:
                final_components = unique_components + [self.replacement_word]
        else:  # auto or hierarchical
            # Use all selected components
            final_components = unique_components + [self.replacement_word]
        
        # Create filename
        filename = '_'.join(final_components).upper()
        return f"{filename}.csv"
    
    def calculate_confidence(self, 
                           original_components: int, 
                           final_components: int,
                           patterns_found: int) -> float:
        """Calculate confidence score for the normalization"""
        if original_components == 0:
            return 0.0
        
        # Base confidence from component preservation
        component_ratio = min(final_components / original_components, 1.0)
        
        # Bonus for pattern detection
        pattern_bonus = min(patterns_found * 0.1, 0.3)
        
        # Penalty for too much reduction
        if original_components > 5 and final_components < 2:
            reduction_penalty = 0.2
        else:
            reduction_penalty = 0.0
        
        confidence = (component_ratio * 0.7) + pattern_bonus - reduction_penalty
        return max(0.0, min(1.0, confidence))
    
    def normalize_filename(self, filename: str) -> NormalizationResult:
        """Main normalization function"""
        if not filename.lower().endswith('.csv'):
            return NormalizationResult(
                original=filename,
                normalized=filename,
                extracted_components={},
                removed_patterns={},
                confidence=0.0,
                status='skipped',
                message='Not a CSV file'
            )
        
        # Track what we extract/remove
        extracted_components = {}
        removed_patterns = {}
        
        # Start with the filename
        working_text = filename
        
        # Extract temporal patterns
        working_text, temporal_extracted = self.extract_temporal_patterns(working_text)
        removed_patterns.update(temporal_extracted)
        
        # Extract version patterns
        working_text, version_extracted = self.extract_version_patterns(working_text)
        removed_patterns.update(version_extracted)
        
        # Extract status patterns
        working_text, status_extracted = self.extract_status_patterns(working_text)
        removed_patterns.update(status_extracted)
        
        # Clean and split remaining components
        components = self.clean_and_split_components(working_text)
        original_component_count = len(components)
        
        # Classify components
        classified = self.classify_components(components)
        extracted_components = classified
        
        # Select best components
        selected_components = self.select_best_components(classified)
        
        # Generate normalized filename
        normalized = self.generate_normalized_filename(selected_components)
        
        # Calculate confidence
        total_patterns_found = sum(len(patterns) for patterns in removed_patterns.values())
        confidence = self.calculate_confidence(
            original_component_count, 
            len(selected_components), 
            total_patterns_found
        )
        
        # Determine status
        if normalized == filename:
            status = 'no_change'
            message = 'No patterns detected for normalization'
        elif confidence > 0.7:
            status = 'success'
            message = f'Successfully normalized with {confidence:.1%} confidence'
        elif confidence > 0.4:
            status = 'partial'
            message = f'Partial normalization with {confidence:.1%} confidence'
        else:
            status = 'low_confidence'
            message = f'Low confidence normalization ({confidence:.1%})'
        
        return NormalizationResult(
            original=filename,
            normalized=normalized,
            extracted_components=extracted_components,
            removed_patterns=removed_patterns,
            confidence=confidence,
            status=status,
            message=message
        )
    
    def process_single_file(self, filename: str) -> NormalizationResult:
        """Process a single filename"""
        return self.normalize_filename(filename)
    
    def process_file_list(self, filenames: List[str]) -> List[NormalizationResult]:
        """Process a list of filenames"""
        return [self.normalize_filename(filename) for filename in filenames]
    
    def process_directory(self, directory_path: str, recursive: bool = False) -> List[NormalizationResult]:
        """Process all CSV files in a directory"""
        results = []
        path = Path(directory_path)
        
        if not path.exists():
            return [NormalizationResult(
                original=directory_path,
                normalized='',
                extracted_components={},
                removed_patterns={},
                confidence=0.0,
                status='error',
                message=f'Directory does not exist: {directory_path}'
            )]
        
        if not path.is_dir():
            return [NormalizationResult(
                original=directory_path,
                normalized='',
                extracted_components={},
                removed_patterns={},
                confidence=0.0,
                status='error',
                message=f'Path is not a directory: {directory_path}'
            )]
        
        # Get CSV files
        if recursive:
            csv_files = list(path.rglob('*.csv'))
        else:
            csv_files = list(path.glob('*.csv'))
        
        for csv_file in csv_files:
            result = self.normalize_filename(csv_file.name)
            results.append(result)
        
        return results
    
    def print_detailed_results(self, results: Union[NormalizationResult, List[NormalizationResult]]):
        """Print detailed results with analysis"""
        if isinstance(results, NormalizationResult):
            results = [results]
        
        print(f"\n{'='*100}")
        print(f"ADVANCED CSV FILENAME NORMALIZATION RESULTS")
        print(f"Replacement word: '{self.replacement_word}' | Format: {self.output_format} | Max components: {self.max_components}")
        print(f"{'='*100}")
        
        for i, result in enumerate(results, 1):
            print(f"\n{i}. ORIGINAL: {result.original}")
            print(f"   NORMALIZED: {result.normalized}")
            print(f"   STATUS: {result.status.upper()} | CONFIDENCE: {result.confidence:.1%}")
            print(f"   MESSAGE: {result.message}")
            
            # Show extracted components
            if any(result.extracted_components.values()):
                print(f"   BUSINESS COMPONENTS:")
                for category, components in result.extracted_components.items():
                    if components:
                        print(f"     {category.title()}: {', '.join(components)}")
            
            # Show removed patterns
            removed_items = []
            for category, patterns in result.removed_patterns.items():
                if patterns:
                    removed_items.extend(patterns)
            
            if removed_items:
                print(f"   REMOVED PATTERNS: {', '.join(removed_items)}")
        
        # Summary statistics
        total = len(results)
        success = len([r for r in results if r.status == 'success'])
        partial = len([r for r in results if r.status == 'partial'])
        low_conf = len([r for r in results if r.status == 'low_confidence'])
        no_change = len([r for r in results if r.status == 'no_change'])
        skipped = len([r for r in results if r.status == 'skipped'])
        errors = len([r for r in results if r.status == 'error'])
        
        avg_confidence = sum(r.confidence for r in results) / total if total > 0 else 0
        
        print(f"\n{'='*100}")
        print(f"NORMALIZATION SUMMARY")
        print(f"Total files: {total} | Average confidence: {avg_confidence:.1%}")
        print(f"✓ Success: {success} | ◐ Partial: {partial} | ⚠ Low confidence: {low_conf}")
        print(f"○ No change: {no_change} | - Skipped: {skipped} | ✗ Errors: {errors}")
        print(f"{'='*100}")


# Convenience functions
def normalize_csv_filename(filename: str, 
                          replacement_word: str = "TEMPLATE",
                          output_format: str = "auto",
                          max_components: int = 4) -> str:
    """Quick function to normalize a single filename"""
    normalizer = AdvancedCSVNormalizer(replacement_word, output_format, max_components)
    result = normalizer.normalize_filename(filename)
    return result.normalized

def process_csv_files(input_data: Union[str, List[str]], 
                     replacement_word: str = "TEMPLATE",
                     output_format: str = "auto",
                     max_components: int = 4,
                     recursive: bool = False,
                     print_output: bool = False) -> Union[NormalizationResult, List[NormalizationResult]]:
    """Process CSV files with advanced normalization"""
    normalizer = AdvancedCSVNormalizer(replacement_word, output_format, max_components)
    
    if isinstance(input_data, str):
        if os.path.isdir(input_data):
            results = normalizer.process_directory(input_data, recursive)
        else:
            results = normalizer.process_single_file(input_data)
    elif isinstance(input_data, list):
        results = normalizer.process_file_list(input_data)
    else:
        raise ValueError("input_data must be a string (filename/directory) or list of filenames")
    
    if print_output:
        normalizer.print_detailed_results(results)
    
    return results


# Comprehensive examples
if __name__ == "__main__":
    # Test with various complex filename patterns
    test_files = [
        # Original example
        "GP_MARKET_25011984.csv",
        
        # Complex business scenarios
        "SALES_NORTH_REGION_2024-03-15_FINAL_v1.csv",
        "HR_PAYROLL_WEEKLY_********_TEMP_BACKUP.csv",
        "FINANCE_ACCOUNTING_QUARTERLY_Q4_2024_APPROVED_rev3.csv",
        "IT_OPERATIONS_DAILY_MONITORING_********143052_DRAFT.csv",
        "MARKETING_CAMPAIGN_EMEA_ANALYSIS_2024.01.20_COMPLETE.csv",
        
        # Edge cases
        "A_B_C_D_E_F_G_********.csv",  # Too many components
        "REPORT_REPORT_SALES_REPORT_2024.csv",  # Duplicates
        "DATA_EXPORT_001_BATCH2_PART1_FINAL_v2.1_********.csv",  # Everything
        "SIMPLE.csv",  # Minimal
        "BACKUP_OLD_ARCHIVE_TEMP_PREVIOUS_20231225.csv",  # Multiple status
        
        # Different separators and formats
        "EXPORT-DATA-2024.03.15-FINAL.csv",
        "IMPORT/DATA/********/v1.csv",
        "ANALYSIS.MARKET.********.TEMP.csv",
        
        # Timestamps and detailed versions
        "BACKUP_********_143052_v1.2.3_FINAL.csv",
        "LOG_FILE_2024-03-15T14:30:52Z_PART001.csv"
    ]
    
    print("="*100)
    print("ADVANCED CSV FILENAME NORMALIZATION - COMPREHENSIVE TEST")
    print("="*100)
    
    # Test with different configurations
    configurations = [
        {"replacement_word": "TEMPLATE", "output_format": "auto", "max_components": 4},
        {"replacement_word": "PATTERN", "output_format": "business_function", "max_components": 3},
        {"replacement_word": "NORMALIZED", "output_format": "minimal", "max_components": 2}
    ]
    
    for i, config in enumerate(configurations):
        print(f"\n\nCONFIGURATION {i+1}: {config}")
        print("-" * 80)
        
        # Test a subset for each configuration
        sample_files = test_files[:5] if i == 0 else test_files[5:8]
        results = process_csv_files(sample_files, **config, print_output=True)
