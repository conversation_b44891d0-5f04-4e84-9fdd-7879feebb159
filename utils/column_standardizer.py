"""
Column Standardizer Utility

Utility to standardize CSV column headers using a mapping DataFrame.
Handles various encodings, delimiters, and large files efficiently.

"""

import os
import csv
import pandas as pd
import chardet
import re
from typing import Dict, Tuple, Optional, Union, List
from pathlib import Path
import logging
from tqdm import tqdm
import warnings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ColumnStandardizer:
    """
    A class to standardize CSV column headers using mapping data.
    Handles encoding detection, delimiter detection, and large file processing.
    """
    
    def __init__(self):
        self.detected_encoding = None
        self.detected_delimiter = None
        self.detected_quotechar = None
        self.detected_format = {}
        
    def detect_encoding(self, file_path: str, sample_size: int = 100000) -> str:
        """
        Detect file encoding using chardet library.
        
        Args:
            file_path (str): Path to the CSV file
            sample_size (int): Number of bytes to sample for detection
            
        Returns:
            str: Detected encoding
        """
        try:
            with open(file_path, 'rb') as file:
                raw_data = file.read(sample_size)
                result = chardet.detect(raw_data)
                encoding = result['encoding']
                confidence = result['confidence']
                
                logger.info(f"Detected encoding: {encoding} (confidence: {confidence:.2f})")
                
                # Fallback encodings if confidence is low
                if confidence < 0.7:
                    fallback_encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
                    for fallback in fallback_encodings:
                        try:
                            with open(file_path, 'r', encoding=fallback) as test_file:
                                test_file.read(1000)  # Try to read a small sample
                            logger.info(f"Using fallback encoding: {fallback}")
                            encoding = fallback
                            break
                        except UnicodeDecodeError:
                            continue
                
                self.detected_encoding = encoding
                return encoding
                
        except Exception as e:
            logger.warning(f"Encoding detection failed: {e}. Using utf-8 as fallback.")
            self.detected_encoding = 'utf-8'
            return 'utf-8'
    
    def sample_file(self, file_path: str, encoding: str, num_lines: int = 10) -> List[str]:
        """
        Sample first few lines of the file for format detection.
        
        Args:
            file_path (str): Path to the CSV file
            encoding (str): File encoding
            num_lines (int): Number of lines to sample
            
        Returns:
            List[str]: Sampled lines
        """
        try:
            with open(file_path, 'r', encoding=encoding) as file:
                lines = []
                for i, line in enumerate(file):
                    if i >= num_lines:
                        break
                    lines.append(line.rstrip('\n\r'))
                return lines
        except Exception as e:
            logger.error(f"Failed to sample file: {e}")
            raise
    
    def intelligent_delimiter_detection(self, sample_data: List[str]) -> str:
        """
        Intelligently detect delimiter by analyzing patterns and characteristics.
        
        Args:
            sample_data (List[str]): Sample lines from the file
            
        Returns:
            str: Detected delimiter
        """
        if not sample_data:
            return ','
        
        # Step 1: Find all potential delimiter characters
        potential_delimiters = set()
        
        # Analyze each line to find consistent separators
        for line in sample_data[:5]:  # Use first 5 lines
            # Find characters that could be delimiters
            # Exclude alphanumeric, common punctuation in data, and whitespace at word boundaries
            for char in line:
                if char in ',.;:|!@#$%^&*()+=[]{}\\/-_~`<>?':
                    # Additional filtering: check if character appears multiple times
                    if line.count(char) > 1:
                        potential_delimiters.add(char)
        
        # Step 2: Score each potential delimiter
        delimiter_scores = {}
        
        for delimiter in potential_delimiters:
            scores = []
            field_counts = []
            
            for line in sample_data[:7]:  # Check more lines for consistency
                if not line.strip():
                    continue
                    
                # Count fields when split by this delimiter
                fields = line.split(delimiter)
                field_count = len(fields)
                field_counts.append(field_count)
                
                # Scoring criteria:
                score = 0
                
                # 1. Consistency: prefer delimiters that create consistent field counts
                if len(field_counts) > 1:
                    consistency = 1.0 - (max(field_counts) - min(field_counts)) / max(field_counts)
                    score += consistency * 40
                
                # 2. Field count: prefer reasonable number of fields (2-50)
                avg_fields = sum(field_counts) / len(field_counts)
                if 2 <= avg_fields <= 50:
                    score += min(avg_fields * 2, 20)  # Cap at 20 points
                elif avg_fields > 50:
                    score -= 10  # Penalize too many fields
                
                # 3. Field content quality: check if fields look reasonable
                non_empty_fields = [f.strip() for f in fields if f.strip()]
                if len(non_empty_fields) >= 2:
                    score += 10
                
                # 4. Balanced distribution: prefer delimiters that create balanced field lengths
                field_lengths = [len(f.strip()) for f in fields if f.strip()]
                if field_lengths:
                    length_variance = max(field_lengths) - min(field_lengths)
                    if length_variance < 100:  # Reasonable variance
                        score += 5
                
                scores.append(score)
            
            # Calculate final score for this delimiter
            if scores:
                avg_score = sum(scores) / len(scores)
                
                # Bonus for consistency across lines
                if len(set(field_counts)) == 1:  # All lines have same field count
                    avg_score += 20
                elif len(set(field_counts)) <= 2:  # At most 2 different field counts
                    avg_score += 10
                
                delimiter_scores[delimiter] = avg_score
        
        # Step 3: Apply heuristics and common patterns
        
        # Common delimiter preferences (bonus points)
        common_delimiters = {',': 5, ';': 4, '\t': 4, '|': 3, ':': 2}
        for delim, bonus in common_delimiters.items():
            if delim in delimiter_scores:
                delimiter_scores[delim] += bonus
        
        # Step 4: Validate top candidates using csv.Sniffer
        if delimiter_scores:
            # Sort by score
            sorted_delimiters = sorted(delimiter_scores.items(), key=lambda x: x[1], reverse=True)
            
            # Try top 3 candidates with csv.Sniffer
            sample_text = '\n'.join(sample_data[:5])
            sniffer = csv.Sniffer()
            
            for delimiter, score in sorted_delimiters[:3]:
                try:
                    # Test if this delimiter works with csv.Sniffer
                    dialect = sniffer.sniff(sample_text, delimiters=delimiter)
                    if dialect.delimiter == delimiter:
                        logger.info(f"Intelligent detection found delimiter: '{delimiter}' (score: {score:.1f})")
                        return delimiter
                except csv.Error:
                    continue
            
            # If csv.Sniffer fails, return highest scored delimiter
            best_delimiter = sorted_delimiters[0][0]
            logger.info(f"Intelligent detection selected: '{best_delimiter}' (score: {sorted_delimiters[0][1]:.1f})")
            return best_delimiter
        
        # Step 5: Advanced pattern analysis if no clear winner
        return self.advanced_pattern_analysis(sample_data)
    
    def advanced_pattern_analysis(self, sample_data: List[str]) -> str:
        """
        Advanced pattern analysis for edge cases.
        
        Args:
            sample_data (List[str]): Sample lines from the file
            
        Returns:
            str: Detected delimiter
        """
        # Look for quoted fields pattern
        quote_patterns = ['"', "'"]
        for quote in quote_patterns:
            for line in sample_data[:3]:
                if quote in line:
                    # Find what separates quoted fields
                    import re
                    # Pattern: quoted_field + delimiter + quoted_field
                    pattern = f'{quote}[^{quote}]*{quote}([^{quote}]+){quote}[^{quote}]*{quote}'
                    matches = re.findall(pattern, line)
                    if matches:
                        for sep in matches:
                            sep = sep.strip()
                            if len(sep) == 1 and sep in ',.;|\t':
                                logger.info(f"Advanced analysis found delimiter: '{sep}' (quoted pattern)")
                                return sep
        
        # Look for numeric patterns (e.g., coordinate data, measurements)
        numeric_line = None
        for line in sample_data:
            if re.search(r'\d+[.,;|\t]\d+', line):
                numeric_line = line
                break
        
        if numeric_line:
            # Find separators between numbers
            separators = re.findall(r'\d+([^0-9\s]+)\d+', numeric_line)
            if separators:
                separator_counts = {}
                for sep in separators:
                    if len(sep) == 1:
                        separator_counts[sep] = separator_counts.get(sep, 0) + 1
                
                if separator_counts:
                    best_sep = max(separator_counts, key=separator_counts.get)
                    logger.info(f"Advanced analysis found delimiter: '{best_sep}' (numeric pattern)")
                    return best_sep
        
        # Final fallback: analyze character frequency and position patterns
        char_frequencies = {}
        position_consistency = {}
        
        for line in sample_data[:5]:
            for i, char in enumerate(line):
                if char in ',.;|\t:-_':
                    char_frequencies[char] = char_frequencies.get(char, 0) + 1
                    
                    # Track position patterns
                    if char not in position_consistency:
                        position_consistency[char] = []
                    position_consistency[char].append(i)
        
        # Score by frequency and position consistency
        final_scores = {}
        for char, freq in char_frequencies.items():
            if freq >= 2:  # Must appear at least twice
                score = freq
                
                # Bonus for consistent positioning
                positions = position_consistency[char]
                if len(positions) > 1:
                    pos_diffs = [positions[i+1] - positions[i] for i in range(len(positions)-1)]
                    if len(set(pos_diffs)) <= 2:  # Consistent spacing
                        score += 10
                
                final_scores[char] = score
        
        if final_scores:
            best_char = max(final_scores, key=final_scores.get)
            logger.info(f"Advanced analysis found delimiter: '{best_char}' (frequency pattern)")
            return best_char
        
        # Ultimate fallback
        logger.warning("Could not detect delimiter, using comma as default")
        return ','

    def detect_csv_format(self, file_path: str, encoding: str, sample_lines: int = 15) -> Dict:
        """
        Detect CSV format parameters (delimiter, quote character, etc.) using intelligent detection.
        
        Args:
            file_path (str): Path to the CSV file
            encoding (str): File encoding
            sample_lines (int): Number of lines to analyze
            
        Returns:
            Dict: Detected format parameters
        """
        try:
            # Sample lines for detection
            sample_data = self.sample_file(file_path, encoding, sample_lines)
            
            if not sample_data:
                raise ValueError("No data found in file")
            
            # Use intelligent delimiter detection
            delimiter = self.intelligent_delimiter_detection(sample_data)
            
            # Now detect other format parameters using the detected delimiter
            sample_text = '\n'.join(sample_data[:7])
            sniffer = csv.Sniffer()
            
            # Detect quote character and other parameters
            try:
                dialect = sniffer.sniff(sample_text, delimiters=delimiter)
                quotechar = dialect.quotechar
                quoting = dialect.quoting
                
                # Verify the dialect actually works
                test_reader = csv.reader([sample_data[0]], dialect=dialect)
                next(test_reader)  # Try to read first line
                
            except (csv.Error, StopIteration):
                # Fallback quote detection
                quotechar = '"'
                quoting = csv.QUOTE_MINIMAL
                
                # Smart quote character detection
                for line in sample_data[:3]:
                    if line.count('"') >= 2:
                        quotechar = '"'
                        break
                    elif line.count("'") >= 2:
                        quotechar = "'"
                        break
            
            # Detect if file has header using intelligent analysis
            has_header = self.intelligent_header_detection(sample_data, delimiter)
            
            format_params = {
                'delimiter': delimiter,
                'quotechar': quotechar,
                'quoting': quoting,
                'has_header': has_header,
                'encoding': encoding
            }
            
            self.detected_format = format_params
            self.detected_delimiter = delimiter
            self.detected_quotechar = quotechar
            
            logger.info(f"Detected CSV format: delimiter='{delimiter}', quotechar='{quotechar}', header={has_header}")
            
            return format_params
            
        except Exception as e:
            logger.warning(f"CSV format detection failed: {e}. Using default parameters.")
            default_format = {
                'delimiter': ',',
                'quotechar': '"',
                'quoting': csv.QUOTE_MINIMAL,
                'has_header': True,
                'encoding': encoding
            }
            self.detected_format = default_format
            return default_format
    
    def intelligent_header_detection(self, sample_data: List[str], delimiter: str) -> bool:
        """
        Intelligently detect if the first row is a header.
        
        Args:
            sample_data (List[str]): Sample lines from the file
            delimiter (str): Detected delimiter
            
        Returns:
            bool: True if first row is likely a header
        """
        if len(sample_data) < 2:
            return True  # Default assumption
        
        try:
            first_row = sample_data[0].split(delimiter)
            second_row = sample_data[1].split(delimiter) if len(sample_data) > 1 else []
            
            # Score for header likelihood
            header_score = 0
            
            # 1. Check if first row has fewer numeric values
            first_row_numeric = sum(1 for field in first_row if self.is_numeric(field.strip()))
            if len(sample_data) > 1:
                second_row_numeric = sum(1 for field in second_row if self.is_numeric(field.strip()))
                if first_row_numeric < second_row_numeric:
                    header_score += 20
            
            # 2. Check for common header patterns
            header_indicators = ['id', 'name', 'date', 'time', 'value', 'count', 'total', 'amount']
            for field in first_row:
                field_lower = field.strip().lower()
                if any(indicator in field_lower for indicator in header_indicators):
                    header_score += 10
                    
                # Check for underscore/camelCase patterns common in headers
                if '_' in field or (field.strip() and field.strip()[0].isupper()):
                    header_score += 5
            
            # 3. Check if all fields in first row are strings (no pure numbers)
            if first_row_numeric == 0 and len(first_row) > 1:
                header_score += 15
            
            # 4. Check length consistency
            if len(first_row) == len(second_row):
                header_score += 10
            
            return header_score >= 20
            
        except Exception:
            return True  # Default to having header
    
    def is_numeric(self, value: str) -> bool:
        """Check if a string represents a numeric value."""
        try:
            float(value.replace(',', ''))  # Handle comma thousands separator
            return True
        except ValueError:
            return False
    
    def estimate_file_size(self, file_path: str) -> Tuple[int, bool]:
        """
        Estimate if file needs chunked processing.
        
        Args:
            file_path (str): Path to the CSV file
            
        Returns:
            Tuple[int, bool]: (file_size_mb, needs_chunking)
        """
        try:
            file_size = os.path.getsize(file_path)
            file_size_mb = file_size / (1024 * 1024)  # Convert to MB
            
            # Consider chunking for files > 100MB
            needs_chunking = file_size_mb > 100
            
            logger.info(f"File size: {file_size_mb:.2f} MB, Chunking needed: {needs_chunking}")
            
            return file_size_mb, needs_chunking
            
        except Exception as e:
            logger.warning(f"Could not estimate file size: {e}")
            return 0, False
    
    def create_mapping_dict(self, mapping_df: pd.DataFrame) -> Dict[str, str]:
        """
        Create mapping dictionary from DataFrame.
        
        Args:
            mapping_df (pd.DataFrame): DataFrame with 'source_column' and 'target_column'
            
        Returns:
            Dict[str, str]: Mapping dictionary
        """
        if not isinstance(mapping_df, pd.DataFrame):
            raise ValueError("mapping_df must be a pandas DataFrame")
        
        required_columns = ['source_column', 'target_column']
        if not all(col in mapping_df.columns for col in required_columns):
            raise ValueError(f"mapping_df must contain columns: {required_columns}")
        
        # Remove any rows with NaN values
        clean_mapping = mapping_df.dropna(subset=required_columns)
        
        # Create mapping dictionary
        mapping_dict = dict(zip(clean_mapping['source_column'], clean_mapping['target_column']))
        
        # Check for duplicate source columns
        if len(mapping_dict) != len(clean_mapping):
            duplicates = clean_mapping['source_column'].duplicated()
            duplicate_sources = clean_mapping[duplicates]['source_column'].tolist()
            logger.warning(f"Duplicate source columns found: {duplicate_sources}")
        
        logger.info(f"Created mapping for {len(mapping_dict)} columns")
        
        return mapping_dict
    
    def preview_changes(self, csv_path: str, mapping_dict: Dict[str, str], 
                       format_params: Dict, num_rows: int = 5) -> pd.DataFrame:
        """
        Preview the changes that will be made to the CSV.
        
        Args:
            csv_path (str): Path to the CSV file
            mapping_dict (Dict[str, str]): Column mapping dictionary
            format_params (Dict): CSV format parameters
            num_rows (int): Number of rows to preview
            
        Returns:
            pd.DataFrame: Preview of the transformed data
        """
        try:
            # Read a small sample
            sample_df = pd.read_csv(
                csv_path,
                nrows=num_rows,
                encoding=format_params['encoding'],
                delimiter=format_params['delimiter'],
                quotechar=format_params['quotechar']
            )
            
            # Show original columns
            original_columns = list(sample_df.columns)
            logger.info(f"Original columns: {original_columns}")
            
            # Show which columns will be mapped
            mapped_columns = []
            unmapped_columns = []
            
            for col in original_columns:
                if col in mapping_dict:
                    mapped_columns.append(f"{col} -> {mapping_dict[col]}")
                else:
                    unmapped_columns.append(col)
            
            if mapped_columns:
                logger.info(f"Columns to be mapped: {mapped_columns}")
            if unmapped_columns:
                logger.info(f"Columns to remain unchanged: {unmapped_columns}")
            
            # Apply mapping to preview
            renamed_df = sample_df.rename(columns=mapping_dict)
            
            return renamed_df
            
        except Exception as e:
            logger.error(f"Preview failed: {e}")
            raise
    
    def process_chunks(self, csv_path: str, mapping_dict: Dict[str, str], 
                      format_params: Dict, output_path: str, chunk_size: int) -> bool:
        """
        Process large CSV file in chunks.
        
        Args:
            csv_path (str): Path to input CSV file
            mapping_dict (Dict[str, str]): Column mapping dictionary
            format_params (Dict): CSV format parameters
            output_path (str): Path to output CSV file
            chunk_size (int): Number of rows per chunk
            
        Returns:
            bool: Success status
        """
        try:
            # Initialize progress tracking
            total_rows = sum(1 for _ in open(csv_path, 'r', encoding=format_params['encoding'])) - 1
            progress_bar = tqdm(total=total_rows, desc="Processing chunks", unit="rows")
            
            first_chunk = True
            
            # Process file in chunks
            chunk_reader = pd.read_csv(
                csv_path,
                chunksize=chunk_size,
                encoding=format_params['encoding'],
                delimiter=format_params['delimiter'],
                quotechar=format_params['quotechar']
            )
            
            for chunk_num, chunk in enumerate(chunk_reader):
                # Apply column mapping
                renamed_chunk = chunk.rename(columns=mapping_dict)
                
                # Write chunk to output file
                mode = 'w' if first_chunk else 'a'
                header = first_chunk
                
                renamed_chunk.to_csv(
                    output_path,
                    mode=mode,
                    header=header,
                    index=False,
                    encoding=format_params['encoding'],
                    sep=format_params['delimiter'],
                    quotechar=format_params['quotechar']
                )
                
                first_chunk = False
                progress_bar.update(len(chunk))
                
                logger.debug(f"Processed chunk {chunk_num + 1} with {len(chunk)} rows")
            
            progress_bar.close()
            logger.info(f"Successfully processed {total_rows} rows in chunks")
            
            return True
            
        except Exception as e:
            logger.error(f"Chunked processing failed: {e}")
            return False
    
    def standardize_columns(self, csv_path: str, mapping_df: pd.DataFrame,
                          output_path: Optional[str] = None,
                          delimiter: Optional[str] = None,
                          encoding: Optional[str] = None,
                          chunk_size: Optional[int] = None,
                          auto_detect_all: bool = True,
                          preview: bool = False,
                          preserve_original_format: bool = True,
                          sample_size: int = 100) -> Union[pd.DataFrame, bool]:
        """
        Main function to standardize CSV column headers.
        
        Args:
            csv_path (str): Path to the CSV file
            mapping_df (pd.DataFrame): DataFrame with 'source_column' and 'target_column'
            output_path (Optional[str]): Path to save the standardized CSV
            delimiter (Optional[str]): Manual delimiter specification
            encoding (Optional[str]): Manual encoding specification
            chunk_size (Optional[int]): Chunk size for large files
            auto_detect_all (bool): Auto-detect encoding and delimiter
            preview (bool): Show preview without processing
            preserve_original_format (bool): Keep original CSV formatting
            sample_size (int): Number of lines to sample for detection
            
        Returns:
            Union[pd.DataFrame, bool]: DataFrame if successful, or bool for chunked processing
        """
        try:
            # Validate inputs
            if not os.path.exists(csv_path):
                raise FileNotFoundError(f"CSV file not found: {csv_path}")
            
            # Create mapping dictionary
            mapping_dict = self.create_mapping_dict(mapping_df)
            
            if not mapping_dict:
                logger.warning("No valid mappings found")
                return None
            
            # Auto-detect encoding if not specified
            if auto_detect_all and encoding is None:
                encoding = self.detect_encoding(csv_path, sample_size * 1000)
            elif encoding is None:
                encoding = 'utf-8'
            
            # Auto-detect CSV format if not specified
            format_params = {}
            if auto_detect_all:
                format_params = self.detect_csv_format(csv_path, encoding, sample_size)
            else:
                format_params = {
                    'delimiter': delimiter or ',',
                    'quotechar': '"',
                    'quoting': csv.QUOTE_MINIMAL,
                    'has_header': True,
                    'encoding': encoding
                }
            
            # Override with manual specifications
            if delimiter is not None:
                format_params['delimiter'] = delimiter
            if encoding is not None:
                format_params['encoding'] = encoding
            
            # Preview mode
            if preview:
                preview_df = self.preview_changes(csv_path, mapping_dict, format_params, 5)
                print("\nPreview of standardized data:")
                print(preview_df.head())
                print(f"\nNew column names: {list(preview_df.columns)}")
                return preview_df
            
            # Estimate file size and determine processing method
            file_size_mb, needs_chunking = self.estimate_file_size(csv_path)
            
            # Determine chunk size
            if chunk_size is None:
                if needs_chunking:
                    chunk_size = max(1000, int(50000 / max(1, len(mapping_dict))))  # Adaptive chunk size
                else:
                    chunk_size = None
            
            # Process file
            if needs_chunking or chunk_size is not None:
                if output_path is None:
                    output_path = csv_path.replace('.csv', '_standardized.csv')
                
                logger.info(f"Processing large file in chunks of {chunk_size} rows")
                success = self.process_chunks(csv_path, mapping_dict, format_params, 
                                            output_path, chunk_size)
                
                if success:
                    logger.info(f"Standardized CSV saved to: {output_path}")
                    return True
                else:
                    return False
            
            else:
                # Process entire file at once
                logger.info("Processing entire file in memory")
                
                df = pd.read_csv(
                    csv_path,
                    encoding=format_params['encoding'],
                    delimiter=format_params['delimiter'],
                    quotechar=format_params['quotechar']
                )
                
                # Apply column mapping
                original_columns = list(df.columns)
                renamed_df = df.rename(columns=mapping_dict)
                
                # Log changes
                changed_columns = []
                for col in original_columns:
                    if col in mapping_dict:
                        changed_columns.append(f"{col} -> {mapping_dict[col]}")
                
                if changed_columns:
                    logger.info(f"Renamed columns: {changed_columns}")
                
                # Save to file if output path specified
                if output_path:
                    if preserve_original_format:
                        renamed_df.to_csv(
                            output_path,
                            index=False,
                            encoding=format_params['encoding'],
                            sep=format_params['delimiter'],
                            quotechar=format_params['quotechar']
                        )
                    else:
                        renamed_df.to_csv(output_path, index=False)
                    
                    logger.info(f"Standardized CSV saved to: {output_path}")
                
                return renamed_df
                
        except Exception as e:
            logger.error(f"Column standardization failed: {e}")
            raise


# Convenience functions for direct usage
def standardize_columns(csv_path: str, mapping_df: pd.DataFrame, **kwargs) -> Union[pd.DataFrame, bool]:
    """
    Convenience function to standardize CSV columns.
    
    Args:
        csv_path (str): Path to the CSV file
        mapping_df (pd.DataFrame): DataFrame with 'source_column' and 'target_column'
        **kwargs: Additional arguments for ColumnStandardizer.standardize_columns()
        
    Returns:
        Union[pd.DataFrame, bool]: Standardized DataFrame or success status
    """
    standardizer = ColumnStandardizer()
    return standardizer.standardize_columns(csv_path, mapping_df, **kwargs)


def create_mapping_dataframe(source_cols: List[str], target_cols: List[str]) -> pd.DataFrame:
    """
    Helper function to create a mapping DataFrame.
    
    Args:
        source_cols (List[str]): List of source column names
        target_cols (List[str]): List of target column names
        
    Returns:
        pd.DataFrame: Mapping DataFrame
    """
    if len(source_cols) != len(target_cols):
        raise ValueError("Source and target column lists must have the same length")
    
    return pd.DataFrame({
        'source_column': source_cols,
        'target_column': target_cols
    })


# Example usage
if __name__ == "__main__":
    # Example: Create a mapping DataFrame
    mapping_df = create_mapping_dataframe(
        source_cols=['Name', 'Age', 'Email'],
        target_cols=['full_name', 'age_years', 'email_address']
    )
    
    # Example: Standardize columns with full auto-detection
    try:
        result = standardize_columns(
            csv_path='sample_data.csv',
            mapping_df=mapping_df,
            auto_detect_all=True,
            preview=True,  # Preview first
            output_path='standardized_data.csv'
        )
        
        if isinstance(result, pd.DataFrame):
            print("Preview successful!")
        
        # Actual processing
        result = standardize_columns(
            csv_path='sample_data.csv',
            mapping_df=mapping_df,
            auto_detect_all=True,
            preview=False,
            output_path='standardized_data.csv',
            chunk_size=10000  # Process in chunks of 10k rows
        )
        
    except Exception as e:
        print(f"Error: {e}")