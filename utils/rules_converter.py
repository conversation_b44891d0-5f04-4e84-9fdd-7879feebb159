import os
import time
from typing import Dict, List, Any, Optional, Tuple
import pandas as pd
from excel_processor import ValidationInputProcessor
from llm_processor import AzureOpenAIProcessor
from validation_mapper import ValidationRuleMapper
from csv_generator import TargetCSVGenerator
from logger import Logger
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

class ValidationConverter:
    """Main orchestrator for validation rule conversion"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.logger = Logger.get_logger("validation_converter")
        
        # Initialize components
        self.input_processor = ValidationInputProcessor()
        self.llm_processor = AzureOpenAIProcessor(config_manager)
        self.rule_mapper = ValidationRuleMapper(self.llm_processor)
        self.csv_generator = TargetCSVGenerator()
        
        self.logger.info("ValidationConverter initialized successfully")
    
    def convert_validation_file(self, input_path: str, output_path: str, 
                              preview_only: bool = False, preview_rows: int = 5) -> bool:
        """Main conversion pipeline"""
        start_time = time.time()
        
        try:
            self.logger.info(f"Starting conversion: {input_path} -> {output_path}")
            
            # Step 1: Read and validate input file
            self.logger.info("Step 1: Reading input file...")
            rules_df = self.input_processor.read_validation_file(input_path)
            
            # Validate input format
            is_valid_input, input_issues = self.input_processor.validate_input_format(rules_df)
            if not is_valid_input:
                self.logger.error(f"Input validation failed: {input_issues}")
                return False
            
            # Step 2: Parse validation rules
            self.logger.info("Step 2: Parsing validation rules...")
            parsed_rules = self.input_processor.parse_validation_rules(rules_df)
            
            if not parsed_rules:
                self.logger.error("No valid rules found in input file")
                return False
            
            Logger.log_conversion_step("Input Processing", {
                "Total rows": len(rules_df),
                "Valid rules": len(parsed_rules),
                "Input file": input_path
            })
            
            # Step 3: Group rules by type for better processing
            self.logger.info("Step 3: Grouping rules by type...")
            grouped_rules = self.input_processor.group_rules_by_type(parsed_rules)
            
            # For preview mode, limit the number of rules
            if preview_only:
                limited_rules = []
                for group_rules in grouped_rules.values():
                    limited_rules.extend(group_rules[:max(1, preview_rows // len(grouped_rules))])
                limited_rules = limited_rules[:preview_rows]
                self.logger.info(f"Preview mode: Processing {len(limited_rules)} rules")
            else:
                limited_rules = parsed_rules
            
            # Step 4: Map rules using intelligent processing
            self.logger.info("Step 4: Mapping validation rules...")
            mapped_rules = self._map_rules_intelligently(
                limited_rules if preview_only else parsed_rules,
                grouped_rules
            )
            
            if not mapped_rules:
                self.logger.error("No rules were successfully mapped")
                return False
            
            Logger.log_conversion_step("Rule Mapping", {
                "Input rules": len(limited_rules if preview_only else parsed_rules),
                "Mapped rules": len(mapped_rules),
                "Success rate": f"{len(mapped_rules) / len(limited_rules if preview_only else parsed_rules) * 100:.1f}%"
            })
            
            # Step 5: Generate CSV output
            self.logger.info("Step 5: Generating CSV output...")
            if preview_only:
                csv_content = self.csv_generator.preview_csv_output(mapped_rules, preview_rows)
                print("CSV Preview:")
                print("=" * 80)
                print(csv_content)
                print("=" * 80)
                return True
            else:
                csv_content = self.csv_generator.generate_csv_content(mapped_rules)
            
            # Step 6: Validate output format
            self.logger.info("Step 6: Validating output format...")
            is_valid_output, output_issues = self.csv_generator.validate_output_format(csv_content)
            if output_issues:
                self.logger.warning(f"Output validation issues: {output_issues[:5]}...")  # Show first 5 issues
            
            # Step 7: Save output file
            self.logger.info("Step 7: Saving output file...")
            save_success = self.csv_generator.save_csv_file(csv_content, output_path)
            
            if not save_success:
                self.logger.error("Failed to save output file")
                return False
            
            # Step 8: Generate final report
            self._generate_conversion_report(parsed_rules, mapped_rules, output_path, start_time)
            
            self.logger.info(f"Conversion completed successfully: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Conversion failed: {str(e)}")
            Logger.log_error_with_context(e, {
                "input_path": input_path,
                "output_path": output_path,
                "preview_only": preview_only
            })
            return False
    
    def _map_rules_intelligently(self, rules: List[Dict], grouped_rules: Dict[str, List[Dict]]) -> List[Dict]:
        """Map rules using both pattern matching and LLM intelligence"""
        try:
            mapped_rules = []
            
            # Process each rule group
            for group_type, group_rules in grouped_rules.items():
                if not group_rules:
                    continue
                
                self.logger.info(f"Processing {len(group_rules)} rules of type: {group_type}")
                
                # Filter group_rules to only include rules in our processing list
                rules_to_process = [rule for rule in group_rules if rule in rules]
                
                if not rules_to_process:
                    continue
                
                # Use different strategies based on group type
                if group_type == 'All':
                    # File-level rules - use pattern matching first
                    group_mapped = self._process_file_level_rules(rules_to_process)
                elif group_type in ['Date', 'String', 'Numeric']:
                    # Column-specific rules - combine pattern matching with LLM
                    group_mapped = self._process_column_rules(rules_to_process, group_type)
                else:
                    # Unknown type - use LLM
                    group_mapped = self._process_unknown_rules(rules_to_process)
                
                mapped_rules.extend(group_mapped)
                
                # Log progress
                self.logger.info(f"Mapped {len(group_mapped)}/{len(rules_to_process)} rules for {group_type}")
            
            return mapped_rules
            
        except Exception as e:
            self.logger.error(f"Failed to map rules intelligently: {str(e)}")
            raise
    
    def _process_file_level_rules(self, rules: List[Dict]) -> List[Dict]:
        """Process file-level validation rules (All column type)"""
        mapped_rules = []
        
        for rule in rules:
            try:
                # File-level rules are usually pattern-based
                mapped_rule = self.rule_mapper.map_validation_rule(rule)
                
                # Validate mapping
                is_valid, issues = self.rule_mapper.validate_mapped_rule(mapped_rule)
                if is_valid:
                    mapped_rules.append(mapped_rule)
                else:
                    self.logger.warning(f"Invalid mapping for file-level rule: {issues}")
                    
            except Exception as e:
                self.logger.warning(f"Failed to map file-level rule: {str(e)}")
                continue
        
        return mapped_rules
    
    def _process_column_rules(self, rules: List[Dict], column_type: str) -> List[Dict]:
        """Process column-specific validation rules"""
        mapped_rules = []
        
        # Group similar rules for batch processing
        rule_batches = self._group_similar_rules(rules)
        
        for batch in rule_batches:
            try:
                if len(batch) == 1:
                    # Single rule - use pattern matching first
                    mapped_rule = self.rule_mapper.map_validation_rule(batch[0])
                    mapped_rules.append(mapped_rule)
                else:
                    # Multiple similar rules - use LLM for consistency
                    batch_mapped = self.llm_processor.batch_map_rules(batch)
                    mapped_rules.extend(batch_mapped)
                    
            except Exception as e:
                self.logger.warning(f"Failed to process {column_type} rule batch: {str(e)}")
                continue
        
        return mapped_rules
    
    def _process_unknown_rules(self, rules: List[Dict]) -> List[Dict]:
        """Process rules of unknown type using LLM"""
        try:
            return self.llm_processor.batch_map_rules(rules)
        except Exception as e:
            self.logger.warning(f"Failed to process unknown rules: {str(e)}")
            return []
    
    def _group_similar_rules(self, rules: List[Dict]) -> List[List[Dict]]:
        """Group similar rules for efficient batch processing"""
        groups = {}
        
        for rule in rules:
            # Create grouping key based on attribute pattern
            attr_key = rule['attribute'].lower().replace(' ', '_')
            
            # Simplify key for common patterns
            if 'distinct' in attr_key:
                attr_key = 'distinct'
            elif any(word in attr_key for word in ['min', 'max']):
                attr_key = 'range'
            elif 'percentile' in attr_key:
                attr_key = 'percentile'
            
            if attr_key not in groups:
                groups[attr_key] = []
            groups[attr_key].append(rule)
        
        return list(groups.values())
    
    def preview_conversion(self, input_path: str, num_rules: int = 5) -> Dict[str, Any]:
        """Preview conversion for first N rules"""
        try:
            self.logger.info(f"Generating preview for {input_path} ({num_rules} rules)")
            
            # Read and parse input
            rules_df = self.input_processor.read_validation_file(input_path)
            parsed_rules = self.input_processor.parse_validation_rules(rules_df)
            
            # Limit to preview size
            preview_rules = parsed_rules[:num_rules]
            
            # Map rules
            mapped_rules = []
            for rule in preview_rules:
                try:
                    mapped_rule = self.rule_mapper.map_validation_rule(rule)
                    mapped_rules.append(mapped_rule)
                except Exception as e:
                    self.logger.warning(f"Preview mapping failed for rule: {str(e)}")
                    continue
            
            # Generate preview output
            preview_csv = self.csv_generator.preview_csv_output(mapped_rules, num_rules)
            
            # Get statistics
            mapping_stats = self.rule_mapper.get_mapping_statistics(mapped_rules)
            output_stats = self.csv_generator.get_output_statistics(mapped_rules)
            
            return {
                'input_summary': {
                    'total_rules_in_file': len(parsed_rules),
                    'preview_rules_processed': len(preview_rules),
                    'successfully_mapped': len(mapped_rules)
                },
                'preview_csv': preview_csv,
                'mapping_statistics': mapping_stats,
                'output_statistics': output_stats
            }
            
        except Exception as e:
            self.logger.error(f"Preview generation failed: {str(e)}")
            return {'error': str(e)}
    
    def _generate_conversion_report(self, input_rules: List[Dict], mapped_rules: List[Dict], 
                                  output_path: str, start_time: float):
        """Generate comprehensive conversion report"""
        end_time = time.time()
        processing_time = round(end_time - start_time, 2)
        
        # Get statistics
        mapping_stats = self.rule_mapper.get_mapping_statistics(mapped_rules)
        output_stats = self.csv_generator.get_output_statistics(mapped_rules)
        
        # Create report
        report = {
            'conversion_summary': {
                'input_rules': len(input_rules),
                'output_rules': len(mapped_rules),
                'success_rate': f"{len(mapped_rules) / len(input_rules) * 100:.1f}%",
                'processing_time_seconds': processing_time,
                'output_file': output_path
            },
            'mapping_statistics': mapping_stats,
            'output_statistics': output_stats
        }
        
        # Log report
        Logger.log_conversion_step("Conversion Complete", report['conversion_summary'])
        
        # Save detailed report to file
        report_path = output_path.replace('.csv', '_report.json')
        try:
            import json
            with open(report_path, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            self.logger.info(f"Detailed report saved: {report_path}")
        except Exception as e:
            self.logger.warning(f"Failed to save detailed report: {str(e)}")
    
    def validate_input_file(self, input_path: str) -> Tuple[bool, List[str]]:
        """Validate input file before processing"""
        issues = []
        
        try:
            # Check file exists
            if not os.path.exists(input_path):
                issues.append(f"Input file does not exist: {input_path}")
                return False, issues
            
            # Check file extension
            valid_extensions = ['.xlsx', '.xls', '.csv']
            file_ext = os.path.splitext(input_path)[1].lower()
            if file_ext not in valid_extensions:
                issues.append(f"Unsupported file format: {file_ext}")
            
            # Try to read file
            try:
                rules_df = self.input_processor.read_validation_file(input_path)
                is_valid, format_issues = self.input_processor.validate_input_format(rules_df)
                if not is_valid:
                    issues.extend(format_issues)
            except Exception as e:
                issues.append(f"Failed to read file: {str(e)}")
            
        except Exception as e:
            issues.append(f"Validation error: {str(e)}")
        
        is_valid = len(issues) == 0
        return is_valid, issues
    
    def get_conversion_options(self) -> Dict[str, Any]:
        """Get available conversion options and settings"""
        return {
            'supported_input_formats': ['.xlsx', '.xls', '.csv'],
            'supported_rule_types': [
                'NOT_NULL', 'REGEX', 'CROSS_FILE_LEVEL', 'RANGE_CHECK',
                'DISTINCT_COUNT', 'FILE_LEVEL', 'PERCENTILE_CHECK', 
                'AGGREGATE_CHECK', 'CUSTOM_CHECK'
            ],
            'severity_levels': ['Warning', 'Error'],
            'output_format': 'CSV with headers',
            'batch_processing': True,
            'preview_available': True,
            'llm_provider': 'Azure OpenAI'
        }


# Convenience function for direct usage
def convert_validation_file(input_path: str, output_path: str, config_manager, 
                          preview_only: bool = False) -> bool:
    """Convenience function to convert a validation file"""
    converter = ValidationConverter(config_manager)
    return converter.convert_validation_file(input_path, output_path, preview_only)


# CLI interface function
def main():
    """Command line interface for the converter"""
    import argparse
    import sys
    
    parser = argparse.ArgumentParser(description='Convert validation rules to CSV format')
    parser.add_argument('input_file', help='Input Excel/CSV file with validation rules')
    parser.add_argument('output_file', help='Output CSV file path')
    parser.add_argument('--config', default='config.json', help='Config file path')
    parser.add_argument('--preview', action='store_true', help='Preview mode (limited rows)')
    parser.add_argument('--preview-rows', type=int, default=5, help='Number of rows for preview')
    
    args = parser.parse_args()
    
    try:
        # Import config manager (assuming it exists)
        from core.config_manager import ConfigManager
        
        config_manager = ConfigManager(args.config)
        converter = ValidationConverter(config_manager)
        
        if args.preview:
            success = converter.convert_validation_file(
                args.input_file, args.output_file, 
                preview_only=True, preview_rows=args.preview_rows
            )
        else:
            success = converter.convert_validation_file(args.input_file, args.output_file)
        
        if success:
            print(f"Conversion {'preview' if args.preview else 'completed'} successfully!")
            sys.exit(0)
        else:
            print("Conversion failed. Check logs for details.")
            sys.exit(1)
            
    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
