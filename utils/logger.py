import logging
import os
from datetime import datetime
from typing import Dict, Any
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))


class Logger:
    """Centralized logging utility for validation converter"""
    
    _loggers = {}
    
    @classmethod
    def get_logger(cls, name: str = "validation_converter", log_level: str = "INFO") -> logging.Logger:
        """Get or create logger instance"""
        if name not in cls._loggers:
            cls._loggers[name] = cls._setup_logger(name, log_level)
        return cls._loggers[name]
    
    @classmethod
    def _setup_logger(cls, name: str, log_level: str) -> logging.Logger:
        """Setup logger with file and console handlers"""
        logger = logging.getLogger(name)
        logger.setLevel(getattr(logging, log_level.upper()))
        
        # Avoid duplicate handlers
        if logger.handlers:
            return logger
        
        # Create logs directory if it doesn't exist
        log_dir = os.path.join(project_root,"logs")
        os.makedirs(log_dir, exist_ok=True)
        
        # File handler
        log_file = os.path.join(log_dir, f"{name}_{datetime.now().strftime('%Y%m%d')}.log")
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.DEBUG)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(getattr(logging, log_level.upper()))
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    @classmethod
    def log_conversion_step(cls, step: str, details: Dict[str, Any], logger_name: str = "validation_converter"):
        """Log conversion pipeline steps with details"""
        logger = cls.get_logger(logger_name)
        logger.info(f"Step: {step}")
        for key, value in details.items():
            logger.info(f"  {key}: {value}")
    
    @classmethod
    def log_error_with_context(cls, error: Exception, context: Dict[str, Any], logger_name: str = "validation_converter"):
        """Log error with contextual information"""
        logger = cls.get_logger(logger_name)
        logger.error(f"Error: {str(error)}")
        logger.error(f"Error Type: {type(error).__name__}")
        for key, value in context.items():
            logger.error(f"  Context - {key}: {value}")
