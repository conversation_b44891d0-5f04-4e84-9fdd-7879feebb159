import json
import time
from typing import Dict, List, Any, Optional
from openai import AzureOpenAI

import os
import sys
from pathlib import Path

project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from utils.logger import Logger


class AzureOpenAIProcessor:
    """Azure OpenAI integration for intelligent rule mapping"""
    
    def __init__(self, config_manager):
        self.config = config_manager.get_azure_openai_config()
        self.logger = Logger.get_logger("azure_openai_processor")
        self.client = self._initialize_azure_client()
        
        # Prompt templates
        self.rule_mapping_prompt = self._get_rule_mapping_prompt()
        self.severity_prompt = self._get_severity_prompt()
        self.config_generation_prompt = self._get_config_generation_prompt()
    
    def _initialize_azure_client(self) -> AzureOpenAI:
        """Initialize Azure OpenAI client with config"""
        try:
            client = AzureOpenAI(
                api_key=self.config.api_key,
                api_version=self.config.api_version,
                azure_endpoint=self.config.endpoint
            )
            self.logger.info("Azure OpenAI client initialized successfully")
            return client
        except Exception as e:
            self.logger.error(f"Failed to initialize Azure OpenAI client: {str(e)}")
            raise
    
    def map_validation_rule(self, input_rule: Dict) -> Dict[str, Any]:
        """Use LLM to map input rule to target format"""
        try:
            # Extract context for better mapping
            context = self._build_rule_context(input_rule)
            
            # Create prompt for rule mapping
            prompt = self.rule_mapping_prompt.format(
                filename=input_rule['filename'],
                column_type=input_rule['column_type'],
                column_name=input_rule['column_name'],
                attribute=input_rule['attribute'],
                context=json.dumps(context, indent=2)
            )
            
            # Get LLM response
            response = self._call_llm(prompt)
            
            # Parse response to structured format
            mapped_rule = self._parse_rule_mapping_response(response, input_rule)
            
            self.logger.info(f"Successfully mapped rule: {input_rule['attribute'][:50]}...")
            return mapped_rule
            
        except Exception as e:
            self.logger.error(f"Failed to map validation rule: {str(e)}")
            # Return fallback mapping
            return self._create_fallback_mapping(input_rule)
    
    def batch_map_rules(self, input_rules: List[Dict]) -> List[Dict]:
        """Process multiple rules in batches"""
        try:
            mapped_rules = []
            batch_size = self.config.batch_size or 5  # Use config batch_size or default to 5
            
            for i in range(0, len(input_rules), batch_size):
                batch = input_rules[i:i + batch_size]
                self.logger.info(f"Processing batch {i//batch_size + 1}/{(len(input_rules) + batch_size - 1)//batch_size}")
                
                for rule in batch:
                    mapped_rule = self.map_validation_rule(rule)
                    mapped_rules.append(mapped_rule)
                    
                    # Small delay to respect rate limits
                    time.sleep(0.5)
                
                # Longer delay between batches
                if i + batch_size < len(input_rules):
                    time.sleep(2)
            
            self.logger.info(f"Completed batch processing of {len(mapped_rules)} rules")
            return mapped_rules
            
        except Exception as e:
            self.logger.error(f"Failed to process rule batch: {str(e)}")
            raise
    
    def generate_rule_config(self, rule_type: str, context: Dict) -> Dict:
        """Generate JSON rule configuration using LLM"""
        try:
            prompt = self.config_generation_prompt.format(
                rule_type=rule_type,
                context=json.dumps(context, indent=2)
            )
            
            response = self._call_llm(prompt)
            config = self._parse_config_response(response)
            
            return config
            
        except Exception as e:
            self.logger.error(f"Failed to generate rule config: {str(e)}")
            return self._get_default_config(rule_type)
    
    def determine_severity_level(self, rule_context: Dict) -> str:
        """Determine Warning/Error severity using LLM"""
        try:
            prompt = self.severity_prompt.format(
                rule_type=rule_context.get('rule_type', 'Unknown'),
                attribute=rule_context.get('attribute', ''),
                column_type=rule_context.get('column_type', ''),
                business_impact=rule_context.get('business_context', {}).get('validation_criticality', 'medium')
            )
            
            response = self._call_llm(prompt)
            severity = self._parse_severity_response(response)
            
            return severity
            
        except Exception as e:
            self.logger.error(f"Failed to determine severity: {str(e)}")
            return "Warning"  # Default fallback
    
    def _call_llm(self, prompt: str, max_retries: int = None) -> str:
        """Call Azure OpenAI with retry logic"""
        if max_retries is None:
            max_retries = self.config.max_retries
        
        for attempt in range(max_retries):
            try:
                response = self.client.chat.completions.create(
                    model=self.config.deployment,
                    messages=[
                        {"role": "system", "content": "You are an expert data validation engineer."},
                        {"role": "user", "content": prompt}
                    ],
                    temperature=self.config.temperature or 0.1,
                    max_tokens=self.config.max_tokens or 1500,
                    timeout=self.config.timeout
                )
                
                return response.choices[0].message.content.strip()
                
            except Exception as e:
                self.logger.warning(f"LLM call attempt {attempt + 1} failed: {str(e)}")
                if attempt == max_retries - 1:
                    raise
                time.sleep(2 ** attempt)  # Exponential backoff
    
    def _build_rule_context(self, input_rule: Dict) -> Dict:
        """Build comprehensive context for rule mapping"""
        return {
            'column_type': input_rule['column_type'],
            'attribute_category': self._categorize_attribute(input_rule['attribute']),
            'is_multi_column': input_rule['column_name'].startswith('['),
            'data_source': 'Circana' if 'circana' in input_rule['filename'].lower() else 'Unknown'
        }
    
    def _categorize_attribute(self, attribute: str) -> str:
        """Quick categorization of attribute for context"""
        attr_lower = attribute.lower()
        if 'duplicate' in attr_lower:
            return 'duplicate_check'
        elif any(word in attr_lower for word in ['min', 'max']):
            return 'range_check'
        elif 'distinct' in attr_lower:
            return 'distinct_check'
        elif 'percentile' in attr_lower:
            return 'statistical_check'
        else:
            return 'other'
    
    def _parse_rule_mapping_response(self, response: str, input_rule: Dict) -> Dict[str, Any]:
        """Parse LLM response into structured rule mapping"""
        try:
            # Try to extract JSON from response
            lines = response.split('\n')
            rule_data = {}
            
            for line in lines:
                line = line.strip()
                if ':' in line and not line.startswith('#'):
                    key, value = line.split(':', 1)
                    key = key.strip().lower().replace('-', '_').replace(' ', '_')
                    value = value.strip().strip('"\'')
                    
                    # Handle JSON values
                    if value.startswith('{') and value.endswith('}'):
                        try:
                            value = json.loads(value)
                        except:
                            pass
                    
                    rule_data[key] = value
            
            # Fill in required fields with defaults if missing
            mapped_rule = {
                'rule_name': rule_data.get('rule_name', self._generate_rule_name(input_rule)),
                'rule_type': rule_data.get('rule_type', self._infer_rule_type(input_rule)),
                'source_file': f"{input_rule['filename']}",
                'reference_file': rule_data.get('reference_file', ''),
                'source_col': self._extract_column_name(input_rule['column_name']),
                'reference_col': rule_data.get('reference_col', ''),
                'rule_config': rule_data.get('rule_config', self._generate_default_config(input_rule)),
                'severity': rule_data.get('severity', 'Warning'),
                'is_active': rule_data.get('is_active', 'TRUE')
            }
            
            return mapped_rule
            
        except Exception as e:
            self.logger.error(f"Failed to parse rule mapping response: {str(e)}")
            return self._create_fallback_mapping(input_rule)
    
    def _generate_rule_name(self, input_rule: Dict) -> str:
        """Generate meaningful rule name"""
        col_type = input_rule['column_type'].upper()
        col_name = self._extract_column_name(input_rule['column_name'])
        attr = input_rule['attribute'].replace(' ', '_').upper()
        return f"{col_type}_{col_name}_{attr}"[:50]
    
    def _extract_column_name(self, column_name_str: str) -> str:
        """Extract single column name from column name string"""
        if column_name_str.startswith('[') and column_name_str.endswith(']'):
            # For multi-column, return first column or 'ALL_COLUMNS'
            if 'Time' in column_name_str:
                return 'Time'
            elif 'Product' in column_name_str:
                return 'Product'
            else:
                return 'ALL_COLUMNS'
        return column_name_str
    
    def _infer_rule_type(self, input_rule: Dict) -> str:
        """Infer rule type from input rule"""
        attr_lower = input_rule['attribute'].lower()
        
        if 'duplicate' in attr_lower:
            if 'all columns' in attr_lower:
                return 'FILE_LEVEL'
            else:
                return 'NOT_NULL'
        elif any(word in attr_lower for word in ['min', 'max']):
            return 'RANGE_CHECK'
        elif 'distinct' in attr_lower:
            return 'DISTINCT_COUNT'
        elif 'percentile' in attr_lower:
            return 'PERCENTILE_CHECK'
        else:
            return 'CUSTOM_CHECK'
    
    def _generate_default_config(self, input_rule: Dict) -> Dict:
        """Generate default config based on rule type"""
        attr_lower = input_rule['attribute'].lower()
        
        if 'duplicate' in attr_lower:
            return {"check": "duplicates", "scope": "all_columns" if "all columns" in attr_lower else "single_column"}
        elif 'min' in attr_lower:
            return {"check": "min_value", "operator": ">="}
        elif 'max' in attr_lower:
            return {"check": "max_value", "operator": "<="}
        elif 'distinct' in attr_lower:
            return {"check": "distinct_count", "min_threshold": 1}
        else:
            return {"check": "custom", "description": input_rule['attribute']}
    
    def _create_fallback_mapping(self, input_rule: Dict) -> Dict[str, Any]:
        """Create fallback mapping when LLM fails"""
        return {
            'rule_name': self._generate_rule_name(input_rule),
            'rule_type': self._infer_rule_type(input_rule),
            'source_file': f"{input_rule['filename']}",
            'reference_file': '',
            'source_col': self._extract_column_name(input_rule['column_name']),
            'reference_col': '',
            'rule_config': self._generate_default_config(input_rule),
            'severity': 'Warning',
            'is_active': 'TRUE'
        }
    
    def _parse_config_response(self, response: str) -> Dict:
        """Parse config generation response"""
        try:
            # Try to find JSON in response
            start = response.find('{')
            end = response.rfind('}') + 1
            if start != -1 and end != 0:
                json_str = response[start:end]
                return json.loads(json_str)
        except:
            pass
        return {}
    
    def _parse_severity_response(self, response: str) -> str:
        """Parse severity determination response"""
        response_lower = response.lower()
        if 'error' in response_lower:
            return 'Error'
        else:
            return 'Warning'
    
    def _get_default_config(self, rule_type: str) -> Dict:
        """Get default configuration for rule type"""
        configs = {
            'NOT_NULL': {"check": "not_null"},
            'RANGE_CHECK': {"check": "range", "operator": ">="},
            'DISTINCT_COUNT': {"check": "distinct_count", "min_threshold": 1},
            'FILE_LEVEL': {"check": "file_level", "scope": "all_columns"}
        }
        return configs.get(rule_type, {"check": "custom"})
    
    def _get_rule_mapping_prompt(self) -> str:
        """Get rule mapping prompt template"""
        return """
You are an expert data validation engineer. Convert the following validation rule to the target format.

Input Rule:
- Filename: {filename}
- Column Type: {column_type}
- Column Name: {column_name}
- Attribute: {attribute}

Context: {context}

Target Format:
- rule_name: Generate a descriptive name (max 50 chars)
- rule_type: Choose from [NOT_NULL, REGEX, CROSS_FILE_LEVEL, RANGE_CHECK, DISTINCT_COUNT, FILE_LEVEL, PERCENTILE_CHECK, AGGREGATE_CHECK]
- source_file: [filename]
- reference_file: (if applicable, otherwise empty)
- source_col: Primary column name
- reference_col: (if applicable, otherwise empty)
- rule_config: JSON configuration object
- severity: Warning or Error
- is_active: TRUE

Mapping Guidelines:
1. "Does the file have duplicates at all columns?" -> rule_type: FILE_LEVEL, rule_config: {{"check": "duplicates", "scope": "all_columns"}}
2. "Does the file have duplicates at specified column?" -> rule_type: NOT_NULL, rule_config: {{"check": "duplicates", "scope": "single_column"}}
3. Date Min/Max -> rule_type: RANGE_CHECK, rule_config: {{"check": "min_date"/"max_date", "operator": ">="/"<="}}
4. "Number of Distinct value" -> rule_type: DISTINCT_COUNT, rule_config: {{"check": "distinct_count", "min_threshold": 1}}
5. "Distinct values" -> rule_type: DISTINCT_COUNT, rule_config: {{"check": "distinct_values", "action": "list"}}
6. Numeric aggregates (Sum, Average, Min, Max) -> rule_type: AGGREGATE_CHECK
7. Percentiles -> rule_type: PERCENTILE_CHECK

Output format (one item per line):
rule_name: [generated name]
rule_type: [mapped type]
source_file: {filename}
reference_file: [if needed]
source_col: [column name]
reference_col: [if needed]
rule_config: [JSON object]
severity: [Warning/Error]
is_active: TRUE
"""
    
    def _get_severity_prompt(self) -> str:
        """Get severity determination prompt"""
        return """
Determine the appropriate severity level (Warning/Error) for this validation rule:

Rule Type: {rule_type}
Attribute: {attribute}
Column Type: {column_type}
Business Impact: {business_impact}

Guidelines:
- Data integrity issues (duplicates, null checks): Error
- Data quality issues (distinct counts, statistical checks): Warning
- Business critical fields (IDs, keys): Error
- Statistical outliers (percentiles, aggregates): Warning
- Date range validations: Warning
- File-level checks: Error

Return only: Warning OR Error
"""
    
    def _get_config_generation_prompt(self) -> str:
        """Get config generation prompt"""
        return """
Generate a JSON configuration object for this validation rule:

Rule Type: {rule_type}
Context: {context}

Return only a valid JSON object with appropriate configuration parameters.
Examples:
- RANGE_CHECK: {{"check": "min_value", "operator": ">=", "threshold": null}}
- DISTINCT_COUNT: {{"check": "distinct_count", "min_threshold": 1}}
- FILE_LEVEL: {{"check": "duplicates", "scope": "all_columns"}}
"""

    def _get_synonym_generation_prompt(self) -> str:    
        """Get synonym generation prompt template for schema enricher"""
        return """
    You are an expert data analyst specializing in CPG (Consumer Packaged Goods) domain schemas.

    Generate comprehensive synonyms for the following database column:

    Column Name: {column_name}
    Description: {description}
    Category: {category}
    Table Context: {table_context}

    Instructions:
    1. Generate 8-12 relevant synonyms for this column name to ensure comprehensive coverage
    2. Include multiple categories of synonyms:
    - Technical database terms (snake_case, camelCase)
    - Business-friendly names
    - Industry-standard terminology
    - Common abbreviations and acronyms
    - Alternative naming conventions
    - Regional/cultural variations where applicable
    3. Focus on CPG domain terminology and consider all possible variations
    4. Include both formal and informal naming patterns
    5. Return ONLY a valid JSON array format

    Examples of comprehensive synonyms:
    - product_id → ["product_code", "item_id", "sku", "product_number", "item_code", "product_identifier", "merchandise_id", "catalog_id", "upc_code", "barcode", "item_reference", "product_key"]
    - customer_name → ["client_name", "buyer_name", "account_name", "consumer_name", "customer_title", "purchaser_name", "shopper_name", "user_name", "contact_name", "account_holder", "patron_name"]
    - sales_amount → ["revenue", "sales_value", "transaction_amount", "purchase_value", "order_total", "invoice_amount", "billing_amount", "payment_value", "transaction_value", "sales_revenue", "purchase_amount"]

    Return format: ["synonym1", "synonym2", "synonym3", "synonym4", "synonym5", "synonym6", "synonym7", "synonym8", "synonym9", "synonym10"]

    Return 8-12 synonyms in JSON array format. Do not include any explanation, just return the JSON array.
    """

    def _get_domain_mapping_prompt(self) -> str:
        """Get domain mapping prompt template for schema enricher"""
        return """
    You are an expert in CPG (Consumer Packaged Goods) domain classification with comprehensive industry knowledge.

    Analyze the following database column and determine ALL relevant CPG sub-industries where this column would be applicable:

    Column Name: {column_name}
    Description: {description}
    Category: {category}
    Table Type: {table_type}

    Available CPG Sub-Industries:
    {cpg_domains}

    Instructions:
    1. Select ALL relevant CPG sub-industries where this column would be commonly used (aim for 3-8 industries for maximum coverage)
    2. Consider the column's business context, data type, and potential applications across different CPG sectors
    3. Think comprehensively about which industries would typically track this type of information
    4. Include both primary industries (most relevant) and secondary industries (somewhat relevant)
    5. Consider cross-industry applications and shared business processes
    6. Return ONLY a valid JSON array format

    Examples of comprehensive domain mapping:
    - product_id → ["Food & Beverages", "Personal Care & Cosmetics", "Household Products", "Health & Wellness", "Pet Care", "Batteries & Power Solutions", "Electronics Accessories"] (universal identifier used across most CPG)
    - battery_voltage → ["Batteries & Power Solutions", "Electronics Accessories", "Automotive Care", "Sports & Fitness", "Travel & Convenience"]
    - expiration_date → ["Food & Beverages", "Health & Wellness", "Personal Care & Cosmetics", "Baby Care", "Pet Care", "Health Supplements", "Over-the-Counter Pharmaceuticals"]
    - customer_name → ["Food & Beverages", "Personal Care & Cosmetics", "Household Products", "Health & Wellness", "Pet Care", "Tobacco & Alcohol", "Electronics Accessories"]
    - price → ["Food & Beverages", "Personal Care & Cosmetics", "Household Products", "Health & Wellness", "Pet Care", "Batteries & Power Solutions", "Electronics Accessories", "Automotive Care"]

    Return format: ["Industry1", "Industry2", "Industry3", "Industry4", "Industry5", "Industry6"]

    Return 3-8 relevant industries in JSON array format with exact industry names from the provided list. Do not include any explanation, just return the JSON array.
    """