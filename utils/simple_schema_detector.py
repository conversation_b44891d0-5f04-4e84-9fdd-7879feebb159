import re
import json
import statistics
from decimal import Decimal, InvalidOperation
from typing import Dict, List, Any, Union, Optional
from collections import Counter, defaultdict


class SimpleTypeDetector:
    """
    Simple schema detector that identifies only three primitive data types:
    - numeric (integers, floats, decimals)
    - string (text data, IDs, codes)
    - json (objects and arrays)
    """
    
    def __init__(self, 
                 confidence_threshold: float = 0.8,
                 numeric_threshold: float = 0.9):
        """
        Initialize the simple type detector.
        
        Args:
            confidence_threshold: Minimum confidence to assign a type
            numeric_threshold: Minimum percentage of values that must be numeric
        """
        self.confidence_threshold = confidence_threshold
        self.numeric_threshold = numeric_threshold
        self._setup_patterns()
        self._setup_context_hints()
    
    def _setup_patterns(self):
        """Setup regex patterns for basic type detection."""
        self.patterns = {
            # Numeric patterns
            'integer': re.compile(r'^[+-]?\d+$'),
            'float': re.compile(r'^[+-]?\d*\.\d+([eE][+-]?\d+)?$'),
            'scientific': re.compile(r'^[+-]?\d+\.?\d*[eE][+-]?\d+$'),
            
            # String patterns that should NOT be treated as numeric
            'leading_zeros': re.compile(r'^0\d+$'),  # 001, 0123
            'mixed_alphanumeric': re.compile(r'^.*[a-zA-Z].*\d|.*\d.*[a-zA-Z].*$'),  # abc123, 123abc
            'formatted_number': re.compile(r'^.*[-\s\(\)\.#].*$'),  # Phone: ************
            
            # JSON patterns
            'json_object': re.compile(r'^\s*\{.*\}\s*$', re.DOTALL),
            'json_array': re.compile(r'^\s*\[.*\]\s*$', re.DOTALL)
        }
    
    def _setup_context_hints(self):
        """Setup column name hints for context-aware detection."""
        self.context_hints = {
            # Should be strings even if they look numeric
            'string_hints': [
                'id', 'code', 'number', 'phone', 'zip', 'postal', 'ssn', 'account',
                'sku', 'barcode', 'isbn', 'serial', 'license', 'registration',
                'tracking', 'order_number', 'invoice', 'reference', 'token',
                'key', 'uuid', 'guid', 'hash'
            ],
            
            # Should be numeric
            'numeric_hints': [
                'age', 'count', 'quantity', 'amount', 'price', 'cost', 'total',
                'sum', 'average', 'mean', 'score', 'rating', 'weight', 'height',
                'length', 'width', 'depth', 'volume', 'area', 'distance', 'salary',
                'revenue', 'profit', 'loss', 'balance', 'rate', 'percentage'
            ],
            
            # Should be JSON
            'json_hints': [
                'config', 'settings', 'metadata', 'properties', 'attributes',
                'data', 'payload', 'content', 'details', 'options', 'params'
            ]
        }
    
    def detect_schema(self, filename: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main method to detect schema from dictionary data.
        
        Args:
            filename: Name of the input file
            data: Dictionary with column names as keys and values as values
            
        Returns:
            Schema detection result with only numeric, string, json types
        """
        if not isinstance(data, dict):
            raise ValueError("Data must be a dictionary")
        
        schema = {}
        
        for column_name, value in data.items():
            # For single value, create a list for analysis
            values = [value] if not isinstance(value, list) else value
            
            # Detect type
            detected_type = self._analyze_column_simple(column_name, values)
            schema[column_name] = detected_type
        
        return {
            "filename": filename,
            "schema": schema
        }
    
    def detect_schema_from_rows(self, filename: str, rows: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Detect schema from multiple rows for better accuracy.
        
        Args:
            filename: Name of the input file
            rows: List of dictionaries representing rows
            
        Returns:
            Schema detection result
        """
        if not rows:
            return {"filename": filename, "schema": {}}
        
        # Collect all unique column names
        all_columns = set()
        for row in rows:
            if isinstance(row, dict):
                all_columns.update(row.keys())
        
        schema = {}
        
        for column_name in all_columns:
            # Collect all values for this column
            column_values = []
            for row in rows:
                if isinstance(row, dict) and column_name in row:
                    column_values.append(row[column_name])
                else:
                    column_values.append(None)
            
            # Detect type
            detected_type = self._analyze_column_simple(column_name, column_values)
            schema[column_name] = detected_type
        
        return {
            "filename": filename,
            "schema": schema
        }
    
    def _analyze_column_simple(self, column_name: str, values: List[Any]) -> str:
        """
        Analyze column and return one of: numeric, string, json.
        
        Args:
            column_name: Name of the column
            values: List of values in the column
            
        Returns:
            One of: 'numeric', 'string', 'json'
        """
        # Filter out None values for analysis
        non_null_values = [v for v in values if v is not None]
        
        if not non_null_values:
            return "string"  # Default null to string
        
        # Step 1: Get context hint from column name
        context_hint = self._get_context_hint(column_name)
        
        # Step 2: Quick JSON check - if any value is clearly JSON, return json
        json_count = 0
        for value in non_null_values:
            if self._is_json_value(value):
                json_count += 1
        
        if json_count > 0:
            json_ratio = json_count / len(non_null_values)
            if json_ratio >= 0.5:  # If 50%+ are JSON, classify as JSON
                return "json"
        
        # Step 3: Check if context strongly suggests string
        if context_hint == 'string':
            return "string"
        
        # Step 4: Check if context strongly suggests JSON
        if context_hint == 'json':
            return "json"
        
        # Step 5: Analyze for numeric vs string
        numeric_count = 0
        string_indicators = 0
        
        for value in non_null_values:
            # Check if value looks numeric
            if self._is_numeric_value(value):
                numeric_count += 1
            
            # Check for string indicators
            if self._has_string_indicators(value):
                string_indicators += 1
        
        total_values = len(non_null_values)
        numeric_ratio = numeric_count / total_values if total_values > 0 else 0
        string_indicator_ratio = string_indicators / total_values if total_values > 0 else 0
        
        # Step 6: Decision logic with intelligence
        
        # Strong string indicators override numeric detection
        if string_indicator_ratio > 0.1:  # If >10% have string indicators
            return "string"
        
        # Context hint for numeric
        if context_hint == 'numeric' and numeric_ratio > 0.5:
            return "numeric"
        
        # High numeric ratio with no context conflicts
        if numeric_ratio >= self.numeric_threshold:
            return "numeric"
        
        # Medium numeric ratio but with numeric context hint
        if numeric_ratio >= 0.7 and context_hint == 'numeric':
            return "numeric"
        
        # Additional intelligence: check uniqueness (high uniqueness suggests IDs)
        unique_count = len(set(str(v) for v in non_null_values))
        uniqueness_ratio = unique_count / total_values if total_values > 0 else 0
        
        if uniqueness_ratio > 0.9 and numeric_ratio < 0.95:
            # High uniqueness with some non-numeric values suggests IDs
            return "string"
        
        # Check for consistent length (suggests codes/IDs)
        if self._has_consistent_length(non_null_values):
            return "string"
        
        # Default decision
        if numeric_ratio >= 0.6:
            return "numeric"
        else:
            return "string"
    
    def _get_context_hint(self, column_name: str) -> Optional[str]:
        """Get type hint from column name."""
        column_lower = column_name.lower()
        
        # Check for JSON hints first
        for keyword in self.context_hints['json_hints']:
            if keyword in column_lower:
                return 'json'
        
        # Check for string hints
        for keyword in self.context_hints['string_hints']:
            if keyword in column_lower:
                return 'string'
        
        # Check for numeric hints
        for keyword in self.context_hints['numeric_hints']:
            if keyword in column_lower:
                return 'numeric'
        
        return None
    
    def _is_json_value(self, value: Any) -> bool:
        """Check if value is JSON (object or array)."""
        # Direct Python objects
        if isinstance(value, (dict, list)):
            return True
        
        # String that might be JSON
        if isinstance(value, str):
            str_value = value.strip()
            
            # Quick pattern check
            if (self.patterns['json_object'].match(str_value) or 
                self.patterns['json_array'].match(str_value)):
                
                # Try to parse as JSON
                try:
                    json.loads(str_value)
                    return True
                except (json.JSONDecodeError, ValueError):
                    return False
        
        return False
    
    def _is_numeric_value(self, value: Any) -> bool:
        """Check if value can be treated as numeric."""
        # Direct numeric types
        if isinstance(value, (int, float)):
            return True
        
        if isinstance(value, str):
            str_value = value.strip()
            
            if not str_value:
                return False
            
            # Check for string indicators first (these override numeric detection)
            if self._has_string_indicators(value):
                return False
            
            # Try numeric patterns
            if (self.patterns['integer'].match(str_value) or
                self.patterns['float'].match(str_value) or
                self.patterns['scientific'].match(str_value)):
                
                # Double-check by trying to convert
                try:
                    float(str_value)
                    return True
                except (ValueError, OverflowError):
                    return False
        
        return False
    
    def _has_string_indicators(self, value: Any) -> bool:
        """Check if value has indicators that suggest it should be a string."""
        if not isinstance(value, str):
            return False
        
        str_value = value.strip()
        
        # Leading zeros
        if self.patterns['leading_zeros'].match(str_value):
            return True
        
        # Mixed alphanumeric
        if self.patterns['mixed_alphanumeric'].match(str_value):
            return True
        
        # Formatted numbers (with separators)
        if self.patterns['formatted_number'].match(str_value):
            return True
        
        # Very long numbers (likely IDs)
        if str_value.isdigit() and len(str_value) > 10:
            return True
        
        return False
    
    def _has_consistent_length(self, values: List[Any]) -> bool:
        """Check if values have consistent length (suggests codes/IDs)."""
        if len(values) < 2:
            return False
        
        lengths = [len(str(v)) for v in values]
        unique_lengths = set(lengths)
        
        # If all values have the same length or very few different lengths
        return len(unique_lengths) <= 2 and min(lengths) >= 3
    
    def _calculate_statistics(self, values: List[Any]) -> Dict[str, Any]:
        """Calculate basic statistics for the values."""
        stats = {
            'total_count': len(values),
            'unique_count': len(set(str(v) for v in values)),
            'numeric_count': 0,
            'json_count': 0,
            'string_indicators_count': 0,
            'avg_length': 0,
            'consistent_length': False
        }
        
        if not values:
            return stats
        
        lengths = []
        for value in values:
            str_val = str(value)
            lengths.append(len(str_val))
            
            if self._is_numeric_value(value):
                stats['numeric_count'] += 1
            
            if self._is_json_value(value):
                stats['json_count'] += 1
            
            if self._has_string_indicators(value):
                stats['string_indicators_count'] += 1
        
        stats['avg_length'] = statistics.mean(lengths) if lengths else 0
        stats['consistent_length'] = self._has_consistent_length(values)
        
        return stats


# Convenience functions for easy usage
def detect_simple_schema(filename: str, data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convenience function for simple schema detection from dictionary.
    
    Args:
        filename: Name of the input file
        data: Dictionary with column names as keys and values as values
        
    Returns:
        Schema detection result (numeric, string, json only)
    """
    detector = SimpleTypeDetector()
    return detector.detect_schema(filename, data)


def detect_simple_schema_from_rows(filename: str, rows: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Convenience function for simple schema detection from multiple rows.
    
    Args:
        filename: Name of the input file
        rows: List of dictionaries representing rows
        
    Returns:
        Schema detection result with improved accuracy
    """
    detector = SimpleTypeDetector()
    return detector.detect_schema_from_rows(filename, rows)


# Example usage and testing
def test_simple_schema_detection():
    """Test the simple schema detection with various data types."""
    
    print("=== Simple Schema Detection Tests (numeric, string, json only) ===\n")
    
    # Test 1: Tricky numeric vs string cases
    test_data_1 = {
        "user_id": "12345",        # Should be string (ID hint)
        "age": "30",               # Should be numeric (age hint + valid number)
        "zipcode": "01234",        # Should be string (leading zero)
        "phone": "555-1234",       # Should be string (formatted)
        "amount": "99.99",         # Should be numeric (amount hint)
        "account_number": "000123", # Should be string (leading zeros)
        "settings": '{"theme": "dark", "lang": "en"}', # Should be json
        "tags": '["admin", "user"]' # Should be json
    }
    
    result1 = detect_simple_schema("users.json", test_data_1)
    print("Test 1 - Tricky Cases:")
    print(json.dumps(result1, indent=2))
    
    # Test 2: Multiple rows for better detection
    test_rows = [
        {
            "id": "001", 
            "score": "95", 
            "config": {"notifications": True},
            "mixed": "123abc"
        },
        {
            "id": "002", 
            "score": "87", 
            "config": {"notifications": False},
            "mixed": "456def"
        },
        {
            "id": "003", 
            "score": "92", 
            "config": {"notifications": True},
            "mixed": "789ghi"
        }
    ]
    
    result2 = detect_simple_schema_from_rows("scores.json", test_rows)
    print("\nTest 2 - Multiple Rows:")
    print(json.dumps(result2, indent=2))
    
    # Test 3: Pure numeric data
    test_data_3 = {
        "quantity": 100,
        "price": 29.99,
        "revenue": "50000",
        "count": "42"
    }
    
    result3 = detect_simple_schema("metrics.json", test_data_3)
    print("\nTest 3 - Numeric Data:")
    print(json.dumps(result3, indent=2))
    
    # Test 4: Mixed JSON and primitive types
    test_data_4 = {
        "name": "John Doe",
        "metadata": {"role": "admin", "permissions": ["read", "write"]},
        "preferences": ["dark_mode", "notifications"],
        "employee_id": "EMP001",
        "salary": 75000
    }
    
    result4 = detect_simple_schema("employees.json", test_data_4)
    print("\nTest 4 - Mixed Types:")
    print(json.dumps(result4, indent=2))
    
    return result1, result2, result3, result4


if __name__ == "__main__":
    # Run comprehensive tests
    test_simple_schema_detection()
    
    # Simple usage example
    print("\n" + "="*50)
    print("Simple Usage Example:")
    
    sample_data = {
        "product_id": "PROD-001",    # → string (ID pattern)
        "price": "29.99",           # → numeric (price hint + valid number)
        "quantity": "5",            # → numeric (quantity hint)
        "tags": ["electronics", "gadget"],  # → json (array)
        "metadata": {"brand": "TechCorp", "warranty": "2 years"}  # → json (object)
    }
    
    result = detect_simple_schema("products.json", sample_data)
    print(json.dumps(result, indent=2))