import pandas as pd
import os
from typing import List, Dict, Any, <PERSON><PERSON>
from logger import Logger

import sys
from pathlib import Path



class ValidationInputProcessor:
    """Process input validation files (Excel/CSV)"""
    
    def __init__(self):
        self.logger = Logger.get_logger("validation_input_processor")
        self.supported_formats = ['.xlsx', '.xls', '.csv']
    
    def read_validation_file(self, file_path: str) -> pd.DataFrame:
        """Read Excel or CSV validation rules file"""
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"File not found: {file_path}")
            
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext not in self.supported_formats:
                raise ValueError(f"Unsupported file format: {file_ext}. Supported: {self.supported_formats}")
            
            self.logger.info(f"Reading validation file: {file_path}")
            
            if file_ext == '.csv':
                df = pd.read_csv(file_path)
            else:
                df = pd.read_excel(file_path)
            
            self.logger.info(f"Successfully read {len(df)} validation rules")
            return df
            
        except Exception as e:
            self.logger.error(f"Failed to read file {file_path}: {str(e)}")
            raise
    
    def parse_validation_rules(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Parse validation rules into structured format"""
        try:
            # Expected columns: Filename, Column Type, Column Name, Attribute
            expected_columns = ['Filename', 'Column Type', 'Column Name', 'Attribute']
            
            # Check if all expected columns exist
            missing_columns = [col for col in expected_columns if col not in df.columns]
            if missing_columns:
                raise ValueError(f"Missing required columns: {missing_columns}")
            
            rules = []
            for idx, row in df.iterrows():
                try:
                    rule = {
                        'filename': str(row['Filename']).strip(),
                        'column_type': str(row['Column Type']).strip(),
                        'column_name': str(row['Column Name']).strip(),
                        'attribute': str(row['Attribute']).strip(),
                        'row_index': idx
                    }
                    
                    # Skip empty rows
                    if any(pd.isna(row[col]) or str(row[col]).strip() == '' for col in expected_columns):
                        self.logger.warning(f"Skipping empty row at index {idx}")
                        continue
                    
                    rules.append(rule)
                    
                except Exception as e:
                    self.logger.warning(f"Error parsing row {idx}: {str(e)}")
                    continue
            
            self.logger.info(f"Parsed {len(rules)} valid validation rules")
            return rules
            
        except Exception as e:
            self.logger.error(f"Failed to parse validation rules: {str(e)}")
            raise
    
    def group_rules_by_type(self, rules: List[Dict]) -> Dict[str, List[Dict]]:
        """Group rules by column type and validation pattern"""
        try:
            grouped = {
                'All': [],
                'Date': [],
                'String': [],
                'Numeric': []
            }
            
            for rule in rules:
                column_type = rule['column_type']
                if column_type in grouped:
                    grouped[column_type].append(rule)
                else:
                    self.logger.warning(f"Unknown column type: {column_type} for rule: {rule}")
                    # Add to a generic group if needed
                    if 'Other' not in grouped:
                        grouped['Other'] = []
                    grouped['Other'].append(rule)
            
            # Log group statistics
            for group_type, group_rules in grouped.items():
                if group_rules:
                    self.logger.info(f"Group '{group_type}': {len(group_rules)} rules")
            
            return grouped
            
        except Exception as e:
            self.logger.error(f"Failed to group rules by type: {str(e)}")
            raise
    
    def extract_rule_context(self, rule: Dict) -> Dict[str, Any]:
        """Extract contextual information for LLM processing"""
        try:
            context = {
                'source_filename': rule['filename'],
                'column_type': rule['column_type'],
                'column_name': rule['column_name'],
                'attribute_description': rule['attribute'],
                'rule_category': self._categorize_attribute(rule['attribute']),
                'business_context': self._extract_business_context(rule)
            }
            
            # Handle special column name formats (like lists)
            if rule['column_name'].startswith('[') and rule['column_name'].endswith(']'):
                context['is_multi_column'] = True
                context['columns_list'] = self._parse_column_list(rule['column_name'])
            else:
                context['is_multi_column'] = False
                context['columns_list'] = [rule['column_name']]
            
            return context
            
        except Exception as e:
            self.logger.error(f"Failed to extract rule context: {str(e)}")
            return {}
    
    def _categorize_attribute(self, attribute: str) -> str:
        """Categorize attribute into rule types"""
        attribute_lower = attribute.lower()
        
        if 'duplicate' in attribute_lower:
            return 'duplicate_check'
        elif any(word in attribute_lower for word in ['min', 'max', 'range']):
            return 'range_validation'
        elif 'distinct' in attribute_lower:
            return 'distinct_validation'
        elif 'percentile' in attribute_lower:
            return 'statistical_validation'
        elif any(word in attribute_lower for word in ['sum', 'average', 'mean']):
            return 'aggregate_validation'
        else:
            return 'other'
    
    def _parse_column_list(self, column_str: str) -> List[str]:
        """Parse column list string into individual column names"""
        try:
            # Remove brackets and split by comma
            cleaned = column_str.strip('[]')
            # Handle quoted column names
            columns = [col.strip().strip("'\"") for col in cleaned.split(',')]
            return [col for col in columns if col]
        except Exception as e:
            self.logger.warning(f"Failed to parse column list: {column_str}. Error: {e}")
            return [column_str]
    
    def _extract_business_context(self, rule: Dict) -> Dict[str, Any]:
        """Extract business context from rule information"""
        context = {
            'data_source': self._extract_data_source(rule['filename']),
            'column_category': self._categorize_column_name(rule['column_name']),
            'validation_criticality': self._assess_criticality(rule['attribute'])
        }
        return context
    
    def _extract_data_source(self, filename: str) -> str:
        """Extract data source information from filename"""
        if 'circana' in filename.lower():
            return 'Circana'
        elif 'gp_' in filename.lower():
            return 'GP'
        else:
            return 'Unknown'
    
    def _categorize_column_name(self, column_name: str) -> str:
        """Categorize column based on name patterns"""
        column_lower = column_name.lower()
        
        if any(word in column_lower for word in ['time', 'date']):
            return 'temporal'
        elif any(word in column_lower for word in ['geography', 'location']):
            return 'geographic'
        elif any(word in column_lower for word in ['product', 'upc', 'brand']):
            return 'product'
        elif any(word in column_lower for word in ['units', 'volume', 'sales']):
            return 'metric'
        else:
            return 'dimension'
    
    def _assess_criticality(self, attribute: str) -> str:
        """Assess the criticality level of the validation rule"""
        attribute_lower = attribute.lower()
        
        if 'duplicate' in attribute_lower:
            return 'high'  # Data integrity is critical
        elif any(word in attribute_lower for word in ['min', 'max']):
            return 'medium'  # Range validations are important
        elif 'distinct' in attribute_lower:
            return 'medium'  # Data quality checks
        else:
            return 'low'  # Statistical validations are informational
    
    def validate_input_format(self, df: pd.DataFrame) -> Tuple[bool, List[str]]:
        """Validate that input DataFrame has correct format"""
        issues = []
        
        # Check required columns
        required_columns = ['Filename', 'Column Type', 'Column Name', 'Attribute']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            issues.append(f"Missing required columns: {missing_columns}")
        
        # Check for empty DataFrame
        if df.empty:
            issues.append("Input file is empty")
        
        # Check for completely empty rows
        empty_rows = df.isnull().all(axis=1).sum()
        if empty_rows > 0:
            issues.append(f"Found {empty_rows} completely empty rows")
        
        # Check for valid column types
        if 'Column Type' in df.columns:
            valid_types = ['All', 'Date', 'String', 'Numeric']
            invalid_types = set(df['Column Type'].dropna().unique()) - set(valid_types)
            if invalid_types:
                issues.append(f"Invalid column types found: {list(invalid_types)}")
        
        is_valid = len(issues) == 0
        return is_valid, issues
