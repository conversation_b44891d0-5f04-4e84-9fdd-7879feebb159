"""
CSV to DataFrame Converter

A robust utility to convert CSV files to pandas DataFrames with automatic
encoding and delimiter detection, handling various file formats and sizes.

"""

import os
import csv
import pandas as pd
import chardet
import re
from typing import Optional, Tuple, Union, Dict, List
import logging
import warnings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CSVToDataFrameConverter:
    """
    A class to convert CSV files to pandas DataFrames with intelligent format detection.
    """
    
    def __init__(self):
        self.detected_encoding = None
        self.detected_delimiter = None
        self.detected_format = {}
    
    def detect_encoding(self, file_path: str, sample_size: int = 100000) -> str:
        """
        Detect file encoding using chardet library.
        
        Args:
            file_path (str): Path to the CSV file
            sample_size (int): Number of bytes to sample for detection
            
        Returns:
            str: Detected encoding
        """
        try:
            with open(file_path, 'rb') as file:
                raw_data = file.read(sample_size)
                result = chardet.detect(raw_data)
                encoding = result['encoding']
                confidence = result['confidence']
                
                logger.info(f"Detected encoding: {encoding} (confidence: {confidence:.2f})")
                
                # Fallback encodings if confidence is low
                if confidence < 0.7:
                    fallback_encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
                    for fallback in fallback_encodings:
                        try:
                            with open(file_path, 'r', encoding=fallback) as test_file:
                                test_file.read(1000)
                            logger.info(f"Using fallback encoding: {fallback}")
                            encoding = fallback
                            break
                        except UnicodeDecodeError:
                            continue
                
                self.detected_encoding = encoding
                return encoding
                
        except Exception as e:
            logger.warning(f"Encoding detection failed: {e}. Using utf-8 as fallback.")
            self.detected_encoding = 'utf-8'
            return 'utf-8'
    
    def intelligent_delimiter_detection(self, sample_data: List[str]) -> str:
        """
        Intelligently detect delimiter by analyzing patterns and characteristics.
        
        Args:
            sample_data (List[str]): Sample lines from the file
            
        Returns:
            str: Detected delimiter
        """
        if not sample_data:
            return ','
        
        # Find all potential delimiter characters
        potential_delimiters = set()
        
        for line in sample_data[:5]:
            for char in line:
                if char in ',.;:|!@#$%^&*()+=[]{}\\/-~`<>?':
                    if line.count(char) > 1:
                        potential_delimiters.add(char)
        
        # Score each potential delimiter
        delimiter_scores = {}
        
        for delimiter in potential_delimiters:
            scores = []
            field_counts = []
            
            for line in sample_data[:7]:
                if not line.strip():
                    continue
                    
                fields = line.split(delimiter)
                field_count = len(fields)
                field_counts.append(field_count)
                
                score = 0
                
                # Consistency scoring
                if len(field_counts) > 1:
                    consistency = 1.0 - (max(field_counts) - min(field_counts)) / max(field_counts)
                    score += consistency * 40
                
                # Field count scoring
                avg_fields = sum(field_counts) / len(field_counts)
                if 2 <= avg_fields <= 50:
                    score += min(avg_fields * 2, 20)
                elif avg_fields > 50:
                    score -= 10
                
                # Field content quality
                non_empty_fields = [f.strip() for f in fields if f.strip()]
                if len(non_empty_fields) >= 2:
                    score += 10
                
                scores.append(score)
            
            if scores:
                avg_score = sum(scores) / len(scores)
                
                # Bonus for consistency
                if len(set(field_counts)) == 1:
                    avg_score += 20
                elif len(set(field_counts)) <= 2:
                    avg_score += 10
                
                delimiter_scores[delimiter] = avg_score
        
        # Common delimiter preferences
        common_delimiters = {',': 5, ';': 4, '\t': 4, '|': 3, ':': 2}
        for delim, bonus in common_delimiters.items():
            if delim in delimiter_scores:
                delimiter_scores[delim] += bonus
        
        # Return best delimiter
        if delimiter_scores:
            sorted_delimiters = sorted(delimiter_scores.items(), key=lambda x: x[1], reverse=True)
            
            # Validate with csv.Sniffer
            sample_text = '\n'.join(sample_data[:5])
            sniffer = csv.Sniffer()
            
            for delimiter, score in sorted_delimiters[:3]:
                try:
                    dialect = sniffer.sniff(sample_text, delimiters=delimiter)
                    if dialect.delimiter == delimiter:
                        logger.info(f"Detected delimiter: '{delimiter}' (score: {score:.1f})")
                        return delimiter
                except csv.Error:
                    continue
            
            best_delimiter = sorted_delimiters[0][0]
            logger.info(f"Selected delimiter: '{best_delimiter}' (score: {sorted_delimiters[0][1]:.1f})")
            return best_delimiter
        
        # Final fallback
        logger.warning("Could not detect delimiter, using comma as default")
        return ','
    
    def sample_file(self, file_path: str, encoding: str, num_lines: int = 15) -> List[str]:
        """
        Sample first few lines of the file for format detection.
        """
        try:
            with open(file_path, 'r', encoding=encoding) as file:
                lines = []
                for i, line in enumerate(file):
                    if i >= num_lines:
                        break
                    lines.append(line.rstrip('\n\r'))
                return lines
        except Exception as e:
            logger.error(f"Failed to sample file: {e}")
            raise
    
    def analyze_column_consistency(self, sample_data: List[str], delimiter: str) -> Dict:
        """
        Analyze column consistency across rows to detect issues.
        
        Args:
            sample_data (List[str]): Sample lines from the file
            delimiter (str): Detected delimiter
            
        Returns:
            Dict: Analysis results including consistency info
        """
        if not sample_data:
            return {'consistent': True, 'max_columns': 0, 'column_counts': []}
        
        column_counts = []
        line_analysis = []
        
        for i, line in enumerate(sample_data[:20]):  # Analyze more lines
            if not line.strip():
                continue
                
            # Split by delimiter and count fields
            fields = line.split(delimiter)
            field_count = len(fields)
            column_counts.append(field_count)
            
            # Analyze field content
            non_empty_fields = [f.strip() for f in fields if f.strip()]
            empty_fields = field_count - len(non_empty_fields)
            
            line_analysis.append({
                'line_num': i + 1,
                'total_fields': field_count,
                'non_empty_fields': len(non_empty_fields),
                'empty_fields': empty_fields,
                'line_content': line[:100] + '...' if len(line) > 100 else line
            })
        
        if not column_counts:
            return {'consistent': True, 'max_columns': 0, 'column_counts': []}
        
        max_columns = max(column_counts)
        min_columns = min(column_counts)
        most_common_count = max(set(column_counts), key=column_counts.count)
        
        # Determine consistency
        consistent = (max_columns == min_columns)
        
        # Find problematic lines
        problematic_lines = []
        for analysis in line_analysis:
            if analysis['total_fields'] != most_common_count:
                problematic_lines.append(analysis)
        
        analysis_result = {
            'consistent': consistent,
            'max_columns': max_columns,
            'min_columns': min_columns,
            'most_common_count': most_common_count,
            'column_counts': column_counts,
            'problematic_lines': problematic_lines,
            'total_lines_analyzed': len(column_counts)
        }
        
        if not consistent:
            logger.warning(f"Inconsistent column counts detected:")
            logger.warning(f"  - Range: {min_columns} to {max_columns} columns")
            logger.warning(f"  - Most common: {most_common_count} columns")
            logger.warning(f"  - Problematic lines: {len(problematic_lines)}")
            
            for prob_line in problematic_lines[:3]:  # Show first 3 problematic lines
                logger.warning(f"    Line {prob_line['line_num']}: {prob_line['total_fields']} fields - {prob_line['line_content']}")
        
        return analysis_result

    def detect_csv_format(self, file_path: str, encoding: str) -> Dict:
        """
        Detect CSV format parameters using intelligent detection with consistency analysis.
        """
        try:
            sample_data = self.sample_file(file_path, encoding, 25)  # Sample more lines
            
            if not sample_data:
                raise ValueError("No data found in file")
            
            # Intelligent delimiter detection
            delimiter = self.intelligent_delimiter_detection(sample_data)
            
            # Analyze column consistency
            consistency_analysis = self.analyze_column_consistency(sample_data, delimiter)
            
            # Detect quote character
            sample_text = '\n'.join(sample_data[:7])
            sniffer = csv.Sniffer()
            
            try:
                dialect = sniffer.sniff(sample_text, delimiters=delimiter)
                quotechar = dialect.quotechar
                quoting = dialect.quoting
            except (csv.Error, Exception):
                quotechar = '"'
                quoting = csv.QUOTE_MINIMAL
                
                # Smart quote detection
                for line in sample_data[:3]:
                    if line.count('"') >= 2:
                        quotechar = '"'
                        break
                    elif line.count("'") >= 2:
                        quotechar = "'"
                        break
            
            # Detect header
            has_header = self.detect_header(sample_data, delimiter)
            
            format_params = {
                'delimiter': delimiter,
                'quotechar': quotechar,
                'quoting': quoting,
                'has_header': has_header,
                'encoding': encoding,
                'consistency_analysis': consistency_analysis
            }
            
            self.detected_format = format_params
            self.detected_delimiter = delimiter
            
            logger.info(f"CSV format: delimiter='{delimiter}', quotechar='{quotechar}', header={has_header}")
            
            if not consistency_analysis['consistent']:
                logger.info(f"Inconsistent columns detected - will use error handling strategies")
            
            return format_params
            
        except Exception as e:
            logger.warning(f"Format detection failed: {e}. Using defaults.")
            return {
                'delimiter': ',',
                'quotechar': '"',
                'quoting': csv.QUOTE_MINIMAL,
                'has_header': True,
                'encoding': encoding,
                'consistency_analysis': {'consistent': True, 'max_columns': 0}
            }
    
    def detect_header(self, sample_data: List[str], delimiter: str) -> bool:
        """
        Detect if the first row is a header.
        """
        if len(sample_data) < 2:
            return True
        
        try:
            first_row = sample_data[0].split(delimiter)
            second_row = sample_data[1].split(delimiter) if len(sample_data) > 1 else []
            
            header_score = 0
            
            # Check numeric content
            first_row_numeric = sum(1 for field in first_row if self.is_numeric(field.strip()))
            if len(sample_data) > 1:
                second_row_numeric = sum(1 for field in second_row if self.is_numeric(field.strip()))
                if first_row_numeric < second_row_numeric:
                    header_score += 20
            
            # Check for header patterns
            header_indicators = ['id', 'name', 'date', 'time', 'value', 'count', 'total', 'amount']
            for field in first_row:
                field_lower = field.strip().lower()
                if any(indicator in field_lower for indicator in header_indicators):
                    header_score += 10
                
                if '_' in field or (field.strip() and field.strip()[0].isupper()):
                    header_score += 5
            
            # Check if first row is all strings
            if first_row_numeric == 0 and len(first_row) > 1:
                header_score += 15
            
            # Length consistency
            if len(first_row) == len(second_row):
                header_score += 10
            
            return header_score >= 20
            
        except Exception:
            return True
    
    def is_numeric(self, value: str) -> bool:
        """Check if a string represents a numeric value."""
        try:
            float(value.replace(',', ''))
            return True
        except ValueError:
            return False
    
    def estimate_memory_usage(self, file_path: str) -> Tuple[float, bool]:
        """
        Estimate if file needs chunked processing.
        
        Returns:
            Tuple[float, bool]: (file_size_mb, needs_chunking)
        """
        try:
            file_size = os.path.getsize(file_path)
            file_size_mb = file_size / (1024 * 1024)
            
            # Consider chunking for files > 200MB
            needs_chunking = file_size_mb > 200
            
            logger.info(f"File size: {file_size_mb:.2f} MB")
            if needs_chunking:
                logger.info("Large file detected - will use chunked processing if requested")
            
            return file_size_mb, needs_chunking
            
        except Exception as e:
            logger.warning(f"Could not estimate file size: {e}")
            return 0, False


def csv_to_dataframe(csv_path: str,
                    encoding: Optional[str] = None,
                    delimiter: Optional[str] = None,
                    auto_detect: bool = True,
                    chunk_size: Optional[int] = None,
                    nrows: Optional[int] = None,
                    handle_bad_lines: str = 'auto',
                    fill_missing_columns: bool = True,
                    **pandas_kwargs) -> Union[pd.DataFrame, pd.io.parsers.readers.TextFileReader]:
    """
    Convert CSV file to pandas DataFrame with intelligent format detection and error handling.
    
    Args:
        csv_path (str): Path to the CSV file
        encoding (Optional[str]): File encoding (auto-detected if None)
        delimiter (Optional[str]): CSV delimiter (auto-detected if None)
        auto_detect (bool): Enable automatic format detection
        chunk_size (Optional[int]): If specified, return iterator for chunked processing
        nrows (Optional[int]): Number of rows to read (None for all)
        handle_bad_lines (str): How to handle inconsistent rows:
            - 'auto': Automatically choose best strategy
            - 'skip': Skip bad lines with warning
            - 'error': Raise error on bad lines
            - 'fill': Fill missing columns with NaN
        fill_missing_columns (bool): Fill missing columns with NaN values
        **pandas_kwargs: Additional arguments passed to pd.read_csv()
        
    Returns:
        Union[pd.DataFrame, TextFileReader]: DataFrame or chunk iterator
        
    Examples:
        # Basic usage with auto error handling
        df = csv_to_dataframe('inconsistent_data.csv')
        
        # Skip problematic lines
        df = csv_to_dataframe('data.csv', handle_bad_lines='skip')
        
        # Fill missing columns with NaN
        df = csv_to_dataframe('data.csv', handle_bad_lines='fill')
        
        # For large files with error handling
        chunk_iterator = csv_to_dataframe('large_data.csv', 
                                        chunk_size=10000,
                                        handle_bad_lines='auto')
    """
    
    # Validate file exists
    if not os.path.exists(csv_path):
        raise FileNotFoundError(f"CSV file not found: {csv_path}")
    
    converter = CSVToDataFrameConverter()
    
    try:
        # Auto-detect encoding if not specified
        if auto_detect and encoding is None:
            encoding = converter.detect_encoding(csv_path)
        elif encoding is None:
            encoding = 'utf-8'
        
        # Auto-detect CSV format if not specified
        if auto_detect:
            format_params = converter.detect_csv_format(csv_path, encoding)
            
            # Use detected parameters if not manually specified
            if delimiter is None:
                delimiter = format_params['delimiter']
            
            detected_quotechar = format_params['quotechar']
            detected_header = 0 if format_params['has_header'] else None
            consistency_analysis = format_params.get('consistency_analysis', {'consistent': True})
            
        else:
            # Use defaults
            delimiter = delimiter or ','
            detected_quotechar = '"'
            detected_header = 0
            consistency_analysis = {'consistent': True}
        
        # Estimate file size
        file_size_mb, suggested_chunking = converter.estimate_memory_usage(csv_path)
        
        if suggested_chunking and chunk_size is None:
            logger.info(f"Large file detected ({file_size_mb:.1f}MB). Consider using chunk_size parameter.")
        
        # Prepare pandas read_csv arguments
        read_args = {
            'encoding': encoding,
            'sep': delimiter,
            'quotechar': detected_quotechar,
            'header': detected_header,
        }
        
        # Handle inconsistent columns
        if not consistency_analysis['consistent'] or handle_bad_lines != 'error':
            
            if handle_bad_lines == 'auto':
                # Choose strategy based on analysis
                problematic_ratio = len(consistency_analysis.get('problematic_lines', [])) / max(1, consistency_analysis.get('total_lines_analyzed', 1))
                
                if problematic_ratio > 0.1:  # More than 10% problematic lines
                    strategy = 'fill'
                    logger.info("Auto-selected strategy: Fill missing columns (many inconsistent lines)")
                else:
                    strategy = 'skip'
                    logger.info("Auto-selected strategy: Skip bad lines (few inconsistent lines)")
            else:
                strategy = handle_bad_lines
            
            # Apply error handling strategy
            if strategy == 'skip':
                # Pandas 2.0+ syntax
                if hasattr(pd, '__version__') and pd.__version__.startswith(('2.', '1.5', '1.4')):
                    read_args['on_bad_lines'] = 'skip'
                else:
                    read_args['error_bad_lines'] = False
                    read_args['warn_bad_lines'] = True
                
                logger.info("Will skip lines with inconsistent column counts")
                
            elif strategy == 'fill':
                # Read with maximum columns and fill missing
                max_cols = consistency_analysis.get('max_columns', 0)
                if max_cols > 0:
                    # Generate column names for maximum columns
                    if detected_header == 0:
                        # Will read header from file, let pandas handle it
                        pass
                    else:
                        # No header, create column names
                        read_args['names'] = [f'col_{i}' for i in range(max_cols)]
                
                # Use pandas' built-in handling
                if hasattr(pd, '__version__') and pd.__version__.startswith(('2.', '1.5', '1.4')):
                    read_args['on_bad_lines'] = 'skip'  # Still skip completely malformed lines
                else:
                    read_args['error_bad_lines'] = False
                
                logger.info(f"Will fill missing columns up to {max_cols} columns")
        
        # Add optional parameters
        if chunk_size is not None:
            read_args['chunksize'] = chunk_size
        if nrows is not None:
            read_args['nrows'] = nrows
        
        # Override with any user-provided pandas arguments
        read_args.update(pandas_kwargs)
        
        # Read the CSV
        logger.info(f"Reading CSV with parameters: delimiter='{delimiter}', encoding='{encoding}', error_handling='{handle_bad_lines}'")
        
        try:
            result = pd.read_csv(csv_path, **read_args)
            
            # Post-process for filling strategy
            if (not consistency_analysis['consistent'] and 
                handle_bad_lines in ['fill', 'auto'] and 
                chunk_size is None):  # Don't post-process chunks
                
                result = _post_process_inconsistent_data(result, consistency_analysis, fill_missing_columns)
            
            if chunk_size is not None:
                logger.info(f"Returning chunk iterator with chunk_size={chunk_size}")
                return result
            else:
                logger.info(f"Successfully loaded DataFrame with shape: {result.shape}")
                return result
                
        except pd.errors.ParserError as pe:
            logger.warning(f"Pandas parser error: {pe}")
            
            # Try more aggressive error handling
            logger.info("Attempting recovery with aggressive error handling...")
            
            recovery_args = read_args.copy()
            
            # Use most permissive settings
            if hasattr(pd, '__version__') and pd.__version__.startswith(('2.', '1.5', '1.4')):
                recovery_args['on_bad_lines'] = 'skip'
            else:
                recovery_args['error_bad_lines'] = False
                recovery_args['warn_bad_lines'] = True
            
            # Remove any columns specifications that might cause issues
            recovery_args.pop('names', None)
            
            # Try with python engine (more forgiving)
            recovery_args['engine'] = 'python'
            
            result = pd.read_csv(csv_path, **recovery_args)
            
            logger.info(f"Recovery successful. DataFrame shape: {result.shape}")
            
            if chunk_size is not None:
                return result
            else:
                return result
            
    except Exception as e:
        logger.error(f"Failed to convert CSV to DataFrame: {e}")
        
        # Final fallback attempt with most basic parameters
        logger.info("Attempting final fallback with minimal parameters...")
        try:
            fallback_args = {
                'encoding': 'utf-8',
                'sep': ',',
                'engine': 'python',
            }
            
            # Add most permissive error handling
            if hasattr(pd, '__version__') and pd.__version__.startswith(('2.', '1.5', '1.4')):
                fallback_args['on_bad_lines'] = 'skip'
            else:
                fallback_args['error_bad_lines'] = False
            
            if chunk_size is not None:
                fallback_args['chunksize'] = chunk_size
            if nrows is not None:
                fallback_args['nrows'] = nrows
            
            fallback_result = pd.read_csv(csv_path, **fallback_args)
            logger.info(f"Fallback successful. DataFrame shape: {fallback_result.shape if chunk_size is None else 'chunked'}")
            return fallback_result
            
        except Exception as fallback_error:
            logger.error(f"All attempts failed. Final error: {fallback_error}")
            raise


def _post_process_inconsistent_data(df: pd.DataFrame, consistency_analysis: Dict, fill_missing: bool) -> pd.DataFrame:
    """
    Post-process DataFrame loaded from inconsistent CSV data.
    
    Args:
        df (pd.DataFrame): Loaded DataFrame
        consistency_analysis (Dict): Analysis results
        fill_missing (bool): Whether to fill missing columns
        
    Returns:
        pd.DataFrame: Processed DataFrame
    """
    if not fill_missing or consistency_analysis.get('consistent', True):
        return df
    
    max_cols = consistency_analysis.get('max_columns', len(df.columns))
    current_cols = len(df.columns)
    
    if max_cols > current_cols:
        # Add missing columns
        for i in range(current_cols, max_cols):
            col_name = f'unnamed_col_{i}'
            # Ensure unique column name
            counter = 1
            while col_name in df.columns:
                col_name = f'unnamed_col_{i}_{counter}'
                counter += 1
            
            df[col_name] = pd.NA
        
        logger.info(f"Added {max_cols - current_cols} missing columns to maintain consistency")
    
    return df


# Convenience functions for common use cases
def quick_csv_load(csv_path: str, handle_bad_lines: str = 'auto') -> pd.DataFrame:
    """
    Quick CSV loading with full auto-detection and error handling.
    
    Args:
        csv_path (str): Path to the CSV file
        handle_bad_lines (str): How to handle inconsistent rows
        
    Returns:
        pd.DataFrame: Loaded DataFrame
    """
    return csv_to_dataframe(csv_path, auto_detect=True, handle_bad_lines=handle_bad_lines)


def large_csv_load(csv_path: str, chunk_size: int = 10000, handle_bad_lines: str = 'auto'):
    """
    Load large CSV files in chunks with error handling.
    
    Args:
        csv_path (str): Path to the CSV file
        chunk_size (int): Size of each chunk
        handle_bad_lines (str): How to handle inconsistent rows
        
    Returns:
        Iterator: Chunk iterator
    """
    return csv_to_dataframe(csv_path, auto_detect=True, chunk_size=chunk_size, handle_bad_lines=handle_bad_lines)


def csv_sample(csv_path: str, nrows: int = 1000, handle_bad_lines: str = 'auto') -> pd.DataFrame:
    """
    Load a sample of rows from a CSV file with error handling.
    
    Args:
        csv_path (str): Path to the CSV file
        nrows (int): Number of rows to sample
        handle_bad_lines (str): How to handle inconsistent rows
        
    Returns:
        pd.DataFrame: Sample DataFrame
    """
    return csv_to_dataframe(csv_path, auto_detect=True, nrows=nrows, handle_bad_lines=handle_bad_lines)


def robust_csv_load(csv_path: str, **kwargs) -> pd.DataFrame:
    """
    Most robust CSV loading - tries multiple strategies to load problematic files.
    
    Args:
        csv_path (str): Path to the CSV file
        **kwargs: Additional arguments
        
    Returns:
        pd.DataFrame: Loaded DataFrame
    """
    strategies = [
        {'handle_bad_lines': 'auto'},
        {'handle_bad_lines': 'fill', 'engine': 'python'},
        {'handle_bad_lines': 'skip', 'engine': 'python'},
        {'delimiter': ',', 'handle_bad_lines': 'skip', 'engine': 'python'},
        {'delimiter': ';', 'handle_bad_lines': 'skip', 'engine': 'python'},
        {'delimiter': '\t', 'handle_bad_lines': 'skip', 'engine': 'python'},
    ]
    
    last_error = None
    
    for i, strategy in enumerate(strategies):
        try:
            strategy.update(kwargs)  # Override with user parameters
            logger.info(f"Trying robust loading strategy {i+1}: {strategy}")
            
            df = csv_to_dataframe(csv_path, auto_detect=True, **strategy)
            logger.info(f"Strategy {i+1} successful!")
            return df
            
        except Exception as e:
            last_error = e
            logger.warning(f"Strategy {i+1} failed: {e}")
            continue
    
    logger.error("All robust loading strategies failed")
    raise last_error if last_error else Exception("All loading strategies failed")


# Example usage
if __name__ == "__main__":
    # Example 1: Basic usage with error handling
    try:
        # This will automatically handle inconsistent columns
        df = csv_to_dataframe('inconsistent_data.csv')
        print(f"Loaded DataFrame with shape: {df.shape}")
        print(f"Columns: {list(df.columns)}")
        
    except Exception as e:
        print(f"Error: {e}")
    
    # Example 2: Different error handling strategies
    try:
        # Skip bad lines
        df_skip = csv_to_dataframe('problematic_data.csv', handle_bad_lines='skip')
        print(f"Skip strategy - Shape: {df_skip.shape}")
        
        # Fill missing columns
        df_fill = csv_to_dataframe('problematic_data.csv', handle_bad_lines='fill')
        print(f"Fill strategy - Shape: {df_fill.shape}")
        
        # Auto-select strategy
        df_auto = csv_to_dataframe('problematic_data.csv', handle_bad_lines='auto')
        print(f"Auto strategy - Shape: {df_auto.shape}")
        
    except Exception as e:
        print(f"Error in error handling examples: {e}")
    
    # Example 3: Robust loading for very problematic files
    try:
        df = robust_csv_load('very_messy_data.csv')
        print(f"Robust loading successful! Shape: {df.shape}")
        
    except Exception as e:
        print(f"Even robust loading failed: {e}")
    
    # Example 4: Large file with error handling
    try:
        chunk_iterator = csv_to_dataframe('large_inconsistent_data.csv', 
                                        chunk_size=5000,
                                        handle_bad_lines='auto')
        
        total_rows = 0
        for i, chunk in enumerate(chunk_iterator):
            total_rows += len(chunk)
            print(f"Processed chunk {i+1} with {len(chunk)} rows")
            
            if i >= 2:  # Just process first 3 chunks for example
                break
        
        print(f"Total rows processed: {total_rows}")
        
    except Exception as e:
        print(f"Error in chunk processing: {e}")
    
    # Example 5: Analyzing problematic files
    try:
        # Load a sample to understand the issues
        sample_df = csv_sample('problematic_data.csv', nrows=100)
        print(f"Sample loaded: {sample_df.shape}")
        print(f"Sample columns: {list(sample_df.columns)}")
        
        # Check for missing values
        print(f"Missing values per column:")
        print(sample_df.isnull().sum())
        
    except Exception as e:
        print(f"Error in analysis: {e}")