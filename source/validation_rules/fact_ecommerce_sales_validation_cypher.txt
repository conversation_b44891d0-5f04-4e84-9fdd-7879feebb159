// ========================================
// DATA QUALITY VALIDATION RULES FOR FACT_ECOMMERCE_SALES
// This script creates validation rules and links them to columns
// ========================================

// Create constraint for ValidationRule uniqueness (run once if not exists)
CREATE CONSTRAINT validation_rule_unique IF NOT EXISTS
FOR (v:ValidationRule) REQUIRE v.rule_id IS UNIQUE;

// ========================================
// CORE E-COMMERCE IDENTIFIERS
// ========================================

// ECOMMERCE_TRANSACTION_ID - NOT NULL
MERGE (v1:ValidationRule {rule_id: "VAL_ECOMMERCE_TRANSACTION_ID_001"})
SET v1.rule_name = "ECOMMERCE_TRANSACTION_ID Not Null Check",
    v1.column_name = "ECOMMERCE_TRANSACTION_ID",
    v1.table_id = "FACT_ECOMMERCE_SALES",
    v1.validation_type = "NOT_NULL",
    v1.validation_rule = "NOT_NULL",
    v1.error_message = "E-Commerce Transaction ID is required",
    v1.business_criticality = "critical",
    v1.priority = "P1",
    v1.applicable_domains = ["ALL"],
    v1.is_active = true,
    v1.created_date = datetime(),
    v1.last_updated_date = datetime()
WITH v1
MATCH (c:Column {column_id: "COL_ECOMMERCE_TRANSACTION_ID_FACT_4001"})
MERGE (c)-[:HAS_VALIDATION]->(v1);

// ECOMMERCE_TRANSACTION_ID - UNIQUE
MERGE (v2:ValidationRule {rule_id: "VAL_ECOMMERCE_TRANSACTION_ID_002"})
SET v2.rule_name = "ECOMMERCE_TRANSACTION_ID Uniqueness Check",
    v2.column_name = "ECOMMERCE_TRANSACTION_ID",
    v2.table_id = "FACT_ECOMMERCE_SALES",
    v2.validation_type = "UNIQUE",
    v2.validation_rule = "UNIQUE",
    v2.error_message = "E-Commerce Transaction ID must be unique",
    v2.business_criticality = "critical",
    v2.priority = "P1",
    v2.applicable_domains = ["ALL"],
    v2.is_active = true,
    v2.created_date = datetime(),
    v2.last_updated_date = datetime()
WITH v2
MATCH (c:Column {column_id: "COL_ECOMMERCE_TRANSACTION_ID_FACT_4001"})
MERGE (c)-[:HAS_VALIDATION]->(v2);

// DATE_KEY - NOT NULL
MERGE (v3:ValidationRule {rule_id: "VAL_DATE_KEY_FACT_001"})
SET v3.rule_name = "DATE_KEY Not Null Check",
    v3.column_name = "DATE_KEY",
    v3.table_id = "FACT_ECOMMERCE_SALES",
    v3.validation_type = "NOT_NULL",
    v3.validation_rule = "NOT_NULL",
    v3.error_message = "Date Key is required for temporal analysis",
    v3.business_criticality = "critical",
    v3.priority = "P1",
    v3.applicable_domains = ["ALL"],
    v3.is_active = true,
    v3.created_date = datetime(),
    v3.last_updated_date = datetime()
WITH v3
MATCH (c:Column {column_id: "COL_DATE_KEY_FACT_4002"})
MERGE (c)-[:HAS_VALIDATION]->(v3);

// CUSTOMER_KEY - NOT NULL
MERGE (v4:ValidationRule {rule_id: "VAL_CUSTOMER_KEY_FACT_001"})
SET v4.rule_name = "CUSTOMER_KEY Not Null Check",
    v4.column_name = "CUSTOMER_KEY",
    v4.table_id = "FACT_ECOMMERCE_SALES",
    v4.validation_type = "NOT_NULL",
    v4.validation_rule = "NOT_NULL",
    v4.error_message = "Customer Key is required for customer tracking",
    v4.business_criticality = "critical",
    v4.priority = "P1",
    v4.applicable_domains = ["ALL"],
    v4.is_active = true,
    v4.created_date = datetime(),
    v4.last_updated_date = datetime()
WITH v4
MATCH (c:Column {column_id: "COL_CUSTOMER_KEY_FACT_4003"})
MERGE (c)-[:HAS_VALIDATION]->(v4);

// PRODUCT_HIERARCHY_KEY - NOT NULL
MERGE (v5:ValidationRule {rule_id: "VAL_PRODUCT_HIERARCHY_KEY_001"})
SET v5.rule_name = "PRODUCT_HIERARCHY_KEY Not Null Check",
    v5.column_name = "PRODUCT_HIERARCHY_KEY",
    v5.table_id = "FACT_ECOMMERCE_SALES",
    v5.validation_type = "NOT_NULL",
    v5.validation_rule = "NOT_NULL",
    v5.error_message = "Product Hierarchy Key is required for product tracking",
    v5.business_criticality = "critical",
    v5.priority = "P1",
    v5.applicable_domains = ["ALL"],
    v5.is_active = true,
    v5.created_date = datetime(),
    v5.last_updated_date = datetime()
WITH v5
MATCH (c:Column {column_id: "COL_PRODUCT_HIERARCHY_KEY_FACT_4004"})
MERGE (c)-[:HAS_VALIDATION]->(v5);

// ========================================
// CORE SALES MEASURES VALIDATIONS
// ========================================

// GROSS_SALES_AMOUNT - NOT NULL
MERGE (v6:ValidationRule {rule_id: "VAL_GROSS_SALES_AMOUNT_001"})
SET v6.rule_name = "GROSS_SALES_AMOUNT Not Null Check",
    v6.column_name = "GROSS_SALES_AMOUNT",
    v6.table_id = "FACT_ECOMMERCE_SALES",
    v6.validation_type = "NOT_NULL",
    v6.validation_rule = "NOT_NULL",
    v6.error_message = "Gross Sales Amount is required",
    v6.business_criticality = "critical",
    v6.priority = "P1",
    v6.applicable_domains = ["ALL"],
    v6.is_active = true,
    v6.created_date = datetime(),
    v6.last_updated_date = datetime()
WITH v6
MATCH (c:Column {column_id: "COL_GROSS_SALES_AMOUNT_FACT_4006"})
MERGE (c)-[:HAS_VALIDATION]->(v6);

// GROSS_SALES_AMOUNT - RANGE
MERGE (v7:ValidationRule {rule_id: "VAL_GROSS_SALES_AMOUNT_002"})
SET v7.rule_name = "GROSS_SALES_AMOUNT Range Check",
    v7.column_name = "GROSS_SALES_AMOUNT",
    v7.table_id = "FACT_ECOMMERCE_SALES",
    v7.validation_type = "RANGE",
    v7.validation_rule = "RANGE:0,9999999.99",
    v7.error_message = "Gross Sales Amount must be between 0 and 9999999.99",
    v7.business_criticality = "critical",
    v7.priority = "P1",
    v7.applicable_domains = ["ALL"],
    v7.is_active = true,
    v7.created_date = datetime(),
    v7.last_updated_date = datetime()
WITH v7
MATCH (c:Column {column_id: "COL_GROSS_SALES_AMOUNT_FACT_4006"})
MERGE (c)-[:HAS_VALIDATION]->(v7);

// NET_SALES_AMOUNT - NOT NULL
MERGE (v8:ValidationRule {rule_id: "VAL_NET_SALES_AMOUNT_001"})
SET v8.rule_name = "NET_SALES_AMOUNT Not Null Check",
    v8.column_name = "NET_SALES_AMOUNT",
    v8.table_id = "FACT_ECOMMERCE_SALES",
    v8.validation_type = "NOT_NULL",
    v8.validation_rule = "NOT_NULL",
    v8.error_message = "Net Sales Amount is required",
    v8.business_criticality = "critical",
    v8.priority = "P1",
    v8.applicable_domains = ["ALL"],
    v8.is_active = true,
    v8.created_date = datetime(),
    v8.last_updated_date = datetime()
WITH v8
MATCH (c:Column {column_id: "COL_NET_SALES_AMOUNT_FACT_4007"})
MERGE (c)-[:HAS_VALIDATION]->(v8);

// QUANTITY_SOLD - NOT NULL
MERGE (v9:ValidationRule {rule_id: "VAL_QUANTITY_SOLD_001"})
SET v9.rule_name = "QUANTITY_SOLD Not Null Check",
    v9.column_name = "QUANTITY_SOLD",
    v9.table_id = "FACT_ECOMMERCE_SALES",
    v9.validation_type = "NOT_NULL",
    v9.validation_rule = "NOT_NULL",
    v9.error_message = "Quantity Sold is required",
    v9.business_criticality = "critical",
    v9.priority = "P1",
    v9.applicable_domains = ["ALL"],
    v9.is_active = true,
    v9.created_date = datetime(),
    v9.last_updated_date = datetime()
WITH v9
MATCH (c:Column {column_id: "COL_QUANTITY_SOLD_FACT_4008"})
MERGE (c)-[:HAS_VALIDATION]->(v9);

// QUANTITY_SOLD - RANGE
MERGE (v10:ValidationRule {rule_id: "VAL_QUANTITY_SOLD_002"})
SET v10.rule_name = "QUANTITY_SOLD Range Check",
    v10.column_name = "QUANTITY_SOLD",
    v10.table_id = "FACT_ECOMMERCE_SALES",
    v10.validation_type = "RANGE",
    v10.validation_rule = "RANGE:0.001,99999.999",
    v10.error_message = "Quantity Sold must be positive and less than 100000",
    v10.business_criticality = "critical",
    v10.priority = "P1",
    v10.applicable_domains = ["ALL"],
    v10.is_active = true,
    v10.created_date = datetime(),
    v10.last_updated_date = datetime()
WITH v10
MATCH (c:Column {column_id: "COL_QUANTITY_SOLD_FACT_4008"})
MERGE (c)-[:HAS_VALIDATION]->(v10);

// UNIT_PRICE - RANGE
MERGE (v11:ValidationRule {rule_id: "VAL_UNIT_PRICE_001"})
SET v11.rule_name = "UNIT_PRICE Range Check",
    v11.column_name = "UNIT_PRICE",
    v11.table_id = "FACT_ECOMMERCE_SALES",
    v11.validation_type = "RANGE",
    v11.validation_rule = "RANGE:0,99999.9999",
    v11.error_message = "Unit Price must be between 0 and 99999.9999",
    v11.business_criticality = "high",
    v11.priority = "P1",
    v11.applicable_domains = ["ALL"],
    v11.is_active = true,
    v11.created_date = datetime(),
    v11.last_updated_date = datetime()
WITH v11
MATCH (c:Column {column_id: "COL_UNIT_PRICE_FACT_4009"})
MERGE (c)-[:HAS_VALIDATION]->(v11);

// DISCOUNT_AMOUNT - RANGE
MERGE (v12:ValidationRule {rule_id: "VAL_DISCOUNT_AMOUNT_001"})
SET v12.rule_name = "DISCOUNT_AMOUNT Range Check",
    v12.column_name = "DISCOUNT_AMOUNT",
    v12.table_id = "FACT_ECOMMERCE_SALES",
    v12.validation_type = "RANGE",
    v12.validation_rule = "RANGE:0,999999.99",
    v12.error_message = "Discount Amount must be non-negative",
    v12.business_criticality = "high",
    v12.priority = "P1",
    v12.applicable_domains = ["ALL"],
    v12.is_active = true,
    v12.created_date = datetime(),
    v12.last_updated_date = datetime()
WITH v12
MATCH (c:Column {column_id: "COL_DISCOUNT_AMOUNT_FACT_4010"})
MERGE (c)-[:HAS_VALIDATION]->(v12);

// ========================================
// DIGITAL COMMERCE METRICS VALIDATIONS
// ========================================

// CART_VALUE - RANGE
MERGE (v13:ValidationRule {rule_id: "VAL_CART_VALUE_001"})
SET v13.rule_name = "CART_VALUE Range Check",
    v13.column_name = "CART_VALUE",
    v13.table_id = "FACT_ECOMMERCE_SALES",
    v13.validation_type = "RANGE",
    v13.validation_rule = "RANGE:0,9999999.99",
    v13.error_message = "Cart Value must be non-negative",
    v13.business_criticality = "high",
    v13.priority = "P1",
    v13.applicable_domains = ["ALL"],
    v13.is_active = true,
    v13.created_date = datetime(),
    v13.last_updated_date = datetime()
WITH v13
MATCH (c:Column {column_id: "COL_CART_VALUE_FACT_4011"})
MERGE (c)-[:HAS_VALIDATION]->(v13);

// ITEMS_PER_ORDER - RANGE
MERGE (v14:ValidationRule {rule_id: "VAL_ITEMS_PER_ORDER_001"})
SET v14.rule_name = "ITEMS_PER_ORDER Range Check",
    v14.column_name = "ITEMS_PER_ORDER",
    v14.table_id = "FACT_ECOMMERCE_SALES",
    v14.validation_type = "RANGE",
    v14.validation_rule = "RANGE:1,9999",
    v14.error_message = "Items Per Order must be at least 1",
    v14.business_criticality = "medium",
    v14.priority = "P2",
    v14.applicable_domains = ["ALL"],
    v14.is_active = true,
    v14.created_date = datetime(),
    v14.last_updated_date = datetime()
WITH v14
MATCH (c:Column {column_id: "COL_ITEMS_PER_ORDER_FACT_4012"})
MERGE (c)-[:HAS_VALIDATION]->(v14);

// CONVERSION_RATE - RANGE
MERGE (v15:ValidationRule {rule_id: "VAL_CONVERSION_RATE_001"})
SET v15.rule_name = "CONVERSION_RATE Range Check",
    v15.column_name = "CONVERSION_RATE",
    v15.table_id = "FACT_ECOMMERCE_SALES",
    v15.validation_type = "RANGE",
    v15.validation_rule = "RANGE:0,100",
    v15.error_message = "Conversion Rate must be between 0 and 100",
    v15.business_criticality = "critical",
    v15.priority = "P1",
    v15.applicable_domains = ["ALL"],
    v15.is_active = true,
    v15.created_date = datetime(),
    v15.last_updated_date = datetime()
WITH v15
MATCH (c:Column {column_id: "COL_CONVERSION_RATE_FACT_4013"})
MERGE (c)-[:HAS_VALIDATION]->(v15);

// CART_ABANDONMENT_FLAG - NOT NULL
MERGE (v16:ValidationRule {rule_id: "VAL_CART_ABANDONMENT_FLAG_001"})
SET v16.rule_name = "CART_ABANDONMENT_FLAG Not Null Check",
    v16.column_name = "CART_ABANDONMENT_FLAG",
    v16.table_id = "FACT_ECOMMERCE_SALES",
    v16.validation_type = "NOT_NULL",
    v16.validation_rule = "NOT_NULL",
    v16.error_message = "Cart Abandonment Flag is required",
    v16.business_criticality = "high",
    v16.priority = "P1",
    v16.applicable_domains = ["ALL"],
    v16.is_active = true,
    v16.created_date = datetime(),
    v16.last_updated_date = datetime()
WITH v16
MATCH (c:Column {column_id: "COL_CART_ABANDONMENT_FLAG_FACT_4014"})
MERGE (c)-[:HAS_VALIDATION]->(v16);

// ========================================
// CUSTOMER BEHAVIOR AND CHANNEL VALIDATIONS
// ========================================

// NEW_CUSTOMER_FLAG - NOT NULL
MERGE (v17:ValidationRule {rule_id: "VAL_NEW_CUSTOMER_FLAG_001"})
SET v17.rule_name = "NEW_CUSTOMER_FLAG Not Null Check",
    v17.column_name = "NEW_CUSTOMER_FLAG",
    v17.table_id = "FACT_ECOMMERCE_SALES",
    v17.validation_type = "NOT_NULL",
    v17.validation_rule = "NOT_NULL",
    v17.error_message = "New Customer Flag is required",
    v17.business_criticality = "high",
    v17.priority = "P1",
    v17.applicable_domains = ["ALL"],
    v17.is_active = true,
    v17.created_date = datetime(),
    v17.last_updated_date = datetime()
WITH v17
MATCH (c:Column {column_id: "COL_NEW_CUSTOMER_FLAG_FACT_4015"})
MERGE (c)-[:HAS_VALIDATION]->(v17);

// RETURNING_CUSTOMER_FLAG - NOT NULL
MERGE (v18:ValidationRule {rule_id: "VAL_RETURNING_CUSTOMER_FLAG_001"})
SET v18.rule_name = "RETURNING_CUSTOMER_FLAG Not Null Check",
    v18.column_name = "RETURNING_CUSTOMER_FLAG",
    v18.table_id = "FACT_ECOMMERCE_SALES",
    v18.validation_type = "NOT_NULL",
    v18.validation_rule = "NOT_NULL",
    v18.error_message = "Returning Customer Flag is required",
    v18.business_criticality = "high",
    v18.priority = "P1",
    v18.applicable_domains = ["ALL"],
    v18.is_active = true,
    v18.created_date = datetime(),
    v18.last_updated_date = datetime()
WITH v18
MATCH (c:Column {column_id: "COL_RETURNING_CUSTOMER_FLAG_FACT_4016"})
MERGE (c)-[:HAS_VALIDATION]->(v18);

// DEVICE_TYPE - NOT NULL
MERGE (v19:ValidationRule {rule_id: "VAL_DEVICE_TYPE_001"})
SET v19.rule_name = "DEVICE_TYPE Not Null Check",
    v19.column_name = "DEVICE_TYPE",
    v19.table_id = "FACT_ECOMMERCE_SALES",
    v19.validation_type = "NOT_NULL",
    v19.validation_rule = "NOT_NULL",
    v19.error_message = "Device Type is required",
    v19.business_criticality = "high",
    v19.priority = "P1",
    v19.applicable_domains = ["ALL"],
    v19.is_active = true,
    v19.created_date = datetime(),
    v19.last_updated_date = datetime()
WITH v19
MATCH (c:Column {column_id: "COL_DEVICE_TYPE_FACT_4017"})
MERGE (c)-[:HAS_VALIDATION]->(v19);

// DEVICE_TYPE - ENUM
MERGE (v20:ValidationRule {rule_id: "VAL_DEVICE_TYPE_002"})
SET v20.rule_name = "DEVICE_TYPE Domain Check",
    v20.column_name = "DEVICE_TYPE",
    v20.table_id = "FACT_ECOMMERCE_SALES",
    v20.validation_type = "ENUM",
    v20.validation_rule = "ENUM:Mobile,Desktop,Tablet,App,Smart TV,Other",
    v20.error_message = "Invalid device type",
    v20.business_criticality = "high",
    v20.priority = "P1",
    v20.applicable_domains = ["ALL"],
    v20.is_active = true,
    v20.created_date = datetime(),
    v20.last_updated_date = datetime()
WITH v20
MATCH (c:Column {column_id: "COL_DEVICE_TYPE_FACT_4017"})
MERGE (c)-[:HAS_VALIDATION]->(v20);

// TRAFFIC_SOURCE - ENUM
MERGE (v21:ValidationRule {rule_id: "VAL_TRAFFIC_SOURCE_001"})
SET v21.rule_name = "TRAFFIC_SOURCE Domain Check",
    v21.column_name = "TRAFFIC_SOURCE",
    v21.table_id = "FACT_ECOMMERCE_SALES",
    v21.validation_type = "ENUM",
    v21.validation_rule = "ENUM:Organic,Paid Search,Social,Direct,Email,Referral,Display,Affiliate,Other",
    v21.error_message = "Invalid traffic source",
    v21.business_criticality = "high",
    v21.priority = "P1",
    v21.applicable_domains = ["ALL"],
    v21.is_active = true,
    v21.created_date = datetime(),
    v21.last_updated_date = datetime()
WITH v21
MATCH (c:Column {column_id: "COL_TRAFFIC_SOURCE_FACT_4018"})
MERGE (c)-[:HAS_VALIDATION]->(v21);

// ========================================
// FULFILLMENT AND DELIVERY VALIDATIONS
// ========================================

// SHIPPING_AMOUNT - RANGE
MERGE (v22:ValidationRule {rule_id: "VAL_SHIPPING_AMOUNT_001"})
SET v22.rule_name = "SHIPPING_AMOUNT Range Check",
    v22.column_name = "SHIPPING_AMOUNT",
    v22.table_id = "FACT_ECOMMERCE_SALES",
    v22.validation_type = "RANGE",
    v22.validation_rule = "RANGE:0,9999.99",
    v22.error_message = "Shipping Amount must be non-negative",
    v22.business_criticality = "medium",
    v22.priority = "P2",
    v22.applicable_domains = ["ALL"],
    v22.is_active = true,
    v22.created_date = datetime(),
    v22.last_updated_date = datetime()
WITH v22
MATCH (c:Column {column_id: "COL_SHIPPING_AMOUNT_FACT_4019"})
MERGE (c)-[:HAS_VALIDATION]->(v22);

// DELIVERY_METHOD - ENUM
MERGE (v23:ValidationRule {rule_id: "VAL_DELIVERY_METHOD_001"})
SET v23.rule_name = "DELIVERY_METHOD Domain Check",
    v23.column_name = "DELIVERY_METHOD",
    v23.table_id = "FACT_ECOMMERCE_SALES",
    v23.validation_type = "ENUM",
    v23.validation_rule = "ENUM:Standard,Express,Same-Day,Next-Day,Two-Day,Pickup,White Glove,Scheduled",
    v23.error_message = "Invalid delivery method",
    v23.business_criticality = "medium",
    v23.priority = "P2",
    v23.applicable_domains = ["ALL"],
    v23.is_active = true,
    v23.created_date = datetime(),
    v23.last_updated_date = datetime()
WITH v23
MATCH (c:Column {column_id: "COL_DELIVERY_METHOD_FACT_4020"})
MERGE (c)-[:HAS_VALIDATION]->(v23);

// DELIVERY_TIME_DAYS - RANGE
MERGE (v24:ValidationRule {rule_id: "VAL_DELIVERY_TIME_DAYS_001"})
SET v24.rule_name = "DELIVERY_TIME_DAYS Range Check",
    v24.column_name = "DELIVERY_TIME_DAYS",
    v24.table_id = "FACT_ECOMMERCE_SALES",
    v24.validation_type = "RANGE",
    v24.validation_rule = "RANGE:0,365",
    v24.error_message = "Delivery Time Days must be between 0 and 365",
    v24.business_criticality = "high",
    v24.priority = "P1",
    v24.applicable_domains = ["ALL"],
    v24.is_active = true,
    v24.created_date = datetime(),
    v24.last_updated_date = datetime()
WITH v24
MATCH (c:Column {column_id: "COL_DELIVERY_TIME_DAYS_FACT_4021"})
MERGE (c)-[:HAS_VALIDATION]->(v24);

// CLICK_TO_SHIP_HOURS - RANGE
MERGE (v25:ValidationRule {rule_id: "VAL_CLICK_TO_SHIP_HOURS_001"})
SET v25.rule_name = "CLICK_TO_SHIP_HOURS Range Check",
    v25.column_name = "CLICK_TO_SHIP_HOURS",
    v25.table_id = "FACT_ECOMMERCE_SALES",
    v25.validation_type = "RANGE",
    v25.validation_rule = "RANGE:0,720",
    v25.error_message = "Click to Ship Hours must be between 0 and 720 (30 days)",
    v25.business_criticality = "medium",
    v25.priority = "P2",
    v25.applicable_domains = ["ALL"],
    v25.is_active = true,
    v25.created_date = datetime(),
    v25.last_updated_date = datetime()
WITH v25
MATCH (c:Column {column_id: "COL_CLICK_TO_SHIP_HOURS_FACT_4022"})
MERGE (c)-[:HAS_VALIDATION]->(v25);

// ========================================
// PAYMENT AND FINANCIAL VALIDATIONS
// ========================================

// PAYMENT_METHOD - NOT NULL
MERGE (v26:ValidationRule {rule_id: "VAL_PAYMENT_METHOD_001"})
SET v26.rule_name = "PAYMENT_METHOD Not Null Check",
    v26.column_name = "PAYMENT_METHOD",
    v26.table_id = "FACT_ECOMMERCE_SALES",
    v26.validation_type = "NOT_NULL",
    v26.validation_rule = "NOT_NULL",
    v26.error_message = "Payment Method is required",
    v26.business_criticality = "high",
    v26.priority = "P1",
    v26.applicable_domains = ["ALL"],
    v26.is_active = true,
    v26.created_date = datetime(),
    v26.last_updated_date = datetime()
WITH v26
MATCH (c:Column {column_id: "COL_PAYMENT_METHOD_FACT_4023"})
MERGE (c)-[:HAS_VALIDATION]->(v26);

// PAYMENT_METHOD - ENUM
MERGE (v27:ValidationRule {rule_id: "VAL_PAYMENT_METHOD_002"})
SET v27.rule_name = "PAYMENT_METHOD Domain Check",
    v27.column_name = "PAYMENT_METHOD",
    v27.table_id = "FACT_ECOMMERCE_SALES",
    v27.validation_type = "ENUM",
    v27.validation_rule = "ENUM:Credit Card,Debit Card,PayPal,Apple Pay,Google Pay,BNPL,Gift Card,Bank Transfer,Cash on Delivery,Cryptocurrency,Other",
    v27.error_message = "Invalid payment method",
    v27.business_criticality = "high",
    v27.priority = "P1",
    v27.applicable_domains = ["ALL"],
    v27.is_active = true,
    v27.created_date = datetime(),
    v27.last_updated_date = datetime()
WITH v27
MATCH (c:Column {column_id: "COL_PAYMENT_METHOD_FACT_4023"})
MERGE (c)-[:HAS_VALIDATION]->(v27);

// TAX_AMOUNT - NOT NULL
MERGE (v28:ValidationRule {rule_id: "VAL_TAX_AMOUNT_001"})
SET v28.rule_name = "TAX_AMOUNT Not Null Check",
    v28.column_name = "TAX_AMOUNT",
    v28.table_id = "FACT_ECOMMERCE_SALES",
    v28.validation_type = "NOT_NULL",
    v28.validation_rule = "NOT_NULL",
    v28.error_message = "Tax Amount is required",
    v28.business_criticality = "high",
    v28.priority = "P1",
    v28.applicable_domains = ["ALL"],
    v28.is_active = true,
    v28.created_date = datetime(),
    v28.last_updated_date = datetime()
WITH v28
MATCH (c:Column {column_id: "COL_TAX_AMOUNT_FACT_4024"})
MERGE (c)-[:HAS_VALIDATION]->(v28);

// TAX_AMOUNT - RANGE
MERGE (v29:ValidationRule {rule_id: "VAL_TAX_AMOUNT_002"})
SET v29.rule_name = "TAX_AMOUNT Range Check",
    v29.column_name = "TAX_AMOUNT",
    v29.table_id = "FACT_ECOMMERCE_SALES",
    v29.validation_type = "RANGE",
    v29.validation_rule = "RANGE:0,999999.99",
    v29.error_message = "Tax Amount must be non-negative",
    v29.business_criticality = "high",
    v29.priority = "P1",
    v29.applicable_domains = ["ALL"],
    v29.is_active = true,
    v29.created_date = datetime(),
    v29.last_updated_date = datetime()
WITH v29
MATCH (c:Column {column_id: "COL_TAX_AMOUNT_FACT_4024"})
MERGE (c)-[:HAS_VALIDATION]->(v29);

// PAYMENT_PROCESSING_FEE - RANGE
MERGE (v30:ValidationRule {rule_id: "VAL_PAYMENT_PROCESSING_FEE_001"})
SET v30.rule_name = "PAYMENT_PROCESSING_FEE Range Check",
    v30.column_name = "PAYMENT_PROCESSING_FEE",
    v30.table_id = "FACT_ECOMMERCE_SALES",
    v30.validation_type = "RANGE",
    v30.validation_rule = "RANGE:0,9999.99",
    v30.error_message = "Payment Processing Fee must be non-negative",
    v30.business_criticality = "medium",
    v30.priority = "P2",
    v30.applicable_domains = ["ALL"],
    v30.is_active = true,
    v30.created_date = datetime(),
    v30.last_updated_date = datetime()
WITH v30
MATCH (c:Column {column_id: "COL_PAYMENT_PROCESSING_FEE_FACT_4025"})
MERGE (c)-[:HAS_VALIDATION]->(v30);

// CURRENCY_CODE - NOT NULL
MERGE (v31:ValidationRule {rule_id: "VAL_CURRENCY_CODE_001"})
SET v31.rule_name = "CURRENCY_CODE Not Null Check",
    v31.column_name = "CURRENCY_CODE",
    v31.table_id = "FACT_ECOMMERCE_SALES",
    v31.validation_type = "NOT_NULL",
    v31.validation_rule = "NOT_NULL",
    v31.error_message = "Currency Code is required",
    v31.business_criticality = "high",
    v31.priority = "P1",
    v31.applicable_domains = ["ALL"],
    v31.is_active = true,
    v31.created_date = datetime(),
    v31.last_updated_date = datetime()
WITH v31
MATCH (c:Column {column_id: "COL_CURRENCY_CODE_FACT_4026"})
MERGE (c)-[:HAS_VALIDATION]->(v31);

// CURRENCY_CODE - ENUM
MERGE (v32:ValidationRule {rule_id: "VAL_CURRENCY_CODE_002"})
SET v32.rule_name = "CURRENCY_CODE Domain Check",
    v32.column_name = "CURRENCY_CODE",
    v32.table_id = "FACT_ECOMMERCE_SALES",
    v32.validation_type = "ENUM",
    v32.validation_rule = "ENUM:USD,EUR,GBP,CAD,AUD,JPY,CNY,INR,MXN,BRL",
    v32.error_message = "Invalid currency code",
    v32.business_criticality = "high",
    v32.priority = "P1",
    v32.applicable_domains = ["ALL"],
    v32.is_active = true,
    v32.created_date = datetime(),
    v32.last_updated_date = datetime()
WITH v32
MATCH (c:Column {column_id: "COL_CURRENCY_CODE_FACT_4026"})
MERGE (c)-[:HAS_VALIDATION]->(v32);

// ========================================
// CUSTOMER EXPERIENCE VALIDATIONS
// ========================================

// PAGE_VIEWS_COUNT - RANGE
MERGE (v33:ValidationRule {rule_id: "VAL_PAGE_VIEWS_COUNT_001"})
SET v33.rule_name = "PAGE_VIEWS_COUNT Range Check",
    v33.column_name = "PAGE_VIEWS_COUNT",
    v33.table_id = "FACT_ECOMMERCE_SALES",
    v33.validation_type = "RANGE",
    v33.validation_rule = "RANGE:1,9999",
    v33.error_message = "Page Views Count must be at least 1",
    v33.business_criticality = "medium",
    v33.priority = "P2",
    v33.applicable_domains = ["ALL"],
    v33.is_active = true,
    v33.created_date = datetime(),
    v33.last_updated_date = datetime()
WITH v33
MATCH (c:Column {column_id: "COL_PAGE_VIEWS_COUNT_FACT_4027"})
MERGE (c)-[:HAS_VALIDATION]->(v33);

// SESSION_DURATION_MINUTES - RANGE
MERGE (v34:ValidationRule {rule_id: "VAL_SESSION_DURATION_MINUTES_001"})
SET v34.rule_name = "SESSION_DURATION_MINUTES Range Check",
    v34.column_name = "SESSION_DURATION_MINUTES",
    v34.table_id = "FACT_ECOMMERCE_SALES",
    v34.validation_type = "RANGE",
    v34.validation_rule = "RANGE:0,9999.99",
    v34.error_message = "Session Duration must be non-negative",
    v34.business_criticality = "medium",
    v34.priority = "P2",
    v34.applicable_domains = ["ALL"],
    v34.is_active = true,
    v34.created_date = datetime(),
    v34.last_updated_date = datetime()
WITH v34
MATCH (c:Column {column_id: "COL_SESSION_DURATION_MINUTES_FACT_4028"})
MERGE (c)-[:HAS_VALIDATION]->(v34);

// ========================================
// DOMAIN-SPECIFIC VALIDATIONS - ALCOHOLIC BEVERAGES
// ========================================

// AGE_VERIFICATION_FLAG - NOT NULL (Alcoholic Beverages)
MERGE (v35:ValidationRule {rule_id: "VAL_AGE_VERIFICATION_FLAG_001"})
SET v35.rule_name = "AGE_VERIFICATION_FLAG Not Null Check",
    v35.column_name = "AGE_VERIFICATION_FLAG",
    v35.table_id = "FACT_ECOMMERCE_SALES",
    v35.validation_type = "NOT_NULL",
    v35.validation_rule = "NOT_NULL",
    v35.error_message = "Age Verification Flag is required for alcohol sales",
    v35.business_criticality = "critical",
    v35.priority = "P1",
    v35.applicable_domains = ["alcoholic_beverages"],
    v35.is_active = true,
    v35.created_date = datetime(),
    v35.last_updated_date = datetime()
WITH v35
MATCH (c:Column {column_id: "COL_AGE_VERIFICATION_FLAG_FACT_4030"})
MERGE (c)-[:HAS_VALIDATION]->(v35);

// DELIVERY_SIGNATURE_REQUIRED - NOT NULL (Alcoholic Beverages)
MERGE (v36:ValidationRule {rule_id: "VAL_DELIVERY_SIGNATURE_REQUIRED_001"})
SET v36.rule_name = "DELIVERY_SIGNATURE_REQUIRED Not Null Check",
    v36.column_name = "DELIVERY_SIGNATURE_REQUIRED",
    v36.table_id = "FACT_ECOMMERCE_SALES",
    v36.validation_type = "NOT_NULL",
    v36.validation_rule = "NOT_NULL",
    v36.error_message = "Delivery Signature Required flag is required for alcohol sales",
    v36.business_criticality = "critical",
    v36.priority = "P1",
    v36.applicable_domains = ["alcoholic_beverages"],
    v36.is_active = true,
    v36.created_date = datetime(),
    v36.last_updated_date = datetime()
WITH v36
MATCH (c:Column {column_id: "COL_DELIVERY_SIGNATURE_REQUIRED_FACT_4031"})
MERGE (c)-[:HAS_VALIDATION]->(v36);

// ========================================
// DOMAIN-SPECIFIC VALIDATIONS - PHARMACEUTICALS
// ========================================

// PRESCRIPTION_REQUIRED_FLAG - NOT NULL (Pharmaceuticals)
MERGE (v37:ValidationRule {rule_id: "VAL_PRESCRIPTION_REQUIRED_FLAG_001"})
SET v37.rule_name = "PRESCRIPTION_REQUIRED_FLAG Not Null Check",
    v37.column_name = "PRESCRIPTION_REQUIRED_FLAG",
    v37.table_id = "FACT_ECOMMERCE_SALES",
    v37.validation_type = "NOT_NULL",
    v37.validation_rule = "NOT_NULL",
    v37.error_message = "Prescription Required Flag is required for pharmaceutical sales",
    v37.business_criticality = "critical",
    v37.priority = "P1",
    v37.applicable_domains = ["pharmaceuticals"],
    v37.is_active = true,
    v37.created_date = datetime(),
    v37.last_updated_date = datetime()
WITH v37
MATCH (c:Column {column_id: "COL_PRESCRIPTION_REQUIRED_FLAG_FACT_4032"})
MERGE (c)-[:HAS_VALIDATION]->(v37);

// PHARMACY_VERIFICATION_STATUS - ENUM (Pharmaceuticals)
MERGE (v38:ValidationRule {rule_id: "VAL_PHARMACY_VERIFICATION_STATUS_001"})
SET v38.rule_name = "PHARMACY_VERIFICATION_STATUS Domain Check",
    v38.column_name = "PHARMACY_VERIFICATION_STATUS",
    v38.table_id = "FACT_ECOMMERCE_SALES",
    v38.validation_type = "ENUM",
    v38.validation_rule = "ENUM:Verified,Pending,Failed,Not Required,In Review",
    v38.error_message = "Invalid pharmacy verification status",
    v38.business_criticality = "critical",
    v38.priority = "P1",
    v38.applicable_domains = ["pharmaceuticals"],
    v38.is_active = true,
    v38.created_date = datetime(),
    v38.last_updated_date = datetime()
WITH v38
MATCH (c:Column {column_id: "COL_PHARMACY_VERIFICATION_STATUS_FACT_4033"})
MERGE (c)-[:HAS_VALIDATION]->(v38);

// INSURANCE_CLAIM_AMOUNT - RANGE (Pharmaceuticals)
MERGE (v39:ValidationRule {rule_id: "VAL_INSURANCE_CLAIM_AMOUNT_001"})
SET v39.rule_name = "INSURANCE_CLAIM_AMOUNT Range Check",
    v39.column_name = "INSURANCE_CLAIM_AMOUNT",
    v39.table_id = "FACT_ECOMMERCE_SALES",
    v39.validation_type = "RANGE",
    v39.validation_rule = "RANGE:0,99999.99",
    v39.error_message = "Insurance Claim Amount must be non-negative",
    v39.business_criticality = "high",
    v39.priority = "P1",
    v39.applicable_domains = ["pharmaceuticals"],
    v39.is_active = true,
    v39.created_date = datetime(),
    v39.last_updated_date = datetime()
WITH v39
MATCH (c:Column {column_id: "COL_INSURANCE_CLAIM_AMOUNT_FACT_4034"})
MERGE (c)-[:HAS_VALIDATION]->(v39);

// ========================================
// SUBSCRIPTION AND RECURRING REVENUE VALIDATIONS
// ========================================

// SUBSCRIPTION_FLAG - NOT NULL
MERGE (v40:ValidationRule {rule_id: "VAL_SUBSCRIPTION_FLAG_001"})
SET v40.rule_name = "SUBSCRIPTION_FLAG Not Null Check",
    v40.column_name = "SUBSCRIPTION_FLAG",
    v40.table_id = "FACT_ECOMMERCE_SALES",
    v40.validation_type = "NOT_NULL",
    v40.validation_rule = "NOT_NULL",
    v40.error_message = "Subscription Flag is required",
    v40.business_criticality = "high",
    v40.priority = "P1",
    v40.applicable_domains = ["ALL"],
    v40.is_active = true,
    v40.created_date = datetime(),
    v40.last_updated_date = datetime()
WITH v40
MATCH (c:Column {column_id: "COL_SUBSCRIPTION_FLAG_FACT_4035"})
MERGE (c)-[:HAS_VALIDATION]->(v40);

// SUBSCRIPTION_FREQUENCY_DAYS - RANGE
MERGE (v41:ValidationRule {rule_id: "VAL_SUBSCRIPTION_FREQUENCY_DAYS_001"})
SET v41.rule_name = "SUBSCRIPTION_FREQUENCY_DAYS Range Check",
    v41.column_name = "SUBSCRIPTION_FREQUENCY_DAYS",
    v41.table_id = "FACT_ECOMMERCE_SALES",
    v41.validation_type = "RANGE",
    v41.validation_rule = "RANGE:7,365",
    v41.error_message = "Subscription Frequency must be between 7 and 365 days",
    v41.business_criticality = "medium",
    v41.priority = "P2",
    v41.applicable_domains = ["ALL"],
    v41.is_active = true,
    v41.created_date = datetime(),
    v41.last_updated_date = datetime()
WITH v41
MATCH (c:Column {column_id: "COL_SUBSCRIPTION_FREQUENCY_DAYS_FACT_4036"})
MERGE (c)-[:HAS_VALIDATION]->(v41);

// SUBSCRIPTION_DISCOUNT_PERCENTAGE - RANGE
MERGE (v42:ValidationRule {rule_id: "VAL_SUBSCRIPTION_DISCOUNT_PCT_001"})
SET v42.rule_name = "SUBSCRIPTION_DISCOUNT_PERCENTAGE Range Check",
    v42.column_name = "SUBSCRIPTION_DISCOUNT_PERCENTAGE",
    v42.table_id = "FACT_ECOMMERCE_SALES",
    v42.validation_type = "RANGE",
    v42.validation_rule = "RANGE:0,100",
    v42.error_message = "Subscription Discount Percentage must be between 0 and 100",
    v42.business_criticality = "medium",
    v42.priority = "P2",
    v42.applicable_domains = ["ALL"],
    v42.is_active = true,
    v42.created_date = datetime(),
    v42.last_updated_date = datetime()
WITH v42
MATCH (c:Column {column_id: "COL_SUBSCRIPTION_DISCOUNT_PCT_FACT_4037"})
MERGE (c)-[:HAS_VALIDATION]->(v42);

// ========================================
// MOBILE AND APP COMMERCE VALIDATIONS
// ========================================

// MOBILE_APP_FLAG - NOT NULL
MERGE (v43:ValidationRule {rule_id: "VAL_MOBILE_APP_FLAG_001"})
SET v43.rule_name = "MOBILE_APP_FLAG Not Null Check",
    v43.column_name = "MOBILE_APP_FLAG",
    v43.table_id = "FACT_ECOMMERCE_SALES",
    v43.validation_type = "NOT_NULL",
    v43.validation_rule = "NOT_NULL",
    v43.error_message = "Mobile App Flag is required",
    v43.business_criticality = "high",
    v43.priority = "P1",
    v43.applicable_domains = ["ALL"],
    v43.is_active = true,
    v43.created_date = datetime(),
    v43.last_updated_date = datetime()
WITH v43
MATCH (c:Column {column_id: "COL_MOBILE_APP_FLAG_FACT_4038"})
MERGE (c)-[:HAS_VALIDATION]->(v43);

// ========================================
// SOCIAL COMMERCE VALIDATIONS
// ========================================

// SOCIAL_COMMERCE_FLAG - NOT NULL
MERGE (v44:ValidationRule {rule_id: "VAL_SOCIAL_COMMERCE_FLAG_001"})
SET v44.rule_name = "SOCIAL_COMMERCE_FLAG Not Null Check",
    v44.column_name = "SOCIAL_COMMERCE_FLAG",
    v44.table_id = "FACT_ECOMMERCE_SALES",
    v44.validation_type = "NOT_NULL",
    v44.validation_rule = "NOT_NULL",
    v44.error_message = "Social Commerce Flag is required",
    v44.business_criticality = "high",
    v44.priority = "P1",
    v44.applicable_domains = ["ALL"],
    v44.is_active = true,
    v44.created_date = datetime(),
    v44.last_updated_date = datetime()
WITH v44
MATCH (c:Column {column_id: "COL_SOCIAL_COMMERCE_FLAG_FACT_4041"})
MERGE (c)-[:HAS_VALIDATION]->(v44);

// SOCIAL_PLATFORM - ENUM
MERGE (v45:ValidationRule {rule_id: "VAL_SOCIAL_PLATFORM_001"})
SET v45.rule_name = "SOCIAL_PLATFORM Domain Check",
    v45.column_name = "SOCIAL_PLATFORM",
    v45.table_id = "FACT_ECOMMERCE_SALES",
    v45.validation_type = "ENUM",
    v45.validation_rule = "ENUM:Instagram,Facebook,TikTok,Pinterest,YouTube,Twitter,Snapchat,Other",
    v45.error_message = "Invalid social platform",
    v45.business_criticality = "medium",
    v45.priority = "P2",
    v45.applicable_domains = ["ALL"],
    v45.is_active = true,
    v45.created_date = datetime(),
    v45.last_updated_date = datetime()
WITH v45
MATCH (c:Column {column_id: "COL_SOCIAL_PLATFORM_FACT_4042"})
MERGE (c)-[:HAS_VALIDATION]->(v45);

// ========================================
// RETURNS AND SATISFACTION VALIDATIONS
// ========================================

// RETURN_FLAG - NOT NULL
MERGE (v46:ValidationRule {rule_id: "VAL_RETURN_FLAG_001"})
SET v46.rule_name = "RETURN_FLAG Not Null Check",
    v46.column_name = "RETURN_FLAG",
    v46.table_id = "FACT_ECOMMERCE_SALES",
    v46.validation_type = "NOT_NULL",
    v46.validation_rule = "NOT_NULL",
    v46.error_message = "Return Flag is required",
    v46.business_criticality = "high",
    v46.priority = "P1",
    v46.applicable_domains = ["ALL"],
    v46.is_active = true,
    v46.created_date = datetime(),
    v46.last_updated_date = datetime()
WITH v46
MATCH (c:Column {column_id: "COL_RETURN_FLAG_FACT_4047"})
MERGE (c)-[:HAS_VALIDATION]->(v46);

// RETURN_REASON - ENUM
MERGE (v47:ValidationRule {rule_id: "VAL_RETURN_REASON_001"})
SET v47.rule_name = "RETURN_REASON Domain Check",
    v47.column_name = "RETURN_REASON",
    v47.table_id = "FACT_ECOMMERCE_SALES",
    v47.validation_type = "ENUM",
    v47.validation_rule = "ENUM:Defective,Wrong Item,Not as Described,Changed Mind,Too Late,Damaged in Transit,Quality Issue,Size Issue,Other",
    v47.error_message = "Invalid return reason",
    v47.business_criticality = "medium",
    v47.priority = "P2",
    v47.applicable_domains = ["ALL"],
    v47.is_active = true,
    v47.created_date = datetime(),
    v47.last_updated_date = datetime()
WITH v47
MATCH (c:Column {column_id: "COL_RETURN_REASON_FACT_4048"})
MERGE (c)-[:HAS_VALIDATION]->(v47);

// CUSTOMER_RATING - RANGE
MERGE (v48:ValidationRule {rule_id: "VAL_CUSTOMER_RATING_001"})
SET v48.rule_name = "CUSTOMER_RATING Range Check",
    v48.column_name = "CUSTOMER_RATING",
    v48.table_id = "FACT_ECOMMERCE_SALES",
    v48.validation_type = "RANGE",
    v48.validation_rule = "RANGE:1,5",
    v48.error_message = "Customer Rating must be between 1 and 5",
    v48.business_criticality = "high",
    v48.priority = "P1",
    v48.applicable_domains = ["ALL"],
    v48.is_active = true,
    v48.created_date = datetime(),
    v48.last_updated_date = datetime()
WITH v48
MATCH (c:Column {column_id: "COL_CUSTOMER_RATING_FACT_4049"})
MERGE (c)-[:HAS_VALIDATION]->(v48);

// ========================================
// LOYALTY AND REWARDS VALIDATIONS
// ========================================

// LOYALTY_POINTS_EARNED - RANGE
MERGE (v49:ValidationRule {rule_id: "VAL_LOYALTY_POINTS_EARNED_001"})
SET v49.rule_name = "LOYALTY_POINTS_EARNED Range Check",
    v49.column_name = "LOYALTY_POINTS_EARNED",
    v49.table_id = "FACT_ECOMMERCE_SALES",
    v49.validation_type = "RANGE",
    v49.validation_rule = "RANGE:0,999999",
    v49.error_message = "Loyalty Points Earned must be non-negative",
    v49.business_criticality = "medium",
    v49.priority = "P2",
    v49.applicable_domains = ["ALL"],
    v49.is_active = true,
    v49.created_date = datetime(),
    v49.last_updated_date = datetime()
WITH v49
MATCH (c:Column {column_id: "COL_LOYALTY_POINTS_EARNED_FACT_4051"})
MERGE (c)-[:HAS_VALIDATION]->(v49);

// LOYALTY_POINTS_REDEEMED - RANGE
MERGE (v50:ValidationRule {rule_id: "VAL_LOYALTY_POINTS_REDEEMED_001"})
SET v50.rule_name = "LOYALTY_POINTS_REDEEMED Range Check",
    v50.column_name = "LOYALTY_POINTS_REDEEMED",
    v50.table_id = "FACT_ECOMMERCE_SALES",
    v50.validation_type = "RANGE",
    v50.validation_rule = "RANGE:0,999999",
    v50.error_message = "Loyalty Points Redeemed must be non-negative",
    v50.business_criticality = "medium",
    v50.priority = "P2",
    v50.applicable_domains = ["ALL"],
    v50.is_active = true,
    v50.created_date = datetime(),
    v50.last_updated_date = datetime()
WITH v50
MATCH (c:Column {column_id: "COL_LOYALTY_POINTS_REDEEMED_FACT_4052"})
MERGE (c)-[:HAS_VALIDATION]->(v50);

// ========================================
// METADATA VALIDATIONS
// ========================================

// ORDER_SOURCE_SYSTEM - ENUM
MERGE (v51:ValidationRule {rule_id: "VAL_ORDER_SOURCE_SYSTEM_001"})
SET v51.rule_name = "ORDER_SOURCE_SYSTEM Domain Check",
    v51.column_name = "ORDER_SOURCE_SYSTEM",
    v51.table_id = "FACT_ECOMMERCE_SALES",
    v51.validation_type = "ENUM",
    v51.validation_rule = "ENUM:Shopify,Magento,WooCommerce,BigCommerce,Custom,SAP Commerce,Salesforce Commerce,Oracle Commerce,Other",
    v51.error_message = "Invalid order source system",
    v51.business_criticality = "medium",
    v51.priority = "P2",
    v51.applicable_domains = ["ALL"],
    v51.is_active = true,
    v51.created_date = datetime(),
    v51.last_updated_date = datetime()
WITH v51
MATCH (c:Column {column_id: "COL_ORDER_SOURCE_SYSTEM_FACT_4053"})
MERGE (c)-[:HAS_VALIDATION]->(v51);

// FRAUD_RISK_SCORE - RANGE
MERGE (v52:ValidationRule {rule_id: "VAL_FRAUD_RISK_SCORE_001"})
SET v52.rule_name = "FRAUD_RISK_SCORE Range Check",
    v52.column_name = "FRAUD_RISK_SCORE",
    v52.table_id = "FACT_ECOMMERCE_SALES",
    v52.validation_type = "RANGE",
    v52.validation_rule = "RANGE:0,100",
    v52.error_message = "Fraud Risk Score must be between 0 and 100",
    v52.business_criticality = "high",
    v52.priority = "P1",
    v52.applicable_domains = ["ALL"],
    v52.is_active = true,
    v52.created_date = datetime(),
    v52.last_updated_date = datetime()
WITH v52
MATCH (c:Column {column_id: "COL_FRAUD_RISK_SCORE_FACT_4054"})
MERGE (c)-[:HAS_VALIDATION]->(v52);

// ========================================
// CROSS-FIELD VALIDATIONS
// ========================================

// NEW_CUSTOMER_FLAG vs RETURNING_CUSTOMER_FLAG
MERGE (v53:ValidationRule {rule_id: "VAL_CUSTOMER_TYPE_CONSISTENCY_001"})
SET v53.rule_name = "Customer Type Consistency Check",
    v53.column_name = "NEW_CUSTOMER_FLAG,RETURNING_CUSTOMER_FLAG",
    v53.table_id = "FACT_ECOMMERCE_SALES",
    v53.validation_type = "CUSTOM",
    v53.validation_rule = "CUSTOM:NEW_CUSTOMER_FLAG XOR RETURNING_CUSTOMER_FLAG",
    v53.error_message = "Customer must be either new or returning, not both",
    v53.business_criticality = "high",
    v53.priority = "P1",
    v53.applicable_domains = ["ALL"],
    v53.is_active = true,
    v53.created_date = datetime(),
    v53.last_updated_date = datetime()
WITH v53
MATCH (t:Table {table_id: "FACT_ECOMMERCE_SALES"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v53);

// GROSS_SALES_AMOUNT vs NET_SALES_AMOUNT
MERGE (v54:ValidationRule {rule_id: "VAL_SALES_AMOUNT_CONSISTENCY_001"})
SET v54.rule_name = "Sales Amount Consistency Check",
    v54.column_name = "GROSS_SALES_AMOUNT,NET_SALES_AMOUNT,DISCOUNT_AMOUNT",
    v54.table_id = "FACT_ECOMMERCE_SALES",
    v54.validation_type = "CUSTOM",
    v54.validation_rule = "CUSTOM:NET_SALES_AMOUNT=GROSS_SALES_AMOUNT-DISCOUNT_AMOUNT",
    v54.error_message = "Net Sales must equal Gross Sales minus Discount",
    v54.business_criticality = "critical",
    v54.priority = "P1",
    v54.applicable_domains = ["ALL"],
    v54.is_active = true,
    v54.created_date = datetime(),
    v54.last_updated_date = datetime()
WITH v54
MATCH (t:Table {table_id: "FACT_ECOMMERCE_SALES"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v54);

// QUANTITY_SOLD vs UNIT_PRICE vs GROSS_SALES_AMOUNT
MERGE (v55:ValidationRule {rule_id: "VAL_SALES_CALCULATION_001"})
SET v55.rule_name = "Sales Calculation Check",
    v55.column_name = "QUANTITY_SOLD,UNIT_PRICE,GROSS_SALES_AMOUNT",
    v55.table_id = "FACT_ECOMMERCE_SALES",
    v55.validation_type = "CUSTOM",
    v55.validation_rule = "CUSTOM:ABS(GROSS_SALES_AMOUNT-(QUANTITY_SOLD*UNIT_PRICE))<0.01",
    v55.error_message = "Gross Sales must equal Quantity times Unit Price",
    v55.business_criticality = "critical",
    v55.priority = "P1",
    v55.applicable_domains = ["ALL"],
    v55.is_active = true,
    v55.created_date = datetime(),
    v55.last_updated_date = datetime()
WITH v55
MATCH (t:Table {table_id: "FACT_ECOMMERCE_SALES"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v55);

// AGE_VERIFICATION_FLAG vs DELIVERY_SIGNATURE_REQUIRED (Alcohol)
MERGE (v56:ValidationRule {rule_id: "VAL_ALCOHOL_COMPLIANCE_001"})
SET v56.rule_name = "Alcohol Compliance Check",
    v56.column_name = "AGE_VERIFICATION_FLAG,DELIVERY_SIGNATURE_REQUIRED",
    v56.table_id = "FACT_ECOMMERCE_SALES",
    v56.validation_type = "CUSTOM",
    v56.validation_rule = "CUSTOM:IF(AGE_VERIFICATION_FLAG=true,DELIVERY_SIGNATURE_REQUIRED=true)",
    v56.error_message = "Age-verified orders must require delivery signature",
    v56.business_criticality = "critical",
    v56.priority = "P1",
    v56.applicable_domains = ["alcoholic_beverages"],
    v56.is_active = true,
    v56.created_date = datetime(),
    v56.last_updated_date = datetime()
WITH v56
MATCH (t:Table {table_id: "FACT_ECOMMERCE_SALES"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v56);

// SUBSCRIPTION_FLAG vs SUBSCRIPTION_FREQUENCY_DAYS
MERGE (v57:ValidationRule {rule_id: "VAL_SUBSCRIPTION_CONSISTENCY_001"})
SET v57.rule_name = "Subscription Consistency Check",
    v57.column_name = "SUBSCRIPTION_FLAG,SUBSCRIPTION_FREQUENCY_DAYS",
    v57.table_id = "FACT_ECOMMERCE_SALES",
    v57.validation_type = "CUSTOM",
    v57.validation_rule = "CUSTOM:IF(SUBSCRIPTION_FLAG=true,SUBSCRIPTION_FREQUENCY_DAYS IS NOT NULL)",
    v57.error_message = "Subscription orders must have frequency defined",
    v57.business_criticality = "high",
    v57.priority = "P1",
    v57.applicable_domains = ["ALL"],
    v57.is_active = true,
    v57.created_date = datetime(),
    v57.last_updated_date = datetime()
WITH v57
MATCH (t:Table {table_id: "FACT_ECOMMERCE_SALES"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v57);

// MOBILE_APP_FLAG vs DEVICE_TYPE
MERGE (v58:ValidationRule {rule_id: "VAL_MOBILE_CONSISTENCY_001"})
SET v58.rule_name = "Mobile App Consistency Check",
    v58.column_name = "MOBILE_APP_FLAG,DEVICE_TYPE",
    v58.table_id = "FACT_ECOMMERCE_SALES",
    v58.validation_type = "CUSTOM",
    v58.validation_rule = "CUSTOM:IF(MOBILE_APP_FLAG=true,DEVICE_TYPE='App')",
    v58.error_message = "Mobile app orders must have device type as App",
    v58.business_criticality = "medium",
    v58.priority = "P2",
    v58.applicable_domains = ["ALL"],
    v58.is_active = true,
    v58.created_date = datetime(),
    v58.last_updated_date = datetime()
WITH v58
MATCH (t:Table {table_id: "FACT_ECOMMERCE_SALES"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v58);

// RETURN_FLAG vs RETURN_REASON
MERGE (v59:ValidationRule {rule_id: "VAL_RETURN_CONSISTENCY_001"})
SET v59.rule_name = "Return Consistency Check",
    v59.column_name = "RETURN_FLAG,RETURN_REASON",
    v59.table_id = "FACT_ECOMMERCE_SALES",
    v59.validation_type = "CUSTOM",
    v59.validation_rule = "CUSTOM:IF(RETURN_FLAG=true,RETURN_REASON IS NOT NULL)",
    v59.error_message = "Returned orders must have a return reason",
    v59.business_criticality = "high",
    v59.priority = "P1",
    v59.applicable_domains = ["ALL"],
    v59.is_active = true,
    v59.created_date = datetime(),
    v59.last_updated_date = datetime()
WITH v59
MATCH (t:Table {table_id: "FACT_ECOMMERCE_SALES"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v59);

// PRESCRIPTION_REQUIRED_FLAG vs PHARMACY_VERIFICATION_STATUS (Pharmaceuticals)
MERGE (v60:ValidationRule {rule_id: "VAL_PRESCRIPTION_CONSISTENCY_001"})
SET v60.rule_name = "Prescription Consistency Check",
    v60.column_name = "PRESCRIPTION_REQUIRED_FLAG,PHARMACY_VERIFICATION_STATUS",
    v60.table_id = "FACT_ECOMMERCE_SALES",
    v60.validation_type = "CUSTOM",
    v60.validation_rule = "CUSTOM:IF(PRESCRIPTION_REQUIRED_FLAG=true,PHARMACY_VERIFICATION_STATUS IN ('Verified','Pending','In Review'))",
    v60.error_message = "Prescription orders must have valid verification status",
    v60.business_criticality = "critical",
    v60.priority = "P1",
    v60.applicable_domains = ["pharmaceuticals"],
    v60.is_active = true,
    v60.created_date = datetime(),
    v60.last_updated_date = datetime()
WITH v60
MATCH (t:Table {table_id: "FACT_ECOMMERCE_SALES"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v60);

// ========================================
// QUERY TO VERIFY CREATED VALIDATIONS
// ========================================

// Count validations by type
MATCH (v:ValidationRule {table_id: "FACT_ECOMMERCE_SALES"})
RETURN v.validation_type as Type, count(*) as Count
ORDER BY Count DESC;

// Count validations by priority
MATCH (v:ValidationRule {table_id: "FACT_ECOMMERCE_SALES"})
RETURN v.priority as Priority, count(*) as Count
ORDER BY Priority;

// Count domain-specific validations
MATCH (v:ValidationRule {table_id: "FACT_ECOMMERCE_SALES"})
WHERE v.applicable_domains <> ["ALL"]
RETURN v.applicable_domains as Domains, count(*) as Count
ORDER BY Count DESC;