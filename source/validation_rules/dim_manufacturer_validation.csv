rule_id,rule_name,column_name,table_id,validation_type,validation_rule,error_message,business_criticality,priority,applicable_domains,is_active
VAL_MANUFACTURER_ID_001,MANUFACTURER_ID Not Null Check,MANUFACTURER_ID,DIM_MANUFACTURER,NOT_NULL,NOT_NULL,Manufacturer ID is required,critical,P1,ALL,true
VAL_MANUFACTURER_ID_002,MANUFACTURER_ID Uniqueness Check,MANUFACTURER_ID,DIM_MANUFACTURER,UNIQUE,UNIQUE,Manufacturer ID must be unique,critical,P1,ALL,true
VAL_MANUFACTURER_ID_003,MANUFACTURER_ID Format Check,MANUFACTURER_ID,DIM_MANUFACTURER,REG<PERSON>,^[A-Za-z0-9_-]{1\,20}$,Manufacturer ID must be alphanumeric with dash/underscore max 20 chars,critical,P1,ALL,true
VAL_MANUFACTURER_NAME_001,MANUFACTURER_NAME Not Null Check,MANUFACTURER_NAME,<PERSON>IM_MANUFACTURER,NOT_NULL,NOT_NULL,Manufacturer Name is required,critical,P1,ALL,true
VAL_MANUFACTURER_NAME_002,MANUFACTURER_NAME Length Check,MANUFACTURER_NAME,DIM_MANUFACTURER,LENGTH,LENGTH:1\,300,Manufacturer Name must be 1-300 characters,critical,P1,ALL,true
VAL_DUNS_NUMBER_001,DUNS_NUMBER Format Check,DUNS_NUMBER,DIM_MANUFACTURER,REGEX,^\\d{9}$,DUNS Number must be exactly 9 digits,high,P1,ALL,true
VAL_TAX_ID_NUMBER_001,TAX_ID_NUMBER Not Null Check,TAX_ID_NUMBER,DIM_MANUFACTURER,NOT_NULL,NOT_NULL,Tax ID Number is required,critical,P1,ALL,true
VAL_TAX_ID_NUMBER_002,TAX_ID_NUMBER Format Check,TAX_ID_NUMBER,DIM_MANUFACTURER,REGEX,^\\d{2}-\\d{7}$|^\\d{9}$,Tax ID must be valid format (XX-XXXXXXX or XXXXXXXXX),critical,P1,ALL,true
VAL_MANUFACTURER_TYPE_001,MANUFACTURER_TYPE Not Null Check,MANUFACTURER_TYPE,DIM_MANUFACTURER,NOT_NULL,NOT_NULL,Manufacturer Type is required,high,P1,ALL,true
VAL_MANUFACTURER_TYPE_002,MANUFACTURER_TYPE Domain Check,MANUFACTURER_TYPE,DIM_MANUFACTURER,ENUM,ENUM:OEM\,Contract Manufacturer\,Co-Packer\,Raw Material Supplier\,Packaging Supplier\,Component Supplier,Invalid manufacturer type,high,P1,ALL,true
VAL_COMPANY_SIZE_001,COMPANY_SIZE Domain Check,COMPANY_SIZE,DIM_MANUFACTURER,ENUM,ENUM:Small\,Medium\,Large\,Enterprise,Invalid company size classification,medium,P2,ALL,true
VAL_ANNUAL_REVENUE_001,ANNUAL_REVENUE Range Check,ANNUAL_REVENUE,DIM_MANUFACTURER,RANGE,RANGE:0\,999999999999.99,Annual Revenue must be non-negative,high,P1,ALL,true
VAL_EMPLOYEE_COUNT_001,EMPLOYEE_COUNT Range Check,EMPLOYEE_COUNT,DIM_MANUFACTURER,RANGE,RANGE:1\,999999,Employee Count must be positive,medium,P2,ALL,true
VAL_YEAR_ESTABLISHED_001,YEAR_ESTABLISHED Range Check,YEAR_ESTABLISHED,DIM_MANUFACTURER,RANGE,RANGE:1800\,2025,Year Established must be between 1800 and current year,low,P3,ALL,true
VAL_OWNERSHIP_TYPE_001,OWNERSHIP_TYPE Domain Check,OWNERSHIP_TYPE,DIM_MANUFACTURER,ENUM,ENUM:Public\,Private\,Family-Owned\,Private Equity\,Cooperative\,Government,Invalid ownership type,medium,P2,ALL,true
VAL_HQ_ADDRESS_001,HEADQUARTERS_ADDRESS Not Null Check,HEADQUARTERS_ADDRESS,DIM_MANUFACTURER,NOT_NULL,NOT_NULL,Headquarters Address is required,high,P1,ALL,true
VAL_HQ_COUNTRY_001,HEADQUARTERS_COUNTRY Not Null Check,HEADQUARTERS_COUNTRY,DIM_MANUFACTURER,NOT_NULL,NOT_NULL,Headquarters Country is required,high,P1,ALL,true
VAL_FDA_FACILITY_REG_001,FDA_FACILITY_REGISTRATION Not Null Check,FDA_FACILITY_REGISTRATION,DIM_MANUFACTURER,NOT_NULL,NOT_NULL,FDA Facility Registration is required for regulated products,critical,P1,"pharmaceuticals,food_beverage,cosmetics",true
VAL_FDA_FACILITY_REG_002,FDA_FACILITY_REGISTRATION Format Check,FDA_FACILITY_REGISTRATION,DIM_MANUFACTURER,REGEX,^\\d{10\,11}$,FDA Registration must be 10-11 digits,critical,P1,"pharmaceuticals,food_beverage,cosmetics",true
VAL_DEA_REGISTRATION_001,DEA_REGISTRATION Not Null Check,DEA_REGISTRATION,DIM_MANUFACTURER,NOT_NULL,NOT_NULL,DEA Registration is required for controlled substances,critical,P1,pharmaceuticals,true
VAL_DEA_REGISTRATION_002,DEA_REGISTRATION Format Check,DEA_REGISTRATION,DIM_MANUFACTURER,REGEX,^[A-Z]{2}\\d{7}$,DEA Registration must be 2 letters followed by 7 digits,critical,P1,pharmaceuticals,true
VAL_TTB_PERMIT_001,TTB_PERMIT_NUMBER Not Null Check,TTB_PERMIT_NUMBER,DIM_MANUFACTURER,NOT_NULL,NOT_NULL,TTB Permit is required for alcoholic beverages,critical,P1,alcoholic_beverages,true
VAL_TTB_PERMIT_002,TTB_PERMIT_NUMBER Format Check,TTB_PERMIT_NUMBER,DIM_MANUFACTURER,REGEX,^[A-Z]{2}-[A-Z]{2}-\\d{5}$,TTB Permit must be format XX-XX-12345,critical,P1,alcoholic_beverages,true
VAL_GMP_CERTIFIED_001,GMP_CERTIFIED Boolean Check,GMP_CERTIFIED,DIM_MANUFACTURER,BOOLEAN,BOOLEAN,GMP Certified must be TRUE or FALSE,critical,P1,"pharmaceuticals,food_beverage,cosmetics",true
VAL_HACCP_CERTIFIED_001,HACCP_CERTIFIED Boolean Check,HACCP_CERTIFIED,DIM_MANUFACTURER,BOOLEAN,BOOLEAN,HACCP Certified must be TRUE or FALSE,critical,P1,food_beverage,true
VAL_ORGANIC_CERTIFIED_001,ORGANIC_CERTIFIED Boolean Check,ORGANIC_CERTIFIED,DIM_MANUFACTURER,BOOLEAN,BOOLEAN,Organic Certified must be TRUE or FALSE,high,P1,"food_beverage,cosmetics",true
VAL_QUALITY_SCORE_001,QUALITY_SCORE Range Check,QUALITY_SCORE,DIM_MANUFACTURER,RANGE,RANGE:0\,100,Quality Score must be between 0 and 100,high,P1,ALL,true
VAL_ON_TIME_DELIVERY_001,ON_TIME_DELIVERY_RATE Range Check,ON_TIME_DELIVERY_RATE,DIM_MANUFACTURER,RANGE,RANGE:0\,100,On-Time Delivery Rate must be between 0 and 100,high,P1,ALL,true
VAL_DEFECT_RATE_PPM_001,DEFECT_RATE_PPM Range Check,DEFECT_RATE_PPM,DIM_MANUFACTURER,RANGE,RANGE:0\,1000000,Defect Rate PPM must be between 0 and 1000000,high,P1,ALL,true
VAL_LAST_AUDIT_SCORE_001,LAST_AUDIT_SCORE Range Check,LAST_AUDIT_SCORE,DIM_MANUFACTURER,RANGE,RANGE:0\,100,Last Audit Score must be between 0 and 100,high,P1,ALL,true
VAL_RECALL_COUNT_5YR_001,RECALL_COUNT_5YR Range Check,RECALL_COUNT_5YR,DIM_MANUFACTURER,RANGE,RANGE:0\,100,5-Year Recall Count must be reasonable (0-100),high,P1,ALL,true
VAL_PAYMENT_TERMS_001,PAYMENT_TERMS Not Null Check,PAYMENT_TERMS,DIM_MANUFACTURER,NOT_NULL,NOT_NULL,Payment Terms are required,high,P1,ALL,true
VAL_PAYMENT_TERMS_002,PAYMENT_TERMS Domain Check,PAYMENT_TERMS,DIM_MANUFACTURER,ENUM,ENUM:Net 30\,Net 45\,Net 60\,2/10 Net 30\,Due on Receipt\,COD\,Prepaid,Invalid payment terms,high,P1,ALL,true
VAL_CREDIT_LIMIT_001,CREDIT_LIMIT Range Check,CREDIT_LIMIT,DIM_MANUFACTURER,RANGE,RANGE:0\,999999999.99,Credit Limit must be non-negative,medium,P2,ALL,true
VAL_CURRENCY_CODE_001,CURRENCY_CODE Format Check,CURRENCY_CODE,DIM_MANUFACTURER,REGEX,^[A-Z]{3}$,Currency Code must be 3 uppercase letters (ISO 4217),high,P1,ALL,true
VAL_INCOTERMS_001,INCOTERMS Domain Check,INCOTERMS,DIM_MANUFACTURER,ENUM,ENUM:EXW\,FOB\,CIF\,DDP\,DAP\,CPT\,CIP\,FCA\,FAS\,CFR\,DAT,Invalid Incoterms code,high,P1,ALL,true
VAL_LEAD_TIME_STANDARD_001,LEAD_TIME_STANDARD Range Check,LEAD_TIME_STANDARD,DIM_MANUFACTURER,RANGE,RANGE:1\,365,Standard Lead Time must be between 1 and 365 days,high,P1,ALL,true
VAL_CAPACITY_UTILIZATION_001,CAPACITY_UTILIZATION Range Check,CAPACITY_UTILIZATION,DIM_MANUFACTURER,RANGE,RANGE:0\,100,Capacity Utilization must be between 0 and 100,medium,P2,ALL,true
VAL_MINIMUM_ORDER_VALUE_001,MINIMUM_ORDER_VALUE Range Check,MINIMUM_ORDER_VALUE,DIM_MANUFACTURER,RANGE,RANGE:0\,999999999.99,Minimum Order Value must be non-negative,medium,P2,ALL,true
VAL_EDI_CAPABLE_001,EDI_CAPABLE Boolean Check,EDI_CAPABLE,DIM_MANUFACTURER,BOOLEAN,BOOLEAN,EDI Capable must be TRUE or FALSE,medium,P2,ALL,true
VAL_API_INTEGRATION_001,API_INTEGRATION Boolean Check,API_INTEGRATION,DIM_MANUFACTURER,BOOLEAN,BOOLEAN,API Integration must be TRUE or FALSE,medium,P2,ALL,true
VAL_SUSTAINABILITY_SCORE_001,SUSTAINABILITY_SCORE Range Check,SUSTAINABILITY_SCORE,DIM_MANUFACTURER,RANGE,RANGE:0\,100,Sustainability Score must be between 0 and 100,medium,P2,ALL,true
VAL_RENEWABLE_ENERGY_PCT_001,RENEWABLE_ENERGY_PERCENT Range Check,RENEWABLE_ENERGY_PERCENT,DIM_MANUFACTURER,RANGE,RANGE:0\,100,Renewable Energy Percentage must be between 0 and 100,medium,P2,ALL,true
VAL_WASTE_DIVERSION_RATE_001,WASTE_DIVERSION_RATE Range Check,WASTE_DIVERSION_RATE,DIM_MANUFACTURER,RANGE,RANGE:0\,100,Waste Diversion Rate must be between 0 and 100,low,P3,ALL,true
VAL_RISK_SCORE_001,RISK_SCORE Range Check,RISK_SCORE,DIM_MANUFACTURER,RANGE,RANGE:0\,100,Risk Score must be between 0 and 100,high,P1,ALL,true
VAL_INSURANCE_COVERAGE_001,INSURANCE_COVERAGE Range Check,INSURANCE_COVERAGE,DIM_MANUFACTURER,RANGE,RANGE:0\,999999999999.99,Insurance Coverage must be non-negative,high,P1,ALL,true
VAL_PRIMARY_CONTACT_EMAIL_001,PRIMARY_CONTACT_EMAIL Format Check,PRIMARY_CONTACT_EMAIL,DIM_MANUFACTURER,REGEX,^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2\,}$,Invalid email format,medium,P2,ALL,true
VAL_PRIMARY_CONTACT_PHONE_001,PRIMARY_CONTACT_PHONE Format Check,PRIMARY_CONTACT_PHONE,DIM_MANUFACTURER,REGEX,^\\+?[1-9]\\d{1\,14}$,Invalid phone format (E.164 standard),medium,P2,ALL,true
VAL_RECORD_STATUS_001,RECORD_STATUS Not Null Check,RECORD_STATUS,DIM_MANUFACTURER,NOT_NULL,NOT_NULL,Record Status is required,critical,P1,ALL,true
VAL_RECORD_STATUS_002,RECORD_STATUS Domain Check,RECORD_STATUS,DIM_MANUFACTURER,ENUM,ENUM:Active\,Inactive\,Suspended\,Terminated\,Pending,Invalid record status,critical,P1,ALL,true
VAL_SIZE_EMPLOYEE_CONSISTENCY_001,Company Size vs Employee Count Check,"COMPANY_SIZE,EMPLOYEE_COUNT",DIM_MANUFACTURER,CUSTOM,CUSTOM:IF(COMPANY_SIZE='Small'\,EMPLOYEE_COUNT<50) AND IF(COMPANY_SIZE='Medium'\,EMPLOYEE_COUNT BETWEEN 50 AND 250) AND IF(COMPANY_SIZE='Large'\,EMPLOYEE_COUNT>250),Company size inconsistent with employee count,medium,P2,ALL,true
VAL_AUDIT_CONSISTENCY_001,Audit Date vs Score Consistency Check,"LAST_AUDIT_DATE,LAST_AUDIT_SCORE",DIM_MANUFACTURER,CUSTOM,CUSTOM:IF(LAST_AUDIT_DATE!=NULL\,LAST_AUDIT_SCORE!=NULL),Audit date exists but score is missing,high,P1,ALL,true
VAL_SUPPLIER_HIERARCHY_001,Strategic vs Preferred Supplier Check,"STRATEGIC_SUPPLIER,PREFERRED_SUPPLIER",DIM_MANUFACTURER,CUSTOM,CUSTOM:IF(STRATEGIC_SUPPLIER=TRUE\,PREFERRED_SUPPLIER=TRUE),Strategic suppliers must also be preferred suppliers,high,P2,ALL,true
VAL_FDA_GMP_CONSISTENCY_001,FDA Registration vs GMP Consistency Check,"FDA_FACILITY_REGISTRATION,GMP_CERTIFIED",DIM_MANUFACTURER,CUSTOM,CUSTOM:IF(GMP_CERTIFIED=TRUE\,FDA_FACILITY_REGISTRATION!=NULL),GMP certified facilities must have FDA registration,critical,P1,"pharmaceuticals,food_beverage,cosmetics",true
VAL_CAPACITY_MOV_001,Production Capacity vs MOV Check,"PRODUCTION_CAPACITY,MINIMUM_ORDER_VALUE",DIM_MANUFACTURER,CUSTOM,CUSTOM:MINIMUM_ORDER_VALUE<=(PRODUCTION_CAPACITY*0.05),Minimum order value seems too high for production capacity,medium,P3,ALL,true
VAL_RISK_INSURANCE_001,Risk Score vs Insurance Coverage Check,"RISK_SCORE,INSURANCE_COVERAGE",DIM_MANUFACTURER,CUSTOM,CUSTOM:IF(RISK_SCORE>75\,INSURANCE_COVERAGE>=10000000),High risk suppliers need minimum $10M insurance coverage,high,P1,ALL,true
VAL_CONTRACT_STATUS_001,Contract Expiry vs Record Status Check,"CONTRACT_EXPIRY_DATE,RECORD_STATUS",DIM_MANUFACTURER,CUSTOM,CUSTOM:IF(CONTRACT_EXPIRY_DATE<TODAY AND RECORD_STATUS='Active'\,WARNING),Active supplier has expired contract,high,P1,ALL,true
VAL_SUSTAINABILITY_COMPONENTS_001,Sustainability Score Components Check,"SUSTAINABILITY_SCORE,RENEWABLE_ENERGY_PERCENT,WASTE_DIVERSION_RATE",DIM_MANUFACTURER,CUSTOM,CUSTOM:IF(SUSTAINABILITY_SCORE>80\,(RENEWABLE_ENERGY_PERCENT>50 AND WASTE_DIVERSION_RATE>75)),High sustainability score requires strong component metrics,medium,P3,ALL,true