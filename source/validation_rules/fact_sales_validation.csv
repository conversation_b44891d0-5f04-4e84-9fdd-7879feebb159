rule_id,rule_name,column_name,table_id,validation_type,validation_rule,error_message,business_criticality,priority,applicable_domains,is_active
VAL_SALES_TRANSACTION_ID_001,SALES_TRANSACTION_ID Not Null Check,SALES_TRANSACTION_ID,FACT_SALES,NOT_NULL,NOT_NULL,Sales Transaction ID is required,critical,P1,ALL,TRUE
VAL_SALES_TRANSACTION_ID_002,SALES_TRANSACTION_ID Uniqueness Check,SALES_TRANSACTION_ID,FACT_SALES,UNIQUE,UNIQUE,Sales Transaction ID must be unique,critical,P1,ALL,TRUE
VAL_SALES_TRANSACTION_ID_003,SALES_TRANSACTION_ID Format Check,SALES_TRANSACTION_ID,FACT_SALES,REGEX,^[A-Za-z0-9_-]{1,50}$,Sales Transaction ID must be alphanumeric with dash/underscore max 50 chars,critical,P1,ALL,TRUE
VAL_DATE_KEY_001,DATE_KEY Not Null Check,DATE_KEY,FACT_SALES,NOT_NULL,NOT_NULL,Date Key is required,critical,P1,ALL,TRUE
VAL_DATE_KEY_002,DATE_KEY Range Check,DATE_KEY,FACT_SALES,RANGE,RANGE:20000101,20301231,Date Key must be between 20000101 and 20301231,critical,P1,ALL,TRUE
VAL_STORE_KEY_001,STORE_KEY Not Null Check,STORE_KEY,FACT_SALES,NOT_NULL,NOT_NULL,Store Key is required,critical,P1,ALL,TRUE
VAL_PRODUCT_HIERARCHY_KEY_001,PRODUCT_HIERARCHY_KEY Not Null Check,PRODUCT_HIERARCHY_KEY,FACT_SALES,NOT_NULL,NOT_NULL,Product Hierarchy Key is required,critical,P1,ALL,TRUE
VAL_QUANTITY_SOLD_001,QUANTITY_SOLD Not Null Check,QUANTITY_SOLD,FACT_SALES,NOT_NULL,NOT_NULL,Quantity Sold is required,critical,P1,ALL,TRUE
VAL_QUANTITY_SOLD_002,QUANTITY_SOLD Range Check,QUANTITY_SOLD,FACT_SALES,RANGE,RANGE:0.001,999999.999,Quantity Sold must be positive,critical,P1,ALL,TRUE
VAL_CASES_SOLD_001,CASES_SOLD Range Check,CASES_SOLD,FACT_SALES,RANGE,RANGE:0,99999.999,Cases Sold must be non-negative,medium,P2,ALL,TRUE
VAL_WEIGHT_SOLD_001,WEIGHT_SOLD Range Check,WEIGHT_SOLD,FACT_SALES,RANGE,RANGE:0,999999.999,Weight Sold must be non-negative,medium,P2,ALL,TRUE
VAL_GROSS_SALES_AMOUNT_001,GROSS_SALES_AMOUNT Not Null Check,GROSS_SALES_AMOUNT,FACT_SALES,NOT_NULL,NOT_NULL,Gross Sales Amount is required,critical,P1,ALL,TRUE
VAL_GROSS_SALES_AMOUNT_002,GROSS_SALES_AMOUNT Range Check,GROSS_SALES_AMOUNT,FACT_SALES,RANGE,RANGE:0.01,*********999.99,Gross Sales Amount must be positive,critical,P1,ALL,TRUE
VAL_NET_SALES_AMOUNT_001,NET_SALES_AMOUNT Not Null Check,NET_SALES_AMOUNT,FACT_SALES,NOT_NULL,NOT_NULL,Net Sales Amount is required,critical,P1,ALL,TRUE
VAL_NET_SALES_AMOUNT_002,NET_SALES_AMOUNT Range Check,NET_SALES_AMOUNT,FACT_SALES,RANGE,RANGE:0.01,*********999.99,Net Sales Amount must be positive,critical,P1,ALL,TRUE
VAL_UNIT_PRICE_001,UNIT_PRICE Range Check,UNIT_PRICE,FACT_SALES,RANGE,RANGE:0.01,999999.99,Unit Price must be positive,high,P1,ALL,TRUE
VAL_PROMOTIONAL_DISCOUNT_001,PROMOTIONAL_DISCOUNT_AMOUNT Range Check,PROMOTIONAL_DISCOUNT_AMOUNT,FACT_SALES,RANGE,RANGE:0,99999999.99,Promotional Discount Amount must be non-negative,high,P1,ALL,TRUE
VAL_TOTAL_DISCOUNT_AMOUNT_001,TOTAL_DISCOUNT_AMOUNT Range Check,TOTAL_DISCOUNT_AMOUNT,FACT_SALES,RANGE,RANGE:0,99999999.99,Total Discount Amount must be non-negative,high,P1,ALL,TRUE
VAL_COST_OF_GOODS_SOLD_001,COST_OF_GOODS_SOLD Not Null Check,COST_OF_GOODS_SOLD,FACT_SALES,NOT_NULL,NOT_NULL,Cost of Goods Sold is required,critical,P1,ALL,TRUE
VAL_COST_OF_GOODS_SOLD_002,COST_OF_GOODS_SOLD Range Check,COST_OF_GOODS_SOLD,FACT_SALES,RANGE,RANGE:0,*********999.99,Cost of Goods Sold must be non-negative,critical,P1,ALL,TRUE
VAL_GROSS_MARGIN_PERCENT_001,GROSS_MARGIN_PERCENT Range Check,GROSS_MARGIN_PERCENT,FACT_SALES,RANGE,RANGE:-100,100,Gross Margin Percent must be between -100 and 100,high,P1,ALL,TRUE
VAL_SALES_TAX_AMOUNT_001,SALES_TAX_AMOUNT Range Check,SALES_TAX_AMOUNT,FACT_SALES,RANGE,RANGE:0,9999999.99,Sales Tax Amount must be non-negative,medium,P2,ALL,TRUE
VAL_EXCISE_TAX_AMOUNT_001,EXCISE_TAX_AMOUNT Range Check,EXCISE_TAX_AMOUNT,FACT_SALES,RANGE,RANGE:0,9999999.99,Excise Tax Amount must be non-negative,high,P1,"alcoholic_beverages,pharmaceuticals,battery",TRUE
VAL_RETURN_QUANTITY_001,RETURN_QUANTITY Range Check,RETURN_QUANTITY,FACT_SALES,RANGE,RANGE:0,999999.999,Return Quantity must be non-negative,medium,P2,ALL,TRUE
VAL_RETURN_AMOUNT_001,RETURN_AMOUNT Range Check,RETURN_AMOUNT,FACT_SALES,RANGE,RANGE:0,*********.99,Return Amount must be non-negative,medium,P2,ALL,TRUE
VAL_DIGITAL_SALES_FLAG_001,DIGITAL_SALES_FLAG Boolean Check,DIGITAL_SALES_FLAG,FACT_SALES,BOOLEAN,BOOLEAN,Digital Sales Flag must be TRUE or FALSE,high,P1,ALL,TRUE
VAL_MOBILE_SALES_FLAG_001,MOBILE_SALES_FLAG Boolean Check,MOBILE_SALES_FLAG,FACT_SALES,BOOLEAN,BOOLEAN,Mobile Sales Flag must be TRUE or FALSE,medium,P2,ALL,TRUE
VAL_AGE_VERIFICATION_FLAG_001,AGE_VERIFICATION_FLAG Boolean Check,AGE_VERIFICATION_FLAG,FACT_SALES,BOOLEAN,BOOLEAN,Age Verification Flag must be TRUE or FALSE,critical,P1,"alcoholic_beverages,pharmaceuticals",TRUE
VAL_REPEAT_PURCHASE_FLAG_001,REPEAT_PURCHASE_FLAG Boolean Check,REPEAT_PURCHASE_FLAG,FACT_SALES,BOOLEAN,BOOLEAN,Repeat Purchase Flag must be TRUE or FALSE,high,P1,ALL,TRUE
VAL_PRESCRIPTION_NUMBER_001,PRESCRIPTION_NUMBER Format Check,PRESCRIPTION_NUMBER,FACT_SALES,REGEX,^[A-Za-z0-9]{1,50}$,Prescription Number must be alphanumeric max 50 chars,critical,P1,pharmaceuticals,TRUE
VAL_PAYMENT_METHOD_001,PAYMENT_METHOD Domain Check,PAYMENT_METHOD,FACT_SALES,ENUM,"ENUM:Cash,Credit Card,Debit Card,Digital Wallet,Bank Transfer,Check,Gift Card,Store Credit,BNPL",Invalid payment method,medium,P2,ALL,TRUE
VAL_PRODUCT_RATING_001,PRODUCT_RATING Range Check,PRODUCT_RATING,FACT_SALES,RANGE,RANGE:1,5,Product Rating must be between 1 and 5,medium,P2,ALL,TRUE
VAL_LOT_NUMBER_001,LOT_NUMBER Format Check,LOT_NUMBER,FACT_SALES,REGEX,^[A-Za-z0-9-]{1,50}$,Lot Number must be alphanumeric with dash max 50 chars,high,P1,ALL,TRUE