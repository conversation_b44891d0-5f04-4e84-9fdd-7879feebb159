// ========================================
// DATA QUALITY VALIDATION RULES FOR DIM_CATEGORY
// This script creates validation rules and links them to columns
// ========================================

// Create constraint for ValidationRule uniqueness (run once if not exists)
CREATE CONSTRAINT validation_rule_unique IF NOT EXISTS
FOR (v:ValidationRule) REQUIRE v.rule_id IS UNIQUE;

// ========================================
// CATEGORY_ID Validations
// ========================================

// CATEGORY_ID - NOT NULL
MERGE (v1:ValidationRule {rule_id: "VAL_CATEGORY_ID_001"})
SET v1.rule_name = "CATEGORY_ID Not Null Check",
    v1.column_name = "CATEGORY_ID",
    v1.table_id = "DIM_CATEGORY",
    v1.validation_type = "NOT_NULL",
    v1.validation_rule = "NOT_NULL",
    v1.error_message = "Category ID is required",
    v1.business_criticality = "critical",
    v1.priority = "P1",
    v1.applicable_domains = ["ALL"],
    v1.is_active = true,
    v1.created_date = datetime(),
    v1.last_updated_date = datetime()
WITH v1
MATCH (c:Column {column_id: "COL_CATEGORY_ID_DIM_250"})
MERGE (c)-[:HAS_VALIDATION]->(v1);

// CATEGORY_ID - UNIQUE
MERGE (v2:ValidationRule {rule_id: "VAL_CATEGORY_ID_002"})
SET v2.rule_name = "CATEGORY_ID Uniqueness Check",
    v2.column_name = "CATEGORY_ID",
    v2.table_id = "DIM_CATEGORY",
    v2.validation_type = "UNIQUE",
    v2.validation_rule = "UNIQUE",
    v2.error_message = "Category ID must be unique",
    v2.business_criticality = "critical",
    v2.priority = "P1",
    v2.applicable_domains = ["ALL"],
    v2.is_active = true,
    v2.created_date = datetime(),
    v2.last_updated_date = datetime()
WITH v2
MATCH (c:Column {column_id: "COL_CATEGORY_ID_DIM_250"})
MERGE (c)-[:HAS_VALIDATION]->(v2);

// CATEGORY_ID - FORMAT
MERGE (v3:ValidationRule {rule_id: "VAL_CATEGORY_ID_003"})
SET v3.rule_name = "CATEGORY_ID Format Check",
    v3.column_name = "CATEGORY_ID",
    v3.table_id = "DIM_CATEGORY",
    v3.validation_type = "REGEX",
    v3.validation_rule = "^[A-Za-z0-9_-]{1,20}$",
    v3.error_message = "Category ID must be alphanumeric with underscores/hyphens, max 20 chars",
    v3.business_criticality = "critical",
    v3.priority = "P1",
    v3.applicable_domains = ["ALL"],
    v3.is_active = true,
    v3.created_date = datetime(),
    v3.last_updated_date = datetime()
WITH v3
MATCH (c:Column {column_id: "COL_CATEGORY_ID_DIM_250"})
MERGE (c)-[:HAS_VALIDATION]->(v3);

// ========================================
// CATEGORY_NAME Validations
// ========================================

// CATEGORY_NAME - NOT NULL
MERGE (v4:ValidationRule {rule_id: "VAL_CATEGORY_NAME_001"})
SET v4.rule_name = "CATEGORY_NAME Not Null Check",
    v4.column_name = "CATEGORY_NAME",
    v4.table_id = "DIM_CATEGORY",
    v4.validation_type = "NOT_NULL",
    v4.validation_rule = "NOT_NULL",
    v4.error_message = "Category Name is required",
    v4.business_criticality = "critical",
    v4.priority = "P1",
    v4.applicable_domains = ["ALL"],
    v4.is_active = true,
    v4.created_date = datetime(),
    v4.last_updated_date = datetime()
WITH v4
MATCH (c:Column {column_id: "COL_CATEGORY_NAME_251"})
MERGE (c)-[:HAS_VALIDATION]->(v4);

// CATEGORY_NAME - LENGTH
MERGE (v5:ValidationRule {rule_id: "VAL_CATEGORY_NAME_002"})
SET v5.rule_name = "CATEGORY_NAME Length Check",
    v5.column_name = "CATEGORY_NAME",
    v5.table_id = "DIM_CATEGORY",
    v5.validation_type = "LENGTH",
    v5.validation_rule = "LENGTH:1,200",
    v5.error_message = "Category Name must be 1-200 characters",
    v5.business_criticality = "critical",
    v5.priority = "P1",
    v5.applicable_domains = ["ALL"],
    v5.is_active = true,
    v5.created_date = datetime(),
    v5.last_updated_date = datetime()
WITH v5
MATCH (c:Column {column_id: "COL_CATEGORY_NAME_251"})
MERGE (c)-[:HAS_VALIDATION]->(v5);

// ========================================
// CATEGORY_LEVEL Validations
// ========================================

// CATEGORY_LEVEL - NOT NULL
MERGE (v6:ValidationRule {rule_id: "VAL_CATEGORY_LEVEL_001"})
SET v6.rule_name = "CATEGORY_LEVEL Not Null Check",
    v6.column_name = "CATEGORY_LEVEL",
    v6.table_id = "DIM_CATEGORY",
    v6.validation_type = "NOT_NULL",
    v6.validation_rule = "NOT_NULL",
    v6.error_message = "Category Level is required",
    v6.business_criticality = "high",
    v6.priority = "P1",
    v6.applicable_domains = ["ALL"],
    v6.is_active = true,
    v6.created_date = datetime(),
    v6.last_updated_date = datetime()
WITH v6
MATCH (c:Column {column_id: "COL_CATEGORY_LEVEL_252"})
MERGE (c)-[:HAS_VALIDATION]->(v6);

// CATEGORY_LEVEL - RANGE
MERGE (v7:ValidationRule {rule_id: "VAL_CATEGORY_LEVEL_002"})
SET v7.rule_name = "CATEGORY_LEVEL Range Check",
    v7.column_name = "CATEGORY_LEVEL",
    v7.table_id = "DIM_CATEGORY",
    v7.validation_type = "RANGE",
    v7.validation_rule = "RANGE:1,5",
    v7.error_message = "Category Level must be between 1 and 5",
    v7.business_criticality = "high",
    v7.priority = "P1",
    v7.applicable_domains = ["ALL"],
    v7.is_active = true,
    v7.created_date = datetime(),
    v7.last_updated_date = datetime()
WITH v7
MATCH (c:Column {column_id: "COL_CATEGORY_LEVEL_252"})
MERGE (c)-[:HAS_VALIDATION]->(v7);

// ========================================
// PARENT_CATEGORY_ID Validations
// ========================================

// PARENT_CATEGORY_ID - REFERENCE
MERGE (v8:ValidationRule {rule_id: "VAL_PARENT_CATEGORY_ID_001"})
SET v8.rule_name = "PARENT_CATEGORY_ID Reference Check",
    v8.column_name = "PARENT_CATEGORY_ID",
    v8.table_id = "DIM_CATEGORY",
    v8.validation_type = "REFERENCE",
    v8.validation_rule = "FK:DIM_CATEGORY.CATEGORY_ID",
    v8.error_message = "Parent Category ID must exist in Category dimension",
    v8.business_criticality = "high",
    v8.priority = "P1",
    v8.applicable_domains = ["ALL"],
    v8.is_active = true,
    v8.created_date = datetime(),
    v8.last_updated_date = datetime()
WITH v8
MATCH (c:Column {column_id: "COL_PARENT_CATEGORY_ID_253"})
MERGE (c)-[:HAS_VALIDATION]->(v8);

// ========================================
// CATEGORY_ROLE Validations
// ========================================

// CATEGORY_ROLE - NOT NULL
MERGE (v9:ValidationRule {rule_id: "VAL_CATEGORY_ROLE_001"})
SET v9.rule_name = "CATEGORY_ROLE Not Null Check",
    v9.column_name = "CATEGORY_ROLE",
    v9.table_id = "DIM_CATEGORY",
    v9.validation_type = "NOT_NULL",
    v9.validation_rule = "NOT_NULL",
    v9.error_message = "Category Role is required",
    v9.business_criticality = "high",
    v9.priority = "P1",
    v9.applicable_domains = ["ALL"],
    v9.is_active = true,
    v9.created_date = datetime(),
    v9.last_updated_date = datetime()
WITH v9
MATCH (c:Column {column_id: "COL_CATEGORY_ROLE_256"})
MERGE (c)-[:HAS_VALIDATION]->(v9);

// CATEGORY_ROLE - ENUM
MERGE (v10:ValidationRule {rule_id: "VAL_CATEGORY_ROLE_002"})
SET v10.rule_name = "CATEGORY_ROLE Domain Check",
    v10.column_name = "CATEGORY_ROLE",
    v10.table_id = "DIM_CATEGORY",
    v10.validation_type = "ENUM",
    v10.validation_rule = "ENUM:Destination,Routine,Convenience,Seasonal/Fill-in",
    v10.error_message = "Invalid category role",
    v10.business_criticality = "high",
    v10.priority = "P1",
    v10.applicable_domains = ["ALL"],
    v10.is_active = true,
    v10.created_date = datetime(),
    v10.last_updated_date = datetime()
WITH v10
MATCH (c:Column {column_id: "COL_CATEGORY_ROLE_256"})
MERGE (c)-[:HAS_VALIDATION]->(v10);

// ========================================
// CATEGORY_STATUS Validations
// ========================================

// CATEGORY_STATUS - NOT NULL
MERGE (v11:ValidationRule {rule_id: "VAL_CATEGORY_STATUS_001"})
SET v11.rule_name = "CATEGORY_STATUS Not Null Check",
    v11.column_name = "CATEGORY_STATUS",
    v11.table_id = "DIM_CATEGORY",
    v11.validation_type = "NOT_NULL",
    v11.validation_rule = "NOT_NULL",
    v11.error_message = "Category Status is required",
    v11.business_criticality = "high",
    v11.priority = "P1",
    v11.applicable_domains = ["ALL"],
    v11.is_active = true,
    v11.created_date = datetime(),
    v11.last_updated_date = datetime()
WITH v11
MATCH (c:Column {column_id: "COL_CATEGORY_STATUS_301"})
MERGE (c)-[:HAS_VALIDATION]->(v11);

// CATEGORY_STATUS - ENUM
MERGE (v12:ValidationRule {rule_id: "VAL_CATEGORY_STATUS_002"})
SET v12.rule_name = "CATEGORY_STATUS Domain Check",
    v12.column_name = "CATEGORY_STATUS",
    v12.table_id = "DIM_CATEGORY",
    v12.validation_type = "ENUM",
    v12.validation_rule = "ENUM:Active,Phasing Out,Discontinued,Seasonal",
    v12.error_message = "Invalid category status",
    v12.business_criticality = "high",
    v12.priority = "P1",
    v12.applicable_domains = ["ALL"],
    v12.is_active = true,
    v12.created_date = datetime(),
    v12.last_updated_date = datetime()
WITH v12
MATCH (c:Column {column_id: "COL_CATEGORY_STATUS_301"})
MERGE (c)-[:HAS_VALIDATION]->(v12);

// ========================================
// Financial Metric Validations
// ========================================

// GROSS_MARGIN_PERCENT - RANGE
MERGE (v13:ValidationRule {rule_id: "VAL_GROSS_MARGIN_PCT_001"})
SET v13.rule_name = "GROSS_MARGIN_PERCENT Range Check",
    v13.column_name = "GROSS_MARGIN_PERCENT",
    v13.table_id = "DIM_CATEGORY",
    v13.validation_type = "RANGE",
    v13.validation_rule = "RANGE:0.0,100.0",
    v13.error_message = "Gross Margin must be between 0 and 100",
    v13.business_criticality = "high",
    v13.priority = "P2",
    v13.applicable_domains = ["ALL"],
    v13.is_active = true,
    v13.created_date = datetime(),
    v13.last_updated_date = datetime()
WITH v13
MATCH (c:Column {column_id: "COL_GROSS_MARGIN_PCT_268"})
MERGE (c)-[:HAS_VALIDATION]->(v13);

// PRIVATE_LABEL_SHARE - RANGE
MERGE (v14:ValidationRule {rule_id: "VAL_PRIVATE_LABEL_SHARE_001"})
SET v14.rule_name = "PRIVATE_LABEL_SHARE Range Check",
    v14.column_name = "PRIVATE_LABEL_SHARE",
    v14.table_id = "DIM_CATEGORY",
    v14.validation_type = "RANGE",
    v14.validation_rule = "RANGE:0.0,100.0",
    v14.error_message = "Private Label Share must be between 0 and 100",
    v14.business_criticality = "high",
    v14.priority = "P2",
    v14.applicable_domains = ["ALL"],
    v14.is_active = true,
    v14.created_date = datetime(),
    v14.last_updated_date = datetime()
WITH v14
MATCH (c:Column {column_id: "COL_PRIVATE_LABEL_SHARE_269"})
MERGE (c)-[:HAS_VALIDATION]->(v14);

// BASKET_PENETRATION - RANGE
MERGE (v15:ValidationRule {rule_id: "VAL_BASKET_PENETRATION_001"})
SET v15.rule_name = "BASKET_PENETRATION Range Check",
    v15.column_name = "BASKET_PENETRATION",
    v15.table_id = "DIM_CATEGORY",
    v15.validation_type = "RANGE",
    v15.validation_rule = "RANGE:0.0,100.0",
    v15.error_message = "Basket Penetration must be between 0 and 100",
    v15.business_criticality = "high",
    v15.priority = "P2",
    v15.applicable_domains = ["ALL"],
    v15.is_active = true,
    v15.created_date = datetime(),
    v15.last_updated_date = datetime()
WITH v15
MATCH (c:Column {column_id: "COL_BASKET_PENETRATION_260"})
MERGE (c)-[:HAS_VALIDATION]->(v15);

// HOUSEHOLD_PENETRATION - RANGE
MERGE (v16:ValidationRule {rule_id: "VAL_HOUSEHOLD_PENETRATION_001"})
SET v16.rule_name = "HOUSEHOLD_PENETRATION Range Check",
    v16.column_name = "HOUSEHOLD_PENETRATION",
    v16.table_id = "DIM_CATEGORY",
    v16.validation_type = "RANGE",
    v16.validation_rule = "RANGE:0.0,100.0",
    v16.error_message = "Household Penetration must be between 0 and 100",
    v16.business_criticality = "high",
    v16.priority = "P2",
    v16.applicable_domains = ["ALL"],
    v16.is_active = true,
    v16.created_date = datetime(),
    v16.last_updated_date = datetime()
WITH v16
MATCH (c:Column {column_id: "COL_HOUSEHOLD_PENETRATION_274"})
MERGE (c)-[:HAS_VALIDATION]->(v16);

// CATEGORY_REVENUE_LTM - RANGE
MERGE (v17:ValidationRule {rule_id: "VAL_CATEGORY_REVENUE_LTM_001"})
SET v17.rule_name = "CATEGORY_REVENUE_LTM Range Check",
    v17.column_name = "CATEGORY_REVENUE_LTM",
    v17.table_id = "DIM_CATEGORY",
    v17.validation_type = "RANGE",
    v17.validation_rule = "RANGE:0,999999999999.99",
    v17.error_message = "Category Revenue must be non-negative",
    v17.business_criticality = "high",
    v17.priority = "P2",
    v17.applicable_domains = ["ALL"],
    v17.is_active = true,
    v17.created_date = datetime(),
    v17.last_updated_date = datetime()
WITH v17
MATCH (c:Column {column_id: "COL_CATEGORY_REVENUE_LTM_265"})
MERGE (c)-[:HAS_VALIDATION]->(v17);

// AVERAGE_UNIT_PRICE - RANGE
MERGE (v18:ValidationRule {rule_id: "VAL_AVERAGE_UNIT_PRICE_001"})
SET v18.rule_name = "AVERAGE_UNIT_PRICE Range Check",
    v18.column_name = "AVERAGE_UNIT_PRICE",
    v18.table_id = "DIM_CATEGORY",
    v18.validation_type = "RANGE",
    v18.validation_rule = "RANGE:0.01,9999.99",
    v18.error_message = "Average Unit Price must be positive",
    v18.business_criticality = "high",
    v18.priority = "P2",
    v18.applicable_domains = ["ALL"],
    v18.is_active = true,
    v18.created_date = datetime(),
    v18.last_updated_date = datetime()
WITH v18
MATCH (c:Column {column_id: "COL_AVERAGE_UNIT_PRICE_267"})
MERGE (c)-[:HAS_VALIDATION]->(v18);

// ========================================
// Merchandising Validations
// ========================================

// PURCHASE_FREQUENCY - RANGE
MERGE (v19:ValidationRule {rule_id: "VAL_PURCHASE_FREQUENCY_001"})
SET v19.rule_name = "PURCHASE_FREQUENCY Range Check",
    v19.column_name = "PURCHASE_FREQUENCY",
    v19.table_id = "DIM_CATEGORY",
    v19.validation_type = "RANGE",
    v19.validation_rule = "RANGE:1,365",
    v19.error_message = "Purchase Frequency must be between 1 and 365 days",
    v19.business_criticality = "high",
    v19.priority = "P2",
    v19.applicable_domains = ["ALL"],
    v19.is_active = true,
    v19.created_date = datetime(),
    v19.last_updated_date = datetime()
WITH v19
MATCH (c:Column {column_id: "COL_PURCHASE_FREQUENCY_259"})
MERGE (c)-[:HAS_VALIDATION]->(v19);

// SPACE_TO_SALES_INDEX - RANGE
MERGE (v20:ValidationRule {rule_id: "VAL_SPACE_TO_SALES_INDEX_001"})
SET v20.rule_name = "SPACE_TO_SALES_INDEX Range Check",
    v20.column_name = "SPACE_TO_SALES_INDEX",
    v20.table_id = "DIM_CATEGORY",
    v20.validation_type = "RANGE",
    v20.validation_rule = "RANGE:0,500",
    v20.error_message = "Space to Sales Index must be between 0 and 500",
    v20.business_criticality = "high",
    v20.priority = "P2",
    v20.applicable_domains = ["ALL"],
    v20.is_active = true,
    v20.created_date = datetime(),
    v20.last_updated_date = datetime()
WITH v20
MATCH (c:Column {column_id: "COL_SPACE_TO_SALES_INDEX_298"})
MERGE (c)-[:HAS_VALIDATION]->(v20);

// ========================================
// Promotional Effectiveness Validations
// ========================================

// PROMOTIONAL_SENSITIVITY - RANGE
MERGE (v21:ValidationRule {rule_id: "VAL_PROMOTIONAL_SENSITIVITY_001"})
SET v21.rule_name = "PROMOTIONAL_SENSITIVITY Range Check",
    v21.column_name = "PROMOTIONAL_SENSITIVITY",
    v21.table_id = "DIM_CATEGORY",
    v21.validation_type = "RANGE",
    v21.validation_rule = "RANGE:0,100",
    v21.error_message = "Promotional Sensitivity must be between 0 and 100",
    v21.business_criticality = "high",
    v21.priority = "P2",
    v21.applicable_domains = ["ALL"],
    v21.is_active = true,
    v21.created_date = datetime(),
    v21.last_updated_date = datetime()
WITH v21
MATCH (c:Column {column_id: "COL_PROMOTIONAL_SENSITIVITY_278"})
MERGE (c)-[:HAS_VALIDATION]->(v21);

// OPTIMAL_DISCOUNT_DEPTH - RANGE
MERGE (v22:ValidationRule {rule_id: "VAL_OPTIMAL_DISCOUNT_001"})
SET v22.rule_name = "OPTIMAL_DISCOUNT_DEPTH Range Check",
    v22.column_name = "OPTIMAL_DISCOUNT_DEPTH",
    v22.table_id = "DIM_CATEGORY",
    v22.validation_type = "RANGE",
    v22.validation_rule = "RANGE:0,100",
    v22.error_message = "Optimal Discount Depth must be between 0 and 100",
    v22.business_criticality = "high",
    v22.priority = "P2",
    v22.applicable_domains = ["ALL"],
    v22.is_active = true,
    v22.created_date = datetime(),
    v22.last_updated_date = datetime()
WITH v22
MATCH (c:Column {column_id: "COL_OPTIMAL_DISCOUNT_279"})
MERGE (c)-[:HAS_VALIDATION]->(v22);

// ========================================
// Seasonality Validations
// ========================================

// SEASONALITY_INDEX - RANGE
MERGE (v23:ValidationRule {rule_id: "VAL_SEASONALITY_INDEX_001"})
SET v23.rule_name = "SEASONALITY_INDEX Range Check",
    v23.column_name = "SEASONALITY_INDEX",
    v23.table_id = "DIM_CATEGORY",
    v23.validation_type = "RANGE",
    v23.validation_rule = "RANGE:0,100",
    v23.error_message = "Seasonality Index must be between 0 and 100",
    v23.business_criticality = "high",
    v23.priority = "P2",
    v23.applicable_domains = ["ALL"],
    v23.is_active = true,
    v23.created_date = datetime(),
    v23.last_updated_date = datetime()
WITH v23
MATCH (c:Column {column_id: "COL_SEASONALITY_INDEX_281"})
MERGE (c)-[:HAS_VALIDATION]->(v23);

// PEAK_SEASON_PERIOD - ENUM
MERGE (v24:ValidationRule {rule_id: "VAL_PEAK_SEASON_PERIOD_001"})
SET v24.rule_name = "PEAK_SEASON_PERIOD Domain Check",
    v24.column_name = "PEAK_SEASON_PERIOD",
    v24.table_id = "DIM_CATEGORY",
    v24.validation_type = "ENUM",
    v24.validation_rule = "ENUM:Q1,Q2,Q3,Q4,Q4 Holiday,Summer,Back-to-School,Spring,Fall,Winter,Year-Round",
    v24.error_message = "Invalid peak season period",
    v24.business_criticality = "high",
    v24.priority = "P2",
    v24.applicable_domains = ["ALL"],
    v24.is_active = true,
    v24.created_date = datetime(),
    v24.last_updated_date = datetime()
WITH v24
MATCH (c:Column {column_id: "COL_PEAK_SEASON_PERIOD_282"})
MERGE (c)-[:HAS_VALIDATION]->(v24);

// ========================================
// Innovation Validations
// ========================================

// INNOVATION_RATE - RANGE
MERGE (v25:ValidationRule {rule_id: "VAL_INNOVATION_RATE_001"})
SET v25.rule_name = "INNOVATION_RATE Range Check",
    v25.column_name = "INNOVATION_RATE",
    v25.table_id = "DIM_CATEGORY",
    v25.validation_type = "RANGE",
    v25.validation_rule = "RANGE:0,100",
    v25.error_message = "Innovation Rate must be between 0 and 100",
    v25.business_criticality = "high",
    v25.priority = "P2",
    v25.applicable_domains = ["ALL"],
    v25.is_active = true,
    v25.created_date = datetime(),
    v25.last_updated_date = datetime()
WITH v25
MATCH (c:Column {column_id: "COL_INNOVATION_RATE_284"})
MERGE (c)-[:HAS_VALIDATION]->(v25);

// DIGITAL_ENGAGEMENT_LEVEL - ENUM
MERGE (v26:ValidationRule {rule_id: "VAL_DIGITAL_ENGAGEMENT_001"})
SET v26.rule_name = "DIGITAL_ENGAGEMENT_LEVEL Domain Check",
    v26.column_name = "DIGITAL_ENGAGEMENT_LEVEL",
    v26.table_id = "DIM_CATEGORY",
    v26.validation_type = "ENUM",
    v26.validation_rule = "ENUM:Low,Medium,High",
    v26.error_message = "Digital Engagement Level must be Low, Medium, or High",
    v26.business_criticality = "medium",
    v26.priority = "P3",
    v26.applicable_domains = ["ALL"],
    v26.is_active = true,
    v26.created_date = datetime(),
    v26.last_updated_date = datetime()
WITH v26
MATCH (c:Column {column_id: "COL_DIGITAL_ENGAGEMENT_286"})
MERGE (c)-[:HAS_VALIDATION]->(v26);

// ========================================
// Domain-Specific Validations
// ========================================

// CONTROLLED_SUBSTANCE_FLAG - NOT NULL (Pharmaceuticals)
MERGE (v27:ValidationRule {rule_id: "VAL_CONTROLLED_SUBSTANCE_001"})
SET v27.rule_name = "CONTROLLED_SUBSTANCE_FLAG Not Null Check",
    v27.column_name = "CONTROLLED_SUBSTANCE_FLAG",
    v27.table_id = "DIM_CATEGORY",
    v27.validation_type = "NOT_NULL",
    v27.validation_rule = "NOT_NULL",
    v27.error_message = "Controlled Substance Flag is required for pharmaceutical categories",
    v27.business_criticality = "critical",
    v27.priority = "P1",
    v27.applicable_domains = ["pharmaceuticals"],
    v27.is_active = true,
    v27.created_date = datetime(),
    v27.last_updated_date = datetime()
WITH v27
MATCH (c:Column {column_id: "COL_CONTROLLED_SUBSTANCE_291"})
MERGE (c)-[:HAS_VALIDATION]->(v27);

// AGE_RESTRICTED_FLAG - NOT NULL (Alcoholic Beverages, Toys)
MERGE (v28:ValidationRule {rule_id: "VAL_AGE_RESTRICTED_FLAG_001"})
SET v28.rule_name = "AGE_RESTRICTED_FLAG Not Null Check",
    v28.column_name = "AGE_RESTRICTED_FLAG",
    v28.table_id = "DIM_CATEGORY",
    v28.validation_type = "NOT_NULL",
    v28.validation_rule = "NOT_NULL",
    v28.error_message = "Age Restricted Flag is required",
    v28.business_criticality = "high",
    v28.priority = "P1",
    v28.applicable_domains = ["alcoholic_beverages", "toys"],
    v28.is_active = true,
    v28.created_date = datetime(),
    v28.last_updated_date = datetime()
WITH v28
MATCH (c:Column {column_id: "COL_AGE_RESTRICTED_FLAG_292"})
MERGE (c)-[:HAS_VALIDATION]->(v28);

// BATTERY_RECYCLING_REQUIRED - NOT NULL (Battery)
MERGE (v29:ValidationRule {rule_id: "VAL_BATTERY_RECYCLING_001"})
SET v29.rule_name = "BATTERY_RECYCLING_REQUIRED Not Null Check",
    v29.column_name = "BATTERY_RECYCLING_REQUIRED",
    v29.table_id = "DIM_CATEGORY",
    v29.validation_type = "NOT_NULL",
    v29.validation_rule = "NOT_NULL",
    v29.error_message = "Battery Recycling Required flag is required for battery categories",
    v29.business_criticality = "medium",
    v29.priority = "P2",
    v29.applicable_domains = ["battery"],
    v29.is_active = true,
    v29.created_date = datetime(),
    v29.last_updated_date = datetime()
WITH v29
MATCH (c:Column {column_id: "COL_BATTERY_RECYCLING_293"})
MERGE (c)-[:HAS_VALIDATION]->(v29);

// ORGANIC_SUBCATEGORY - NOT NULL (Food & Beverage, Cosmetics)
MERGE (v30:ValidationRule {rule_id: "VAL_ORGANIC_SUBCATEGORY_001"})
SET v30.rule_name = "ORGANIC_SUBCATEGORY Not Null Check",
    v30.column_name = "ORGANIC_SUBCATEGORY",
    v30.table_id = "DIM_CATEGORY",
    v30.validation_type = "NOT_NULL",
    v30.validation_rule = "NOT_NULL",
    v30.error_message = "Organic Subcategory flag is required",
    v30.business_criticality = "medium",
    v30.priority = "P2",
    v30.applicable_domains = ["food_beverage", "cosmetics"],
    v30.is_active = true,
    v30.created_date = datetime(),
    v30.last_updated_date = datetime()
WITH v30
MATCH (c:Column {column_id: "COL_ORGANIC_SUBCATEGORY_294"})
MERGE (c)-[:HAS_VALIDATION]->(v30);

// BEAUTY_PRESTIGE_FLAG - NOT NULL (Cosmetics)
MERGE (v31:ValidationRule {rule_id: "VAL_BEAUTY_PRESTIGE_FLAG_001"})
SET v31.rule_name = "BEAUTY_PRESTIGE_FLAG Not Null Check",
    v31.column_name = "BEAUTY_PRESTIGE_FLAG",
    v31.table_id = "DIM_CATEGORY",
    v31.validation_type = "NOT_NULL",
    v31.validation_rule = "NOT_NULL",
    v31.error_message = "Beauty Prestige Flag is required for cosmetics categories",
    v31.business_criticality = "medium",
    v31.priority = "P2",
    v31.applicable_domains = ["cosmetics"],
    v31.is_active = true,
    v31.created_date = datetime(),
    v31.last_updated_date = datetime()
WITH v31
MATCH (c:Column {column_id: "COL_BEAUTY_PRESTIGE_FLAG_295"})
MERGE (c)-[:HAS_VALIDATION]->(v31);

// ALCOHOL_ABV_RANGE - NOT NULL (Alcoholic Beverages)
MERGE (v32:ValidationRule {rule_id: "VAL_ALCOHOL_ABV_RANGE_001"})
SET v32.rule_name = "ALCOHOL_ABV_RANGE Not Null Check",
    v32.column_name = "ALCOHOL_ABV_RANGE",
    v32.table_id = "DIM_CATEGORY",
    v32.validation_type = "NOT_NULL",
    v32.validation_rule = "NOT_NULL",
    v32.error_message = "Alcohol ABV Range is required for alcoholic beverage categories",
    v32.business_criticality = "high",
    v32.priority = "P1",
    v32.applicable_domains = ["alcoholic_beverages"],
    v32.is_active = true,
    v32.created_date = datetime(),
    v32.last_updated_date = datetime()
WITH v32
MATCH (c:Column {column_id: "COL_ALCOHOL_ABV_RANGE_290"})
MERGE (c)-[:HAS_VALIDATION]->(v32);

// ========================================
// Additional High Priority Validations
// ========================================

// NIELSEN_CATEGORY_CODE - NOT NULL
MERGE (v33:ValidationRule {rule_id: "VAL_NIELSEN_CATEGORY_001"})
SET v33.rule_name = "NIELSEN_CATEGORY_CODE Not Null Check",
    v33.column_name = "NIELSEN_CATEGORY_CODE",
    v33.table_id = "DIM_CATEGORY",
    v33.validation_type = "NOT_NULL",
    v33.validation_rule = "NOT_NULL",
    v33.error_message = "Nielsen Category Code is required",
    v33.business_criticality = "high",
    v33.priority = "P1",
    v33.applicable_domains = ["ALL"],
    v33.is_active = true,
    v33.created_date = datetime(),
    v33.last_updated_date = datetime()
WITH v33
MATCH (c:Column {column_id: "COL_NIELSEN_CATEGORY_261"})
MERGE (c)-[:HAS_VALIDATION]->(v33);

// CATEGORY_LIFECYCLE - ENUM
MERGE (v34:ValidationRule {rule_id: "VAL_CATEGORY_LIFECYCLE_001"})
SET v34.rule_name = "CATEGORY_LIFECYCLE Domain Check",
    v34.column_name = "CATEGORY_LIFECYCLE",
    v34.table_id = "DIM_CATEGORY",
    v34.validation_type = "ENUM",
    v34.validation_rule = "ENUM:Introduction,Growth,Maturity,Decline",
    v34.error_message = "Invalid category lifecycle stage",
    v34.business_criticality = "medium",
    v34.priority = "P2",
    v34.applicable_domains = ["ALL"],
    v34.is_active = true,
    v34.created_date = datetime(),
    v34.last_updated_date = datetime()
WITH v34
MATCH (c:Column {column_id: "COL_CATEGORY_LIFECYCLE_258"})
MERGE (c)-[:HAS_VALIDATION]->(v34);

// TOP_3_BRAND_SHARE - RANGE
MERGE (v35:ValidationRule {rule_id: "VAL_TOP_3_BRAND_SHARE_001"})
SET v35.rule_name = "TOP_3_BRAND_SHARE Range Check",
    v35.column_name = "TOP_3_BRAND_SHARE",
    v35.table_id = "DIM_CATEGORY",
    v35.validation_type = "RANGE",
    v35.validation_rule = "RANGE:0.0,100.0",
    v35.error_message = "Top 3 Brand Share must be between 0 and 100",
    v35.business_criticality = "medium",
    v35.priority = "P3",
    v35.applicable_domains = ["ALL"],
    v35.is_active = true,
    v35.created_date = datetime(),
    v35.last_updated_date = datetime()
WITH v35
MATCH (c:Column {column_id: "COL_TOP_3_BRAND_SHARE_288"})
MERGE (c)-[:HAS_VALIDATION]->(v35);

// ========================================
// Cross-Field Validations
// ========================================

// PARENT_CATEGORY_ID vs CATEGORY_LEVEL
MERGE (v36:ValidationRule {rule_id: "VAL_CATEGORY_HIERARCHY_001"})
SET v36.rule_name = "Category Hierarchy Consistency Check",
    v36.column_name = "PARENT_CATEGORY_ID,CATEGORY_LEVEL",
    v36.table_id = "DIM_CATEGORY",
    v36.validation_type = "CUSTOM",
    v36.validation_rule = "CUSTOM:IF(CATEGORY_LEVEL=1,PARENT_CATEGORY_ID=NULL)",
    v36.error_message = "Top-level categories cannot have parent categories",
    v36.business_criticality = "high",
    v36.priority = "P2",
    v36.applicable_domains = ["ALL"],
    v36.is_active = true,
    v36.created_date = datetime(),
    v36.last_updated_date = datetime()
WITH v36
MATCH (t:Table {table_id: "DIM_CATEGORY"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v36);

// EFFECTIVE_START_DATE vs EFFECTIVE_END_DATE
MERGE (v37:ValidationRule {rule_id: "VAL_DATE_CONSISTENCY_001"})
SET v37.rule_name = "Effective Date Consistency Check",
    v37.column_name = "EFFECTIVE_START_DATE,EFFECTIVE_END_DATE",
    v37.table_id = "DIM_CATEGORY",
    v37.validation_type = "DATE_COMPARISON",
    v37.validation_rule = "DATE_COMPARE:EFFECTIVE_START_DATE<EFFECTIVE_END_DATE",
    v37.error_message = "Effective start date must be before end date",
    v37.business_criticality = "medium",
    v37.priority = "P3",
    v37.applicable_domains = ["ALL"],
    v37.is_active = true,
    v37.created_date = datetime(),
    v37.last_updated_date = datetime()
WITH v37
MATCH (t:Table {table_id: "DIM_CATEGORY"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v37);

// SPACE_TO_SALES_INDEX Logic
MERGE (v38:ValidationRule {rule_id: "VAL_SPACE_SALES_LOGIC_001"})
SET v38.rule_name = "Space to Sales Proportion Check",
    v38.column_name = "SPACE_TO_SALES_INDEX,AVERAGE_SHELF_FOOTAGE",
    v38.table_id = "DIM_CATEGORY",
    v38.validation_type = "CUSTOM",
    v38.validation_rule = "CUSTOM:IF(SPACE_TO_SALES_INDEX>150,AVERAGE_SHELF_FOOTAGE>0)",
    v38.error_message = "Over-spaced categories must have shelf footage data",
    v38.business_criticality = "medium",
    v38.priority = "P3",
    v38.applicable_domains = ["ALL"],
    v38.is_active = true,
    v38.created_date = datetime(),
    v38.last_updated_date = datetime()
WITH v38
MATCH (t:Table {table_id: "DIM_CATEGORY"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v38);

// ========================================
// QUERY TO VERIFY CREATED VALIDATIONS
// ========================================

// Count validations by type
MATCH (v:ValidationRule {table_id: "DIM_CATEGORY"})
RETURN v.validation_type as Type, count(*) as Count
ORDER BY Count DESC;

// Count validations by priority
MATCH (v:ValidationRule {table_id: "DIM_CATEGORY"})
RETURN v.priority as Priority, count(*) as Count
ORDER BY Priority;