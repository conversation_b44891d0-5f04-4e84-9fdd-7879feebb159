Rule_ID,Rule_Name,Column_Name,Table_ID,Business_Criticality,Validation_Type,Validation_Rule,Error_Message,Applicable_Domains,Priority
VAL_PRODUCT_ID_001,PRODUCT_ID Not Null Check,PRODUCT_ID,DIM_PRODUCT_MASTER,Critical,NOT_NULL,NOT_NULL,Product ID is required,ALL,P1
VAL_PRODUCT_ID_002,PRODUCT_ID Uniqueness Check,PRODUCT_ID,DIM_PRODUCT_MASTER,Critical,UNIQUE,UNIQUE,Product ID must be unique,ALL,P1
VAL_PRODUCT_ID_003,PRODUCT_ID Format Check,PRODUCT_ID,DIM_PRODUCT_MASTER,Critical,REGEX,^[A-Za-z0-9]{1\,25}$,Product ID must be alphanumeric\, max 25 chars,ALL,P1
VAL_PRODUCT_NAME_001,PRODUCT_NAME Not Null Check,PRODUCT_NAME,DIM_PRODUCT_MASTER,Critical,NOT_NULL,NOT_NULL,Product Name is required,ALL,P1
VAL_PRODUCT_NAME_002,PRODUCT_NAME Length Check,PRODUCT_NAME,DIM_PRODUCT_MASTER,Critical,LENGTH,LENGTH:1\,500,Product Name must be 1-500 characters,ALL,P1
VAL_UPC_CODE_001,UPC_CODE Not Null Check,UPC_CODE,DIM_PRODUCT_MASTER,Critical,NOT_NULL,NOT_NULL,UPC Code is required,ALL,P1
VAL_UPC_CODE_002,UPC_CODE Format Check,UPC_CODE,DIM_PRODUCT_MASTER,Critical,REGEX,^\d{12}$,UPC must be exactly 12 digits,ALL,P1
VAL_UPC_CODE_003,UPC_CODE Check Digit Validation,UPC_CODE,DIM_PRODUCT_MASTER,Critical,UPC_CHECK_DIGIT,UPC_CHECK_DIGIT,Invalid UPC check digit,ALL,P1
VAL_BRAND_ID_001,BRAND_ID Not Null Check,BRAND_ID,DIM_PRODUCT_MASTER,Critical,NOT_NULL,NOT_NULL,Brand ID is required,ALL,P1
VAL_BRAND_ID_002,BRAND_ID Reference Check,BRAND_ID,DIM_PRODUCT_MASTER,Critical,REFERENCE,FK:DIM_BRAND.BRAND_ID,Brand ID must exist in Brand dimension,ALL,P1
VAL_PRODUCT_STATUS_001,PRODUCT_STATUS Not Null Check,PRODUCT_STATUS,DIM_PRODUCT_MASTER,Critical,NOT_NULL,NOT_NULL,Product Status is required,ALL,P1
VAL_PRODUCT_STATUS_002,PRODUCT_STATUS Domain Check,PRODUCT_STATUS,DIM_PRODUCT_MASTER,Critical,ENUM,ENUM:Active\,Discontinued\,Seasonal\,Limited Edition\,Phase Out,Invalid product status,ALL,P1
VAL_UNIT_COST_001,UNIT_COST Not Null Check,UNIT_COST,DIM_PRODUCT_MASTER,Critical,NOT_NULL,NOT_NULL,Unit Cost is required,ALL,P1
VAL_UNIT_COST_002,UNIT_COST Range Check,UNIT_COST,DIM_PRODUCT_MASTER,Critical,RANGE,RANGE:0.0001\,999999.9999,Unit Cost must be positive,ALL,P1
VAL_FDA_NDC_001,FDA_NDC_NUMBER Not Null Check,FDA_NDC_NUMBER,DIM_PRODUCT_MASTER,Critical,NOT_NULL,NOT_NULL,FDA NDC is required for pharmaceuticals,pharmaceuticals,P1
VAL_FDA_NDC_002,FDA_NDC_NUMBER Format Check,FDA_NDC_NUMBER,DIM_PRODUCT_MASTER,Critical,REGEX,^\d{5}-\d{4}-\d{2}$|^\d{5}-\d{3}-\d{2}$|^\d{11}$,Invalid NDC format,pharmaceuticals,P1
VAL_TTB_COLA_001,TTB_COLA_NUMBER Not Null Check,TTB_COLA_NUMBER,DIM_PRODUCT_MASTER,Critical,NOT_NULL,NOT_NULL,TTB COLA is required for alcoholic beverages,alcoholic_beverages,P1
VAL_TTB_COLA_002,TTB_COLA_NUMBER Format Check,TTB_COLA_NUMBER,DIM_PRODUCT_MASTER,Critical,REGEX,^\d{4}/\d{5}$,Invalid TTB COLA format (YYYY/NNNNN),alcoholic_beverages,P1
VAL_CPSC_CERT_001,CPSC_CERTIFICATE Not Null Check,CPSC_CERTIFICATE,DIM_PRODUCT_MASTER,Critical,NOT_NULL,NOT_NULL,CPSC Certificate is required for toys,toys,P1
VAL_INGREDIENTS_001,INGREDIENTS_LIST Not Null Check,INGREDIENTS_LIST,DIM_PRODUCT_MASTER,Critical,NOT_NULL,NOT_NULL,Ingredients list is required,food_beverage;cosmetics;pharmaceuticals,P1
VAL_ALLERGEN_001,ALLERGEN_STATEMENT Not Null Check,ALLERGEN_STATEMENT,DIM_PRODUCT_MASTER,Critical,NOT_NULL,NOT_NULL,Allergen statement is required,food_beverage;cosmetics,P1
VAL_ABV_001,ALCOHOL_BY_VOLUME Not Null Check,ALCOHOL_BY_VOLUME,DIM_PRODUCT_MASTER,Critical,NOT_NULL,NOT_NULL,ABV is required for alcoholic beverages,alcoholic_beverages,P1
VAL_ABV_002,ALCOHOL_BY_VOLUME Range Check,ALCOHOL_BY_VOLUME,DIM_PRODUCT_MASTER,Critical,RANGE,RANGE:0.0\,100.0,ABV must be between 0 and 100,alcoholic_beverages,P1
VAL_DRUG_SCHEDULE_001,DRUG_SCHEDULE Domain Check,DRUG_SCHEDULE,DIM_PRODUCT_MASTER,Critical,ENUM_NULLABLE,ENUM_NULLABLE:I\,II\,III\,IV\,V,Invalid DEA schedule,pharmaceuticals,P1
VAL_AGE_GRADING_001,AGE_GRADING Not Null Check,AGE_GRADING,DIM_PRODUCT_MASTER,Critical,NOT_NULL,NOT_NULL,Age grading is required for toys,toys,P1
VAL_AGE_GRADING_002,AGE_GRADING Format Check,AGE_GRADING,DIM_PRODUCT_MASTER,Critical,REGEX,^Ages?\s+\d+\+?|^\d+\+?\s+(months?|years?),Invalid age grading format,toys,P1
VAL_DATE_CONSISTENCY_001,Launch vs Discontinue Date Check,LAUNCH_DATE;DISCONTINUE_DATE,DIM_PRODUCT_MASTER,High,DATE_COMPARISON,DATE_COMPARE:LAUNCH_DATE<DISCONTINUE_DATE,Launch date must be before discontinue date,ALL,P2
VAL_PRICE_HIERARCHY_001,Price Hierarchy Check,MANUFACTURER_LIST_PRICE;WHOLESALE_PRICE;UNIT_COST,DIM_PRODUCT_MASTER,High,CUSTOM,CUSTOM:MSRP>WHOLESALE_PRICE>UNIT_COST,Price hierarchy violation,ALL,P2
VAL_STATUS_DATE_001,Status Date Consistency Check,PRODUCT_STATUS;DISCONTINUE_DATE,DIM_PRODUCT_MASTER,Critical,CUSTOM,CUSTOM:IF(PRODUCT_STATUS='Discontinued'\,DISCONTINUE_DATE!=NULL),Discontinued products must have discontinue date,ALL,P1