// ========================================
// DATA QUALITY VALIDATION RULES FOR DIM_PRODUCT_MASTER
// This script creates validation rules and links them to columns
// ========================================

// Create constraint for ValidationRule uniqueness (run once)
CREATE CONSTRAINT validation_rule_unique IF NOT EXISTS
FOR (v:ValidationRule) REQUIRE v.rule_id IS UNIQUE;

// ========================================
// PRODUCT_ID Validations
// ========================================

// PRODUCT_ID - NOT NULL
MERGE (v1:ValidationRule {rule_id: "VAL_PRODUCT_ID_001"})
SET v1.rule_name = "PRODUCT_ID Not Null Check",
    v1.column_name = "PRODUCT_ID",
    v1.table_id = "DIM_PRODUCT_MASTER",
    v1.validation_type = "NOT_NULL",
    v1.validation_rule = "NOT_NULL",
    v1.error_message = "Product ID is required",
    v1.business_criticality = "critical",
    v1.priority = "P1",
    v1.applicable_domains = ["ALL"],
    v1.is_active = true,
    v1.created_date = datetime(),
    v1.last_updated_date = datetime()
WITH v1
MATCH (c:Column {column_id: "COL_PRODUCT_ID_001"})
MERGE (c)-[:HAS_VALIDATION]->(v1);

// PRODUCT_ID - UNIQUE
MERGE (v2:ValidationRule {rule_id: "VAL_PRODUCT_ID_002"})
SET v2.rule_name = "PRODUCT_ID Uniqueness Check",
    v2.column_name = "PRODUCT_ID",
    v2.table_id = "DIM_PRODUCT_MASTER",
    v2.validation_type = "UNIQUE",
    v2.validation_rule = "UNIQUE",
    v2.error_message = "Product ID must be unique",
    v2.business_criticality = "critical",
    v2.priority = "P1",
    v2.applicable_domains = ["ALL"],
    v2.is_active = true,
    v2.created_date = datetime(),
    v2.last_updated_date = datetime()
WITH v2
MATCH (c:Column {column_id: "COL_PRODUCT_ID_001"})
MERGE (c)-[:HAS_VALIDATION]->(v2);

// PRODUCT_ID - FORMAT
MERGE (v3:ValidationRule {rule_id: "VAL_PRODUCT_ID_003"})
SET v3.rule_name = "PRODUCT_ID Format Check",
    v3.column_name = "PRODUCT_ID",
    v3.table_id = "DIM_PRODUCT_MASTER",
    v3.validation_type = "REGEX",
    v3.validation_rule = "^[A-Za-z0-9]{1,25}$",
    v3.error_message = "Product ID must be alphanumeric, max 25 chars",
    v3.business_criticality = "critical",
    v3.priority = "P1",
    v3.applicable_domains = ["ALL"],
    v3.is_active = true,
    v3.created_date = datetime(),
    v3.last_updated_date = datetime()
WITH v3
MATCH (c:Column {column_id: "COL_PRODUCT_ID_001"})
MERGE (c)-[:HAS_VALIDATION]->(v3);

// ========================================
// PRODUCT_NAME Validations
// ========================================

// PRODUCT_NAME - NOT NULL
MERGE (v4:ValidationRule {rule_id: "VAL_PRODUCT_NAME_001"})
SET v4.rule_name = "PRODUCT_NAME Not Null Check",
    v4.column_name = "PRODUCT_NAME",
    v4.table_id = "DIM_PRODUCT_MASTER",
    v4.validation_type = "NOT_NULL",
    v4.validation_rule = "NOT_NULL",
    v4.error_message = "Product Name is required",
    v4.business_criticality = "critical",
    v4.priority = "P1",
    v4.applicable_domains = ["ALL"],
    v4.is_active = true,
    v4.created_date = datetime(),
    v4.last_updated_date = datetime()
WITH v4
MATCH (c:Column {column_id: "COL_PRODUCT_NAME_002"})
MERGE (c)-[:HAS_VALIDATION]->(v4);

// PRODUCT_NAME - LENGTH
MERGE (v5:ValidationRule {rule_id: "VAL_PRODUCT_NAME_002"})
SET v5.rule_name = "PRODUCT_NAME Length Check",
    v5.column_name = "PRODUCT_NAME",
    v5.table_id = "DIM_PRODUCT_MASTER",
    v5.validation_type = "LENGTH",
    v5.validation_rule = "LENGTH:1,500",
    v5.error_message = "Product Name must be 1-500 characters",
    v5.business_criticality = "critical",
    v5.priority = "P1",
    v5.applicable_domains = ["ALL"],
    v5.is_active = true,
    v5.created_date = datetime(),
    v5.last_updated_date = datetime()
WITH v5
MATCH (c:Column {column_id: "COL_PRODUCT_NAME_002"})
MERGE (c)-[:HAS_VALIDATION]->(v5);

// ========================================
// UPC_CODE Validations
// ========================================

// UPC_CODE - NOT NULL
MERGE (v6:ValidationRule {rule_id: "VAL_UPC_CODE_001"})
SET v6.rule_name = "UPC_CODE Not Null Check",
    v6.column_name = "UPC_CODE",
    v6.table_id = "DIM_PRODUCT_MASTER",
    v6.validation_type = "NOT_NULL",
    v6.validation_rule = "NOT_NULL",
    v6.error_message = "UPC Code is required",
    v6.business_criticality = "critical",
    v6.priority = "P1",
    v6.applicable_domains = ["ALL"],
    v6.is_active = true,
    v6.created_date = datetime(),
    v6.last_updated_date = datetime()
WITH v6
MATCH (c:Column {column_id: "COL_UPC_CODE_003"})
MERGE (c)-[:HAS_VALIDATION]->(v6);

// UPC_CODE - FORMAT
MERGE (v7:ValidationRule {rule_id: "VAL_UPC_CODE_002"})
SET v7.rule_name = "UPC_CODE Format Check",
    v7.column_name = "UPC_CODE",
    v7.table_id = "DIM_PRODUCT_MASTER",
    v7.validation_type = "REGEX",
    v7.validation_rule = "^\\d{12}$",
    v7.error_message = "UPC must be exactly 12 digits",
    v7.business_criticality = "critical",
    v7.priority = "P1",
    v7.applicable_domains = ["ALL"],
    v7.is_active = true,
    v7.created_date = datetime(),
    v7.last_updated_date = datetime()
WITH v7
MATCH (c:Column {column_id: "COL_UPC_CODE_003"})
MERGE (c)-[:HAS_VALIDATION]->(v7);

// UPC_CODE - CHECK DIGIT
MERGE (v8:ValidationRule {rule_id: "VAL_UPC_CODE_003"})
SET v8.rule_name = "UPC_CODE Check Digit Validation",
    v8.column_name = "UPC_CODE",
    v8.table_id = "DIM_PRODUCT_MASTER",
    v8.validation_type = "UPC_CHECK_DIGIT",
    v8.validation_rule = "UPC_CHECK_DIGIT",
    v8.error_message = "Invalid UPC check digit",
    v8.business_criticality = "critical",
    v8.priority = "P1",
    v8.applicable_domains = ["ALL"],
    v8.is_active = true,
    v8.created_date = datetime(),
    v8.last_updated_date = datetime()
WITH v8
MATCH (c:Column {column_id: "COL_UPC_CODE_003"})
MERGE (c)-[:HAS_VALIDATION]->(v8);

// ========================================
// BRAND_ID Validations
// ========================================

// BRAND_ID - NOT NULL
MERGE (v9:ValidationRule {rule_id: "VAL_BRAND_ID_001"})
SET v9.rule_name = "BRAND_ID Not Null Check",
    v9.column_name = "BRAND_ID",
    v9.table_id = "DIM_PRODUCT_MASTER",
    v9.validation_type = "NOT_NULL",
    v9.validation_rule = "NOT_NULL",
    v9.error_message = "Brand ID is required",
    v9.business_criticality = "critical",
    v9.priority = "P1",
    v9.applicable_domains = ["ALL"],
    v9.is_active = true,
    v9.created_date = datetime(),
    v9.last_updated_date = datetime()
WITH v9
MATCH (c:Column {column_id: "COL_BRAND_ID_006"})
MERGE (c)-[:HAS_VALIDATION]->(v9);

// BRAND_ID - REFERENCE
MERGE (v10:ValidationRule {rule_id: "VAL_BRAND_ID_002"})
SET v10.rule_name = "BRAND_ID Reference Check",
    v10.column_name = "BRAND_ID",
    v10.table_id = "DIM_PRODUCT_MASTER",
    v10.validation_type = "REFERENCE",
    v10.validation_rule = "FK:DIM_BRAND.BRAND_ID",
    v10.error_message = "Brand ID must exist in Brand dimension",
    v10.business_criticality = "critical",
    v10.priority = "P1",
    v10.applicable_domains = ["ALL"],
    v10.is_active = true,
    v10.created_date = datetime(),
    v10.last_updated_date = datetime()
WITH v10
MATCH (c:Column {column_id: "COL_BRAND_ID_006"})
MERGE (c)-[:HAS_VALIDATION]->(v10);

// ========================================
// PRODUCT_STATUS Validations
// ========================================

// PRODUCT_STATUS - NOT NULL
MERGE (v11:ValidationRule {rule_id: "VAL_PRODUCT_STATUS_001"})
SET v11.rule_name = "PRODUCT_STATUS Not Null Check",
    v11.column_name = "PRODUCT_STATUS",
    v11.table_id = "DIM_PRODUCT_MASTER",
    v11.validation_type = "NOT_NULL",
    v11.validation_rule = "NOT_NULL",
    v11.error_message = "Product Status is required",
    v11.business_criticality = "critical",
    v11.priority = "P1",
    v11.applicable_domains = ["ALL"],
    v11.is_active = true,
    v11.created_date = datetime(),
    v11.last_updated_date = datetime()
WITH v11
MATCH (c:Column {column_id: "COL_PRODUCT_STATUS_019"})
MERGE (c)-[:HAS_VALIDATION]->(v11);

// PRODUCT_STATUS - ENUM
MERGE (v12:ValidationRule {rule_id: "VAL_PRODUCT_STATUS_002"})
SET v12.rule_name = "PRODUCT_STATUS Domain Check",
    v12.column_name = "PRODUCT_STATUS",
    v12.table_id = "DIM_PRODUCT_MASTER",
    v12.validation_type = "ENUM",
    v12.validation_rule = "ENUM:Active,Discontinued,Seasonal,Limited Edition,Phase Out",
    v12.error_message = "Invalid product status",
    v12.business_criticality = "critical",
    v12.priority = "P1",
    v12.applicable_domains = ["ALL"],
    v12.is_active = true,
    v12.created_date = datetime(),
    v12.last_updated_date = datetime()
WITH v12
MATCH (c:Column {column_id: "COL_PRODUCT_STATUS_019"})
MERGE (c)-[:HAS_VALIDATION]->(v12);

// ========================================
// UNIT_COST Validations
// ========================================

// UNIT_COST - NOT NULL
MERGE (v13:ValidationRule {rule_id: "VAL_UNIT_COST_001"})
SET v13.rule_name = "UNIT_COST Not Null Check",
    v13.column_name = "UNIT_COST",
    v13.table_id = "DIM_PRODUCT_MASTER",
    v13.validation_type = "NOT_NULL",
    v13.validation_rule = "NOT_NULL",
    v13.error_message = "Unit Cost is required",
    v13.business_criticality = "critical",
    v13.priority = "P1",
    v13.applicable_domains = ["ALL"],
    v13.is_active = true,
    v13.created_date = datetime(),
    v13.last_updated_date = datetime()
WITH v13
MATCH (c:Column {column_id: "COL_UNIT_COST_024"})
MERGE (c)-[:HAS_VALIDATION]->(v13);

// UNIT_COST - RANGE
MERGE (v14:ValidationRule {rule_id: "VAL_UNIT_COST_002"})
SET v14.rule_name = "UNIT_COST Range Check",
    v14.column_name = "UNIT_COST",
    v14.table_id = "DIM_PRODUCT_MASTER",
    v14.validation_type = "RANGE",
    v14.validation_rule = "RANGE:0.0001,999999.9999",
    v14.error_message = "Unit Cost must be positive",
    v14.business_criticality = "critical",
    v14.priority = "P1",
    v14.applicable_domains = ["ALL"],
    v14.is_active = true,
    v14.created_date = datetime(),
    v14.last_updated_date = datetime()
WITH v14
MATCH (c:Column {column_id: "COL_UNIT_COST_024"})
MERGE (c)-[:HAS_VALIDATION]->(v14);

// ========================================
// Domain-Specific Validations
// ========================================

// FDA_NDC_NUMBER - NOT NULL (Pharmaceuticals)
MERGE (v15:ValidationRule {rule_id: "VAL_FDA_NDC_001"})
SET v15.rule_name = "FDA_NDC_NUMBER Not Null Check",
    v15.column_name = "FDA_NDC_NUMBER",
    v15.table_id = "DIM_PRODUCT_MASTER",
    v15.validation_type = "NOT_NULL",
    v15.validation_rule = "NOT_NULL",
    v15.error_message = "FDA NDC is required for pharmaceuticals",
    v15.business_criticality = "critical",
    v15.priority = "P1",
    v15.applicable_domains = ["pharmaceuticals"],
    v15.is_active = true,
    v15.created_date = datetime(),
    v15.last_updated_date = datetime()
WITH v15
MATCH (c:Column {column_id: "COL_FDA_NDC_NUMBER_015"})
MERGE (c)-[:HAS_VALIDATION]->(v15);

// FDA_NDC_NUMBER - FORMAT
MERGE (v16:ValidationRule {rule_id: "VAL_FDA_NDC_002"})
SET v16.rule_name = "FDA_NDC_NUMBER Format Check",
    v16.column_name = "FDA_NDC_NUMBER",
    v16.table_id = "DIM_PRODUCT_MASTER",
    v16.validation_type = "REGEX",
    v16.validation_rule = "^\\d{5}-\\d{4}-\\d{2}$|^\\d{5}-\\d{3}-\\d{2}$|^\\d{11}$",
    v16.error_message = "Invalid NDC format",
    v16.business_criticality = "critical",
    v16.priority = "P1",
    v16.applicable_domains = ["pharmaceuticals"],
    v16.is_active = true,
    v16.created_date = datetime(),
    v16.last_updated_date = datetime()
WITH v16
MATCH (c:Column {column_id: "COL_FDA_NDC_NUMBER_015"})
MERGE (c)-[:HAS_VALIDATION]->(v16);

// TTB_COLA_NUMBER - NOT NULL (Alcoholic Beverages)
MERGE (v17:ValidationRule {rule_id: "VAL_TTB_COLA_001"})
SET v17.rule_name = "TTB_COLA_NUMBER Not Null Check",
    v17.column_name = "TTB_COLA_NUMBER",
    v17.table_id = "DIM_PRODUCT_MASTER",
    v17.validation_type = "NOT_NULL",
    v17.validation_rule = "NOT_NULL",
    v17.error_message = "TTB COLA is required for alcoholic beverages",
    v17.business_criticality = "critical",
    v17.priority = "P1",
    v17.applicable_domains = ["alcoholic_beverages"],
    v17.is_active = true,
    v17.created_date = datetime(),
    v17.last_updated_date = datetime()
WITH v17
MATCH (c:Column {column_id: "COL_TTB_COLA_NUMBER_016"})
MERGE (c)-[:HAS_VALIDATION]->(v17);

// TTB_COLA_NUMBER - FORMAT
MERGE (v18:ValidationRule {rule_id: "VAL_TTB_COLA_002"})
SET v18.rule_name = "TTB_COLA_NUMBER Format Check",
    v18.column_name = "TTB_COLA_NUMBER",
    v18.table_id = "DIM_PRODUCT_MASTER",
    v18.validation_type = "REGEX",
    v18.validation_rule = "^\\d{4}/\\d{5}$",
    v18.error_message = "Invalid TTB COLA format (YYYY/NNNNN)",
    v18.business_criticality = "critical",
    v18.priority = "P1",
    v18.applicable_domains = ["alcoholic_beverages"],
    v18.is_active = true,
    v18.created_date = datetime(),
    v18.last_updated_date = datetime()
WITH v18
MATCH (c:Column {column_id: "COL_TTB_COLA_NUMBER_016"})
MERGE (c)-[:HAS_VALIDATION]->(v18);

// CPSC_CERTIFICATE - NOT NULL (Toys)
MERGE (v19:ValidationRule {rule_id: "VAL_CPSC_CERT_001"})
SET v19.rule_name = "CPSC_CERTIFICATE Not Null Check",
    v19.column_name = "CPSC_CERTIFICATE",
    v19.table_id = "DIM_PRODUCT_MASTER",
    v19.validation_type = "NOT_NULL",
    v19.validation_rule = "NOT_NULL",
    v19.error_message = "CPSC Certificate is required for toys",
    v19.business_criticality = "critical",
    v19.priority = "P1",
    v19.applicable_domains = ["toys"],
    v19.is_active = true,
    v19.created_date = datetime(),
    v19.last_updated_date = datetime()
WITH v19
MATCH (c:Column {column_id: "COL_CPSC_CERTIFICATE_017"})
MERGE (c)-[:HAS_VALIDATION]->(v19);

// INGREDIENTS_LIST - NOT NULL (Food/Cosmetics/Pharma)
MERGE (v20:ValidationRule {rule_id: "VAL_INGREDIENTS_001"})
SET v20.rule_name = "INGREDIENTS_LIST Not Null Check",
    v20.column_name = "INGREDIENTS_LIST",
    v20.table_id = "DIM_PRODUCT_MASTER",
    v20.validation_type = "NOT_NULL",
    v20.validation_rule = "NOT_NULL",
    v20.error_message = "Ingredients list is required",
    v20.business_criticality = "critical",
    v20.priority = "P1",
    v20.applicable_domains = ["food_beverage", "cosmetics", "pharmaceuticals"],
    v20.is_active = true,
    v20.created_date = datetime(),
    v20.last_updated_date = datetime()
WITH v20
MATCH (c:Column {column_id: "COL_INGREDIENTS_LIST_034"})
MERGE (c)-[:HAS_VALIDATION]->(v20);

// ALLERGEN_STATEMENT - NOT NULL (Food/Cosmetics)
MERGE (v21:ValidationRule {rule_id: "VAL_ALLERGEN_001"})
SET v21.rule_name = "ALLERGEN_STATEMENT Not Null Check",
    v21.column_name = "ALLERGEN_STATEMENT",
    v21.table_id = "DIM_PRODUCT_MASTER",
    v21.validation_type = "NOT_NULL",
    v21.validation_rule = "NOT_NULL",
    v21.error_message = "Allergen statement is required",
    v21.business_criticality = "critical",
    v21.priority = "P1",
    v21.applicable_domains = ["food_beverage", "cosmetics"],
    v21.is_active = true,
    v21.created_date = datetime(),
    v21.last_updated_date = datetime()
WITH v21
MATCH (c:Column {column_id: "COL_ALLERGEN_STATEMENT_035"})
MERGE (c)-[:HAS_VALIDATION]->(v21);

// ALCOHOL_BY_VOLUME - NOT NULL (Alcoholic Beverages)
MERGE (v22:ValidationRule {rule_id: "VAL_ABV_001"})
SET v22.rule_name = "ALCOHOL_BY_VOLUME Not Null Check",
    v22.column_name = "ALCOHOL_BY_VOLUME",
    v22.table_id = "DIM_PRODUCT_MASTER",
    v22.validation_type = "NOT_NULL",
    v22.validation_rule = "NOT_NULL",
    v22.error_message = "ABV is required for alcoholic beverages",
    v22.business_criticality = "critical",
    v22.priority = "P1",
    v22.applicable_domains = ["alcoholic_beverages"],
    v22.is_active = true,
    v22.created_date = datetime(),
    v22.last_updated_date = datetime()
WITH v22
MATCH (c:Column {column_id: "COL_ALCOHOL_BY_VOLUME_054"})
MERGE (c)-[:HAS_VALIDATION]->(v22);

// ALCOHOL_BY_VOLUME - RANGE
MERGE (v23:ValidationRule {rule_id: "VAL_ABV_002"})
SET v23.rule_name = "ALCOHOL_BY_VOLUME Range Check",
    v23.column_name = "ALCOHOL_BY_VOLUME",
    v23.table_id = "DIM_PRODUCT_MASTER",
    v23.validation_type = "RANGE",
    v23.validation_rule = "RANGE:0.0,100.0",
    v23.error_message = "ABV must be between 0 and 100",
    v23.business_criticality = "critical",
    v23.priority = "P1",
    v23.applicable_domains = ["alcoholic_beverages"],
    v23.is_active = true,
    v23.created_date = datetime(),
    v23.last_updated_date = datetime()
WITH v23
MATCH (c:Column {column_id: "COL_ALCOHOL_BY_VOLUME_054"})
MERGE (c)-[:HAS_VALIDATION]->(v23);

// DRUG_SCHEDULE - ENUM NULLABLE (Pharmaceuticals)
MERGE (v24:ValidationRule {rule_id: "VAL_DRUG_SCHEDULE_001"})
SET v24.rule_name = "DRUG_SCHEDULE Domain Check",
    v24.column_name = "DRUG_SCHEDULE",
    v24.table_id = "DIM_PRODUCT_MASTER",
    v24.validation_type = "ENUM_NULLABLE",
    v24.validation_rule = "ENUM_NULLABLE:I,II,III,IV,V",
    v24.error_message = "Invalid DEA schedule",
    v24.business_criticality = "critical",
    v24.priority = "P1",
    v24.applicable_domains = ["pharmaceuticals"],
    v24.is_active = true,
    v24.created_date = datetime(),
    v24.last_updated_date = datetime()
WITH v24
MATCH (c:Column {column_id: "COL_DRUG_SCHEDULE_055"})
MERGE (c)-[:HAS_VALIDATION]->(v24);

// AGE_GRADING - NOT NULL (Toys)
MERGE (v25:ValidationRule {rule_id: "VAL_AGE_GRADING_001"})
SET v25.rule_name = "AGE_GRADING Not Null Check",
    v25.column_name = "AGE_GRADING",
    v25.table_id = "DIM_PRODUCT_MASTER",
    v25.validation_type = "NOT_NULL",
    v25.validation_rule = "NOT_NULL",
    v25.error_message = "Age grading is required for toys",
    v25.business_criticality = "critical",
    v25.priority = "P1",
    v25.applicable_domains = ["toys"],
    v25.is_active = true,
    v25.created_date = datetime(),
    v25.last_updated_date = datetime()
WITH v25
MATCH (c:Column {column_id: "COL_AGE_GRADING_056"})
MERGE (c)-[:HAS_VALIDATION]->(v25);

// AGE_GRADING - FORMAT
MERGE (v26:ValidationRule {rule_id: "VAL_AGE_GRADING_002"})
SET v26.rule_name = "AGE_GRADING Format Check",
    v26.column_name = "AGE_GRADING",
    v26.table_id = "DIM_PRODUCT_MASTER",
    v26.validation_type = "REGEX",
    v26.validation_rule = "^Ages?\\s+\\d+\\+?|^\\d+\\+?\\s+(months?|years?)",
    v26.error_message = "Invalid age grading format",
    v26.business_criticality = "critical",
    v26.priority = "P1",
    v26.applicable_domains = ["toys"],
    v26.is_active = true,
    v26.created_date = datetime(),
    v26.last_updated_date = datetime()
WITH v26
MATCH (c:Column {column_id: "COL_AGE_GRADING_056"})
MERGE (c)-[:HAS_VALIDATION]->(v26);

// ========================================
// Cross-Field Validations
// ========================================

// LAUNCH_DATE vs DISCONTINUE_DATE
MERGE (v27:ValidationRule {rule_id: "VAL_DATE_CONSISTENCY_001"})
SET v27.rule_name = "Launch vs Discontinue Date Check",
    v27.column_name = "LAUNCH_DATE,DISCONTINUE_DATE",
    v27.table_id = "DIM_PRODUCT_MASTER",
    v27.validation_type = "DATE_COMPARISON",
    v27.validation_rule = "DATE_COMPARE:LAUNCH_DATE<DISCONTINUE_DATE",
    v27.error_message = "Launch date must be before discontinue date",
    v27.business_criticality = "high",
    v27.priority = "P2",
    v27.applicable_domains = ["ALL"],
    v27.is_active = true,
    v27.created_date = datetime(),
    v27.last_updated_date = datetime()
WITH v27
MATCH (t:Table {table_id: "DIM_PRODUCT_MASTER"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v27);

// PRICE HIERARCHY
MERGE (v28:ValidationRule {rule_id: "VAL_PRICE_HIERARCHY_001"})
SET v28.rule_name = "Price Hierarchy Check",
    v28.column_name = "MANUFACTURER_LIST_PRICE,WHOLESALE_PRICE,UNIT_COST",
    v28.table_id = "DIM_PRODUCT_MASTER",
    v28.validation_type = "CUSTOM",
    v28.validation_rule = "CUSTOM:MSRP>WHOLESALE_PRICE>UNIT_COST",
    v28.error_message = "Price hierarchy violation",
    v28.business_criticality = "high",
    v28.priority = "P2",
    v28.applicable_domains = ["ALL"],
    v28.is_active = true,
    v28.created_date = datetime(),
    v28.last_updated_date = datetime()
WITH v28
MATCH (t:Table {table_id: "DIM_PRODUCT_MASTER"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v28);

// STATUS DATE CONSISTENCY
MERGE (v29:ValidationRule {rule_id: "VAL_STATUS_DATE_001"})
SET v29.rule_name = "Status Date Consistency Check",
    v29.column_name = "PRODUCT_STATUS,DISCONTINUE_DATE",
    v29.table_id = "DIM_PRODUCT_MASTER",
    v29.validation_type = "CUSTOM",
    v29.validation_rule = "CUSTOM:IF(PRODUCT_STATUS='Discontinued',DISCONTINUE_DATE!=NULL)",
    v29.error_message = "Discontinued products must have discontinue date",
    v29.business_criticality = "critical",
    v29.priority = "P1",
    v29.applicable_domains = ["ALL"],
    v29.is_active = true,
    v29.created_date = datetime(),
    v29.last_updated_date = datetime()
WITH v29
MATCH (t:Table {table_id: "DIM_PRODUCT_MASTER"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v29);

// ========================================
// QUERY TO VERIFY CREATED VALIDATIONS
// ========================================

// Count validations by type
MATCH (v:ValidationRule {table_id: "DIM_PRODUCT_MASTER"})
RETURN v.validation_type as Type, count(*) as Count
ORDER BY Count DESC;

// Count validations by priority
MATCH (v:ValidationRule {table_id: "DIM_PRODUCT_MASTER"})
RETURN v.priority as Priority, count(*) as Count
ORDER BY Priority;