// ========================================
// DATA QUALITY VALIDATION RULES FOR DIM_BRAND_MASTER
// This script creates validation rules and links them to columns
// ========================================

// Create constraint for ValidationRule uniqueness (run once if not exists)
CREATE CONSTRAINT validation_rule_unique IF NOT EXISTS
FOR (v:ValidationRule) REQUIRE v.rule_id IS UNIQUE;

// ========================================
// BRAND_ID Validations
// ========================================

// BRAND_ID - NOT NULL
MERGE (v1:ValidationRule {rule_id: "VAL_BRAND_ID_001"})
SET v1.rule_name = "BRAND_ID Not Null Check",
    v1.column_name = "BRAND_ID",
    v1.table_id = "DIM_BRAND_MASTER",
    v1.validation_type = "NOT_NULL",
    v1.validation_rule = "NOT_NULL",
    v1.error_message = "Brand ID is required",
    v1.business_criticality = "critical",
    v1.priority = "P1",
    v1.applicable_domains = ["ALL"],
    v1.is_active = true,
    v1.created_date = datetime(),
    v1.last_updated_date = datetime()
WITH v1
MATCH (c:Column {column_id: "COL_BRAND_ID_DIM_050"})
MERGE (c)-[:HAS_VALIDATION]->(v1);

// BRAND_ID - UNIQUE
MERGE (v2:ValidationRule {rule_id: "VAL_BRAND_ID_002"})
SET v2.rule_name = "BRAND_ID Uniqueness Check",
    v2.column_name = "BRAND_ID",
    v2.table_id = "DIM_BRAND_MASTER",
    v2.validation_type = "UNIQUE",
    v2.validation_rule = "UNIQUE",
    v2.error_message = "Brand ID must be unique",
    v2.business_criticality = "critical",
    v2.priority = "P1",
    v2.applicable_domains = ["ALL"],
    v2.is_active = true,
    v2.created_date = datetime(),
    v2.last_updated_date = datetime()
WITH v2
MATCH (c:Column {column_id: "COL_BRAND_ID_DIM_050"})
MERGE (c)-[:HAS_VALIDATION]->(v2);

// BRAND_ID - FORMAT
MERGE (v3:ValidationRule {rule_id: "VAL_BRAND_ID_003"})
SET v3.rule_name = "BRAND_ID Format Check",
    v3.column_name = "BRAND_ID",
    v3.table_id = "DIM_BRAND_MASTER",
    v3.validation_type = "REGEX",
    v3.validation_rule = "^[A-Za-z0-9]{1,20}$",
    v3.error_message = "Brand ID must be alphanumeric, max 20 chars",
    v3.business_criticality = "critical",
    v3.priority = "P1",
    v3.applicable_domains = ["ALL"],
    v3.is_active = true,
    v3.created_date = datetime(),
    v3.last_updated_date = datetime()
WITH v3
MATCH (c:Column {column_id: "COL_BRAND_ID_DIM_050"})
MERGE (c)-[:HAS_VALIDATION]->(v3);

// ========================================
// BRAND_NAME Validations
// ========================================

// BRAND_NAME - NOT NULL
MERGE (v4:ValidationRule {rule_id: "VAL_BRAND_NAME_001"})
SET v4.rule_name = "BRAND_NAME Not Null Check",
    v4.column_name = "BRAND_NAME",
    v4.table_id = "DIM_BRAND_MASTER",
    v4.validation_type = "NOT_NULL",
    v4.validation_rule = "NOT_NULL",
    v4.error_message = "Brand Name is required",
    v4.business_criticality = "critical",
    v4.priority = "P1",
    v4.applicable_domains = ["ALL"],
    v4.is_active = true,
    v4.created_date = datetime(),
    v4.last_updated_date = datetime()
WITH v4
MATCH (c:Column {column_id: "COL_BRAND_NAME_051"})
MERGE (c)-[:HAS_VALIDATION]->(v4);

// BRAND_NAME - LENGTH
MERGE (v5:ValidationRule {rule_id: "VAL_BRAND_NAME_002"})
SET v5.rule_name = "BRAND_NAME Length Check",
    v5.column_name = "BRAND_NAME",
    v5.table_id = "DIM_BRAND_MASTER",
    v5.validation_type = "LENGTH",
    v5.validation_rule = "LENGTH:1,200",
    v5.error_message = "Brand Name must be 1-200 characters",
    v5.business_criticality = "critical",
    v5.priority = "P1",
    v5.applicable_domains = ["ALL"],
    v5.is_active = true,
    v5.created_date = datetime(),
    v5.last_updated_date = datetime()
WITH v5
MATCH (c:Column {column_id: "COL_BRAND_NAME_051"})
MERGE (c)-[:HAS_VALIDATION]->(v5);

// ========================================
// PARENT_BRAND_ID Validations
// ========================================

// PARENT_BRAND_ID - REFERENCE
MERGE (v6:ValidationRule {rule_id: "VAL_PARENT_BRAND_ID_001"})
SET v6.rule_name = "PARENT_BRAND_ID Reference Check",
    v6.column_name = "PARENT_BRAND_ID",
    v6.table_id = "DIM_BRAND_MASTER",
    v6.validation_type = "REFERENCE",
    v6.validation_rule = "FK:DIM_BRAND_MASTER.BRAND_ID",
    v6.error_message = "Parent Brand ID must exist in Brand Master",
    v6.business_criticality = "high",
    v6.priority = "P1",
    v6.applicable_domains = ["ALL"],
    v6.is_active = true,
    v6.created_date = datetime(),
    v6.last_updated_date = datetime()
WITH v6
MATCH (c:Column {column_id: "COL_PARENT_BRAND_ID_052"})
MERGE (c)-[:HAS_VALIDATION]->(v6);

// ========================================
// BRAND_LEVEL Validations
// ========================================

// BRAND_LEVEL - NOT NULL
MERGE (v7:ValidationRule {rule_id: "VAL_BRAND_LEVEL_001"})
SET v7.rule_name = "BRAND_LEVEL Not Null Check",
    v7.column_name = "BRAND_LEVEL",
    v7.table_id = "DIM_BRAND_MASTER",
    v7.validation_type = "NOT_NULL",
    v7.validation_rule = "NOT_NULL",
    v7.error_message = "Brand Level is required",
    v7.business_criticality = "high",
    v7.priority = "P1",
    v7.applicable_domains = ["ALL"],
    v7.is_active = true,
    v7.created_date = datetime(),
    v7.last_updated_date = datetime()
WITH v7
MATCH (c:Column {column_id: "COL_BRAND_LEVEL_053"})
MERGE (c)-[:HAS_VALIDATION]->(v7);

// BRAND_LEVEL - ENUM
MERGE (v8:ValidationRule {rule_id: "VAL_BRAND_LEVEL_002"})
SET v8.rule_name = "BRAND_LEVEL Domain Check",
    v8.column_name = "BRAND_LEVEL",
    v8.table_id = "DIM_BRAND_MASTER",
    v8.validation_type = "ENUM",
    v8.validation_rule = "ENUM:Corporate,Master,Sub-brand,Variant",
    v8.error_message = "Invalid brand level",
    v8.business_criticality = "high",
    v8.priority = "P1",
    v8.applicable_domains = ["ALL"],
    v8.is_active = true,
    v8.created_date = datetime(),
    v8.last_updated_date = datetime()
WITH v8
MATCH (c:Column {column_id: "COL_BRAND_LEVEL_053"})
MERGE (c)-[:HAS_VALIDATION]->(v8);

// ========================================
// GLOBAL_BRAND_FLAG Validations
// ========================================

// GLOBAL_BRAND_FLAG - NOT NULL
MERGE (v9:ValidationRule {rule_id: "VAL_GLOBAL_BRAND_FLAG_001"})
SET v9.rule_name = "GLOBAL_BRAND_FLAG Not Null Check",
    v9.column_name = "GLOBAL_BRAND_FLAG",
    v9.table_id = "DIM_BRAND_MASTER",
    v9.validation_type = "NOT_NULL",
    v9.validation_rule = "NOT_NULL",
    v9.error_message = "Global Brand Flag is required",
    v9.business_criticality = "high",
    v9.priority = "P1",
    v9.applicable_domains = ["ALL"],
    v9.is_active = true,
    v9.created_date = datetime(),
    v9.last_updated_date = datetime()
WITH v9
MATCH (c:Column {column_id: "COL_GLOBAL_BRAND_FLAG_055"})
MERGE (c)-[:HAS_VALIDATION]->(v9);

// ========================================
// BRAND_STATUS Validations
// ========================================

// BRAND_STATUS - NOT NULL
MERGE (v10:ValidationRule {rule_id: "VAL_BRAND_STATUS_001"})
SET v10.rule_name = "BRAND_STATUS Not Null Check",
    v10.column_name = "BRAND_STATUS",
    v10.table_id = "DIM_BRAND_MASTER",
    v10.validation_type = "NOT_NULL",
    v10.validation_rule = "NOT_NULL",
    v10.error_message = "Brand Status is required",
    v10.business_criticality = "critical",
    v10.priority = "P1",
    v10.applicable_domains = ["ALL"],
    v10.is_active = true,
    v10.created_date = datetime(),
    v10.last_updated_date = datetime()
WITH v10
MATCH (c:Column {column_id: "COL_BRAND_STATUS_096"})
MERGE (c)-[:HAS_VALIDATION]->(v10);

// BRAND_STATUS - ENUM
MERGE (v11:ValidationRule {rule_id: "VAL_BRAND_STATUS_002"})
SET v11.rule_name = "BRAND_STATUS Domain Check",
    v11.column_name = "BRAND_STATUS",
    v11.table_id = "DIM_BRAND_MASTER",
    v11.validation_type = "ENUM",
    v11.validation_rule = "ENUM:Active,Dormant,Sunset,Divested",
    v11.error_message = "Invalid brand status",
    v11.business_criticality = "critical",
    v11.priority = "P1",
    v11.applicable_domains = ["ALL"],
    v11.is_active = true,
    v11.created_date = datetime(),
    v11.last_updated_date = datetime()
WITH v11
MATCH (c:Column {column_id: "COL_BRAND_STATUS_096"})
MERGE (c)-[:HAS_VALIDATION]->(v11);

// ========================================
// Financial Metric Validations
// ========================================

// BRAND_REVENUE_LTM - NOT NULL
MERGE (v12:ValidationRule {rule_id: "VAL_BRAND_REVENUE_LTM_001"})
SET v12.rule_name = "BRAND_REVENUE_LTM Not Null Check",
    v12.column_name = "BRAND_REVENUE_LTM",
    v12.table_id = "DIM_BRAND_MASTER",
    v12.validation_type = "NOT_NULL",
    v12.validation_rule = "NOT_NULL",
    v12.error_message = "Brand Revenue LTM is required",
    v12.business_criticality = "critical",
    v12.priority = "P1",
    v12.applicable_domains = ["ALL"],
    v12.is_active = true,
    v12.created_date = datetime(),
    v12.last_updated_date = datetime()
WITH v12
MATCH (c:Column {column_id: "COL_BRAND_REVENUE_LTM_066"})
MERGE (c)-[:HAS_VALIDATION]->(v12);

// BRAND_REVENUE_LTM - RANGE
MERGE (v13:ValidationRule {rule_id: "VAL_BRAND_REVENUE_LTM_002"})
SET v13.rule_name = "BRAND_REVENUE_LTM Range Check",
    v13.column_name = "BRAND_REVENUE_LTM",
    v13.table_id = "DIM_BRAND_MASTER",
    v13.validation_type = "RANGE",
    v13.validation_rule = "RANGE:0,999999999999.99",
    v13.error_message = "Brand Revenue must be non-negative",
    v13.business_criticality = "critical",
    v13.priority = "P1",
    v13.applicable_domains = ["ALL"],
    v13.is_active = true,
    v13.created_date = datetime(),
    v13.last_updated_date = datetime()
WITH v13
MATCH (c:Column {column_id: "COL_BRAND_REVENUE_LTM_066"})
MERGE (c)-[:HAS_VALIDATION]->(v13);

// MARKET_SHARE_PERCENT - NOT NULL
MERGE (v14:ValidationRule {rule_id: "VAL_MARKET_SHARE_PCT_001"})
SET v14.rule_name = "MARKET_SHARE_PERCENT Not Null Check",
    v14.column_name = "MARKET_SHARE_PERCENT",
    v14.table_id = "DIM_BRAND_MASTER",
    v14.validation_type = "NOT_NULL",
    v14.validation_rule = "NOT_NULL",
    v14.error_message = "Market Share Percentage is required",
    v14.business_criticality = "critical",
    v14.priority = "P1",
    v14.applicable_domains = ["ALL"],
    v14.is_active = true,
    v14.created_date = datetime(),
    v14.last_updated_date = datetime()
WITH v14
MATCH (c:Column {column_id: "COL_MARKET_SHARE_PERCENT_067"})
MERGE (c)-[:HAS_VALIDATION]->(v14);

// MARKET_SHARE_PERCENT - RANGE
MERGE (v15:ValidationRule {rule_id: "VAL_MARKET_SHARE_PCT_002"})
SET v15.rule_name = "MARKET_SHARE_PERCENT Range Check",
    v15.column_name = "MARKET_SHARE_PERCENT",
    v15.table_id = "DIM_BRAND_MASTER",
    v15.validation_type = "RANGE",
    v15.validation_rule = "RANGE:0.0,100.0",
    v15.error_message = "Market Share must be between 0 and 100",
    v15.business_criticality = "critical",
    v15.priority = "P1",
    v15.applicable_domains = ["ALL"],
    v15.is_active = true,
    v15.created_date = datetime(),
    v15.last_updated_date = datetime()
WITH v15
MATCH (c:Column {column_id: "COL_MARKET_SHARE_PERCENT_067"})
MERGE (c)-[:HAS_VALIDATION]->(v15);

// ========================================
// Brand Equity Metric Validations
// ========================================

// BRAND_EQUITY_SCORE - RANGE
MERGE (v16:ValidationRule {rule_id: "VAL_BRAND_EQUITY_SCORE_001"})
SET v16.rule_name = "BRAND_EQUITY_SCORE Range Check",
    v16.column_name = "BRAND_EQUITY_SCORE",
    v16.table_id = "DIM_BRAND_MASTER",
    v16.validation_type = "RANGE",
    v16.validation_rule = "RANGE:0.0,100.0",
    v16.error_message = "Brand Equity Score must be between 0 and 100",
    v16.business_criticality = "high",
    v16.priority = "P2",
    v16.applicable_domains = ["ALL"],
    v16.is_active = true,
    v16.created_date = datetime(),
    v16.last_updated_date = datetime()
WITH v16
MATCH (c:Column {column_id: "COL_BRAND_EQUITY_SCORE_061"})
MERGE (c)-[:HAS_VALIDATION]->(v16);

// BRAND_AWARENESS_PERCENT - RANGE
MERGE (v17:ValidationRule {rule_id: "VAL_BRAND_AWARENESS_PCT_001"})
SET v17.rule_name = "BRAND_AWARENESS_PERCENT Range Check",
    v17.column_name = "BRAND_AWARENESS_PERCENT",
    v17.table_id = "DIM_BRAND_MASTER",
    v17.validation_type = "RANGE",
    v17.validation_rule = "RANGE:0.0,100.0",
    v17.error_message = "Brand Awareness must be between 0 and 100",
    v17.business_criticality = "high",
    v17.priority = "P2",
    v17.applicable_domains = ["ALL"],
    v17.is_active = true,
    v17.created_date = datetime(),
    v17.last_updated_date = datetime()
WITH v17
MATCH (c:Column {column_id: "COL_BRAND_AWARENESS_062"})
MERGE (c)-[:HAS_VALIDATION]->(v17);

// NET_PROMOTER_SCORE - RANGE
MERGE (v18:ValidationRule {rule_id: "VAL_NET_PROMOTER_SCORE_001"})
SET v18.rule_name = "NET_PROMOTER_SCORE Range Check",
    v18.column_name = "NET_PROMOTER_SCORE",
    v18.table_id = "DIM_BRAND_MASTER",
    v18.validation_type = "RANGE",
    v18.validation_rule = "RANGE:-100,100",
    v18.error_message = "Net Promoter Score must be between -100 and 100",
    v18.business_criticality = "high",
    v18.priority = "P2",
    v18.applicable_domains = ["ALL"],
    v18.is_active = true,
    v18.created_date = datetime(),
    v18.last_updated_date = datetime()
WITH v18
MATCH (c:Column {column_id: "COL_NET_PROMOTER_SCORE_065"})
MERGE (c)-[:HAS_VALIDATION]->(v18);

// ========================================
// Domain-Specific Validations
// ========================================

// ALCOHOL_CATEGORY - NOT NULL (Alcoholic Beverages)
MERGE (v19:ValidationRule {rule_id: "VAL_ALCOHOL_CATEGORY_001"})
SET v19.rule_name = "ALCOHOL_CATEGORY Not Null Check",
    v19.column_name = "ALCOHOL_CATEGORY",
    v19.table_id = "DIM_BRAND_MASTER",
    v19.validation_type = "NOT_NULL",
    v19.validation_rule = "NOT_NULL",
    v19.error_message = "Alcohol Category is required for alcoholic beverages",
    v19.business_criticality = "critical",
    v19.priority = "P1",
    v19.applicable_domains = ["alcoholic_beverages"],
    v19.is_active = true,
    v19.created_date = datetime(),
    v19.last_updated_date = datetime()
WITH v19
MATCH (c:Column {column_id: "COL_ALCOHOL_CATEGORY_090"})
MERGE (c)-[:HAS_VALIDATION]->(v19);

// ALCOHOL_CATEGORY - ENUM
MERGE (v20:ValidationRule {rule_id: "VAL_ALCOHOL_CATEGORY_002"})
SET v20.rule_name = "ALCOHOL_CATEGORY Domain Check",
    v20.column_name = "ALCOHOL_CATEGORY",
    v20.table_id = "DIM_BRAND_MASTER",
    v20.validation_type = "ENUM",
    v20.validation_rule = "ENUM:Beer,Wine,Spirits,RTD/Seltzers",
    v20.error_message = "Invalid alcohol category",
    v20.business_criticality = "critical",
    v20.priority = "P1",
    v20.applicable_domains = ["alcoholic_beverages"],
    v20.is_active = true,
    v20.created_date = datetime(),
    v20.last_updated_date = datetime()
WITH v20
MATCH (c:Column {column_id: "COL_ALCOHOL_CATEGORY_090"})
MERGE (c)-[:HAS_VALIDATION]->(v20);

// THERAPEUTIC_AREA - NOT NULL (Pharmaceuticals)
MERGE (v21:ValidationRule {rule_id: "VAL_THERAPEUTIC_AREA_001"})
SET v21.rule_name = "THERAPEUTIC_AREA Not Null Check",
    v21.column_name = "THERAPEUTIC_AREA",
    v21.table_id = "DIM_BRAND_MASTER",
    v21.validation_type = "NOT_NULL",
    v21.validation_rule = "NOT_NULL",
    v21.error_message = "Therapeutic Area is required for pharmaceuticals",
    v21.business_criticality = "critical",
    v21.priority = "P1",
    v21.applicable_domains = ["pharmaceuticals"],
    v21.is_active = true,
    v21.created_date = datetime(),
    v21.last_updated_date = datetime()
WITH v21
MATCH (c:Column {column_id: "COL_THERAPEUTIC_AREA_091"})
MERGE (c)-[:HAS_VALIDATION]->(v21);

// AGE_SEGMENT_TOYS - NOT NULL (Toys)
MERGE (v22:ValidationRule {rule_id: "VAL_AGE_SEGMENT_TOYS_001"})
SET v22.rule_name = "AGE_SEGMENT_TOYS Not Null Check",
    v22.column_name = "AGE_SEGMENT_TOYS",
    v22.table_id = "DIM_BRAND_MASTER",
    v22.validation_type = "NOT_NULL",
    v22.validation_rule = "NOT_NULL",
    v22.error_message = "Age Segment is required for toys",
    v22.business_criticality = "high",
    v22.priority = "P1",
    v22.applicable_domains = ["toys"],
    v22.is_active = true,
    v22.created_date = datetime(),
    v22.last_updated_date = datetime()
WITH v22
MATCH (c:Column {column_id: "COL_AGE_SEGMENT_TOYS_092"})
MERGE (c)-[:HAS_VALIDATION]->(v22);

// AGE_SEGMENT_TOYS - ENUM
MERGE (v23:ValidationRule {rule_id: "VAL_AGE_SEGMENT_TOYS_002"})
SET v23.rule_name = "AGE_SEGMENT_TOYS Domain Check",
    v23.column_name = "AGE_SEGMENT_TOYS",
    v23.table_id = "DIM_BRAND_MASTER",
    v23.validation_type = "ENUM",
    v23.validation_rule = "ENUM:Infant/Toddler,Preschool,Kids,Tweens",
    v23.error_message = "Invalid toy age segment",
    v23.business_criticality = "high",
    v23.priority = "P1",
    v23.applicable_domains = ["toys"],
    v23.is_active = true,
    v23.created_date = datetime(),
    v23.last_updated_date = datetime()
WITH v23
MATCH (c:Column {column_id: "COL_AGE_SEGMENT_TOYS_092"})
MERGE (c)-[:HAS_VALIDATION]->(v23);

// BEAUTY_CATEGORY - NOT NULL (Cosmetics)
MERGE (v24:ValidationRule {rule_id: "VAL_BEAUTY_CATEGORY_001"})
SET v24.rule_name = "BEAUTY_CATEGORY Not Null Check",
    v24.column_name = "BEAUTY_CATEGORY",
    v24.table_id = "DIM_BRAND_MASTER",
    v24.validation_type = "NOT_NULL",
    v24.validation_rule = "NOT_NULL",
    v24.error_message = "Beauty Category is required for cosmetics",
    v24.business_criticality = "high",
    v24.priority = "P1",
    v24.applicable_domains = ["cosmetics"],
    v24.is_active = true,
    v24.created_date = datetime(),
    v24.last_updated_date = datetime()
WITH v24
MATCH (c:Column {column_id: "COL_BEAUTY_CATEGORY_093"})
MERGE (c)-[:HAS_VALIDATION]->(v24);

// BEAUTY_CATEGORY - ENUM
MERGE (v25:ValidationRule {rule_id: "VAL_BEAUTY_CATEGORY_002"})
SET v25.rule_name = "BEAUTY_CATEGORY Domain Check",
    v25.column_name = "BEAUTY_CATEGORY",
    v25.table_id = "DIM_BRAND_MASTER",
    v25.validation_type = "ENUM",
    v25.validation_rule = "ENUM:Skincare,Makeup,Fragrance,Haircare",
    v25.error_message = "Invalid beauty category",
    v25.business_criticality = "high",
    v25.priority = "P1",
    v25.applicable_domains = ["cosmetics"],
    v25.is_active = true,
    v25.created_date = datetime(),
    v25.last_updated_date = datetime()
WITH v25
MATCH (c:Column {column_id: "COL_BEAUTY_CATEGORY_093"})
MERGE (c)-[:HAS_VALIDATION]->(v25);

// ORGANIC_PORTFOLIO_FLAG - NOT NULL (Food & Beverage)
MERGE (v26:ValidationRule {rule_id: "VAL_ORGANIC_PORTFOLIO_001"})
SET v26.rule_name = "ORGANIC_PORTFOLIO_FLAG Not Null Check",
    v26.column_name = "ORGANIC_PORTFOLIO_FLAG",
    v26.table_id = "DIM_BRAND_MASTER",
    v26.validation_type = "NOT_NULL",
    v26.validation_rule = "NOT_NULL",
    v26.error_message = "Organic Portfolio Flag is required for food & beverage",
    v26.business_criticality = "high",
    v26.priority = "P1",
    v26.applicable_domains = ["food_beverage"],
    v26.is_active = true,
    v26.created_date = datetime(),
    v26.last_updated_date = datetime()
WITH v26
MATCH (c:Column {column_id: "COL_ORGANIC_PORTFOLIO_094"})
MERGE (c)-[:HAS_VALIDATION]->(v26);

// BATTERY_TECHNOLOGY_TYPE - NOT NULL (Battery)
MERGE (v27:ValidationRule {rule_id: "VAL_BATTERY_TECH_TYPE_001"})
SET v27.rule_name = "BATTERY_TECHNOLOGY_TYPE Not Null Check",
    v27.column_name = "BATTERY_TECHNOLOGY_TYPE",
    v27.table_id = "DIM_BRAND_MASTER",
    v27.validation_type = "NOT_NULL",
    v27.validation_rule = "NOT_NULL",
    v27.error_message = "Battery Technology Type is required for battery brands",
    v27.business_criticality = "high",
    v27.priority = "P1",
    v27.applicable_domains = ["battery"],
    v27.is_active = true,
    v27.created_date = datetime(),
    v27.last_updated_date = datetime()
WITH v27
MATCH (c:Column {column_id: "COL_BATTERY_TECH_TYPE_095"})
MERGE (c)-[:HAS_VALIDATION]->(v27);

// BATTERY_TECHNOLOGY_TYPE - ENUM
MERGE (v28:ValidationRule {rule_id: "VAL_BATTERY_TECH_TYPE_002"})
SET v28.rule_name = "BATTERY_TECHNOLOGY_TYPE Domain Check",
    v28.column_name = "BATTERY_TECHNOLOGY_TYPE",
    v28.table_id = "DIM_BRAND_MASTER",
    v28.validation_type = "ENUM",
    v28.validation_rule = "ENUM:Alkaline,Lithium,Rechargeable,Specialty",
    v28.error_message = "Invalid battery technology type",
    v28.business_criticality = "high",
    v28.priority = "P1",
    v28.applicable_domains = ["battery"],
    v28.is_active = true,
    v28.created_date = datetime(),
    v28.last_updated_date = datetime()
WITH v28
MATCH (c:Column {column_id: "COL_BATTERY_TECH_TYPE_095"})
MERGE (c)-[:HAS_VALIDATION]->(v28);

// ========================================
// Additional High Priority Validations
// ========================================

// PRICE_POSITIONING - NOT NULL
MERGE (v29:ValidationRule {rule_id: "VAL_PRICE_POSITIONING_001"})
SET v29.rule_name = "PRICE_POSITIONING Not Null Check",
    v29.column_name = "PRICE_POSITIONING",
    v29.table_id = "DIM_BRAND_MASTER",
    v29.validation_type = "NOT_NULL",
    v29.validation_rule = "NOT_NULL",
    v29.error_message = "Price Positioning is required",
    v29.business_criticality = "high",
    v29.priority = "P1",
    v29.applicable_domains = ["ALL"],
    v29.is_active = true,
    v29.created_date = datetime(),
    v29.last_updated_date = datetime()
WITH v29
MATCH (c:Column {column_id: "COL_PRICE_POSITIONING_058"})
MERGE (c)-[:HAS_VALIDATION]->(v29);

// PRICE_POSITIONING - ENUM
MERGE (v30:ValidationRule {rule_id: "VAL_PRICE_POSITIONING_002"})
SET v30.rule_name = "PRICE_POSITIONING Domain Check",
    v30.column_name = "PRICE_POSITIONING",
    v30.table_id = "DIM_BRAND_MASTER",
    v30.validation_type = "ENUM",
    v30.validation_rule = "ENUM:Premium,Mid-tier,Value,Ultra-value",
    v30.error_message = "Invalid price positioning",
    v30.business_criticality = "high",
    v30.priority = "P1",
    v30.applicable_domains = ["ALL"],
    v30.is_active = true,
    v30.created_date = datetime(),
    v30.last_updated_date = datetime()
WITH v30
MATCH (c:Column {column_id: "COL_PRICE_POSITIONING_058"})
MERGE (c)-[:HAS_VALIDATION]->(v30);

// TRADEMARK_REGISTRATION - NOT NULL
MERGE (v31:ValidationRule {rule_id: "VAL_TRADEMARK_REG_001"})
SET v31.rule_name = "TRADEMARK_REGISTRATION Not Null Check",
    v31.column_name = "TRADEMARK_REGISTRATION",
    v31.table_id = "DIM_BRAND_MASTER",
    v31.validation_type = "NOT_NULL",
    v31.validation_rule = "NOT_NULL",
    v31.error_message = "Trademark Registration is required",
    v31.business_criticality = "high",
    v31.priority = "P1",
    v31.applicable_domains = ["ALL"],
    v31.is_active = true,
    v31.created_date = datetime(),
    v31.last_updated_date = datetime()
WITH v31
MATCH (c:Column {column_id: "COL_TRADEMARK_REG_081"})
MERGE (c)-[:HAS_VALIDATION]->(v31);

// GROSS_MARGIN_PERCENT - RANGE
MERGE (v32:ValidationRule {rule_id: "VAL_GROSS_MARGIN_PCT_001"})
SET v32.rule_name = "GROSS_MARGIN_PERCENT Range Check",
    v32.column_name = "GROSS_MARGIN_PERCENT",
    v32.table_id = "DIM_BRAND_MASTER",
    v32.validation_type = "RANGE",
    v32.validation_rule = "RANGE:0.0,100.0",
    v32.error_message = "Gross Margin must be between 0 and 100",
    v32.business_criticality = "high",
    v32.priority = "P2",
    v32.applicable_domains = ["ALL"],
    v32.is_active = true,
    v32.created_date = datetime(),
    v32.last_updated_date = datetime()
WITH v32
MATCH (c:Column {column_id: "COL_GROSS_MARGIN_PERCENT_069"})
MERGE (c)-[:HAS_VALIDATION]->(v32);

// ECOMMERCE_REVENUE_PERCENT - RANGE
MERGE (v33:ValidationRule {rule_id: "VAL_ECOMMERCE_REV_PCT_001"})
SET v33.rule_name = "ECOMMERCE_REVENUE_PERCENT Range Check",
    v33.column_name = "ECOMMERCE_REVENUE_PERCENT",
    v33.table_id = "DIM_BRAND_MASTER",
    v33.validation_type = "RANGE",
    v33.validation_rule = "RANGE:0.0,100.0",
    v33.error_message = "E-commerce Revenue Percentage must be between 0 and 100",
    v33.business_criticality = "high",
    v33.priority = "P2",
    v33.applicable_domains = ["ALL"],
    v33.is_active = true,
    v33.created_date = datetime(),
    v33.last_updated_date = datetime()
WITH v33
MATCH (c:Column {column_id: "COL_ECOMMERCE_REV_PCT_073"})
MERGE (c)-[:HAS_VALIDATION]->(v33);

// BRAND_WEBSITE_URL - FORMAT
MERGE (v34:ValidationRule {rule_id: "VAL_BRAND_WEBSITE_URL_001"})
SET v34.rule_name = "BRAND_WEBSITE_URL Format Check",
    v34.column_name = "BRAND_WEBSITE_URL",
    v34.table_id = "DIM_BRAND_MASTER",
    v34.validation_type = "REGEX",
    v34.validation_rule = "^https?://[\\w\\-]+(\\.[\\w\\-]+)+[/#?]?.*$",
    v34.error_message = "Invalid website URL format",
    v34.business_criticality = "medium",
    v34.priority = "P3",
    v34.applicable_domains = ["ALL"],
    v34.is_active = true,
    v34.created_date = datetime(),
    v34.last_updated_date = datetime()
WITH v34
MATCH (c:Column {column_id: "COL_BRAND_WEBSITE_URL_074"})
MERGE (c)-[:HAS_VALIDATION]->(v34);

// ========================================
// Cross-Field Validations
// ========================================

// PARENT_BRAND_ID vs BRAND_LEVEL
MERGE (v35:ValidationRule {rule_id: "VAL_BRAND_HIERARCHY_001"})
SET v35.rule_name = "Brand Hierarchy Consistency Check",
    v35.column_name = "PARENT_BRAND_ID,BRAND_LEVEL",
    v35.table_id = "DIM_BRAND_MASTER",
    v35.validation_type = "CUSTOM",
    v35.validation_rule = "CUSTOM:IF(BRAND_LEVEL='Corporate',PARENT_BRAND_ID=NULL)",
    v35.error_message = "Corporate brands cannot have parent brands",
    v35.business_criticality = "high",
    v35.priority = "P2",
    v35.applicable_domains = ["ALL"],
    v35.is_active = true,
    v35.created_date = datetime(),
    v35.last_updated_date = datetime()
WITH v35
MATCH (t:Table {table_id: "DIM_BRAND_MASTER"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v35);

// BRAND_LAUNCH_DATE vs BRAND_ACQUISITION_DATE
MERGE (v36:ValidationRule {rule_id: "VAL_DATE_CONSISTENCY_001"})
SET v36.rule_name = "Launch vs Acquisition Date Check",
    v36.column_name = "BRAND_LAUNCH_DATE,BRAND_ACQUISITION_DATE",
    v36.table_id = "DIM_BRAND_MASTER",
    v36.validation_type = "DATE_COMPARISON",
    v36.validation_rule = "DATE_COMPARE:BRAND_LAUNCH_DATE<BRAND_ACQUISITION_DATE",
    v36.error_message = "Brand launch date must be before acquisition date",
    v36.business_criticality = "medium",
    v36.priority = "P3",
    v36.applicable_domains = ["ALL"],
    v36.is_active = true,
    v36.created_date = datetime(),
    v36.last_updated_date = datetime()
WITH v36
MATCH (t:Table {table_id: "DIM_BRAND_MASTER"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v36);

// TRADEMARK_REGISTRATION vs TRADEMARK_EXPIRY_DATE
MERGE (v37:ValidationRule {rule_id: "VAL_TRADEMARK_CONSISTENCY_001"})
SET v37.rule_name = "Trademark Registration Consistency Check",
    v37.column_name = "TRADEMARK_REGISTRATION,TRADEMARK_EXPIRY_DATE",
    v37.table_id = "DIM_BRAND_MASTER",
    v37.validation_type = "CUSTOM",
    v37.validation_rule = "CUSTOM:IF(TRADEMARK_REGISTRATION!=NULL,TRADEMARK_EXPIRY_DATE!=NULL)",
    v37.error_message = "Trademark registration requires expiry date",
    v37.business_criticality = "high",
    v37.priority = "P2",
    v37.applicable_domains = ["ALL"],
    v37.is_active = true,
    v37.created_date = datetime(),
    v37.last_updated_date = datetime()
WITH v37
MATCH (t:Table {table_id: "DIM_BRAND_MASTER"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v37);

// GLOBAL_BRAND_FLAG vs INTERNATIONAL_MARKETS_COUNT
MERGE (v38:ValidationRule {rule_id: "VAL_GLOBAL_CONSISTENCY_001"})
SET v38.rule_name = "Global Brand Consistency Check",
    v38.column_name = "GLOBAL_BRAND_FLAG,INTERNATIONAL_MARKETS_COUNT",
    v38.table_id = "DIM_BRAND_MASTER",
    v38.validation_type = "CUSTOM",
    v38.validation_rule = "CUSTOM:IF(GLOBAL_BRAND_FLAG=TRUE,INTERNATIONAL_MARKETS_COUNT>1)",
    v38.error_message = "Global brands must have more than 1 international market",
    v38.business_criticality = "medium",
    v38.priority = "P3",
    v38.applicable_domains = ["ALL"],
    v38.is_active = true,
    v38.created_date = datetime(),
    v38.last_updated_date = datetime()
WITH v38
MATCH (t:Table {table_id: "DIM_BRAND_MASTER"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v38);

// ========================================
// QUERY TO VERIFY CREATED VALIDATIONS
// ========================================

// Count validations by type
MATCH (v:ValidationRule {table_id: "DIM_BRAND_MASTER"})
RETURN v.validation_type as Type, count(*) as Count
ORDER BY Count DESC;

// Count validations by priority
MATCH (v:ValidationRule {table_id: "DIM_BRAND_MASTER"})
RETURN v.priority as Priority, count(*) as Count
ORDER BY Priority;