rule_id,rule_name,column_name,table_id,validation_type,validation_rule,error_message,business_criticality,priority,applicable_domains,is_active
VAL_CUSTOMER_ID_001,CUSTOMER_ID Not Null Check,CUSTOMER_ID,DIM_CUSTOMER,NOT_NULL,NOT_NULL,Customer ID is required,critical,P1,ALL,true
VAL_CUSTOMER_ID_002,CUSTOMER_ID Uniqueness Check,CUSTOMER_ID,DIM_CUSTOMER,UNIQUE,UNIQUE,Customer ID must be unique,critical,P1,ALL,true
VAL_CUSTOMER_ID_003,CUSTOMER_ID Format Check,CUSTOMER_ID,DIM_CUSTOMER,REGEX,^[A-Za-z0-9_-]{1\,50}$,Customer ID must be alphanumeric with underscores/hyphens\, max 50 chars,critical,P1,ALL,true
VAL_FIRST_NAME_001,FIRST_NAME Length Check,FIRST_NAME,DIM_CUSTOMER,LENGTH,LENGTH:1\,50,First Name must be 1-50 characters,medium,P2,ALL,true
VAL_LAST_NAME_001,LAST_NAME Length Check,LAST_NAME,DIM_CUSTOMER,LENGTH,LENGTH:1\,50,Last Name must be 1-50 characters,medium,P2,ALL,true
VAL_EMAIL_ADDRESS_001,EMAIL_ADDRESS Not Null Check,EMAIL_ADDRESS,DIM_CUSTOMER,NOT_NULL,NOT_NULL,Email Address is required,high,P1,ALL,true
VAL_EMAIL_ADDRESS_002,EMAIL_ADDRESS Format Check,EMAIL_ADDRESS,DIM_CUSTOMER,REGEX,^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2\,}$,Email Address must be in valid format,high,P1,ALL,true
VAL_PHONE_NUMBER_001,PHONE_NUMBER Format Check,PHONE_NUMBER,DIM_CUSTOMER,REGEX,^[+]?[0-9\\s\\-\\(\\)]{7\,30}$,Phone Number must be in valid format,high,P1,ALL,true
VAL_AGE_001,AGE Range Check,AGE,DIM_CUSTOMER,RANGE,RANGE:0\,120,Age must be between 0 and 120,high,P1,ALL,true
VAL_GENDER_001,GENDER Domain Check,GENDER,DIM_CUSTOMER,ENUM,ENUM:Male\,Female\,Other\,Prefer not to say,Invalid gender value,medium,P2,ALL,true
VAL_ADDRESS_LINE_1_001,ADDRESS_LINE_1 Not Null Check,ADDRESS_LINE_1,DIM_CUSTOMER,NOT_NULL,NOT_NULL,Primary address is required,high,P1,ALL,true
VAL_CITY_001,CITY Not Null Check,CITY,DIM_CUSTOMER,NOT_NULL,NOT_NULL,City is required,medium,P1,ALL,true
VAL_STATE_PROVINCE_001,STATE_PROVINCE Not Null Check,STATE_PROVINCE,DIM_CUSTOMER,NOT_NULL,NOT_NULL,State or Province is required,high,P1,ALL,true
VAL_ZIP_CODE_001,ZIP_CODE Format Check,ZIP_CODE,DIM_CUSTOMER,REGEX,^[A-Za-z0-9\\s-]{3\,15}$,ZIP Code must be in valid format,medium,P2,ALL,true
VAL_COUNTRY_001,COUNTRY Not Null Check,COUNTRY,DIM_CUSTOMER,NOT_NULL,NOT_NULL,Country is required,high,P1,ALL,true
VAL_LATITUDE_001,LATITUDE Range Check,LATITUDE,DIM_CUSTOMER,RANGE,RANGE:-90\,90,Latitude must be between -90 and 90,low,P3,ALL,true
VAL_LONGITUDE_001,LONGITUDE Range Check,LONGITUDE,DIM_CUSTOMER,RANGE,RANGE:-180\,180,Longitude must be between -180 and 180,low,P3,ALL,true
VAL_CUSTOMER_TYPE_001,CUSTOMER_TYPE Not Null Check,CUSTOMER_TYPE,DIM_CUSTOMER,NOT_NULL,NOT_NULL,Customer Type is required,high,P1,ALL,true
VAL_CUSTOMER_TYPE_002,CUSTOMER_TYPE Domain Check,CUSTOMER_TYPE,DIM_CUSTOMER,ENUM,ENUM:Individual\,Business\,Healthcare Provider\,Institutional,Invalid customer type,high,P1,ALL,true
VAL_CUSTOMER_SEGMENT_001,CUSTOMER_SEGMENT Not Null Check,CUSTOMER_SEGMENT,DIM_CUSTOMER,NOT_NULL,NOT_NULL,Customer Segment is required,high,P1,ALL,true
VAL_CUSTOMER_TIER_001,CUSTOMER_TIER Domain Check,CUSTOMER_TIER,DIM_CUSTOMER,ENUM,ENUM:Platinum\,Gold\,Silver\,Bronze\,Standard,Invalid customer tier,medium,P2,ALL,true
VAL_LIFECYCLE_STAGE_001,LIFECYCLE_STAGE Domain Check,LIFECYCLE_STAGE,DIM_CUSTOMER,ENUM,ENUM:Prospect\,New\,Active\,At-Risk\,Inactive\,Churned,Invalid lifecycle stage,high,P1,ALL,true
VAL_PREFERRED_CHANNEL_001,PREFERRED_CHANNEL Domain Check,PREFERRED_CHANNEL,DIM_CUSTOMER,ENUM,ENUM:Online\,In-Store\,Mobile\,Mixed\,Catalog,Invalid preferred channel,high,P2,ALL,true
VAL_SHOPPING_FREQUENCY_001,SHOPPING_FREQUENCY Domain Check,SHOPPING_FREQUENCY,DIM_CUSTOMER,ENUM,ENUM:Daily\,Weekly\,Bi-Weekly\,Monthly\,Occasionally,Invalid shopping frequency,medium,P2,ALL,true
VAL_PRICE_SENSITIVITY_001,PRICE_SENSITIVITY Domain Check,PRICE_SENSITIVITY,DIM_CUSTOMER,ENUM,ENUM:High\,Medium\,Low,Invalid price sensitivity level,high,P2,ALL,true
VAL_BRAND_LOYALTY_LEVEL_001,BRAND_LOYALTY_LEVEL Domain Check,BRAND_LOYALTY_LEVEL,DIM_CUSTOMER,ENUM,ENUM:Highly Loyal\,Moderately Loyal\,Switch-Prone\,Price-Driven,Invalid brand loyalty level,high,P2,ALL,true
VAL_LOYALTY_PROGRAM_MEMBER_001,LOYALTY_PROGRAM_MEMBER Not Null Check,LOYALTY_PROGRAM_MEMBER,DIM_CUSTOMER,NOT_NULL,NOT_NULL,Loyalty program member flag is required,high,P1,ALL,true
VAL_LOYALTY_POINTS_BALANCE_001,LOYALTY_POINTS_BALANCE Range Check,LOYALTY_POINTS_BALANCE,DIM_CUSTOMER,RANGE,RANGE:0\,999999999,Loyalty points must be non-negative,medium,P2,ALL,true
VAL_ENGAGEMENT_SCORE_001,ENGAGEMENT_SCORE Range Check,ENGAGEMENT_SCORE,DIM_CUSTOMER,RANGE,RANGE:0\,100,Engagement score must be between 0 and 100,high,P2,ALL,true
VAL_TOTAL_LIFETIME_VALUE_001,TOTAL_LIFETIME_VALUE Range Check,TOTAL_LIFETIME_VALUE,DIM_CUSTOMER,RANGE,RANGE:0\,9999999999.99,Total lifetime value must be non-negative,critical,P1,ALL,true
VAL_AVERAGE_ORDER_VALUE_001,AVERAGE_ORDER_VALUE Range Check,AVERAGE_ORDER_VALUE,DIM_CUSTOMER,RANGE,RANGE:0\,99999.99,Average order value must be non-negative,high,P2,ALL,true
VAL_PURCHASE_FREQUENCY_ANNUAL_001,PURCHASE_FREQUENCY_ANNUAL Range Check,PURCHASE_FREQUENCY_ANNUAL,DIM_CUSTOMER,RANGE,RANGE:0\,365,Annual purchase frequency must be between 0 and 365,medium,P2,ALL,true
VAL_HOUSEHOLD_SIZE_001,HOUSEHOLD_SIZE Range Check,HOUSEHOLD_SIZE,DIM_CUSTOMER,RANGE,RANGE:1\,20,Household size must be between 1 and 20,medium,P2,ALL,true
VAL_CHILDREN_IN_HOUSEHOLD_001,CHILDREN_IN_HOUSEHOLD Range Check,CHILDREN_IN_HOUSEHOLD,DIM_CUSTOMER,RANGE,RANGE:0\,15,Number of children must be between 0 and 15,medium,P2,ALL,true
VAL_LIFE_STAGE_001,LIFE_STAGE Domain Check,LIFE_STAGE,DIM_CUSTOMER,ENUM,ENUM:Young Adult\,Family Formation\,Established Family\,Empty Nester\,Retiree,Invalid life stage,high,P2,ALL,true
VAL_EMAIL_OPT_IN_001,EMAIL_OPT_IN Not Null Check,EMAIL_OPT_IN,DIM_CUSTOMER,NOT_NULL,NOT_NULL,Email opt-in status is required,high,P1,ALL,true
VAL_SMS_OPT_IN_001,SMS_OPT_IN Not Null Check,SMS_OPT_IN,DIM_CUSTOMER,NOT_NULL,NOT_NULL,SMS opt-in status is required,medium,P2,ALL,true
VAL_ORGANIC_PREFERENCE_001,ORGANIC_PREFERENCE Domain Check,ORGANIC_PREFERENCE,DIM_CUSTOMER,ENUM,ENUM:Strong\,Moderate\,Low\,None,Invalid organic preference level,medium,P3,ALL,true
VAL_HEALTH_WELLNESS_FOCUS_001,HEALTH_WELLNESS_FOCUS Domain Check,HEALTH_WELLNESS_FOCUS,DIM_CUSTOMER,ENUM,ENUM:Very High\,High\,Medium\,Low,Invalid health wellness focus level,medium,P3,ALL,true
VAL_SUSTAINABILITY_CONCERN_001,SUSTAINABILITY_CONCERN Domain Check,SUSTAINABILITY_CONCERN,DIM_CUSTOMER,ENUM,ENUM:Very High\,High\,Medium\,Low,Invalid sustainability concern level,medium,P3,ALL,true
VAL_AGE_VERIFICATION_STATUS_001,AGE_VERIFICATION_STATUS Not Null Check,AGE_VERIFICATION_STATUS,DIM_CUSTOMER,NOT_NULL,NOT_NULL,Age verification status is required for age-restricted products,critical,P1,alcoholic_beverages|pharmaceuticals,true
VAL_AGE_VERIFICATION_STATUS_002,AGE_VERIFICATION_STATUS Domain Check,AGE_VERIFICATION_STATUS,DIM_CUSTOMER,ENUM,ENUM:Verified\,Not Verified\,Pending\,Expired,Invalid age verification status,critical,P1,alcoholic_beverages|pharmaceuticals,true
VAL_PRESCRIPTION_INSURANCE_001,PRESCRIPTION_INSURANCE Not Null Check,PRESCRIPTION_INSURANCE,DIM_CUSTOMER,NOT_NULL,NOT_NULL,Prescription insurance flag is required for pharmaceutical customers,high,P1,pharmaceuticals,true
VAL_CUSTOMER_STATUS_001,CUSTOMER_STATUS Not Null Check,CUSTOMER_STATUS,DIM_CUSTOMER,NOT_NULL,NOT_NULL,Customer status is required,critical,P1,ALL,true
VAL_CUSTOMER_STATUS_002,CUSTOMER_STATUS Domain Check,CUSTOMER_STATUS,DIM_CUSTOMER,ENUM,ENUM:Active\,Inactive\,Suspended\,Closed,Invalid customer status,critical,P1,ALL,true
VAL_DATA_SHARING_CONSENT_001,DATA_SHARING_CONSENT Not Null Check,DATA_SHARING_CONSENT,DIM_CUSTOMER,NOT_NULL,NOT_NULL,Data sharing consent is required,high,P1,ALL,true
VAL_PRIVACY_PREFERENCE_001,PRIVACY_PREFERENCE Domain Check,PRIVACY_PREFERENCE,DIM_CUSTOMER,ENUM,ENUM:High\,Medium\,Low,Invalid privacy preference level,medium,P2,ALL,true
VAL_AGE_VERIFICATION_001,Age Verification Consistency Check,AGE\,AGE_VERIFICATION_STATUS,DIM_CUSTOMER,CUSTOM,CUSTOM:IF(AGE>=21\,AGE_VERIFICATION_STATUS='Verified'),Customers 21+ must have verified age status for alcoholic beverages,critical,P1,alcoholic_beverages,true
VAL_LOYALTY_POINTS_001,Loyalty Points Consistency Check,LOYALTY_PROGRAM_MEMBER\,LOYALTY_POINTS_BALANCE,DIM_CUSTOMER,CUSTOM,CUSTOM:IF(LOYALTY_PROGRAM_MEMBER=false\,LOYALTY_POINTS_BALANCE=0),Non-members cannot have loyalty points,medium,P2,ALL,true
VAL_HOUSEHOLD_LOGIC_001,Household Composition Check,CHILDREN_IN_HOUSEHOLD\,HOUSEHOLD_SIZE,DIM_CUSTOMER,CUSTOM,CUSTOM:CHILDREN_IN_HOUSEHOLD<HOUSEHOLD_SIZE,Number of children cannot exceed household size,medium,P2,ALL,true
VAL_PARENT_CHILDREN_001,Parent Flag Consistency Check,PARENT_FLAG\,CHILDREN_IN_HOUSEHOLD,DIM_CUSTOMER,CUSTOM,CUSTOM:IF(PARENT_FLAG=true\,CHILDREN_IN_HOUSEHOLD>0),Parents must have at least one child in household,medium,P3,toys|baby_products,true
VAL_PURCHASE_DATE_001,Purchase Date Consistency Check,FIRST_PURCHASE_DATE\,LAST_PURCHASE_DATE,DIM_CUSTOMER,DATE_COMPARISON,DATE_COMPARE:FIRST_PURCHASE_DATE<=LAST_PURCHASE_DATE,First purchase date must be before or equal to last purchase date,high,P2,ALL,true
VAL_EMAIL_OPTIN_001,Email Opt-In Consistency Check,EMAIL_OPT_IN\,EMAIL_ADDRESS,DIM_CUSTOMER,CUSTOM,CUSTOM:IF(EMAIL_OPT_IN=true\,EMAIL_ADDRESS IS NOT NULL),Email opt-in requires valid email address,high,P2,ALL,true