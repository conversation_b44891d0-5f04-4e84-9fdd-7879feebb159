// ========================================
// DATA QUALITY VALIDATION RULES FOR FACT_INVENTORY
// This script creates validation rules and links them to columns
// ========================================

// Create constraint for ValidationRule uniqueness (run once)
CREATE CONSTRAINT validation_rule_unique IF NOT EXISTS
FOR (v:ValidationRule) REQUIRE v.rule_id IS UNIQUE;

// ========================================
// INVENTORY_SNAPSHOT_ID Validations
// ========================================

// INVENTORY_SNAPSHOT_ID - NOT NULL
MERGE (v1:ValidationRule {rule_id: "VAL_INVENTORY_SNAPSHOT_ID_001"})
SET v1.rule_name = "INVENTORY_SNAPSHOT_ID Not Null Check",
    v1.column_name = "INVENTORY_SNAPSHOT_ID",
    v1.table_id = "FACT_INVENTORY",
    v1.validation_type = "NOT_NULL",
    v1.validation_rule = "NOT_NULL",
    v1.error_message = "Inventory Snapshot ID is required",
    v1.business_criticality = "critical",
    v1.priority = "P1",
    v1.applicable_domains = ["ALL"],
    v1.is_active = true,
    v1.created_date = datetime(),
    v1.last_updated_date = datetime()
WITH v1
MATCH (c:Column {column_id: "COL_INVENTORY_SNAPSHOT_ID_FACT_3001"})
MERGE (c)-[:HAS_VALIDATION]->(v1);

// INVENTORY_SNAPSHOT_ID - UNIQUE
MERGE (v2:ValidationRule {rule_id: "VAL_INVENTORY_SNAPSHOT_ID_002"})
SET v2.rule_name = "INVENTORY_SNAPSHOT_ID Uniqueness Check",
    v2.column_name = "INVENTORY_SNAPSHOT_ID",
    v2.table_id = "FACT_INVENTORY",
    v2.validation_type = "UNIQUE",
    v2.validation_rule = "UNIQUE",
    v2.error_message = "Inventory Snapshot ID must be unique",
    v2.business_criticality = "critical",
    v2.priority = "P1",
    v2.applicable_domains = ["ALL"],
    v2.is_active = true,
    v2.created_date = datetime(),
    v2.last_updated_date = datetime()
WITH v2
MATCH (c:Column {column_id: "COL_INVENTORY_SNAPSHOT_ID_FACT_3001"})
MERGE (c)-[:HAS_VALIDATION]->(v2);

// INVENTORY_SNAPSHOT_ID - FORMAT
MERGE (v3:ValidationRule {rule_id: "VAL_INVENTORY_SNAPSHOT_ID_003"})
SET v3.rule_name = "INVENTORY_SNAPSHOT_ID Format Check",
    v3.column_name = "INVENTORY_SNAPSHOT_ID",
    v3.table_id = "FACT_INVENTORY",
    v3.validation_type = "REGEX",
    v3.validation_rule = "^[A-Za-z0-9_-]{1,50}$",
    v3.error_message = "Inventory Snapshot ID must be alphanumeric with dash/underscore, max 50 chars",
    v3.business_criticality = "critical",
    v3.priority = "P1",
    v3.applicable_domains = ["ALL"],
    v3.is_active = true,
    v3.created_date = datetime(),
    v3.last_updated_date = datetime()
WITH v3
MATCH (c:Column {column_id: "COL_INVENTORY_SNAPSHOT_ID_FACT_3001"})
MERGE (c)-[:HAS_VALIDATION]->(v3);

// ========================================
// Foreign Key Validations
// ========================================

// DATE_KEY - NOT NULL
MERGE (v4:ValidationRule {rule_id: "VAL_DATE_KEY_001"})
SET v4.rule_name = "DATE_KEY Not Null Check",
    v4.column_name = "DATE_KEY",
    v4.table_id = "FACT_INVENTORY",
    v4.validation_type = "NOT_NULL",
    v4.validation_rule = "NOT_NULL",
    v4.error_message = "Date Key is required",
    v4.business_criticality = "critical",
    v4.priority = "P1",
    v4.applicable_domains = ["ALL"],
    v4.is_active = true,
    v4.created_date = datetime(),
    v4.last_updated_date = datetime()
WITH v4
MATCH (c:Column {column_id: "COL_DATE_KEY_FACT_3002"})
MERGE (c)-[:HAS_VALIDATION]->(v4);

// DATE_KEY - REFERENCE
MERGE (v5:ValidationRule {rule_id: "VAL_DATE_KEY_002"})
SET v5.rule_name = "DATE_KEY Reference Check",
    v5.column_name = "DATE_KEY",
    v5.table_id = "FACT_INVENTORY",
    v5.validation_type = "REFERENCE",
    v5.validation_rule = "FK:DIM_DATE.DATE_KEY",
    v5.error_message = "Date Key must exist in Date dimension",
    v5.business_criticality = "critical",
    v5.priority = "P1",
    v5.applicable_domains = ["ALL"],
    v5.is_active = true,
    v5.created_date = datetime(),
    v5.last_updated_date = datetime()
WITH v5
MATCH (c:Column {column_id: "COL_DATE_KEY_FACT_3002"})
MERGE (c)-[:HAS_VALIDATION]->(v5);

// STORE_KEY - NOT NULL
MERGE (v6:ValidationRule {rule_id: "VAL_STORE_KEY_001"})
SET v6.rule_name = "STORE_KEY Not Null Check",
    v6.column_name = "STORE_KEY",
    v6.table_id = "FACT_INVENTORY",
    v6.validation_type = "NOT_NULL",
    v6.validation_rule = "NOT_NULL",
    v6.error_message = "Store Key is required",
    v6.business_criticality = "critical",
    v6.priority = "P1",
    v6.applicable_domains = ["ALL"],
    v6.is_active = true,
    v6.created_date = datetime(),
    v6.last_updated_date = datetime()
WITH v6
MATCH (c:Column {column_id: "COL_STORE_KEY_FACT_3003"})
MERGE (c)-[:HAS_VALIDATION]->(v6);

// STORE_KEY - REFERENCE
MERGE (v7:ValidationRule {rule_id: "VAL_STORE_KEY_002"})
SET v7.rule_name = "STORE_KEY Reference Check",
    v7.column_name = "STORE_KEY",
    v7.table_id = "FACT_INVENTORY",
    v7.validation_type = "REFERENCE",
    v7.validation_rule = "FK:DIM_STORE.STORE_KEY",
    v7.error_message = "Store Key must exist in Store dimension",
    v7.business_criticality = "critical",
    v7.priority = "P1",
    v7.applicable_domains = ["ALL"],
    v7.is_active = true,
    v7.created_date = datetime(),
    v7.last_updated_date = datetime()
WITH v7
MATCH (c:Column {column_id: "COL_STORE_KEY_FACT_3003"})
MERGE (c)-[:HAS_VALIDATION]->(v7);

// PRODUCT_HIERARCHY_KEY - NOT NULL
MERGE (v8:ValidationRule {rule_id: "VAL_PRODUCT_HIERARCHY_KEY_001"})
SET v8.rule_name = "PRODUCT_HIERARCHY_KEY Not Null Check",
    v8.column_name = "PRODUCT_HIERARCHY_KEY",
    v8.table_id = "FACT_INVENTORY",
    v8.validation_type = "NOT_NULL",
    v8.validation_rule = "NOT_NULL",
    v8.error_message = "Product Hierarchy Key is required",
    v8.business_criticality = "critical",
    v8.priority = "P1",
    v8.applicable_domains = ["ALL"],
    v8.is_active = true,
    v8.created_date = datetime(),
    v8.last_updated_date = datetime()
WITH v8
MATCH (c:Column {column_id: "COL_PRODUCT_HIERARCHY_KEY_FACT_3004"})
MERGE (c)-[:HAS_VALIDATION]->(v8);

// PRODUCT_HIERARCHY_KEY - REFERENCE
MERGE (v9:ValidationRule {rule_id: "VAL_PRODUCT_HIERARCHY_KEY_002"})
SET v9.rule_name = "PRODUCT_HIERARCHY_KEY Reference Check",
    v9.column_name = "PRODUCT_HIERARCHY_KEY",
    v9.table_id = "FACT_INVENTORY",
    v9.validation_type = "REFERENCE",
    v9.validation_rule = "FK:DIM_PRODUCT_HIERARCHY.PRODUCT_HIERARCHY_KEY",
    v9.error_message = "Product Hierarchy Key must exist in Product Hierarchy dimension",
    v9.business_criticality = "critical",
    v9.priority = "P1",
    v9.applicable_domains = ["ALL"],
    v9.is_active = true,
    v9.created_date = datetime(),
    v9.last_updated_date = datetime()
WITH v9
MATCH (c:Column {column_id: "COL_PRODUCT_HIERARCHY_KEY_FACT_3004"})
MERGE (c)-[:HAS_VALIDATION]->(v9);

// SUPPLIER_KEY - REFERENCE (nullable)
MERGE (v10:ValidationRule {rule_id: "VAL_SUPPLIER_KEY_001"})
SET v10.rule_name = "SUPPLIER_KEY Reference Check",
    v10.column_name = "SUPPLIER_KEY",
    v10.table_id = "FACT_INVENTORY",
    v10.validation_type = "REFERENCE_NULLABLE",
    v10.validation_rule = "FK_NULLABLE:DIM_SUPPLIER.SUPPLIER_KEY",
    v10.error_message = "Supplier Key must exist in Supplier dimension if provided",
    v10.business_criticality = "high",
    v10.priority = "P2",
    v10.applicable_domains = ["ALL"],
    v10.is_active = true,
    v10.created_date = datetime(),
    v10.last_updated_date = datetime()
WITH v10
MATCH (c:Column {column_id: "COL_SUPPLIER_KEY_FACT_3005"})
MERGE (c)-[:HAS_VALIDATION]->(v10);

// ========================================
// Core Quantity Measures Validations
// ========================================

// ON_HAND_QUANTITY - NOT NULL
MERGE (v11:ValidationRule {rule_id: "VAL_ON_HAND_QUANTITY_001"})
SET v11.rule_name = "ON_HAND_QUANTITY Not Null Check",
    v11.column_name = "ON_HAND_QUANTITY",
    v11.table_id = "FACT_INVENTORY",
    v11.validation_type = "NOT_NULL",
    v11.validation_rule = "NOT_NULL",
    v11.error_message = "On Hand Quantity is required",
    v11.business_criticality = "critical",
    v11.priority = "P1",
    v11.applicable_domains = ["ALL"],
    v11.is_active = true,
    v11.created_date = datetime(),
    v11.last_updated_date = datetime()
WITH v11
MATCH (c:Column {column_id: "COL_ON_HAND_QUANTITY_FACT_3006"})
MERGE (c)-[:HAS_VALIDATION]->(v11);

// ON_HAND_QUANTITY - RANGE
MERGE (v12:ValidationRule {rule_id: "VAL_ON_HAND_QUANTITY_002"})
SET v12.rule_name = "ON_HAND_QUANTITY Range Check",
    v12.column_name = "ON_HAND_QUANTITY",
    v12.table_id = "FACT_INVENTORY",
    v12.validation_type = "RANGE",
    v12.validation_rule = "RANGE:0,999999999.999",
    v12.error_message = "On Hand Quantity must be non-negative",
    v12.business_criticality = "critical",
    v12.priority = "P1",
    v12.applicable_domains = ["ALL"],
    v12.is_active = true,
    v12.created_date = datetime(),
    v12.last_updated_date = datetime()
WITH v12
MATCH (c:Column {column_id: "COL_ON_HAND_QUANTITY_FACT_3006"})
MERGE (c)-[:HAS_VALIDATION]->(v12);

// AVAILABLE_QUANTITY - NOT NULL
MERGE (v13:ValidationRule {rule_id: "VAL_AVAILABLE_QUANTITY_001"})
SET v13.rule_name = "AVAILABLE_QUANTITY Not Null Check",
    v13.column_name = "AVAILABLE_QUANTITY",
    v13.table_id = "FACT_INVENTORY",
    v13.validation_type = "NOT_NULL",
    v13.validation_rule = "NOT_NULL",
    v13.error_message = "Available Quantity is required",
    v13.business_criticality = "critical",
    v13.priority = "P1",
    v13.applicable_domains = ["ALL"],
    v13.is_active = true,
    v13.created_date = datetime(),
    v13.last_updated_date = datetime()
WITH v13
MATCH (c:Column {column_id: "COL_AVAILABLE_QUANTITY_FACT_3007"})
MERGE (c)-[:HAS_VALIDATION]->(v13);

// AVAILABLE_QUANTITY - RANGE
MERGE (v14:ValidationRule {rule_id: "VAL_AVAILABLE_QUANTITY_002"})
SET v14.rule_name = "AVAILABLE_QUANTITY Range Check",
    v14.column_name = "AVAILABLE_QUANTITY",
    v14.table_id = "FACT_INVENTORY",
    v14.validation_type = "RANGE",
    v14.validation_rule = "RANGE:0,999999999.999",
    v14.error_message = "Available Quantity must be non-negative",
    v14.business_criticality = "critical",
    v14.priority = "P1",
    v14.applicable_domains = ["ALL"],
    v14.is_active = true,
    v14.created_date = datetime(),
    v14.last_updated_date = datetime()
WITH v14
MATCH (c:Column {column_id: "COL_AVAILABLE_QUANTITY_FACT_3007"})
MERGE (c)-[:HAS_VALIDATION]->(v14);

// ALLOCATED_QUANTITY - RANGE
MERGE (v15:ValidationRule {rule_id: "VAL_ALLOCATED_QUANTITY_001"})
SET v15.rule_name = "ALLOCATED_QUANTITY Range Check",
    v15.column_name = "ALLOCATED_QUANTITY",
    v15.table_id = "FACT_INVENTORY",
    v15.validation_type = "RANGE",
    v15.validation_rule = "RANGE:0,999999999.999",
    v15.error_message = "Allocated Quantity must be non-negative",
    v15.business_criticality = "high",
    v15.priority = "P1",
    v15.applicable_domains = ["ALL"],
    v15.is_active = true,
    v15.created_date = datetime(),
    v15.last_updated_date = datetime()
WITH v15
MATCH (c:Column {column_id: "COL_ALLOCATED_QUANTITY_FACT_3008"})
MERGE (c)-[:HAS_VALIDATION]->(v15);

// IN_TRANSIT_QUANTITY - RANGE
MERGE (v16:ValidationRule {rule_id: "VAL_IN_TRANSIT_QUANTITY_001"})
SET v16.rule_name = "IN_TRANSIT_QUANTITY Range Check",
    v16.column_name = "IN_TRANSIT_QUANTITY",
    v16.table_id = "FACT_INVENTORY",
    v16.validation_type = "RANGE",
    v16.validation_rule = "RANGE:0,999999999.999",
    v16.error_message = "In Transit Quantity must be non-negative",
    v16.business_criticality = "high",
    v16.priority = "P1",
    v16.applicable_domains = ["ALL"],
    v16.is_active = true,
    v16.created_date = datetime(),
    v16.last_updated_date = datetime()
WITH v16
MATCH (c:Column {column_id: "COL_IN_TRANSIT_QUANTITY_FACT_3009"})
MERGE (c)-[:HAS_VALIDATION]->(v16);

// ========================================
// Inventory Valuation Measures Validations
// ========================================

// INVENTORY_VALUE - NOT NULL
MERGE (v17:ValidationRule {rule_id: "VAL_INVENTORY_VALUE_001"})
SET v17.rule_name = "INVENTORY_VALUE Not Null Check",
    v17.column_name = "INVENTORY_VALUE",
    v17.table_id = "FACT_INVENTORY",
    v17.validation_type = "NOT_NULL",
    v17.validation_rule = "NOT_NULL",
    v17.error_message = "Inventory Value is required",
    v17.business_criticality = "critical",
    v17.priority = "P1",
    v17.applicable_domains = ["ALL"],
    v17.is_active = true,
    v17.created_date = datetime(),
    v17.last_updated_date = datetime()
WITH v17
MATCH (c:Column {column_id: "COL_INVENTORY_VALUE_FACT_3010"})
MERGE (c)-[:HAS_VALIDATION]->(v17);

// INVENTORY_VALUE - RANGE
MERGE (v18:ValidationRule {rule_id: "VAL_INVENTORY_VALUE_002"})
SET v18.rule_name = "INVENTORY_VALUE Range Check",
    v18.column_name = "INVENTORY_VALUE",
    v18.table_id = "FACT_INVENTORY",
    v18.validation_type = "RANGE",
    v18.validation_rule = "RANGE:0,999999999999.99",
    v18.error_message = "Inventory Value must be non-negative",
    v18.business_criticality = "critical",
    v18.priority = "P1",
    v18.applicable_domains = ["ALL"],
    v18.is_active = true,
    v18.created_date = datetime(),
    v18.last_updated_date = datetime()
WITH v18
MATCH (c:Column {column_id: "COL_INVENTORY_VALUE_FACT_3010"})
MERGE (c)-[:HAS_VALIDATION]->(v18);

// UNIT_COST - NOT NULL
MERGE (v19:ValidationRule {rule_id: "VAL_UNIT_COST_001"})
SET v19.rule_name = "UNIT_COST Not Null Check",
    v19.column_name = "UNIT_COST",
    v19.table_id = "FACT_INVENTORY",
    v19.validation_type = "NOT_NULL",
    v19.validation_rule = "NOT_NULL",
    v19.error_message = "Unit Cost is required",
    v19.business_criticality = "high",
    v19.priority = "P1",
    v19.applicable_domains = ["ALL"],
    v19.is_active = true,
    v19.created_date = datetime(),
    v19.last_updated_date = datetime()
WITH v19
MATCH (c:Column {column_id: "COL_UNIT_COST_FACT_3011"})
MERGE (c)-[:HAS_VALIDATION]->(v19);

// UNIT_COST - RANGE
MERGE (v20:ValidationRule {rule_id: "VAL_UNIT_COST_002"})
SET v20.rule_name = "UNIT_COST Range Check",
    v20.column_name = "UNIT_COST",
    v20.table_id = "FACT_INVENTORY",
    v20.validation_type = "RANGE",
    v20.validation_rule = "RANGE:0,99999999.9999",
    v20.error_message = "Unit Cost must be non-negative",
    v20.business_criticality = "high",
    v20.priority = "P1",
    v20.applicable_domains = ["ALL"],
    v20.is_active = true,
    v20.created_date = datetime(),
    v20.last_updated_date = datetime()
WITH v20
MATCH (c:Column {column_id: "COL_UNIT_COST_FACT_3011"})
MERGE (c)-[:HAS_VALIDATION]->(v20);

// LANDED_COST - RANGE
MERGE (v21:ValidationRule {rule_id: "VAL_LANDED_COST_001"})
SET v21.rule_name = "LANDED_COST Range Check",
    v21.column_name = "LANDED_COST",
    v21.table_id = "FACT_INVENTORY",
    v21.validation_type = "RANGE",
    v21.validation_rule = "RANGE:0,99999999.9999",
    v21.error_message = "Landed Cost must be non-negative",
    v21.business_criticality = "high",
    v21.priority = "P1",
    v21.applicable_domains = ["ALL"],
    v21.is_active = true,
    v21.created_date = datetime(),
    v21.last_updated_date = datetime()
WITH v21
MATCH (c:Column {column_id: "COL_LANDED_COST_FACT_3012"})
MERGE (c)-[:HAS_VALIDATION]->(v21);

// ========================================
// Turnover and Velocity Measures Validations
// ========================================

// INVENTORY_TURNS - RANGE
MERGE (v22:ValidationRule {rule_id: "VAL_INVENTORY_TURNS_001"})
SET v22.rule_name = "INVENTORY_TURNS Range Check",
    v22.column_name = "INVENTORY_TURNS",
    v22.table_id = "FACT_INVENTORY",
    v22.validation_type = "RANGE",
    v22.validation_rule = "RANGE:0,999.99",
    v22.error_message = "Inventory Turns must be non-negative",
    v22.business_criticality = "critical",
    v22.priority = "P1",
    v22.applicable_domains = ["ALL"],
    v22.is_active = true,
    v22.created_date = datetime(),
    v22.last_updated_date = datetime()
WITH v22
MATCH (c:Column {column_id: "COL_INVENTORY_TURNS_FACT_3014"})
MERGE (c)-[:HAS_VALIDATION]->(v22);

// DAYS_ON_HAND - RANGE
MERGE (v23:ValidationRule {rule_id: "VAL_DAYS_ON_HAND_001"})
SET v23.rule_name = "DAYS_ON_HAND Range Check",
    v23.column_name = "DAYS_ON_HAND",
    v23.table_id = "FACT_INVENTORY",
    v23.validation_type = "RANGE",
    v23.validation_rule = "RANGE:0,9999.9",
    v23.error_message = "Days On Hand must be non-negative",
    v23.business_criticality = "critical",
    v23.priority = "P1",
    v23.applicable_domains = ["ALL"],
    v23.is_active = true,
    v23.created_date = datetime(),
    v23.last_updated_date = datetime()
WITH v23
MATCH (c:Column {column_id: "COL_DAYS_ON_HAND_FACT_3015"})
MERGE (c)-[:HAS_VALIDATION]->(v23);

// VELOCITY_SCORE - RANGE
MERGE (v24:ValidationRule {rule_id: "VAL_VELOCITY_SCORE_001"})
SET v24.rule_name = "VELOCITY_SCORE Range Check",
    v24.column_name = "VELOCITY_SCORE",
    v24.table_id = "FACT_INVENTORY",
    v24.validation_type = "RANGE",
    v24.validation_rule = "RANGE:0,100",
    v24.error_message = "Velocity Score must be between 0 and 100",
    v24.business_criticality = "high",
    v24.priority = "P1",
    v24.applicable_domains = ["ALL"],
    v24.is_active = true,
    v24.created_date = datetime(),
    v24.last_updated_date = datetime()
WITH v24
MATCH (c:Column {column_id: "COL_VELOCITY_SCORE_FACT_3016"})
MERGE (c)-[:HAS_VALIDATION]->(v24);

// SELL_THROUGH_RATE - RANGE
MERGE (v25:ValidationRule {rule_id: "VAL_SELL_THROUGH_RATE_001"})
SET v25.rule_name = "SELL_THROUGH_RATE Range Check",
    v25.column_name = "SELL_THROUGH_RATE",
    v25.table_id = "FACT_INVENTORY",
    v25.validation_type = "RANGE",
    v25.validation_rule = "RANGE:0,100",
    v25.error_message = "Sell Through Rate must be between 0 and 100",
    v25.business_criticality = "high",
    v25.priority = "P1",
    v25.applicable_domains = ["ALL"],
    v25.is_active = true,
    v25.created_date = datetime(),
    v25.last_updated_date = datetime()
WITH v25
MATCH (c:Column {column_id: "COL_SELL_THROUGH_RATE_FACT_3017"})
MERGE (c)-[:HAS_VALIDATION]->(v25);

// ========================================
// Stock Status and Aging Validations
// ========================================

// SAFETY_STOCK_QUANTITY - RANGE
MERGE (v26:ValidationRule {rule_id: "VAL_SAFETY_STOCK_QUANTITY_001"})
SET v26.rule_name = "SAFETY_STOCK_QUANTITY Range Check",
    v26.column_name = "SAFETY_STOCK_QUANTITY",
    v26.table_id = "FACT_INVENTORY",
    v26.validation_type = "RANGE",
    v26.validation_rule = "RANGE:0,999999999.999",
    v26.error_message = "Safety Stock Quantity must be non-negative",
    v26.business_criticality = "high",
    v26.priority = "P1",
    v26.applicable_domains = ["ALL"],
    v26.is_active = true,
    v26.created_date = datetime(),
    v26.last_updated_date = datetime()
WITH v26
MATCH (c:Column {column_id: "COL_SAFETY_STOCK_QUANTITY_FACT_3018"})
MERGE (c)-[:HAS_VALIDATION]->(v26);

// REORDER_POINT_QUANTITY - RANGE
MERGE (v27:ValidationRule {rule_id: "VAL_REORDER_POINT_QUANTITY_001"})
SET v27.rule_name = "REORDER_POINT_QUANTITY Range Check",
    v27.column_name = "REORDER_POINT_QUANTITY",
    v27.table_id = "FACT_INVENTORY",
    v27.validation_type = "RANGE",
    v27.validation_rule = "RANGE:0,999999999.999",
    v27.error_message = "Reorder Point Quantity must be non-negative",
    v27.business_criticality = "high",
    v27.priority = "P1",
    v27.applicable_domains = ["ALL"],
    v27.is_active = true,
    v27.created_date = datetime(),
    v27.last_updated_date = datetime()
WITH v27
MATCH (c:Column {column_id: "COL_REORDER_POINT_QUANTITY_FACT_3019"})
MERGE (c)-[:HAS_VALIDATION]->(v27);

// STOCKOUT_FLAG - BOOLEAN
MERGE (v28:ValidationRule {rule_id: "VAL_STOCKOUT_FLAG_001"})
SET v28.rule_name = "STOCKOUT_FLAG Boolean Check",
    v28.column_name = "STOCKOUT_FLAG",
    v28.table_id = "FACT_INVENTORY",
    v28.validation_type = "BOOLEAN",
    v28.validation_rule = "BOOLEAN",
    v28.error_message = "Stockout Flag must be TRUE or FALSE",
    v28.business_criticality = "critical",
    v28.priority = "P1",
    v28.applicable_domains = ["ALL"],
    v28.is_active = true,
    v28.created_date = datetime(),
    v28.last_updated_date = datetime()
WITH v28
MATCH (c:Column {column_id: "COL_STOCKOUT_FLAG_FACT_3021"})
MERGE (c)-[:HAS_VALIDATION]->(v28);

// INVENTORY_AGE_DAYS - RANGE
MERGE (v29:ValidationRule {rule_id: "VAL_INVENTORY_AGE_DAYS_001"})
SET v29.rule_name = "INVENTORY_AGE_DAYS Range Check",
    v29.column_name = "INVENTORY_AGE_DAYS",
    v29.table_id = "FACT_INVENTORY",
    v29.validation_type = "RANGE",
    v29.validation_rule = "RANGE:0,99999",
    v29.error_message = "Inventory Age Days must be non-negative",
    v29.business_criticality = "high",
    v29.priority = "P1",
    v29.applicable_domains = ["ALL"],
    v29.is_active = true,
    v29.created_date = datetime(),
    v29.last_updated_date = datetime()
WITH v29
MATCH (c:Column {column_id: "COL_INVENTORY_AGE_DAYS_FACT_3022"})
MERGE (c)-[:HAS_VALIDATION]->(v29);

// DAYS_UNTIL_EXPIRATION - RANGE (can be negative for expired items)
MERGE (v30:ValidationRule {rule_id: "VAL_DAYS_UNTIL_EXPIRATION_001"})
SET v30.rule_name = "DAYS_UNTIL_EXPIRATION Range Check",
    v30.column_name = "DAYS_UNTIL_EXPIRATION",
    v30.table_id = "FACT_INVENTORY",
    v30.validation_type = "RANGE",
    v30.validation_rule = "RANGE:-9999,99999",
    v30.error_message = "Days Until Expiration out of range",
    v30.business_criticality = "critical",
    v30.priority = "P1",
    v30.applicable_domains = ["pharmaceuticals", "food_beverage", "personal_care", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products", "cosmetics"],
    v30.is_active = true,
    v30.created_date = datetime(),
    v30.last_updated_date = datetime()
WITH v30
MATCH (c:Column {column_id: "COL_DAYS_UNTIL_EXPIRATION_FACT_3023"})
MERGE (c)-[:HAS_VALIDATION]->(v30);

// EXPIRATION_RISK_FLAG - BOOLEAN
MERGE (v31:ValidationRule {rule_id: "VAL_EXPIRATION_RISK_FLAG_001"})
SET v31.rule_name = "EXPIRATION_RISK_FLAG Boolean Check",
    v31.column_name = "EXPIRATION_RISK_FLAG",
    v31.table_id = "FACT_INVENTORY",
    v31.validation_type = "BOOLEAN",
    v31.validation_rule = "BOOLEAN",
    v31.error_message = "Expiration Risk Flag must be TRUE or FALSE",
    v31.business_criticality = "high",
    v31.priority = "P1",
    v31.applicable_domains = ["pharmaceuticals", "food_beverage", "personal_care", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products", "cosmetics"],
    v31.is_active = true,
    v31.created_date = datetime(),
    v31.last_updated_date = datetime()
WITH v31
MATCH (c:Column {column_id: "COL_EXPIRATION_RISK_FLAG_FACT_3024"})
MERGE (c)-[:HAS_VALIDATION]->(v31);

// ========================================
// Warehouse and Location Validations
// ========================================

// WAREHOUSE_CAPACITY_UTILIZATION - RANGE
MERGE (v32:ValidationRule {rule_id: "VAL_WAREHOUSE_CAPACITY_UTIL_001"})
SET v32.rule_name = "WAREHOUSE_CAPACITY_UTILIZATION Range Check",
    v32.column_name = "WAREHOUSE_CAPACITY_UTILIZATION",
    v32.table_id = "FACT_INVENTORY",
    v32.validation_type = "RANGE",
    v32.validation_rule = "RANGE:0,100",
    v32.error_message = "Warehouse Capacity Utilization must be between 0 and 100",
    v32.business_criticality = "medium",
    v32.priority = "P2",
    v32.applicable_domains = ["ALL"],
    v32.is_active = true,
    v32.created_date = datetime(),
    v32.last_updated_date = datetime()
WITH v32
MATCH (c:Column {column_id: "COL_WAREHOUSE_CAPACITY_UTIL_FACT_3026"})
MERGE (c)-[:HAS_VALIDATION]->(v32);

// LOCATION_COUNT - RANGE
MERGE (v33:ValidationRule {rule_id: "VAL_LOCATION_COUNT_001"})
SET v33.rule_name = "LOCATION_COUNT Range Check",
    v33.column_name = "LOCATION_COUNT",
    v33.table_id = "FACT_INVENTORY",
    v33.validation_type = "RANGE",
    v33.validation_rule = "RANGE:0,9999",
    v33.error_message = "Location Count must be non-negative",
    v33.business_criticality = "low",
    v33.priority = "P3",
    v33.applicable_domains = ["ALL"],
    v33.is_active = true,
    v33.created_date = datetime(),
    v33.last_updated_date = datetime()
WITH v33
MATCH (c:Column {column_id: "COL_LOCATION_COUNT_FACT_3027"})
MERGE (c)-[:HAS_VALIDATION]->(v33);

// ========================================
// Domain-Specific Validations - Alcoholic Beverages
// ========================================

// BONDED_INVENTORY_QUANTITY - NOT NULL (Alcoholic Beverages)
MERGE (v34:ValidationRule {rule_id: "VAL_BONDED_INVENTORY_QTY_001"})
SET v34.rule_name = "BONDED_INVENTORY_QUANTITY Not Null Check",
    v34.column_name = "BONDED_INVENTORY_QUANTITY",
    v34.table_id = "FACT_INVENTORY",
    v34.validation_type = "NOT_NULL",
    v34.validation_rule = "NOT_NULL",
    v34.error_message = "Bonded Inventory Quantity is required for alcoholic beverages",
    v34.business_criticality = "high",
    v34.priority = "P1",
    v34.applicable_domains = ["alcoholic_beverages"],
    v34.is_active = true,
    v34.created_date = datetime(),
    v34.last_updated_date = datetime()
WITH v34
MATCH (c:Column {column_id: "COL_BONDED_INVENTORY_QUANTITY_FACT_3029"})
MERGE (c)-[:HAS_VALIDATION]->(v34);

// BONDED_INVENTORY_QUANTITY - RANGE
MERGE (v35:ValidationRule {rule_id: "VAL_BONDED_INVENTORY_QTY_002"})
SET v35.rule_name = "BONDED_INVENTORY_QUANTITY Range Check",
    v35.column_name = "BONDED_INVENTORY_QUANTITY",
    v35.table_id = "FACT_INVENTORY",
    v35.validation_type = "RANGE",
    v35.validation_rule = "RANGE:0,999999999.999",
    v35.error_message = "Bonded Inventory Quantity must be non-negative",
    v35.business_criticality = "high",
    v35.priority = "P1",
    v35.applicable_domains = ["alcoholic_beverages"],
    v35.is_active = true,
    v35.created_date = datetime(),
    v35.last_updated_date = datetime()
WITH v35
MATCH (c:Column {column_id: "COL_BONDED_INVENTORY_QUANTITY_FACT_3029"})
MERGE (c)-[:HAS_VALIDATION]->(v35);

// VINTAGE_YEAR - RANGE
MERGE (v36:ValidationRule {rule_id: "VAL_VINTAGE_YEAR_001"})
SET v36.rule_name = "VINTAGE_YEAR Range Check",
    v36.column_name = "VINTAGE_YEAR",
    v36.table_id = "FACT_INVENTORY",
    v36.validation_type = "RANGE",
    v36.validation_rule = "RANGE:1800,2100",
    v36.error_message = "Vintage Year must be between 1800 and 2100",
    v36.business_criticality = "medium",
    v36.priority = "P2",
    v36.applicable_domains = ["alcoholic_beverages"],
    v36.is_active = true,
    v36.created_date = datetime(),
    v36.last_updated_date = datetime()
WITH v36
MATCH (c:Column {column_id: "COL_VINTAGE_YEAR_FACT_3030"})
MERGE (c)-[:HAS_VALIDATION]->(v36);

// PROOF_GALLONS - NOT NULL (Alcoholic Beverages)
MERGE (v37:ValidationRule {rule_id: "VAL_PROOF_GALLONS_001"})
SET v37.rule_name = "PROOF_GALLONS Not Null Check",
    v37.column_name = "PROOF_GALLONS",
    v37.table_id = "FACT_INVENTORY",
    v37.validation_type = "NOT_NULL",
    v37.validation_rule = "NOT_NULL",
    v37.error_message = "Proof Gallons is required for alcoholic beverages",
    v37.business_criticality = "critical",
    v37.priority = "P1",
    v37.applicable_domains = ["alcoholic_beverages"],
    v37.is_active = true,
    v37.created_date = datetime(),
    v37.last_updated_date = datetime()
WITH v37
MATCH (c:Column {column_id: "COL_PROOF_GALLONS_FACT_3031"})
MERGE (c)-[:HAS_VALIDATION]->(v37);

// PROOF_GALLONS - RANGE
MERGE (v38:ValidationRule {rule_id: "VAL_PROOF_GALLONS_002"})
SET v38.rule_name = "PROOF_GALLONS Range Check",
    v38.column_name = "PROOF_GALLONS",
    v38.table_id = "FACT_INVENTORY",
    v38.validation_type = "RANGE",
    v38.validation_rule = "RANGE:0,999999999.999",
    v38.error_message = "Proof Gallons must be non-negative",
    v38.business_criticality = "critical",
    v38.priority = "P1",
    v38.applicable_domains = ["alcoholic_beverages"],
    v38.is_active = true,
    v38.created_date = datetime(),
    v38.last_updated_date = datetime()
WITH v38
MATCH (c:Column {column_id: "COL_PROOF_GALLONS_FACT_3031"})
MERGE (c)-[:HAS_VALIDATION]->(v38);

// ========================================
// Domain-Specific Validations - Pharmaceuticals
// ========================================

// LOT_NUMBER - NOT NULL (Pharmaceuticals and other regulated)
MERGE (v39:ValidationRule {rule_id: "VAL_LOT_NUMBER_001"})
SET v39.rule_name = "LOT_NUMBER Not Null Check",
    v39.column_name = "LOT_NUMBER",
    v39.table_id = "FACT_INVENTORY",
    v39.validation_type = "NOT_NULL",
    v39.validation_rule = "NOT_NULL",
    v39.error_message = "Lot Number is required for regulated products",
    v39.business_criticality = "critical",
    v39.priority = "P1",
    v39.applicable_domains = ["pharmaceuticals", "food_beverage", "cosmetics", "personal_care", "health_supplements", "baby_products"],
    v39.is_active = true,
    v39.created_date = datetime(),
    v39.last_updated_date = datetime()
WITH v39
MATCH (c:Column {column_id: "COL_LOT_NUMBER_FACT_3032"})
MERGE (c)-[:HAS_VALIDATION]->(v39);

// LOT_NUMBER - FORMAT
MERGE (v40:ValidationRule {rule_id: "VAL_LOT_NUMBER_002"})
SET v40.rule_name = "LOT_NUMBER Format Check",
    v40.column_name = "LOT_NUMBER",
    v40.table_id = "FACT_INVENTORY",
    v40.validation_type = "REGEX",
    v40.validation_rule = "^[A-Za-z0-9-]{1,50}$",
    v40.error_message = "Lot Number must be alphanumeric with dash, max 50 chars",
    v40.business_criticality = "critical",
    v40.priority = "P1",
    v40.applicable_domains = ["pharmaceuticals", "food_beverage", "cosmetics", "personal_care", "health_supplements", "baby_products"],
    v40.is_active = true,
    v40.created_date = datetime(),
    v40.last_updated_date = datetime()
WITH v40
MATCH (c:Column {column_id: "COL_LOT_NUMBER_FACT_3032"})
MERGE (c)-[:HAS_VALIDATION]->(v40);

// CONTROLLED_SUBSTANCE_FLAG - BOOLEAN
MERGE (v41:ValidationRule {rule_id: "VAL_CONTROLLED_SUBSTANCE_FLAG_001"})
SET v41.rule_name = "CONTROLLED_SUBSTANCE_FLAG Boolean Check",
    v41.column_name = "CONTROLLED_SUBSTANCE_FLAG",
    v41.table_id = "FACT_INVENTORY",
    v41.validation_type = "BOOLEAN",
    v41.validation_rule = "BOOLEAN",
    v41.error_message = "Controlled Substance Flag must be TRUE or FALSE",
    v41.business_criticality = "critical",
    v41.priority = "P1",
    v41.applicable_domains = ["pharmaceuticals"],
    v41.is_active = true,
    v41.created_date = datetime(),
    v41.last_updated_date = datetime()
WITH v41
MATCH (c:Column {column_id: "COL_CONTROLLED_SUBSTANCE_FLAG_FACT_3033"})
MERGE (c)-[:HAS_VALIDATION]->(v41);

// COLD_CHAIN_FLAG - BOOLEAN
MERGE (v42:ValidationRule {rule_id: "VAL_COLD_CHAIN_FLAG_001"})
SET v42.rule_name = "COLD_CHAIN_FLAG Boolean Check",
    v42.column_name = "COLD_CHAIN_FLAG",
    v42.table_id = "FACT_INVENTORY",
    v42.validation_type = "BOOLEAN",
    v42.validation_rule = "BOOLEAN",
    v42.error_message = "Cold Chain Flag must be TRUE or FALSE",
    v42.business_criticality = "critical",
    v42.priority = "P1",
    v42.applicable_domains = ["pharmaceuticals", "food_beverage", "dairy", "frozen_foods", "health_supplements"],
    v42.is_active = true,
    v42.created_date = datetime(),
    v42.last_updated_date = datetime()
WITH v42
MATCH (c:Column {column_id: "COL_COLD_CHAIN_FLAG_FACT_3034"})
MERGE (c)-[:HAS_VALIDATION]->(v42);

// ========================================
// Quality and Compliance Validations
// ========================================

// QUALITY_HOLD_QUANTITY - RANGE
MERGE (v43:ValidationRule {rule_id: "VAL_QUALITY_HOLD_QUANTITY_001"})
SET v43.rule_name = "QUALITY_HOLD_QUANTITY Range Check",
    v43.column_name = "QUALITY_HOLD_QUANTITY",
    v43.table_id = "FACT_INVENTORY",
    v43.validation_type = "RANGE",
    v43.validation_rule = "RANGE:0,999999999.999",
    v43.error_message = "Quality Hold Quantity must be non-negative",
    v43.business_criticality = "high",
    v43.priority = "P1",
    v43.applicable_domains = ["ALL"],
    v43.is_active = true,
    v43.created_date = datetime(),
    v43.last_updated_date = datetime()
WITH v43
MATCH (c:Column {column_id: "COL_QUALITY_HOLD_QUANTITY_FACT_3035"})
MERGE (c)-[:HAS_VALIDATION]->(v43);

// QUARANTINE_QUANTITY - RANGE
MERGE (v44:ValidationRule {rule_id: "VAL_QUARANTINE_QUANTITY_001"})
SET v44.rule_name = "QUARANTINE_QUANTITY Range Check",
    v44.column_name = "QUARANTINE_QUANTITY",
    v44.table_id = "FACT_INVENTORY",
    v44.validation_type = "RANGE",
    v44.validation_rule = "RANGE:0,999999999.999",
    v44.error_message = "Quarantine Quantity must be non-negative",
    v44.business_criticality = "critical",
    v44.priority = "P1",
    v44.applicable_domains = ["ALL"],
    v44.is_active = true,
    v44.created_date = datetime(),
    v44.last_updated_date = datetime()
WITH v44
MATCH (c:Column {column_id: "COL_QUARANTINE_QUANTITY_FACT_3036"})
MERGE (c)-[:HAS_VALIDATION]->(v44);

// DAMAGED_QUANTITY - RANGE
MERGE (v45:ValidationRule {rule_id: "VAL_DAMAGED_QUANTITY_001"})
SET v45.rule_name = "DAMAGED_QUANTITY Range Check",
    v45.column_name = "DAMAGED_QUANTITY",
    v45.table_id = "FACT_INVENTORY",
    v45.validation_type = "RANGE",
    v45.validation_rule = "RANGE:0,999999999.999",
    v45.error_message = "Damaged Quantity must be non-negative",
    v45.business_criticality = "medium",
    v45.priority = "P2",
    v45.applicable_domains = ["ALL"],
    v45.is_active = true,
    v45.created_date = datetime(),
    v45.last_updated_date = datetime()
WITH v45
MATCH (c:Column {column_id: "COL_DAMAGED_QUANTITY_FACT_3037"})
MERGE (c)-[:HAS_VALIDATION]->(v45);

// ========================================
// Performance Percentages Validations
// ========================================

// FILL_RATE_PERCENTAGE - RANGE
MERGE (v46:ValidationRule {rule_id: "VAL_FILL_RATE_PERCENTAGE_001"})
SET v46.rule_name = "FILL_RATE_PERCENTAGE Range Check",
    v46.column_name = "FILL_RATE_PERCENTAGE",
    v46.table_id = "FACT_INVENTORY",
    v46.validation_type = "RANGE",
    v46.validation_rule = "RANGE:0,100",
    v46.error_message = "Fill Rate Percentage must be between 0 and 100",
    v46.business_criticality = "critical",
    v46.priority = "P1",
    v46.applicable_domains = ["ALL"],
    v46.is_active = true,
    v46.created_date = datetime(),
    v46.last_updated_date = datetime()
WITH v46
MATCH (c:Column {column_id: "COL_FILL_RATE_PERCENTAGE_FACT_3050"})
MERGE (c)-[:HAS_VALIDATION]->(v46);

// PERFECT_ORDER_PERCENTAGE - RANGE
MERGE (v47:ValidationRule {rule_id: "VAL_PERFECT_ORDER_PERCENTAGE_001"})
SET v47.rule_name = "PERFECT_ORDER_PERCENTAGE Range Check",
    v47.column_name = "PERFECT_ORDER_PERCENTAGE",
    v47.table_id = "FACT_INVENTORY",
    v47.validation_type = "RANGE",
    v47.validation_rule = "RANGE:0,100",
    v47.error_message = "Perfect Order Percentage must be between 0 and 100",
    v47.business_criticality = "high",
    v47.priority = "P1",
    v47.applicable_domains = ["ALL"],
    v47.is_active = true,
    v47.created_date = datetime(),
    v47.last_updated_date = datetime()
WITH v47
MATCH (c:Column {column_id: "COL_PERFECT_ORDER_PERCENTAGE_FACT_3051"})
MERGE (c)-[:HAS_VALIDATION]->(v47);

// FORECAST_ACCURACY - RANGE
MERGE (v48:ValidationRule {rule_id: "VAL_FORECAST_ACCURACY_001"})
SET v48.rule_name = "FORECAST_ACCURACY Range Check",
    v48.column_name = "FORECAST_ACCURACY",
    v48.table_id = "FACT_INVENTORY",
    v48.validation_type = "RANGE",
    v48.validation_rule = "RANGE:0,100",
    v48.error_message = "Forecast Accuracy must be between 0 and 100",
    v48.business_criticality = "high",
    v48.priority = "P1",
    v48.applicable_domains = ["ALL"],
    v48.is_active = true,
    v48.created_date = datetime(),
    v48.last_updated_date = datetime()
WITH v48
MATCH (c:Column {column_id: "COL_FORECAST_ACCURACY_FACT_3042"})
MERGE (c)-[:HAS_VALIDATION]->(v48);

// INVENTORY_ACCURACY_PERCENTAGE - RANGE
MERGE (v49:ValidationRule {rule_id: "VAL_INVENTORY_ACCURACY_PCT_001"})
SET v49.rule_name = "INVENTORY_ACCURACY_PERCENTAGE Range Check",
    v49.column_name = "INVENTORY_ACCURACY_PERCENTAGE",
    v49.table_id = "FACT_INVENTORY",
    v49.validation_type = "RANGE",
    v49.validation_rule = "RANGE:0,100",
    v49.error_message = "Inventory Accuracy Percentage must be between 0 and 100",
    v49.business_criticality = "high",
    v49.priority = "P1",
    v49.applicable_domains = ["ALL"],
    v49.is_active = true,
    v49.created_date = datetime(),
    v49.last_updated_date = datetime()
WITH v49
MATCH (c:Column {column_id: "COL_INVENTORY_ACCURACY_PCT_FACT_3045"})
MERGE (c)-[:HAS_VALIDATION]->(v49);

// ========================================
// Status and Classification Validations
// ========================================

// INVENTORY_STATUS - NOT NULL
MERGE (v50:ValidationRule {rule_id: "VAL_INVENTORY_STATUS_001"})
SET v50.rule_name = "INVENTORY_STATUS Not Null Check",
    v50.column_name = "INVENTORY_STATUS",
    v50.table_id = "FACT_INVENTORY",
    v50.validation_type = "NOT_NULL",
    v50.validation_rule = "NOT_NULL",
    v50.error_message = "Inventory Status is required",
    v50.business_criticality = "high",
    v50.priority = "P1",
    v50.applicable_domains = ["ALL"],
    v50.is_active = true,
    v50.created_date = datetime(),
    v50.last_updated_date = datetime()
WITH v50
MATCH (c:Column {column_id: "COL_INVENTORY_STATUS_FACT_3053"})
MERGE (c)-[:HAS_VALIDATION]->(v50);

// INVENTORY_STATUS - ENUM
MERGE (v51:ValidationRule {rule_id: "VAL_INVENTORY_STATUS_002"})
SET v51.rule_name = "INVENTORY_STATUS Domain Check",
    v51.column_name = "INVENTORY_STATUS",
    v51.table_id = "FACT_INVENTORY",
    v51.validation_type = "ENUM",
    v51.validation_rule = "ENUM:Active,Hold,Obsolete,Expired,Quarantine,Damaged",
    v51.error_message = "Invalid inventory status",
    v51.business_criticality = "high",
    v51.priority = "P1",
    v51.applicable_domains = ["ALL"],
    v51.is_active = true,
    v51.created_date = datetime(),
    v51.last_updated_date = datetime()
WITH v51
MATCH (c:Column {column_id: "COL_INVENTORY_STATUS_FACT_3053"})
MERGE (c)-[:HAS_VALIDATION]->(v51);

// ABC_CLASSIFICATION - ENUM
MERGE (v52:ValidationRule {rule_id: "VAL_ABC_CLASSIFICATION_001"})
SET v52.rule_name = "ABC_CLASSIFICATION Domain Check",
    v52.column_name = "ABC_CLASSIFICATION",
    v52.table_id = "FACT_INVENTORY",
    v52.validation_type = "ENUM",
    v52.validation_rule = "ENUM:A,B,C",
    v52.error_message = "ABC Classification must be A, B, or C",
    v52.business_criticality = "medium",
    v52.priority = "P2",
    v52.applicable_domains = ["ALL"],
    v52.is_active = true,
    v52.created_date = datetime(),
    v52.last_updated_date = datetime()
WITH v52
MATCH (c:Column {column_id: "COL_ABC_CLASSIFICATION_FACT_3054"})
MERGE (c)-[:HAS_VALIDATION]->(v52);

// ========================================
// Cross-Field Validations
// ========================================

// ON_HAND vs AVAILABLE QUANTITY
MERGE (v53:ValidationRule {rule_id: "VAL_QUANTITY_CONSISTENCY_001"})
SET v53.rule_name = "On Hand vs Available Quantity Check",
    v53.column_name = "ON_HAND_QUANTITY,AVAILABLE_QUANTITY,ALLOCATED_QUANTITY",
    v53.table_id = "FACT_INVENTORY",
    v53.validation_type = "CUSTOM",
    v53.validation_rule = "CUSTOM:AVAILABLE_QUANTITY<=ON_HAND_QUANTITY",
    v53.error_message = "Available quantity cannot exceed on-hand quantity",
    v53.business_criticality = "critical",
    v53.priority = "P1",
    v53.applicable_domains = ["ALL"],
    v53.is_active = true,
    v53.created_date = datetime(),
    v53.last_updated_date = datetime()
WITH v53
MATCH (t:Table {table_id: "FACT_INVENTORY"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v53);

// ALLOCATED vs ON_HAND QUANTITY
MERGE (v54:ValidationRule {rule_id: "VAL_QUANTITY_CONSISTENCY_002"})
SET v54.rule_name = "Allocated vs On Hand Quantity Check",
    v54.column_name = "ON_HAND_QUANTITY,ALLOCATED_QUANTITY",
    v54.table_id = "FACT_INVENTORY",
    v54.validation_type = "CUSTOM",
    v54.validation_rule = "CUSTOM:ALLOCATED_QUANTITY<=ON_HAND_QUANTITY",
    v54.error_message = "Allocated quantity cannot exceed on-hand quantity",
    v54.business_criticality = "critical",
    v54.priority = "P1",
    v54.applicable_domains = ["ALL"],
    v54.is_active = true,
    v54.created_date = datetime(),
    v54.last_updated_date = datetime()
WITH v54
MATCH (t:Table {table_id: "FACT_INVENTORY"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v54);

// INVENTORY VALUE vs QUANTITY AND COST
MERGE (v55:ValidationRule {rule_id: "VAL_VALUE_CALCULATION_001"})
SET v55.rule_name = "Inventory Value Calculation Check",
    v55.column_name = "INVENTORY_VALUE,ON_HAND_QUANTITY,UNIT_COST",
    v55.table_id = "FACT_INVENTORY",
    v55.validation_type = "CUSTOM",
    v55.validation_rule = "CUSTOM:ABS(INVENTORY_VALUE-(ON_HAND_QUANTITY*UNIT_COST))<0.01",
    v55.error_message = "Inventory value does not match quantity times unit cost",
    v55.business_criticality = "critical",
    v55.priority = "P1",
    v55.applicable_domains = ["ALL"],
    v55.is_active = true,
    v55.created_date = datetime(),
    v55.last_updated_date = datetime()
WITH v55
MATCH (t:Table {table_id: "FACT_INVENTORY"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v55);

// REORDER POINT vs SAFETY STOCK
MERGE (v56:ValidationRule {rule_id: "VAL_REORDER_SAFETY_001"})
SET v56.rule_name = "Reorder Point vs Safety Stock Check",
    v56.column_name = "REORDER_POINT_QUANTITY,SAFETY_STOCK_QUANTITY",
    v56.table_id = "FACT_INVENTORY",
    v56.validation_type = "CUSTOM",
    v56.validation_rule = "CUSTOM:REORDER_POINT_QUANTITY>=SAFETY_STOCK_QUANTITY",
    v56.error_message = "Reorder point should be greater than or equal to safety stock",
    v56.business_criticality = "high",
    v56.priority = "P2",
    v56.applicable_domains = ["ALL"],
    v56.is_active = true,
    v56.created_date = datetime(),
    v56.last_updated_date = datetime()
WITH v56
MATCH (t:Table {table_id: "FACT_INVENTORY"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v56);

// STOCKOUT FLAG CONSISTENCY
MERGE (v57:ValidationRule {rule_id: "VAL_STOCKOUT_CONSISTENCY_001"})
SET v57.rule_name = "Stockout Flag Consistency Check",
    v57.column_name = "STOCKOUT_FLAG,ON_HAND_QUANTITY",
    v57.table_id = "FACT_INVENTORY",
    v57.validation_type = "CUSTOM",
    v57.validation_rule = "CUSTOM:IF(STOCKOUT_FLAG=TRUE,ON_HAND_QUANTITY=0)",
    v57.error_message = "Stockout flag inconsistent with on-hand quantity",
    v57.business_criticality = "critical",
    v57.priority = "P1",
    v57.applicable_domains = ["ALL"],
    v57.is_active = true,
    v57.created_date = datetime(),
    v57.last_updated_date = datetime()
WITH v57
MATCH (t:Table {table_id: "FACT_INVENTORY"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v57);

// EXPIRATION RISK CONSISTENCY
MERGE (v58:ValidationRule {rule_id: "VAL_EXPIRATION_CONSISTENCY_001"})
SET v58.rule_name = "Expiration Risk Consistency Check",
    v58.column_name = "EXPIRATION_RISK_FLAG,DAYS_UNTIL_EXPIRATION",
    v58.table_id = "FACT_INVENTORY",
    v58.validation_type = "CUSTOM",
    v58.validation_rule = "CUSTOM:IF(EXPIRATION_RISK_FLAG=TRUE,DAYS_UNTIL_EXPIRATION<=30)",
    v58.error_message = "Expiration risk flag inconsistent with days until expiration",
    v58.business_criticality = "high",
    v58.priority = "P1",
    v58.applicable_domains = ["pharmaceuticals", "food_beverage", "personal_care", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products", "cosmetics"],
    v58.is_active = true,
    v58.created_date = datetime(),
    v58.last_updated_date = datetime()
WITH v58
MATCH (t:Table {table_id: "FACT_INVENTORY"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v58);

// QUALITY HOLD TOTAL
MERGE (v59:ValidationRule {rule_id: "VAL_QUALITY_TOTAL_001"})
SET v59.rule_name = "Quality Hold Total Check",
    v59.column_name = "ON_HAND_QUANTITY,QUALITY_HOLD_QUANTITY,QUARANTINE_QUANTITY,DAMAGED_QUANTITY",
    v59.table_id = "FACT_INVENTORY",
    v59.validation_type = "CUSTOM",
    v59.validation_rule = "CUSTOM:(QUALITY_HOLD_QUANTITY+QUARANTINE_QUANTITY+DAMAGED_QUANTITY)<=ON_HAND_QUANTITY",
    v59.error_message = "Total problematic inventory exceeds on-hand quantity",
    v59.business_criticality = "high",
    v59.priority = "P1",
    v59.applicable_domains = ["ALL"],
    v59.is_active = true,
    v59.created_date = datetime(),
    v59.last_updated_date = datetime()
WITH v59
MATCH (t:Table {table_id: "FACT_INVENTORY"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v59);

// COLD CHAIN PRODUCT EXPIRATION
MERGE (v60:ValidationRule {rule_id: "VAL_COLD_CHAIN_EXPIRY_001"})
SET v60.rule_name = "Cold Chain Product Expiration Check",
    v60.column_name = "COLD_CHAIN_FLAG,DAYS_UNTIL_EXPIRATION",
    v60.table_id = "FACT_INVENTORY",
    v60.validation_type = "CUSTOM",
    v60.validation_rule = "CUSTOM:IF(COLD_CHAIN_FLAG=TRUE,DAYS_UNTIL_EXPIRATION!=NULL)",
    v60.error_message = "Cold chain products must have expiration tracking",
    v60.business_criticality = "critical",
    v60.priority = "P1",
    v60.applicable_domains = ["pharmaceuticals", "food_beverage", "dairy", "frozen_foods", "health_supplements"],
    v60.is_active = true,
    v60.created_date = datetime(),
    v60.last_updated_date = datetime()
WITH v60
MATCH (t:Table {table_id: "FACT_INVENTORY"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v60);

// ========================================
// QUERY TO VERIFY CREATED VALIDATIONS
// ========================================

// Count validations by type
MATCH (v:ValidationRule {table_id: "FACT_INVENTORY"})
RETURN v.validation_type as Type, count(*) as Count
ORDER BY Count DESC;

// Count validations by priority
MATCH (v:ValidationRule {table_id: "FACT_INVENTORY"})
RETURN v.priority as Priority, count(*) as Count
ORDER BY Priority;

// Count validations by domain
MATCH (v:ValidationRule {table_id: "FACT_INVENTORY"})
UNWIND v.applicable_domains as domain
RETURN domain as Domain, count(DISTINCT v) as ValidationCount
ORDER BY ValidationCount DESC;