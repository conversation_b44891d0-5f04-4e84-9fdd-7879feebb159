// ========================================
// DATA QUALITY VALIDATION RULES FOR DIM_MANUFACTURER
// This script creates validation rules and links them to columns
// ========================================

// Create constraint for ValidationRule uniqueness (run once)
CREATE CONSTRAINT validation_rule_unique IF NOT EXISTS
FOR (v:ValidationRule) REQUIRE v.rule_id IS UNIQUE;

// ========================================
// MANUFACTURER_ID Validations
// ========================================

// MANUFACTURER_ID - NOT NULL
MERGE (v1:ValidationRule {rule_id: "VAL_MANUFACTURER_ID_001"})
SET v1.rule_name = "MANUFACTURER_ID Not Null Check",
    v1.column_name = "MANUFACTURER_ID",
    v1.table_id = "DIM_MANUFACTURER",
    v1.validation_type = "NOT_NULL",
    v1.validation_rule = "NOT_NULL",
    v1.error_message = "Manufacturer ID is required",
    v1.business_criticality = "critical",
    v1.priority = "P1",
    v1.applicable_domains = ["ALL"],
    v1.is_active = true,
    v1.created_date = datetime(),
    v1.last_updated_date = datetime()
WITH v1
MATCH (c:Column {column_id: "COL_MANUFACTURER_ID_DIM_150"})
MERGE (c)-[:HAS_VALIDATION]->(v1);

// MANUFACTURER_ID - UNIQUE
MERGE (v2:ValidationRule {rule_id: "VAL_MANUFACTURER_ID_002"})
SET v2.rule_name = "MANUFACTURER_ID Uniqueness Check",
    v2.column_name = "MANUFACTURER_ID",
    v2.table_id = "DIM_MANUFACTURER",
    v2.validation_type = "UNIQUE",
    v2.validation_rule = "UNIQUE",
    v2.error_message = "Manufacturer ID must be unique",
    v2.business_criticality = "critical",
    v2.priority = "P1",
    v2.applicable_domains = ["ALL"],
    v2.is_active = true,
    v2.created_date = datetime(),
    v2.last_updated_date = datetime()
WITH v2
MATCH (c:Column {column_id: "COL_MANUFACTURER_ID_DIM_150"})
MERGE (c)-[:HAS_VALIDATION]->(v2);

// MANUFACTURER_ID - FORMAT
MERGE (v3:ValidationRule {rule_id: "VAL_MANUFACTURER_ID_003"})
SET v3.rule_name = "MANUFACTURER_ID Format Check",
    v3.column_name = "MANUFACTURER_ID",
    v3.table_id = "DIM_MANUFACTURER",
    v3.validation_type = "REGEX",
    v3.validation_rule = "^[A-Za-z0-9_-]{1,20}$",
    v3.error_message = "Manufacturer ID must be alphanumeric with dash/underscore, max 20 chars",
    v3.business_criticality = "critical",
    v3.priority = "P1",
    v3.applicable_domains = ["ALL"],
    v3.is_active = true,
    v3.created_date = datetime(),
    v3.last_updated_date = datetime()
WITH v3
MATCH (c:Column {column_id: "COL_MANUFACTURER_ID_DIM_150"})
MERGE (c)-[:HAS_VALIDATION]->(v3);

// ========================================
// MANUFACTURER_NAME Validations
// ========================================

// MANUFACTURER_NAME - NOT NULL
MERGE (v4:ValidationRule {rule_id: "VAL_MANUFACTURER_NAME_001"})
SET v4.rule_name = "MANUFACTURER_NAME Not Null Check",
    v4.column_name = "MANUFACTURER_NAME",
    v4.table_id = "DIM_MANUFACTURER",
    v4.validation_type = "NOT_NULL",
    v4.validation_rule = "NOT_NULL",
    v4.error_message = "Manufacturer Name is required",
    v4.business_criticality = "critical",
    v4.priority = "P1",
    v4.applicable_domains = ["ALL"],
    v4.is_active = true,
    v4.created_date = datetime(),
    v4.last_updated_date = datetime()
WITH v4
MATCH (c:Column {column_id: "COL_MANUFACTURER_NAME_151"})
MERGE (c)-[:HAS_VALIDATION]->(v4);

// MANUFACTURER_NAME - LENGTH
MERGE (v5:ValidationRule {rule_id: "VAL_MANUFACTURER_NAME_002"})
SET v5.rule_name = "MANUFACTURER_NAME Length Check",
    v5.column_name = "MANUFACTURER_NAME",
    v5.table_id = "DIM_MANUFACTURER",
    v5.validation_type = "LENGTH",
    v5.validation_rule = "LENGTH:1,300",
    v5.error_message = "Manufacturer Name must be 1-300 characters",
    v5.business_criticality = "critical",
    v5.priority = "P1",
    v5.applicable_domains = ["ALL"],
    v5.is_active = true,
    v5.created_date = datetime(),
    v5.last_updated_date = datetime()
WITH v5
MATCH (c:Column {column_id: "COL_MANUFACTURER_NAME_151"})
MERGE (c)-[:HAS_VALIDATION]->(v5);

// ========================================
// Identifier Validations
// ========================================

// DUNS_NUMBER - FORMAT
MERGE (v6:ValidationRule {rule_id: "VAL_DUNS_NUMBER_001"})
SET v6.rule_name = "DUNS_NUMBER Format Check",
    v6.column_name = "DUNS_NUMBER",
    v6.table_id = "DIM_MANUFACTURER",
    v6.validation_type = "REGEX",
    v6.validation_rule = "^\\d{9}$",
    v6.error_message = "DUNS Number must be exactly 9 digits",
    v6.business_criticality = "high",
    v6.priority = "P1",
    v6.applicable_domains = ["ALL"],
    v6.is_active = true,
    v6.created_date = datetime(),
    v6.last_updated_date = datetime()
WITH v6
MATCH (c:Column {column_id: "COL_DUNS_NUMBER_153"})
MERGE (c)-[:HAS_VALIDATION]->(v6);

// TAX_ID_NUMBER - NOT NULL
MERGE (v7:ValidationRule {rule_id: "VAL_TAX_ID_NUMBER_001"})
SET v7.rule_name = "TAX_ID_NUMBER Not Null Check",
    v7.column_name = "TAX_ID_NUMBER",
    v7.table_id = "DIM_MANUFACTURER",
    v7.validation_type = "NOT_NULL",
    v7.validation_rule = "NOT_NULL",
    v7.error_message = "Tax ID Number is required",
    v7.business_criticality = "critical",
    v7.priority = "P1",
    v7.applicable_domains = ["ALL"],
    v7.is_active = true,
    v7.created_date = datetime(),
    v7.last_updated_date = datetime()
WITH v7
MATCH (c:Column {column_id: "COL_TAX_ID_NUMBER_154"})
MERGE (c)-[:HAS_VALIDATION]->(v7);

// TAX_ID_NUMBER - FORMAT (US EIN)
MERGE (v8:ValidationRule {rule_id: "VAL_TAX_ID_NUMBER_002"})
SET v8.rule_name = "TAX_ID_NUMBER Format Check",
    v8.column_name = "TAX_ID_NUMBER",
    v8.table_id = "DIM_MANUFACTURER",
    v8.validation_type = "REGEX",
    v8.validation_rule = "^\\d{2}-\\d{7}$|^\\d{9}$",
    v8.error_message = "Tax ID must be valid format (XX-XXXXXXX or XXXXXXXXX)",
    v8.business_criticality = "critical",
    v8.priority = "P1",
    v8.applicable_domains = ["ALL"],
    v8.is_active = true,
    v8.created_date = datetime(),
    v8.last_updated_date = datetime()
WITH v8
MATCH (c:Column {column_id: "COL_TAX_ID_NUMBER_154"})
MERGE (c)-[:HAS_VALIDATION]->(v8);

// ========================================
// Company Profile Validations
// ========================================

// MANUFACTURER_TYPE - NOT NULL
MERGE (v9:ValidationRule {rule_id: "VAL_MANUFACTURER_TYPE_001"})
SET v9.rule_name = "MANUFACTURER_TYPE Not Null Check",
    v9.column_name = "MANUFACTURER_TYPE",
    v9.table_id = "DIM_MANUFACTURER",
    v9.validation_type = "NOT_NULL",
    v9.validation_rule = "NOT_NULL",
    v9.error_message = "Manufacturer Type is required",
    v9.business_criticality = "high",
    v9.priority = "P1",
    v9.applicable_domains = ["ALL"],
    v9.is_active = true,
    v9.created_date = datetime(),
    v9.last_updated_date = datetime()
WITH v9
MATCH (c:Column {column_id: "COL_MANUFACTURER_TYPE_155"})
MERGE (c)-[:HAS_VALIDATION]->(v9);

// MANUFACTURER_TYPE - ENUM
MERGE (v10:ValidationRule {rule_id: "VAL_MANUFACTURER_TYPE_002"})
SET v10.rule_name = "MANUFACTURER_TYPE Domain Check",
    v10.column_name = "MANUFACTURER_TYPE",
    v10.table_id = "DIM_MANUFACTURER",
    v10.validation_type = "ENUM",
    v10.validation_rule = "ENUM:OEM,Contract Manufacturer,Co-Packer,Raw Material Supplier,Packaging Supplier,Component Supplier",
    v10.error_message = "Invalid manufacturer type",
    v10.business_criticality = "high",
    v10.priority = "P1",
    v10.applicable_domains = ["ALL"],
    v10.is_active = true,
    v10.created_date = datetime(),
    v10.last_updated_date = datetime()
WITH v10
MATCH (c:Column {column_id: "COL_MANUFACTURER_TYPE_155"})
MERGE (c)-[:HAS_VALIDATION]->(v10);

// COMPANY_SIZE - ENUM
MERGE (v11:ValidationRule {rule_id: "VAL_COMPANY_SIZE_001"})
SET v11.rule_name = "COMPANY_SIZE Domain Check",
    v11.column_name = "COMPANY_SIZE",
    v11.table_id = "DIM_MANUFACTURER",
    v11.validation_type = "ENUM",
    v11.validation_rule = "ENUM:Small,Medium,Large,Enterprise",
    v11.error_message = "Invalid company size classification",
    v11.business_criticality = "medium",
    v11.priority = "P2",
    v11.applicable_domains = ["ALL"],
    v11.is_active = true,
    v11.created_date = datetime(),
    v11.last_updated_date = datetime()
WITH v11
MATCH (c:Column {column_id: "COL_COMPANY_SIZE_156"})
MERGE (c)-[:HAS_VALIDATION]->(v11);

// ANNUAL_REVENUE - RANGE
MERGE (v12:ValidationRule {rule_id: "VAL_ANNUAL_REVENUE_001"})
SET v12.rule_name = "ANNUAL_REVENUE Range Check",
    v12.column_name = "ANNUAL_REVENUE",
    v12.table_id = "DIM_MANUFACTURER",
    v12.validation_type = "RANGE",
    v12.validation_rule = "RANGE:0,999999999999.99",
    v12.error_message = "Annual Revenue must be non-negative",
    v12.business_criticality = "high",
    v12.priority = "P1",
    v12.applicable_domains = ["ALL"],
    v12.is_active = true,
    v12.created_date = datetime(),
    v12.last_updated_date = datetime()
WITH v12
MATCH (c:Column {column_id: "COL_ANNUAL_REVENUE_157"})
MERGE (c)-[:HAS_VALIDATION]->(v12);

// EMPLOYEE_COUNT - RANGE
MERGE (v13:ValidationRule {rule_id: "VAL_EMPLOYEE_COUNT_001"})
SET v13.rule_name = "EMPLOYEE_COUNT Range Check",
    v13.column_name = "EMPLOYEE_COUNT",
    v13.table_id = "DIM_MANUFACTURER",
    v13.validation_type = "RANGE",
    v13.validation_rule = "RANGE:1,999999",
    v13.error_message = "Employee Count must be positive",
    v13.business_criticality = "medium",
    v13.priority = "P2",
    v13.applicable_domains = ["ALL"],
    v13.is_active = true,
    v13.created_date = datetime(),
    v13.last_updated_date = datetime()
WITH v13
MATCH (c:Column {column_id: "COL_EMPLOYEE_COUNT_158"})
MERGE (c)-[:HAS_VALIDATION]->(v13);

// YEAR_ESTABLISHED - RANGE
MERGE (v14:ValidationRule {rule_id: "VAL_YEAR_ESTABLISHED_001"})
SET v14.rule_name = "YEAR_ESTABLISHED Range Check",
    v14.column_name = "YEAR_ESTABLISHED",
    v14.table_id = "DIM_MANUFACTURER",
    v14.validation_type = "RANGE",
    v14.validation_rule = "RANGE:1800,2025",
    v14.error_message = "Year Established must be between 1800 and current year",
    v14.business_criticality = "low",
    v14.priority = "P3",
    v14.applicable_domains = ["ALL"],
    v14.is_active = true,
    v14.created_date = datetime(),
    v14.last_updated_date = datetime()
WITH v14
MATCH (c:Column {column_id: "COL_YEAR_ESTABLISHED_159"})
MERGE (c)-[:HAS_VALIDATION]->(v14);

// OWNERSHIP_TYPE - ENUM
MERGE (v15:ValidationRule {rule_id: "VAL_OWNERSHIP_TYPE_001"})
SET v15.rule_name = "OWNERSHIP_TYPE Domain Check",
    v15.column_name = "OWNERSHIP_TYPE",
    v15.table_id = "DIM_MANUFACTURER",
    v15.validation_type = "ENUM",
    v15.validation_rule = "ENUM:Public,Private,Family-Owned,Private Equity,Cooperative,Government",
    v15.error_message = "Invalid ownership type",
    v15.business_criticality = "medium",
    v15.priority = "P2",
    v15.applicable_domains = ["ALL"],
    v15.is_active = true,
    v15.created_date = datetime(),
    v15.last_updated_date = datetime()
WITH v15
MATCH (c:Column {column_id: "COL_OWNERSHIP_TYPE_160"})
MERGE (c)-[:HAS_VALIDATION]->(v15);

// ========================================
// Geographic Validations
// ========================================

// HEADQUARTERS_ADDRESS - NOT NULL
MERGE (v16:ValidationRule {rule_id: "VAL_HQ_ADDRESS_001"})
SET v16.rule_name = "HEADQUARTERS_ADDRESS Not Null Check",
    v16.column_name = "HEADQUARTERS_ADDRESS",
    v16.table_id = "DIM_MANUFACTURER",
    v16.validation_type = "NOT_NULL",
    v16.validation_rule = "NOT_NULL",
    v16.error_message = "Headquarters Address is required",
    v16.business_criticality = "high",
    v16.priority = "P1",
    v16.applicable_domains = ["ALL"],
    v16.is_active = true,
    v16.created_date = datetime(),
    v16.last_updated_date = datetime()
WITH v16
MATCH (c:Column {column_id: "COL_HEADQUARTERS_ADDRESS_161"})
MERGE (c)-[:HAS_VALIDATION]->(v16);

// HEADQUARTERS_COUNTRY - NOT NULL
MERGE (v17:ValidationRule {rule_id: "VAL_HQ_COUNTRY_001"})
SET v17.rule_name = "HEADQUARTERS_COUNTRY Not Null Check",
    v17.column_name = "HEADQUARTERS_COUNTRY",
    v17.table_id = "DIM_MANUFACTURER",
    v17.validation_type = "NOT_NULL",
    v17.validation_rule = "NOT_NULL",
    v17.error_message = "Headquarters Country is required",
    v17.business_criticality = "high",
    v17.priority = "P1",
    v17.applicable_domains = ["ALL"],
    v17.is_active = true,
    v17.created_date = datetime(),
    v17.last_updated_date = datetime()
WITH v17
MATCH (c:Column {column_id: "COL_HEADQUARTERS_COUNTRY_162"})
MERGE (c)-[:HAS_VALIDATION]->(v17);

// ========================================
// Regulatory and Compliance Validations (Domain-Specific)
// ========================================

// FDA_FACILITY_REGISTRATION - NOT NULL (Food/Pharma/Cosmetics)
MERGE (v18:ValidationRule {rule_id: "VAL_FDA_FACILITY_REG_001"})
SET v18.rule_name = "FDA_FACILITY_REGISTRATION Not Null Check",
    v18.column_name = "FDA_FACILITY_REGISTRATION",
    v18.table_id = "DIM_MANUFACTURER",
    v18.validation_type = "NOT_NULL",
    v18.validation_rule = "NOT_NULL",
    v18.error_message = "FDA Facility Registration is required for regulated products",
    v18.business_criticality = "critical",
    v18.priority = "P1",
    v18.applicable_domains = ["pharmaceuticals", "food_beverage", "cosmetics"],
    v18.is_active = true,
    v18.created_date = datetime(),
    v18.last_updated_date = datetime()
WITH v18
MATCH (c:Column {column_id: "COL_FDA_FACILITY_REG_165"})
MERGE (c)-[:HAS_VALIDATION]->(v18);

// FDA_FACILITY_REGISTRATION - FORMAT
MERGE (v19:ValidationRule {rule_id: "VAL_FDA_FACILITY_REG_002"})
SET v19.rule_name = "FDA_FACILITY_REGISTRATION Format Check",
    v19.column_name = "FDA_FACILITY_REGISTRATION",
    v19.table_id = "DIM_MANUFACTURER",
    v19.validation_type = "REGEX",
    v19.validation_rule = "^\\d{10,11}$",
    v19.error_message = "FDA Registration must be 10-11 digits",
    v19.business_criticality = "critical",
    v19.priority = "P1",
    v19.applicable_domains = ["pharmaceuticals", "food_beverage", "cosmetics"],
    v19.is_active = true,
    v19.created_date = datetime(),
    v19.last_updated_date = datetime()
WITH v19
MATCH (c:Column {column_id: "COL_FDA_FACILITY_REG_165"})
MERGE (c)-[:HAS_VALIDATION]->(v19);

// DEA_REGISTRATION - NOT NULL (Pharmaceuticals)
MERGE (v20:ValidationRule {rule_id: "VAL_DEA_REGISTRATION_001"})
SET v20.rule_name = "DEA_REGISTRATION Not Null Check",
    v20.column_name = "DEA_REGISTRATION",
    v20.table_id = "DIM_MANUFACTURER",
    v20.validation_type = "NOT_NULL",
    v20.validation_rule = "NOT_NULL",
    v20.error_message = "DEA Registration is required for controlled substances",
    v20.business_criticality = "critical",
    v20.priority = "P1",
    v20.applicable_domains = ["pharmaceuticals"],
    v20.is_active = true,
    v20.created_date = datetime(),
    v20.last_updated_date = datetime()
WITH v20
MATCH (c:Column {column_id: "COL_DEA_REGISTRATION_166"})
MERGE (c)-[:HAS_VALIDATION]->(v20);

// DEA_REGISTRATION - FORMAT
MERGE (v21:ValidationRule {rule_id: "VAL_DEA_REGISTRATION_002"})
SET v21.rule_name = "DEA_REGISTRATION Format Check",
    v21.column_name = "DEA_REGISTRATION",
    v21.table_id = "DIM_MANUFACTURER",
    v21.validation_type = "REGEX",
    v21.validation_rule = "^[A-Z]{2}\\d{7}$",
    v21.error_message = "DEA Registration must be 2 letters followed by 7 digits",
    v21.business_criticality = "critical",
    v21.priority = "P1",
    v21.applicable_domains = ["pharmaceuticals"],
    v21.is_active = true,
    v21.created_date = datetime(),
    v21.last_updated_date = datetime()
WITH v21
MATCH (c:Column {column_id: "COL_DEA_REGISTRATION_166"})
MERGE (c)-[:HAS_VALIDATION]->(v21);

// TTB_PERMIT_NUMBER - NOT NULL (Alcoholic Beverages)
MERGE (v22:ValidationRule {rule_id: "VAL_TTB_PERMIT_001"})
SET v22.rule_name = "TTB_PERMIT_NUMBER Not Null Check",
    v22.column_name = "TTB_PERMIT_NUMBER",
    v22.table_id = "DIM_MANUFACTURER",
    v22.validation_type = "NOT_NULL",
    v22.validation_rule = "NOT_NULL",
    v22.error_message = "TTB Permit is required for alcoholic beverages",
    v22.business_criticality = "critical",
    v22.priority = "P1",
    v22.applicable_domains = ["alcoholic_beverages"],
    v22.is_active = true,
    v22.created_date = datetime(),
    v22.last_updated_date = datetime()
WITH v22
MATCH (c:Column {column_id: "COL_TTB_PERMIT_NUMBER_167"})
MERGE (c)-[:HAS_VALIDATION]->(v22);

// TTB_PERMIT_NUMBER - FORMAT
MERGE (v23:ValidationRule {rule_id: "VAL_TTB_PERMIT_002"})
SET v23.rule_name = "TTB_PERMIT_NUMBER Format Check",
    v23.column_name = "TTB_PERMIT_NUMBER",
    v23.table_id = "DIM_MANUFACTURER",
    v23.validation_type = "REGEX",
    v23.validation_rule = "^[A-Z]{2}-[A-Z]{2}-\\d{5}$",
    v23.error_message = "TTB Permit must be format XX-XX-12345",
    v23.business_criticality = "critical",
    v23.priority = "P1",
    v23.applicable_domains = ["alcoholic_beverages"],
    v23.is_active = true,
    v23.created_date = datetime(),
    v23.last_updated_date = datetime()
WITH v23
MATCH (c:Column {column_id: "COL_TTB_PERMIT_NUMBER_167"})
MERGE (c)-[:HAS_VALIDATION]->(v23);

// ========================================
// Certification Boolean Validations
// ========================================

// GMP_CERTIFIED - BOOLEAN
MERGE (v24:ValidationRule {rule_id: "VAL_GMP_CERTIFIED_001"})
SET v24.rule_name = "GMP_CERTIFIED Boolean Check",
    v24.column_name = "GMP_CERTIFIED",
    v24.table_id = "DIM_MANUFACTURER",
    v24.validation_type = "BOOLEAN",
    v24.validation_rule = "BOOLEAN",
    v24.error_message = "GMP Certified must be TRUE or FALSE",
    v24.business_criticality = "critical",
    v24.priority = "P1",
    v24.applicable_domains = ["pharmaceuticals", "food_beverage", "cosmetics"],
    v24.is_active = true,
    v24.created_date = datetime(),
    v24.last_updated_date = datetime()
WITH v24
MATCH (c:Column {column_id: "COL_GMP_CERTIFIED_169"})
MERGE (c)-[:HAS_VALIDATION]->(v24);

// HACCP_CERTIFIED - BOOLEAN
MERGE (v25:ValidationRule {rule_id: "VAL_HACCP_CERTIFIED_001"})
SET v25.rule_name = "HACCP_CERTIFIED Boolean Check",
    v25.column_name = "HACCP_CERTIFIED",
    v25.table_id = "DIM_MANUFACTURER",
    v25.validation_type = "BOOLEAN",
    v25.validation_rule = "BOOLEAN",
    v25.error_message = "HACCP Certified must be TRUE or FALSE",
    v25.business_criticality = "critical",
    v25.priority = "P1",
    v25.applicable_domains = ["food_beverage"],
    v25.is_active = true,
    v25.created_date = datetime(),
    v25.last_updated_date = datetime()
WITH v25
MATCH (c:Column {column_id: "COL_HACCP_CERTIFIED_170"})
MERGE (c)-[:HAS_VALIDATION]->(v25);

// ORGANIC_CERTIFIED - BOOLEAN
MERGE (v26:ValidationRule {rule_id: "VAL_ORGANIC_CERTIFIED_001"})
SET v26.rule_name = "ORGANIC_CERTIFIED Boolean Check",
    v26.column_name = "ORGANIC_CERTIFIED",
    v26.table_id = "DIM_MANUFACTURER",
    v26.validation_type = "BOOLEAN",
    v26.validation_rule = "BOOLEAN",
    v26.error_message = "Organic Certified must be TRUE or FALSE",
    v26.business_criticality = "high",
    v26.priority = "P1",
    v26.applicable_domains = ["food_beverage", "cosmetics"],
    v26.is_active = true,
    v26.created_date = datetime(),
    v26.last_updated_date = datetime()
WITH v26
MATCH (c:Column {column_id: "COL_ORGANIC_CERTIFIED_171"})
MERGE (c)-[:HAS_VALIDATION]->(v26);

// ========================================
// Quality and Performance Metrics Validations
// ========================================

// QUALITY_SCORE - RANGE
MERGE (v27:ValidationRule {rule_id: "VAL_QUALITY_SCORE_001"})
SET v27.rule_name = "QUALITY_SCORE Range Check",
    v27.column_name = "QUALITY_SCORE",
    v27.table_id = "DIM_MANUFACTURER",
    v27.validation_type = "RANGE",
    v27.validation_rule = "RANGE:0,100",
    v27.error_message = "Quality Score must be between 0 and 100",
    v27.business_criticality = "high",
    v27.priority = "P1",
    v27.applicable_domains = ["ALL"],
    v27.is_active = true,
    v27.created_date = datetime(),
    v27.last_updated_date = datetime()
WITH v27
MATCH (c:Column {column_id: "COL_QUALITY_SCORE_172"})
MERGE (c)-[:HAS_VALIDATION]->(v27);

// ON_TIME_DELIVERY_RATE - RANGE
MERGE (v28:ValidationRule {rule_id: "VAL_ON_TIME_DELIVERY_001"})
SET v28.rule_name = "ON_TIME_DELIVERY_RATE Range Check",
    v28.column_name = "ON_TIME_DELIVERY_RATE",
    v28.table_id = "DIM_MANUFACTURER",
    v28.validation_type = "RANGE",
    v28.validation_rule = "RANGE:0,100",
    v28.error_message = "On-Time Delivery Rate must be between 0 and 100",
    v28.business_criticality = "high",
    v28.priority = "P1",
    v28.applicable_domains = ["ALL"],
    v28.is_active = true,
    v28.created_date = datetime(),
    v28.last_updated_date = datetime()
WITH v28
MATCH (c:Column {column_id: "COL_ON_TIME_DELIVERY_173"})
MERGE (c)-[:HAS_VALIDATION]->(v28);

// DEFECT_RATE_PPM - RANGE
MERGE (v29:ValidationRule {rule_id: "VAL_DEFECT_RATE_PPM_001"})
SET v29.rule_name = "DEFECT_RATE_PPM Range Check",
    v29.column_name = "DEFECT_RATE_PPM",
    v29.table_id = "DIM_MANUFACTURER",
    v29.validation_type = "RANGE",
    v29.validation_rule = "RANGE:0,1000000",
    v29.error_message = "Defect Rate PPM must be between 0 and 1000000",
    v29.business_criticality = "high",
    v29.priority = "P1",
    v29.applicable_domains = ["ALL"],
    v29.is_active = true,
    v29.created_date = datetime(),
    v29.last_updated_date = datetime()
WITH v29
MATCH (c:Column {column_id: "COL_DEFECT_RATE_PPM_174"})
MERGE (c)-[:HAS_VALIDATION]->(v29);

// LAST_AUDIT_SCORE - RANGE
MERGE (v30:ValidationRule {rule_id: "VAL_LAST_AUDIT_SCORE_001"})
SET v30.rule_name = "LAST_AUDIT_SCORE Range Check",
    v30.column_name = "LAST_AUDIT_SCORE",
    v30.table_id = "DIM_MANUFACTURER",
    v30.validation_type = "RANGE",
    v30.validation_rule = "RANGE:0,100",
    v30.error_message = "Last Audit Score must be between 0 and 100",
    v30.business_criticality = "high",
    v30.priority = "P1",
    v30.applicable_domains = ["ALL"],
    v30.is_active = true,
    v30.created_date = datetime(),
    v30.last_updated_date = datetime()
WITH v30
MATCH (c:Column {column_id: "COL_LAST_AUDIT_SCORE_176"})
MERGE (c)-[:HAS_VALIDATION]->(v30);

// RECALL_COUNT_5YR - RANGE
MERGE (v31:ValidationRule {rule_id: "VAL_RECALL_COUNT_5YR_001"})
SET v31.rule_name = "RECALL_COUNT_5YR Range Check",
    v31.column_name = "RECALL_COUNT_5YR",
    v31.table_id = "DIM_MANUFACTURER",
    v31.validation_type = "RANGE",
    v31.validation_rule = "RANGE:0,100",
    v31.error_message = "5-Year Recall Count must be reasonable (0-100)",
    v31.business_criticality = "high",
    v31.priority = "P1",
    v31.applicable_domains = ["ALL"],
    v31.is_active = true,
    v31.created_date = datetime(),
    v31.last_updated_date = datetime()
WITH v31
MATCH (c:Column {column_id: "COL_RECALL_COUNT_5YR_177"})
MERGE (c)-[:HAS_VALIDATION]->(v31);

// ========================================
// Financial and Commercial Terms Validations
// ========================================

// PAYMENT_TERMS - NOT NULL
MERGE (v32:ValidationRule {rule_id: "VAL_PAYMENT_TERMS_001"})
SET v32.rule_name = "PAYMENT_TERMS Not Null Check",
    v32.column_name = "PAYMENT_TERMS",
    v32.table_id = "DIM_MANUFACTURER",
    v32.validation_type = "NOT_NULL",
    v32.validation_rule = "NOT_NULL",
    v32.error_message = "Payment Terms are required",
    v32.business_criticality = "high",
    v32.priority = "P1",
    v32.applicable_domains = ["ALL"],
    v32.is_active = true,
    v32.created_date = datetime(),
    v32.last_updated_date = datetime()
WITH v32
MATCH (c:Column {column_id: "COL_PAYMENT_TERMS_178"})
MERGE (c)-[:HAS_VALIDATION]->(v32);

// PAYMENT_TERMS - ENUM
MERGE (v33:ValidationRule {rule_id: "VAL_PAYMENT_TERMS_002"})
SET v33.rule_name = "PAYMENT_TERMS Domain Check",
    v33.column_name = "PAYMENT_TERMS",
    v33.table_id = "DIM_MANUFACTURER",
    v33.validation_type = "ENUM",
    v33.validation_rule = "ENUM:Net 30,Net 45,Net 60,2/10 Net 30,Due on Receipt,COD,Prepaid",
    v33.error_message = "Invalid payment terms",
    v33.business_criticality = "high",
    v33.priority = "P1",
    v33.applicable_domains = ["ALL"],
    v33.is_active = true,
    v33.created_date = datetime(),
    v33.last_updated_date = datetime()
WITH v33
MATCH (c:Column {column_id: "COL_PAYMENT_TERMS_178"})
MERGE (c)-[:HAS_VALIDATION]->(v33);

// CREDIT_LIMIT - RANGE
MERGE (v34:ValidationRule {rule_id: "VAL_CREDIT_LIMIT_001"})
SET v34.rule_name = "CREDIT_LIMIT Range Check",
    v34.column_name = "CREDIT_LIMIT",
    v34.table_id = "DIM_MANUFACTURER",
    v34.validation_type = "RANGE",
    v34.validation_rule = "RANGE:0,999999999.99",
    v34.error_message = "Credit Limit must be non-negative",
    v34.business_criticality = "medium",
    v34.priority = "P2",
    v34.applicable_domains = ["ALL"],
    v34.is_active = true,
    v34.created_date = datetime(),
    v34.last_updated_date = datetime()
WITH v34
MATCH (c:Column {column_id: "COL_CREDIT_LIMIT_179"})
MERGE (c)-[:HAS_VALIDATION]->(v34);

// CURRENCY_CODE - FORMAT
MERGE (v35:ValidationRule {rule_id: "VAL_CURRENCY_CODE_001"})
SET v35.rule_name = "CURRENCY_CODE Format Check",
    v35.column_name = "CURRENCY_CODE",
    v35.table_id = "DIM_MANUFACTURER",
    v35.validation_type = "REGEX",
    v35.validation_rule = "^[A-Z]{3}$",
    v35.error_message = "Currency Code must be 3 uppercase letters (ISO 4217)",
    v35.business_criticality = "high",
    v35.priority = "P1",
    v35.applicable_domains = ["ALL"],
    v35.is_active = true,
    v35.created_date = datetime(),
    v35.last_updated_date = datetime()
WITH v35
MATCH (c:Column {column_id: "COL_CURRENCY_CODE_181"})
MERGE (c)-[:HAS_VALIDATION]->(v35);

// INCOTERMS - ENUM
MERGE (v36:ValidationRule {rule_id: "VAL_INCOTERMS_001"})
SET v36.rule_name = "INCOTERMS Domain Check",
    v36.column_name = "INCOTERMS",
    v36.table_id = "DIM_MANUFACTURER",
    v36.validation_type = "ENUM",
    v36.validation_rule = "ENUM:EXW,FOB,CIF,DDP,DAP,CPT,CIP,FCA,FAS,CFR,DAT",
    v36.error_message = "Invalid Incoterms code",
    v36.business_criticality = "high",
    v36.priority = "P1",
    v36.applicable_domains = ["ALL"],
    v36.is_active = true,
    v36.created_date = datetime(),
    v36.last_updated_date = datetime()
WITH v36
MATCH (c:Column {column_id: "COL_INCOTERMS_182"})
MERGE (c)-[:HAS_VALIDATION]->(v36);

// ========================================
// Supply Chain Capabilities Validations
// ========================================

// LEAD_TIME_STANDARD - RANGE
MERGE (v37:ValidationRule {rule_id: "VAL_LEAD_TIME_STANDARD_001"})
SET v37.rule_name = "LEAD_TIME_STANDARD Range Check",
    v37.column_name = "LEAD_TIME_STANDARD",
    v37.table_id = "DIM_MANUFACTURER",
    v37.validation_type = "RANGE",
    v37.validation_rule = "RANGE:1,365",
    v37.error_message = "Standard Lead Time must be between 1 and 365 days",
    v37.business_criticality = "high",
    v37.priority = "P1",
    v37.applicable_domains = ["ALL"],
    v37.is_active = true,
    v37.created_date = datetime(),
    v37.last_updated_date = datetime()
WITH v37
MATCH (c:Column {column_id: "COL_LEAD_TIME_STANDARD_185"})
MERGE (c)-[:HAS_VALIDATION]->(v37);

// CAPACITY_UTILIZATION - RANGE
MERGE (v38:ValidationRule {rule_id: "VAL_CAPACITY_UTILIZATION_001"})
SET v38.rule_name = "CAPACITY_UTILIZATION Range Check",
    v38.column_name = "CAPACITY_UTILIZATION",
    v38.table_id = "DIM_MANUFACTURER",
    v38.validation_type = "RANGE",
    v38.validation_rule = "RANGE:0,100",
    v38.error_message = "Capacity Utilization must be between 0 and 100",
    v38.business_criticality = "medium",
    v38.priority = "P2",
    v38.applicable_domains = ["ALL"],
    v38.is_active = true,
    v38.created_date = datetime(),
    v38.last_updated_date = datetime()
WITH v38
MATCH (c:Column {column_id: "COL_CAPACITY_UTILIZATION_184"})
MERGE (c)-[:HAS_VALIDATION]->(v38);

// MINIMUM_ORDER_VALUE - RANGE
MERGE (v39:ValidationRule {rule_id: "VAL_MINIMUM_ORDER_VALUE_001"})
SET v39.rule_name = "MINIMUM_ORDER_VALUE Range Check",
    v39.column_name = "MINIMUM_ORDER_VALUE",
    v39.table_id = "DIM_MANUFACTURER",
    v39.validation_type = "RANGE",
    v39.validation_rule = "RANGE:0,999999999.99",
    v39.error_message = "Minimum Order Value must be non-negative",
    v39.business_criticality = "medium",
    v39.priority = "P2",
    v39.applicable_domains = ["ALL"],
    v39.is_active = true,
    v39.created_date = datetime(),
    v39.last_updated_date = datetime()
WITH v39
MATCH (c:Column {column_id: "COL_MINIMUM_ORDER_VALUE_186"})
MERGE (c)-[:HAS_VALIDATION]->(v39);

// ========================================
// Technology Capability Boolean Validations
// ========================================

// EDI_CAPABLE - BOOLEAN
MERGE (v40:ValidationRule {rule_id: "VAL_EDI_CAPABLE_001"})
SET v40.rule_name = "EDI_CAPABLE Boolean Check",
    v40.column_name = "EDI_CAPABLE",
    v40.table_id = "DIM_MANUFACTURER",
    v40.validation_type = "BOOLEAN",
    v40.validation_rule = "BOOLEAN",
    v40.error_message = "EDI Capable must be TRUE or FALSE",
    v40.business_criticality = "medium",
    v40.priority = "P2",
    v40.applicable_domains = ["ALL"],
    v40.is_active = true,
    v40.created_date = datetime(),
    v40.last_updated_date = datetime()
WITH v40
MATCH (c:Column {column_id: "COL_EDI_CAPABLE_188"})
MERGE (c)-[:HAS_VALIDATION]->(v40);

// API_INTEGRATION - BOOLEAN
MERGE (v41:ValidationRule {rule_id: "VAL_API_INTEGRATION_001"})
SET v41.rule_name = "API_INTEGRATION Boolean Check",
    v41.column_name = "API_INTEGRATION",
    v41.table_id = "DIM_MANUFACTURER",
    v41.validation_type = "BOOLEAN",
    v41.validation_rule = "BOOLEAN",
    v41.error_message = "API Integration must be TRUE or FALSE",
    v41.business_criticality = "medium",
    v41.priority = "P2",
    v41.applicable_domains = ["ALL"],
    v41.is_active = true,
    v41.created_date = datetime(),
    v41.last_updated_date = datetime()
WITH v41
MATCH (c:Column {column_id: "COL_API_INTEGRATION_189"})
MERGE (c)-[:HAS_VALIDATION]->(v41);

// ========================================
// Sustainability and ESG Validations
// ========================================

// SUSTAINABILITY_SCORE - RANGE
MERGE (v42:ValidationRule {rule_id: "VAL_SUSTAINABILITY_SCORE_001"})
SET v42.rule_name = "SUSTAINABILITY_SCORE Range Check",
    v42.column_name = "SUSTAINABILITY_SCORE",
    v42.table_id = "DIM_MANUFACTURER",
    v42.validation_type = "RANGE",
    v42.validation_rule = "RANGE:0,100",
    v42.error_message = "Sustainability Score must be between 0 and 100",
    v42.business_criticality = "medium",
    v42.priority = "P2",
    v42.applicable_domains = ["ALL"],
    v42.is_active = true,
    v42.created_date = datetime(),
    v42.last_updated_date = datetime()
WITH v42
MATCH (c:Column {column_id: "COL_SUSTAINABILITY_SCORE_192"})
MERGE (c)-[:HAS_VALIDATION]->(v42);

// RENEWABLE_ENERGY_PERCENT - RANGE
MERGE (v43:ValidationRule {rule_id: "VAL_RENEWABLE_ENERGY_PCT_001"})
SET v43.rule_name = "RENEWABLE_ENERGY_PERCENT Range Check",
    v43.column_name = "RENEWABLE_ENERGY_PERCENT",
    v43.table_id = "DIM_MANUFACTURER",
    v43.validation_type = "RANGE",
    v43.validation_rule = "RANGE:0,100",
    v43.error_message = "Renewable Energy Percentage must be between 0 and 100",
    v43.business_criticality = "medium",
    v43.priority = "P2",
    v43.applicable_domains = ["ALL"],
    v43.is_active = true,
    v43.created_date = datetime(),
    v43.last_updated_date = datetime()
WITH v43
MATCH (c:Column {column_id: "COL_RENEWABLE_ENERGY_PCT_194"})
MERGE (c)-[:HAS_VALIDATION]->(v43);

// WASTE_DIVERSION_RATE - RANGE
MERGE (v44:ValidationRule {rule_id: "VAL_WASTE_DIVERSION_RATE_001"})
SET v44.rule_name = "WASTE_DIVERSION_RATE Range Check",
    v44.column_name = "WASTE_DIVERSION_RATE",
    v44.table_id = "DIM_MANUFACTURER",
    v44.validation_type = "RANGE",
    v44.validation_rule = "RANGE:0,100",
    v44.error_message = "Waste Diversion Rate must be between 0 and 100",
    v44.business_criticality = "low",
    v44.priority = "P3",
    v44.applicable_domains = ["ALL"],
    v44.is_active = true,
    v44.created_date = datetime(),
    v44.last_updated_date = datetime()
WITH v44
MATCH (c:Column {column_id: "COL_WASTE_DIVERSION_RATE_195"})
MERGE (c)-[:HAS_VALIDATION]->(v44);

// ========================================
// Risk and Compliance Validations
// ========================================

// RISK_SCORE - RANGE
MERGE (v45:ValidationRule {rule_id: "VAL_RISK_SCORE_001"})
SET v45.rule_name = "RISK_SCORE Range Check",
    v45.column_name = "RISK_SCORE",
    v45.table_id = "DIM_MANUFACTURER",
    v45.validation_type = "RANGE",
    v45.validation_rule = "RANGE:0,100",
    v45.error_message = "Risk Score must be between 0 and 100",
    v45.business_criticality = "high",
    v45.priority = "P1",
    v45.applicable_domains = ["ALL"],
    v45.is_active = true,
    v45.created_date = datetime(),
    v45.last_updated_date = datetime()
WITH v45
MATCH (c:Column {column_id: "COL_RISK_SCORE_196"})
MERGE (c)-[:HAS_VALIDATION]->(v45);

// INSURANCE_COVERAGE - RANGE
MERGE (v46:ValidationRule {rule_id: "VAL_INSURANCE_COVERAGE_001"})
SET v46.rule_name = "INSURANCE_COVERAGE Range Check",
    v46.column_name = "INSURANCE_COVERAGE",
    v46.table_id = "DIM_MANUFACTURER",
    v46.validation_type = "RANGE",
    v46.validation_rule = "RANGE:0,999999999999.99",
    v46.error_message = "Insurance Coverage must be non-negative",
    v46.business_criticality = "high",
    v46.priority = "P1",
    v46.applicable_domains = ["ALL"],
    v46.is_active = true,
    v46.created_date = datetime(),
    v46.last_updated_date = datetime()
WITH v46
MATCH (c:Column {column_id: "COL_INSURANCE_COVERAGE_197"})
MERGE (c)-[:HAS_VALIDATION]->(v46);

// ========================================
// Contact Information Validations
// ========================================

// PRIMARY_CONTACT_EMAIL - FORMAT
MERGE (v47:ValidationRule {rule_id: "VAL_PRIMARY_CONTACT_EMAIL_001"})
SET v47.rule_name = "PRIMARY_CONTACT_EMAIL Format Check",
    v47.column_name = "PRIMARY_CONTACT_EMAIL",
    v47.table_id = "DIM_MANUFACTURER",
    v47.validation_type = "REGEX",
    v47.validation_rule = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$",
    v47.error_message = "Invalid email format",
    v47.business_criticality = "medium",
    v47.priority = "P2",
    v47.applicable_domains = ["ALL"],
    v47.is_active = true,
    v47.created_date = datetime(),
    v47.last_updated_date = datetime()
WITH v47
MATCH (c:Column {column_id: "COL_PRIMARY_CONTACT_EMAIL_205"})
MERGE (c)-[:HAS_VALIDATION]->(v47);

// PRIMARY_CONTACT_PHONE - FORMAT
MERGE (v48:ValidationRule {rule_id: "VAL_PRIMARY_CONTACT_PHONE_001"})
SET v48.rule_name = "PRIMARY_CONTACT_PHONE Format Check",
    v48.column_name = "PRIMARY_CONTACT_PHONE",
    v48.table_id = "DIM_MANUFACTURER",
    v48.validation_type = "REGEX",
    v48.validation_rule = "^\\+?[1-9]\\d{1,14}$",
    v48.error_message = "Invalid phone format (E.164 standard)",
    v48.business_criticality = "medium",
    v48.priority = "P2",
    v48.applicable_domains = ["ALL"],
    v48.is_active = true,
    v48.created_date = datetime(),
    v48.last_updated_date = datetime()
WITH v48
MATCH (c:Column {column_id: "COL_PRIMARY_CONTACT_PHONE_206"})
MERGE (c)-[:HAS_VALIDATION]->(v48);

// ========================================
// Status and Metadata Validations
// ========================================

// RECORD_STATUS - NOT NULL
MERGE (v49:ValidationRule {rule_id: "VAL_RECORD_STATUS_001"})
SET v49.rule_name = "RECORD_STATUS Not Null Check",
    v49.column_name = "RECORD_STATUS",
    v49.table_id = "DIM_MANUFACTURER",
    v49.validation_type = "NOT_NULL",
    v49.validation_rule = "NOT_NULL",
    v49.error_message = "Record Status is required",
    v49.business_criticality = "critical",
    v49.priority = "P1",
    v49.applicable_domains = ["ALL"],
    v49.is_active = true,
    v49.created_date = datetime(),
    v49.last_updated_date = datetime()
WITH v49
MATCH (c:Column {column_id: "COL_RECORD_STATUS_211"})
MERGE (c)-[:HAS_VALIDATION]->(v49);

// RECORD_STATUS - ENUM
MERGE (v50:ValidationRule {rule_id: "VAL_RECORD_STATUS_002"})
SET v50.rule_name = "RECORD_STATUS Domain Check",
    v50.column_name = "RECORD_STATUS",
    v50.table_id = "DIM_MANUFACTURER",
    v50.validation_type = "ENUM",
    v50.validation_rule = "ENUM:Active,Inactive,Suspended,Terminated,Pending",
    v50.error_message = "Invalid record status",
    v50.business_criticality = "critical",
    v50.priority = "P1",
    v50.applicable_domains = ["ALL"],
    v50.is_active = true,
    v50.created_date = datetime(),
    v50.last_updated_date = datetime()
WITH v50
MATCH (c:Column {column_id: "COL_RECORD_STATUS_211"})
MERGE (c)-[:HAS_VALIDATION]->(v50);

// ========================================
// Cross-Field Validations
// ========================================

// Company Size vs Employee Count Consistency
MERGE (v51:ValidationRule {rule_id: "VAL_SIZE_EMPLOYEE_CONSISTENCY_001"})
SET v51.rule_name = "Company Size vs Employee Count Check",
    v51.column_name = "COMPANY_SIZE,EMPLOYEE_COUNT",
    v51.table_id = "DIM_MANUFACTURER",
    v51.validation_type = "CUSTOM",
    v51.validation_rule = "CUSTOM:IF(COMPANY_SIZE='Small',EMPLOYEE_COUNT<50) AND IF(COMPANY_SIZE='Medium',EMPLOYEE_COUNT BETWEEN 50 AND 250) AND IF(COMPANY_SIZE='Large',EMPLOYEE_COUNT>250)",
    v51.error_message = "Company size inconsistent with employee count",
    v51.business_criticality = "medium",
    v51.priority = "P2",
    v51.applicable_domains = ["ALL"],
    v51.is_active = true,
    v51.created_date = datetime(),
    v51.last_updated_date = datetime()
WITH v51
MATCH (t:Table {table_id: "DIM_MANUFACTURER"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v51);

// Audit Date vs Audit Score
MERGE (v52:ValidationRule {rule_id: "VAL_AUDIT_CONSISTENCY_001"})
SET v52.rule_name = "Audit Date vs Score Consistency Check",
    v52.column_name = "LAST_AUDIT_DATE,LAST_AUDIT_SCORE",
    v52.table_id = "DIM_MANUFACTURER",
    v52.validation_type = "CUSTOM",
    v52.validation_rule = "CUSTOM:IF(LAST_AUDIT_DATE!=NULL,LAST_AUDIT_SCORE!=NULL)",
    v52.error_message = "Audit date exists but score is missing",
    v52.business_criticality = "high",
    v52.priority = "P1",
    v52.applicable_domains = ["ALL"],
    v52.is_active = true,
    v52.created_date = datetime(),
    v52.last_updated_date = datetime()
WITH v52
MATCH (t:Table {table_id: "DIM_MANUFACTURER"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v52);

// Strategic Supplier vs Preferred Supplier
MERGE (v53:ValidationRule {rule_id: "VAL_SUPPLIER_HIERARCHY_001"})
SET v53.rule_name = "Strategic vs Preferred Supplier Check",
    v53.column_name = "STRATEGIC_SUPPLIER,PREFERRED_SUPPLIER",
    v53.table_id = "DIM_MANUFACTURER",
    v53.validation_type = "CUSTOM",
    v53.validation_rule = "CUSTOM:IF(STRATEGIC_SUPPLIER=TRUE,PREFERRED_SUPPLIER=TRUE)",
    v53.error_message = "Strategic suppliers must also be preferred suppliers",
    v53.business_criticality = "high",
    v53.priority = "P2",
    v53.applicable_domains = ["ALL"],
    v53.is_active = true,
    v53.created_date = datetime(),
    v53.last_updated_date = datetime()
WITH v53
MATCH (t:Table {table_id: "DIM_MANUFACTURER"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v53);

// FDA Registration Required for GMP
MERGE (v54:ValidationRule {rule_id: "VAL_FDA_GMP_CONSISTENCY_001"})
SET v54.rule_name = "FDA Registration vs GMP Consistency Check",
    v54.column_name = "FDA_FACILITY_REGISTRATION,GMP_CERTIFIED",
    v54.table_id = "DIM_MANUFACTURER",
    v54.validation_type = "CUSTOM",
    v54.validation_rule = "CUSTOM:IF(GMP_CERTIFIED=TRUE,FDA_FACILITY_REGISTRATION!=NULL)",
    v54.error_message = "GMP certified facilities must have FDA registration",
    v54.business_criticality = "critical",
    v54.priority = "P1",
    v54.applicable_domains = ["pharmaceuticals", "food_beverage", "cosmetics"],
    v54.is_active = true,
    v54.created_date = datetime(),
    v54.last_updated_date = datetime()
WITH v54
MATCH (t:Table {table_id: "DIM_MANUFACTURER"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v54);

// Capacity vs Minimum Order Value
MERGE (v55:ValidationRule {rule_id: "VAL_CAPACITY_MOV_001"})
SET v55.rule_name = "Production Capacity vs MOV Check",
    v55.column_name = "PRODUCTION_CAPACITY,MINIMUM_ORDER_VALUE",
    v55.table_id = "DIM_MANUFACTURER",
    v55.validation_type = "CUSTOM",
    v55.validation_rule = "CUSTOM:MINIMUM_ORDER_VALUE<=(PRODUCTION_CAPACITY*0.05)",
    v55.error_message = "Minimum order value seems too high for production capacity",
    v55.business_criticality = "medium",
    v55.priority = "P3",
    v55.applicable_domains = ["ALL"],
    v55.is_active = true,
    v55.created_date = datetime(),
    v55.last_updated_date = datetime()
WITH v55
MATCH (t:Table {table_id: "DIM_MANUFACTURER"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v55);

// Risk Score vs Insurance Coverage
MERGE (v56:ValidationRule {rule_id: "VAL_RISK_INSURANCE_001"})
SET v56.rule_name = "Risk Score vs Insurance Coverage Check",
    v56.column_name = "RISK_SCORE,INSURANCE_COVERAGE",
    v56.table_id = "DIM_MANUFACTURER",
    v56.validation_type = "CUSTOM",
    v56.validation_rule = "CUSTOM:IF(RISK_SCORE>75,INSURANCE_COVERAGE>=10000000)",
    v56.error_message = "High risk suppliers need minimum $10M insurance coverage",
    v56.business_criticality = "high",
    v56.priority = "P1",
    v56.applicable_domains = ["ALL"],
    v56.is_active = true,
    v56.created_date = datetime(),
    v56.last_updated_date = datetime()
WITH v56
MATCH (t:Table {table_id: "DIM_MANUFACTURER"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v56);

// Contract Expiry vs Record Status
MERGE (v57:ValidationRule {rule_id: "VAL_CONTRACT_STATUS_001"})
SET v57.rule_name = "Contract Expiry vs Record Status Check",
    v57.column_name = "CONTRACT_EXPIRY_DATE,RECORD_STATUS",
    v57.table_id = "DIM_MANUFACTURER",
    v57.validation_type = "CUSTOM",
    v57.validation_rule = "CUSTOM:IF(CONTRACT_EXPIRY_DATE<TODAY AND RECORD_STATUS='Active',WARNING)",
    v57.error_message = "Active supplier has expired contract",
    v57.business_criticality = "high",
    v57.priority = "P1",
    v57.applicable_domains = ["ALL"],
    v57.is_active = true,
    v57.created_date = datetime(),
    v57.last_updated_date = datetime()
WITH v57
MATCH (t:Table {table_id: "DIM_MANUFACTURER"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v57);

// Sustainability Score Components
MERGE (v58:ValidationRule {rule_id: "VAL_SUSTAINABILITY_COMPONENTS_001"})
SET v58.rule_name = "Sustainability Score Components Check",
    v58.column_name = "SUSTAINABILITY_SCORE,RENEWABLE_ENERGY_PERCENT,WASTE_DIVERSION_RATE",
    v58.table_id = "DIM_MANUFACTURER",
    v58.validation_type = "CUSTOM",
    v58.validation_rule = "CUSTOM:IF(SUSTAINABILITY_SCORE>80,(RENEWABLE_ENERGY_PERCENT>50 AND WASTE_DIVERSION_RATE>75))",
    v58.error_message = "High sustainability score requires strong component metrics",
    v58.business_criticality = "medium",
    v58.priority = "P3",
    v58.applicable_domains = ["ALL"],
    v58.is_active = true,
    v58.created_date = datetime(),
    v58.last_updated_date = datetime()
WITH v58
MATCH (t:Table {table_id: "DIM_MANUFACTURER"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v58);

// ========================================
// QUERY TO VERIFY CREATED VALIDATIONS
// ========================================

// Count validations by type
MATCH (v:ValidationRule {table_id: "DIM_MANUFACTURER"})
RETURN v.validation_type as Type, count(*) as Count
ORDER BY Count DESC;

// Count validations by priority
MATCH (v:ValidationRule {table_id: "DIM_MANUFACTURER"})
RETURN v.priority as Priority, count(*) as Count
ORDER BY Priority;

// Count validations by domain
MATCH (v:ValidationRule {table_id: "DIM_MANUFACTURER"})
UNWIND v.applicable_domains as domain
RETURN domain as Domain, count(DISTINCT v) as ValidationCount
ORDER BY ValidationCount DESC;