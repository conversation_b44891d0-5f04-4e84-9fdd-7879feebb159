rule_id,rule_name,column_name,table_id,validation_type,validation_rule,error_message,business_criticality,priority,applicable_domains,is_active
VAL_DATE_KEY_001,DATE_KEY Not Null Check,DATE_KEY,DIM_DATE,NOT_NULL,NOT_NULL,Date Key is required,critical,P1,ALL,true
VAL_DATE_KEY_002,DATE_KEY Uniqueness Check,DATE_KEY,DIM_DATE,UNIQUE,UNIQUE,Date Key must be unique,critical,P1,ALL,true
VAL_DATE_KEY_003,DATE_KEY Format Check,DATE_KEY,DIM_DATE,REGEX,^[0-9]{8}$,Date Key must be in YYYYMMDD format (8 digits),critical,P1,ALL,true
VAL_FULL_DATE_001,FULL_DATE Not Null Check,FULL_DATE,DIM_DATE,NOT_NULL,NOT_NULL,Full Date is required,critical,P1,ALL,true
VAL_FULL_DATE_002,FULL_DATE Uniqueness Check,FULL_DATE,DIM_DATE,UNIQUE,UNIQUE,Full Date must be unique,critical,P1,ALL,true
VAL_YEAR_001,YEAR Not Null Check,YEAR,DIM_DATE,NOT_NULL,NOT_NULL,Year is required,high,P1,ALL,true
VAL_YEAR_002,YEAR Range Check,YEAR,DIM_DATE,RANGE,RANGE:1900\,2100,Year must be between 1900 and 2100,high,P1,ALL,true
VAL_QUARTER_001,QUARTER Not Null Check,QUARTER,DIM_DATE,NOT_NULL,NOT_NULL,Quarter is required,high,P1,ALL,true
VAL_QUARTER_002,QUARTER Range Check,QUARTER,DIM_DATE,RANGE,RANGE:1\,4,Quarter must be between 1 and 4,high,P1,ALL,true
VAL_QUARTER_NAME_001,QUARTER_NAME Domain Check,QUARTER_NAME,DIM_DATE,ENUM,ENUM:Q1\,Q2\,Q3\,Q4,Quarter name must be Q1\, Q2\, Q3\, or Q4,medium,P2,ALL,true
VAL_MONTH_001,MONTH Not Null Check,MONTH,DIM_DATE,NOT_NULL,NOT_NULL,Month is required,high,P1,ALL,true
VAL_MONTH_002,MONTH Range Check,MONTH,DIM_DATE,RANGE,RANGE:1\,12,Month must be between 1 and 12,high,P1,ALL,true
VAL_MONTH_NAME_001,MONTH_NAME Domain Check,MONTH_NAME,DIM_DATE,ENUM,ENUM:January\,February\,March\,April\,May\,June\,July\,August\,September\,October\,November\,December,Invalid month name,medium,P2,ALL,true
VAL_MONTH_NAME_SHORT_001,MONTH_NAME_SHORT Domain Check,MONTH_NAME_SHORT,DIM_DATE,ENUM,ENUM:Jan\,Feb\,Mar\,Apr\,May\,Jun\,Jul\,Aug\,Sep\,Oct\,Nov\,Dec,Invalid month abbreviation,low,P3,ALL,true
VAL_WEEK_001,WEEK Range Check,WEEK,DIM_DATE,RANGE,RANGE:1\,53,Week must be between 1 and 53,medium,P2,ALL,true
VAL_ISO_WEEK_001,ISO_WEEK Range Check,ISO_WEEK,DIM_DATE,RANGE,RANGE:1\,53,ISO Week must be between 1 and 53,medium,P2,ALL,true
VAL_DAY_001,DAY Not Null Check,DAY,DIM_DATE,NOT_NULL,NOT_NULL,Day is required,medium,P2,ALL,true
VAL_DAY_002,DAY Range Check,DAY,DIM_DATE,RANGE,RANGE:1\,31,Day must be between 1 and 31,medium,P2,ALL,true
VAL_DAY_OF_WEEK_001,DAY_OF_WEEK Not Null Check,DAY_OF_WEEK,DIM_DATE,NOT_NULL,NOT_NULL,Day of week is required,high,P1,ALL,true
VAL_DAY_OF_WEEK_002,DAY_OF_WEEK Range Check,DAY_OF_WEEK,DIM_DATE,RANGE,RANGE:1\,7,Day of week must be between 1 and 7,high,P1,ALL,true
VAL_DAY_NAME_001,DAY_NAME Domain Check,DAY_NAME,DIM_DATE,ENUM,ENUM:Sunday\,Monday\,Tuesday\,Wednesday\,Thursday\,Friday\,Saturday,Invalid day name,medium,P2,ALL,true
VAL_DAY_NAME_SHORT_001,DAY_NAME_SHORT Domain Check,DAY_NAME_SHORT,DIM_DATE,ENUM,ENUM:Sun\,Mon\,Tue\,Wed\,Thu\,Fri\,Sat,Invalid day abbreviation,low,P3,ALL,true
VAL_DAY_OF_YEAR_001,DAY_OF_YEAR Range Check,DAY_OF_YEAR,DIM_DATE,RANGE,RANGE:1\,366,Day of year must be between 1 and 366,low,P3,ALL,true
VAL_IS_WEEKEND_001,IS_WEEKEND Not Null Check,IS_WEEKEND,DIM_DATE,NOT_NULL,NOT_NULL,Weekend flag is required,high,P1,ALL,true
VAL_IS_WEEKDAY_001,IS_WEEKDAY Not Null Check,IS_WEEKDAY,DIM_DATE,NOT_NULL,NOT_NULL,Weekday flag is required,medium,P2,ALL,true
VAL_IS_HOLIDAY_001,IS_HOLIDAY Not Null Check,IS_HOLIDAY,DIM_DATE,NOT_NULL,NOT_NULL,Holiday flag is required,high,P1,ALL,true
VAL_IS_LEAP_YEAR_001,IS_LEAP_YEAR Not Null Check,IS_LEAP_YEAR,DIM_DATE,NOT_NULL,NOT_NULL,Leap year flag is required,low,P3,ALL,true
VAL_SEASON_001,SEASON Not Null Check,SEASON,DIM_DATE,NOT_NULL,NOT_NULL,Season is required,high,P1,ALL,true
VAL_SEASON_002,SEASON Domain Check,SEASON,DIM_DATE,ENUM,ENUM:Spring\,Summer\,Fall\,Winter,Invalid season name,high,P1,ALL,true
VAL_FISCAL_YEAR_001,FISCAL_YEAR Not Null Check,FISCAL_YEAR,DIM_DATE,NOT_NULL,NOT_NULL,Fiscal year is required,high,P1,ALL,true
VAL_FISCAL_YEAR_002,FISCAL_YEAR Range Check,FISCAL_YEAR,DIM_DATE,RANGE,RANGE:1900\,2100,Fiscal year must be between 1900 and 2100,high,P1,ALL,true
VAL_FISCAL_QUARTER_001,FISCAL_QUARTER Not Null Check,FISCAL_QUARTER,DIM_DATE,NOT_NULL,NOT_NULL,Fiscal quarter is required,high,P1,ALL,true
VAL_FISCAL_QUARTER_002,FISCAL_QUARTER Range Check,FISCAL_QUARTER,DIM_DATE,RANGE,RANGE:1\,4,Fiscal quarter must be between 1 and 4,high,P1,ALL,true
VAL_FISCAL_MONTH_001,FISCAL_MONTH Range Check,FISCAL_MONTH,DIM_DATE,RANGE,RANGE:1\,12,Fiscal month must be between 1 and 12,medium,P2,ALL,true
VAL_FISCAL_WEEK_001,FISCAL_WEEK Range Check,FISCAL_WEEK,DIM_DATE,RANGE,RANGE:1\,53,Fiscal week must be between 1 and 53,medium,P2,ALL,true
VAL_RETAIL_YEAR_001,RETAIL_YEAR Not Null Check,RETAIL_YEAR,DIM_DATE,NOT_NULL,NOT_NULL,Retail year is required,high,P1,ALL,true
VAL_RETAIL_QUARTER_001,RETAIL_QUARTER Range Check,RETAIL_QUARTER,DIM_DATE,RANGE,RANGE:1\,4,Retail quarter must be between 1 and 4,high,P1,ALL,true
VAL_RETAIL_MONTH_001,RETAIL_MONTH Range Check,RETAIL_MONTH,DIM_DATE,RANGE,RANGE:1\,12,Retail month must be between 1 and 12,medium,P2,ALL,true
VAL_RETAIL_WEEK_001,RETAIL_WEEK Range Check,RETAIL_WEEK,DIM_DATE,RANGE,RANGE:1\,53,Retail week must be between 1 and 53,medium,P2,ALL,true
VAL_IS_MONTH_END_001,IS_MONTH_END Not Null Check,IS_MONTH_END,DIM_DATE,NOT_NULL,NOT_NULL,Month end flag is required,medium,P2,ALL,true
VAL_IS_QUARTER_END_001,IS_QUARTER_END Not Null Check,IS_QUARTER_END,DIM_DATE,NOT_NULL,NOT_NULL,Quarter end flag is required,high,P1,ALL,true
VAL_IS_YEAR_END_001,IS_YEAR_END Not Null Check,IS_YEAR_END,DIM_DATE,NOT_NULL,NOT_NULL,Year end flag is required,high,P1,ALL,true
VAL_DATE_ISO_FORMAT_001,DATE_ISO_FORMAT Format Check,DATE_ISO_FORMAT,DIM_DATE,REGEX,^[0-9]{4}-[0-9]{2}-[0-9]{2}$,Date ISO format must be YYYY-MM-DD,low,P3,ALL,true
VAL_DATE_US_FORMAT_001,DATE_US_FORMAT Format Check,DATE_US_FORMAT,DIM_DATE,REGEX,^[0-9]{2}/[0-9]{2}/[0-9]{4}$,Date US format must be MM/DD/YYYY,low,P3,ALL,true
VAL_WEEKEND_WEEKDAY_001,Weekend/Weekday Consistency Check,IS_WEEKEND\,IS_WEEKDAY,DIM_DATE,CUSTOM,CUSTOM:IS_WEEKEND XOR IS_WEEKDAY,A date must be either weekend or weekday\, not both,high,P1,ALL,true
VAL_DATE_KEY_CONSISTENCY_001,Date Key Consistency Check,DATE_KEY\,FULL_DATE,DIM_DATE,CUSTOM,CUSTOM:DATE_KEY=TO_INTEGER(FORMAT(FULL_DATE\,'YYYYMMDD')),Date key must match full date in YYYYMMDD format,critical,P1,ALL,true
VAL_MONTH_QUARTER_001,Month-Quarter Consistency Check,MONTH\,QUARTER,DIM_DATE,CUSTOM,CUSTOM:QUARTER=CEILING(MONTH/3),Quarter must correspond correctly to month,high,P1,ALL,true
VAL_DAY_OF_WEEK_NAME_001,Day of Week Name Consistency Check,DAY_OF_WEEK\,DAY_NAME,DIM_DATE,CUSTOM,CUSTOM:DAY_NAME=MAP(DAY_OF_WEEK),Day name must match day of week number,medium,P2,ALL,true
VAL_LEAP_YEAR_001,Leap Year Consistency Check,IS_LEAP_YEAR\,YEAR,DIM_DATE,CUSTOM,CUSTOM:IS_LEAP_YEAR=((YEAR%4=0 AND YEAR%100<>0) OR YEAR%400=0),Leap year flag must be correct for the given year,low,P3,ALL,true
VAL_PREVIOUS_DAY_001,Previous Day Date Check,PREVIOUS_DAY_DATE\,FULL_DATE,DIM_DATE,DATE_COMPARISON,DATE_COMPARE:PREVIOUS_DAY_DATE=FULL_DATE-1,Previous day date must be exactly one day before,low,P3,ALL,true
VAL_NEXT_DAY_001,Next Day Date Check,NEXT_DAY_DATE\,FULL_DATE,DIM_DATE,DATE_COMPARISON,DATE_COMPARE:NEXT_DAY_DATE=FULL_DATE+1,Next day date must be exactly one day after,low,P3,ALL,true
VAL_SAME_DAY_LAST_YEAR_001,Same Day Last Year Check,SAME_DAY_LAST_YEAR\,FULL_DATE,DIM_DATE,DATE_COMPARISON,DATE_COMPARE:SAME_DAY_LAST_YEAR=DATE_SUB(FULL_DATE\,INTERVAL 1 YEAR),Same day last year must be exactly one year before,medium,P2,ALL,true