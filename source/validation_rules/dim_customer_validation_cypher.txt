// ========================================
// DATA QUALITY VALIDATION RULES FOR DIM_CUSTOMER
// This script creates validation rules and links them to columns
// ========================================

// Create constraint for ValidationRule uniqueness (run once if not exists)
CREATE CONSTRAINT validation_rule_unique IF NOT EXISTS
FOR (v:ValidationRule) REQUIRE v.rule_id IS UNIQUE;

// ========================================
// CORE CUSTOMER IDENTIFIERS
// ========================================

// CUSTOMER_ID - NOT NULL
MERGE (v1:ValidationRule {rule_id: "VAL_CUSTOMER_ID_001"})
SET v1.rule_name = "CUSTOMER_ID Not Null Check",
    v1.column_name = "CUSTOMER_ID",
    v1.table_id = "DIM_CUSTOMER",
    v1.validation_type = "NOT_NULL",
    v1.validation_rule = "NOT_NULL",
    v1.error_message = "Customer ID is required",
    v1.business_criticality = "critical",
    v1.priority = "P1",
    v1.applicable_domains = ["ALL"],
    v1.is_active = true,
    v1.created_date = datetime(),
    v1.last_updated_date = datetime()
WITH v1
MATCH (c:Column {column_id: "COL_CUSTOMER_ID_DIM_501"})
MERGE (c)-[:HAS_VALIDATION]->(v1);

// CUSTOMER_ID - UNIQUE
MERGE (v2:ValidationRule {rule_id: "VAL_CUSTOMER_ID_002"})
SET v2.rule_name = "CUSTOMER_ID Uniqueness Check",
    v2.column_name = "CUSTOMER_ID",
    v2.table_id = "DIM_CUSTOMER",
    v2.validation_type = "UNIQUE",
    v2.validation_rule = "UNIQUE",
    v2.error_message = "Customer ID must be unique",
    v2.business_criticality = "critical",
    v2.priority = "P1",
    v2.applicable_domains = ["ALL"],
    v2.is_active = true,
    v2.created_date = datetime(),
    v2.last_updated_date = datetime()
WITH v2
MATCH (c:Column {column_id: "COL_CUSTOMER_ID_DIM_501"})
MERGE (c)-[:HAS_VALIDATION]->(v2);

// CUSTOMER_ID - FORMAT
MERGE (v3:ValidationRule {rule_id: "VAL_CUSTOMER_ID_003"})
SET v3.rule_name = "CUSTOMER_ID Format Check",
    v3.column_name = "CUSTOMER_ID",
    v3.table_id = "DIM_CUSTOMER",
    v3.validation_type = "REGEX",
    v3.validation_rule = "^[A-Za-z0-9_-]{1,50}$",
    v3.error_message = "Customer ID must be alphanumeric with underscores/hyphens, max 50 chars",
    v3.business_criticality = "critical",
    v3.priority = "P1",
    v3.applicable_domains = ["ALL"],
    v3.is_active = true,
    v3.created_date = datetime(),
    v3.last_updated_date = datetime()
WITH v3
MATCH (c:Column {column_id: "COL_CUSTOMER_ID_DIM_501"})
MERGE (c)-[:HAS_VALIDATION]->(v3);

// ========================================
// PERSONAL INFORMATION VALIDATIONS
// ========================================

// FIRST_NAME - LENGTH
MERGE (v4:ValidationRule {rule_id: "VAL_FIRST_NAME_001"})
SET v4.rule_name = "FIRST_NAME Length Check",
    v4.column_name = "FIRST_NAME",
    v4.table_id = "DIM_CUSTOMER",
    v4.validation_type = "LENGTH",
    v4.validation_rule = "LENGTH:1,50",
    v4.error_message = "First Name must be 1-50 characters",
    v4.business_criticality = "medium",
    v4.priority = "P2",
    v4.applicable_domains = ["ALL"],
    v4.is_active = true,
    v4.created_date = datetime(),
    v4.last_updated_date = datetime()
WITH v4
MATCH (c:Column {column_id: "COL_FIRST_NAME_503"})
MERGE (c)-[:HAS_VALIDATION]->(v4);

// LAST_NAME - LENGTH
MERGE (v5:ValidationRule {rule_id: "VAL_LAST_NAME_001"})
SET v5.rule_name = "LAST_NAME Length Check",
    v5.column_name = "LAST_NAME",
    v5.table_id = "DIM_CUSTOMER",
    v5.validation_type = "LENGTH",
    v5.validation_rule = "LENGTH:1,50",
    v5.error_message = "Last Name must be 1-50 characters",
    v5.business_criticality = "medium",
    v5.priority = "P2",
    v5.applicable_domains = ["ALL"],
    v5.is_active = true,
    v5.created_date = datetime(),
    v5.last_updated_date = datetime()
WITH v5
MATCH (c:Column {column_id: "COL_LAST_NAME_504"})
MERGE (c)-[:HAS_VALIDATION]->(v5);

// EMAIL_ADDRESS - NOT NULL
MERGE (v6:ValidationRule {rule_id: "VAL_EMAIL_ADDRESS_001"})
SET v6.rule_name = "EMAIL_ADDRESS Not Null Check",
    v6.column_name = "EMAIL_ADDRESS",
    v6.table_id = "DIM_CUSTOMER",
    v6.validation_type = "NOT_NULL",
    v6.validation_rule = "NOT_NULL",
    v6.error_message = "Email Address is required",
    v6.business_criticality = "high",
    v6.priority = "P1",
    v6.applicable_domains = ["ALL"],
    v6.is_active = true,
    v6.created_date = datetime(),
    v6.last_updated_date = datetime()
WITH v6
MATCH (c:Column {column_id: "COL_EMAIL_ADDRESS_506"})
MERGE (c)-[:HAS_VALIDATION]->(v6);

// EMAIL_ADDRESS - FORMAT
MERGE (v7:ValidationRule {rule_id: "VAL_EMAIL_ADDRESS_002"})
SET v7.rule_name = "EMAIL_ADDRESS Format Check",
    v7.column_name = "EMAIL_ADDRESS",
    v7.table_id = "DIM_CUSTOMER",
    v7.validation_type = "REGEX",
    v7.validation_rule = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$",
    v7.error_message = "Email Address must be in valid format",
    v7.business_criticality = "high",
    v7.priority = "P1",
    v7.applicable_domains = ["ALL"],
    v7.is_active = true,
    v7.created_date = datetime(),
    v7.last_updated_date = datetime()
WITH v7
MATCH (c:Column {column_id: "COL_EMAIL_ADDRESS_506"})
MERGE (c)-[:HAS_VALIDATION]->(v7);

// PHONE_NUMBER - FORMAT
MERGE (v8:ValidationRule {rule_id: "VAL_PHONE_NUMBER_001"})
SET v8.rule_name = "PHONE_NUMBER Format Check",
    v8.column_name = "PHONE_NUMBER",
    v8.table_id = "DIM_CUSTOMER",
    v8.validation_type = "REGEX",
    v8.validation_rule = "^[+]?[0-9\\s\\-\\(\\)]{7,30}$",
    v8.error_message = "Phone Number must be in valid format",
    v8.business_criticality = "high",
    v8.priority = "P1",
    v8.applicable_domains = ["ALL"],
    v8.is_active = true,
    v8.created_date = datetime(),
    v8.last_updated_date = datetime()
WITH v8
MATCH (c:Column {column_id: "COL_PHONE_NUMBER_507"})
MERGE (c)-[:HAS_VALIDATION]->(v8);

// AGE - RANGE
MERGE (v9:ValidationRule {rule_id: "VAL_AGE_001"})
SET v9.rule_name = "AGE Range Check",
    v9.column_name = "AGE",
    v9.table_id = "DIM_CUSTOMER",
    v9.validation_type = "RANGE",
    v9.validation_rule = "RANGE:0,120",
    v9.error_message = "Age must be between 0 and 120",
    v9.business_criticality = "high",
    v9.priority = "P1",
    v9.applicable_domains = ["ALL"],
    v9.is_active = true,
    v9.created_date = datetime(),
    v9.last_updated_date = datetime()
WITH v9
MATCH (c:Column {column_id: "COL_AGE_509"})
MERGE (c)-[:HAS_VALIDATION]->(v9);

// GENDER - ENUM
MERGE (v10:ValidationRule {rule_id: "VAL_GENDER_001"})
SET v10.rule_name = "GENDER Domain Check",
    v10.column_name = "GENDER",
    v10.table_id = "DIM_CUSTOMER",
    v10.validation_type = "ENUM",
    v10.validation_rule = "ENUM:Male,Female,Other,Prefer not to say",
    v10.error_message = "Invalid gender value",
    v10.business_criticality = "medium",
    v10.priority = "P2",
    v10.applicable_domains = ["ALL"],
    v10.is_active = true,
    v10.created_date = datetime(),
    v10.last_updated_date = datetime()
WITH v10
MATCH (c:Column {column_id: "COL_GENDER_510"})
MERGE (c)-[:HAS_VALIDATION]->(v10);

// ========================================
// GEOGRAPHIC INFORMATION VALIDATIONS
// ========================================

// ADDRESS_LINE_1 - NOT NULL
MERGE (v11:ValidationRule {rule_id: "VAL_ADDRESS_LINE_1_001"})
SET v11.rule_name = "ADDRESS_LINE_1 Not Null Check",
    v11.column_name = "ADDRESS_LINE_1",
    v11.table_id = "DIM_CUSTOMER",
    v11.validation_type = "NOT_NULL",
    v11.validation_rule = "NOT_NULL",
    v11.error_message = "Primary address is required",
    v11.business_criticality = "high",
    v11.priority = "P1",
    v11.applicable_domains = ["ALL"],
    v11.is_active = true,
    v11.created_date = datetime(),
    v11.last_updated_date = datetime()
WITH v11
MATCH (c:Column {column_id: "COL_ADDRESS_LINE_1_511"})
MERGE (c)-[:HAS_VALIDATION]->(v11);

// CITY - NOT NULL
MERGE (v12:ValidationRule {rule_id: "VAL_CITY_001"})
SET v12.rule_name = "CITY Not Null Check",
    v12.column_name = "CITY",
    v12.table_id = "DIM_CUSTOMER",
    v12.validation_type = "NOT_NULL",
    v12.validation_rule = "NOT_NULL",
    v12.error_message = "City is required",
    v12.business_criticality = "medium",
    v12.priority = "P1",
    v12.applicable_domains = ["ALL"],
    v12.is_active = true,
    v12.created_date = datetime(),
    v12.last_updated_date = datetime()
WITH v12
MATCH (c:Column {column_id: "COL_CITY_513"})
MERGE (c)-[:HAS_VALIDATION]->(v12);

// STATE_PROVINCE - NOT NULL
MERGE (v13:ValidationRule {rule_id: "VAL_STATE_PROVINCE_001"})
SET v13.rule_name = "STATE_PROVINCE Not Null Check",
    v13.column_name = "STATE_PROVINCE",
    v13.table_id = "DIM_CUSTOMER",
    v13.validation_type = "NOT_NULL",
    v13.validation_rule = "NOT_NULL",
    v13.error_message = "State or Province is required",
    v13.business_criticality = "high",
    v13.priority = "P1",
    v13.applicable_domains = ["ALL"],
    v13.is_active = true,
    v13.created_date = datetime(),
    v13.last_updated_date = datetime()
WITH v13
MATCH (c:Column {column_id: "COL_STATE_PROVINCE_514"})
MERGE (c)-[:HAS_VALIDATION]->(v13);

// ZIP_CODE - FORMAT
MERGE (v14:ValidationRule {rule_id: "VAL_ZIP_CODE_001"})
SET v14.rule_name = "ZIP_CODE Format Check",
    v14.column_name = "ZIP_CODE",
    v14.table_id = "DIM_CUSTOMER",
    v14.validation_type = "REGEX",
    v14.validation_rule = "^[A-Za-z0-9\\s-]{3,15}$",
    v14.error_message = "ZIP Code must be in valid format",
    v14.business_criticality = "medium",
    v14.priority = "P2",
    v14.applicable_domains = ["ALL"],
    v14.is_active = true,
    v14.created_date = datetime(),
    v14.last_updated_date = datetime()
WITH v14
MATCH (c:Column {column_id: "COL_ZIP_CODE_515"})
MERGE (c)-[:HAS_VALIDATION]->(v14);

// COUNTRY - NOT NULL
MERGE (v15:ValidationRule {rule_id: "VAL_COUNTRY_001"})
SET v15.rule_name = "COUNTRY Not Null Check",
    v15.column_name = "COUNTRY",
    v15.table_id = "DIM_CUSTOMER",
    v15.validation_type = "NOT_NULL",
    v15.validation_rule = "NOT_NULL",
    v15.error_message = "Country is required",
    v15.business_criticality = "high",
    v15.priority = "P1",
    v15.applicable_domains = ["ALL"],
    v15.is_active = true,
    v15.created_date = datetime(),
    v15.last_updated_date = datetime()
WITH v15
MATCH (c:Column {column_id: "COL_COUNTRY_516"})
MERGE (c)-[:HAS_VALIDATION]->(v15);

// LATITUDE - RANGE
MERGE (v16:ValidationRule {rule_id: "VAL_LATITUDE_001"})
SET v16.rule_name = "LATITUDE Range Check",
    v16.column_name = "LATITUDE",
    v16.table_id = "DIM_CUSTOMER",
    v16.validation_type = "RANGE",
    v16.validation_rule = "RANGE:-90,90",
    v16.error_message = "Latitude must be between -90 and 90",
    v16.business_criticality = "low",
    v16.priority = "P3",
    v16.applicable_domains = ["ALL"],
    v16.is_active = true,
    v16.created_date = datetime(),
    v16.last_updated_date = datetime()
WITH v16
MATCH (c:Column {column_id: "COL_LATITUDE_517"})
MERGE (c)-[:HAS_VALIDATION]->(v16);

// LONGITUDE - RANGE
MERGE (v17:ValidationRule {rule_id: "VAL_LONGITUDE_001"})
SET v17.rule_name = "LONGITUDE Range Check",
    v17.column_name = "LONGITUDE",
    v17.table_id = "DIM_CUSTOMER",
    v17.validation_type = "RANGE",
    v17.validation_rule = "RANGE:-180,180",
    v17.error_message = "Longitude must be between -180 and 180",
    v17.business_criticality = "low",
    v17.priority = "P3",
    v17.applicable_domains = ["ALL"],
    v17.is_active = true,
    v17.created_date = datetime(),
    v17.last_updated_date = datetime()
WITH v17
MATCH (c:Column {column_id: "COL_LONGITUDE_518"})
MERGE (c)-[:HAS_VALIDATION]->(v17);

// ========================================
// CUSTOMER CLASSIFICATION VALIDATIONS
// ========================================

// CUSTOMER_TYPE - NOT NULL
MERGE (v18:ValidationRule {rule_id: "VAL_CUSTOMER_TYPE_001"})
SET v18.rule_name = "CUSTOMER_TYPE Not Null Check",
    v18.column_name = "CUSTOMER_TYPE",
    v18.table_id = "DIM_CUSTOMER",
    v18.validation_type = "NOT_NULL",
    v18.validation_rule = "NOT_NULL",
    v18.error_message = "Customer Type is required",
    v18.business_criticality = "high",
    v18.priority = "P1",
    v18.applicable_domains = ["ALL"],
    v18.is_active = true,
    v18.created_date = datetime(),
    v18.last_updated_date = datetime()
WITH v18
MATCH (c:Column {column_id: "COL_CUSTOMER_TYPE_519"})
MERGE (c)-[:HAS_VALIDATION]->(v18);

// CUSTOMER_TYPE - ENUM
MERGE (v19:ValidationRule {rule_id: "VAL_CUSTOMER_TYPE_002"})
SET v19.rule_name = "CUSTOMER_TYPE Domain Check",
    v19.column_name = "CUSTOMER_TYPE",
    v19.table_id = "DIM_CUSTOMER",
    v19.validation_type = "ENUM",
    v19.validation_rule = "ENUM:Individual,Business,Healthcare Provider,Institutional",
    v19.error_message = "Invalid customer type",
    v19.business_criticality = "high",
    v19.priority = "P1",
    v19.applicable_domains = ["ALL"],
    v19.is_active = true,
    v19.created_date = datetime(),
    v19.last_updated_date = datetime()
WITH v19
MATCH (c:Column {column_id: "COL_CUSTOMER_TYPE_519"})
MERGE (c)-[:HAS_VALIDATION]->(v19);

// CUSTOMER_SEGMENT - NOT NULL
MERGE (v20:ValidationRule {rule_id: "VAL_CUSTOMER_SEGMENT_001"})
SET v20.rule_name = "CUSTOMER_SEGMENT Not Null Check",
    v20.column_name = "CUSTOMER_SEGMENT",
    v20.table_id = "DIM_CUSTOMER",
    v20.validation_type = "NOT_NULL",
    v20.validation_rule = "NOT_NULL",
    v20.error_message = "Customer Segment is required",
    v20.business_criticality = "high",
    v20.priority = "P1",
    v20.applicable_domains = ["ALL"],
    v20.is_active = true,
    v20.created_date = datetime(),
    v20.last_updated_date = datetime()
WITH v20
MATCH (c:Column {column_id: "COL_CUSTOMER_SEGMENT_520"})
MERGE (c)-[:HAS_VALIDATION]->(v20);

// CUSTOMER_TIER - ENUM
MERGE (v21:ValidationRule {rule_id: "VAL_CUSTOMER_TIER_001"})
SET v21.rule_name = "CUSTOMER_TIER Domain Check",
    v21.column_name = "CUSTOMER_TIER",
    v21.table_id = "DIM_CUSTOMER",
    v21.validation_type = "ENUM",
    v21.validation_rule = "ENUM:Platinum,Gold,Silver,Bronze,Standard",
    v21.error_message = "Invalid customer tier",
    v21.business_criticality = "medium",
    v21.priority = "P2",
    v21.applicable_domains = ["ALL"],
    v21.is_active = true,
    v21.created_date = datetime(),
    v21.last_updated_date = datetime()
WITH v21
MATCH (c:Column {column_id: "COL_CUSTOMER_TIER_521"})
MERGE (c)-[:HAS_VALIDATION]->(v21);

// LIFECYCLE_STAGE - ENUM
MERGE (v22:ValidationRule {rule_id: "VAL_LIFECYCLE_STAGE_001"})
SET v22.rule_name = "LIFECYCLE_STAGE Domain Check",
    v22.column_name = "LIFECYCLE_STAGE",
    v22.table_id = "DIM_CUSTOMER",
    v22.validation_type = "ENUM",
    v22.validation_rule = "ENUM:Prospect,New,Active,At-Risk,Inactive,Churned",
    v22.error_message = "Invalid lifecycle stage",
    v22.business_criticality = "high",
    v22.priority = "P1",
    v22.applicable_domains = ["ALL"],
    v22.is_active = true,
    v22.created_date = datetime(),
    v22.last_updated_date = datetime()
WITH v22
MATCH (c:Column {column_id: "COL_LIFECYCLE_STAGE_522"})
MERGE (c)-[:HAS_VALIDATION]->(v22);

// ========================================
// SHOPPING BEHAVIOR VALIDATIONS
// ========================================

// PREFERRED_CHANNEL - ENUM
MERGE (v23:ValidationRule {rule_id: "VAL_PREFERRED_CHANNEL_001"})
SET v23.rule_name = "PREFERRED_CHANNEL Domain Check",
    v23.column_name = "PREFERRED_CHANNEL",
    v23.table_id = "DIM_CUSTOMER",
    v23.validation_type = "ENUM",
    v23.validation_rule = "ENUM:Online,In-Store,Mobile,Mixed,Catalog",
    v23.error_message = "Invalid preferred channel",
    v23.business_criticality = "high",
    v23.priority = "P2",
    v23.applicable_domains = ["ALL"],
    v23.is_active = true,
    v23.created_date = datetime(),
    v23.last_updated_date = datetime()
WITH v23
MATCH (c:Column {column_id: "COL_PREFERRED_CHANNEL_523"})
MERGE (c)-[:HAS_VALIDATION]->(v23);

// SHOPPING_FREQUENCY - ENUM
MERGE (v24:ValidationRule {rule_id: "VAL_SHOPPING_FREQUENCY_001"})
SET v24.rule_name = "SHOPPING_FREQUENCY Domain Check",
    v24.column_name = "SHOPPING_FREQUENCY",
    v24.table_id = "DIM_CUSTOMER",
    v24.validation_type = "ENUM",
    v24.validation_rule = "ENUM:Daily,Weekly,Bi-Weekly,Monthly,Occasionally",
    v24.error_message = "Invalid shopping frequency",
    v24.business_criticality = "medium",
    v24.priority = "P2",
    v24.applicable_domains = ["ALL"],
    v24.is_active = true,
    v24.created_date = datetime(),
    v24.last_updated_date = datetime()
WITH v24
MATCH (c:Column {column_id: "COL_SHOPPING_FREQUENCY_524"})
MERGE (c)-[:HAS_VALIDATION]->(v24);

// PRICE_SENSITIVITY - ENUM
MERGE (v25:ValidationRule {rule_id: "VAL_PRICE_SENSITIVITY_001"})
SET v25.rule_name = "PRICE_SENSITIVITY Domain Check",
    v25.column_name = "PRICE_SENSITIVITY",
    v25.table_id = "DIM_CUSTOMER",
    v25.validation_type = "ENUM",
    v25.validation_rule = "ENUM:High,Medium,Low",
    v25.error_message = "Invalid price sensitivity level",
    v25.business_criticality = "high",
    v25.priority = "P2",
    v25.applicable_domains = ["ALL"],
    v25.is_active = true,
    v25.created_date = datetime(),
    v25.last_updated_date = datetime()
WITH v25
MATCH (c:Column {column_id: "COL_PRICE_SENSITIVITY_525"})
MERGE (c)-[:HAS_VALIDATION]->(v25);

// BRAND_LOYALTY_LEVEL - ENUM
MERGE (v26:ValidationRule {rule_id: "VAL_BRAND_LOYALTY_LEVEL_001"})
SET v26.rule_name = "BRAND_LOYALTY_LEVEL Domain Check",
    v26.column_name = "BRAND_LOYALTY_LEVEL",
    v26.table_id = "DIM_CUSTOMER",
    v26.validation_type = "ENUM",
    v26.validation_rule = "ENUM:Highly Loyal,Moderately Loyal,Switch-Prone,Price-Driven",
    v26.error_message = "Invalid brand loyalty level",
    v26.business_criticality = "high",
    v26.priority = "P2",
    v26.applicable_domains = ["ALL"],
    v26.is_active = true,
    v26.created_date = datetime(),
    v26.last_updated_date = datetime()
WITH v26
MATCH (c:Column {column_id: "COL_BRAND_LOYALTY_LEVEL_526"})
MERGE (c)-[:HAS_VALIDATION]->(v26);

// ========================================
// LOYALTY PROGRAM VALIDATIONS
// ========================================

// LOYALTY_PROGRAM_MEMBER - NOT NULL
MERGE (v27:ValidationRule {rule_id: "VAL_LOYALTY_PROGRAM_MEMBER_001"})
SET v27.rule_name = "LOYALTY_PROGRAM_MEMBER Not Null Check",
    v27.column_name = "LOYALTY_PROGRAM_MEMBER",
    v27.table_id = "DIM_CUSTOMER",
    v27.validation_type = "NOT_NULL",
    v27.validation_rule = "NOT_NULL",
    v27.error_message = "Loyalty program member flag is required",
    v27.business_criticality = "high",
    v27.priority = "P1",
    v27.applicable_domains = ["ALL"],
    v27.is_active = true,
    v27.created_date = datetime(),
    v27.last_updated_date = datetime()
WITH v27
MATCH (c:Column {column_id: "COL_LOYALTY_PROGRAM_MEMBER_527"})
MERGE (c)-[:HAS_VALIDATION]->(v27);

// LOYALTY_POINTS_BALANCE - RANGE
MERGE (v28:ValidationRule {rule_id: "VAL_LOYALTY_POINTS_BALANCE_001"})
SET v28.rule_name = "LOYALTY_POINTS_BALANCE Range Check",
    v28.column_name = "LOYALTY_POINTS_BALANCE",
    v28.table_id = "DIM_CUSTOMER",
    v28.validation_type = "RANGE",
    v28.validation_rule = "RANGE:0,999999999",
    v28.error_message = "Loyalty points must be non-negative",
    v28.business_criticality = "medium",
    v28.priority = "P2",
    v28.applicable_domains = ["ALL"],
    v28.is_active = true,
    v28.created_date = datetime(),
    v28.last_updated_date = datetime()
WITH v28
MATCH (c:Column {column_id: "COL_LOYALTY_POINTS_BALANCE_529"})
MERGE (c)-[:HAS_VALIDATION]->(v28);

// ENGAGEMENT_SCORE - RANGE
MERGE (v29:ValidationRule {rule_id: "VAL_ENGAGEMENT_SCORE_001"})
SET v29.rule_name = "ENGAGEMENT_SCORE Range Check",
    v29.column_name = "ENGAGEMENT_SCORE",
    v29.table_id = "DIM_CUSTOMER",
    v29.validation_type = "RANGE",
    v29.validation_rule = "RANGE:0,100",
    v29.error_message = "Engagement score must be between 0 and 100",
    v29.business_criticality = "high",
    v29.priority = "P2",
    v29.applicable_domains = ["ALL"],
    v29.is_active = true,
    v29.created_date = datetime(),
    v29.last_updated_date = datetime()
WITH v29
MATCH (c:Column {column_id: "COL_ENGAGEMENT_SCORE_530"})
MERGE (c)-[:HAS_VALIDATION]->(v29);

// ========================================
// PURCHASE HISTORY METRICS VALIDATIONS
// ========================================

// TOTAL_LIFETIME_VALUE - RANGE
MERGE (v30:ValidationRule {rule_id: "VAL_TOTAL_LIFETIME_VALUE_001"})
SET v30.rule_name = "TOTAL_LIFETIME_VALUE Range Check",
    v30.column_name = "TOTAL_LIFETIME_VALUE",
    v30.table_id = "DIM_CUSTOMER",
    v30.validation_type = "RANGE",
    v30.validation_rule = "RANGE:0,9999999999.99",
    v30.error_message = "Total lifetime value must be non-negative",
    v30.business_criticality = "critical",
    v30.priority = "P1",
    v30.applicable_domains = ["ALL"],
    v30.is_active = true,
    v30.created_date = datetime(),
    v30.last_updated_date = datetime()
WITH v30
MATCH (c:Column {column_id: "COL_TOTAL_LIFETIME_VALUE_531"})
MERGE (c)-[:HAS_VALIDATION]->(v30);

// AVERAGE_ORDER_VALUE - RANGE
MERGE (v31:ValidationRule {rule_id: "VAL_AVERAGE_ORDER_VALUE_001"})
SET v31.rule_name = "AVERAGE_ORDER_VALUE Range Check",
    v31.column_name = "AVERAGE_ORDER_VALUE",
    v31.table_id = "DIM_CUSTOMER",
    v31.validation_type = "RANGE",
    v31.validation_rule = "RANGE:0,99999.99",
    v31.error_message = "Average order value must be non-negative",
    v31.business_criticality = "high",
    v31.priority = "P2",
    v31.applicable_domains = ["ALL"],
    v31.is_active = true,
    v31.created_date = datetime(),
    v31.last_updated_date = datetime()
WITH v31
MATCH (c:Column {column_id: "COL_AVERAGE_ORDER_VALUE_532"})
MERGE (c)-[:HAS_VALIDATION]->(v31);

// PURCHASE_FREQUENCY_ANNUAL - RANGE
MERGE (v32:ValidationRule {rule_id: "VAL_PURCHASE_FREQUENCY_ANNUAL_001"})
SET v32.rule_name = "PURCHASE_FREQUENCY_ANNUAL Range Check",
    v32.column_name = "PURCHASE_FREQUENCY_ANNUAL",
    v32.table_id = "DIM_CUSTOMER",
    v32.validation_type = "RANGE",
    v32.validation_rule = "RANGE:0,365",
    v32.error_message = "Annual purchase frequency must be between 0 and 365",
    v32.business_criticality = "medium",
    v32.priority = "P2",
    v32.applicable_domains = ["ALL"],
    v32.is_active = true,
    v32.created_date = datetime(),
    v32.last_updated_date = datetime()
WITH v32
MATCH (c:Column {column_id: "COL_PURCHASE_FREQUENCY_ANNUAL_533"})
MERGE (c)-[:HAS_VALIDATION]->(v32);

// ========================================
// DEMOGRAPHICS VALIDATIONS
// ========================================

// HOUSEHOLD_SIZE - RANGE
MERGE (v33:ValidationRule {rule_id: "VAL_HOUSEHOLD_SIZE_001"})
SET v33.rule_name = "HOUSEHOLD_SIZE Range Check",
    v33.column_name = "HOUSEHOLD_SIZE",
    v33.table_id = "DIM_CUSTOMER",
    v33.validation_type = "RANGE",
    v33.validation_rule = "RANGE:1,20",
    v33.error_message = "Household size must be between 1 and 20",
    v33.business_criticality = "medium",
    v33.priority = "P2",
    v33.applicable_domains = ["ALL"],
    v33.is_active = true,
    v33.created_date = datetime(),
    v33.last_updated_date = datetime()
WITH v33
MATCH (c:Column {column_id: "COL_HOUSEHOLD_SIZE_540"})
MERGE (c)-[:HAS_VALIDATION]->(v33);

// CHILDREN_IN_HOUSEHOLD - RANGE
MERGE (v34:ValidationRule {rule_id: "VAL_CHILDREN_IN_HOUSEHOLD_001"})
SET v34.rule_name = "CHILDREN_IN_HOUSEHOLD Range Check",
    v34.column_name = "CHILDREN_IN_HOUSEHOLD",
    v34.table_id = "DIM_CUSTOMER",
    v34.validation_type = "RANGE",
    v34.validation_rule = "RANGE:0,15",
    v34.error_message = "Number of children must be between 0 and 15",
    v34.business_criticality = "medium",
    v34.priority = "P2",
    v34.applicable_domains = ["ALL"],
    v34.is_active = true,
    v34.created_date = datetime(),
    v34.last_updated_date = datetime()
WITH v34
MATCH (c:Column {column_id: "COL_CHILDREN_IN_HOUSEHOLD_541"})
MERGE (c)-[:HAS_VALIDATION]->(v34);

// LIFE_STAGE - ENUM
MERGE (v35:ValidationRule {rule_id: "VAL_LIFE_STAGE_001"})
SET v35.rule_name = "LIFE_STAGE Domain Check",
    v35.column_name = "LIFE_STAGE",
    v35.table_id = "DIM_CUSTOMER",
    v35.validation_type = "ENUM",
    v35.validation_rule = "ENUM:Young Adult,Family Formation,Established Family,Empty Nester,Retiree",
    v35.error_message = "Invalid life stage",
    v35.business_criticality = "high",
    v35.priority = "P2",
    v35.applicable_domains = ["ALL"],
    v35.is_active = true,
    v35.created_date = datetime(),
    v35.last_updated_date = datetime()
WITH v35
MATCH (c:Column {column_id: "COL_LIFE_STAGE_542"})
MERGE (c)-[:HAS_VALIDATION]->(v35);

// ========================================
// DIGITAL ENGAGEMENT VALIDATIONS
// ========================================

// EMAIL_OPT_IN - NOT NULL
MERGE (v36:ValidationRule {rule_id: "VAL_EMAIL_OPT_IN_001"})
SET v36.rule_name = "EMAIL_OPT_IN Not Null Check",
    v36.column_name = "EMAIL_OPT_IN",
    v36.table_id = "DIM_CUSTOMER",
    v36.validation_type = "NOT_NULL",
    v36.validation_rule = "NOT_NULL",
    v36.error_message = "Email opt-in status is required",
    v36.business_criticality = "high",
    v36.priority = "P1",
    v36.applicable_domains = ["ALL"],
    v36.is_active = true,
    v36.created_date = datetime(),
    v36.last_updated_date = datetime()
WITH v36
MATCH (c:Column {column_id: "COL_EMAIL_OPT_IN_543"})
MERGE (c)-[:HAS_VALIDATION]->(v36);

// SMS_OPT_IN - NOT NULL
MERGE (v37:ValidationRule {rule_id: "VAL_SMS_OPT_IN_001"})
SET v37.rule_name = "SMS_OPT_IN Not Null Check",
    v37.column_name = "SMS_OPT_IN",
    v37.table_id = "DIM_CUSTOMER",
    v37.validation_type = "NOT_NULL",
    v37.validation_rule = "NOT_NULL",
    v37.error_message = "SMS opt-in status is required",
    v37.business_criticality = "medium",
    v37.priority = "P2",
    v37.applicable_domains = ["ALL"],
    v37.is_active = true,
    v37.created_date = datetime(),
    v37.last_updated_date = datetime()
WITH v37
MATCH (c:Column {column_id: "COL_SMS_OPT_IN_544"})
MERGE (c)-[:HAS_VALIDATION]->(v37);

// ========================================
// CPG-SPECIFIC PREFERENCES VALIDATIONS
// ========================================

// ORGANIC_PREFERENCE - ENUM
MERGE (v38:ValidationRule {rule_id: "VAL_ORGANIC_PREFERENCE_001"})
SET v38.rule_name = "ORGANIC_PREFERENCE Domain Check",
    v38.column_name = "ORGANIC_PREFERENCE",
    v38.table_id = "DIM_CUSTOMER",
    v38.validation_type = "ENUM",
    v38.validation_rule = "ENUM:Strong,Moderate,Low,None",
    v38.error_message = "Invalid organic preference level",
    v38.business_criticality = "medium",
    v38.priority = "P3",
    v38.applicable_domains = ["ALL"],
    v38.is_active = true,
    v38.created_date = datetime(),
    v38.last_updated_date = datetime()
WITH v38
MATCH (c:Column {column_id: "COL_ORGANIC_PREFERENCE_547"})
MERGE (c)-[:HAS_VALIDATION]->(v38);

// HEALTH_WELLNESS_FOCUS - ENUM
MERGE (v39:ValidationRule {rule_id: "VAL_HEALTH_WELLNESS_FOCUS_001"})
SET v39.rule_name = "HEALTH_WELLNESS_FOCUS Domain Check",
    v39.column_name = "HEALTH_WELLNESS_FOCUS",
    v39.table_id = "DIM_CUSTOMER",
    v39.validation_type = "ENUM",
    v39.validation_rule = "ENUM:Very High,High,Medium,Low",
    v39.error_message = "Invalid health wellness focus level",
    v39.business_criticality = "medium",
    v39.priority = "P3",
    v39.applicable_domains = ["ALL"],
    v39.is_active = true,
    v39.created_date = datetime(),
    v39.last_updated_date = datetime()
WITH v39
MATCH (c:Column {column_id: "COL_HEALTH_WELLNESS_FOCUS_548"})
MERGE (c)-[:HAS_VALIDATION]->(v39);

// SUSTAINABILITY_CONCERN - ENUM
MERGE (v40:ValidationRule {rule_id: "VAL_SUSTAINABILITY_CONCERN_001"})
SET v40.rule_name = "SUSTAINABILITY_CONCERN Domain Check",
    v40.column_name = "SUSTAINABILITY_CONCERN",
    v40.table_id = "DIM_CUSTOMER",
    v40.validation_type = "ENUM",
    v40.validation_rule = "ENUM:Very High,High,Medium,Low",
    v40.error_message = "Invalid sustainability concern level",
    v40.business_criticality = "medium",
    v40.priority = "P3",
    v40.applicable_domains = ["ALL"],
    v40.is_active = true,
    v40.created_date = datetime(),
    v40.last_updated_date = datetime()
WITH v40
MATCH (c:Column {column_id: "COL_SUSTAINABILITY_CONCERN_549"})
MERGE (c)-[:HAS_VALIDATION]->(v40);

// ========================================
// DOMAIN-SPECIFIC VALIDATIONS
// ========================================

// AGE_VERIFICATION_STATUS - NOT NULL (Alcoholic Beverages, Pharmaceuticals)
MERGE (v41:ValidationRule {rule_id: "VAL_AGE_VERIFICATION_STATUS_001"})
SET v41.rule_name = "AGE_VERIFICATION_STATUS Not Null Check",
    v41.column_name = "AGE_VERIFICATION_STATUS",
    v41.table_id = "DIM_CUSTOMER",
    v41.validation_type = "NOT_NULL",
    v41.validation_rule = "NOT_NULL",
    v41.error_message = "Age verification status is required for age-restricted products",
    v41.business_criticality = "critical",
    v41.priority = "P1",
    v41.applicable_domains = ["alcoholic_beverages", "pharmaceuticals"],
    v41.is_active = true,
    v41.created_date = datetime(),
    v41.last_updated_date = datetime()
WITH v41
MATCH (c:Column {column_id: "COL_AGE_VERIFICATION_STATUS_551"})
MERGE (c)-[:HAS_VALIDATION]->(v41);

// AGE_VERIFICATION_STATUS - ENUM
MERGE (v42:ValidationRule {rule_id: "VAL_AGE_VERIFICATION_STATUS_002"})
SET v42.rule_name = "AGE_VERIFICATION_STATUS Domain Check",
    v42.column_name = "AGE_VERIFICATION_STATUS",
    v42.table_id = "DIM_CUSTOMER",
    v42.validation_type = "ENUM",
    v42.validation_rule = "ENUM:Verified,Not Verified,Pending,Expired",
    v42.error_message = "Invalid age verification status",
    v42.business_criticality = "critical",
    v42.priority = "P1",
    v42.applicable_domains = ["alcoholic_beverages", "pharmaceuticals"],
    v42.is_active = true,
    v42.created_date = datetime(),
    v42.last_updated_date = datetime()
WITH v42
MATCH (c:Column {column_id: "COL_AGE_VERIFICATION_STATUS_551"})
MERGE (c)-[:HAS_VALIDATION]->(v42);

// PRESCRIPTION_INSURANCE - NOT NULL (Pharmaceuticals)
MERGE (v43:ValidationRule {rule_id: "VAL_PRESCRIPTION_INSURANCE_001"})
SET v43.rule_name = "PRESCRIPTION_INSURANCE Not Null Check",
    v43.column_name = "PRESCRIPTION_INSURANCE",
    v43.table_id = "DIM_CUSTOMER",
    v43.validation_type = "NOT_NULL",
    v43.validation_rule = "NOT_NULL",
    v43.error_message = "Prescription insurance flag is required for pharmaceutical customers",
    v43.business_criticality = "high",
    v43.priority = "P1",
    v43.applicable_domains = ["pharmaceuticals"],
    v43.is_active = true,
    v43.created_date = datetime(),
    v43.last_updated_date = datetime()
WITH v43
MATCH (c:Column {column_id: "COL_PRESCRIPTION_INSURANCE_552"})
MERGE (c)-[:HAS_VALIDATION]->(v43);

// ========================================
// ACCOUNT MANAGEMENT VALIDATIONS
// ========================================

// CUSTOMER_STATUS - NOT NULL
MERGE (v44:ValidationRule {rule_id: "VAL_CUSTOMER_STATUS_001"})
SET v44.rule_name = "CUSTOMER_STATUS Not Null Check",
    v44.column_name = "CUSTOMER_STATUS",
    v44.table_id = "DIM_CUSTOMER",
    v44.validation_type = "NOT_NULL",
    v44.validation_rule = "NOT_NULL",
    v44.error_message = "Customer status is required",
    v44.business_criticality = "critical",
    v44.priority = "P1",
    v44.applicable_domains = ["ALL"],
    v44.is_active = true,
    v44.created_date = datetime(),
    v44.last_updated_date = datetime()
WITH v44
MATCH (c:Column {column_id: "COL_CUSTOMER_STATUS_562"})
MERGE (c)-[:HAS_VALIDATION]->(v44);

// CUSTOMER_STATUS - ENUM
MERGE (v45:ValidationRule {rule_id: "VAL_CUSTOMER_STATUS_002"})
SET v45.rule_name = "CUSTOMER_STATUS Domain Check",
    v45.column_name = "CUSTOMER_STATUS",
    v45.table_id = "DIM_CUSTOMER",
    v45.validation_type = "ENUM",
    v45.validation_rule = "ENUM:Active,Inactive,Suspended,Closed",
    v45.error_message = "Invalid customer status",
    v45.business_criticality = "critical",
    v45.priority = "P1",
    v45.applicable_domains = ["ALL"],
    v45.is_active = true,
    v45.created_date = datetime(),
    v45.last_updated_date = datetime()
WITH v45
MATCH (c:Column {column_id: "COL_CUSTOMER_STATUS_562"})
MERGE (c)-[:HAS_VALIDATION]->(v45);

// ========================================
// PRIVACY AND CONSENT VALIDATIONS
// ========================================

// DATA_SHARING_CONSENT - NOT NULL
MERGE (v46:ValidationRule {rule_id: "VAL_DATA_SHARING_CONSENT_001"})
SET v46.rule_name = "DATA_SHARING_CONSENT Not Null Check",
    v46.column_name = "DATA_SHARING_CONSENT",
    v46.table_id = "DIM_CUSTOMER",
    v46.validation_type = "NOT_NULL",
    v46.validation_rule = "NOT_NULL",
    v46.error_message = "Data sharing consent is required",
    v46.business_criticality = "high",
    v46.priority = "P1",
    v46.applicable_domains = ["ALL"],
    v46.is_active = true,
    v46.created_date = datetime(),
    v46.last_updated_date = datetime()
WITH v46
MATCH (c:Column {column_id: "COL_DATA_SHARING_CONSENT_558"})
MERGE (c)-[:HAS_VALIDATION]->(v46);

// PRIVACY_PREFERENCE - ENUM
MERGE (v47:ValidationRule {rule_id: "VAL_PRIVACY_PREFERENCE_001"})
SET v47.rule_name = "PRIVACY_PREFERENCE Domain Check",
    v47.column_name = "PRIVACY_PREFERENCE",
    v47.table_id = "DIM_CUSTOMER",
    v47.validation_type = "ENUM",
    v47.validation_rule = "ENUM:High,Medium,Low",
    v47.error_message = "Invalid privacy preference level",
    v47.business_criticality = "medium",
    v47.priority = "P2",
    v47.applicable_domains = ["ALL"],
    v47.is_active = true,
    v47.created_date = datetime(),
    v47.last_updated_date = datetime()
WITH v47
MATCH (c:Column {column_id: "COL_PRIVACY_PREFERENCE_559"})
MERGE (c)-[:HAS_VALIDATION]->(v47);

// ========================================
// CROSS-FIELD VALIDATIONS
// ========================================

// AGE vs AGE_VERIFICATION_STATUS
MERGE (v48:ValidationRule {rule_id: "VAL_AGE_VERIFICATION_001"})
SET v48.rule_name = "Age Verification Consistency Check",
    v48.column_name = "AGE,AGE_VERIFICATION_STATUS",
    v48.table_id = "DIM_CUSTOMER",
    v48.validation_type = "CUSTOM",
    v48.validation_rule = "CUSTOM:IF(AGE>=21,AGE_VERIFICATION_STATUS='Verified')",
    v48.error_message = "Customers 21+ must have verified age status for alcoholic beverages",
    v48.business_criticality = "critical",
    v48.priority = "P1",
    v48.applicable_domains = ["alcoholic_beverages"],
    v48.is_active = true,
    v48.created_date = datetime(),
    v48.last_updated_date = datetime()
WITH v48
MATCH (t:Table {table_id: "DIM_CUSTOMER"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v48);

// LOYALTY_PROGRAM_MEMBER vs LOYALTY_POINTS_BALANCE
MERGE (v49:ValidationRule {rule_id: "VAL_LOYALTY_POINTS_001"})
SET v49.rule_name = "Loyalty Points Consistency Check",
    v49.column_name = "LOYALTY_PROGRAM_MEMBER,LOYALTY_POINTS_BALANCE",
    v49.table_id = "DIM_CUSTOMER",
    v49.validation_type = "CUSTOM",
    v49.validation_rule = "CUSTOM:IF(LOYALTY_PROGRAM_MEMBER=false,LOYALTY_POINTS_BALANCE=0)",
    v49.error_message = "Non-members cannot have loyalty points",
    v49.business_criticality = "medium",
    v49.priority = "P2",
    v49.applicable_domains = ["ALL"],
    v49.is_active = true,
    v49.created_date = datetime(),
    v49.last_updated_date = datetime()
WITH v49
MATCH (t:Table {table_id: "DIM_CUSTOMER"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v49);

// CHILDREN_IN_HOUSEHOLD vs HOUSEHOLD_SIZE
MERGE (v50:ValidationRule {rule_id: "VAL_HOUSEHOLD_LOGIC_001"})
SET v50.rule_name = "Household Composition Check",
    v50.column_name = "CHILDREN_IN_HOUSEHOLD,HOUSEHOLD_SIZE",
    v50.table_id = "DIM_CUSTOMER",
    v50.validation_type = "CUSTOM",
    v50.validation_rule = "CUSTOM:CHILDREN_IN_HOUSEHOLD<HOUSEHOLD_SIZE",
    v50.error_message = "Number of children cannot exceed household size",
    v50.business_criticality = "medium",
    v50.priority = "P2",
    v50.applicable_domains = ["ALL"],
    v50.is_active = true,
    v50.created_date = datetime(),
    v50.last_updated_date = datetime()
WITH v50
MATCH (t:Table {table_id: "DIM_CUSTOMER"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v50);

// PARENT_FLAG vs CHILDREN_IN_HOUSEHOLD
MERGE (v51:ValidationRule {rule_id: "VAL_PARENT_CHILDREN_001"})
SET v51.rule_name = "Parent Flag Consistency Check",
    v51.column_name = "PARENT_FLAG,CHILDREN_IN_HOUSEHOLD",
    v51.table_id = "DIM_CUSTOMER",
    v51.validation_type = "CUSTOM",
    v51.validation_rule = "CUSTOM:IF(PARENT_FLAG=true,CHILDREN_IN_HOUSEHOLD>0)",
    v51.error_message = "Parents must have at least one child in household",
    v51.business_criticality = "medium",
    v51.priority = "P3",
    v51.applicable_domains = ["toys", "baby_products"],
    v51.is_active = true,
    v51.created_date = datetime(),
    v51.last_updated_date = datetime()
WITH v51
MATCH (t:Table {table_id: "DIM_CUSTOMER"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v51);

// FIRST_PURCHASE_DATE vs LAST_PURCHASE_DATE
MERGE (v52:ValidationRule {rule_id: "VAL_PURCHASE_DATE_001"})
SET v52.rule_name = "Purchase Date Consistency Check",
    v52.column_name = "FIRST_PURCHASE_DATE,LAST_PURCHASE_DATE",
    v52.table_id = "DIM_CUSTOMER",
    v52.validation_type = "DATE_COMPARISON",
    v52.validation_rule = "DATE_COMPARE:FIRST_PURCHASE_DATE<=LAST_PURCHASE_DATE",
    v52.error_message = "First purchase date must be before or equal to last purchase date",
    v52.business_criticality = "high",
    v52.priority = "P2",
    v52.applicable_domains = ["ALL"],
    v52.is_active = true,
    v52.created_date = datetime(),
    v52.last_updated_date = datetime()
WITH v52
MATCH (t:Table {table_id: "DIM_CUSTOMER"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v52);

// EMAIL_OPT_IN vs EMAIL_ADDRESS
MERGE (v53:ValidationRule {rule_id: "VAL_EMAIL_OPTIN_001"})
SET v53.rule_name = "Email Opt-In Consistency Check",
    v53.column_name = "EMAIL_OPT_IN,EMAIL_ADDRESS",
    v53.table_id = "DIM_CUSTOMER",
    v53.validation_type = "CUSTOM",
    v53.validation_rule = "CUSTOM:IF(EMAIL_OPT_IN=true,EMAIL_ADDRESS IS NOT NULL)",
    v53.error_message = "Email opt-in requires valid email address",
    v53.business_criticality = "high",
    v53.priority = "P2",
    v53.applicable_domains = ["ALL"],
    v53.is_active = true,
    v53.created_date = datetime(),
    v53.last_updated_date = datetime()
WITH v53
MATCH (t:Table {table_id: "DIM_CUSTOMER"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v53);

// ========================================
// QUERY TO VERIFY CREATED VALIDATIONS
// ========================================

// Count validations by type
MATCH (v:ValidationRule {table_id: "DIM_CUSTOMER"})
RETURN v.validation_type as Type, count(*) as Count
ORDER BY Count DESC;

// Count validations by priority
MATCH (v:ValidationRule {table_id: "DIM_CUSTOMER"})
RETURN v.priority as Priority, count(*) as Count
ORDER BY Priority;

// Count domain-specific validations
MATCH (v:ValidationRule {table_id: "DIM_CUSTOMER"})
WHERE v.applicable_domains <> ["ALL"]
RETURN v.applicable_domains as Domains, count(*) as Count
ORDER BY Count DESC;