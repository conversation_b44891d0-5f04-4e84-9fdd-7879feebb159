rule_id,rule_name,column_name,table_id,validation_type,validation_rule,error_message,business_criticality,priority,applicable_domains,is_active
VAL_INVENTORY_SNAPSHOT_ID_001,INVENTORY_SNAPSHOT_ID Not Null Check,INVENTORY_SNAPSHOT_ID,FACT_INVENTORY,NOT_NULL,NOT_NULL,Inventory Snapshot ID is required,critical,P1,ALL,true
VAL_INVENTORY_SNAPSHOT_ID_002,INVENTORY_SNAPSHOT_ID Uniqueness Check,INVENTORY_SNAPSHOT_ID,FACT_INVENTORY,UNIQUE,UNIQUE,Inventory Snapshot ID must be unique,critical,P1,ALL,true
VAL_INVENTORY_SNAPSHOT_ID_003,INVENTORY_SNAPSHOT_ID Format Check,INVENTORY_SNAPSHOT_ID,FACT_INVENTORY,REG<PERSON>,^[A-Za-z0-9_-]{1\,50}$,Inventory Snapshot ID must be alphanumeric with dash/underscore max 50 chars,critical,P1,ALL,true
VAL_DATE_KEY_001,DATE_KEY Not Null Check,DATE_KEY,FACT_INVENTORY,NOT_NULL,NOT_NULL,Date Key is required,critical,P1,ALL,true
VAL_DATE_KEY_002,DATE_KEY Reference Check,DATE_KEY,FACT_INVENTORY,REFERENCE,FK:DIM_DATE.DATE_KEY,Date Key must exist in Date dimension,critical,P1,ALL,true
VAL_STORE_KEY_001,STORE_KEY Not Null Check,STORE_KEY,FACT_INVENTORY,NOT_NULL,NOT_NULL,Store Key is required,critical,P1,ALL,true
VAL_STORE_KEY_002,STORE_KEY Reference Check,STORE_KEY,FACT_INVENTORY,REFERENCE,FK:DIM_STORE.STORE_KEY,Store Key must exist in Store dimension,critical,P1,ALL,true
VAL_PRODUCT_HIERARCHY_KEY_001,PRODUCT_HIERARCHY_KEY Not Null Check,PRODUCT_HIERARCHY_KEY,FACT_INVENTORY,NOT_NULL,NOT_NULL,Product Hierarchy Key is required,critical,P1,ALL,true
VAL_PRODUCT_HIERARCHY_KEY_002,PRODUCT_HIERARCHY_KEY Reference Check,PRODUCT_HIERARCHY_KEY,FACT_INVENTORY,REFERENCE,FK:DIM_PRODUCT_HIERARCHY.PRODUCT_HIERARCHY_KEY,Product Hierarchy Key must exist in Product Hierarchy dimension,critical,P1,ALL,true
VAL_SUPPLIER_KEY_001,SUPPLIER_KEY Reference Check,SUPPLIER_KEY,FACT_INVENTORY,REFERENCE_NULLABLE,FK_NULLABLE:DIM_SUPPLIER.SUPPLIER_KEY,Supplier Key must exist in Supplier dimension if provided,high,P2,ALL,true
VAL_ON_HAND_QUANTITY_001,ON_HAND_QUANTITY Not Null Check,ON_HAND_QUANTITY,FACT_INVENTORY,NOT_NULL,NOT_NULL,On Hand Quantity is required,critical,P1,ALL,true
VAL_ON_HAND_QUANTITY_002,ON_HAND_QUANTITY Range Check,ON_HAND_QUANTITY,FACT_INVENTORY,RANGE,RANGE:0\,999999999.999,On Hand Quantity must be non-negative,critical,P1,ALL,true
VAL_AVAILABLE_QUANTITY_001,AVAILABLE_QUANTITY Not Null Check,AVAILABLE_QUANTITY,FACT_INVENTORY,NOT_NULL,NOT_NULL,Available Quantity is required,critical,P1,ALL,true
VAL_AVAILABLE_QUANTITY_002,AVAILABLE_QUANTITY Range Check,AVAILABLE_QUANTITY,FACT_INVENTORY,RANGE,RANGE:0\,999999999.999,Available Quantity must be non-negative,critical,P1,ALL,true
VAL_ALLOCATED_QUANTITY_001,ALLOCATED_QUANTITY Range Check,ALLOCATED_QUANTITY,FACT_INVENTORY,RANGE,RANGE:0\,999999999.999,Allocated Quantity must be non-negative,high,P1,ALL,true
VAL_IN_TRANSIT_QUANTITY_001,IN_TRANSIT_QUANTITY Range Check,IN_TRANSIT_QUANTITY,FACT_INVENTORY,RANGE,RANGE:0\,999999999.999,In Transit Quantity must be non-negative,high,P1,ALL,true
VAL_INVENTORY_VALUE_001,INVENTORY_VALUE Not Null Check,INVENTORY_VALUE,FACT_INVENTORY,NOT_NULL,NOT_NULL,Inventory Value is required,critical,P1,ALL,true
VAL_INVENTORY_VALUE_002,INVENTORY_VALUE Range Check,INVENTORY_VALUE,FACT_INVENTORY,RANGE,RANGE:0\,999999999999.99,Inventory Value must be non-negative,critical,P1,ALL,true
VAL_UNIT_COST_001,UNIT_COST Not Null Check,UNIT_COST,FACT_INVENTORY,NOT_NULL,NOT_NULL,Unit Cost is required,high,P1,ALL,true
VAL_UNIT_COST_002,UNIT_COST Range Check,UNIT_COST,FACT_INVENTORY,RANGE,RANGE:0\,99999999.9999,Unit Cost must be non-negative,high,P1,ALL,true
VAL_LANDED_COST_001,LANDED_COST Range Check,LANDED_COST,FACT_INVENTORY,RANGE,RANGE:0\,99999999.9999,Landed Cost must be non-negative,high,P1,ALL,true
VAL_INVENTORY_TURNS_001,INVENTORY_TURNS Range Check,INVENTORY_TURNS,FACT_INVENTORY,RANGE,RANGE:0\,999.99,Inventory Turns must be non-negative,critical,P1,ALL,true
VAL_DAYS_ON_HAND_001,DAYS_ON_HAND Range Check,DAYS_ON_HAND,FACT_INVENTORY,RANGE,RANGE:0\,9999.9,Days On Hand must be non-negative,critical,P1,ALL,true
VAL_VELOCITY_SCORE_001,VELOCITY_SCORE Range Check,VELOCITY_SCORE,FACT_INVENTORY,RANGE,RANGE:0\,100,Velocity Score must be between 0 and 100,high,P1,ALL,true
VAL_SELL_THROUGH_RATE_001,SELL_THROUGH_RATE Range Check,SELL_THROUGH_RATE,FACT_INVENTORY,RANGE,RANGE:0\,100,Sell Through Rate must be between 0 and 100,high,P1,ALL,true
VAL_SAFETY_STOCK_QUANTITY_001,SAFETY_STOCK_QUANTITY Range Check,SAFETY_STOCK_QUANTITY,FACT_INVENTORY,RANGE,RANGE:0\,999999999.999,Safety Stock Quantity must be non-negative,high,P1,ALL,true
VAL_REORDER_POINT_QUANTITY_001,REORDER_POINT_QUANTITY Range Check,REORDER_POINT_QUANTITY,FACT_INVENTORY,RANGE,RANGE:0\,999999999.999,Reorder Point Quantity must be non-negative,high,P1,ALL,true
VAL_STOCKOUT_FLAG_001,STOCKOUT_FLAG Boolean Check,STOCKOUT_FLAG,FACT_INVENTORY,BOOLEAN,BOOLEAN,Stockout Flag must be TRUE or FALSE,critical,P1,ALL,true
VAL_INVENTORY_AGE_DAYS_001,INVENTORY_AGE_DAYS Range Check,INVENTORY_AGE_DAYS,FACT_INVENTORY,RANGE,RANGE:0\,99999,Inventory Age Days must be non-negative,high,P1,ALL,true
VAL_DAYS_UNTIL_EXPIRATION_001,DAYS_UNTIL_EXPIRATION Range Check,DAYS_UNTIL_EXPIRATION,FACT_INVENTORY,RANGE,RANGE:-9999\,99999,Days Until Expiration out of range,critical,P1,"pharmaceuticals,food_beverage,personal_care,pet_food,snacks,dairy,frozen_foods,beverages,health_supplements,baby_products,cosmetics",true
VAL_EXPIRATION_RISK_FLAG_001,EXPIRATION_RISK_FLAG Boolean Check,EXPIRATION_RISK_FLAG,FACT_INVENTORY,BOOLEAN,BOOLEAN,Expiration Risk Flag must be TRUE or FALSE,high,P1,"pharmaceuticals,food_beverage,personal_care,pet_food,snacks,dairy,frozen_foods,beverages,health_supplements,baby_products,cosmetics",true
VAL_WAREHOUSE_CAPACITY_UTIL_001,WAREHOUSE_CAPACITY_UTILIZATION Range Check,WAREHOUSE_CAPACITY_UTILIZATION,FACT_INVENTORY,RANGE,RANGE:0\,100,Warehouse Capacity Utilization must be between 0 and 100,medium,P2,ALL,true
VAL_LOCATION_COUNT_001,LOCATION_COUNT Range Check,LOCATION_COUNT,FACT_INVENTORY,RANGE,RANGE:0\,9999,Location Count must be non-negative,low,P3,ALL,true
VAL_BONDED_INVENTORY_QTY_001,BONDED_INVENTORY_QUANTITY Not Null Check,BONDED_INVENTORY_QUANTITY,FACT_INVENTORY,NOT_NULL,NOT_NULL,Bonded Inventory Quantity is required for alcoholic beverages,high,P1,alcoholic_beverages,true
VAL_BONDED_INVENTORY_QTY_002,BONDED_INVENTORY_QUANTITY Range Check,BONDED_INVENTORY_QUANTITY,FACT_INVENTORY,RANGE,RANGE:0\,999999999.999,Bonded Inventory Quantity must be non-negative,high,P1,alcoholic_beverages,true
VAL_VINTAGE_YEAR_001,VINTAGE_YEAR Range Check,VINTAGE_YEAR,FACT_INVENTORY,RANGE,RANGE:1800\,2100,Vintage Year must be between 1800 and 2100,medium,P2,alcoholic_beverages,true
VAL_PROOF_GALLONS_001,PROOF_GALLONS Not Null Check,PROOF_GALLONS,FACT_INVENTORY,NOT_NULL,NOT_NULL,Proof Gallons is required for alcoholic beverages,critical,P1,alcoholic_beverages,true
VAL_PROOF_GALLONS_002,PROOF_GALLONS Range Check,PROOF_GALLONS,FACT_INVENTORY,RANGE,RANGE:0\,999999999.999,Proof Gallons must be non-negative,critical,P1,alcoholic_beverages,true
VAL_LOT_NUMBER_001,LOT_NUMBER Not Null Check,LOT_NUMBER,FACT_INVENTORY,NOT_NULL,NOT_NULL,Lot Number is required for regulated products,critical,P1,"pharmaceuticals,food_beverage,cosmetics,personal_care,health_supplements,baby_products",true
VAL_LOT_NUMBER_002,LOT_NUMBER Format Check,LOT_NUMBER,FACT_INVENTORY,REGEX,^[A-Za-z0-9-]{1\,50}$,Lot Number must be alphanumeric with dash max 50 chars,critical,P1,"pharmaceuticals,food_beverage,cosmetics,personal_care,health_supplements,baby_products",true
VAL_CONTROLLED_SUBSTANCE_FLAG_001,CONTROLLED_SUBSTANCE_FLAG Boolean Check,CONTROLLED_SUBSTANCE_FLAG,FACT_INVENTORY,BOOLEAN,BOOLEAN,Controlled Substance Flag must be TRUE or FALSE,critical,P1,pharmaceuticals,true
VAL_COLD_CHAIN_FLAG_001,COLD_CHAIN_FLAG Boolean Check,COLD_CHAIN_FLAG,FACT_INVENTORY,BOOLEAN,BOOLEAN,Cold Chain Flag must be TRUE or FALSE,critical,P1,"pharmaceuticals,food_beverage,dairy,frozen_foods,health_supplements",true
VAL_QUALITY_HOLD_QUANTITY_001,QUALITY_HOLD_QUANTITY Range Check,QUALITY_HOLD_QUANTITY,FACT_INVENTORY,RANGE,RANGE:0\,999999999.999,Quality Hold Quantity must be non-negative,high,P1,ALL,true
VAL_QUARANTINE_QUANTITY_001,QUARANTINE_QUANTITY Range Check,QUARANTINE_QUANTITY,FACT_INVENTORY,RANGE,RANGE:0\,999999999.999,Quarantine Quantity must be non-negative,critical,P1,ALL,true
VAL_DAMAGED_QUANTITY_001,DAMAGED_QUANTITY Range Check,DAMAGED_QUANTITY,FACT_INVENTORY,RANGE,RANGE:0\,999999999.999,Damaged Quantity must be non-negative,medium,P2,ALL,true
VAL_FILL_RATE_PERCENTAGE_001,FILL_RATE_PERCENTAGE Range Check,FILL_RATE_PERCENTAGE,FACT_INVENTORY,RANGE,RANGE:0\,100,Fill Rate Percentage must be between 0 and 100,critical,P1,ALL,true
VAL_PERFECT_ORDER_PERCENTAGE_001,PERFECT_ORDER_PERCENTAGE Range Check,PERFECT_ORDER_PERCENTAGE,FACT_INVENTORY,RANGE,RANGE:0\,100,Perfect Order Percentage must be between 0 and 100,high,P1,ALL,true
VAL_FORECAST_ACCURACY_001,FORECAST_ACCURACY Range Check,FORECAST_ACCURACY,FACT_INVENTORY,RANGE,RANGE:0\,100,Forecast Accuracy must be between 0 and 100,high,P1,ALL,true
VAL_INVENTORY_ACCURACY_PCT_001,INVENTORY_ACCURACY_PERCENTAGE Range Check,INVENTORY_ACCURACY_PERCENTAGE,FACT_INVENTORY,RANGE,RANGE:0\,100,Inventory Accuracy Percentage must be between 0 and 100,high,P1,ALL,true
VAL_INVENTORY_STATUS_001,INVENTORY_STATUS Not Null Check,INVENTORY_STATUS,FACT_INVENTORY,NOT_NULL,NOT_NULL,Inventory Status is required,high,P1,ALL,true
VAL_INVENTORY_STATUS_002,INVENTORY_STATUS Domain Check,INVENTORY_STATUS,FACT_INVENTORY,ENUM,ENUM:Active\,Hold\,Obsolete\,Expired\,Quarantine\,Damaged,Invalid inventory status,high,P1,ALL,true
VAL_ABC_CLASSIFICATION_001,ABC_CLASSIFICATION Domain Check,ABC_CLASSIFICATION,FACT_INVENTORY,ENUM,ENUM:A\,B\,C,ABC Classification must be A B or C,medium,P2,ALL,true
VAL_QUANTITY_CONSISTENCY_001,On Hand vs Available Quantity Check,"ON_HAND_QUANTITY,AVAILABLE_QUANTITY,ALLOCATED_QUANTITY",FACT_INVENTORY,CUSTOM,CUSTOM:AVAILABLE_QUANTITY<=ON_HAND_QUANTITY,Available quantity cannot exceed on-hand quantity,critical,P1,ALL,true
VAL_QUANTITY_CONSISTENCY_002,Allocated vs On Hand Quantity Check,"ON_HAND_QUANTITY,ALLOCATED_QUANTITY",FACT_INVENTORY,CUSTOM,CUSTOM:ALLOCATED_QUANTITY<=ON_HAND_QUANTITY,Allocated quantity cannot exceed on-hand quantity,critical,P1,ALL,true
VAL_VALUE_CALCULATION_001,Inventory Value Calculation Check,"INVENTORY_VALUE,ON_HAND_QUANTITY,UNIT_COST",FACT_INVENTORY,CUSTOM,CUSTOM:ABS(INVENTORY_VALUE-(ON_HAND_QUANTITY*UNIT_COST))<0.01,Inventory value does not match quantity times unit cost,critical,P1,ALL,true
VAL_REORDER_SAFETY_001,Reorder Point vs Safety Stock Check,"REORDER_POINT_QUANTITY,SAFETY_STOCK_QUANTITY",FACT_INVENTORY,CUSTOM,CUSTOM:REORDER_POINT_QUANTITY>=SAFETY_STOCK_QUANTITY,Reorder point should be greater than or equal to safety stock,high,P2,ALL,true
VAL_STOCKOUT_CONSISTENCY_001,Stockout Flag Consistency Check,"STOCKOUT_FLAG,ON_HAND_QUANTITY",FACT_INVENTORY,CUSTOM,CUSTOM:IF(STOCKOUT_FLAG=TRUE\,ON_HAND_QUANTITY=0),Stockout flag inconsistent with on-hand quantity,critical,P1,ALL,true
VAL_EXPIRATION_CONSISTENCY_001,Expiration Risk Consistency Check,"EXPIRATION_RISK_FLAG,DAYS_UNTIL_EXPIRATION",FACT_INVENTORY,CUSTOM,CUSTOM:IF(EXPIRATION_RISK_FLAG=TRUE\,DAYS_UNTIL_EXPIRATION<=30),Expiration risk flag inconsistent with days until expiration,high,P1,"pharmaceuticals,food_beverage,personal_care,pet_food,snacks,dairy,frozen_foods,beverages,health_supplements,baby_products,cosmetics",true
VAL_QUALITY_TOTAL_001,Quality Hold Total Check,"ON_HAND_QUANTITY,QUALITY_HOLD_QUANTITY,QUARANTINE_QUANTITY,DAMAGED_QUANTITY",FACT_INVENTORY,CUSTOM,CUSTOM:(QUALITY_HOLD_QUANTITY+QUARANTINE_QUANTITY+DAMAGED_QUANTITY)<=ON_HAND_QUANTITY,Total problematic inventory exceeds on-hand quantity,high,P1,ALL,true
VAL_COLD_CHAIN_EXPIRY_001,Cold Chain Product Expiration Check,"COLD_CHAIN_FLAG,DAYS_UNTIL_EXPIRATION",FACT_INVENTORY,CUSTOM,CUSTOM:IF(COLD_CHAIN_FLAG=TRUE\,DAYS_UNTIL_EXPIRATION!=NULL),Cold chain products must have expiration tracking,critical,P1,"pharmaceuticals,food_beverage,dairy,frozen_foods,health_supplements",true