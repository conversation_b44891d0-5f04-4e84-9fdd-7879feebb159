rule_id,rule_name,involved_columns,table_id,validation_rule,error_message,business_criticality,priority,applicable_domains,is_active
VAL_NET_GROSS_SALES_001,Net Sales vs Gross Sales Check,"NET_SALES_AMOUNT,GROSS_SALES_AMOUNT",FACT_SALES,NET_SALES_AMOUNT <= GROSS_SALES_AMOUNT,Net Sales Amount cannot exceed Gross Sales Amount,critical,P1,ALL,TRUE
VAL_TOTAL_DISCOUNT_SUM_001,Total Discount Sum Check,"TOTAL_DISCOUNT_AMOUNT,PROMOTIONAL_DISCOUNT_AMOUNT,TRADE_ALLOWANCE_AMOUNT,LOYALTY_DISCOUNT_AMOUNT,COUPON_DISCOUNT_AMOUNT",FACT_SALES,TOTAL_DISCOUNT_AMOUNT = COALESCE(PROMOTIONAL_DISCOUNT_AMOUNT,0) + COALESCE(TRADE_ALLOWANCE_AMOUNT,0) + COALESCE(LOYALTY_DISCOUNT_AMOUNT,0) + COALESCE(COUPON_DISCOUNT_AMOUNT,0),Total Discount Amount should equal sum of individual discounts,high,P1,ALL,TRUE
VAL_RETURN_QTY_LIMIT_001,Return Quantity Limit Check,"RETURN_QUANTITY,QUANTITY_SOLD",FACT_SALES,COALESCE(RETURN_QUANTITY,0) <= QUANTITY_SOLD,Return Quantity cannot exceed Quantity Sold,medium,P2,ALL,TRUE
VAL_GROSS_MARGIN_CALC_001,Gross Margin Calculation Check,"GROSS_MARGIN_AMOUNT,NET_SALES_AMOUNT,COST_OF_GOODS_SOLD",FACT_SALES,ABS(GROSS_MARGIN_AMOUNT - (NET_SALES_AMOUNT - COST_OF_GOODS_SOLD)) < 0.01,Gross Margin Amount should equal Net Sales minus COGS,critical,P1,ALL,TRUE
VAL_AGE_VERIFICATION_REQ_001,Age Verification Required Check,AGE_VERIFICATION_FLAG,FACT_SALES,IF(PRODUCT_CATEGORY='Alcoholic Beverages',AGE_VERIFICATION_FLAG=TRUE),Age verification required for alcoholic beverage sales,critical,P1,alcoholic_beverages,TRUE
VAL_MOBILE_DIGITAL_CONSISTENCY_001,Mobile implies Digital Check,"MOBILE_SALES_FLAG,DIGITAL_SALES_FLAG",FACT_SALES,IF(MOBILE_SALES_FLAG=TRUE,DIGITAL_SALES_FLAG=TRUE),Mobile sales must also be flagged as digital sales,medium,P2,ALL,TRUE
VAL_UNIT_PRICE_CONSISTENCY_001,Unit Price Consistency Check,"UNIT_PRICE,NET_SALES_AMOUNT,QUANTITY_SOLD",FACT_SALES,ABS(UNIT_PRICE - (NET_SALES_AMOUNT / QUANTITY_SOLD)) < 0.01,Unit Price should equal Net Sales Amount divided by Quantity Sold,high,P1,ALL,TRUE
VAL_PRESCRIPTION_REQ_001,Prescription Number Required Check,PRESCRIPTION_NUMBER,FACT_SALES,IF(PRODUCT_CATEGORY='Pharmaceuticals',PRESCRIPTION_NUMBER IS NOT NULL),Prescription number required for pharmaceutical sales,critical,P1,pharmaceuticals,TRUE