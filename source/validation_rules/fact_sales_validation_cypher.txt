// ========================================
// DATA QUALITY VALIDATION RULES FOR FACT_SALES
// This script creates validation rules and links them to columns
// ========================================

// Create constraint for ValidationRule uniqueness (run once)
CREATE CONSTRAINT validation_rule_unique_fact_sales IF NOT EXISTS
FOR (v:ValidationRule) REQUIRE v.rule_id IS UNIQUE;

// ========================================
// CORE TRANSACTION IDENTIFIERS - PRIMARY KEY
// ========================================

// SALES_TRANSACTION_ID - NOT NULL
MERGE (v1:ValidationRule {rule_id: "VAL_SALES_TRANSACTION_ID_001"})
SET v1.rule_name = "SALES_TRANSACTION_ID Not Null Check",
    v1.column_name = "SALES_TRANSACTION_ID",
    v1.table_id = "FACT_SALES",
    v1.validation_type = "NOT_NULL",
    v1.validation_rule = "NOT_NULL",
    v1.error_message = "Sales Transaction ID is required",
    v1.business_criticality = "critical",
    v1.priority = "P1",
    v1.applicable_domains = ["ALL"],
    v1.is_active = true,
    v1.created_date = datetime(),
    v1.last_updated_date = datetime()
WITH v1
MATCH (c:Column {column_id: "COL_SALES_TRANSACTION_ID_FACT_901"})
MERGE (c)-[:HAS_VALIDATION]->(v1);

// SALES_TRANSACTION_ID - UNIQUE
MERGE (v2:ValidationRule {rule_id: "VAL_SALES_TRANSACTION_ID_002"})
SET v2.rule_name = "SALES_TRANSACTION_ID Uniqueness Check",
    v2.column_name = "SALES_TRANSACTION_ID",
    v2.table_id = "FACT_SALES",
    v2.validation_type = "UNIQUE",
    v2.validation_rule = "UNIQUE",
    v2.error_message = "Sales Transaction ID must be unique",
    v2.business_criticality = "critical",
    v2.priority = "P1",
    v2.applicable_domains = ["ALL"],
    v2.is_active = true,
    v2.created_date = datetime(),
    v2.last_updated_date = datetime()
WITH v2
MATCH (c:Column {column_id: "COL_SALES_TRANSACTION_ID_FACT_901"})
MERGE (c)-[:HAS_VALIDATION]->(v2);

// SALES_TRANSACTION_ID - FORMAT
MERGE (v3:ValidationRule {rule_id: "VAL_SALES_TRANSACTION_ID_003"})
SET v3.rule_name = "SALES_TRANSACTION_ID Format Check",
    v3.column_name = "SALES_TRANSACTION_ID",
    v3.table_id = "FACT_SALES",
    v3.validation_type = "REGEX",
    v3.validation_rule = "^[A-Za-z0-9_-]{1,50}$",
    v3.error_message = "Sales Transaction ID must be alphanumeric with dash/underscore, max 50 chars",
    v3.business_criticality = "critical",
    v3.priority = "P1",
    v3.applicable_domains = ["ALL"],
    v3.is_active = true,
    v3.created_date = datetime(),
    v3.last_updated_date = datetime()
WITH v3
MATCH (c:Column {column_id: "COL_SALES_TRANSACTION_ID_FACT_901"})
MERGE (c)-[:HAS_VALIDATION]->(v3);

// ========================================
// FOREIGN KEY VALIDATIONS
// ========================================

// DATE_KEY - NOT NULL
MERGE (v4:ValidationRule {rule_id: "VAL_DATE_KEY_001"})
SET v4.rule_name = "DATE_KEY Not Null Check",
    v4.column_name = "DATE_KEY",
    v4.table_id = "FACT_SALES",
    v4.validation_type = "NOT_NULL",
    v4.validation_rule = "NOT_NULL",
    v4.error_message = "Date Key is required",
    v4.business_criticality = "critical",
    v4.priority = "P1",
    v4.applicable_domains = ["ALL"],
    v4.is_active = true,
    v4.created_date = datetime(),
    v4.last_updated_date = datetime()
WITH v4
MATCH (c:Column {column_id: "COL_DATE_KEY_FACT_902"})
MERGE (c)-[:HAS_VALIDATION]->(v4);

// DATE_KEY - RANGE (Valid date range)
MERGE (v5:ValidationRule {rule_id: "VAL_DATE_KEY_002"})
SET v5.rule_name = "DATE_KEY Range Check",
    v5.column_name = "DATE_KEY",
    v5.table_id = "FACT_SALES",
    v5.validation_type = "RANGE",
    v5.validation_rule = "RANGE:20000101,20301231",
    v5.error_message = "Date Key must be between 20000101 and 20301231",
    v5.business_criticality = "critical",
    v5.priority = "P1",
    v5.applicable_domains = ["ALL"],
    v5.is_active = true,
    v5.created_date = datetime(),
    v5.last_updated_date = datetime()
WITH v5
MATCH (c:Column {column_id: "COL_DATE_KEY_FACT_902"})
MERGE (c)-[:HAS_VALIDATION]->(v5);

// STORE_KEY - NOT NULL
MERGE (v6:ValidationRule {rule_id: "VAL_STORE_KEY_001"})
SET v6.rule_name = "STORE_KEY Not Null Check",
    v6.column_name = "STORE_KEY",
    v6.table_id = "FACT_SALES",
    v6.validation_type = "NOT_NULL",
    v6.validation_rule = "NOT_NULL",
    v6.error_message = "Store Key is required",
    v6.business_criticality = "critical",
    v6.priority = "P1",
    v6.applicable_domains = ["ALL"],
    v6.is_active = true,
    v6.created_date = datetime(),
    v6.last_updated_date = datetime()
WITH v6
MATCH (c:Column {column_id: "COL_STORE_KEY_FACT_903"})
MERGE (c)-[:HAS_VALIDATION]->(v6);

// PRODUCT_HIERARCHY_KEY - NOT NULL
MERGE (v7:ValidationRule {rule_id: "VAL_PRODUCT_HIERARCHY_KEY_001"})
SET v7.rule_name = "PRODUCT_HIERARCHY_KEY Not Null Check",
    v7.column_name = "PRODUCT_HIERARCHY_KEY",
    v7.table_id = "FACT_SALES",
    v7.validation_type = "NOT_NULL",
    v7.validation_rule = "NOT_NULL",
    v7.error_message = "Product Hierarchy Key is required",
    v7.business_criticality = "critical",
    v7.priority = "P1",
    v7.applicable_domains = ["ALL"],
    v7.is_active = true,
    v7.created_date = datetime(),
    v7.last_updated_date = datetime()
WITH v7
MATCH (c:Column {column_id: "COL_PRODUCT_HIERARCHY_KEY_FACT_905"})
MERGE (c)-[:HAS_VALIDATION]->(v7);

// ========================================
// CORE QUANTITY MEASURES VALIDATIONS
// ========================================

// QUANTITY_SOLD - NOT NULL
MERGE (v8:ValidationRule {rule_id: "VAL_QUANTITY_SOLD_001"})
SET v8.rule_name = "QUANTITY_SOLD Not Null Check",
    v8.column_name = "QUANTITY_SOLD",
    v8.table_id = "FACT_SALES",
    v8.validation_type = "NOT_NULL",
    v8.validation_rule = "NOT_NULL",
    v8.error_message = "Quantity Sold is required",
    v8.business_criticality = "critical",
    v8.priority = "P1",
    v8.applicable_domains = ["ALL"],
    v8.is_active = true,
    v8.created_date = datetime(),
    v8.last_updated_date = datetime()
WITH v8
MATCH (c:Column {column_id: "COL_QUANTITY_SOLD_FACT_908"})
MERGE (c)-[:HAS_VALIDATION]->(v8);

// QUANTITY_SOLD - RANGE (Positive values)
MERGE (v9:ValidationRule {rule_id: "VAL_QUANTITY_SOLD_002"})
SET v9.rule_name = "QUANTITY_SOLD Range Check",
    v9.column_name = "QUANTITY_SOLD",
    v9.table_id = "FACT_SALES",
    v9.validation_type = "RANGE",
    v9.validation_rule = "RANGE:0.001,999999.999",
    v9.error_message = "Quantity Sold must be positive",
    v9.business_criticality = "critical",
    v9.priority = "P1",
    v9.applicable_domains = ["ALL"],
    v9.is_active = true,
    v9.created_date = datetime(),
    v9.last_updated_date = datetime()
WITH v9
MATCH (c:Column {column_id: "COL_QUANTITY_SOLD_FACT_908"})
MERGE (c)-[:HAS_VALIDATION]->(v9);

// CASES_SOLD - RANGE (Non-negative)
MERGE (v10:ValidationRule {rule_id: "VAL_CASES_SOLD_001"})
SET v10.rule_name = "CASES_SOLD Range Check",
    v10.column_name = "CASES_SOLD",
    v10.table_id = "FACT_SALES",
    v10.validation_type = "RANGE",
    v10.validation_rule = "RANGE:0,99999.999",
    v10.error_message = "Cases Sold must be non-negative",
    v10.business_criticality = "medium",
    v10.priority = "P2",
    v10.applicable_domains = ["ALL"],
    v10.is_active = true,
    v10.created_date = datetime(),
    v10.last_updated_date = datetime()
WITH v10
MATCH (c:Column {column_id: "COL_CASES_SOLD_FACT_909"})
MERGE (c)-[:HAS_VALIDATION]->(v10);

// WEIGHT_SOLD - RANGE (Non-negative)
MERGE (v11:ValidationRule {rule_id: "VAL_WEIGHT_SOLD_001"})
SET v11.rule_name = "WEIGHT_SOLD Range Check",
    v11.column_name = "WEIGHT_SOLD",
    v11.table_id = "FACT_SALES",
    v11.validation_type = "RANGE",
    v11.validation_rule = "RANGE:0,999999.999",
    v11.error_message = "Weight Sold must be non-negative",
    v11.business_criticality = "medium",
    v11.priority = "P2",
    v11.applicable_domains = ["ALL"],
    v11.is_active = true,
    v11.created_date = datetime(),
    v11.last_updated_date = datetime()
WITH v11
MATCH (c:Column {column_id: "COL_WEIGHT_SOLD_FACT_910"})
MERGE (c)-[:HAS_VALIDATION]->(v11);

// ========================================
// CORE FINANCIAL MEASURES VALIDATIONS
// ========================================

// GROSS_SALES_AMOUNT - NOT NULL
MERGE (v12:ValidationRule {rule_id: "VAL_GROSS_SALES_AMOUNT_001"})
SET v12.rule_name = "GROSS_SALES_AMOUNT Not Null Check",
    v12.column_name = "GROSS_SALES_AMOUNT",
    v12.table_id = "FACT_SALES",
    v12.validation_type = "NOT_NULL",
    v12.validation_rule = "NOT_NULL",
    v12.error_message = "Gross Sales Amount is required",
    v12.business_criticality = "critical",
    v12.priority = "P1",
    v12.applicable_domains = ["ALL"],
    v12.is_active = true,
    v12.created_date = datetime(),
    v12.last_updated_date = datetime()
WITH v12
MATCH (c:Column {column_id: "COL_GROSS_SALES_AMOUNT_FACT_912"})
MERGE (c)-[:HAS_VALIDATION]->(v12);

// GROSS_SALES_AMOUNT - RANGE (Positive values)
MERGE (v13:ValidationRule {rule_id: "VAL_GROSS_SALES_AMOUNT_002"})
SET v13.rule_name = "GROSS_SALES_AMOUNT Range Check",
    v13.column_name = "GROSS_SALES_AMOUNT",
    v13.table_id = "FACT_SALES",
    v13.validation_type = "RANGE",
    v13.validation_rule = "RANGE:0.01,999999999999.99",
    v13.error_message = "Gross Sales Amount must be positive",
    v13.business_criticality = "critical",
    v13.priority = "P1",
    v13.applicable_domains = ["ALL"],
    v13.is_active = true,
    v13.created_date = datetime(),
    v13.last_updated_date = datetime()
WITH v13
MATCH (c:Column {column_id: "COL_GROSS_SALES_AMOUNT_FACT_912"})
MERGE (c)-[:HAS_VALIDATION]->(v13);

// NET_SALES_AMOUNT - NOT NULL
MERGE (v14:ValidationRule {rule_id: "VAL_NET_SALES_AMOUNT_001"})
SET v14.rule_name = "NET_SALES_AMOUNT Not Null Check",
    v14.column_name = "NET_SALES_AMOUNT",
    v14.table_id = "FACT_SALES",
    v14.validation_type = "NOT_NULL",
    v14.validation_rule = "NOT_NULL",
    v14.error_message = "Net Sales Amount is required",
    v14.business_criticality = "critical",
    v14.priority = "P1",
    v14.applicable_domains = ["ALL"],
    v14.is_active = true,
    v14.created_date = datetime(),
    v14.last_updated_date = datetime()
WITH v14
MATCH (c:Column {column_id: "COL_NET_SALES_AMOUNT_FACT_913"})
MERGE (c)-[:HAS_VALIDATION]->(v14);

// NET_SALES_AMOUNT - RANGE (Positive values)
MERGE (v15:ValidationRule {rule_id: "VAL_NET_SALES_AMOUNT_002"})
SET v15.rule_name = "NET_SALES_AMOUNT Range Check",
    v15.column_name = "NET_SALES_AMOUNT",
    v15.table_id = "FACT_SALES",
    v15.validation_type = "RANGE",
    v15.validation_rule = "RANGE:0.01,999999999999.99",
    v15.error_message = "Net Sales Amount must be positive",
    v15.business_criticality = "critical",
    v15.priority = "P1",
    v15.applicable_domains = ["ALL"],
    v15.is_active = true,
    v15.created_date = datetime(),
    v15.last_updated_date = datetime()
WITH v15
MATCH (c:Column {column_id: "COL_NET_SALES_AMOUNT_FACT_913"})
MERGE (c)-[:HAS_VALIDATION]->(v15);

// UNIT_PRICE - RANGE (Positive values)
MERGE (v16:ValidationRule {rule_id: "VAL_UNIT_PRICE_001"})
SET v16.rule_name = "UNIT_PRICE Range Check",
    v16.column_name = "UNIT_PRICE",
    v16.table_id = "FACT_SALES",
    v16.validation_type = "RANGE",
    v16.validation_rule = "RANGE:0.01,999999.99",
    v16.error_message = "Unit Price must be positive",
    v16.business_criticality = "high",
    v16.priority = "P1",
    v16.applicable_domains = ["ALL"],
    v16.is_active = true,
    v16.created_date = datetime(),
    v16.last_updated_date = datetime()
WITH v16
MATCH (c:Column {column_id: "COL_UNIT_PRICE_FACT_915"})
MERGE (c)-[:HAS_VALIDATION]->(v16);

// ========================================
// DISCOUNT AND ALLOWANCE VALIDATIONS
// ========================================

// PROMOTIONAL_DISCOUNT_AMOUNT - RANGE (Non-negative)
MERGE (v17:ValidationRule {rule_id: "VAL_PROMOTIONAL_DISCOUNT_001"})
SET v17.rule_name = "PROMOTIONAL_DISCOUNT_AMOUNT Range Check",
    v17.column_name = "PROMOTIONAL_DISCOUNT_AMOUNT",
    v17.table_id = "FACT_SALES",
    v17.validation_type = "RANGE",
    v17.validation_rule = "RANGE:0,99999999.99",
    v17.error_message = "Promotional Discount Amount must be non-negative",
    v17.business_criticality = "high",
    v17.priority = "P1",
    v17.applicable_domains = ["ALL"],
    v17.is_active = true,
    v17.created_date = datetime(),
    v17.last_updated_date = datetime()
WITH v17
MATCH (c:Column {column_id: "COL_PROMOTIONAL_DISCOUNT_AMOUNT_FACT_916"})
MERGE (c)-[:HAS_VALIDATION]->(v17);

// TOTAL_DISCOUNT_AMOUNT - RANGE (Non-negative)
MERGE (v18:ValidationRule {rule_id: "VAL_TOTAL_DISCOUNT_AMOUNT_001"})
SET v18.rule_name = "TOTAL_DISCOUNT_AMOUNT Range Check",
    v18.column_name = "TOTAL_DISCOUNT_AMOUNT",
    v18.table_id = "FACT_SALES",
    v18.validation_type = "RANGE",
    v18.validation_rule = "RANGE:0,99999999.99",
    v18.error_message = "Total Discount Amount must be non-negative",
    v18.business_criticality = "high",
    v18.priority = "P1",
    v18.applicable_domains = ["ALL"],
    v18.is_active = true,
    v18.created_date = datetime(),
    v18.last_updated_date = datetime()
WITH v18
MATCH (c:Column {column_id: "COL_TOTAL_DISCOUNT_AMOUNT_FACT_920"})
MERGE (c)-[:HAS_VALIDATION]->(v18);

// ========================================
// COST AND PROFITABILITY VALIDATIONS
// ========================================

// COST_OF_GOODS_SOLD - NOT NULL
MERGE (v19:ValidationRule {rule_id: "VAL_COST_OF_GOODS_SOLD_001"})
SET v19.rule_name = "COST_OF_GOODS_SOLD Not Null Check",
    v19.column_name = "COST_OF_GOODS_SOLD",
    v19.table_id = "FACT_SALES",
    v19.validation_type = "NOT_NULL",
    v19.validation_rule = "NOT_NULL",
    v19.error_message = "Cost of Goods Sold is required",
    v19.business_criticality = "critical",
    v19.priority = "P1",
    v19.applicable_domains = ["ALL"],
    v19.is_active = true,
    v19.created_date = datetime(),
    v19.last_updated_date = datetime()
WITH v19
MATCH (c:Column {column_id: "COL_COST_OF_GOODS_SOLD_FACT_921"})
MERGE (c)-[:HAS_VALIDATION]->(v19);

// COST_OF_GOODS_SOLD - RANGE (Non-negative)
MERGE (v20:ValidationRule {rule_id: "VAL_COST_OF_GOODS_SOLD_002"})
SET v20.rule_name = "COST_OF_GOODS_SOLD Range Check",
    v20.column_name = "COST_OF_GOODS_SOLD",
    v20.table_id = "FACT_SALES",
    v20.validation_type = "RANGE",
    v20.validation_rule = "RANGE:0,999999999999.99",
    v20.error_message = "Cost of Goods Sold must be non-negative",
    v20.business_criticality = "critical",
    v20.priority = "P1",
    v20.applicable_domains = ["ALL"],
    v20.is_active = true,
    v20.created_date = datetime(),
    v20.last_updated_date = datetime()
WITH v20
MATCH (c:Column {column_id: "COL_COST_OF_GOODS_SOLD_FACT_921"})
MERGE (c)-[:HAS_VALIDATION]->(v20);

// GROSS_MARGIN_PERCENT - RANGE (0-100%)
MERGE (v21:ValidationRule {rule_id: "VAL_GROSS_MARGIN_PERCENT_001"})
SET v21.rule_name = "GROSS_MARGIN_PERCENT Range Check",
    v21.column_name = "GROSS_MARGIN_PERCENT",
    v21.table_id = "FACT_SALES",
    v21.validation_type = "RANGE",
    v21.validation_rule = "RANGE:-100,100",
    v21.error_message = "Gross Margin Percent must be between -100 and 100",
    v21.business_criticality = "high",
    v21.priority = "P1",
    v21.applicable_domains = ["ALL"],
    v21.is_active = true,
    v21.created_date = datetime(),
    v21.last_updated_date = datetime()
WITH v21
MATCH (c:Column {column_id: "COL_GROSS_MARGIN_PERCENT_FACT_923"})
MERGE (c)-[:HAS_VALIDATION]->(v21);

// ========================================
// TAX AMOUNT VALIDATIONS
// ========================================

// SALES_TAX_AMOUNT - RANGE (Non-negative)
MERGE (v22:ValidationRule {rule_id: "VAL_SALES_TAX_AMOUNT_001"})
SET v22.rule_name = "SALES_TAX_AMOUNT Range Check",
    v22.column_name = "SALES_TAX_AMOUNT",
    v22.table_id = "FACT_SALES",
    v22.validation_type = "RANGE",
    v22.validation_rule = "RANGE:0,9999999.99",
    v22.error_message = "Sales Tax Amount must be non-negative",
    v22.business_criticality = "medium",
    v22.priority = "P2",
    v22.applicable_domains = ["ALL"],
    v22.is_active = true,
    v22.created_date = datetime(),
    v22.last_updated_date = datetime()
WITH v22
MATCH (c:Column {column_id: "COL_SALES_TAX_AMOUNT_FACT_924"})
MERGE (c)-[:HAS_VALIDATION]->(v22);

// EXCISE_TAX_AMOUNT - RANGE (Non-negative, domain-specific)
MERGE (v23:ValidationRule {rule_id: "VAL_EXCISE_TAX_AMOUNT_001"})
SET v23.rule_name = "EXCISE_TAX_AMOUNT Range Check",
    v23.column_name = "EXCISE_TAX_AMOUNT",
    v23.table_id = "FACT_SALES",
    v23.validation_type = "RANGE",
    v23.validation_rule = "RANGE:0,9999999.99",
    v23.error_message = "Excise Tax Amount must be non-negative",
    v23.business_criticality = "high",
    v23.priority = "P1",
    v23.applicable_domains = ["alcoholic_beverages", "pharmaceuticals", "battery"],
    v23.is_active = true,
    v23.created_date = datetime(),
    v23.last_updated_date = datetime()
WITH v23
MATCH (c:Column {column_id: "COL_EXCISE_TAX_AMOUNT_FACT_925"})
MERGE (c)-[:HAS_VALIDATION]->(v23);

// ========================================
// RETURNS AND ADJUSTMENTS VALIDATIONS
// ========================================

// RETURN_QUANTITY - RANGE (Non-negative)
MERGE (v24:ValidationRule {rule_id: "VAL_RETURN_QUANTITY_001"})
SET v24.rule_name = "RETURN_QUANTITY Range Check",
    v24.column_name = "RETURN_QUANTITY",
    v24.table_id = "FACT_SALES",
    v24.validation_type = "RANGE",
    v24.validation_rule = "RANGE:0,999999.999",
    v24.error_message = "Return Quantity must be non-negative",
    v24.business_criticality = "medium",
    v24.priority = "P2",
    v24.applicable_domains = ["ALL"],
    v24.is_active = true,
    v24.created_date = datetime(),
    v24.last_updated_date = datetime()
WITH v24
MATCH (c:Column {column_id: "COL_RETURN_QUANTITY_FACT_927"})
MERGE (c)-[:HAS_VALIDATION]->(v24);

// RETURN_AMOUNT - RANGE (Non-negative)
MERGE (v25:ValidationRule {rule_id: "VAL_RETURN_AMOUNT_001"})
SET v25.rule_name = "RETURN_AMOUNT Range Check",
    v25.column_name = "RETURN_AMOUNT",
    v25.table_id = "FACT_SALES",
    v25.validation_type = "RANGE",
    v25.validation_rule = "RANGE:0,999999999.99",
    v25.error_message = "Return Amount must be non-negative",
    v25.business_criticality = "medium",
    v25.priority = "P2",
    v25.applicable_domains = ["ALL"],
    v25.is_active = true,
    v25.created_date = datetime(),
    v25.last_updated_date = datetime()
WITH v25
MATCH (c:Column {column_id: "COL_RETURN_AMOUNT_FACT_928"})
MERGE (c)-[:HAS_VALIDATION]->(v25);

// ========================================
// BOOLEAN FLAG VALIDATIONS
// ========================================

// DIGITAL_SALES_FLAG - BOOLEAN
MERGE (v26:ValidationRule {rule_id: "VAL_DIGITAL_SALES_FLAG_001"})
SET v26.rule_name = "DIGITAL_SALES_FLAG Boolean Check",
    v26.column_name = "DIGITAL_SALES_FLAG",
    v26.table_id = "FACT_SALES",
    v26.validation_type = "BOOLEAN",
    v26.validation_rule = "BOOLEAN",
    v26.error_message = "Digital Sales Flag must be TRUE or FALSE",
    v26.business_criticality = "high",
    v26.priority = "P1",
    v26.applicable_domains = ["ALL"],
    v26.is_active = true,
    v26.created_date = datetime(),
    v26.last_updated_date = datetime()
WITH v26
MATCH (c:Column {column_id: "COL_DIGITAL_SALES_FLAG_FACT_934"})
MERGE (c)-[:HAS_VALIDATION]->(v26);

// MOBILE_SALES_FLAG - BOOLEAN
MERGE (v27:ValidationRule {rule_id: "VAL_MOBILE_SALES_FLAG_001"})
SET v27.rule_name = "MOBILE_SALES_FLAG Boolean Check",
    v27.column_name = "MOBILE_SALES_FLAG",
    v27.table_id = "FACT_SALES",
    v27.validation_type = "BOOLEAN",
    v27.validation_rule = "BOOLEAN",
    v27.error_message = "Mobile Sales Flag must be TRUE or FALSE",
    v27.business_criticality = "medium",
    v27.priority = "P2",
    v27.applicable_domains = ["ALL"],
    v27.is_active = true,
    v27.created_date = datetime(),
    v27.last_updated_date = datetime()
WITH v27
MATCH (c:Column {column_id: "COL_MOBILE_SALES_FLAG_FACT_935"})
MERGE (c)-[:HAS_VALIDATION]->(v27);

// AGE_VERIFICATION_FLAG - BOOLEAN (Domain-specific)
MERGE (v28:ValidationRule {rule_id: "VAL_AGE_VERIFICATION_FLAG_001"})
SET v28.rule_name = "AGE_VERIFICATION_FLAG Boolean Check",
    v28.column_name = "AGE_VERIFICATION_FLAG",
    v28.table_id = "FACT_SALES",
    v28.validation_type = "BOOLEAN",
    v28.validation_rule = "BOOLEAN",
    v28.error_message = "Age Verification Flag must be TRUE or FALSE",
    v28.business_criticality = "critical",
    v28.priority = "P1",
    v28.applicable_domains = ["alcoholic_beverages", "pharmaceuticals"],
    v28.is_active = true,
    v28.created_date = datetime(),
    v28.last_updated_date = datetime()
WITH v28
MATCH (c:Column {column_id: "COL_AGE_VERIFICATION_FLAG_FACT_937"})
MERGE (c)-[:HAS_VALIDATION]->(v28);

// REPEAT_PURCHASE_FLAG - BOOLEAN
MERGE (v29:ValidationRule {rule_id: "VAL_REPEAT_PURCHASE_FLAG_001"})
SET v29.rule_name = "REPEAT_PURCHASE_FLAG Boolean Check",
    v29.column_name = "REPEAT_PURCHASE_FLAG",
    v29.table_id = "FACT_SALES",
    v29.validation_type = "BOOLEAN",
    v29.validation_rule = "BOOLEAN",
    v29.error_message = "Repeat Purchase Flag must be TRUE or FALSE",
    v29.business_criticality = "high",
    v29.priority = "P1",
    v29.applicable_domains = ["ALL"],
    v29.is_active = true,
    v29.created_date = datetime(),
    v29.last_updated_date = datetime()
WITH v29
MATCH (c:Column {column_id: "COL_REPEAT_PURCHASE_FLAG_FACT_953"})
MERGE (c)-[:HAS_VALIDATION]->(v29);

// ========================================
// PRESCRIPTION NUMBER VALIDATION (Domain-specific)
// ========================================

// PRESCRIPTION_NUMBER - FORMAT (Pharmaceuticals)
MERGE (v30:ValidationRule {rule_id: "VAL_PRESCRIPTION_NUMBER_001"})
SET v30.rule_name = "PRESCRIPTION_NUMBER Format Check",
    v30.column_name = "PRESCRIPTION_NUMBER",
    v30.table_id = "FACT_SALES",
    v30.validation_type = "REGEX",
    v30.validation_rule = "^[A-Za-z0-9]{1,50}$",
    v30.error_message = "Prescription Number must be alphanumeric, max 50 chars",
    v30.business_criticality = "critical",
    v30.priority = "P1",
    v30.applicable_domains = ["pharmaceuticals"],
    v30.is_active = true,
    v30.created_date = datetime(),
    v30.last_updated_date = datetime()
WITH v30
MATCH (c:Column {column_id: "COL_PRESCRIPTION_NUMBER_FACT_938"})
MERGE (c)-[:HAS_VALIDATION]->(v30);

// ========================================
// PAYMENT METHOD VALIDATION
// ========================================

// PAYMENT_METHOD - ENUM
MERGE (v31:ValidationRule {rule_id: "VAL_PAYMENT_METHOD_001"})
SET v31.rule_name = "PAYMENT_METHOD Domain Check",
    v31.column_name = "PAYMENT_METHOD",
    v31.table_id = "FACT_SALES",
    v31.validation_type = "ENUM",
    v31.validation_rule = "ENUM:Cash,Credit Card,Debit Card,Digital Wallet,Bank Transfer,Check,Gift Card,Store Credit,BNPL",
    v31.error_message = "Invalid payment method",
    v31.business_criticality = "medium",
    v31.priority = "P2",
    v31.applicable_domains = ["ALL"],
    v31.is_active = true,
    v31.created_date = datetime(),
    v31.last_updated_date = datetime()
WITH v31
MATCH (c:Column {column_id: "COL_PAYMENT_METHOD_FACT_950"})
MERGE (c)-[:HAS_VALIDATION]->(v31);

// ========================================
// PRODUCT RATING VALIDATION
// ========================================

// PRODUCT_RATING - RANGE (1-5 stars)
MERGE (v32:ValidationRule {rule_id: "VAL_PRODUCT_RATING_001"})
SET v32.rule_name = "PRODUCT_RATING Range Check",
    v32.column_name = "PRODUCT_RATING",
    v32.table_id = "FACT_SALES",
    v32.validation_type = "RANGE",
    v32.validation_rule = "RANGE:1,5",
    v32.error_message = "Product Rating must be between 1 and 5",
    v32.business_criticality = "medium",
    v32.priority = "P2",
    v32.applicable_domains = ["ALL"],
    v32.is_active = true,
    v32.created_date = datetime(),
    v32.last_updated_date = datetime()
WITH v32
MATCH (c:Column {column_id: "COL_PRODUCT_RATING_FACT_952"})
MERGE (c)-[:HAS_VALIDATION]->(v32);

// ========================================
// LOT NUMBER VALIDATION
// ========================================

// LOT_NUMBER - FORMAT
MERGE (v33:ValidationRule {rule_id: "VAL_LOT_NUMBER_001"})
SET v33.rule_name = "LOT_NUMBER Format Check",
    v33.column_name = "LOT_NUMBER",
    v33.table_id = "FACT_SALES",
    v33.validation_type = "REGEX",
    v33.validation_rule = "^[A-Za-z0-9-]{1,50}$",
    v33.error_message = "Lot Number must be alphanumeric with dash, max 50 chars",
    v33.business_criticality = "high",
    v33.priority = "P1",
    v33.applicable_domains = ["ALL"],
    v33.is_active = true,
    v33.created_date = datetime(),
    v33.last_updated_date = datetime()
WITH v33
MATCH (c:Column {column_id: "COL_LOT_NUMBER_FACT_944"})
MERGE (c)-[:HAS_VALIDATION]->(v33);

// ========================================
// CROSS-FIELD VALIDATIONS
// ========================================

// Net Sales <= Gross Sales
MERGE (v34:ValidationRule {rule_id: "VAL_NET_GROSS_SALES_001"})
SET v34.rule_name = "Net Sales vs Gross Sales Check",
    v34.column_name = "NET_SALES_AMOUNT,GROSS_SALES_AMOUNT",
    v34.table_id = "FACT_SALES",
    v34.validation_type = "CUSTOM",
    v34.validation_rule = "CUSTOM:NET_SALES_AMOUNT <= GROSS_SALES_AMOUNT",
    v34.error_message = "Net Sales Amount cannot exceed Gross Sales Amount",
    v34.business_criticality = "critical",
    v34.priority = "P1",
    v34.applicable_domains = ["ALL"],
    v34.is_active = true,
    v34.created_date = datetime(),
    v34.last_updated_date = datetime()
WITH v34
MATCH (t:Table {table_id: "FACT_SALES"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v34);

// Total Discount = Sum of individual discounts
MERGE (v35:ValidationRule {rule_id: "VAL_TOTAL_DISCOUNT_SUM_001"})
SET v35.rule_name = "Total Discount Sum Check",
    v35.column_name = "TOTAL_DISCOUNT_AMOUNT,PROMOTIONAL_DISCOUNT_AMOUNT,TRADE_ALLOWANCE_AMOUNT,LOYALTY_DISCOUNT_AMOUNT,COUPON_DISCOUNT_AMOUNT",
    v35.table_id = "FACT_SALES",
    v35.validation_type = "CUSTOM",
    v35.validation_rule = "CUSTOM:TOTAL_DISCOUNT_AMOUNT = COALESCE(PROMOTIONAL_DISCOUNT_AMOUNT,0) + COALESCE(TRADE_ALLOWANCE_AMOUNT,0) + COALESCE(LOYALTY_DISCOUNT_AMOUNT,0) + COALESCE(COUPON_DISCOUNT_AMOUNT,0)",
    v35.error_message = "Total Discount Amount should equal sum of individual discounts",
    v35.business_criticality = "high",
    v35.priority = "P1",
    v35.applicable_domains = ["ALL"],
    v35.is_active = true,
    v35.created_date = datetime(),
    v35.last_updated_date = datetime()
WITH v35
MATCH (t:Table {table_id: "FACT_SALES"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v35);

// Return Quantity <= Original Quantity
MERGE (v36:ValidationRule {rule_id: "VAL_RETURN_QTY_LIMIT_001"})
SET v36.rule_name = "Return Quantity Limit Check",
    v36.column_name = "RETURN_QUANTITY,QUANTITY_SOLD",
    v36.table_id = "FACT_SALES",
    v36.validation_type = "CUSTOM",
    v36.validation_rule = "CUSTOM:COALESCE(RETURN_QUANTITY,0) <= QUANTITY_SOLD",
    v36.error_message = "Return Quantity cannot exceed Quantity Sold",
    v36.business_criticality = "medium",
    v36.priority = "P2",
    v36.applicable_domains = ["ALL"],
    v36.is_active = true,
    v36.created_date = datetime(),
    v36.last_updated_date = datetime()
WITH v36
MATCH (t:Table {table_id: "FACT_SALES"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v36);

// Gross Margin = Net Sales - COGS
MERGE (v37:ValidationRule {rule_id: "VAL_GROSS_MARGIN_CALC_001"})
SET v37.rule_name = "Gross Margin Calculation Check",
    v37.column_name = "GROSS_MARGIN_AMOUNT,NET_SALES_AMOUNT,COST_OF_GOODS_SOLD",
    v37.table_id = "FACT_SALES",
    v37.validation_type = "CUSTOM",
    v37.validation_rule = "CUSTOM:ABS(GROSS_MARGIN_AMOUNT - (NET_SALES_AMOUNT - COST_OF_GOODS_SOLD)) < 0.01",
    v37.error_message = "Gross Margin Amount should equal Net Sales minus COGS",
    v37.business_criticality = "critical",
    v37.priority = "P1",
    v37.applicable_domains = ["ALL"],
    v37.is_active = true,
    v37.created_date = datetime(),
    v37.last_updated_date = datetime()
WITH v37
MATCH (t:Table {table_id: "FACT_SALES"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v37);

// Age Verification Required for Alcoholic Beverages
MERGE (v38:ValidationRule {rule_id: "VAL_AGE_VERIFICATION_REQ_001"})
SET v38.rule_name = "Age Verification Required Check",
    v38.column_name = "AGE_VERIFICATION_FLAG",
    v38.table_id = "FACT_SALES",
    v38.validation_type = "CUSTOM",
    v38.validation_rule = "CUSTOM:IF(PRODUCT_CATEGORY='Alcoholic Beverages',AGE_VERIFICATION_FLAG=TRUE)",
    v38.error_message = "Age verification required for alcoholic beverage sales",
    v38.business_criticality = "critical",
    v38.priority = "P1",
    v38.applicable_domains = ["alcoholic_beverages"],
    v38.is_active = true,
    v38.created_date = datetime(),
    v38.last_updated_date = datetime()
WITH v38
MATCH (t:Table {table_id: "FACT_SALES"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v38);

// Mobile Sales implies Digital Sales
MERGE (v39:ValidationRule {rule_id: "VAL_MOBILE_DIGITAL_CONSISTENCY_001"})
SET v39.rule_name = "Mobile implies Digital Check",
    v39.column_name = "MOBILE_SALES_FLAG,DIGITAL_SALES_FLAG",
    v39.table_id = "FACT_SALES",
    v39.validation_type = "CUSTOM",
    v39.validation_rule = "CUSTOM:IF(MOBILE_SALES_FLAG=TRUE,DIGITAL_SALES_FLAG=TRUE)",
    v39.error_message = "Mobile sales must also be flagged as digital sales",
    v39.business_criticality = "medium",
    v39.priority = "P2",
    v39.applicable_domains = ["ALL"],
    v39.is_active = true,
    v39.created_date = datetime(),
    v39.last_updated_date = datetime()
WITH v39
MATCH (t:Table {table_id: "FACT_SALES"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v39);

// Unit Price Consistency
MERGE (v40:ValidationRule {rule_id: "VAL_UNIT_PRICE_CONSISTENCY_001"})
SET v40.rule_name = "Unit Price Consistency Check",
    v40.column_name = "UNIT_PRICE,NET_SALES_AMOUNT,QUANTITY_SOLD",
    v40.table_id = "FACT_SALES",
    v40.validation_type = "CUSTOM",
    v40.validation_rule = "CUSTOM:ABS(UNIT_PRICE - (NET_SALES_AMOUNT / QUANTITY_SOLD)) < 0.01",
    v40.error_message = "Unit Price should equal Net Sales Amount divided by Quantity Sold",
    v40.business_criticality = "high",
    v40.priority = "P1",
    v40.applicable_domains = ["ALL"],
    v40.is_active = true,
    v40.created_date = datetime(),
    v40.last_updated_date = datetime()
WITH v40
MATCH (t:Table {table_id: "FACT_SALES"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v40);

// Prescription Number Required for Pharmaceuticals
MERGE (v41:ValidationRule {rule_id: "VAL_PRESCRIPTION_REQ_001"})
SET v41.rule_name = "Prescription Number Required Check",
    v41.column_name = "PRESCRIPTION_NUMBER",
    v41.table_id = "FACT_SALES",
    v41.validation_type = "CUSTOM",
    v41.validation_rule = "CUSTOM:IF(PRODUCT_CATEGORY='Pharmaceuticals',PRESCRIPTION_NUMBER IS NOT NULL)",
    v41.error_message = "Prescription number required for pharmaceutical sales",
    v41.business_criticality = "critical",
    v41.priority = "P1",
    v41.applicable_domains = ["pharmaceuticals"],
    v41.is_active = true,
    v41.created_date = datetime(),
    v41.last_updated_date = datetime()
WITH v41
MATCH (t:Table {table_id: "FACT_SALES"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v41);

// ========================================
// VERIFICATION QUERIES
// ========================================

// Count validation rules by criticality
MATCH (v:ValidationRule {table_id: "FACT_SALES"})
RETURN v.business_criticality AS criticality, count(v) AS rule_count
ORDER BY criticality;

// Count validation rules by type
MATCH (v:ValidationRule {table_id: "FACT_SALES"})
RETURN v.validation_type AS validation_type, count(v) AS rule_count
ORDER BY rule_count DESC;

// List critical validation rules
MATCH (v:ValidationRule {table_id: "FACT_SALES"})
WHERE v.business_criticality = "critical"
RETURN v.rule_name AS critical_rule, v.error_message AS error_message
ORDER BY v.rule_name;

// Count domain-specific validations
MATCH (v:ValidationRule {table_id: "FACT_SALES"})
WHERE size(v.applicable_domains) < 15
RETURN v.applicable_domains AS domains, count(v) AS rule_count
ORDER BY rule_count DESC;

// List cross-field validations
MATCH (t:Table {table_id: "FACT_SALES"})-[:HAS_CROSS_FIELD_VALIDATION]->(v:ValidationRule)
RETURN v.rule_name AS cross_field_rule, v.error_message AS error_message
ORDER BY v.rule_name;

// ========================================
// END OF FACT_SALES VALIDATION RULES
// ========================================