// ========================================
// DATA QUALITY VALIDATION RULES FOR DIM_MEDIA
// This script creates validation rules and links them to columns
// ========================================

// Create constraint for ValidationRule uniqueness (run once)
CREATE CONSTRAINT validation_rule_unique IF NOT EXISTS
FOR (v:ValidationRule) REQUIRE v.rule_id IS UNIQUE;

// ========================================
// MEDIA_ID Validations
// ========================================

// MEDIA_ID - NOT NULL
MERGE (v1:ValidationRule {rule_id: "VAL_MEDIA_ID_001"})
SET v1.rule_name = "MEDIA_ID Not Null Check",
    v1.column_name = "MEDIA_ID",
    v1.table_id = "DIM_MEDIA",
    v1.validation_type = "NOT_NULL",
    v1.validation_rule = "NOT_NULL",
    v1.error_message = "Media ID is required",
    v1.business_criticality = "critical",
    v1.priority = "P1",
    v1.applicable_domains = ["ALL"],
    v1.is_active = true,
    v1.created_date = datetime(),
    v1.last_updated_date = datetime()
WITH v1
MATCH (c:Column {column_id: "COL_MEDIA_ID_DIM_801"})
MERGE (c)-[:HAS_VALIDATION]->(v1);

// MEDIA_ID - UNIQUE
MERGE (v2:ValidationRule {rule_id: "VAL_MEDIA_ID_002"})
SET v2.rule_name = "MEDIA_ID Uniqueness Check",
    v2.column_name = "MEDIA_ID",
    v2.table_id = "DIM_MEDIA",
    v2.validation_type = "UNIQUE",
    v2.validation_rule = "UNIQUE",
    v2.error_message = "Media ID must be unique",
    v2.business_criticality = "critical",
    v2.priority = "P1",
    v2.applicable_domains = ["ALL"],
    v2.is_active = true,
    v2.created_date = datetime(),
    v2.last_updated_date = datetime()
WITH v2
MATCH (c:Column {column_id: "COL_MEDIA_ID_DIM_801"})
MERGE (c)-[:HAS_VALIDATION]->(v2);

// MEDIA_ID - FORMAT
MERGE (v3:ValidationRule {rule_id: "VAL_MEDIA_ID_003"})
SET v3.rule_name = "MEDIA_ID Format Check",
    v3.column_name = "MEDIA_ID",
    v3.table_id = "DIM_MEDIA",
    v3.validation_type = "REGEX",
    v3.validation_rule = "^[A-Za-z0-9_-]{1,50}$",
    v3.error_message = "Media ID must be alphanumeric with dash/underscore, max 50 chars",
    v3.business_criticality = "critical",
    v3.priority = "P1",
    v3.applicable_domains = ["ALL"],
    v3.is_active = true,
    v3.created_date = datetime(),
    v3.last_updated_date = datetime()
WITH v3
MATCH (c:Column {column_id: "COL_MEDIA_ID_DIM_801"})
MERGE (c)-[:HAS_VALIDATION]->(v3);

// ========================================
// Foreign Key Validations
// ========================================

// CAMPAIGN_ID - REFERENCE
MERGE (v4:ValidationRule {rule_id: "VAL_CAMPAIGN_ID_001"})
SET v4.rule_name = "CAMPAIGN_ID Reference Check",
    v4.column_name = "CAMPAIGN_ID",
    v4.table_id = "DIM_MEDIA",
    v4.validation_type = "REFERENCE",
    v4.validation_rule = "FK:DIM_CAMPAIGN.CAMPAIGN_ID",
    v4.error_message = "Campaign ID must exist in Campaign dimension",
    v4.business_criticality = "high",
    v4.priority = "P1",
    v4.applicable_domains = ["ALL"],
    v4.is_active = true,
    v4.created_date = datetime(),
    v4.last_updated_date = datetime()
WITH v4
MATCH (c:Column {column_id: "COL_CAMPAIGN_ID_802"})
MERGE (c)-[:HAS_VALIDATION]->(v4);

// CREATIVE_ID - REFERENCE
MERGE (v5:ValidationRule {rule_id: "VAL_CREATIVE_ID_001"})
SET v5.rule_name = "CREATIVE_ID Reference Check",
    v5.column_name = "CREATIVE_ID",
    v5.table_id = "DIM_MEDIA",
    v5.validation_type = "REFERENCE",
    v5.validation_rule = "FK:DIM_CREATIVE.CREATIVE_ID",
    v5.error_message = "Creative ID must exist in Creative dimension",
    v5.business_criticality = "high",
    v5.priority = "P1",
    v5.applicable_domains = ["ALL"],
    v5.is_active = true,
    v5.created_date = datetime(),
    v5.last_updated_date = datetime()
WITH v5
MATCH (c:Column {column_id: "COL_CREATIVE_ID_803"})
MERGE (c)-[:HAS_VALIDATION]->(v5);

// ========================================
// Media Classification Validations
// ========================================

// MEDIA_TYPE - NOT NULL
MERGE (v6:ValidationRule {rule_id: "VAL_MEDIA_TYPE_001"})
SET v6.rule_name = "MEDIA_TYPE Not Null Check",
    v6.column_name = "MEDIA_TYPE",
    v6.table_id = "DIM_MEDIA",
    v6.validation_type = "NOT_NULL",
    v6.validation_rule = "NOT_NULL",
    v6.error_message = "Media Type is required",
    v6.business_criticality = "critical",
    v6.priority = "P1",
    v6.applicable_domains = ["ALL"],
    v6.is_active = true,
    v6.created_date = datetime(),
    v6.last_updated_date = datetime()
WITH v6
MATCH (c:Column {column_id: "COL_MEDIA_TYPE_804"})
MERGE (c)-[:HAS_VALIDATION]->(v6);

// MEDIA_TYPE - ENUM
MERGE (v7:ValidationRule {rule_id: "VAL_MEDIA_TYPE_002"})
SET v7.rule_name = "MEDIA_TYPE Domain Check",
    v7.column_name = "MEDIA_TYPE",
    v7.table_id = "DIM_MEDIA",
    v7.validation_type = "ENUM",
    v7.validation_rule = "ENUM:Television,Digital,Print,Radio,Outdoor,Social Media,Streaming,Direct Mail,Cinema",
    v7.error_message = "Invalid media type",
    v7.business_criticality = "critical",
    v7.priority = "P1",
    v7.applicable_domains = ["ALL"],
    v7.is_active = true,
    v7.created_date = datetime(),
    v7.last_updated_date = datetime()
WITH v7
MATCH (c:Column {column_id: "COL_MEDIA_TYPE_804"})
MERGE (c)-[:HAS_VALIDATION]->(v7);

// AD_FORMAT - NOT NULL
MERGE (v8:ValidationRule {rule_id: "VAL_AD_FORMAT_001"})
SET v8.rule_name = "AD_FORMAT Not Null Check",
    v8.column_name = "AD_FORMAT",
    v8.table_id = "DIM_MEDIA",
    v8.validation_type = "NOT_NULL",
    v8.validation_rule = "NOT_NULL",
    v8.error_message = "Ad Format is required",
    v8.business_criticality = "high",
    v8.priority = "P1",
    v8.applicable_domains = ["ALL"],
    v8.is_active = true,
    v8.created_date = datetime(),
    v8.last_updated_date = datetime()
WITH v8
MATCH (c:Column {column_id: "COL_AD_FORMAT_807"})
MERGE (c)-[:HAS_VALIDATION]->(v8);

// ========================================
// Financial and Investment Validations
// ========================================

// MEDIA_SPEND - NOT NULL
MERGE (v9:ValidationRule {rule_id: "VAL_MEDIA_SPEND_001"})
SET v9.rule_name = "MEDIA_SPEND Not Null Check",
    v9.column_name = "MEDIA_SPEND",
    v9.table_id = "DIM_MEDIA",
    v9.validation_type = "NOT_NULL",
    v9.validation_rule = "NOT_NULL",
    v9.error_message = "Media Spend is required",
    v9.business_criticality = "critical",
    v9.priority = "P1",
    v9.applicable_domains = ["ALL"],
    v9.is_active = true,
    v9.created_date = datetime(),
    v9.last_updated_date = datetime()
WITH v9
MATCH (c:Column {column_id: "COL_MEDIA_SPEND_813"})
MERGE (c)-[:HAS_VALIDATION]->(v9);

// MEDIA_SPEND - RANGE
MERGE (v10:ValidationRule {rule_id: "VAL_MEDIA_SPEND_002"})
SET v10.rule_name = "MEDIA_SPEND Range Check",
    v10.column_name = "MEDIA_SPEND",
    v10.table_id = "DIM_MEDIA",
    v10.validation_type = "RANGE",
    v10.validation_rule = "RANGE:0,999999999999.99",
    v10.error_message = "Media Spend must be non-negative",
    v10.business_criticality = "critical",
    v10.priority = "P1",
    v10.applicable_domains = ["ALL"],
    v10.is_active = true,
    v10.created_date = datetime(),
    v10.last_updated_date = datetime()
WITH v10
MATCH (c:Column {column_id: "COL_MEDIA_SPEND_813"})
MERGE (c)-[:HAS_VALIDATION]->(v10);

// CPM_RATE - RANGE
MERGE (v11:ValidationRule {rule_id: "VAL_CPM_RATE_001"})
SET v11.rule_name = "CPM_RATE Range Check",
    v11.column_name = "CPM_RATE",
    v11.table_id = "DIM_MEDIA",
    v11.validation_type = "RANGE",
    v11.validation_rule = "RANGE:0,99999.99",
    v11.error_message = "CPM Rate must be non-negative",
    v11.business_criticality = "high",
    v11.priority = "P1",
    v11.applicable_domains = ["ALL"],
    v11.is_active = true,
    v11.created_date = datetime(),
    v11.last_updated_date = datetime()
WITH v11
MATCH (c:Column {column_id: "COL_CPM_RATE_814"})
MERGE (c)-[:HAS_VALIDATION]->(v11);

// UNIT_RATE - RANGE
MERGE (v12:ValidationRule {rule_id: "VAL_UNIT_RATE_001"})
SET v12.rule_name = "UNIT_RATE Range Check",
    v12.column_name = "UNIT_RATE",
    v12.table_id = "DIM_MEDIA",
    v12.validation_type = "RANGE",
    v12.validation_rule = "RANGE:0,99999.99",
    v12.error_message = "Unit Rate must be non-negative",
    v12.business_criticality = "medium",
    v12.priority = "P2",
    v12.applicable_domains = ["ALL"],
    v12.is_active = true,
    v12.created_date = datetime(),
    v12.last_updated_date = datetime()
WITH v12
MATCH (c:Column {column_id: "COL_UNIT_RATE_815"})
MERGE (c)-[:HAS_VALIDATION]->(v12);

// MEDIA_ALLOCATION_PERCENT - RANGE
MERGE (v13:ValidationRule {rule_id: "VAL_MEDIA_ALLOCATION_PCT_001"})
SET v13.rule_name = "MEDIA_ALLOCATION_PERCENT Range Check",
    v13.column_name = "MEDIA_ALLOCATION_PERCENT",
    v13.table_id = "DIM_MEDIA",
    v13.validation_type = "RANGE",
    v13.validation_rule = "RANGE:0,100",
    v13.error_message = "Media Allocation must be between 0 and 100",
    v13.business_criticality = "medium",
    v13.priority = "P2",
    v13.applicable_domains = ["ALL"],
    v13.is_active = true,
    v13.created_date = datetime(),
    v13.last_updated_date = datetime()
WITH v13
MATCH (c:Column {column_id: "COL_MEDIA_ALLOCATION_PERCENT_816"})
MERGE (c)-[:HAS_VALIDATION]->(v13);

// ========================================
// Date Validations
// ========================================

// FLIGHT_START_DATE - NOT NULL
MERGE (v14:ValidationRule {rule_id: "VAL_FLIGHT_START_DATE_001"})
SET v14.rule_name = "FLIGHT_START_DATE Not Null Check",
    v14.column_name = "FLIGHT_START_DATE",
    v14.table_id = "DIM_MEDIA",
    v14.validation_type = "NOT_NULL",
    v14.validation_rule = "NOT_NULL",
    v14.error_message = "Flight Start Date is required",
    v14.business_criticality = "high",
    v14.priority = "P1",
    v14.applicable_domains = ["ALL"],
    v14.is_active = true,
    v14.created_date = datetime(),
    v14.last_updated_date = datetime()
WITH v14
MATCH (c:Column {column_id: "COL_FLIGHT_START_DATE_810"})
MERGE (c)-[:HAS_VALIDATION]->(v14);

// FLIGHT_END_DATE - NOT NULL
MERGE (v15:ValidationRule {rule_id: "VAL_FLIGHT_END_DATE_001"})
SET v15.rule_name = "FLIGHT_END_DATE Not Null Check",
    v15.column_name = "FLIGHT_END_DATE",
    v15.table_id = "DIM_MEDIA",
    v15.validation_type = "NOT_NULL",
    v15.validation_rule = "NOT_NULL",
    v15.error_message = "Flight End Date is required",
    v15.business_criticality = "high",
    v15.priority = "P1",
    v15.applicable_domains = ["ALL"],
    v15.is_active = true,
    v15.created_date = datetime(),
    v15.last_updated_date = datetime()
WITH v15
MATCH (c:Column {column_id: "COL_FLIGHT_END_DATE_811"})
MERGE (c)-[:HAS_VALIDATION]->(v15);

// FLIGHT_DURATION_DAYS - RANGE
MERGE (v16:ValidationRule {rule_id: "VAL_FLIGHT_DURATION_001"})
SET v16.rule_name = "FLIGHT_DURATION_DAYS Range Check",
    v16.column_name = "FLIGHT_DURATION_DAYS",
    v16.table_id = "DIM_MEDIA",
    v16.validation_type = "RANGE",
    v16.validation_rule = "RANGE:1,365",
    v16.error_message = "Flight Duration must be between 1 and 365 days",
    v16.business_criticality = "medium",
    v16.priority = "P2",
    v16.applicable_domains = ["ALL"],
    v16.is_active = true,
    v16.created_date = datetime(),
    v16.last_updated_date = datetime()
WITH v16
MATCH (c:Column {column_id: "COL_FLIGHT_DURATION_DAYS_812"})
MERGE (c)-[:HAS_VALIDATION]->(v16);

// ========================================
// Audience and Targeting Validations
// ========================================

// CAMPAIGN_OBJECTIVE - ENUM
MERGE (v17:ValidationRule {rule_id: "VAL_CAMPAIGN_OBJECTIVE_001"})
SET v17.rule_name = "CAMPAIGN_OBJECTIVE Domain Check",
    v17.column_name = "CAMPAIGN_OBJECTIVE",
    v17.table_id = "DIM_MEDIA",
    v17.validation_type = "ENUM",
    v17.validation_rule = "ENUM:Brand Awareness,Consideration,Trial,Sales,Retention,Launch,Seasonal",
    v17.error_message = "Invalid campaign objective",
    v17.business_criticality = "high",
    v17.priority = "P1",
    v17.applicable_domains = ["ALL"],
    v17.is_active = true,
    v17.created_date = datetime(),
    v17.last_updated_date = datetime()
WITH v17
MATCH (c:Column {column_id: "COL_CAMPAIGN_OBJECTIVE_809"})
MERGE (c)-[:HAS_VALIDATION]->(v17);

// GENDER_TARGET - ENUM
MERGE (v18:ValidationRule {rule_id: "VAL_GENDER_TARGET_001"})
SET v18.rule_name = "GENDER_TARGET Domain Check",
    v18.column_name = "GENDER_TARGET",
    v18.table_id = "DIM_MEDIA",
    v18.validation_type = "ENUM",
    v18.validation_rule = "ENUM:Male,Female,All,Male Skew,Female Skew",
    v18.error_message = "Invalid gender target",
    v18.business_criticality = "medium",
    v18.priority = "P2",
    v18.applicable_domains = ["ALL"],
    v18.is_active = true,
    v18.created_date = datetime(),
    v18.last_updated_date = datetime()
WITH v18
MATCH (c:Column {column_id: "COL_GENDER_TARGET_819"})
MERGE (c)-[:HAS_VALIDATION]->(v18);

// ========================================
// Performance Metrics Validations
// ========================================

// IMPRESSIONS_TARGET - RANGE
MERGE (v19:ValidationRule {rule_id: "VAL_IMPRESSIONS_TARGET_001"})
SET v19.rule_name = "IMPRESSIONS_TARGET Range Check",
    v19.column_name = "IMPRESSIONS_TARGET",
    v19.table_id = "DIM_MEDIA",
    v19.validation_type = "RANGE",
    v19.validation_rule = "RANGE:0,999999999999",
    v19.error_message = "Target Impressions must be non-negative",
    v19.business_criticality = "high",
    v19.priority = "P1",
    v19.applicable_domains = ["ALL"],
    v19.is_active = true,
    v19.created_date = datetime(),
    v19.last_updated_date = datetime()
WITH v19
MATCH (c:Column {column_id: "COL_IMPRESSIONS_TARGET_822"})
MERGE (c)-[:HAS_VALIDATION]->(v19);

// FREQUENCY_TARGET - RANGE
MERGE (v20:ValidationRule {rule_id: "VAL_FREQUENCY_TARGET_001"})
SET v20.rule_name = "FREQUENCY_TARGET Range Check",
    v20.column_name = "FREQUENCY_TARGET",
    v20.table_id = "DIM_MEDIA",
    v20.validation_type = "RANGE",
    v20.validation_rule = "RANGE:0.1,50",
    v20.error_message = "Frequency Target must be between 0.1 and 50",
    v20.business_criticality = "medium",
    v20.priority = "P2",
    v20.applicable_domains = ["ALL"],
    v20.is_active = true,
    v20.created_date = datetime(),
    v20.last_updated_date = datetime()
WITH v20
MATCH (c:Column {column_id: "COL_FREQUENCY_TARGET_823"})
MERGE (c)-[:HAS_VALIDATION]->(v20);

// GRP_TARGET - RANGE
MERGE (v21:ValidationRule {rule_id: "VAL_GRP_TARGET_001"})
SET v21.rule_name = "GRP_TARGET Range Check",
    v21.column_name = "GRP_TARGET",
    v21.table_id = "DIM_MEDIA",
    v21.validation_type = "RANGE",
    v21.validation_rule = "RANGE:0,99999.99",
    v21.error_message = "GRP Target must be non-negative",
    v21.business_criticality = "medium",
    v21.priority = "P2",
    v21.applicable_domains = ["ALL"],
    v21.is_active = true,
    v21.created_date = datetime(),
    v21.last_updated_date = datetime()
WITH v21
MATCH (c:Column {column_id: "COL_GRP_TARGET_824"})
MERGE (c)-[:HAS_VALIDATION]->(v21);

// ========================================
// Traditional Media Validations
// ========================================

// DAYPART - ENUM
MERGE (v22:ValidationRule {rule_id: "VAL_DAYPART_001"})
SET v22.rule_name = "DAYPART Domain Check",
    v22.column_name = "DAYPART",
    v22.table_id = "DIM_MEDIA",
    v22.validation_type = "ENUM",
    v22.validation_rule = "ENUM:Prime Time,Daytime,Late Night,Early Morning,Overnight,Weekend,All Day",
    v22.error_message = "Invalid daypart",
    v22.business_criticality = "medium",
    v22.priority = "P2",
    v22.applicable_domains = ["ALL"],
    v22.is_active = true,
    v22.created_date = datetime(),
    v22.last_updated_date = datetime()
WITH v22
MATCH (c:Column {column_id: "COL_DAYPART_825"})
MERGE (c)-[:HAS_VALIDATION]->(v22);

// NETWORK_TIER - ENUM
MERGE (v23:ValidationRule {rule_id: "VAL_NETWORK_TIER_001"})
SET v23.rule_name = "NETWORK_TIER Domain Check",
    v23.column_name = "NETWORK_TIER",
    v23.table_id = "DIM_MEDIA",
    v23.validation_type = "ENUM",
    v23.validation_rule = "ENUM:Broadcast,Cable,Premium,Streaming,Local,Syndicated",
    v23.error_message = "Invalid network tier",
    v23.business_criticality = "medium",
    v23.priority = "P2",
    v23.applicable_domains = ["ALL"],
    v23.is_active = true,
    v23.created_date = datetime(),
    v23.last_updated_date = datetime()
WITH v23
MATCH (c:Column {column_id: "COL_NETWORK_TIER_827"})
MERGE (c)-[:HAS_VALIDATION]->(v23);

// ========================================
// Digital Media Validations
// ========================================

// DIGITAL_PLACEMENT_TYPE - ENUM
MERGE (v24:ValidationRule {rule_id: "VAL_DIGITAL_PLACEMENT_TYPE_001"})
SET v24.rule_name = "DIGITAL_PLACEMENT_TYPE Domain Check",
    v24.column_name = "DIGITAL_PLACEMENT_TYPE",
    v24.table_id = "DIM_MEDIA",
    v24.validation_type = "ENUM",
    v24.validation_rule = "ENUM:Programmatic,Direct Buy,Social Native,Search,Display,Video,Audio,Native",
    v24.error_message = "Invalid digital placement type",
    v24.business_criticality = "high",
    v24.priority = "P1",
    v24.applicable_domains = ["ALL"],
    v24.is_active = true,
    v24.created_date = datetime(),
    v24.last_updated_date = datetime()
WITH v24
MATCH (c:Column {column_id: "COL_DIGITAL_PLACEMENT_TYPE_828"})
MERGE (c)-[:HAS_VALIDATION]->(v24);

// DEVICE_TARGETING - ENUM
MERGE (v25:ValidationRule {rule_id: "VAL_DEVICE_TARGETING_001"})
SET v25.rule_name = "DEVICE_TARGETING Domain Check",
    v25.column_name = "DEVICE_TARGETING",
    v25.table_id = "DIM_MEDIA",
    v25.validation_type = "ENUM",
    v25.validation_rule = "ENUM:Desktop,Mobile,Tablet,Connected TV,All Devices,Mobile Web,Mobile App",
    v25.error_message = "Invalid device targeting",
    v25.business_criticality = "medium",
    v25.priority = "P2",
    v25.applicable_domains = ["ALL"],
    v25.is_active = true,
    v25.created_date = datetime(),
    v25.last_updated_date = datetime()
WITH v25
MATCH (c:Column {column_id: "COL_DEVICE_TARGETING_829"})
MERGE (c)-[:HAS_VALIDATION]->(v25);

// PROGRAMMATIC_FLAG - BOOLEAN
MERGE (v26:ValidationRule {rule_id: "VAL_PROGRAMMATIC_FLAG_001"})
SET v26.rule_name = "PROGRAMMATIC_FLAG Boolean Check",
    v26.column_name = "PROGRAMMATIC_FLAG",
    v26.table_id = "DIM_MEDIA",
    v26.validation_type = "BOOLEAN",
    v26.validation_rule = "BOOLEAN",
    v26.error_message = "Programmatic Flag must be TRUE or FALSE",
    v26.business_criticality = "medium",
    v26.priority = "P2",
    v26.applicable_domains = ["ALL"],
    v26.is_active = true,
    v26.created_date = datetime(),
    v26.last_updated_date = datetime()
WITH v26
MATCH (c:Column {column_id: "COL_PROGRAMMATIC_FLAG_831"})
MERGE (c)-[:HAS_VALIDATION]->(v26);

// ========================================
// Creative Validations
// ========================================

// CREATIVE_LENGTH - RANGE
MERGE (v27:ValidationRule {rule_id: "VAL_CREATIVE_LENGTH_001"})
SET v27.rule_name = "CREATIVE_LENGTH Range Check",
    v27.column_name = "CREATIVE_LENGTH",
    v27.table_id = "DIM_MEDIA",
    v27.validation_type = "RANGE",
    v27.validation_rule = "RANGE:1,3600",
    v27.error_message = "Creative Length must be between 1 and 3600 seconds",
    v27.business_criticality = "medium",
    v27.priority = "P2",
    v27.applicable_domains = ["ALL"],
    v27.is_active = true,
    v27.created_date = datetime(),
    v27.last_updated_date = datetime()
WITH v27
MATCH (c:Column {column_id: "COL_CREATIVE_LENGTH_834"})
MERGE (c)-[:HAS_VALIDATION]->(v27);

// LANGUAGE - ENUM
MERGE (v28:ValidationRule {rule_id: "VAL_LANGUAGE_001"})
SET v28.rule_name = "LANGUAGE Domain Check",
    v28.column_name = "LANGUAGE",
    v28.table_id = "DIM_MEDIA",
    v28.validation_type = "ENUM",
    v28.validation_rule = "ENUM:English,Spanish,French,German,Italian,Portuguese,Chinese,Japanese,Korean,Other",
    v28.error_message = "Invalid language",
    v28.business_criticality = "medium",
    v28.priority = "P2",
    v28.applicable_domains = ["ALL"],
    v28.is_active = true,
    v28.created_date = datetime(),
    v28.last_updated_date = datetime()
WITH v28
MATCH (c:Column {column_id: "COL_LANGUAGE_835"})
MERGE (c)-[:HAS_VALIDATION]->(v28);

// ========================================
// Compliance and Brand Safety Validations
// ========================================

// BRAND_SAFETY_CATEGORY - ENUM
MERGE (v29:ValidationRule {rule_id: "VAL_BRAND_SAFETY_CATEGORY_001"})
SET v29.rule_name = "BRAND_SAFETY_CATEGORY Domain Check",
    v29.column_name = "BRAND_SAFETY_CATEGORY",
    v29.table_id = "DIM_MEDIA",
    v29.validation_type = "ENUM",
    v29.validation_rule = "ENUM:Safe,Moderate Risk,High Risk,Restricted",
    v29.error_message = "Invalid brand safety category",
    v29.business_criticality = "high",
    v29.priority = "P1",
    v29.applicable_domains = ["ALL"],
    v29.is_active = true,
    v29.created_date = datetime(),
    v29.last_updated_date = datetime()
WITH v29
MATCH (c:Column {column_id: "COL_BRAND_SAFETY_CATEGORY_842"})
MERGE (c)-[:HAS_VALIDATION]->(v29);

// CONTENT_RESTRICTION_FLAG - BOOLEAN
MERGE (v30:ValidationRule {rule_id: "VAL_CONTENT_RESTRICTION_FLAG_001"})
SET v30.rule_name = "CONTENT_RESTRICTION_FLAG Boolean Check",
    v30.column_name = "CONTENT_RESTRICTION_FLAG",
    v30.table_id = "DIM_MEDIA",
    v30.validation_type = "BOOLEAN",
    v30.validation_rule = "BOOLEAN",
    v30.error_message = "Content Restriction Flag must be TRUE or FALSE",
    v30.business_criticality = "high",
    v30.priority = "P1",
    v30.applicable_domains = ["ALL"],
    v30.is_active = true,
    v30.created_date = datetime(),
    v30.last_updated_date = datetime()
WITH v30
MATCH (c:Column {column_id: "COL_CONTENT_RESTRICTION_FLAG_843"})
MERGE (c)-[:HAS_VALIDATION]->(v30);

// REGULATORY_APPROVAL_FLAG - BOOLEAN
MERGE (v31:ValidationRule {rule_id: "VAL_REGULATORY_APPROVAL_FLAG_001"})
SET v31.rule_name = "REGULATORY_APPROVAL_FLAG Boolean Check",
    v31.column_name = "REGULATORY_APPROVAL_FLAG",
    v31.table_id = "DIM_MEDIA",
    v31.validation_type = "BOOLEAN",
    v31.validation_rule = "BOOLEAN",
    v31.error_message = "Regulatory Approval Flag must be TRUE or FALSE",
    v31.business_criticality = "high",
    v31.priority = "P1",
    v31.applicable_domains = ["ALL"],
    v31.is_active = true,
    v31.created_date = datetime(),
    v31.last_updated_date = datetime()
WITH v31
MATCH (c:Column {column_id: "COL_REGULATORY_APPROVAL_FLAG_844"})
MERGE (c)-[:HAS_VALIDATION]->(v31);

// ========================================
// Domain-Specific Regulatory Validations
// ========================================

// ALCOHOL_ADVERTISING_RESTRICTION - NOT NULL (Alcoholic Beverages)
MERGE (v32:ValidationRule {rule_id: "VAL_ALCOHOL_AD_RESTRICTION_001"})
SET v32.rule_name = "ALCOHOL_ADVERTISING_RESTRICTION Not Null Check",
    v32.column_name = "ALCOHOL_ADVERTISING_RESTRICTION",
    v32.table_id = "DIM_MEDIA",
    v32.validation_type = "NOT_NULL",
    v32.validation_rule = "NOT_NULL",
    v32.error_message = "Alcohol advertising restrictions required for alcoholic beverages",
    v32.business_criticality = "critical",
    v32.priority = "P1",
    v32.applicable_domains = ["alcoholic_beverages"],
    v32.is_active = true,
    v32.created_date = datetime(),
    v32.last_updated_date = datetime()
WITH v32
MATCH (c:Column {column_id: "COL_ALCOHOL_AD_RESTRICTION_845"})
MERGE (c)-[:HAS_VALIDATION]->(v32);

// PHARMA_INDICATION_CLAIM - NOT NULL (Pharmaceuticals)
MERGE (v33:ValidationRule {rule_id: "VAL_PHARMA_INDICATION_001"})
SET v33.rule_name = "PHARMA_INDICATION_CLAIM Not Null Check",
    v33.column_name = "PHARMA_INDICATION_CLAIM",
    v33.table_id = "DIM_MEDIA",
    v33.validation_type = "NOT_NULL",
    v33.validation_rule = "NOT_NULL",
    v33.error_message = "Pharmaceutical indication claim required for pharma ads",
    v33.business_criticality = "critical",
    v33.priority = "P1",
    v33.applicable_domains = ["pharmaceuticals", "health_supplements"],
    v33.is_active = true,
    v33.created_date = datetime(),
    v33.last_updated_date = datetime()
WITH v33
MATCH (c:Column {column_id: "COL_PHARMA_INDICATION_CLAIM_846"})
MERGE (c)-[:HAS_VALIDATION]->(v33);

// CHILD_DIRECTED_FLAG - BOOLEAN
MERGE (v34:ValidationRule {rule_id: "VAL_CHILD_DIRECTED_FLAG_001"})
SET v34.rule_name = "CHILD_DIRECTED_FLAG Boolean Check",
    v34.column_name = "CHILD_DIRECTED_FLAG",
    v34.table_id = "DIM_MEDIA",
    v34.validation_type = "BOOLEAN",
    v34.validation_rule = "BOOLEAN",
    v34.error_message = "Child-Directed Flag must be TRUE or FALSE",
    v34.business_criticality = "critical",
    v34.priority = "P1",
    v34.applicable_domains = ["toys", "baby_products", "food_beverage", "snacks"],
    v34.is_active = true,
    v34.created_date = datetime(),
    v34.last_updated_date = datetime()
WITH v34
MATCH (c:Column {column_id: "COL_CHILD_DIRECTED_FLAG_847"})
MERGE (c)-[:HAS_VALIDATION]->(v34);

// ========================================
// Performance and Attribution Validations
// ========================================

// ATTRIBUTION_MODEL - ENUM
MERGE (v35:ValidationRule {rule_id: "VAL_ATTRIBUTION_MODEL_001"})
SET v35.rule_name = "ATTRIBUTION_MODEL Domain Check",
    v35.column_name = "ATTRIBUTION_MODEL",
    v35.table_id = "DIM_MEDIA",
    v35.validation_type = "ENUM",
    v35.validation_rule = "ENUM:First Touch,Last Touch,Multi-Touch,Linear,Time Decay,Position Based,Data Driven",
    v35.error_message = "Invalid attribution model",
    v35.business_criticality = "medium",
    v35.priority = "P2",
    v35.applicable_domains = ["ALL"],
    v35.is_active = true,
    v35.created_date = datetime(),
    v35.last_updated_date = datetime()
WITH v35
MATCH (c:Column {column_id: "COL_ATTRIBUTION_MODEL_848"})
MERGE (c)-[:HAS_VALIDATION]->(v35);

// TRACKING_URL - FORMAT
MERGE (v36:ValidationRule {rule_id: "VAL_TRACKING_URL_001"})
SET v36.rule_name = "TRACKING_URL Format Check",
    v36.column_name = "TRACKING_URL",
    v36.table_id = "DIM_MEDIA",
    v36.validation_type = "REGEX",
    v36.validation_rule = "^(https?://)?(www\\.)?[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}(/.*)?$",
    v36.error_message = "Invalid tracking URL format",
    v36.business_criticality = "low",
    v36.priority = "P3",
    v36.applicable_domains = ["ALL"],
    v36.is_active = true,
    v36.created_date = datetime(),
    v36.last_updated_date = datetime()
WITH v36
MATCH (c:Column {column_id: "COL_TRACKING_URL_850"})
MERGE (c)-[:HAS_VALIDATION]->(v36);

// ========================================
// Market and Competition Validations
// ========================================

// COMPETITIVE_RESPONSE_FLAG - BOOLEAN
MERGE (v37:ValidationRule {rule_id: "VAL_COMPETITIVE_RESPONSE_FLAG_001"})
SET v37.rule_name = "COMPETITIVE_RESPONSE_FLAG Boolean Check",
    v37.column_name = "COMPETITIVE_RESPONSE_FLAG",
    v37.table_id = "DIM_MEDIA",
    v37.validation_type = "BOOLEAN",
    v37.validation_rule = "BOOLEAN",
    v37.error_message = "Competitive Response Flag must be TRUE or FALSE",
    v37.business_criticality = "medium",
    v37.priority = "P2",
    v37.applicable_domains = ["ALL"],
    v37.is_active = true,
    v37.created_date = datetime(),
    v37.last_updated_date = datetime()
WITH v37
MATCH (c:Column {column_id: "COL_COMPETITIVE_RESPONSE_FLAG_851"})
MERGE (c)-[:HAS_VALIDATION]->(v37);

// SHARE_OF_VOICE_TARGET - RANGE
MERGE (v38:ValidationRule {rule_id: "VAL_SHARE_OF_VOICE_TARGET_001"})
SET v38.rule_name = "SHARE_OF_VOICE_TARGET Range Check",
    v38.column_name = "SHARE_OF_VOICE_TARGET",
    v38.table_id = "DIM_MEDIA",
    v38.validation_type = "RANGE",
    v38.validation_rule = "RANGE:0,100",
    v38.error_message = "Share of Voice Target must be between 0 and 100",
    v38.business_criticality = "medium",
    v38.priority = "P2",
    v38.applicable_domains = ["ALL"],
    v38.is_active = true,
    v38.created_date = datetime(),
    v38.last_updated_date = datetime()
WITH v38
MATCH (c:Column {column_id: "COL_SHARE_OF_VOICE_TARGET_852"})
MERGE (c)-[:HAS_VALIDATION]->(v38);

// ========================================
// Omnichannel Integration Validations
// ========================================

// OMNICHANNEL_INTEGRATION - ENUM
MERGE (v39:ValidationRule {rule_id: "VAL_OMNICHANNEL_INTEGRATION_001"})
SET v39.rule_name = "OMNICHANNEL_INTEGRATION Domain Check",
    v39.column_name = "OMNICHANNEL_INTEGRATION",
    v39.table_id = "DIM_MEDIA",
    v39.validation_type = "ENUM",
    v39.validation_rule = "ENUM:High,Medium,Low,None",
    v39.error_message = "Invalid omnichannel integration level",
    v39.business_criticality = "medium",
    v39.priority = "P2",
    v39.applicable_domains = ["ALL"],
    v39.is_active = true,
    v39.created_date = datetime(),
    v39.last_updated_date = datetime()
WITH v39
MATCH (c:Column {column_id: "COL_OMNICHANNEL_INTEGRATION_855"})
MERGE (c)-[:HAS_VALIDATION]->(v39);

// CROSS_MEDIA_SYNERGY_FLAG - BOOLEAN
MERGE (v40:ValidationRule {rule_id: "VAL_CROSS_MEDIA_SYNERGY_FLAG_001"})
SET v40.rule_name = "CROSS_MEDIA_SYNERGY_FLAG Boolean Check",
    v40.column_name = "CROSS_MEDIA_SYNERGY_FLAG",
    v40.table_id = "DIM_MEDIA",
    v40.validation_type = "BOOLEAN",
    v40.validation_rule = "BOOLEAN",
    v40.error_message = "Cross-Media Synergy Flag must be TRUE or FALSE",
    v40.business_criticality = "medium",
    v40.priority = "P2",
    v40.applicable_domains = ["ALL"],
    v40.is_active = true,
    v40.created_date = datetime(),
    v40.last_updated_date = datetime()
WITH v40
MATCH (c:Column {column_id: "COL_CROSS_MEDIA_SYNERGY_FLAG_856"})
MERGE (c)-[:HAS_VALIDATION]->(v40);

// ========================================
// Test and Learn Validations
// ========================================

// OPTIMIZATION_STATUS - ENUM
MERGE (v41:ValidationRule {rule_id: "VAL_OPTIMIZATION_STATUS_001"})
SET v41.rule_name = "OPTIMIZATION_STATUS Domain Check",
    v41.column_name = "OPTIMIZATION_STATUS",
    v41.table_id = "DIM_MEDIA",
    v41.validation_type = "ENUM",
    v41.validation_rule = "ENUM:Learning,Optimizing,Optimized,Paused,Testing,Complete",
    v41.error_message = "Invalid optimization status",
    v41.business_criticality = "medium",
    v41.priority = "P2",
    v41.applicable_domains = ["ALL"],
    v41.is_active = true,
    v41.created_date = datetime(),
    v41.last_updated_date = datetime()
WITH v41
MATCH (c:Column {column_id: "COL_OPTIMIZATION_STATUS_858"})
MERGE (c)-[:HAS_VALIDATION]->(v41);

// ========================================
// Cross-Field Validations
// ========================================

// Flight Start vs End Date
MERGE (v42:ValidationRule {rule_id: "VAL_FLIGHT_DATE_CONSISTENCY_001"})
SET v42.rule_name = "Flight Start vs End Date Check",
    v42.column_name = "FLIGHT_START_DATE,FLIGHT_END_DATE",
    v42.table_id = "DIM_MEDIA",
    v42.validation_type = "CUSTOM",
    v42.validation_rule = "CUSTOM:FLIGHT_START_DATE<=FLIGHT_END_DATE",
    v42.error_message = "Flight start date must be before or equal to end date",
    v42.business_criticality = "high",
    v42.priority = "P1",
    v42.applicable_domains = ["ALL"],
    v42.is_active = true,
    v42.created_date = datetime(),
    v42.last_updated_date = datetime()
WITH v42
MATCH (t:Table {table_id: "DIM_MEDIA"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v42);

// Flight Duration Calculation
MERGE (v43:ValidationRule {rule_id: "VAL_FLIGHT_DURATION_CALC_001"})
SET v43.rule_name = "Flight Duration Calculation Check",
    v43.column_name = "FLIGHT_START_DATE,FLIGHT_END_DATE,FLIGHT_DURATION_DAYS",
    v43.table_id = "DIM_MEDIA",
    v43.validation_type = "CUSTOM",
    v43.validation_rule = "CUSTOM:FLIGHT_DURATION_DAYS=DATEDIFF(FLIGHT_END_DATE,FLIGHT_START_DATE)+1",
    v43.error_message = "Flight duration does not match date difference",
    v43.business_criticality = "medium",
    v43.priority = "P2",
    v43.applicable_domains = ["ALL"],
    v43.is_active = true,
    v43.created_date = datetime(),
    v43.last_updated_date = datetime()
WITH v43
MATCH (t:Table {table_id: "DIM_MEDIA"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v43);

// Media Spend vs CPM and Impressions
MERGE (v44:ValidationRule {rule_id: "VAL_SPEND_CPM_CONSISTENCY_001"})
SET v44.rule_name = "Media Spend vs CPM Consistency Check",
    v44.column_name = "MEDIA_SPEND,CPM_RATE,IMPRESSIONS_TARGET",
    v44.table_id = "DIM_MEDIA",
    v44.validation_type = "CUSTOM",
    v44.validation_rule = "CUSTOM:ABS(MEDIA_SPEND-(CPM_RATE*IMPRESSIONS_TARGET/1000))<1",
    v44.error_message = "Media spend inconsistent with CPM and impressions",
    v44.business_criticality = "high",
    v44.priority = "P1",
    v44.applicable_domains = ["ALL"],
    v44.is_active = true,
    v43.created_date = datetime(),
    v43.last_updated_date = datetime()
WITH v44
MATCH (t:Table {table_id: "DIM_MEDIA"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v44);

// Digital Placement vs Programmatic Flag
MERGE (v45:ValidationRule {rule_id: "VAL_DIGITAL_PROGRAMMATIC_001"})
SET v45.rule_name = "Digital vs Programmatic Consistency Check",
    v45.column_name = "DIGITAL_PLACEMENT_TYPE,PROGRAMMATIC_FLAG",
    v45.table_id = "DIM_MEDIA",
    v45.validation_type = "CUSTOM",
    v45.validation_rule = "CUSTOM:IF(DIGITAL_PLACEMENT_TYPE='Programmatic',PROGRAMMATIC_FLAG=TRUE)",
    v45.error_message = "Programmatic placement must have programmatic flag set",
    v45.business_criticality = "medium",
    v45.priority = "P2",
    v45.applicable_domains = ["ALL"],
    v45.is_active = true,
    v45.created_date = datetime(),
    v45.last_updated_date = datetime()
WITH v45
MATCH (t:Table {table_id: "DIM_MEDIA"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v45);

// TV Media vs Daypart
MERGE (v46:ValidationRule {rule_id: "VAL_TV_DAYPART_001"})
SET v46.rule_name = "TV Media vs Daypart Consistency Check",
    v46.column_name = "MEDIA_TYPE,DAYPART",
    v46.table_id = "DIM_MEDIA",
    v46.validation_type = "CUSTOM",
    v46.validation_rule = "CUSTOM:IF(MEDIA_TYPE='Television',DAYPART!=NULL)",
    v46.error_message = "Television media must have daypart specified",
    v46.business_criticality = "medium",
    v46.priority = "P2",
    v46.applicable_domains = ["ALL"],
    v46.is_active = true,
    v46.created_date = datetime(),
    v46.last_updated_date = datetime()
WITH v46
MATCH (t:Table {table_id: "DIM_MEDIA"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v46);

// Regulatory Approval for Restricted Content
MERGE (v47:ValidationRule {rule_id: "VAL_REGULATORY_CONTENT_001"})
SET v47.rule_name = "Regulatory vs Content Restriction Check",
    v47.column_name = "CONTENT_RESTRICTION_FLAG,REGULATORY_APPROVAL_FLAG",
    v47.table_id = "DIM_MEDIA",
    v47.validation_type = "CUSTOM",
    v47.validation_rule = "CUSTOM:IF(CONTENT_RESTRICTION_FLAG=TRUE,REGULATORY_APPROVAL_FLAG=TRUE)",
    v47.error_message = "Content with restrictions must have regulatory approval",
    v47.business_criticality = "high",
    v47.priority = "P1",
    v47.applicable_domains = ["ALL"],
    v47.is_active = true,
    v47.created_date = datetime(),
    v47.last_updated_date = datetime()
WITH v47
MATCH (t:Table {table_id: "DIM_MEDIA"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v47);

// Child-Directed vs Age Range
MERGE (v48:ValidationRule {rule_id: "VAL_CHILD_AGE_CONSISTENCY_001"})
SET v48.rule_name = "Child-Directed vs Age Range Check",
    v48.column_name = "CHILD_DIRECTED_FLAG,AGE_RANGE",
    v48.table_id = "DIM_MEDIA",
    v48.validation_type = "CUSTOM",
    v48.validation_rule = "CUSTOM:IF(CHILD_DIRECTED_FLAG=TRUE,AGE_RANGE CONTAINS 'Child' OR AGE_RANGE CONTAINS '0-12')",
    v48.error_message = "Child-directed flag inconsistent with age range",
    v48.business_criticality = "critical",
    v48.priority = "P1",
    v48.applicable_domains = ["toys", "baby_products", "food_beverage", "snacks"],
    v48.is_active = true,
    v48.created_date = datetime(),
    v48.last_updated_date = datetime()
WITH v48
MATCH (t:Table {table_id: "DIM_MEDIA"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v48);

// Alcohol Media vs Age Targeting
MERGE (v49:ValidationRule {rule_id: "VAL_ALCOHOL_AGE_001"})
SET v49.rule_name = "Alcohol Media Age Restriction Check",
    v49.column_name = "PRODUCT_CATEGORY,AGE_RANGE",
    v49.table_id = "DIM_MEDIA",
    v49.validation_type = "CUSTOM",
    v49.validation_rule = "CUSTOM:IF(PRODUCT_CATEGORY='Alcoholic Beverages',AGE_RANGE NOT CONTAINS 'Under 21')",
    v49.error_message = "Alcoholic beverage ads cannot target under 21",
    v49.business_criticality = "critical",
    v49.priority = "P1",
    v49.applicable_domains = ["alcoholic_beverages"],
    v49.is_active = true,
    v49.created_date = datetime(),
    v49.last_updated_date = datetime()
WITH v49
MATCH (t:Table {table_id: "DIM_MEDIA"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v49);

// Creative Length vs Media Type
MERGE (v50:ValidationRule {rule_id: "VAL_CREATIVE_LENGTH_TYPE_001"})
SET v50.rule_name = "Creative Length vs Media Type Check",
    v50.column_name = "MEDIA_TYPE,CREATIVE_LENGTH",
    v50.table_id = "DIM_MEDIA",
    v50.validation_type = "CUSTOM",
    v50.validation_rule = "CUSTOM:IF(MEDIA_TYPE IN ('Television','Radio','Digital Video'),CREATIVE_LENGTH!=NULL)",
    v50.error_message = "Video/Audio media must have creative length specified",
    v50.business_criticality = "medium",
    v50.priority = "P2",
    v50.applicable_domains = ["ALL"],
    v50.is_active = true,
    v50.created_date = datetime(),
    v50.last_updated_date = datetime()
WITH v50
MATCH (t:Table {table_id: "DIM_MEDIA"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v50);

// Test Cell vs Optimization Status
MERGE (v51:ValidationRule {rule_id: "VAL_TEST_OPTIMIZATION_001"})
SET v51.rule_name = "Test Cell vs Optimization Status Check",
    v51.column_name = "TEST_CELL_IDENTIFIER,OPTIMIZATION_STATUS",
    v51.table_id = "DIM_MEDIA",
    v51.validation_type = "CUSTOM",
    v51.validation_rule = "CUSTOM:IF(TEST_CELL_IDENTIFIER!=NULL,OPTIMIZATION_STATUS IN ('Testing','Learning'))",
    v51.error_message = "Test cells must have appropriate optimization status",
    v51.business_criticality = "medium",
    v51.priority = "P2",
    v51.applicable_domains = ["ALL"],
    v51.is_active = true,
    v51.created_date = datetime(),
    v51.last_updated_date = datetime()
WITH v51
MATCH (t:Table {table_id: "DIM_MEDIA"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v51);

// ========================================
// QUERY TO VERIFY CREATED VALIDATIONS
// ========================================

// Count validations by type
MATCH (v:ValidationRule {table_id: "DIM_MEDIA"})
RETURN v.validation_type as Type, count(*) as Count
ORDER BY Count DESC;

// Count validations by priority
MATCH (v:ValidationRule {table_id: "DIM_MEDIA"})
RETURN v.priority as Priority, count(*) as Count
ORDER BY Priority;

// Count validations by domain
MATCH (v:ValidationRule {table_id: "DIM_MEDIA"})
UNWIND v.applicable_domains as domain
RETURN domain as Domain, count(DISTINCT v) as ValidationCount
ORDER BY ValidationCount DESC;