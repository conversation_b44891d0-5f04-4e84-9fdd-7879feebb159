rule_id,rule_name,column_name,table_id,validation_type,validation_rule,error_message,business_criticality,priority,applicable_domains,is_active
VAL_MEDIA_ID_001,MEDIA_ID Not Null Check,MEDIA_ID,DIM_MEDIA,NOT_NULL,NOT_NULL,Media ID is required,critical,P1,ALL,true
VAL_MEDIA_ID_002,MEDIA_ID Uniqueness Check,MEDIA_ID,DIM_MEDIA,UNIQUE,UNIQUE,Media ID must be unique,critical,P1,ALL,true
VAL_MEDIA_ID_003,MEDIA_ID Format Check,MEDIA_ID,DIM_MEDIA,REGEX,^[A-Za-z0-9_-]{1\,50}$,Media ID must be alphanumeric with dash/underscore max 50 chars,critical,P1,ALL,true
VAL_CAMPAIGN_ID_001,CAMPAIGN_ID Reference Check,CAMPAIGN_ID,DIM_MEDIA,REFERENCE,FK:DIM_CAMPAIGN.CAMPAIGN_ID,Campaign ID must exist in Campaign dimension,high,P1,ALL,true
VAL_CREATIVE_ID_001,CREATIVE_ID Reference Check,CREATIVE_ID,DIM_MEDIA,REFERENCE,FK:DIM_CREATIVE.CREATIVE_ID,Creative ID must exist in Creative dimension,high,P1,ALL,true
VAL_MEDIA_TYPE_001,MEDIA_TYPE Not Null Check,MEDIA_TYPE,DIM_MEDIA,NOT_NULL,NOT_NULL,Media Type is required,critical,P1,ALL,true
VAL_MEDIA_TYPE_002,MEDIA_TYPE Domain Check,MEDIA_TYPE,DIM_MEDIA,ENUM,ENUM:Television\,Digital\,Print\,Radio\,Outdoor\,Social Media\,Streaming\,Direct Mail\,Cinema,Invalid media type,critical,P1,ALL,true
VAL_AD_FORMAT_001,AD_FORMAT Not Null Check,AD_FORMAT,DIM_MEDIA,NOT_NULL,NOT_NULL,Ad Format is required,high,P1,ALL,true
VAL_MEDIA_SPEND_001,MEDIA_SPEND Not Null Check,MEDIA_SPEND,DIM_MEDIA,NOT_NULL,NOT_NULL,Media Spend is required,critical,P1,ALL,true
VAL_MEDIA_SPEND_002,MEDIA_SPEND Range Check,MEDIA_SPEND,DIM_MEDIA,RANGE,RANGE:0\,999999999999.99,Media Spend must be non-negative,critical,P1,ALL,true
VAL_CPM_RATE_001,CPM_RATE Range Check,CPM_RATE,DIM_MEDIA,RANGE,RANGE:0\,99999.99,CPM Rate must be non-negative,high,P1,ALL,true
VAL_UNIT_RATE_001,UNIT_RATE Range Check,UNIT_RATE,DIM_MEDIA,RANGE,RANGE:0\,99999.99,Unit Rate must be non-negative,medium,P2,ALL,true
VAL_MEDIA_ALLOCATION_PCT_001,MEDIA_ALLOCATION_PERCENT Range Check,MEDIA_ALLOCATION_PERCENT,DIM_MEDIA,RANGE,RANGE:0\,100,Media Allocation must be between 0 and 100,medium,P2,ALL,true
VAL_FLIGHT_START_DATE_001,FLIGHT_START_DATE Not Null Check,FLIGHT_START_DATE,DIM_MEDIA,NOT_NULL,NOT_NULL,Flight Start Date is required,high,P1,ALL,true
VAL_FLIGHT_END_DATE_001,FLIGHT_END_DATE Not Null Check,FLIGHT_END_DATE,DIM_MEDIA,NOT_NULL,NOT_NULL,Flight End Date is required,high,P1,ALL,true
VAL_FLIGHT_DURATION_001,FLIGHT_DURATION_DAYS Range Check,FLIGHT_DURATION_DAYS,DIM_MEDIA,RANGE,RANGE:1\,365,Flight Duration must be between 1 and 365 days,medium,P2,ALL,true
VAL_CAMPAIGN_OBJECTIVE_001,CAMPAIGN_OBJECTIVE Domain Check,CAMPAIGN_OBJECTIVE,DIM_MEDIA,ENUM,ENUM:Brand Awareness\,Consideration\,Trial\,Sales\,Retention\,Launch\,Seasonal,Invalid campaign objective,high,P1,ALL,true
VAL_GENDER_TARGET_001,GENDER_TARGET Domain Check,GENDER_TARGET,DIM_MEDIA,ENUM,ENUM:Male\,Female\,All\,Male Skew\,Female Skew,Invalid gender target,medium,P2,ALL,true
VAL_IMPRESSIONS_TARGET_001,IMPRESSIONS_TARGET Range Check,IMPRESSIONS_TARGET,DIM_MEDIA,RANGE,RANGE:0\,999999999999,Target Impressions must be non-negative,high,P1,ALL,true
VAL_FREQUENCY_TARGET_001,FREQUENCY_TARGET Range Check,FREQUENCY_TARGET,DIM_MEDIA,RANGE,RANGE:0.1\,50,Frequency Target must be between 0.1 and 50,medium,P2,ALL,true
VAL_GRP_TARGET_001,GRP_TARGET Range Check,GRP_TARGET,DIM_MEDIA,RANGE,RANGE:0\,99999.99,GRP Target must be non-negative,medium,P2,ALL,true
VAL_DAYPART_001,DAYPART Domain Check,DAYPART,DIM_MEDIA,ENUM,ENUM:Prime Time\,Daytime\,Late Night\,Early Morning\,Overnight\,Weekend\,All Day,Invalid daypart,medium,P2,ALL,true
VAL_NETWORK_TIER_001,NETWORK_TIER Domain Check,NETWORK_TIER,DIM_MEDIA,ENUM,ENUM:Broadcast\,Cable\,Premium\,Streaming\,Local\,Syndicated,Invalid network tier,medium,P2,ALL,true
VAL_DIGITAL_PLACEMENT_TYPE_001,DIGITAL_PLACEMENT_TYPE Domain Check,DIGITAL_PLACEMENT_TYPE,DIM_MEDIA,ENUM,ENUM:Programmatic\,Direct Buy\,Social Native\,Search\,Display\,Video\,Audio\,Native,Invalid digital placement type,high,P1,ALL,true
VAL_DEVICE_TARGETING_001,DEVICE_TARGETING Domain Check,DEVICE_TARGETING,DIM_MEDIA,ENUM,ENUM:Desktop\,Mobile\,Tablet\,Connected TV\,All Devices\,Mobile Web\,Mobile App,Invalid device targeting,medium,P2,ALL,true
VAL_PROGRAMMATIC_FLAG_001,PROGRAMMATIC_FLAG Boolean Check,PROGRAMMATIC_FLAG,DIM_MEDIA,BOOLEAN,BOOLEAN,Programmatic Flag must be TRUE or FALSE,medium,P2,ALL,true
VAL_CREATIVE_LENGTH_001,CREATIVE_LENGTH Range Check,CREATIVE_LENGTH,DIM_MEDIA,RANGE,RANGE:1\,3600,Creative Length must be between 1 and 3600 seconds,medium,P2,ALL,true
VAL_LANGUAGE_001,LANGUAGE Domain Check,LANGUAGE,DIM_MEDIA,ENUM,ENUM:English\,Spanish\,French\,German\,Italian\,Portuguese\,Chinese\,Japanese\,Korean\,Other,Invalid language,medium,P2,ALL,true
VAL_BRAND_SAFETY_CATEGORY_001,BRAND_SAFETY_CATEGORY Domain Check,BRAND_SAFETY_CATEGORY,DIM_MEDIA,ENUM,ENUM:Safe\,Moderate Risk\,High Risk\,Restricted,Invalid brand safety category,high,P1,ALL,true
VAL_CONTENT_RESTRICTION_FLAG_001,CONTENT_RESTRICTION_FLAG Boolean Check,CONTENT_RESTRICTION_FLAG,DIM_MEDIA,BOOLEAN,BOOLEAN,Content Restriction Flag must be TRUE or FALSE,high,P1,ALL,true
VAL_REGULATORY_APPROVAL_FLAG_001,REGULATORY_APPROVAL_FLAG Boolean Check,REGULATORY_APPROVAL_FLAG,DIM_MEDIA,BOOLEAN,BOOLEAN,Regulatory Approval Flag must be TRUE or FALSE,high,P1,ALL,true
VAL_ALCOHOL_AD_RESTRICTION_001,ALCOHOL_ADVERTISING_RESTRICTION Not Null Check,ALCOHOL_ADVERTISING_RESTRICTION,DIM_MEDIA,NOT_NULL,NOT_NULL,Alcohol advertising restrictions required for alcoholic beverages,critical,P1,alcoholic_beverages,true
VAL_PHARMA_INDICATION_001,PHARMA_INDICATION_CLAIM Not Null Check,PHARMA_INDICATION_CLAIM,DIM_MEDIA,NOT_NULL,NOT_NULL,Pharmaceutical indication claim required for pharma ads,critical,P1,"pharmaceuticals,health_supplements",true
VAL_CHILD_DIRECTED_FLAG_001,CHILD_DIRECTED_FLAG Boolean Check,CHILD_DIRECTED_FLAG,DIM_MEDIA,BOOLEAN,BOOLEAN,Child-Directed Flag must be TRUE or FALSE,critical,P1,"toys,baby_products,food_beverage,snacks",true
VAL_ATTRIBUTION_MODEL_001,ATTRIBUTION_MODEL Domain Check,ATTRIBUTION_MODEL,DIM_MEDIA,ENUM,ENUM:First Touch\,Last Touch\,Multi-Touch\,Linear\,Time Decay\,Position Based\,Data Driven,Invalid attribution model,medium,P2,ALL,true
VAL_TRACKING_URL_001,TRACKING_URL Format Check,TRACKING_URL,DIM_MEDIA,REGEX,^(https?://)?(www\\.)?[a-zA-Z0-9.-]+\\.[a-zA-Z]{2\,}(/.*)?$,Invalid tracking URL format,low,P3,ALL,true
VAL_COMPETITIVE_RESPONSE_FLAG_001,COMPETITIVE_RESPONSE_FLAG Boolean Check,COMPETITIVE_RESPONSE_FLAG,DIM_MEDIA,BOOLEAN,BOOLEAN,Competitive Response Flag must be TRUE or FALSE,medium,P2,ALL,true
VAL_SHARE_OF_VOICE_TARGET_001,SHARE_OF_VOICE_TARGET Range Check,SHARE_OF_VOICE_TARGET,DIM_MEDIA,RANGE,RANGE:0\,100,Share of Voice Target must be between 0 and 100,medium,P2,ALL,true
VAL_OMNICHANNEL_INTEGRATION_001,OMNICHANNEL_INTEGRATION Domain Check,OMNICHANNEL_INTEGRATION,DIM_MEDIA,ENUM,ENUM:High\,Medium\,Low\,None,Invalid omnichannel integration level,medium,P2,ALL,true
VAL_CROSS_MEDIA_SYNERGY_FLAG_001,CROSS_MEDIA_SYNERGY_FLAG Boolean Check,CROSS_MEDIA_SYNERGY_FLAG,DIM_MEDIA,BOOLEAN,BOOLEAN,Cross-Media Synergy Flag must be TRUE or FALSE,medium,P2,ALL,true
VAL_OPTIMIZATION_STATUS_001,OPTIMIZATION_STATUS Domain Check,OPTIMIZATION_STATUS,DIM_MEDIA,ENUM,ENUM:Learning\,Optimizing\,Optimized\,Paused\,Testing\,Complete,Invalid optimization status,medium,P2,ALL,true
VAL_FLIGHT_DATE_CONSISTENCY_001,Flight Start vs End Date Check,"FLIGHT_START_DATE,FLIGHT_END_DATE",DIM_MEDIA,CUSTOM,CUSTOM:FLIGHT_START_DATE<=FLIGHT_END_DATE,Flight start date must be before or equal to end date,high,P1,ALL,true
VAL_FLIGHT_DURATION_CALC_001,Flight Duration Calculation Check,"FLIGHT_START_DATE,FLIGHT_END_DATE,FLIGHT_DURATION_DAYS",DIM_MEDIA,CUSTOM,CUSTOM:FLIGHT_DURATION_DAYS=DATEDIFF(FLIGHT_END_DATE\,FLIGHT_START_DATE)+1,Flight duration does not match date difference,medium,P2,ALL,true
VAL_SPEND_CPM_CONSISTENCY_001,Media Spend vs CPM Consistency Check,"MEDIA_SPEND,CPM_RATE,IMPRESSIONS_TARGET",DIM_MEDIA,CUSTOM,CUSTOM:ABS(MEDIA_SPEND-(CPM_RATE*IMPRESSIONS_TARGET/1000))<1,Media spend inconsistent with CPM and impressions,high,P1,ALL,true
VAL_DIGITAL_PROGRAMMATIC_001,Digital vs Programmatic Consistency Check,"DIGITAL_PLACEMENT_TYPE,PROGRAMMATIC_FLAG",DIM_MEDIA,CUSTOM,CUSTOM:IF(DIGITAL_PLACEMENT_TYPE='Programmatic'\,PROGRAMMATIC_FLAG=TRUE),Programmatic placement must have programmatic flag set,medium,P2,ALL,true
VAL_TV_DAYPART_001,TV Media vs Daypart Consistency Check,"MEDIA_TYPE,DAYPART",DIM_MEDIA,CUSTOM,CUSTOM:IF(MEDIA_TYPE='Television'\,DAYPART!=NULL),Television media must have daypart specified,medium,P2,ALL,true
VAL_REGULATORY_CONTENT_001,Regulatory vs Content Restriction Check,"CONTENT_RESTRICTION_FLAG,REGULATORY_APPROVAL_FLAG",DIM_MEDIA,CUSTOM,CUSTOM:IF(CONTENT_RESTRICTION_FLAG=TRUE\,REGULATORY_APPROVAL_FLAG=TRUE),Content with restrictions must have regulatory approval,high,P1,ALL,true
VAL_CHILD_AGE_CONSISTENCY_001,Child-Directed vs Age Range Check,"CHILD_DIRECTED_FLAG,AGE_RANGE",DIM_MEDIA,CUSTOM,CUSTOM:IF(CHILD_DIRECTED_FLAG=TRUE\,AGE_RANGE CONTAINS 'Child' OR AGE_RANGE CONTAINS '0-12'),Child-directed flag inconsistent with age range,critical,P1,"toys,baby_products,food_beverage,snacks",true
VAL_ALCOHOL_AGE_001,Alcohol Media Age Restriction Check,"PRODUCT_CATEGORY,AGE_RANGE",DIM_MEDIA,CUSTOM,CUSTOM:IF(PRODUCT_CATEGORY='Alcoholic Beverages'\,AGE_RANGE NOT CONTAINS 'Under 21'),Alcoholic beverage ads cannot target under 21,critical,P1,alcoholic_beverages,true
VAL_CREATIVE_LENGTH_TYPE_001,Creative Length vs Media Type Check,"MEDIA_TYPE,CREATIVE_LENGTH",DIM_MEDIA,CUSTOM,CUSTOM:IF(MEDIA_TYPE IN ('Television'\,'Radio'\,'Digital Video')\,CREATIVE_LENGTH!=NULL),Video/Audio media must have creative length specified,medium,P2,ALL,true
VAL_TEST_OPTIMIZATION_001,Test Cell vs Optimization Status Check,"TEST_CELL_IDENTIFIER,OPTIMIZATION_STATUS",DIM_MEDIA,CUSTOM,CUSTOM:IF(TEST_CELL_IDENTIFIER!=NULL\,OPTIMIZATION_STATUS IN ('Testing'\,'Learning')),Test cells must have appropriate optimization status,medium,P2,ALL,true