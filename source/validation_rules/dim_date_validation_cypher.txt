// ========================================
// DATA QUALITY VALIDATION RULES FOR DIM_DATE
// This script creates validation rules and links them to columns
// ========================================

// Create constraint for ValidationRule uniqueness (run once if not exists)
CREATE CONSTRAINT validation_rule_unique IF NOT EXISTS
FOR (v:ValidationRule) REQUIRE v.rule_id IS UNIQUE;

// ========================================
// CORE DATE IDENTIFIERS
// ========================================

// DATE_KEY - NOT NULL
MERGE (v1:ValidationRule {rule_id: "VAL_DATE_KEY_001"})
SET v1.rule_name = "DATE_KEY Not Null Check",
    v1.column_name = "DATE_KEY",
    v1.table_id = "DIM_DATE",
    v1.validation_type = "NOT_NULL",
    v1.validation_rule = "NOT_NULL",
    v1.error_message = "Date Key is required",
    v1.business_criticality = "critical",
    v1.priority = "P1",
    v1.applicable_domains = ["ALL"],
    v1.is_active = true,
    v1.created_date = datetime(),
    v1.last_updated_date = datetime()
WITH v1
MATCH (c:Column {column_id: "COL_DATE_KEY_DIM_401"})
MERGE (c)-[:HAS_VALIDATION]->(v1);

// DATE_KEY - UNIQUE
MERGE (v2:ValidationRule {rule_id: "VAL_DATE_KEY_002"})
SET v2.rule_name = "DATE_KEY Uniqueness Check",
    v2.column_name = "DATE_KEY",
    v2.table_id = "DIM_DATE",
    v2.validation_type = "UNIQUE",
    v2.validation_rule = "UNIQUE",
    v2.error_message = "Date Key must be unique",
    v2.business_criticality = "critical",
    v2.priority = "P1",
    v2.applicable_domains = ["ALL"],
    v2.is_active = true,
    v2.created_date = datetime(),
    v2.last_updated_date = datetime()
WITH v2
MATCH (c:Column {column_id: "COL_DATE_KEY_DIM_401"})
MERGE (c)-[:HAS_VALIDATION]->(v2);

// DATE_KEY - FORMAT
MERGE (v3:ValidationRule {rule_id: "VAL_DATE_KEY_003"})
SET v3.rule_name = "DATE_KEY Format Check",
    v3.column_name = "DATE_KEY",
    v3.table_id = "DIM_DATE",
    v3.validation_type = "REGEX",
    v3.validation_rule = "^[0-9]{8}$",
    v3.error_message = "Date Key must be in YYYYMMDD format (8 digits)",
    v3.business_criticality = "critical",
    v3.priority = "P1",
    v3.applicable_domains = ["ALL"],
    v3.is_active = true,
    v3.created_date = datetime(),
    v3.last_updated_date = datetime()
WITH v3
MATCH (c:Column {column_id: "COL_DATE_KEY_DIM_401"})
MERGE (c)-[:HAS_VALIDATION]->(v3);

// FULL_DATE - NOT NULL
MERGE (v4:ValidationRule {rule_id: "VAL_FULL_DATE_001"})
SET v4.rule_name = "FULL_DATE Not Null Check",
    v4.column_name = "FULL_DATE",
    v4.table_id = "DIM_DATE",
    v4.validation_type = "NOT_NULL",
    v4.validation_rule = "NOT_NULL",
    v4.error_message = "Full Date is required",
    v4.business_criticality = "critical",
    v4.priority = "P1",
    v4.applicable_domains = ["ALL"],
    v4.is_active = true,
    v4.created_date = datetime(),
    v4.last_updated_date = datetime()
WITH v4
MATCH (c:Column {column_id: "COL_FULL_DATE_DIM_402"})
MERGE (c)-[:HAS_VALIDATION]->(v4);

// FULL_DATE - UNIQUE
MERGE (v5:ValidationRule {rule_id: "VAL_FULL_DATE_002"})
SET v5.rule_name = "FULL_DATE Uniqueness Check",
    v5.column_name = "FULL_DATE",
    v5.table_id = "DIM_DATE",
    v5.validation_type = "UNIQUE",
    v5.validation_rule = "UNIQUE",
    v5.error_message = "Full Date must be unique",
    v5.business_criticality = "critical",
    v5.priority = "P1",
    v5.applicable_domains = ["ALL"],
    v5.is_active = true,
    v5.created_date = datetime(),
    v5.last_updated_date = datetime()
WITH v5
MATCH (c:Column {column_id: "COL_FULL_DATE_DIM_402"})
MERGE (c)-[:HAS_VALIDATION]->(v5);

// ========================================
// CALENDAR HIERARCHY - YEAR VALIDATIONS
// ========================================

// YEAR - NOT NULL
MERGE (v6:ValidationRule {rule_id: "VAL_YEAR_001"})
SET v6.rule_name = "YEAR Not Null Check",
    v6.column_name = "YEAR",
    v6.table_id = "DIM_DATE",
    v6.validation_type = "NOT_NULL",
    v6.validation_rule = "NOT_NULL",
    v6.error_message = "Year is required",
    v6.business_criticality = "high",
    v6.priority = "P1",
    v6.applicable_domains = ["ALL"],
    v6.is_active = true,
    v6.created_date = datetime(),
    v6.last_updated_date = datetime()
WITH v6
MATCH (c:Column {column_id: "COL_YEAR_DIM_403"})
MERGE (c)-[:HAS_VALIDATION]->(v6);

// YEAR - RANGE
MERGE (v7:ValidationRule {rule_id: "VAL_YEAR_002"})
SET v7.rule_name = "YEAR Range Check",
    v7.column_name = "YEAR",
    v7.table_id = "DIM_DATE",
    v7.validation_type = "RANGE",
    v7.validation_rule = "RANGE:1900,2100",
    v7.error_message = "Year must be between 1900 and 2100",
    v7.business_criticality = "high",
    v7.priority = "P1",
    v7.applicable_domains = ["ALL"],
    v7.is_active = true,
    v7.created_date = datetime(),
    v7.last_updated_date = datetime()
WITH v7
MATCH (c:Column {column_id: "COL_YEAR_DIM_403"})
MERGE (c)-[:HAS_VALIDATION]->(v7);

// ========================================
// CALENDAR HIERARCHY - QUARTER VALIDATIONS
// ========================================

// QUARTER - NOT NULL
MERGE (v8:ValidationRule {rule_id: "VAL_QUARTER_001"})
SET v8.rule_name = "QUARTER Not Null Check",
    v8.column_name = "QUARTER",
    v8.table_id = "DIM_DATE",
    v8.validation_type = "NOT_NULL",
    v8.validation_rule = "NOT_NULL",
    v8.error_message = "Quarter is required",
    v8.business_criticality = "high",
    v8.priority = "P1",
    v8.applicable_domains = ["ALL"],
    v8.is_active = true,
    v8.created_date = datetime(),
    v8.last_updated_date = datetime()
WITH v8
MATCH (c:Column {column_id: "COL_QUARTER_DIM_405"})
MERGE (c)-[:HAS_VALIDATION]->(v8);

// QUARTER - RANGE
MERGE (v9:ValidationRule {rule_id: "VAL_QUARTER_002"})
SET v9.rule_name = "QUARTER Range Check",
    v9.column_name = "QUARTER",
    v9.table_id = "DIM_DATE",
    v9.validation_type = "RANGE",
    v9.validation_rule = "RANGE:1,4",
    v9.error_message = "Quarter must be between 1 and 4",
    v9.business_criticality = "high",
    v9.priority = "P1",
    v9.applicable_domains = ["ALL"],
    v9.is_active = true,
    v9.created_date = datetime(),
    v9.last_updated_date = datetime()
WITH v9
MATCH (c:Column {column_id: "COL_QUARTER_DIM_405"})
MERGE (c)-[:HAS_VALIDATION]->(v9);

// QUARTER_NAME - ENUM
MERGE (v10:ValidationRule {rule_id: "VAL_QUARTER_NAME_001"})
SET v10.rule_name = "QUARTER_NAME Domain Check",
    v10.column_name = "QUARTER_NAME",
    v10.table_id = "DIM_DATE",
    v10.validation_type = "ENUM",
    v10.validation_rule = "ENUM:Q1,Q2,Q3,Q4",
    v10.error_message = "Quarter name must be Q1, Q2, Q3, or Q4",
    v10.business_criticality = "medium",
    v10.priority = "P2",
    v10.applicable_domains = ["ALL"],
    v10.is_active = true,
    v10.created_date = datetime(),
    v10.last_updated_date = datetime()
WITH v10
MATCH (c:Column {column_id: "COL_QUARTER_NAME_DIM_406"})
MERGE (c)-[:HAS_VALIDATION]->(v10);

// ========================================
// CALENDAR HIERARCHY - MONTH VALIDATIONS
// ========================================

// MONTH - NOT NULL
MERGE (v11:ValidationRule {rule_id: "VAL_MONTH_001"})
SET v11.rule_name = "MONTH Not Null Check",
    v11.column_name = "MONTH",
    v11.table_id = "DIM_DATE",
    v11.validation_type = "NOT_NULL",
    v11.validation_rule = "NOT_NULL",
    v11.error_message = "Month is required",
    v11.business_criticality = "high",
    v11.priority = "P1",
    v11.applicable_domains = ["ALL"],
    v11.is_active = true,
    v11.created_date = datetime(),
    v11.last_updated_date = datetime()
WITH v11
MATCH (c:Column {column_id: "COL_MONTH_DIM_408"})
MERGE (c)-[:HAS_VALIDATION]->(v11);

// MONTH - RANGE
MERGE (v12:ValidationRule {rule_id: "VAL_MONTH_002"})
SET v12.rule_name = "MONTH Range Check",
    v12.column_name = "MONTH",
    v12.table_id = "DIM_DATE",
    v12.validation_type = "RANGE",
    v12.validation_rule = "RANGE:1,12",
    v12.error_message = "Month must be between 1 and 12",
    v12.business_criticality = "high",
    v12.priority = "P1",
    v12.applicable_domains = ["ALL"],
    v12.is_active = true,
    v12.created_date = datetime(),
    v12.last_updated_date = datetime()
WITH v12
MATCH (c:Column {column_id: "COL_MONTH_DIM_408"})
MERGE (c)-[:HAS_VALIDATION]->(v12);

// MONTH_NAME - ENUM
MERGE (v13:ValidationRule {rule_id: "VAL_MONTH_NAME_001"})
SET v13.rule_name = "MONTH_NAME Domain Check",
    v13.column_name = "MONTH_NAME",
    v13.table_id = "DIM_DATE",
    v13.validation_type = "ENUM",
    v13.validation_rule = "ENUM:January,February,March,April,May,June,July,August,September,October,November,December",
    v13.error_message = "Invalid month name",
    v13.business_criticality = "medium",
    v13.priority = "P2",
    v13.applicable_domains = ["ALL"],
    v13.is_active = true,
    v13.created_date = datetime(),
    v13.last_updated_date = datetime()
WITH v13
MATCH (c:Column {column_id: "COL_MONTH_NAME_DIM_409"})
MERGE (c)-[:HAS_VALIDATION]->(v13);

// MONTH_NAME_SHORT - ENUM
MERGE (v14:ValidationRule {rule_id: "VAL_MONTH_NAME_SHORT_001"})
SET v14.rule_name = "MONTH_NAME_SHORT Domain Check",
    v14.column_name = "MONTH_NAME_SHORT",
    v14.table_id = "DIM_DATE",
    v14.validation_type = "ENUM",
    v14.validation_rule = "ENUM:Jan,Feb,Mar,Apr,May,Jun,Jul,Aug,Sep,Oct,Nov,Dec",
    v14.error_message = "Invalid month abbreviation",
    v14.business_criticality = "low",
    v14.priority = "P3",
    v14.applicable_domains = ["ALL"],
    v14.is_active = true,
    v14.created_date = datetime(),
    v14.last_updated_date = datetime()
WITH v14
MATCH (c:Column {column_id: "COL_MONTH_NAME_SHORT_DIM_410"})
MERGE (c)-[:HAS_VALIDATION]->(v14);

// ========================================
// CALENDAR HIERARCHY - WEEK VALIDATIONS
// ========================================

// WEEK - RANGE
MERGE (v15:ValidationRule {rule_id: "VAL_WEEK_001"})
SET v15.rule_name = "WEEK Range Check",
    v15.column_name = "WEEK",
    v15.table_id = "DIM_DATE",
    v15.validation_type = "RANGE",
    v15.validation_rule = "RANGE:1,53",
    v15.error_message = "Week must be between 1 and 53",
    v15.business_criticality = "medium",
    v15.priority = "P2",
    v15.applicable_domains = ["ALL"],
    v15.is_active = true,
    v15.created_date = datetime(),
    v15.last_updated_date = datetime()
WITH v15
MATCH (c:Column {column_id: "COL_WEEK_DIM_412"})
MERGE (c)-[:HAS_VALIDATION]->(v15);

// ISO_WEEK - RANGE
MERGE (v16:ValidationRule {rule_id: "VAL_ISO_WEEK_001"})
SET v16.rule_name = "ISO_WEEK Range Check",
    v16.column_name = "ISO_WEEK",
    v16.table_id = "DIM_DATE",
    v16.validation_type = "RANGE",
    v16.validation_rule = "RANGE:1,53",
    v16.error_message = "ISO Week must be between 1 and 53",
    v16.business_criticality = "medium",
    v16.priority = "P2",
    v16.applicable_domains = ["ALL"],
    v16.is_active = true,
    v16.created_date = datetime(),
    v16.last_updated_date = datetime()
WITH v16
MATCH (c:Column {column_id: "COL_ISO_WEEK_DIM_413"})
MERGE (c)-[:HAS_VALIDATION]->(v16);

// ========================================
// CALENDAR HIERARCHY - DAY VALIDATIONS
// ========================================

// DAY - NOT NULL
MERGE (v17:ValidationRule {rule_id: "VAL_DAY_001"})
SET v17.rule_name = "DAY Not Null Check",
    v17.column_name = "DAY",
    v17.table_id = "DIM_DATE",
    v17.validation_type = "NOT_NULL",
    v17.validation_rule = "NOT_NULL",
    v17.error_message = "Day is required",
    v17.business_criticality = "medium",
    v17.priority = "P2",
    v17.applicable_domains = ["ALL"],
    v17.is_active = true,
    v17.created_date = datetime(),
    v17.last_updated_date = datetime()
WITH v17
MATCH (c:Column {column_id: "COL_DAY_DIM_416"})
MERGE (c)-[:HAS_VALIDATION]->(v17);

// DAY - RANGE
MERGE (v18:ValidationRule {rule_id: "VAL_DAY_002"})
SET v18.rule_name = "DAY Range Check",
    v18.column_name = "DAY",
    v18.table_id = "DIM_DATE",
    v18.validation_type = "RANGE",
    v18.validation_rule = "RANGE:1,31",
    v18.error_message = "Day must be between 1 and 31",
    v18.business_criticality = "medium",
    v18.priority = "P2",
    v18.applicable_domains = ["ALL"],
    v18.is_active = true,
    v18.created_date = datetime(),
    v18.last_updated_date = datetime()
WITH v18
MATCH (c:Column {column_id: "COL_DAY_DIM_416"})
MERGE (c)-[:HAS_VALIDATION]->(v18);

// DAY_OF_WEEK - NOT NULL
MERGE (v19:ValidationRule {rule_id: "VAL_DAY_OF_WEEK_001"})
SET v19.rule_name = "DAY_OF_WEEK Not Null Check",
    v19.column_name = "DAY_OF_WEEK",
    v19.table_id = "DIM_DATE",
    v19.validation_type = "NOT_NULL",
    v19.validation_rule = "NOT_NULL",
    v19.error_message = "Day of week is required",
    v19.business_criticality = "high",
    v19.priority = "P1",
    v19.applicable_domains = ["ALL"],
    v19.is_active = true,
    v19.created_date = datetime(),
    v19.last_updated_date = datetime()
WITH v19
MATCH (c:Column {column_id: "COL_DAY_OF_WEEK_DIM_417"})
MERGE (c)-[:HAS_VALIDATION]->(v19);

// DAY_OF_WEEK - RANGE
MERGE (v20:ValidationRule {rule_id: "VAL_DAY_OF_WEEK_002"})
SET v20.rule_name = "DAY_OF_WEEK Range Check",
    v20.column_name = "DAY_OF_WEEK",
    v20.table_id = "DIM_DATE",
    v20.validation_type = "RANGE",
    v20.validation_rule = "RANGE:1,7",
    v20.error_message = "Day of week must be between 1 and 7",
    v20.business_criticality = "high",
    v20.priority = "P1",
    v20.applicable_domains = ["ALL"],
    v20.is_active = true,
    v20.created_date = datetime(),
    v20.last_updated_date = datetime()
WITH v20
MATCH (c:Column {column_id: "COL_DAY_OF_WEEK_DIM_417"})
MERGE (c)-[:HAS_VALIDATION]->(v20);

// DAY_NAME - ENUM
MERGE (v21:ValidationRule {rule_id: "VAL_DAY_NAME_001"})
SET v21.rule_name = "DAY_NAME Domain Check",
    v21.column_name = "DAY_NAME",
    v21.table_id = "DIM_DATE",
    v21.validation_type = "ENUM",
    v21.validation_rule = "ENUM:Sunday,Monday,Tuesday,Wednesday,Thursday,Friday,Saturday",
    v21.error_message = "Invalid day name",
    v21.business_criticality = "medium",
    v21.priority = "P2",
    v21.applicable_domains = ["ALL"],
    v21.is_active = true,
    v21.created_date = datetime(),
    v21.last_updated_date = datetime()
WITH v21
MATCH (c:Column {column_id: "COL_DAY_NAME_DIM_418"})
MERGE (c)-[:HAS_VALIDATION]->(v21);

// DAY_NAME_SHORT - ENUM
MERGE (v22:ValidationRule {rule_id: "VAL_DAY_NAME_SHORT_001"})
SET v22.rule_name = "DAY_NAME_SHORT Domain Check",
    v22.column_name = "DAY_NAME_SHORT",
    v22.table_id = "DIM_DATE",
    v22.validation_type = "ENUM",
    v22.validation_rule = "ENUM:Sun,Mon,Tue,Wed,Thu,Fri,Sat",
    v22.error_message = "Invalid day abbreviation",
    v22.business_criticality = "low",
    v22.priority = "P3",
    v22.applicable_domains = ["ALL"],
    v22.is_active = true,
    v22.created_date = datetime(),
    v22.last_updated_date = datetime()
WITH v22
MATCH (c:Column {column_id: "COL_DAY_NAME_SHORT_DIM_419"})
MERGE (c)-[:HAS_VALIDATION]->(v22);

// DAY_OF_YEAR - RANGE
MERGE (v23:ValidationRule {rule_id: "VAL_DAY_OF_YEAR_001"})
SET v23.rule_name = "DAY_OF_YEAR Range Check",
    v23.column_name = "DAY_OF_YEAR",
    v23.table_id = "DIM_DATE",
    v23.validation_type = "RANGE",
    v23.validation_rule = "RANGE:1,366",
    v23.error_message = "Day of year must be between 1 and 366",
    v23.business_criticality = "low",
    v23.priority = "P3",
    v23.applicable_domains = ["ALL"],
    v23.is_active = true,
    v23.created_date = datetime(),
    v23.last_updated_date = datetime()
WITH v23
MATCH (c:Column {column_id: "COL_DAY_OF_YEAR_DIM_420"})
MERGE (c)-[:HAS_VALIDATION]->(v23);

// ========================================
// BUSINESS FLAGS AND INDICATORS
// ========================================

// IS_WEEKEND - NOT NULL
MERGE (v24:ValidationRule {rule_id: "VAL_IS_WEEKEND_001"})
SET v24.rule_name = "IS_WEEKEND Not Null Check",
    v24.column_name = "IS_WEEKEND",
    v24.table_id = "DIM_DATE",
    v24.validation_type = "NOT_NULL",
    v24.validation_rule = "NOT_NULL",
    v24.error_message = "Weekend flag is required",
    v24.business_criticality = "high",
    v24.priority = "P1",
    v24.applicable_domains = ["ALL"],
    v24.is_active = true,
    v24.created_date = datetime(),
    v24.last_updated_date = datetime()
WITH v24
MATCH (c:Column {column_id: "COL_IS_WEEKEND_DIM_421"})
MERGE (c)-[:HAS_VALIDATION]->(v24);

// IS_WEEKDAY - NOT NULL
MERGE (v25:ValidationRule {rule_id: "VAL_IS_WEEKDAY_001"})
SET v25.rule_name = "IS_WEEKDAY Not Null Check",
    v25.column_name = "IS_WEEKDAY",
    v25.table_id = "DIM_DATE",
    v25.validation_type = "NOT_NULL",
    v25.validation_rule = "NOT_NULL",
    v25.error_message = "Weekday flag is required",
    v25.business_criticality = "medium",
    v25.priority = "P2",
    v25.applicable_domains = ["ALL"],
    v25.is_active = true,
    v25.created_date = datetime(),
    v25.last_updated_date = datetime()
WITH v25
MATCH (c:Column {column_id: "COL_IS_WEEKDAY_DIM_422"})
MERGE (c)-[:HAS_VALIDATION]->(v25);

// IS_HOLIDAY - NOT NULL
MERGE (v26:ValidationRule {rule_id: "VAL_IS_HOLIDAY_001"})
SET v26.rule_name = "IS_HOLIDAY Not Null Check",
    v26.column_name = "IS_HOLIDAY",
    v26.table_id = "DIM_DATE",
    v26.validation_type = "NOT_NULL",
    v26.validation_rule = "NOT_NULL",
    v26.error_message = "Holiday flag is required",
    v26.business_criticality = "high",
    v26.priority = "P1",
    v26.applicable_domains = ["ALL"],
    v26.is_active = true,
    v26.created_date = datetime(),
    v26.last_updated_date = datetime()
WITH v26
MATCH (c:Column {column_id: "COL_IS_HOLIDAY_DIM_423"})
MERGE (c)-[:HAS_VALIDATION]->(v26);

// IS_LEAP_YEAR - NOT NULL
MERGE (v27:ValidationRule {rule_id: "VAL_IS_LEAP_YEAR_001"})
SET v27.rule_name = "IS_LEAP_YEAR Not Null Check",
    v27.column_name = "IS_LEAP_YEAR",
    v27.table_id = "DIM_DATE",
    v27.validation_type = "NOT_NULL",
    v27.validation_rule = "NOT_NULL",
    v27.error_message = "Leap year flag is required",
    v27.business_criticality = "low",
    v27.priority = "P3",
    v27.applicable_domains = ["ALL"],
    v27.is_active = true,
    v27.created_date = datetime(),
    v27.last_updated_date = datetime()
WITH v27
MATCH (c:Column {column_id: "COL_IS_LEAP_YEAR_DIM_425"})
MERGE (c)-[:HAS_VALIDATION]->(v27);

// ========================================
// SEASONAL AND BUSINESS PERIODS
// ========================================

// SEASON - NOT NULL
MERGE (v28:ValidationRule {rule_id: "VAL_SEASON_001"})
SET v28.rule_name = "SEASON Not Null Check",
    v28.column_name = "SEASON",
    v28.table_id = "DIM_DATE",
    v28.validation_type = "NOT_NULL",
    v28.validation_rule = "NOT_NULL",
    v28.error_message = "Season is required",
    v28.business_criticality = "high",
    v28.priority = "P1",
    v28.applicable_domains = ["ALL"],
    v28.is_active = true,
    v28.created_date = datetime(),
    v28.last_updated_date = datetime()
WITH v28
MATCH (c:Column {column_id: "COL_SEASON_DIM_426"})
MERGE (c)-[:HAS_VALIDATION]->(v28);

// SEASON - ENUM
MERGE (v29:ValidationRule {rule_id: "VAL_SEASON_002"})
SET v29.rule_name = "SEASON Domain Check",
    v29.column_name = "SEASON",
    v29.table_id = "DIM_DATE",
    v29.validation_type = "ENUM",
    v29.validation_rule = "ENUM:Spring,Summer,Fall,Winter",
    v29.error_message = "Invalid season name",
    v29.business_criticality = "high",
    v29.priority = "P1",
    v29.applicable_domains = ["ALL"],
    v29.is_active = true,
    v29.created_date = datetime(),
    v29.last_updated_date = datetime()
WITH v29
MATCH (c:Column {column_id: "COL_SEASON_DIM_426"})
MERGE (c)-[:HAS_VALIDATION]->(v29);

// ========================================
// FISCAL CALENDAR VALIDATIONS
// ========================================

// FISCAL_YEAR - NOT NULL
MERGE (v30:ValidationRule {rule_id: "VAL_FISCAL_YEAR_001"})
SET v30.rule_name = "FISCAL_YEAR Not Null Check",
    v30.column_name = "FISCAL_YEAR",
    v30.table_id = "DIM_DATE",
    v30.validation_type = "NOT_NULL",
    v30.validation_rule = "NOT_NULL",
    v30.error_message = "Fiscal year is required",
    v30.business_criticality = "high",
    v30.priority = "P1",
    v30.applicable_domains = ["ALL"],
    v30.is_active = true,
    v30.created_date = datetime(),
    v30.last_updated_date = datetime()
WITH v30
MATCH (c:Column {column_id: "COL_FISCAL_YEAR_DIM_428"})
MERGE (c)-[:HAS_VALIDATION]->(v30);

// FISCAL_YEAR - RANGE
MERGE (v31:ValidationRule {rule_id: "VAL_FISCAL_YEAR_002"})
SET v31.rule_name = "FISCAL_YEAR Range Check",
    v31.column_name = "FISCAL_YEAR",
    v31.table_id = "DIM_DATE",
    v31.validation_type = "RANGE",
    v31.validation_rule = "RANGE:1900,2100",
    v31.error_message = "Fiscal year must be between 1900 and 2100",
    v31.business_criticality = "high",
    v31.priority = "P1",
    v31.applicable_domains = ["ALL"],
    v31.is_active = true,
    v31.created_date = datetime(),
    v31.last_updated_date = datetime()
WITH v31
MATCH (c:Column {column_id: "COL_FISCAL_YEAR_DIM_428"})
MERGE (c)-[:HAS_VALIDATION]->(v31);

// FISCAL_QUARTER - NOT NULL
MERGE (v32:ValidationRule {rule_id: "VAL_FISCAL_QUARTER_001"})
SET v32.rule_name = "FISCAL_QUARTER Not Null Check",
    v32.column_name = "FISCAL_QUARTER",
    v32.table_id = "DIM_DATE",
    v32.validation_type = "NOT_NULL",
    v32.validation_rule = "NOT_NULL",
    v32.error_message = "Fiscal quarter is required",
    v32.business_criticality = "high",
    v32.priority = "P1",
    v32.applicable_domains = ["ALL"],
    v32.is_active = true,
    v32.created_date = datetime(),
    v32.last_updated_date = datetime()
WITH v32
MATCH (c:Column {column_id: "COL_FISCAL_QUARTER_DIM_429"})
MERGE (c)-[:HAS_VALIDATION]->(v32);

// FISCAL_QUARTER - RANGE
MERGE (v33:ValidationRule {rule_id: "VAL_FISCAL_QUARTER_002"})
SET v33.rule_name = "FISCAL_QUARTER Range Check",
    v33.column_name = "FISCAL_QUARTER",
    v33.table_id = "DIM_DATE",
    v33.validation_type = "RANGE",
    v33.validation_rule = "RANGE:1,4",
    v33.error_message = "Fiscal quarter must be between 1 and 4",
    v33.business_criticality = "high",
    v33.priority = "P1",
    v33.applicable_domains = ["ALL"],
    v33.is_active = true,
    v33.created_date = datetime(),
    v33.last_updated_date = datetime()
WITH v33
MATCH (c:Column {column_id: "COL_FISCAL_QUARTER_DIM_429"})
MERGE (c)-[:HAS_VALIDATION]->(v33);

// FISCAL_MONTH - RANGE
MERGE (v34:ValidationRule {rule_id: "VAL_FISCAL_MONTH_001"})
SET v34.rule_name = "FISCAL_MONTH Range Check",
    v34.column_name = "FISCAL_MONTH",
    v34.table_id = "DIM_DATE",
    v34.validation_type = "RANGE",
    v34.validation_rule = "RANGE:1,12",
    v34.error_message = "Fiscal month must be between 1 and 12",
    v34.business_criticality = "medium",
    v34.priority = "P2",
    v34.applicable_domains = ["ALL"],
    v34.is_active = true,
    v34.created_date = datetime(),
    v34.last_updated_date = datetime()
WITH v34
MATCH (c:Column {column_id: "COL_FISCAL_MONTH_DIM_430"})
MERGE (c)-[:HAS_VALIDATION]->(v34);

// FISCAL_WEEK - RANGE
MERGE (v35:ValidationRule {rule_id: "VAL_FISCAL_WEEK_001"})
SET v35.rule_name = "FISCAL_WEEK Range Check",
    v35.column_name = "FISCAL_WEEK",
    v35.table_id = "DIM_DATE",
    v35.validation_type = "RANGE",
    v35.validation_rule = "RANGE:1,53",
    v35.error_message = "Fiscal week must be between 1 and 53",
    v35.business_criticality = "medium",
    v35.priority = "P2",
    v35.applicable_domains = ["ALL"],
    v35.is_active = true,
    v35.created_date = datetime(),
    v35.last_updated_date = datetime()
WITH v35
MATCH (c:Column {column_id: "COL_FISCAL_WEEK_DIM_431"})
MERGE (c)-[:HAS_VALIDATION]->(v35);

// ========================================
// RETAIL CALENDAR VALIDATIONS
// ========================================

// RETAIL_YEAR - NOT NULL
MERGE (v36:ValidationRule {rule_id: "VAL_RETAIL_YEAR_001"})
SET v36.rule_name = "RETAIL_YEAR Not Null Check",
    v36.column_name = "RETAIL_YEAR",
    v36.table_id = "DIM_DATE",
    v36.validation_type = "NOT_NULL",
    v36.validation_rule = "NOT_NULL",
    v36.error_message = "Retail year is required",
    v36.business_criticality = "high",
    v36.priority = "P1",
    v36.applicable_domains = ["ALL"],
    v36.is_active = true,
    v36.created_date = datetime(),
    v36.last_updated_date = datetime()
WITH v36
MATCH (c:Column {column_id: "COL_RETAIL_YEAR_DIM_432"})
MERGE (c)-[:HAS_VALIDATION]->(v36);

// RETAIL_QUARTER - RANGE
MERGE (v37:ValidationRule {rule_id: "VAL_RETAIL_QUARTER_001"})
SET v37.rule_name = "RETAIL_QUARTER Range Check",
    v37.column_name = "RETAIL_QUARTER",
    v37.table_id = "DIM_DATE",
    v37.validation_type = "RANGE",
    v37.validation_rule = "RANGE:1,4",
    v37.error_message = "Retail quarter must be between 1 and 4",
    v37.business_criticality = "high",
    v37.priority = "P1",
    v37.applicable_domains = ["ALL"],
    v37.is_active = true,
    v37.created_date = datetime(),
    v37.last_updated_date = datetime()
WITH v37
MATCH (c:Column {column_id: "COL_RETAIL_QUARTER_DIM_433"})
MERGE (c)-[:HAS_VALIDATION]->(v37);

// RETAIL_MONTH - RANGE
MERGE (v38:ValidationRule {rule_id: "VAL_RETAIL_MONTH_001"})
SET v38.rule_name = "RETAIL_MONTH Range Check",
    v38.column_name = "RETAIL_MONTH",
    v38.table_id = "DIM_DATE",
    v38.validation_type = "RANGE",
    v38.validation_rule = "RANGE:1,12",
    v38.error_message = "Retail month must be between 1 and 12",
    v38.business_criticality = "medium",
    v38.priority = "P2",
    v38.applicable_domains = ["ALL"],
    v38.is_active = true,
    v38.created_date = datetime(),
    v38.last_updated_date = datetime()
WITH v38
MATCH (c:Column {column_id: "COL_RETAIL_MONTH_DIM_434"})
MERGE (c)-[:HAS_VALIDATION]->(v38);

// RETAIL_WEEK - RANGE
MERGE (v39:ValidationRule {rule_id: "VAL_RETAIL_WEEK_001"})
SET v39.rule_name = "RETAIL_WEEK Range Check",
    v39.column_name = "RETAIL_WEEK",
    v39.table_id = "DIM_DATE",
    v39.validation_type = "RANGE",
    v39.validation_rule = "RANGE:1,53",
    v39.error_message = "Retail week must be between 1 and 53",
    v39.business_criticality = "medium",
    v39.priority = "P2",
    v39.applicable_domains = ["ALL"],
    v39.is_active = true,
    v39.created_date = datetime(),
    v39.last_updated_date = datetime()
WITH v39
MATCH (c:Column {column_id: "COL_RETAIL_WEEK_DIM_435"})
MERGE (c)-[:HAS_VALIDATION]->(v39);

// ========================================
// SPECIAL BUSINESS PERIODS VALIDATIONS
// ========================================

// IS_MONTH_END - NOT NULL
MERGE (v40:ValidationRule {rule_id: "VAL_IS_MONTH_END_001"})
SET v40.rule_name = "IS_MONTH_END Not Null Check",
    v40.column_name = "IS_MONTH_END",
    v40.table_id = "DIM_DATE",
    v40.validation_type = "NOT_NULL",
    v40.validation_rule = "NOT_NULL",
    v40.error_message = "Month end flag is required",
    v40.business_criticality = "medium",
    v40.priority = "P2",
    v40.applicable_domains = ["ALL"],
    v40.is_active = true,
    v40.created_date = datetime(),
    v40.last_updated_date = datetime()
WITH v40
MATCH (c:Column {column_id: "COL_IS_MONTH_END_DIM_439"})
MERGE (c)-[:HAS_VALIDATION]->(v40);

// IS_QUARTER_END - NOT NULL
MERGE (v41:ValidationRule {rule_id: "VAL_IS_QUARTER_END_001"})
SET v41.rule_name = "IS_QUARTER_END Not Null Check",
    v41.column_name = "IS_QUARTER_END",
    v41.table_id = "DIM_DATE",
    v41.validation_type = "NOT_NULL",
    v41.validation_rule = "NOT_NULL",
    v41.error_message = "Quarter end flag is required",
    v41.business_criticality = "high",
    v41.priority = "P1",
    v41.applicable_domains = ["ALL"],
    v41.is_active = true,
    v41.created_date = datetime(),
    v41.last_updated_date = datetime()
WITH v41
MATCH (c:Column {column_id: "COL_IS_QUARTER_END_DIM_440"})
MERGE (c)-[:HAS_VALIDATION]->(v41);

// IS_YEAR_END - NOT NULL
MERGE (v42:ValidationRule {rule_id: "VAL_IS_YEAR_END_001"})
SET v42.rule_name = "IS_YEAR_END Not Null Check",
    v42.column_name = "IS_YEAR_END",
    v42.table_id = "DIM_DATE",
    v42.validation_type = "NOT_NULL",
    v42.validation_rule = "NOT_NULL",
    v42.error_message = "Year end flag is required",
    v42.business_criticality = "high",
    v42.priority = "P1",
    v42.applicable_domains = ["ALL"],
    v42.is_active = true,
    v42.created_date = datetime(),
    v42.last_updated_date = datetime()
WITH v42
MATCH (c:Column {column_id: "COL_IS_YEAR_END_DIM_441"})
MERGE (c)-[:HAS_VALIDATION]->(v42);

// ========================================
// DATE DISPLAY FORMATS VALIDATIONS
// ========================================

// DATE_ISO_FORMAT - FORMAT
MERGE (v43:ValidationRule {rule_id: "VAL_DATE_ISO_FORMAT_001"})
SET v43.rule_name = "DATE_ISO_FORMAT Format Check",
    v43.column_name = "DATE_ISO_FORMAT",
    v43.table_id = "DIM_DATE",
    v43.validation_type = "REGEX",
    v43.validation_rule = "^[0-9]{4}-[0-9]{2}-[0-9]{2}$",
    v43.error_message = "Date ISO format must be YYYY-MM-DD",
    v43.business_criticality = "low",
    v43.priority = "P3",
    v43.applicable_domains = ["ALL"],
    v43.is_active = true,
    v43.created_date = datetime(),
    v43.last_updated_date = datetime()
WITH v43
MATCH (c:Column {column_id: "COL_DATE_ISO_FORMAT_DIM_442"})
MERGE (c)-[:HAS_VALIDATION]->(v43);

// DATE_US_FORMAT - FORMAT
MERGE (v44:ValidationRule {rule_id: "VAL_DATE_US_FORMAT_001"})
SET v44.rule_name = "DATE_US_FORMAT Format Check",
    v44.column_name = "DATE_US_FORMAT",
    v44.table_id = "DIM_DATE",
    v44.validation_type = "REGEX",
    v44.validation_rule = "^[0-9]{2}/[0-9]{2}/[0-9]{4}$",
    v44.error_message = "Date US format must be MM/DD/YYYY",
    v44.business_criticality = "low",
    v44.priority = "P3",
    v44.applicable_domains = ["ALL"],
    v44.is_active = true,
    v44.created_date = datetime(),
    v44.last_updated_date = datetime()
WITH v44
MATCH (c:Column {column_id: "COL_DATE_US_FORMAT_DIM_443"})
MERGE (c)-[:HAS_VALIDATION]->(v44);

// ========================================
// CROSS-FIELD VALIDATIONS
// ========================================

// IS_WEEKEND vs IS_WEEKDAY
MERGE (v45:ValidationRule {rule_id: "VAL_WEEKEND_WEEKDAY_001"})
SET v45.rule_name = "Weekend/Weekday Consistency Check",
    v45.column_name = "IS_WEEKEND,IS_WEEKDAY",
    v45.table_id = "DIM_DATE",
    v45.validation_type = "CUSTOM",
    v45.validation_rule = "CUSTOM:IS_WEEKEND XOR IS_WEEKDAY",
    v45.error_message = "A date must be either weekend or weekday, not both",
    v45.business_criticality = "high",
    v45.priority = "P1",
    v45.applicable_domains = ["ALL"],
    v45.is_active = true,
    v45.created_date = datetime(),
    v45.last_updated_date = datetime()
WITH v45
MATCH (t:Table {table_id: "DIM_DATE"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v45);

// DATE_KEY vs FULL_DATE
MERGE (v46:ValidationRule {rule_id: "VAL_DATE_KEY_CONSISTENCY_001"})
SET v46.rule_name = "Date Key Consistency Check",
    v46.column_name = "DATE_KEY,FULL_DATE",
    v46.table_id = "DIM_DATE",
    v46.validation_type = "CUSTOM",
    v46.validation_rule = "CUSTOM:DATE_KEY=TO_INTEGER(FORMAT(FULL_DATE,'YYYYMMDD'))",
    v46.error_message = "Date key must match full date in YYYYMMDD format",
    v46.business_criticality = "critical",
    v46.priority = "P1",
    v46.applicable_domains = ["ALL"],
    v46.is_active = true,
    v46.created_date = datetime(),
    v46.last_updated_date = datetime()
WITH v46
MATCH (t:Table {table_id: "DIM_DATE"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v46);

// MONTH vs QUARTER
MERGE (v47:ValidationRule {rule_id: "VAL_MONTH_QUARTER_001"})
SET v47.rule_name = "Month-Quarter Consistency Check",
    v47.column_name = "MONTH,QUARTER",
    v47.table_id = "DIM_DATE",
    v47.validation_type = "CUSTOM",
    v47.validation_rule = "CUSTOM:QUARTER=CEILING(MONTH/3)",
    v47.error_message = "Quarter must correspond correctly to month",
    v47.business_criticality = "high",
    v47.priority = "P1",
    v47.applicable_domains = ["ALL"],
    v47.is_active = true,
    v47.created_date = datetime(),
    v47.last_updated_date = datetime()
WITH v47
MATCH (t:Table {table_id: "DIM_DATE"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v47);

// DAY_OF_WEEK vs DAY_NAME
MERGE (v48:ValidationRule {rule_id: "VAL_DAY_OF_WEEK_NAME_001"})
SET v48.rule_name = "Day of Week Name Consistency Check",
    v48.column_name = "DAY_OF_WEEK,DAY_NAME",
    v48.table_id = "DIM_DATE",
    v48.validation_type = "CUSTOM",
    v48.validation_rule = "CUSTOM:DAY_NAME=MAP(DAY_OF_WEEK)",
    v48.error_message = "Day name must match day of week number",
    v48.business_criticality = "medium",
    v48.priority = "P2",
    v48.applicable_domains = ["ALL"],
    v48.is_active = true,
    v48.created_date = datetime(),
    v48.last_updated_date = datetime()
WITH v48
MATCH (t:Table {table_id: "DIM_DATE"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v48);

// IS_LEAP_YEAR vs YEAR
MERGE (v49:ValidationRule {rule_id: "VAL_LEAP_YEAR_001"})
SET v49.rule_name = "Leap Year Consistency Check",
    v49.column_name = "IS_LEAP_YEAR,YEAR",
    v49.table_id = "DIM_DATE",
    v49.validation_type = "CUSTOM",
    v49.validation_rule = "CUSTOM:IS_LEAP_YEAR=((YEAR%4=0 AND YEAR%100<>0) OR YEAR%400=0)",
    v49.error_message = "Leap year flag must be correct for the given year",
    v49.business_criticality = "low",
    v49.priority = "P3",
    v49.applicable_domains = ["ALL"],
    v49.is_active = true,
    v49.created_date = datetime(),
    v49.last_updated_date = datetime()
WITH v49
MATCH (t:Table {table_id: "DIM_DATE"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v49);

// PREVIOUS_DAY_DATE vs FULL_DATE
MERGE (v50:ValidationRule {rule_id: "VAL_PREVIOUS_DAY_001"})
SET v50.rule_name = "Previous Day Date Check",
    v50.column_name = "PREVIOUS_DAY_DATE,FULL_DATE",
    v50.table_id = "DIM_DATE",
    v50.validation_type = "DATE_COMPARISON",
    v50.validation_rule = "DATE_COMPARE:PREVIOUS_DAY_DATE=FULL_DATE-1",
    v50.error_message = "Previous day date must be exactly one day before",
    v50.business_criticality = "low",
    v50.priority = "P3",
    v50.applicable_domains = ["ALL"],
    v50.is_active = true,
    v50.created_date = datetime(),
    v50.last_updated_date = datetime()
WITH v50
MATCH (t:Table {table_id: "DIM_DATE"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v50);

// NEXT_DAY_DATE vs FULL_DATE
MERGE (v51:ValidationRule {rule_id: "VAL_NEXT_DAY_001"})
SET v51.rule_name = "Next Day Date Check",
    v51.column_name = "NEXT_DAY_DATE,FULL_DATE",
    v51.table_id = "DIM_DATE",
    v51.validation_type = "DATE_COMPARISON",
    v51.validation_rule = "DATE_COMPARE:NEXT_DAY_DATE=FULL_DATE+1",
    v51.error_message = "Next day date must be exactly one day after",
    v51.business_criticality = "low",
    v51.priority = "P3",
    v51.applicable_domains = ["ALL"],
    v51.is_active = true,
    v51.created_date = datetime(),
    v51.last_updated_date = datetime()
WITH v51
MATCH (t:Table {table_id: "DIM_DATE"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v51);

// SAME_DAY_LAST_YEAR vs FULL_DATE
MERGE (v52:ValidationRule {rule_id: "VAL_SAME_DAY_LAST_YEAR_001"})
SET v52.rule_name = "Same Day Last Year Check",
    v52.column_name = "SAME_DAY_LAST_YEAR,FULL_DATE",
    v52.table_id = "DIM_DATE",
    v52.validation_type = "DATE_COMPARISON",
    v52.validation_rule = "DATE_COMPARE:SAME_DAY_LAST_YEAR=DATE_SUB(FULL_DATE,INTERVAL 1 YEAR)",
    v52.error_message = "Same day last year must be exactly one year before",
    v52.business_criticality = "medium",
    v52.priority = "P2",
    v52.applicable_domains = ["ALL"],
    v52.is_active = true,
    v52.created_date = datetime(),
    v52.last_updated_date = datetime()
WITH v52
MATCH (t:Table {table_id: "DIM_DATE"})
MERGE (t)-[:HAS_CROSS_FIELD_VALIDATION]->(v52);

// ========================================
// QUERY TO VERIFY CREATED VALIDATIONS
// ========================================

// Count validations by type
MATCH (v:ValidationRule {table_id: "DIM_DATE"})
RETURN v.validation_type as Type, count(*) as Count
ORDER BY Count DESC;

// Count validations by priority
MATCH (v:ValidationRule {table_id: "DIM_DATE"})
RETURN v.priority as Priority, count(*) as Count
ORDER BY Priority;

// List all critical validations
MATCH (v:ValidationRule {table_id: "DIM_DATE"})
WHERE v.business_criticality = "critical"
RETURN v.rule_name as RuleName, v.column_name as ColumnName
ORDER BY v.rule_id;