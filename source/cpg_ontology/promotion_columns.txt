// ========================================
// DIM_PROMOTION - COMPREHENSIVE COLUMN CREATION
// Complete promotion dimension columns for all CPG domains
// ========================================

// Clear existing columns for DIM_PROMOTION (optional)
MATCH (c:Column {table_id: "DIM_PROMOTION"}) DETACH DELETE c;

// ========================================
// CORE PROMOTION IDENTIFIERS AND BASIC ATTRIBUTES
// ========================================

CREATE 
(promotion_id:Column {
    column_id: "COL_PROMOTION_ID_DIM_601",
    column_name: "PROMOTION_ID",
    table_id: "DIM_PROMOTION",
    ordinal_position: 1,
    business_name: "Promotion ID",
    business_description: "Unique identifier for promotional campaigns across all CPG categories and channels. Critical for promotion performance tracking, ROI analysis, trade spend management, and promotional effectiveness measurement across alcoholic beverages, pharmaceuticals, toys, cosmetics, and all consumer product categories.",
    business_synonyms: ["Promotion Key", "Campaign ID", "Promo ID", "Promotion Code", "Campaign Key", "Promotion Reference", "Promo Key", "Campaign Reference"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_primary_key: true,
    business_criticality: "critical"
}),

(promotion_name:Column {
    column_id: "COL_PROMOTION_NAME_602",
    column_name: "PROMOTION_NAME",
    table_id: "DIM_PROMOTION",
    ordinal_position: 2,
    business_name: "Promotion Name",
    business_description: "Descriptive name for promotional campaign used in internal reporting and external communications. Essential for CPG campaign identification, marketing materials, retailer communications, and promotional calendar management across all product categories.",
    business_synonyms: ["Campaign Name", "Promo Name", "Promotion Title", "Campaign Title", "Marketing Campaign", "Promotional Campaign", "Campaign Description"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 200,
    business_criticality: "high"
}),

(promotion_code:Column {
    column_id: "COL_PROMOTION_CODE_603",
    column_name: "PROMOTION_CODE",
    table_id: "DIM_PROMOTION",
    ordinal_position: 3,
    business_name: "Promotion Code",
    business_description: "Short promotional code used in systems, POS terminals, and customer communications. Critical for CPG promotion tracking, coupon validation, digital promotion redemption, and promotional compliance across all channels and categories.",
    business_synonyms: ["Promo Code", "Campaign Code", "Discount Code", "Coupon Code", "Promotion Abbreviation", "Marketing Code", "Promo Abbreviation"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "high"
}),

// ========================================
// PROMOTION CLASSIFICATION AND TYPE
// ========================================

(promotion_type:Column {
    column_id: "COL_PROMOTION_TYPE_604",
    column_name: "PROMOTION_TYPE",
    table_id: "DIM_PROMOTION",
    ordinal_position: 4,
    business_name: "Promotion Type",
    business_description: "Primary classification of promotional activity (Trade Promotion, Consumer Promotion, Digital Campaign, Loyalty Program). Critical for CPG budget allocation, ROI measurement, and strategic planning across all product categories and retail channels.",
    business_synonyms: ["Campaign Type", "Promo Category", "Promotion Category", "Marketing Type", "Campaign Classification", "Promotional Class"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "critical"
}),

(promotion_subtype:Column {
    column_id: "COL_PROMOTION_SUBTYPE_605",
    column_name: "PROMOTION_SUBTYPE",
    table_id: "DIM_PROMOTION",
    ordinal_position: 5,
    business_name: "Promotion Subtype",
    business_description: "Detailed promotion classification (BOGO, Percentage Off, TPR, Display Allowance, etc.). Essential for CPG promotion effectiveness analysis, tactic optimization, and understanding which promotional mechanics drive best results across categories.",
    business_synonyms: ["Promotion Tactic", "Campaign Subtype", "Promo Mechanism", "Discount Type", "Promotional Mechanic", "Campaign Mechanic"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high"
}),

(promotion_objective:Column {
    column_id: "COL_PROMOTION_OBJECTIVE_606",
    column_name: "PROMOTION_OBJECTIVE",
    table_id: "DIM_PROMOTION",
    ordinal_position: 6,
    business_name: "Promotion Objective",
    business_description: "Primary business objective for promotion (Trial Generation, Market Share Growth, Inventory Clearance, New Product Launch, etc.). Important for CPG strategic planning, performance evaluation against goals, and promotional strategy optimization.",
    business_synonyms: ["Campaign Objective", "Marketing Goal", "Promotion Goal", "Campaign Purpose", "Marketing Objective", "Promotional Purpose"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high"
}),

// ========================================
// PROMOTION TIMING AND DURATION
// ========================================

(start_date:Column {
    column_id: "COL_START_DATE_607",
    column_name: "START_DATE",
    table_id: "DIM_PROMOTION",
    ordinal_position: 7,
    business_name: "Promotion Start Date",
    business_description: "Official start date for promotional campaign. Critical for CPG promotional calendar management, seasonal timing optimization, competitive timing, and coordinating marketing efforts across all channels and product categories.",
    business_synonyms: ["Launch Date", "Begin Date", "Campaign Start", "Promotion Begin", "Go-Live Date", "Activation Date"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    max_length: null,
    business_criticality: "critical"
}),

(end_date:Column {
    column_id: "COL_END_DATE_608",
    column_name: "END_DATE",
    table_id: "DIM_PROMOTION",
    ordinal_position: 8,
    business_name: "Promotion End Date",
    business_description: "Official end date for promotional campaign. Essential for CPG budget planning, performance evaluation periods, inventory planning, and ensuring promotional compliance across all retail partners and channels.",
    business_synonyms: ["Expiration Date", "Campaign End", "Promotion Expiry", "Close Date", "Campaign Close", "Termination Date"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    max_length: null,
    business_criticality: "critical"
}),

(duration_days:Column {
    column_id: "COL_DURATION_DAYS_609",
    column_name: "DURATION_DAYS",
    table_id: "DIM_PROMOTION",
    ordinal_position: 9,
    business_name: "Promotion Duration in Days",
    business_description: "Total duration of promotional campaign in days. Important for CPG promotion planning, cost-per-day analysis, optimal duration identification, and understanding promotional fatigue across different product categories.",
    business_synonyms: ["Campaign Length", "Promotion Length", "Duration", "Campaign Days", "Promotional Period", "Campaign Period"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "medium"
}),

(quarter:Column {
    column_id: "COL_QUARTER_610",
    column_name: "QUARTER",
    table_id: "DIM_PROMOTION",
    ordinal_position: 10,
    business_name: "Promotion Quarter",
    business_description: "Calendar quarter when promotion runs for seasonal analysis. Critical for CPG seasonal planning, especially important for toys (Q4 holiday focus), beverages (summer peaks), and understanding quarterly promotional effectiveness patterns.",
    business_synonyms: ["Campaign Quarter", "Promotional Quarter", "Seasonal Quarter", "Marketing Quarter", "Launch Quarter"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "temporal",
    data_type: "VARCHAR",
    max_length: 10,
    business_criticality: "medium"
}),

(season:Column {
    column_id: "COL_SEASON_611",
    column_name: "SEASON",
    table_id: "DIM_PROMOTION",
    ordinal_position: 11,
    business_name: "Promotion Season",
    business_description: "Season when promotion runs for seasonal effectiveness analysis. Essential for CPG seasonal strategy optimization, particularly critical for cosmetics (seasonal launches), toys (holiday seasons), beverages (summer campaigns), and seasonal product categories.",
    business_synonyms: ["Campaign Season", "Promotional Season", "Marketing Season", "Seasonal Period", "Launch Season"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 15,
    business_criticality: "medium"
}),

// ========================================
// FINANCIAL AND INVESTMENT DETAILS
// ========================================

(promotion_budget:Column {
    column_id: "COL_PROMOTION_BUDGET_612",
    column_name: "PROMOTION_BUDGET",
    table_id: "DIM_PROMOTION",
    ordinal_position: 12,
    business_name: "Promotion Budget",
    business_description: "Total allocated budget for promotional campaign. Critical for CPG trade spend management, ROI calculation, budget tracking, and investment optimization across all product categories and promotional activities.",
    business_synonyms: ["Campaign Budget", "Marketing Budget", "Promotional Investment", "Campaign Investment", "Marketing Spend", "Promotion Cost"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 2,
    business_criticality: "critical"
}),

(actual_spend:Column {
    column_id: "COL_ACTUAL_SPEND_613",
    column_name: "ACTUAL_SPEND",
    table_id: "DIM_PROMOTION",
    ordinal_position: 13,
    business_name: "Actual Promotion Spend",
    business_description: "Actual amount spent on promotional campaign for budget variance analysis. Essential for CPG financial control, budget accuracy improvement, and understanding promotional cost efficiency across all categories.",
    business_synonyms: ["Campaign Spend", "Actual Cost", "Promotional Cost", "Campaign Cost", "Marketing Expense", "Actual Investment"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 2,
    business_criticality: "high"
}),

(discount_percentage:Column {
    column_id: "COL_DISCOUNT_PERCENTAGE_614",
    column_name: "DISCOUNT_PERCENTAGE",
    table_id: "DIM_PROMOTION",
    ordinal_position: 14,
    business_name: "Discount Percentage",
    business_description: "Percentage discount offered to consumers or trade partners. Important for CPG price elasticity analysis, promotional depth optimization, and understanding discount sensitivity across different product categories and price points.",
    business_synonyms: ["Discount Rate", "Percentage Off", "Discount Level", "Reduction Percentage", "Price Reduction", "Promotional Discount"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high"
}),

(discount_amount:Column {
    column_id: "COL_DISCOUNT_AMOUNT_615",
    column_name: "DISCOUNT_AMOUNT",
    table_id: "DIM_PROMOTION",
    ordinal_position: 15,
    business_name: "Discount Amount",
    business_description: "Fixed dollar amount discount per unit or transaction. Critical for CPG margin analysis, promotional economics, and understanding absolute value impact across different price points and product categories.",
    business_synonyms: ["Dollar Off", "Fixed Discount", "Absolute Discount", "Price Reduction", "Monetary Discount", "Dollar Reduction"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 2,
    business_criticality: "high"
}),

(minimum_purchase_amount:Column {
    column_id: "COL_MINIMUM_PURCHASE_AMOUNT_616",
    column_name: "MINIMUM_PURCHASE_AMOUNT",
    table_id: "DIM_PROMOTION",
    ordinal_position: 16,
    business_name: "Minimum Purchase Amount",
    business_description: "Minimum purchase requirement to qualify for promotion. Important for CPG basket building strategies, average order value optimization, and understanding promotional thresholds that drive incremental sales.",
    business_synonyms: ["Min Purchase", "Purchase Threshold", "Qualifying Amount", "Minimum Spend", "Purchase Minimum", "Spend Threshold"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 2,
    business_criticality: "medium"
}),

// ========================================
// CHANNEL AND RETAILER INFORMATION
// ========================================

(promotion_channel:Column {
    column_id: "COL_PROMOTION_CHANNEL_617",
    column_name: "PROMOTION_CHANNEL",
    table_id: "DIM_PROMOTION",
    ordinal_position: 17,
    business_name: "Promotion Channel",
    business_description: "Primary distribution channel for promotion (Retail, E-commerce, Direct-to-Consumer, Healthcare, etc.). Critical for CPG channel strategy, omnichannel coordination, and understanding channel-specific promotional effectiveness.",
    business_synonyms: ["Distribution Channel", "Sales Channel", "Marketing Channel", "Campaign Channel", "Promotional Channel", "Channel Type"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high"
}),

(retailer_type:Column {
    column_id: "COL_RETAILER_TYPE_618",
    column_name: "RETAILER_TYPE",
    table_id: "DIM_PROMOTION",
    ordinal_position: 18,
    business_name: "Retailer Type",
    business_description: "Type of retail partner (Supermarket, Drug, Mass, Club, Convenience, Specialty). Essential for CPG trade marketing, format-specific strategies, and understanding promotional performance across different retail formats.",
    business_synonyms: ["Store Format", "Retail Format", "Channel Format", "Partner Type", "Customer Type", "Account Type"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "high"
}),

(geographic_scope:Column {
    column_id: "COL_GEOGRAPHIC_SCOPE_619",
    column_name: "GEOGRAPHIC_SCOPE",
    table_id: "DIM_PROMOTION",
    ordinal_position: 19,
    business_name: "Geographic Scope",
    business_description: "Geographic coverage of promotion (National, Regional, State, Local, Store-Specific). Important for CPG market development, local marketing optimization, and understanding geographic promotional effectiveness patterns.",
    business_synonyms: ["Geographic Coverage", "Market Scope", "Territory Scope", "Regional Coverage", "Market Coverage", "Geographic Reach"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "medium"
}),

(store_count:Column {
    column_id: "COL_STORE_COUNT_620",
    column_name: "STORE_COUNT",
    table_id: "DIM_PROMOTION",
    ordinal_position: 20,
    business_name: "Participating Store Count",
    business_description: "Number of stores participating in promotion for reach analysis. Critical for CPG distribution assessment, promotional scale measurement, and understanding promotional penetration across retail networks.",
    business_synonyms: ["Store Participation", "Store Coverage", "Participating Stores", "Store Reach", "Location Count", "Store Universe"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "medium"
}),

// ========================================
// PRODUCT AND BRAND COVERAGE
// ========================================

(brand_name:Column {
    column_id: "COL_BRAND_NAME_621",
    column_name: "BRAND_NAME",
    table_id: "DIM_PROMOTION",
    ordinal_position: 21,
    business_name: "Promoted Brand Name",
    business_description: "Primary brand featured in promotion for brand-level analysis. Essential for CPG brand management, brand equity measurement, cross-brand promotional coordination, and understanding brand-specific promotional effectiveness.",
    business_synonyms: ["Brand", "Product Brand", "Promotional Brand", "Featured Brand", "Campaign Brand", "Marketing Brand"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "high"
}),

(category_name:Column {
    column_id: "COL_CATEGORY_NAME_622",
    column_name: "CATEGORY_NAME",
    table_id: "DIM_PROMOTION",
    ordinal_position: 22,
    business_name: "Product Category",
    business_description: "Product category featured in promotion for category management analysis. Critical for CPG category strategy, cross-category promotional impact, and understanding category-specific promotional patterns and effectiveness.",
    business_synonyms: ["Product Category", "Category", "Promotional Category", "Product Class", "Category Classification", "Product Group"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "high"
}),

(product_count:Column {
    column_id: "COL_PRODUCT_COUNT_623",
    column_name: "PRODUCT_COUNT",
    table_id: "DIM_PROMOTION",
    ordinal_position: 23,
    business_name: "Promoted Product Count",
    business_description: "Number of SKUs included in promotion for breadth analysis. Important for CPG promotional complexity management, SKU rationalization, and understanding optimal promotional breadth across different categories.",
    business_synonyms: ["SKU Count", "Product SKUs", "Item Count", "Promotional Items", "Product Breadth", "SKU Coverage"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "medium"
}),

(new_product_flag:Column {
    column_id: "COL_NEW_PRODUCT_FLAG_624",
    column_name: "NEW_PRODUCT_FLAG",
    table_id: "DIM_PROMOTION",
    ordinal_position: 24,
    business_name: "New Product Promotion Flag",
    business_description: "Indicates promotion supports new product launch. Critical for CPG innovation marketing, new product velocity tracking, trial generation measurement, and understanding new product promotional strategies.",
    business_synonyms: ["NPD Flag", "Launch Promotion", "Innovation Flag", "New Item Flag", "Launch Support", "Trial Generation"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium"
}),

// ========================================
// TARGETING AND ELIGIBILITY
// ========================================

(target_audience:Column {
    column_id: "COL_TARGET_AUDIENCE_625",
    column_name: "TARGET_AUDIENCE",
    table_id: "DIM_PROMOTION",
    ordinal_position: 25,
    business_name: "Target Audience",
    business_description: "Primary audience targeted by promotion (Families, Millennials, Seniors, Healthcare Professionals, etc.). Essential for CPG audience strategy, message customization, and understanding demographic-specific promotional effectiveness.",
    business_synonyms: ["Target Market", "Audience Segment", "Customer Target", "Demographic Target", "Market Segment", "Consumer Target"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "medium"
}),

(customer_segment:Column {
    column_id: "COL_CUSTOMER_SEGMENT_626",
    column_name: "CUSTOMER_SEGMENT",
    table_id: "DIM_PROMOTION",
    ordinal_position: 26,
    business_name: "Target Customer Segment",
    business_description: "Specific customer segment targeted (Loyal Customers, Price-Sensitive, Premium Buyers, etc.). Important for CPG segmentation strategy, personalized promotions, and understanding segment-specific promotional response patterns.",
    business_synonyms: ["Customer Target", "Segment Target", "Customer Group", "Loyalty Segment", "Value Segment", "Behavioral Segment"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "medium"
}),

(loyalty_members_only:Column {
    column_id: "COL_LOYALTY_MEMBERS_ONLY_627",
    column_name: "LOYALTY_MEMBERS_ONLY",
    table_id: "DIM_PROMOTION",
    ordinal_position: 27,
    business_name: "Loyalty Members Only Flag",
    business_description: "Indicates promotion is exclusive to loyalty program members. Critical for CPG loyalty program value proposition, member retention, membership driving, and understanding loyalty-specific promotional impact.",
    business_synonyms: ["Member Exclusive", "Loyalty Exclusive", "Member Only", "VIP Only", "Program Member", "Exclusive Offer"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium"
}),

(age_restriction:Column {
    column_id: "COL_AGE_RESTRICTION_628",
    column_name: "AGE_RESTRICTION",
    table_id: "DIM_PROMOTION",
    ordinal_position: 28,
    business_name: "Age Restriction",
    business_description: "Age restrictions for promotion participation. Critical for CPG regulatory compliance, especially alcoholic beverages (21+), certain pharmaceuticals, age-appropriate toys, and ensuring legal promotional compliance.",
    business_synonyms: ["Age Limit", "Age Requirement", "Minimum Age", "Age Eligibility", "Age Compliance", "Legal Age"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "medium"
}),

// ========================================
// PROMOTIONAL MECHANICS AND EXECUTION
// ========================================

(mechanic_description:Column {
    column_id: "COL_MECHANIC_DESCRIPTION_629",
    column_name: "MECHANIC_DESCRIPTION",
    table_id: "DIM_PROMOTION",
    ordinal_position: 29,
    business_name: "Promotional Mechanic Description",
    business_description: "Detailed description of promotional mechanics and customer experience. Essential for CPG execution consistency, training materials, compliance verification, and understanding promotional complexity across channels.",
    business_synonyms: ["Promotion Details", "Mechanic Details", "Execution Details", "Promotional Rules", "Campaign Mechanics", "Offer Details"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "TEXT",
    max_length: null,
    business_criticality: "medium"
}),

(redemption_limit:Column {
    column_id: "COL_REDEMPTION_LIMIT_630",
    column_name: "REDEMPTION_LIMIT",
    table_id: "DIM_PROMOTION",
    ordinal_position: 30,
    business_name: "Redemption Limit per Customer",
    business_description: "Maximum number of times each customer can redeem promotion. Important for CPG budget control, fraud prevention, and managing promotional economics while encouraging trial without excessive redemption.",
    business_synonyms: ["Usage Limit", "Customer Limit", "Redemption Cap", "Use Limit", "Per-Customer Limit", "Maximum Uses"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "medium"
}),

(display_support_flag:Column {
    column_id: "COL_DISPLAY_SUPPORT_FLAG_631",
    column_name: "DISPLAY_SUPPORT_FLAG",
    table_id: "DIM_PROMOTION",
    ordinal_position: 31,
    business_name: "Display Support Flag",
    business_description: "Indicates promotion includes retail display support. Critical for CPG trade marketing, in-store visibility, promotional impact measurement, and understanding display-supported promotional effectiveness.",
    business_synonyms: ["Display Flag", "In-Store Display", "Merchandising Support", "Retail Display", "POS Support", "Visual Support"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium"
}),

(advertising_support_flag:Column {
    column_id: "COL_ADVERTISING_SUPPORT_FLAG_632",
    column_name: "ADVERTISING_SUPPORT_FLAG",
    table_id: "DIM_PROMOTION",
    ordinal_position: 32,
    business_name: "Advertising Support Flag",
    business_description: "Indicates promotion includes paid advertising support. Essential for CPG integrated marketing, media synergy measurement, promotional awareness driving, and understanding advertised vs non-advertised promotional performance.",
    business_synonyms: ["Media Support", "Ad Support", "Advertising Flag", "Media Flag", "Paid Media", "Marketing Support"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium"
}),

// ========================================
// DIGITAL AND TECHNOLOGY ATTRIBUTES
// ========================================

(digital_promotion_flag:Column {
    column_id: "COL_DIGITAL_PROMOTION_FLAG_633",
    column_name: "DIGITAL_PROMOTION_FLAG",
    table_id: "DIM_PROMOTION",
    ordinal_position: 33,
    business_name: "Digital Promotion Flag",
    business_description: "Indicates promotion utilizes digital channels and technology. Critical for CPG digital transformation, omnichannel strategy, digital ROI measurement, and understanding digital vs traditional promotional effectiveness.",
    business_synonyms: ["Digital Flag", "Online Promotion", "Digital Campaign", "Tech-Enabled", "Digital Channel", "E-Promotion"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "high"
}),

(mobile_enabled:Column {
    column_id: "COL_MOBILE_ENABLED_634",
    column_name: "MOBILE_ENABLED",
    table_id: "DIM_PROMOTION",
    ordinal_position: 34,
    business_name: "Mobile Enabled Flag",
    business_description: "Indicates promotion is accessible via mobile devices and apps. Important for CPG mobile strategy, mobile commerce growth, and understanding mobile-driven promotional engagement and redemption patterns.",
    business_synonyms: ["Mobile Flag", "App-Enabled", "Mobile Compatible", "Mobile Accessible", "Smartphone Ready", "Mobile Optimized"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium"
}),

(social_media_component:Column {
    column_id: "COL_SOCIAL_MEDIA_COMPONENT_635",
    column_name: "SOCIAL_MEDIA_COMPONENT",
    table_id: "DIM_PROMOTION",
    ordinal_position: 35,
    business_name: "Social Media Component Flag",
    business_description: "Indicates promotion includes social media elements. Essential for CPG social marketing, viral promotion potential, user-generated content, and understanding social media's role in promotional amplification.",
    business_synonyms: ["Social Flag", "Social Media", "Social Component", "Social Integration", "Social Element", "Social Campaign"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium"
}),

(personalization_level:Column {
    column_id: "COL_PERSONALIZATION_LEVEL_636",
    column_name: "PERSONALIZATION_LEVEL",
    table_id: "DIM_PROMOTION",
    ordinal_position: 36,
    business_name: "Personalization Level",
    business_description: "Degree of personalization in promotional offer (None, Basic, Advanced, Individual). Important for CPG customer experience optimization, personalized marketing effectiveness, and understanding personalization impact on engagement.",
    business_synonyms: ["Customization Level", "Personal Targeting", "Individual Targeting", "Personalized Offer", "Custom Level", "Tailored Level"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "medium"
}),

// ========================================
// PERFORMANCE TRACKING AND MEASUREMENT
// ========================================

(performance_goal:Column {
    column_id: "COL_PERFORMANCE_GOAL_637",
    column_name: "PERFORMANCE_GOAL",
    table_id: "DIM_PROMOTION",
    ordinal_position: 37,
    business_name: "Performance Goal",
    business_description: "Primary performance metric and target for promotion (Sales Lift %, Market Share Growth, Trial Rate, etc.). Critical for CPG performance evaluation, promotional ROI assessment, and strategic planning effectiveness measurement.",
    business_synonyms: ["Success Metric", "Performance Target", "Campaign Goal", "Success Measure", "KPI Target", "Performance KPI"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "high"
}),

(target_sales_lift:Column {
    column_id: "COL_TARGET_SALES_LIFT_638",
    column_name: "TARGET_SALES_LIFT",
    table_id: "DIM_PROMOTION",
    ordinal_position: 38,
    business_name: "Target Sales Lift Percentage",
    business_description: "Expected sales increase percentage from promotional activity. Essential for CPG promotional planning, budget justification, ROI calculation, and understanding promotional effectiveness expectations across categories.",
    business_synonyms: ["Sales Lift Target", "Expected Lift", "Sales Increase Goal", "Volume Target", "Lift Goal", "Sales Goal"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 2,
    business_criticality: "high"
}),

(expected_redemption_rate:Column {
    column_id: "COL_EXPECTED_REDEMPTION_RATE_639",
    column_name: "EXPECTED_REDEMPTION_RATE",
    table_id: "DIM_PROMOTION",
    ordinal_position: 39,
    business_name: "Expected Redemption Rate",
    business_description: "Anticipated percentage of distributed promotions that will be redeemed. Important for CPG budget planning, inventory preparation, and understanding promotional take-up rates across different mechanics and channels.",
    business_synonyms: ["Redemption Target", "Take Rate", "Usage Rate", "Participation Rate", "Redemption Goal", "Response Rate"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "medium"
}),

// ========================================
// APPROVAL AND COMPLIANCE
// ========================================

(approval_status:Column {
    column_id: "COL_APPROVAL_STATUS_640",
    column_name: "APPROVAL_STATUS",
    table_id: "DIM_PROMOTION",
    ordinal_position: 40,
    business_name: "Approval Status",
    business_description: "Current approval status of promotion (Draft, Pending, Approved, Rejected, Cancelled). Critical for CPG governance, compliance management, promotional workflow, and ensuring all promotions meet legal and brand standards.",
    business_synonyms: ["Status", "Approval State", "Campaign Status", "Review Status", "Authorization Status", "Compliance Status"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "critical"
}),

(approved_by:Column {
    column_id: "COL_APPROVED_BY_641",
    column_name: "APPROVED_BY",
    table_id: "DIM_PROMOTION",
    ordinal_position: 41,
    business_name: "Approved By",
    business_description: "Person or role who approved the promotion for accountability tracking. Important for CPG audit trails, responsibility tracking, approval authority management, and compliance verification processes.",
    business_synonyms: ["Approver", "Authorization By", "Approved Person", "Signoff By", "Authorized By", "Approval Authority"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "medium"
}),

(approval_date:Column {
    column_id: "COL_APPROVAL_DATE_642",
    column_name: "APPROVAL_DATE",
    table_id: "DIM_PROMOTION",
    ordinal_position: 42,
    business_name: "Approval Date",
    business_description: "Date when promotion was officially approved. Essential for CPG audit trails, timing analysis, approval process efficiency measurement, and compliance documentation for regulatory purposes.",
    business_synonyms: ["Approved Date", "Authorization Date", "Signoff Date", "Approval Timestamp", "Authorized Date", "Compliance Date"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    max_length: null,
    business_criticality: "medium"
}),

(legal_review_required:Column {
    column_id: "COL_LEGAL_REVIEW_REQUIRED_643",
    column_name: "LEGAL_REVIEW_REQUIRED",
    table_id: "DIM_PROMOTION",
    ordinal_position: 43,
    business_name: "Legal Review Required Flag",
    business_description: "Indicates promotion requires legal review for compliance. Critical for CPG risk management, especially important for alcoholic beverages, pharmaceuticals, health claims, and ensuring regulatory compliance across all categories.",
    business_synonyms: ["Legal Review", "Legal Flag", "Compliance Review", "Legal Required", "Regulatory Review", "Legal Approval"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "high"
}),

// ========================================
// DOMAIN-SPECIFIC ATTRIBUTES
// ========================================

(responsible_drinking_message:Column {
    column_id: "COL_RESPONSIBLE_DRINKING_MSG_644",
    column_name: "RESPONSIBLE_DRINKING_MESSAGE",
    table_id: "DIM_PROMOTION",
    ordinal_position: 44,
    business_name: "Responsible Drinking Message Flag",
    business_description: "Indicates inclusion of responsible drinking messaging in alcoholic beverage promotions. Critical for regulatory compliance, brand responsibility, social responsibility standards, and meeting industry self-regulation requirements.",
    business_synonyms: ["Responsible Message", "Drink Responsibly", "Moderation Message", "Responsibility Flag", "Social Responsibility", "Drinking Message"],
    applicable_domains: ["alcoholic_beverages"],
    domain_specific: true,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "critical",
    regulatory_relevance: ["TTB", "STATE_LIQUOR_BOARDS", "SELF_REGULATION"]
}),

(health_claim_type:Column {
    column_id: "COL_HEALTH_CLAIM_TYPE_645",
    column_name: "HEALTH_CLAIM_TYPE",
    table_id: "DIM_PROMOTION",
    ordinal_position: 45,
    business_name: "Health Claim Type",
    business_description: "Type of health claims made in promotional materials (FDA Approved, Structure/Function, Qualified Health Claim, etc.). Essential for pharmaceutical and supplement promotions, regulatory compliance, and avoiding unauthorized health claims.",
    business_synonyms: ["Health Claim", "Medical Claim", "Health Statement", "Therapeutic Claim", "Wellness Claim", "Benefit Claim"],
    applicable_domains: ["pharmaceuticals", "health_supplements", "food_beverage"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "critical",
    regulatory_relevance: ["FDA", "FTC"]
}),

(age_appropriate_rating:Column {
    column_id: "COL_AGE_APPROPRIATE_RATING_646",
    column_name: "AGE_APPROPRIATE_RATING",
    table_id: "DIM_PROMOTION",
    ordinal_position: 46,
    business_name: "Age Appropriate Rating",
    business_description: "Age appropriateness rating for toy and children's product promotions. Critical for safety compliance, appropriate targeting, parental guidance, and meeting toy industry safety and marketing standards.",
    business_synonyms: ["Age Rating", "Safety Rating", "Age Guidance", "Child Safety", "Age Suitability", "Safety Classification"],
    applicable_domains: ["toys", "baby_products"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "high",
    regulatory_relevance: ["CPSC", "ASTM"]
}),

(organic_certified_flag:Column {
    column_id: "COL_ORGANIC_CERTIFIED_FLAG_647",
    column_name: "ORGANIC_CERTIFIED_FLAG",
    table_id: "DIM_PROMOTION",
    ordinal_position: 47,
    business_name: "Organic Certified Flag",
    business_description: "Indicates promotion features USDA Organic certified products. Important for organic food, personal care, and baby product promotions, premium positioning, health-conscious targeting, and regulatory compliance.",
    business_synonyms: ["Organic Flag", "USDA Organic", "Certified Organic", "Organic Product", "Natural Flag", "Organic Claim"],
    applicable_domains: ["food_beverage", "personal_care", "baby_products", "pet_food", "snacks", "dairy"],
    domain_specific: true,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium",
    regulatory_relevance: ["USDA", "ORGANIC_CERTIFICATION"]
}),

(prescription_required_flag:Column {
    column_id: "COL_PRESCRIPTION_REQUIRED_FLAG_648",
    column_name: "PRESCRIPTION_REQUIRED_FLAG",
    table_id: "DIM_PROMOTION",
    ordinal_position: 48,
    business_name: "Prescription Required Flag",
    business_description: "Indicates promotion applies to prescription-only medicines. Critical for pharmaceutical promotions, healthcare provider targeting, regulatory compliance, and ensuring appropriate professional audience targeting.",
    business_synonyms: ["Rx Required", "Prescription Only", "Rx Flag", "Professional Only", "Healthcare Provider", "Prescription Product"],
    applicable_domains: ["pharmaceuticals"],
    domain_specific: true,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "critical",
    regulatory_relevance: ["FDA", "DEA"]
}),

// ========================================
// COMPETITIVE AND MARKET CONTEXT
// ========================================

(competitive_response:Column {
    column_id: "COL_COMPETITIVE_RESPONSE_649",
    column_name: "COMPETITIVE_RESPONSE",
    table_id: "DIM_PROMOTION",
    ordinal_position: 49,
    business_name: "Competitive Response Flag",
    business_description: "Indicates promotion is in response to competitor activity. Important for CPG competitive strategy, market defense, reactive planning, and understanding competitive promotional dynamics across categories.",
    business_synonyms: ["Competitive Flag", "Response Campaign", "Defensive Promotion", "Competitor Response", "Market Defense", "Reactive Promotion"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium"
}),

(market_share_defense:Column {
    column_id: "COL_MARKET_SHARE_DEFENSE_650",
    column_name: "MARKET_SHARE_DEFENSE",
    table_id: "DIM_PROMOTION",
    ordinal_position: 50,
    business_name: "Market Share Defense Flag",
    business_description: "Indicates promotion aims to defend current market position. Essential for CPG defensive strategy, market position maintenance, competitive pressure response, and understanding share protection investments.",
    business_synonyms: ["Share Defense", "Position Defense", "Defensive Strategy", "Market Protection", "Share Protection", "Market Defense"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium"
}),

// ========================================
// INVENTORY AND SUPPLY CHAIN
// ========================================

(inventory_clearance_flag:Column {
    column_id: "COL_INVENTORY_CLEARANCE_FLAG_651",
    column_name: "INVENTORY_CLEARANCE_FLAG",
    table_id: "DIM_PROMOTION",
    ordinal_position: 51,
    business_name: "Inventory Clearance Flag",
    business_description: "Indicates promotion designed to clear excess inventory. Important for CPG inventory management, margin impact understanding, supply chain optimization, and distinguishing clearance from strategic promotions.",
    business_synonyms: ["Clearance Flag", "Inventory Reduction", "Stock Clearance", "Overstock Flag", "Liquidation Flag", "Inventory Management"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium"
}),

(seasonal_inventory_flag:Column {
    column_id: "COL_SEASONAL_INVENTORY_FLAG_652",
    column_name: "SEASONAL_INVENTORY_FLAG",
    table_id: "DIM_PROMOTION",
    ordinal_position: 52,
    business_name: "Seasonal Inventory Flag",
    business_description: "Indicates promotion targets seasonal product inventory. Critical for CPG seasonal planning, especially important for toys (post-holiday clearance), cosmetics (seasonal colors), and understanding seasonal inventory patterns.",
    business_synonyms: ["Seasonal Flag", "Holiday Clearance", "Seasonal Product", "Holiday Inventory", "Seasonal Stock", "Time-Sensitive"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium"
}),

// ========================================
// COMMUNICATION AND MESSAGING
// ========================================

(promotion_theme:Column {
    column_id: "COL_PROMOTION_THEME_653",
    column_name: "PROMOTION_THEME",
    table_id: "DIM_PROMOTION",
    ordinal_position: 53,
    business_name: "Promotion Theme",
    business_description: "Overarching theme or message of promotional campaign (Health & Wellness, Family Fun, Premium Quality, Value, etc.). Essential for CPG brand consistency, message tracking, and understanding thematic promotional effectiveness.",
    business_synonyms: ["Campaign Theme", "Marketing Theme", "Message Theme", "Promotional Message", "Campaign Message", "Brand Theme"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "medium"
}),

(creative_concept:Column {
    column_id: "COL_CREATIVE_CONCEPT_654",
    column_name: "CREATIVE_CONCEPT",
    table_id: "DIM_PROMOTION",
    ordinal_position: 54,
    business_name: "Creative Concept",
    business_description: "Creative execution concept for promotional materials. Important for CPG creative effectiveness measurement, brand consistency, creative testing, and understanding creative impact on promotional performance.",
    business_synonyms: ["Creative Theme", "Visual Concept", "Design Concept", "Creative Execution", "Creative Idea", "Campaign Creative"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "low"
}),

// ========================================
// PARTNERSHIP AND CO-MARKETING
// ========================================

(co_marketing_partner:Column {
    column_id: "COL_CO_MARKETING_PARTNER_655",
    column_name: "CO_MARKETING_PARTNER",
    table_id: "DIM_PROMOTION",
    ordinal_position: 55,
    business_name: "Co-Marketing Partner",
    business_description: "Partner organization for joint promotional activities. Important for CPG partnership strategy, co-op marketing, cross-promotion opportunities, and understanding partnership promotional effectiveness across categories.",
    business_synonyms: ["Marketing Partner", "Joint Partner", "Co-Promotion Partner", "Collaboration Partner", "Alliance Partner", "Promotional Partner"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "low"
}),

(cross_brand_promotion:Column {
    column_id: "COL_CROSS_BRAND_PROMOTION_656",
    column_name: "CROSS_BRAND_PROMOTION",
    table_id: "DIM_PROMOTION",
    ordinal_position: 56,
    business_name: "Cross-Brand Promotion Flag",
    business_description: "Indicates promotion involves multiple brands from same company. Essential for CPG portfolio optimization, brand synergy measurement, cross-selling effectiveness, and understanding multi-brand promotional strategies.",
    business_synonyms: ["Multi-Brand", "Portfolio Promotion", "Brand Bundle", "Cross-Selling", "Brand Collaboration", "Portfolio Flag"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium"
}),

// ========================================
// PROMOTION LIFECYCLE AND MANAGEMENT
// ========================================

(promotion_manager:Column {
    column_id: "COL_PROMOTION_MANAGER_657",
    column_name: "PROMOTION_MANAGER",
    table_id: "DIM_PROMOTION",
    ordinal_position: 57,
    business_name: "Promotion Manager",
    business_description: "Person responsible for managing promotional campaign. Important for CPG accountability, performance responsibility, contact management, and understanding promotional management effectiveness by individual or team.",
    business_synonyms: ["Campaign Manager", "Marketing Manager", "Promo Manager", "Campaign Lead", "Marketing Lead", "Promotion Owner"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "low"
}),

(business_unit:Column {
    column_id: "COL_BUSINESS_UNIT_658",
    column_name: "BUSINESS_UNIT",
    table_id: "DIM_PROMOTION",
    ordinal_position: 58,
    business_name: "Business Unit",
    business_description: "Business unit or division responsible for promotion. Critical for CPG organizational reporting, budget allocation, P&L responsibility, and understanding promotional performance by business unit structure.",
    business_synonyms: ["Division", "Business Division", "Operating Unit", "BU", "Organizational Unit", "Department"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "medium"
}),

(promotion_priority:Column {
    column_id: "COL_PROMOTION_PRIORITY_659",
    column_name: "PROMOTION_PRIORITY",
    table_id: "DIM_PROMOTION",
    ordinal_position: 59,
    business_name: "Promotion Priority Level",
    business_description: "Priority level for promotional campaign (High, Medium, Low). Important for CPG resource allocation, execution prioritization, support level determination, and understanding priority impact on promotional success.",
    business_synonyms: ["Priority Level", "Campaign Priority", "Importance Level", "Priority Ranking", "Strategic Priority", "Execution Priority"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 15,
    business_criticality: "medium"
}),

// ========================================
// METADATA AND AUDIT INFORMATION
// ========================================

(created_by:Column {
    column_id: "COL_CREATED_BY_660",
    column_name: "CREATED_BY",
    table_id: "DIM_PROMOTION",
    ordinal_position: 60,
    business_name: "Created By",
    business_description: "User who created the promotional campaign record. Important for CPG audit trails, accountability tracking, user activity monitoring, and understanding promotion creation patterns by individual or team.",
    business_synonyms: ["Creator", "Author", "Created User", "Originated By", "Campaign Creator", "Record Creator"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "low"
}),

(created_timestamp:Column {
    column_id: "COL_CREATED_TIMESTAMP_661",
    column_name: "CREATED_TIMESTAMP",
    table_id: "DIM_PROMOTION",
    ordinal_position: 61,
    business_name: "Record Created Timestamp",
    business_description: "System timestamp when promotion record was created. Used for audit trails, data lineage tracking, and data governance. Immutable after creation. Part of comprehensive data governance framework.",
    business_synonyms: ["Creation Time", "Insert Timestamp", "Created Date", "Record Creation", "Entry Timestamp", "Initial Load Time"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    max_length: null,
    business_criticality: "low"
}),

(modified_timestamp:Column {
    column_id: "COL_MODIFIED_TIMESTAMP_662",
    column_name: "MODIFIED_TIMESTAMP",
    table_id: "DIM_PROMOTION",
    ordinal_position: 62,
    business_name: "Last Modified Timestamp",
    business_description: "Most recent update timestamp for promotion record. Tracks data freshness, change frequency, and maintenance activities. Updated automatically with any field modification for data governance compliance.",
    business_synonyms: ["Update Time", "Last Modified", "Change Timestamp", "Modified Date", "Last Updated", "Revision Timestamp"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    max_length: null,
    business_criticality: "low"
});

// ========================================
// CREATE TABLE-COLUMN RELATIONSHIPS
// ========================================

MATCH (t:Table {table_id: "DIM_PROMOTION"})
MATCH (c:Column {table_id: "DIM_PROMOTION"})
CREATE (t)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"]
}]->(c);

// ========================================
// VERIFICATION QUERIES
// ========================================

// Count total columns created
MATCH (c:Column {table_id: "DIM_PROMOTION"})
RETURN count(c) AS total_columns;

// Verify columns by business criticality
MATCH (c:Column {table_id: "DIM_PROMOTION"})
RETURN c.business_criticality AS criticality_level, 
       count(c) AS column_count
ORDER BY criticality_level;

// List critical columns
MATCH (c:Column {table_id: "DIM_PROMOTION"})
WHERE c.business_criticality = "critical"
RETURN c.business_name AS critical_column, 
       c.business_description
ORDER BY c.ordinal_position;

// Check domain-specific columns
MATCH (c:Column {table_id: "DIM_PROMOTION"})
WHERE c.domain_specific = true
RETURN c.business_name AS domain_specific_column, 
       c.applicable_domains AS specific_domains,
       c.regulatory_relevance AS regulations
ORDER BY c.ordinal_position;

// Check columns by category
MATCH (c:Column {table_id: "DIM_PROMOTION"})
RETURN 
    CASE 
        WHEN c.ordinal_position <= 3 THEN "Core Identifiers"
        WHEN c.ordinal_position <= 6 THEN "Classification & Type"
        WHEN c.ordinal_position <= 11 THEN "Timing & Duration"
        WHEN c.ordinal_position <= 16 THEN "Financial & Investment"
        WHEN c.ordinal_position <= 20 THEN "Channel & Retailer"
        WHEN c.ordinal_position <= 24 THEN "Product & Brand Coverage"
        WHEN c.ordinal_position <= 28 THEN "Targeting & Eligibility"
        WHEN c.ordinal_position <= 32 THEN "Mechanics & Execution"
        WHEN c.ordinal_position <= 36 THEN "Digital & Technology"
        WHEN c.ordinal_position <= 39 THEN "Performance & Measurement"
        WHEN c.ordinal_position <= 43 THEN "Approval & Compliance"
        WHEN c.ordinal_position <= 48 THEN "Domain-Specific Attributes"
        WHEN c.ordinal_position <= 50 THEN "Competitive & Market Context"
        WHEN c.ordinal_position <= 52 THEN "Inventory & Supply Chain"
        WHEN c.ordinal_position <= 54 THEN "Communication & Messaging"
        WHEN c.ordinal_position <= 56 THEN "Partnership & Co-Marketing"
        WHEN c.ordinal_position <= 59 THEN "Lifecycle & Management"
        ELSE "Metadata & Audit"
    END AS category,
    count(c) AS column_count
ORDER BY category;

// Verify semantic types distribution
MATCH (c:Column {table_id: "DIM_PROMOTION"})
RETURN c.semantic_type AS semantic_type, 
       count(c) AS column_count
ORDER BY column_count DESC;

// Check for primary key
MATCH (c:Column {table_id: "DIM_PROMOTION"})
WHERE c.is_primary_key = true
RETURN c.business_name AS primary_key_column, 
       c.column_name AS column_name;

// Verify regulatory relevance columns
MATCH (c:Column {table_id: "DIM_PROMOTION"})
WHERE c.regulatory_relevance IS NOT NULL
RETURN c.business_name AS regulatory_column, 
       c.regulatory_relevance AS regulations,
       c.applicable_domains AS domains
ORDER BY c.ordinal_position;

// Check flag/boolean columns
MATCH (c:Column {table_id: "DIM_PROMOTION"})
WHERE c.data_type = "BOOLEAN"
RETURN c.business_name AS boolean_column,
       c.business_description
ORDER BY c.ordinal_position;

// Verify financial columns
MATCH (c:Column {table_id: "DIM_PROMOTION"})
WHERE c.semantic_type = "measure" AND c.data_type = "DECIMAL"
RETURN c.business_name AS financial_column,
       c.precision AS precision,
       c.scale AS scale
ORDER BY c.ordinal_position;

// Check approval and compliance columns
MATCH (c:Column {table_id: "DIM_PROMOTION"})
WHERE c.business_name CONTAINS "Approval" OR c.business_name CONTAINS "Legal" OR c.business_name CONTAINS "Compliance"
RETURN c.business_name AS compliance_column,
       c.business_criticality AS criticality
ORDER BY c.ordinal_position;

// ========================================
// END OF DIM_PROMOTION COLUMN CREATION
// ========================================