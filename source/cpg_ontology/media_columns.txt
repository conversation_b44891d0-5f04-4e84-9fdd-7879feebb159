// ========================================
// DIM_MEDIA - COMPREHENSIVE COLUMN CREATION
// Complete media dimension columns for all CPG domains
// ========================================

// Clear existing columns for DIM_MEDIA (optional)
MATCH (c:Column {table_id: "DIM_MEDIA"}) DETACH DELETE c;

// ========================================
// CORE MEDIA IDENTIFIERS AND BASIC ATTRIBUTES
// ========================================

CREATE 
(media_id:Column {
    column_id: "COL_MEDIA_ID_DIM_801",
    column_name: "MEDIA_ID",
    table_id: "DIM_MEDIA",
    ordinal_position: 1,
    business_name: "Media ID",
    business_description: "Unique identifier for media placements, campaigns, and creative executions across all CPG advertising channels. Critical for media performance tracking, attribution analysis, campaign optimization, and ROI measurement across alcoholic beverages, pharmaceuticals, toys, cosmetics, and all consumer product categories.",
    business_synonyms: ["Media Key", "Placement ID", "Media Code", "Ad ID", "Media Reference", "Campaign Placement ID", "Media Unit ID"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_primary_key: true,
    business_criticality: "critical"
}),

(campaign_id:Column {
    column_id: "COL_CAMPAIGN_ID_802",
    column_name: "CAMPAIGN_ID",
    table_id: "DIM_MEDIA",
    ordinal_position: 2,
    business_name: "Campaign ID",
    business_description: "Identifier linking media placement to broader marketing campaign. Essential for CPG integrated marketing analysis, cross-channel campaign performance, holistic campaign ROI measurement, and coordinating media efforts across all touchpoints.",
    business_synonyms: ["Campaign Key", "Campaign Code", "Marketing Campaign ID", "Campaign Reference", "Integrated Campaign ID", "Campaign Number"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_foreign_key: true,
    business_criticality: "high"
}),

(creative_id:Column {
    column_id: "COL_CREATIVE_ID_803",
    column_name: "CREATIVE_ID",
    table_id: "DIM_MEDIA",
    ordinal_position: 3,
    business_name: "Creative ID",
    business_description: "Unique identifier for creative execution used in media placement. Important for CPG creative performance analysis, A/B testing, creative effectiveness measurement, and optimizing creative impact across different media channels and audiences.",
    business_synonyms: ["Creative Code", "Ad Creative ID", "Creative Reference", "Creative Unit ID", "Asset ID", "Creative Version ID"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_foreign_key: true,
    business_criticality: "high"
}),

// ========================================
// MEDIA CLASSIFICATION AND TYPE
// ========================================

(media_type:Column {
    column_id: "COL_MEDIA_TYPE_804",
    column_name: "MEDIA_TYPE",
    table_id: "DIM_MEDIA",
    ordinal_position: 4,
    business_name: "Media Type",
    business_description: "Primary media classification (Television, Digital, Print, Radio, Outdoor, Social Media, etc.). Critical for CPG media mix optimization, channel performance comparison, budget allocation, and understanding media effectiveness across different touchpoints and consumer journeys.",
    business_synonyms: ["Media Channel", "Channel Type", "Media Category", "Advertising Medium", "Media Format", "Channel Classification"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "critical"
}),

(media_subtype:Column {
    column_id: "COL_MEDIA_SUBTYPE_805",
    column_name: "MEDIA_SUBTYPE",
    table_id: "DIM_MEDIA",
    ordinal_position: 5,
    business_name: "Media Subtype",
    business_description: "Detailed media classification (Broadcast TV, Connected TV, Display, Search, Social Video, etc.). Essential for CPG granular media analysis, platform-specific optimization, and understanding performance nuances within media types.",
    business_synonyms: ["Media Sub-Channel", "Platform Type", "Media Subcategory", "Channel Subtype", "Media Platform", "Detailed Media Type"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high"
}),

(platform_name:Column {
    column_id: "COL_PLATFORM_NAME_806",
    column_name: "PLATFORM_NAME",
    table_id: "DIM_MEDIA",
    ordinal_position: 6,
    business_name: "Platform Name",
    business_description: "Specific media platform or publisher (Facebook, Google, CNN, ESPN, etc.). Important for CPG platform performance analysis, publisher relationship management, and understanding platform-specific audience engagement and effectiveness.",
    business_synonyms: ["Publisher", "Media Publisher", "Platform", "Media Property", "Channel Name", "Network Name"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "high"
}),

(ad_format:Column {
    column_id: "COL_AD_FORMAT_807",
    column_name: "AD_FORMAT",
    table_id: "DIM_MEDIA",
    ordinal_position: 7,
    business_name: "Ad Format",
    business_description: "Specific advertisement format (30-second TV spot, Banner, Video, Native, etc.). Critical for CPG format effectiveness analysis, creative constraints understanding, and optimizing format selection for different campaign objectives and audiences.",
    business_synonyms: ["Creative Format", "Ad Unit", "Advertisement Format", "Creative Type", "Ad Size", "Format Specification"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high"
}),

// ========================================
// CAMPAIGN AND TIMING INFORMATION
// ========================================

(campaign_name:Column {
    column_id: "COL_CAMPAIGN_NAME_808",
    column_name: "CAMPAIGN_NAME",
    table_id: "DIM_MEDIA",
    ordinal_position: 8,
    business_name: "Campaign Name",
    business_description: "Descriptive name for marketing campaign. Essential for CPG campaign identification, performance tracking, cross-channel analysis, and understanding campaign contribution to brand and business objectives across all product categories.",
    business_synonyms: ["Campaign Title", "Marketing Campaign", "Campaign Description", "Campaign Label", "Integrated Campaign", "Brand Campaign"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 200,
    business_criticality: "high"
}),

(campaign_objective:Column {
    column_id: "COL_CAMPAIGN_OBJECTIVE_809",
    column_name: "CAMPAIGN_OBJECTIVE",
    table_id: "DIM_MEDIA",
    ordinal_position: 9,
    business_name: "Campaign Objective",
    business_description: "Primary campaign objective (Brand Awareness, Consideration, Trial, Sales, Retention). Important for CPG performance evaluation against goals, media strategy alignment, and understanding objective-specific media effectiveness across categories.",
    business_synonyms: ["Marketing Objective", "Campaign Goal", "Media Objective", "Campaign Purpose", "Marketing Goal", "Strategic Objective"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high"
}),

(flight_start_date:Column {
    column_id: "COL_FLIGHT_START_DATE_810",
    column_name: "FLIGHT_START_DATE",
    table_id: "DIM_MEDIA",
    ordinal_position: 10,
    business_name: "Flight Start Date",
    business_description: "Campaign flight start date for media scheduling and timing analysis. Critical for CPG seasonal timing optimization, competitive timing analysis, and coordinating media flights with promotional activities and product launches.",
    business_synonyms: ["Campaign Start Date", "Media Start", "Flight Begin", "Schedule Start", "Air Date", "Launch Date"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    max_length: null,
    business_criticality: "high"
}),

(flight_end_date:Column {
    column_id: "COL_FLIGHT_END_DATE_811",
    column_name: "FLIGHT_END_DATE",
    table_id: "DIM_MEDIA",
    ordinal_position: 11,
    business_name: "Flight End Date",
    business_description: "Campaign flight end date for duration analysis and performance evaluation. Essential for CPG campaign duration optimization, budget pacing, and understanding optimal campaign lengths for different objectives and categories.",
    business_synonyms: ["Campaign End Date", "Media End", "Flight Close", "Schedule End", "Campaign Close", "End Date"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    max_length: null,
    business_criticality: "high"
}),

(flight_duration_days:Column {
    column_id: "COL_FLIGHT_DURATION_DAYS_812",
    column_name: "FLIGHT_DURATION_DAYS",
    table_id: "DIM_MEDIA",
    ordinal_position: 12,
    business_name: "Flight Duration in Days",
    business_description: "Total campaign flight duration for duration effectiveness analysis. Important for CPG optimal flight length identification, budget efficiency analysis, and understanding duration impact on campaign performance across different media types.",
    business_synonyms: ["Campaign Length", "Flight Length", "Duration Days", "Campaign Period", "Media Duration", "Flight Period"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "medium"
}),

// ========================================
// FINANCIAL AND INVESTMENT DETAILS
// ========================================

(media_spend:Column {
    column_id: "COL_MEDIA_SPEND_813",
    column_name: "MEDIA_SPEND",
    table_id: "DIM_MEDIA",
    ordinal_position: 13,
    business_name: "Media Spend",
    business_description: "Total media investment for placement or campaign. Critical for CPG media ROI analysis, budget tracking, cost efficiency measurement, and optimizing media investment allocation across channels and campaigns.",
    business_synonyms: ["Media Investment", "Media Cost", "Ad Spend", "Media Budget", "Advertising Cost", "Media Expense"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 2,
    business_criticality: "critical"
}),

(cpm_rate:Column {
    column_id: "COL_CPM_RATE_814",
    column_name: "CPM_RATE",
    table_id: "DIM_MEDIA",
    ordinal_position: 14,
    business_name: "CPM Rate",
    business_description: "Cost per thousand impressions for media efficiency analysis. Essential for CPG media cost benchmarking, efficiency comparison across platforms, and optimizing cost per reach across different media types and audiences.",
    business_synonyms: ["Cost Per Mille", "CPM", "Cost Per Thousand", "Impression Cost", "Reach Cost", "Cost Efficiency"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 2,
    business_criticality: "high"
}),

(unit_rate:Column {
    column_id: "COL_UNIT_RATE_815",
    column_name: "UNIT_RATE",
    table_id: "DIM_MEDIA",
    ordinal_position: 15,
    business_name: "Unit Rate",
    business_description: "Cost per media unit (per spot, per click, per view, etc.). Important for CPG unit cost analysis, rate card optimization, negotiation benchmarking, and understanding unit economics across different media placements.",
    business_synonyms: ["Unit Cost", "Spot Rate", "Placement Rate", "Per Unit Cost", "Individual Rate", "Single Unit Price"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 2,
    business_criticality: "medium"
}),

(media_allocation_percent:Column {
    column_id: "COL_MEDIA_ALLOCATION_PERCENT_816",
    column_name: "MEDIA_ALLOCATION_PERCENT",
    table_id: "DIM_MEDIA",
    ordinal_position: 16,
    business_name: "Media Allocation Percentage",
    business_description: "Percentage of total media budget allocated to this placement. Critical for CPG budget mix optimization, channel allocation strategy, and understanding investment distribution across media types and campaigns.",
    business_synonyms: ["Budget Allocation", "Spend Share", "Investment Share", "Budget Mix", "Allocation Share", "Budget Percentage"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "medium"
}),

// ========================================
// AUDIENCE AND TARGETING DETAILS
// ========================================

(target_audience:Column {
    column_id: "COL_TARGET_AUDIENCE_817",
    column_name: "TARGET_AUDIENCE",
    table_id: "DIM_MEDIA",
    ordinal_position: 17,
    business_name: "Target Audience",
    business_description: "Primary audience demographic target (Adults 25-54, Moms, Gen Z, etc.). Essential for CPG audience strategy evaluation, targeting effectiveness analysis, and ensuring media reaches intended consumer segments for each product category.",
    business_synonyms: ["Target Demo", "Audience Target", "Demographic Target", "Consumer Target", "Audience Segment", "Primary Audience"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "high"
}),

(age_range:Column {
    column_id: "COL_AGE_RANGE_818",
    column_name: "AGE_RANGE",
    table_id: "DIM_MEDIA",
    ordinal_position: 18,
    business_name: "Target Age Range",
    business_description: "Specific age range targeted by media placement. Important for CPG age-appropriate targeting, especially critical for alcoholic beverages (21+), toys (age-specific), and understanding age-based media consumption patterns.",
    business_synonyms: ["Age Target", "Age Demo", "Age Group", "Age Segment", "Target Age", "Demographic Age"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "medium"
}),

(gender_target:Column {
    column_id: "COL_GENDER_TARGET_819",
    column_name: "GENDER_TARGET",
    table_id: "DIM_MEDIA",
    ordinal_position: 19,
    business_name: "Gender Target",
    business_description: "Gender targeting specification (Male, Female, All). Important for CPG gender-specific products like cosmetics, personal care, and understanding gender-based media preferences and consumption patterns.",
    business_synonyms: ["Gender Focus", "Gender Demo", "Gender Segment", "Target Gender", "Gender Audience", "Gender Skew"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "medium"
}),

(geographic_target:Column {
    column_id: "COL_GEOGRAPHIC_TARGET_820",
    column_name: "GEOGRAPHIC_TARGET",
    table_id: "DIM_MEDIA",
    ordinal_position: 20,
    business_name: "Geographic Target",
    business_description: "Geographic targeting specification (National, Regional, Local, DMA-specific). Critical for CPG local marketing, regional brand launches, and understanding geographic media effectiveness and market penetration.",
    business_synonyms: ["Geo Target", "Market Target", "Geographic Focus", "Regional Target", "Location Target", "Market Coverage"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "medium"
}),

(behavioral_targeting:Column {
    column_id: "COL_BEHAVIORAL_TARGETING_821",
    column_name: "BEHAVIORAL_TARGETING",
    table_id: "DIM_MEDIA",
    ordinal_position: 21,
    business_name: "Behavioral Targeting",
    business_description: "Behavioral targeting criteria (Purchase Intent, Brand Affinity, Lifestyle, etc.). Essential for CPG precision targeting, personalized messaging, and leveraging consumer behavior data for improved media effectiveness.",
    business_synonyms: ["Behavioral Criteria", "Interest Targeting", "Lifestyle Targeting", "Behavior Segment", "Intent Targeting", "Psychographic Target"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 200,
    business_criticality: "medium"
}),

// ========================================
// PERFORMANCE AND MEASUREMENT METRICS
// ========================================

(impressions_target:Column {
    column_id: "COL_IMPRESSIONS_TARGET_822",
    column_name: "IMPRESSIONS_TARGET",
    table_id: "DIM_MEDIA",
    ordinal_position: 22,
    business_name: "Target Impressions",
    business_description: "Planned number of impressions for media placement. Important for CPG reach planning, media delivery tracking, and ensuring adequate exposure levels for brand awareness and consideration objectives.",
    business_synonyms: ["Target Reach", "Planned Impressions", "Impression Goal", "Reach Target", "Exposure Target", "Delivery Target"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "BIGINT",
    max_length: null,
    business_criticality: "high"
}),

(frequency_target:Column {
    column_id: "COL_FREQUENCY_TARGET_823",
    column_name: "FREQUENCY_TARGET",
    table_id: "DIM_MEDIA",
    ordinal_position: 23,
    business_name: "Target Frequency",
    business_description: "Planned average frequency of exposure per person reached. Critical for CPG effective frequency optimization, avoiding over-exposure, and achieving optimal message reinforcement for brand recall and purchase intent.",
    business_synonyms: ["Target Freq", "Planned Frequency", "Frequency Goal", "Exposure Frequency", "Message Frequency", "Repetition Target"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "medium"
}),

(grp_target:Column {
    column_id: "COL_GRP_TARGET_824",
    column_name: "GRP_TARGET",
    table_id: "DIM_MEDIA",
    ordinal_position: 24,
    business_name: "Target GRPs",
    business_description: "Target Gross Rating Points for media weight measurement. Essential for CPG traditional media planning, competitive media weight comparison, and ensuring adequate media pressure for campaign objectives.",
    business_synonyms: ["Target Rating Points", "GRP Goal", "Rating Points Target", "Media Weight Target", "TRP Target", "Planned GRPs"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 8,
    scale: 2,
    business_criticality: "medium"
}),

// ========================================
// TRADITIONAL MEDIA SPECIFIC ATTRIBUTES
// ========================================

(daypart:Column {
    column_id: "COL_DAYPART_825",
    column_name: "DAYPART",
    table_id: "DIM_MEDIA",
    ordinal_position: 25,
    business_name: "TV Daypart",
    business_description: "Television daypart classification (Prime Time, Daytime, Late Night, etc.). Important for CPG TV media planning, audience targeting by viewing patterns, and optimizing reach among specific demographics during different dayparts.",
    business_synonyms: ["Time Period", "TV Time", "Broadcast Time", "Program Time", "Time Slot", "Viewing Period"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "medium"
}),

(program_genre:Column {
    column_id: "COL_PROGRAM_GENRE_826",
    column_name: "PROGRAM_GENRE",
    table_id: "DIM_MEDIA",
    ordinal_position: 26,
    business_name: "Program Genre",
    business_description: "TV program genre or content category (News, Sports, Drama, Reality, etc.). Essential for CPG content alignment, brand safety, and reaching audiences based on content preferences and viewing behaviors.",
    business_synonyms: ["Content Type", "Program Type", "Show Category", "Content Genre", "Programming Type", "Content Classification"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "medium"
}),

(network_tier:Column {
    column_id: "COL_NETWORK_TIER_827",
    column_name: "NETWORK_TIER",
    table_id: "DIM_MEDIA",
    ordinal_position: 27,
    business_name: "Network Tier",
    business_description: "TV network classification (Broadcast, Cable, Premium, Streaming). Important for CPG media strategy, audience quality assessment, and understanding different network audience characteristics and reach patterns.",
    business_synonyms: ["Channel Tier", "Network Type", "Channel Classification", "Network Category", "Media Tier", "Channel Type"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "medium"
}),

// ========================================
// DIGITAL MEDIA SPECIFIC ATTRIBUTES
// ========================================

(digital_placement_type:Column {
    column_id: "COL_DIGITAL_PLACEMENT_TYPE_828",
    column_name: "DIGITAL_PLACEMENT_TYPE",
    table_id: "DIM_MEDIA",
    ordinal_position: 28,
    business_name: "Digital Placement Type",
    business_description: "Type of digital media placement (Programmatic, Direct Buy, Social Native, Search, etc.). Critical for CPG digital strategy optimization, programmatic vs direct performance comparison, and digital media effectiveness analysis.",
    business_synonyms: ["Digital Type", "Programmatic Type", "Digital Channel", "Online Placement", "Digital Format", "Digital Category"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high"
}),

(device_targeting:Column {
    column_id: "COL_DEVICE_TARGETING_829",
    column_name: "DEVICE_TARGETING",
    table_id: "DIM_MEDIA",
    ordinal_position: 29,
    business_name: "Device Targeting",
    business_description: "Target device type for digital placements (Desktop, Mobile, Tablet, Connected TV). Essential for CPG mobile-first strategy, cross-device campaigns, and understanding device-specific consumer behaviors and preferences.",
    business_synonyms: ["Device Type", "Platform Device", "Target Device", "Device Focus", "Screen Type", "Device Category"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "medium"
}),

(social_platform_type:Column {
    column_id: "COL_SOCIAL_PLATFORM_TYPE_830",
    column_name: "SOCIAL_PLATFORM_TYPE",
    table_id: "DIM_MEDIA",
    ordinal_position: 30,
    business_name: "Social Platform Type",
    business_description: "Social media platform classification (Video-First, Image-First, Professional, Messaging). Important for CPG social strategy, platform-specific content optimization, and understanding social platform audience dynamics.",
    business_synonyms: ["Social Type", "Social Category", "Platform Category", "Social Channel Type", "Social Format", "Social Classification"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "medium"
}),

(programmatic_flag:Column {
    column_id: "COL_PROGRAMMATIC_FLAG_831",
    column_name: "PROGRAMMATIC_FLAG",
    table_id: "DIM_MEDIA",
    ordinal_position: 31,
    business_name: "Programmatic Flag",
    business_description: "Indicates placement was bought programmatically. Critical for CPG programmatic strategy analysis, automated vs manual buying performance comparison, and understanding programmatic effectiveness and efficiency.",
    business_synonyms: ["Programmatic Buy", "Automated Buy", "RTB Flag", "Programmatic Purchase", "Automated Media", "Algorithmic Buy"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium"
}),

// ========================================
// CREATIVE AND CONTENT ATTRIBUTES
// ========================================

(creative_concept:Column {
    column_id: "COL_CREATIVE_CONCEPT_832",
    column_name: "CREATIVE_CONCEPT",
    table_id: "DIM_MEDIA",
    ordinal_position: 32,
    business_name: "Creative Concept",
    business_description: "Overarching creative concept or theme. Important for CPG creative effectiveness measurement, concept testing analysis, and understanding which creative approaches drive best performance across different media channels.",
    business_synonyms: ["Creative Theme", "Creative Idea", "Campaign Concept", "Creative Strategy", "Creative Approach", "Message Concept"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "medium"
}),

(message_strategy:Column {
    column_id: "COL_MESSAGE_STRATEGY_833",
    column_name: "MESSAGE_STRATEGY",
    table_id: "DIM_MEDIA",
    ordinal_position: 33,
    business_name: "Message Strategy",
    business_description: "Primary message strategy focus (Product Benefits, Emotional Appeal, Social Proof, etc.). Essential for CPG message effectiveness analysis and understanding which message strategies resonate best with target audiences.",
    business_synonyms: ["Messaging Strategy", "Communication Strategy", "Message Approach", "Creative Strategy", "Message Focus", "Communication Approach"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "medium"
}),

(creative_length:Column {
    column_id: "COL_CREATIVE_LENGTH_834",
    column_name: "CREATIVE_LENGTH",
    table_id: "DIM_MEDIA",
    ordinal_position: 34,
    business_name: "Creative Length",
    business_description: "Duration of creative execution in seconds for video/audio content. Important for CPG creative format optimization, cost efficiency analysis, and understanding optimal creative length for different objectives and platforms.",
    business_synonyms: ["Ad Length", "Duration", "Creative Duration", "Spot Length", "Content Length", "Format Duration"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "medium"
}),

(language:Column {
    column_id: "COL_LANGUAGE_835",
    column_name: "LANGUAGE",
    table_id: "DIM_MEDIA",
    ordinal_position: 35,
    business_name: "Creative Language",
    business_description: "Primary language of creative content. Critical for CPG multicultural marketing, Hispanic market targeting, and ensuring language-appropriate media placement for diverse consumer segments.",
    business_synonyms: ["Content Language", "Ad Language", "Message Language", "Communication Language", "Creative Language", "Audience Language"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "medium"
}),

// ========================================
// BRAND AND PRODUCT ASSOCIATION
// ========================================

(brand_name:Column {
    column_id: "COL_BRAND_NAME_836",
    column_name: "BRAND_NAME",
    table_id: "DIM_MEDIA",
    ordinal_position: 36,
    business_name: "Advertised Brand",
    business_description: "Primary brand featured in media placement. Essential for CPG brand-level media analysis, brand investment tracking, portfolio media optimization, and understanding brand-specific media effectiveness across categories.",
    business_synonyms: ["Featured Brand", "Advertised Brand", "Primary Brand", "Brand Focus", "Campaign Brand", "Product Brand"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "high"
}),

(product_category:Column {
    column_id: "COL_PRODUCT_CATEGORY_837",
    column_name: "PRODUCT_CATEGORY",
    table_id: "DIM_MEDIA",
    ordinal_position: 37,
    business_name: "Product Category",
    business_description: "Product category featured in advertising. Important for CPG category-level media analysis, competitive media monitoring, and understanding category-specific media strategies and effectiveness patterns.",
    business_synonyms: ["Category", "Product Class", "Advertised Category", "Product Type", "Category Focus", "Business Category"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "high"
}),

(business_unit:Column {
    column_id: "COL_BUSINESS_UNIT_838",
    column_name: "BUSINESS_UNIT",
    table_id: "DIM_MEDIA",
    ordinal_position: 38,
    business_name: "Business Unit",
    business_description: "Business unit responsible for media investment. Critical for CPG organizational media analysis, P&L attribution, budget accountability, and understanding business unit media strategies and performance.",
    business_synonyms: ["Division", "Business Division", "Operating Unit", "BU", "Organizational Unit", "Media Owner"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "medium"
}),

// ========================================
// AGENCY AND VENDOR MANAGEMENT
// ========================================

(media_agency:Column {
    column_id: "COL_MEDIA_AGENCY_839",
    column_name: "MEDIA_AGENCY",
    table_id: "DIM_MEDIA",
    ordinal_position: 39,
    business_name: "Media Agency",
    business_description: "Media agency responsible for planning and buying. Important for CPG agency performance evaluation, relationship management, and understanding agency-specific capabilities and media strategies.",
    business_synonyms: ["Agency", "Media Partner", "Buying Agency", "Media Company", "Agency Partner", "Media Vendor"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "medium"
}),

(creative_agency:Column {
    column_id: "COL_CREATIVE_AGENCY_840",
    column_name: "CREATIVE_AGENCY",
    table_id: "DIM_MEDIA",
    ordinal_position: 40,
    business_name: "Creative Agency",
    business_description: "Creative agency responsible for ad development. Essential for CPG creative performance analysis, agency accountability, and understanding creative agency impact on media effectiveness.",
    business_synonyms: ["Creative Partner", "Ad Agency", "Creative Company", "Design Agency", "Creative Vendor", "Agency Creative"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "low"
}),

(vendor_type:Column {
    column_id: "COL_VENDOR_TYPE_841",
    column_name: "VENDOR_TYPE",
    table_id: "DIM_MEDIA",
    ordinal_position: 41,
    business_name: "Media Vendor Type",
    business_description: "Type of media vendor relationship (Direct Publisher, Ad Network, DSP, Traditional Rep, etc.). Important for CPG vendor strategy, cost structure analysis, and understanding different vendor model effectiveness.",
    business_synonyms: ["Vendor Category", "Partner Type", "Supplier Type", "Media Partner Type", "Vendor Classification", "Provider Type"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "medium"
}),

// ========================================
// COMPLIANCE AND BRAND SAFETY
// ========================================

(brand_safety_category:Column {
    column_id: "COL_BRAND_SAFETY_CATEGORY_842",
    column_name: "BRAND_SAFETY_CATEGORY",
    table_id: "DIM_MEDIA",
    ordinal_position: 42,
    business_name: "Brand Safety Category",
    business_description: "Brand safety classification for content adjacency (Safe, Moderate Risk, High Risk). Critical for CPG brand protection, especially important for family brands, children's products, and maintaining brand reputation across all placements.",
    business_synonyms: ["Safety Category", "Content Safety", "Brand Protection", "Safety Classification", "Risk Category", "Content Risk"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "high"
}),

(content_restriction_flag:Column {
    column_id: "COL_CONTENT_RESTRICTION_FLAG_843",
    column_name: "CONTENT_RESTRICTION_FLAG",
    table_id: "DIM_MEDIA",
    ordinal_position: 43,
    business_name: "Content Restriction Flag",
    business_description: "Indicates content restrictions apply to placement. Essential for CPG regulatory compliance, age-appropriate advertising, and ensuring media placements meet content guidelines and legal requirements.",
    business_synonyms: ["Restriction Flag", "Content Flag", "Compliance Flag", "Regulatory Flag", "Safety Flag", "Content Control"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "high"
}),

(regulatory_approval_flag:Column {
    column_id: "COL_REGULATORY_APPROVAL_FLAG_844",
    column_name: "REGULATORY_APPROVAL_FLAG",
    table_id: "DIM_MEDIA",
    ordinal_position: 44,
    business_name: "Regulatory Approval Flag",
    business_description: "Indicates creative has received required regulatory approval. Critical for pharmaceutical and alcoholic beverage advertising compliance, ensuring all required approvals are obtained before media placement.",
    business_synonyms: ["Approval Flag", "Compliance Approval", "Legal Approval", "Regulatory Flag", "Cleared Flag", "Compliance Status"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "high"
}),

// ========================================
// DOMAIN-SPECIFIC REGULATORY ATTRIBUTES
// ========================================

(alcohol_advertising_restriction:Column {
    column_id: "COL_ALCOHOL_AD_RESTRICTION_845",
    column_name: "ALCOHOL_ADVERTISING_RESTRICTION",
    table_id: "DIM_MEDIA",
    ordinal_position: 45,
    business_name: "Alcohol Advertising Restriction",
    business_description: "Alcohol advertising restrictions for placement (Time Restrictions, Content Guidelines, Audience Limits). Critical for alcoholic beverage compliance with federal and state advertising regulations and industry self-regulation.",
    business_synonyms: ["Alcohol Restriction", "Beverage Restriction", "Alcohol Compliance", "Liquor Restriction", "Alcohol Guidelines", "TTB Compliance"],
    applicable_domains: ["alcoholic_beverages"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "critical",
    regulatory_relevance: ["TTB", "STATE_LIQUOR_BOARDS", "SELF_REGULATION"]
}),

(pharma_indication_claim:Column {
    column_id: "COL_PHARMA_INDICATION_CLAIM_846",
    column_name: "PHARMA_INDICATION_CLAIM",
    table_id: "DIM_MEDIA",
    ordinal_position: 46,
    business_name: "Pharmaceutical Indication Claim",
    business_description: "Type of health claim made in pharmaceutical advertising (FDA-approved indication, general wellness, etc.). Essential for pharmaceutical advertising compliance and FDA regulation adherence.",
    business_synonyms: ["Health Claim", "Medical Claim", "Therapeutic Claim", "Drug Claim", "FDA Claim", "Indication Type"],
    applicable_domains: ["pharmaceuticals", "health_supplements"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "critical",
    regulatory_relevance: ["FDA", "FTC"]
}),

(child_directed_flag:Column {
    column_id: "COL_CHILD_DIRECTED_FLAG_847",
    column_name: "CHILD_DIRECTED_FLAG",
    table_id: "DIM_MEDIA",
    ordinal_position: 47,
    business_name: "Child-Directed Advertising Flag",
    business_description: "Indicates advertising is directed at children under 13. Critical for children's product compliance with COPPA regulations, appropriate content standards, and child-safe advertising practices.",
    business_synonyms: ["Child Directed", "Kids Advertising", "COPPA Flag", "Child Target", "Minor Target", "Child Compliance"],
    applicable_domains: ["toys", "baby_products", "food_beverage", "snacks"],
    domain_specific: true,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "critical",
    regulatory_relevance: ["FTC", "COPPA", "CARU"]
}),

// ========================================
// PERFORMANCE TRACKING AND ATTRIBUTION
// ========================================

(attribution_model:Column {
    column_id: "COL_ATTRIBUTION_MODEL_848",
    column_name: "ATTRIBUTION_MODEL",
    table_id: "DIM_MEDIA",
    ordinal_position: 48,
    business_name: "Attribution Model",
    business_description: "Attribution methodology used for performance measurement (First Touch, Last Touch, Multi-Touch, etc.). Important for CPG media effectiveness analysis and understanding media contribution to conversion and sales.",
    business_synonyms: ["Attribution Method", "Credit Model", "Performance Model", "Measurement Model", "Contribution Model", "Tracking Model"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "medium"
}),

(measurement_partner:Column {
    column_id: "COL_MEASUREMENT_PARTNER_849",
    column_name: "MEASUREMENT_PARTNER",
    table_id: "DIM_MEDIA",
    ordinal_position: 49,
    business_name: "Measurement Partner",
    business_description: "Third-party measurement vendor for performance tracking (Nielsen, ComScore, IRI, etc.). Essential for CPG standardized measurement, cross-media comparison, and ensuring reliable performance metrics.",
    business_synonyms: ["Measurement Vendor", "Tracking Partner", "Analytics Partner", "Measurement Provider", "Research Partner", "Metrics Vendor"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "medium"
}),

(tracking_url:Column {
    column_id: "COL_TRACKING_URL_850",
    column_name: "TRACKING_URL",
    table_id: "DIM_MEDIA",
    ordinal_position: 50,
    business_name: "Tracking URL",
    business_description: "URL for digital media tracking and attribution. Important for CPG digital performance measurement, click-through analysis, and connecting media exposure to downstream consumer actions.",
    business_synonyms: ["Click URL", "Tracking Link", "Attribution URL", "Measurement URL", "Performance URL", "Campaign URL"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 500,
    business_criticality: "low"
}),

// ========================================
// MARKET AND COMPETITIVE CONTEXT
// ========================================

(competitive_response_flag:Column {
    column_id: "COL_COMPETITIVE_RESPONSE_FLAG_851",
    column_name: "COMPETITIVE_RESPONSE_FLAG",
    table_id: "DIM_MEDIA",
    ordinal_position: 51,
    business_name: "Competitive Response Flag",
    business_description: "Indicates media is in response to competitive activity. Important for CPG competitive strategy, defensive media planning, and understanding reactive vs proactive media investments.",
    business_synonyms: ["Competitive Flag", "Response Media", "Defensive Media", "Reactive Media", "Competitor Response", "Market Defense"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium"
}),

(share_of_voice_target:Column {
    column_id: "COL_SHARE_OF_VOICE_TARGET_852",
    column_name: "SHARE_OF_VOICE_TARGET",
    table_id: "DIM_MEDIA",
    ordinal_position: 52,
    business_name: "Share of Voice Target",
    business_description: "Target share of voice percentage in category media spending. Critical for CPG competitive media strategy, market presence maintenance, and ensuring adequate voice in competitive categories.",
    business_synonyms: ["SOV Target", "Voice Share Target", "Media Share Target", "Competitive Share", "Market Voice", "Category Share"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "medium"
}),

// ========================================
// SEASONALITY AND TIMING STRATEGY
// ========================================

(seasonal_strategy:Column {
    column_id: "COL_SEASONAL_STRATEGY_853",
    column_name: "SEASONAL_STRATEGY",
    table_id: "DIM_MEDIA",
    ordinal_position: 53,
    business_name: "Seasonal Strategy",
    business_description: "Seasonal media strategy classification (Holiday Push, Summer Campaign, Back-to-School, etc.). Essential for CPG seasonal planning, especially important for toys (holiday focus), beverages (summer), and seasonal product categories.",
    business_synonyms: ["Seasonal Focus", "Timing Strategy", "Seasonal Campaign", "Seasonal Planning", "Seasonal Approach", "Calendar Strategy"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "medium"
}),

(media_flighting_strategy:Column {
    column_id: "COL_MEDIA_FLIGHTING_STRATEGY_854",
    column_name: "MEDIA_FLIGHTING_STRATEGY",
    table_id: "DIM_MEDIA",
    ordinal_position: 54,
    business_name: "Media Flighting Strategy",
    business_description: "Media scheduling pattern (Continuous, Pulsing, Flighting). Important for CPG media scheduling optimization, budget efficiency, and understanding optimal media timing patterns for different objectives.",
    business_synonyms: ["Scheduling Strategy", "Flight Pattern", "Media Schedule", "Timing Pattern", "Media Pacing", "Flight Strategy"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "medium"
}),

// ========================================
// INTEGRATION AND OMNICHANNEL
// ========================================

(omnichannel_integration:Column {
    column_id: "COL_OMNICHANNEL_INTEGRATION_855",
    column_name: "OMNICHANNEL_INTEGRATION",
    table_id: "DIM_MEDIA",
    ordinal_position: 55,
    business_name: "Omnichannel Integration Level",
    business_description: "Level of integration with other marketing channels (High, Medium, Low, None). Essential for CPG integrated marketing effectiveness, cross-channel synergy measurement, and omnichannel strategy optimization.",
    business_synonyms: ["Integration Level", "Cross-Channel", "Multi-Channel", "Channel Integration", "Synergy Level", "Channel Coordination"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "medium"
}),

(cross_media_synergy_flag:Column {
    column_id: "COL_CROSS_MEDIA_SYNERGY_FLAG_856",
    column_name: "CROSS_MEDIA_SYNERGY_FLAG",
    table_id: "DIM_MEDIA",
    ordinal_position: 56,
    business_name: "Cross-Media Synergy Flag",
    business_description: "Indicates media placement is part of coordinated cross-media campaign. Important for CPG media synergy analysis, integrated campaign effectiveness, and understanding cross-channel amplification effects.",
    business_synonyms: ["Synergy Flag", "Integrated Flag", "Multi-Media Flag", "Cross-Channel Flag", "Coordinated Flag", "Unified Campaign"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium"
}),

// ========================================
// TEST AND LEARN FRAMEWORK
// ========================================

(test_cell_identifier:Column {
    column_id: "COL_TEST_CELL_IDENTIFIER_857",
    column_name: "TEST_CELL_IDENTIFIER",
    table_id: "DIM_MEDIA",
    ordinal_position: 57,
    business_name: "Test Cell Identifier",
    business_description: "Identifier for media testing and experimentation cells. Critical for CPG media testing, A/B testing analysis, and understanding experimental vs control media performance for optimization.",
    business_synonyms: ["Test Cell", "Experiment ID", "Test Group", "Test Identifier", "A/B Test ID", "Control Group"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "medium"
}),

(optimization_status:Column {
    column_id: "COL_OPTIMIZATION_STATUS_858",
    column_name: "OPTIMIZATION_STATUS",
    table_id: "DIM_MEDIA",
    ordinal_position: 58,
    business_name: "Optimization Status",
    business_description: "Current optimization status of media placement (Learning, Optimizing, Optimized, Paused). Important for CPG campaign management, performance optimization tracking, and understanding optimization lifecycle.",
    business_synonyms: ["Optimization Phase", "Campaign Status", "Learning Status", "Performance Status", "Campaign Phase", "Media Status"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "medium"
}),

// ========================================
// METADATA AND AUDIT INFORMATION
// ========================================

(media_planner:Column {
    column_id: "COL_MEDIA_PLANNER_859",
    column_name: "MEDIA_PLANNER",
    table_id: "DIM_MEDIA",
    ordinal_position: 59,
    business_name: "Media Planner",
    business_description: "Person responsible for media planning and strategy. Important for CPG accountability tracking, planner performance analysis, and understanding individual planner impact on media effectiveness.",
    business_synonyms: ["Planner", "Media Strategist", "Campaign Planner", "Media Lead", "Planning Lead", "Media Manager"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "low"
}),

(created_by:Column {
    column_id: "COL_CREATED_BY_860",
    column_name: "CREATED_BY",
    table_id: "DIM_MEDIA",
    ordinal_position: 60,
    business_name: "Created By",
    business_description: "User who created the media record. Important for CPG audit trails, accountability tracking, user activity monitoring, and understanding media record creation patterns by individual or team.",
    business_synonyms: ["Creator", "Author", "Created User", "Originated By", "Media Creator", "Record Creator"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "low"
}),

(created_timestamp:Column {
    column_id: "COL_CREATED_TIMESTAMP_861",
    column_name: "CREATED_TIMESTAMP",
    table_id: "DIM_MEDIA",
    ordinal_position: 61,
    business_name: "Record Created Timestamp",
    business_description: "System timestamp when media record was created. Used for audit trails, data lineage tracking, and data governance. Immutable after creation. Part of comprehensive data governance framework.",
    business_synonyms: ["Creation Time", "Insert Timestamp", "Created Date", "Record Creation", "Entry Timestamp", "Initial Load Time"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    max_length: null,
    business_criticality: "low"
}),

(modified_timestamp:Column {
    column_id: "COL_MODIFIED_TIMESTAMP_862",
    column_name: "MODIFIED_TIMESTAMP",
    table_id: "DIM_MEDIA",
    ordinal_position: 62,
    business_name: "Last Modified Timestamp",
    business_description: "Most recent update timestamp for media record. Tracks data freshness, change frequency, and maintenance activities. Updated automatically with any field modification for data governance compliance.",
    business_synonyms: ["Update Time", "Last Modified", "Change Timestamp", "Modified Date", "Last Updated", "Revision Timestamp"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    max_length: null,
    business_criticality: "low"
});

// ========================================
// CREATE TABLE-COLUMN RELATIONSHIPS
// ========================================

MATCH (t:Table {table_id: "DIM_MEDIA"})
MATCH (c:Column {table_id: "DIM_MEDIA"})
CREATE (t)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"]
}]->(c);

// ========================================
// VERIFICATION QUERIES
// ========================================

// Count total columns created
MATCH (c:Column {table_id: "DIM_MEDIA"})
RETURN count(c) AS total_columns;

// Verify columns by business criticality
MATCH (c:Column {table_id: "DIM_MEDIA"})
RETURN c.business_criticality AS criticality_level, 
       count(c) AS column_count
ORDER BY criticality_level;

// List critical columns
MATCH (c:Column {table_id: "DIM_MEDIA"})
WHERE c.business_criticality = "critical"
RETURN c.business_name AS critical_column, 
       c.business_description
ORDER BY c.ordinal_position;

// Check domain-specific columns
MATCH (c:Column {table_id: "DIM_MEDIA"})
WHERE c.domain_specific = true
RETURN c.business_name AS domain_specific_column, 
       c.applicable_domains AS specific_domains,
       c.regulatory_relevance AS regulations
ORDER BY c.ordinal_position;

// Check columns by category
MATCH (c:Column {table_id: "DIM_MEDIA"})
RETURN 
    CASE 
        WHEN c.ordinal_position <= 3 THEN "Core Media Identifiers"
        WHEN c.ordinal_position <= 7 THEN "Media Classification & Type"
        WHEN c.ordinal_position <= 12 THEN "Campaign & Timing"
        WHEN c.ordinal_position <= 16 THEN "Financial & Investment"
        WHEN c.ordinal_position <= 21 THEN "Audience & Targeting"
        WHEN c.ordinal_position <= 24 THEN "Performance & Measurement"
        WHEN c.ordinal_position <= 27 THEN "Traditional Media Attributes"
        WHEN c.ordinal_position <= 31 THEN "Digital Media Attributes"
        WHEN c.ordinal_position <= 35 THEN "Creative & Content"
        WHEN c.ordinal_position <= 38 THEN "Brand & Product Association"
        WHEN c.ordinal_position <= 41 THEN "Agency & Vendor Management"
        WHEN c.ordinal_position <= 44 THEN "Compliance & Brand Safety"
        WHEN c.ordinal_position <= 47 THEN "Domain-Specific Regulatory"
        WHEN c.ordinal_position <= 50 THEN "Performance Tracking"
        WHEN c.ordinal_position <= 52 THEN "Market & Competitive Context"
        WHEN c.ordinal_position <= 54 THEN "Seasonality & Timing Strategy"
        WHEN c.ordinal_position <= 56 THEN "Integration & Omnichannel"
        WHEN c.ordinal_position <= 58 THEN "Test & Learn Framework"
        ELSE "Metadata & Audit"
    END AS category,
    count(c) AS column_count
ORDER BY category;

// Verify semantic types distribution
MATCH (c:Column {table_id: "DIM_MEDIA"})
RETURN c.semantic_type AS semantic_type, 
       count(c) AS column_count
ORDER BY column_count DESC;

// Check for primary key
MATCH (c:Column {table_id: "DIM_MEDIA"})
WHERE c.is_primary_key = true
RETURN c.business_name AS primary_key_column, 
       c.column_name AS column_name;

// Verify regulatory relevance columns
MATCH (c:Column {table_id: "DIM_MEDIA"})
WHERE c.regulatory_relevance IS NOT NULL
RETURN c.business_name AS regulatory_column, 
       c.regulatory_relevance AS regulations,
       c.applicable_domains AS domains
ORDER BY c.ordinal_position;

// Check flag/boolean columns
MATCH (c:Column {table_id: "DIM_MEDIA"})
WHERE c.data_type = "BOOLEAN"
RETURN c.business_name AS boolean_column,
       c.business_description
ORDER BY c.ordinal_position;

// Verify financial and measurement columns
MATCH (c:Column {table_id: "DIM_MEDIA"})
WHERE c.business_name CONTAINS "Spend" OR c.business_name CONTAINS "CPM" OR c.business_name CONTAINS "Target" OR c.business_name CONTAINS "Rate"
RETURN c.business_name AS financial_column,
       c.data_type AS data_type,
       c.precision AS precision,
       c.scale AS scale
ORDER BY c.ordinal_position;

// Check media type and platform columns
MATCH (c:Column {table_id: "DIM_MEDIA"})
WHERE c.business_name CONTAINS "Media" OR c.business_name CONTAINS "Platform" OR c.business_name CONTAINS "Channel"
RETURN c.business_name AS media_column,
       c.semantic_type AS type
ORDER BY c.ordinal_position;

// Verify targeting and audience columns
MATCH (c:Column {table_id: "DIM_MEDIA"})
WHERE c.business_name CONTAINS "Target" OR c.business_name CONTAINS "Audience" OR c.business_name CONTAINS "Demo"
RETURN c.business_name AS targeting_column,
       c.business_criticality AS criticality
ORDER BY c.ordinal_position;

// ========================================
// END OF DIM_MEDIA COLUMN CREATION
// ========================================