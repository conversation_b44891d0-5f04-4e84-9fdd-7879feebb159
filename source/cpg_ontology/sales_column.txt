// ========================================
// FACT_SALES - COMPREHENSIVE COLUMN CREATION
// Complete sales fact table columns for all CPG domains
// ========================================

// Clear existing columns for FACT_SALES (optional)
MATCH (c:Column {table_id: "FACT_SALES"}) DETACH DELETE c;

// ========================================
// CORE TRANSACTION IDENTIFIERS AND FOREIGN KEYS
// ========================================

CREATE 
(sales_transaction_id:Column {
    column_id: "COL_SALES_TRANSACTION_ID_FACT_901",
    column_name: "SALES_TRANSACTION_ID",
    table_id: "FACT_SALES",
    ordinal_position: 1,
    business_name: "Sales Transaction ID",
    business_description: "Unique identifier for individual sales transactions across all CPG categories and channels. Critical for transaction-level analysis, customer journey tracking, return processing, and detailed sales analytics across alcoholic beverages, pharmaceuticals, toys, cosmetics, and all consumer product categories.",
    business_synonyms: ["Transaction Key", "Sales ID", "Transaction Number", "Sale ID", "Receipt ID", "Transaction Reference", "Sale Reference"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_primary_key: true,
    business_criticality: "critical"
}),

(date_key:Column {
    column_id: "COL_DATE_KEY_FACT_902",
    column_name: "DATE_KEY",
    table_id: "FACT_SALES",
    ordinal_position: 2,
    business_name: "Date Key",
    business_description: "Foreign key to date dimension for temporal analysis and time-based aggregations. Essential for CPG trend analysis, seasonal patterns, promotional timing effectiveness, and year-over-year comparisons across all product categories and sales channels.",
    business_synonyms: ["Sale Date Key", "Transaction Date Key", "Date FK", "Date Reference", "Temporal Key", "Time Key"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "INTEGER",
    max_length: null,
    is_foreign_key: true,
    business_criticality: "critical"
}),

(store_key:Column {
    column_id: "COL_STORE_KEY_FACT_903",
    column_name: "STORE_KEY",
    table_id: "FACT_SALES",
    ordinal_position: 3,
    business_name: "Store Key",
    business_description: "Foreign key to store dimension for location-based analysis and retailer performance tracking. Critical for CPG channel analysis, geographic performance, retailer-specific strategies, and understanding location impact on sales across all categories.",
    business_synonyms: ["Location Key", "Store FK", "Retailer Key", "Channel Key", "Store Reference", "Location Reference"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_foreign_key: true,
    business_criticality: "critical"
}),

(customer_key:Column {
    column_id: "COL_CUSTOMER_KEY_FACT_904",
    column_name: "CUSTOMER_KEY",
    table_id: "FACT_SALES",
    ordinal_position: 4,
    business_name: "Customer Key",
    business_description: "Foreign key to customer dimension for customer-level analysis and personalization. Essential for CPG customer lifetime value, segmentation analysis, personalized marketing, and understanding customer behavior patterns across product categories.",
    business_synonyms: ["Customer FK", "Shopper Key", "Consumer Key", "Customer Reference", "Buyer Key", "Customer ID FK"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_foreign_key: true,
    business_criticality: "high"
}),

(product_hierarchy_key:Column {
    column_id: "COL_PRODUCT_HIERARCHY_KEY_FACT_905",
    column_name: "PRODUCT_HIERARCHY_KEY",
    table_id: "FACT_SALES",
    ordinal_position: 5,
    business_name: "Product Hierarchy Key",
    business_description: "Foreign key to product hierarchy dimension for product performance analysis and category management. Critical for CPG SKU performance, brand analysis, category trends, and hierarchical product aggregations across all levels from SKU to company.",
    business_synonyms: ["Product Key", "SKU Key", "Product FK", "Item Key", "Product Reference", "Hierarchy FK"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_foreign_key: true,
    business_criticality: "critical"
}),

(promotion_key:Column {
    column_id: "COL_PROMOTION_KEY_FACT_906",
    column_name: "PROMOTION_KEY",
    table_id: "FACT_SALES",
    ordinal_position: 6,
    business_name: "Promotion Key",
    business_description: "Foreign key to promotion dimension for promotional effectiveness analysis. Important for CPG promotional ROI measurement, incremental sales analysis, and understanding promotional impact on sales performance across all categories.",
    business_synonyms: ["Promotion FK", "Campaign Key", "Promo Key", "Promotion Reference", "Campaign FK", "Promotional Key"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_foreign_key: true,
    business_criticality: "high"
}),

(media_key:Column {
    column_id: "COL_MEDIA_KEY_FACT_907",
    column_name: "MEDIA_KEY",
    table_id: "FACT_SALES",
    ordinal_position: 7,
    business_name: "Media Key",
    business_description: "Foreign key to media dimension for media attribution analysis. Essential for CPG media effectiveness measurement, attribution modeling, and understanding media impact on sales conversion across different channels and touchpoints.",
    business_synonyms: ["Media FK", "Attribution Key", "Media Reference", "Campaign Media Key", "Media Attribution Key", "Media Source Key"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_foreign_key: true,
    business_criticality: "medium"
}),

// ========================================
// CORE SALES QUANTITY MEASURES
// ========================================

(quantity_sold:Column {
    column_id: "COL_QUANTITY_SOLD_FACT_908",
    column_name: "QUANTITY_SOLD",
    table_id: "FACT_SALES",
    ordinal_position: 8,
    business_name: "Quantity Sold",
    business_description: "Number of individual units sold in transaction. Fundamental measure for CPG volume analysis, inventory planning, demand forecasting, and understanding unit velocity across all product categories and channels.",
    business_synonyms: ["Units Sold", "Unit Quantity", "Volume Sold", "Items Sold", "Piece Count", "Unit Sales", "Sales Volume"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 3,
    business_criticality: "critical",
    additive_type: "additive"
}),

(cases_sold:Column {
    column_id: "COL_CASES_SOLD_FACT_909",
    column_name: "CASES_SOLD",
    table_id: "FACT_SALES",
    ordinal_position: 9,
    business_name: "Cases Sold",
    business_description: "Number of cases sold for wholesale and distribution analysis. Important for CPG supply chain planning, bulk order analysis, and understanding case-level distribution patterns across different channels.",
    business_synonyms: ["Case Quantity", "Case Volume", "Case Count", "Cases", "Bulk Units", "Case Sales", "Distribution Volume"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 3,
    business_criticality: "medium",
    additive_type: "additive"
}),

(weight_sold:Column {
    column_id: "COL_WEIGHT_SOLD_FACT_910",
    column_name: "WEIGHT_SOLD",
    table_id: "FACT_SALES",
    ordinal_position: 10,
    business_name: "Weight Sold",
    business_description: "Total weight of products sold in transaction (pounds, kilograms). Essential for CPG logistics planning, shipping cost analysis, and understanding weight-based distribution economics across categories.",
    business_synonyms: ["Weight Volume", "Product Weight", "Total Weight", "Mass Sold", "Weight Quantity", "Physical Weight"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 3,
    business_criticality: "medium",
    additive_type: "additive"
}),

(volume_sold:Column {
    column_id: "COL_VOLUME_SOLD_FACT_911",
    column_name: "VOLUME_SOLD",
    table_id: "FACT_SALES",
    ordinal_position: 11,
    business_name: "Volume Sold",
    business_description: "Total liquid/fluid volume sold in transaction (liters, fluid ounces). Critical for beverage and liquid product analysis, capacity planning, and understanding volume-based consumption patterns.",
    business_synonyms: ["Fluid Volume", "Liquid Volume", "Volume Quantity", "Capacity Sold", "Fluid Ounces", "Liters Sold"],
    applicable_domains: ["alcoholic_beverages", "beverages", "food_beverage", "personal_care", "household_products", "dairy"],
    domain_specific: true,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 3,
    business_criticality: "medium",
    additive_type: "additive"
}),

// ========================================
// CORE FINANCIAL MEASURES - GROSS SALES
// ========================================

(gross_sales_amount:Column {
    column_id: "COL_GROSS_SALES_AMOUNT_FACT_912",
    column_name: "GROSS_SALES_AMOUNT",
    table_id: "FACT_SALES",
    ordinal_position: 12,
    business_name: "Gross Sales Amount",
    business_description: "Total sales amount before any discounts or deductions. Fundamental revenue measure for CPG top-line analysis, market sizing, and understanding total revenue potential before promotional impacts across all categories.",
    business_synonyms: ["Gross Revenue", "Total Sales", "Gross Sales Revenue", "List Sales", "Undiscounted Sales", "Total Revenue"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 2,
    business_criticality: "critical",
    additive_type: "additive"
}),

(net_sales_amount:Column {
    column_id: "COL_NET_SALES_AMOUNT_FACT_913",
    column_name: "NET_SALES_AMOUNT",
    table_id: "FACT_SALES",
    ordinal_position: 13,
    business_name: "Net Sales Amount",
    business_description: "Sales amount after all discounts, allowances, and deductions. Critical CPG revenue measure for P&L reporting, profitability analysis, and understanding actual realized revenue across all product categories and channels.",
    business_synonyms: ["Net Revenue", "Realized Sales", "Net Sales Revenue", "Actual Sales", "Final Sales Amount", "Post-Discount Sales"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 2,
    business_criticality: "critical",
    additive_type: "additive"
}),

(base_price_amount:Column {
    column_id: "COL_BASE_PRICE_AMOUNT_FACT_914",
    column_name: "BASE_PRICE_AMOUNT",
    table_id: "FACT_SALES",
    ordinal_position: 14,
    business_name: "Base Price Amount",
    business_description: "Regular selling price before any promotional discounts. Essential for CPG price elasticity analysis, promotional lift measurement, and understanding baseline vs promotional sales performance across categories.",
    business_synonyms: ["Regular Price", "List Price Amount", "Standard Price", "Non-Promotional Price", "Baseline Price", "Normal Price"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 2,
    business_criticality: "high",
    additive_type: "additive"
}),

(unit_price:Column {
    column_id: "COL_UNIT_PRICE_FACT_915",
    column_name: "UNIT_PRICE",
    table_id: "FACT_SALES",
    ordinal_position: 15,
    business_name: "Unit Price",
    business_description: "Price per individual unit sold. Important for CPG price point analysis, competitive pricing, value perception, and understanding unit-level pricing dynamics across different package sizes and formats.",
    business_synonyms: ["Per Unit Price", "Individual Price", "Single Unit Price", "Item Price", "Unit Cost", "Price Per Unit"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

// ========================================
// DISCOUNT AND ALLOWANCE BREAKDOWN
// ========================================

(promotional_discount_amount:Column {
    column_id: "COL_PROMOTIONAL_DISCOUNT_AMOUNT_FACT_916",
    column_name: "PROMOTIONAL_DISCOUNT_AMOUNT",
    table_id: "FACT_SALES",
    ordinal_position: 16,
    business_name: "Promotional Discount Amount",
    business_description: "Total promotional discounts applied to transaction. Critical for CPG promotional effectiveness analysis, trade spend tracking, and understanding promotional impact on margins and volume across all categories.",
    business_synonyms: ["Promo Discount", "Promotional Allowance", "Campaign Discount", "Promotion Savings", "Discount Amount", "Promo Reduction"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 2,
    business_criticality: "high",
    additive_type: "additive"
}),

(trade_allowance_amount:Column {
    column_id: "COL_TRADE_ALLOWANCE_AMOUNT_FACT_917",
    column_name: "TRADE_ALLOWANCE_AMOUNT",
    table_id: "FACT_SALES",
    ordinal_position: 17,
    business_name: "Trade Allowance Amount",
    business_description: "Trade allowances and retailer discounts applied. Essential for CPG trade spend management, channel profitability analysis, and understanding trade investment effectiveness across different retail partners.",
    business_synonyms: ["Trade Discount", "Retailer Allowance", "Channel Discount", "Trade Spend", "Retailer Rebate", "Trade Investment"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 2,
    business_criticality: "high",
    additive_type: "additive"
}),

(loyalty_discount_amount:Column {
    column_id: "COL_LOYALTY_DISCOUNT_AMOUNT_FACT_918",
    column_name: "LOYALTY_DISCOUNT_AMOUNT",
    table_id: "FACT_SALES",
    ordinal_position: 18,
    business_name: "Loyalty Discount Amount",
    business_description: "Loyalty program discounts and rewards applied. Important for CPG loyalty program ROI analysis, member value measurement, and understanding loyalty program impact on customer retention and sales.",
    business_synonyms: ["Loyalty Savings", "Member Discount", "Rewards Discount", "Loyalty Rebate", "Member Savings", "Points Discount"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 2,
    business_criticality: "medium",
    additive_type: "additive"
}),

(coupon_discount_amount:Column {
    column_id: "COL_COUPON_DISCOUNT_AMOUNT_FACT_919",
    column_name: "COUPON_DISCOUNT_AMOUNT",
    table_id: "FACT_SALES",
    ordinal_position: 19,
    business_name: "Coupon Discount Amount",
    business_description: "Manufacturer and retailer coupon discounts applied. Critical for CPG coupon program effectiveness, digital vs traditional coupon analysis, and understanding coupon impact on trial and repeat purchase.",
    business_synonyms: ["Coupon Savings", "Coupon Rebate", "Coupon Value", "Coupon Reduction", "Coupon Allowance", "Coupon Benefit"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 2,
    business_criticality: "medium",
    additive_type: "additive"
}),

(total_discount_amount:Column {
    column_id: "COL_TOTAL_DISCOUNT_AMOUNT_FACT_920",
    column_name: "TOTAL_DISCOUNT_AMOUNT",
    table_id: "FACT_SALES",
    ordinal_position: 20,
    business_name: "Total Discount Amount",
    business_description: "Sum of all discounts and allowances applied to transaction. Essential for CPG discount analysis, margin impact assessment, and understanding total promotional investment across all discount types and categories.",
    business_synonyms: ["Total Savings", "Total Allowances", "Total Rebates", "Total Reductions", "Combined Discounts", "Aggregate Discount"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 2,
    business_criticality: "high",
    additive_type: "additive"
}),

// ========================================
// COST AND PROFITABILITY MEASURES
// ========================================

(cost_of_goods_sold:Column {
    column_id: "COL_COST_OF_GOODS_SOLD_FACT_921",
    column_name: "COST_OF_GOODS_SOLD",
    table_id: "FACT_SALES",
    ordinal_position: 21,
    business_name: "Cost of Goods Sold",
    business_description: "Direct cost of products sold in transaction. Critical for CPG profitability analysis, margin calculation, pricing decisions, and understanding cost structure impact on business performance across all categories.",
    business_synonyms: ["COGS", "Product Cost", "Direct Cost", "Manufacturing Cost", "Unit Cost", "Production Cost"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 2,
    business_criticality: "critical",
    additive_type: "additive"
}),

(gross_margin_amount:Column {
    column_id: "COL_GROSS_MARGIN_AMOUNT_FACT_922",
    column_name: "GROSS_MARGIN_AMOUNT",
    table_id: "FACT_SALES",
    ordinal_position: 22,
    business_name: "Gross Margin Amount",
    business_description: "Gross profit amount (Net Sales - COGS). Fundamental profitability measure for CPG business analysis, product line evaluation, category profitability, and investment prioritization across all product categories.",
    business_synonyms: ["Gross Profit", "Gross Margin", "Profit Amount", "Margin Dollars", "Contribution Margin", "Gross Profit Amount"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 2,
    business_criticality: "critical",
    additive_type: "additive"
}),

(gross_margin_percent:Column {
    column_id: "COL_GROSS_MARGIN_PERCENT_FACT_923",
    column_name: "GROSS_MARGIN_PERCENT",
    table_id: "FACT_SALES",
    ordinal_position: 23,
    business_name: "Gross Margin Percentage",
    business_description: "Gross margin as percentage of net sales. Essential for CPG margin analysis, pricing strategy, competitive positioning, and understanding margin trends across different product categories and price points.",
    business_synonyms: ["Margin %", "Gross Margin %", "Profit Margin %", "Margin Rate", "Profit Percentage", "Margin Ratio"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

// ========================================
// TAX AND REGULATORY AMOUNTS
// ========================================

(sales_tax_amount:Column {
    column_id: "COL_SALES_TAX_AMOUNT_FACT_924",
    column_name: "SALES_TAX_AMOUNT",
    table_id: "FACT_SALES",
    ordinal_position: 24,
    business_name: "Sales Tax Amount",
    business_description: "Sales tax collected on transaction. Important for CPG tax compliance, pricing analysis, and understanding tax impact on consumer behavior and total transaction value across different jurisdictions.",
    business_synonyms: ["Tax Amount", "State Tax", "Local Tax", "Transaction Tax", "VAT Amount", "Tax Collected"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 2,
    business_criticality: "medium",
    additive_type: "additive"
}),

(excise_tax_amount:Column {
    column_id: "COL_EXCISE_TAX_AMOUNT_FACT_925",
    column_name: "EXCISE_TAX_AMOUNT",
    table_id: "FACT_SALES",
    ordinal_position: 25,
    business_name: "Excise Tax Amount",
    business_description: "Excise taxes applied to specific product categories. Critical for alcoholic beverages, tobacco products, and other regulated categories requiring special tax compliance and pricing considerations.",
    business_synonyms: ["Federal Tax", "Special Tax", "Product Tax", "Category Tax", "Regulatory Tax", "Excise Duty"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery"],
    domain_specific: true,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 2,
    business_criticality: "high",
    additive_type: "additive",
    regulatory_relevance: ["TTB", "IRS", "STATE_TAX"]
}),

(deposit_amount:Column {
    column_id: "COL_DEPOSIT_AMOUNT_FACT_926",
    column_name: "DEPOSIT_AMOUNT",
    table_id: "FACT_SALES",
    ordinal_position: 26,
    business_name: "Container Deposit Amount",
    business_description: "Refundable container deposits for bottles and cans. Important for beverage categories in deposit states, environmental compliance, and understanding deposit impact on pricing and consumer behavior.",
    business_synonyms: ["Bottle Deposit", "Can Deposit", "Container Fee", "Refundable Deposit", "Environmental Fee", "Deposit Fee"],
    applicable_domains: ["alcoholic_beverages", "beverages", "food_beverage"],
    domain_specific: true,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 8,
    scale: 2,
    business_criticality: "medium",
    additive_type: "additive"
}),

// ========================================
// RETURNS AND ADJUSTMENTS
// ========================================

(return_quantity:Column {
    column_id: "COL_RETURN_QUANTITY_FACT_927",
    column_name: "RETURN_QUANTITY",
    table_id: "FACT_SALES",
    ordinal_position: 27,
    business_name: "Return Quantity",
    business_description: "Number of units returned in transaction. Essential for CPG return analysis, quality tracking, customer satisfaction measurement, and understanding return patterns across product categories and channels.",
    business_synonyms: ["Returned Units", "Return Volume", "Returned Quantity", "Units Returned", "Return Count", "Returned Items"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 3,
    business_criticality: "medium",
    additive_type: "additive"
}),

(return_amount:Column {
    column_id: "COL_RETURN_AMOUNT_FACT_928",
    column_name: "RETURN_AMOUNT",
    table_id: "FACT_SALES",
    ordinal_position: 28,
    business_name: "Return Amount",
    business_description: "Dollar value of returns processed. Important for CPG financial reconciliation, customer service analysis, quality cost tracking, and understanding financial impact of returns across categories.",
    business_synonyms: ["Return Value", "Returned Amount", "Refund Amount", "Return Revenue", "Return Dollars", "Refund Value"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 2,
    business_criticality: "medium",
    additive_type: "additive"
}),

(adjustment_amount:Column {
    column_id: "COL_ADJUSTMENT_AMOUNT_FACT_929",
    column_name: "ADJUSTMENT_AMOUNT",
    table_id: "FACT_SALES",
    ordinal_position: 29,
    business_name: "Price Adjustment Amount",
    business_description: "Price adjustments and corrections applied post-transaction. Essential for CPG pricing accuracy, customer service resolution, and understanding pricing error patterns and customer satisfaction impact.",
    business_synonyms: ["Price Adjustment", "Correction Amount", "Post-Sale Adjustment", "Price Correction", "Transaction Adjustment", "Billing Adjustment"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 2,
    business_criticality: "low",
    additive_type: "additive"
}),

// ========================================
// ADVANCED ANALYTICS MEASURES
// ========================================

(baseline_sales_amount:Column {
    column_id: "COL_BASELINE_SALES_AMOUNT_FACT_930",
    column_name: "BASELINE_SALES_AMOUNT",
    table_id: "FACT_SALES",
    ordinal_position: 30,
    business_name: "Baseline Sales Amount",
    business_description: "Estimated sales amount without promotional impact. Critical for CPG promotional effectiveness analysis, incremental sales calculation, and understanding true promotional lift across categories and campaigns.",
    business_synonyms: ["Base Sales", "Non-Promotional Sales", "Organic Sales", "Underlying Sales", "Core Sales", "Regular Sales"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 2,
    business_criticality: "high",
    additive_type: "additive"
}),

(incremental_sales_amount:Column {
    column_id: "COL_INCREMENTAL_SALES_AMOUNT_FACT_931",
    column_name: "INCREMENTAL_SALES_AMOUNT",
    table_id: "FACT_SALES",
    ordinal_position: 31,
    business_name: "Incremental Sales Amount",
    business_description: "Additional sales attributed to promotional activities. Essential for CPG promotional ROI calculation, trade spend effectiveness, and understanding true promotional value across different tactics and categories.",
    business_synonyms: ["Promotional Lift", "Incremental Revenue", "Promotion Impact", "Additional Sales", "Promotional Sales", "Lift Amount"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 2,
    business_criticality: "high",
    additive_type: "additive"
}),

(customer_lifetime_value_contribution:Column {
    column_id: "COL_CLV_CONTRIBUTION_FACT_932",
    column_name: "CUSTOMER_LIFETIME_VALUE_CONTRIBUTION",
    table_id: "FACT_SALES",
    ordinal_position: 32,
    business_name: "Customer Lifetime Value Contribution",
    business_description: "Transaction contribution to overall customer lifetime value. Important for CPG customer value analysis, retention strategy, and understanding transaction-level contribution to long-term customer relationships.",
    business_synonyms: ["CLV Contribution", "Customer Value", "Lifetime Value", "Customer Equity", "Long-term Value", "Customer Asset Value"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 2,
    business_criticality: "medium",
    additive_type: "additive"
}),

(market_share_impact:Column {
    column_id: "COL_MARKET_SHARE_IMPACT_FACT_933",
    column_name: "MARKET_SHARE_IMPACT",
    table_id: "FACT_SALES",
    ordinal_position: 33,
    business_name: "Market Share Impact",
    business_description: "Transaction impact on overall market share calculation. Critical for CPG competitive analysis, market position tracking, and understanding individual transaction contribution to market share growth or decline.",
    business_synonyms: ["Share Impact", "Market Contribution", "Competitive Impact", "Share Point", "Market Position", "Share Contribution"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 8,
    scale: 4,
    business_criticality: "medium",
    additive_type: "additive"
}),

// ========================================
// DIGITAL AND CHANNEL SPECIFIC MEASURES
// ========================================

(digital_sales_flag:Column {
    column_id: "COL_DIGITAL_SALES_FLAG_FACT_934",
    column_name: "DIGITAL_SALES_FLAG",
    table_id: "FACT_SALES",
    ordinal_position: 34,
    business_name: "Digital Sales Flag",
    business_description: "Indicates transaction occurred through digital channels. Essential for CPG omnichannel analysis, digital transformation tracking, and understanding digital vs traditional channel performance across categories.",
    business_synonyms: ["Online Sales Flag", "Digital Channel Flag", "E-commerce Flag", "Digital Transaction", "Online Flag", "Digital Indicator"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(mobile_sales_flag:Column {
    column_id: "COL_MOBILE_SALES_FLAG_FACT_935",
    column_name: "MOBILE_SALES_FLAG",
    table_id: "FACT_SALES",
    ordinal_position: 35,
    business_name: "Mobile Sales Flag",
    business_description: "Indicates transaction initiated through mobile device. Important for CPG mobile commerce analysis, mobile-first strategy evaluation, and understanding mobile shopping behavior patterns across categories.",
    business_synonyms: ["Mobile Flag", "Mobile Transaction", "Mobile Channel", "Mobile Commerce", "Mobile Purchase", "Mobile Indicator"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(subscription_sales_flag:Column {
    column_id: "COL_SUBSCRIPTION_SALES_FLAG_FACT_936",
    column_name: "SUBSCRIPTION_SALES_FLAG",
    table_id: "FACT_SALES",
    ordinal_position: 36,
    business_name: "Subscription Sales Flag",
    business_description: "Indicates transaction is part of subscription or auto-delivery program. Critical for CPG subscription commerce analysis, recurring revenue tracking, and understanding subscription impact on customer retention.",
    business_synonyms: ["Auto-Delivery Flag", "Recurring Flag", "Subscription Flag", "Auto-Ship Flag", "Repeat Delivery", "Subscription Indicator"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

// ========================================
// DOMAIN-SPECIFIC REGULATORY AND COMPLIANCE
// ========================================

(age_verification_flag:Column {
    column_id: "COL_AGE_VERIFICATION_FLAG_FACT_937",
    column_name: "AGE_VERIFICATION_FLAG",
    table_id: "FACT_SALES",
    ordinal_position: 37,
    business_name: "Age Verification Flag",
    business_description: "Indicates proper age verification was completed for age-restricted products. Critical for alcoholic beverage compliance, regulatory audit trails, and ensuring legal sales compliance across all channels.",
    business_synonyms: ["Age Verified", "ID Check Flag", "Age Compliance", "Legal Age Flag", "Verification Status", "Age Check Complete"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals"],
    domain_specific: true,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "critical",
    additive_type: "non_additive",
    regulatory_relevance: ["TTB", "STATE_LIQUOR_BOARDS", "FDA"]
}),

(prescription_number:Column {
    column_id: "COL_PRESCRIPTION_NUMBER_FACT_938",
    column_name: "PRESCRIPTION_NUMBER",
    table_id: "FACT_SALES",
    ordinal_position: 38,
    business_name: "Prescription Number",
    business_description: "Prescription number for pharmaceutical sales transactions. Essential for pharmaceutical compliance, traceability, insurance processing, and regulatory reporting requirements in healthcare channels.",
    business_synonyms: ["Rx Number", "Prescription ID", "Rx Reference", "Prescription Code", "Healthcare ID", "Medical Reference"],
    applicable_domains: ["pharmaceuticals"],
    domain_specific: true,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "critical",
    additive_type: "non_additive",
    regulatory_relevance: ["FDA", "DEA", "HIPAA"]
}),

(insurance_reimbursement_amount:Column {
    column_id: "COL_INSURANCE_REIMBURSEMENT_FACT_939",
    column_name: "INSURANCE_REIMBURSEMENT_AMOUNT",
    table_id: "FACT_SALES",
    ordinal_position: 39,
    business_name: "Insurance Reimbursement Amount",
    business_description: "Insurance coverage amount for pharmaceutical purchases. Important for healthcare channel analysis, insurance impact on accessibility, and understanding reimbursement effects on pharmaceutical sales patterns.",
    business_synonyms: ["Insurance Coverage", "Reimbursement", "Insurance Payment", "Coverage Amount", "Health Insurance", "Medical Coverage"],
    applicable_domains: ["pharmaceuticals", "health_supplements"],
    domain_specific: true,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 2,
    business_criticality: "medium",
    additive_type: "additive"
}),

(copay_amount:Column {
    column_id: "COL_COPAY_AMOUNT_FACT_940",
    column_name: "COPAY_AMOUNT",
    table_id: "FACT_SALES",
    ordinal_position: 40,
    business_name: "Patient Copay Amount",
    business_description: "Patient copayment amount for prescription medications. Critical for pharmaceutical pricing analysis, patient affordability assessment, and understanding copay impact on medication access and compliance.",
    business_synonyms: ["Patient Payment", "Copayment", "Patient Copay", "Out-of-Pocket", "Patient Responsibility", "Copay Fee"],
    applicable_domains: ["pharmaceuticals"],
    domain_specific: true,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 8,
    scale: 2,
    business_criticality: "medium",
    additive_type: "additive"
}),

// ========================================
// SEASONAL AND TEMPORAL ANALYSIS
// ========================================

(seasonal_adjustment_factor:Column {
    column_id: "COL_SEASONAL_ADJUSTMENT_FACTOR_FACT_941",
    column_name: "SEASONAL_ADJUSTMENT_FACTOR",
    table_id: "FACT_SALES",
    ordinal_position: 41,
    business_name: "Seasonal Adjustment Factor",
    business_description: "Statistical factor for seasonal sales normalization. Important for CPG trend analysis, year-over-year comparisons, and understanding underlying business performance independent of seasonal variations.",
    business_synonyms: ["Seasonal Factor", "Seasonality Index", "Seasonal Multiplier", "Trend Factor", "Seasonal Weight", "Adjustment Factor"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 8,
    scale: 4,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(holiday_sales_flag:Column {
    column_id: "COL_HOLIDAY_SALES_FLAG_FACT_942",
    column_name: "HOLIDAY_SALES_FLAG",
    table_id: "FACT_SALES",
    ordinal_position: 42,
    business_name: "Holiday Sales Flag",
    business_description: "Indicates transaction occurred during holiday period. Essential for CPG holiday analysis, seasonal planning, and understanding holiday impact on sales performance across categories like toys, food, and gifts.",
    business_synonyms: ["Holiday Flag", "Holiday Period", "Holiday Indicator", "Seasonal Flag", "Holiday Transaction", "Special Period"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

// ========================================
// INVENTORY AND SUPPLY CHAIN METRICS
// ========================================

(inventory_impact_units:Column {
    column_id: "COL_INVENTORY_IMPACT_UNITS_FACT_943",
    column_name: "INVENTORY_IMPACT_UNITS",
    table_id: "FACT_SALES",
    ordinal_position: 43,
    business_name: "Inventory Impact Units",
    business_description: "Units depleted from inventory by transaction. Important for CPG inventory management, stock rotation analysis, and understanding sales impact on inventory levels across different SKUs and locations.",
    business_synonyms: ["Inventory Depletion", "Stock Impact", "Inventory Change", "Stock Reduction", "Inventory Movement", "Stock Depletion"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 3,
    business_criticality: "medium",
    additive_type: "additive"
}),

(lot_number:Column {
    column_id: "COL_LOT_NUMBER_FACT_944",
    column_name: "LOT_NUMBER",
    table_id: "FACT_SALES",
    ordinal_position: 44,
    business_name: "Product Lot Number",
    business_description: "Manufacturing lot or batch number for traceability. Critical for CPG quality control, recall management, supply chain traceability, especially important for food, pharmaceutical, and baby product safety.",
    business_synonyms: ["Batch Number", "Lot Code", "Manufacturing Lot", "Batch Code", "Production Lot", "Trace Code"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(expiration_date:Column {
    column_id: "COL_EXPIRATION_DATE_FACT_945",
    column_name: "EXPIRATION_DATE",
    table_id: "FACT_SALES",
    ordinal_position: 45,
    business_name: "Product Expiration Date",
    business_description: "Product expiration or best-by date for freshness tracking. Essential for CPG perishable product management, shelf life analysis, and ensuring product quality across food, pharmaceutical, and personal care categories.",
    business_synonyms: ["Expiry Date", "Best By Date", "Use By Date", "Shelf Life", "Best Before", "Quality Date"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "food_beverage", "personal_care", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: true,
    semantic_type: "date",
    data_type: "DATE",
    max_length: null,
    business_criticality: "high",
    additive_type: "non_additive"
}),

// ========================================
// COMPETITIVE AND MARKET CONTEXT
// ========================================

(competitive_price_index:Column {
    column_id: "COL_COMPETITIVE_PRICE_INDEX_FACT_946",
    column_name: "COMPETITIVE_PRICE_INDEX",
    table_id: "FACT_SALES",
    ordinal_position: 46,
    business_name: "Competitive Price Index",
    business_description: "Price competitiveness index relative to market average. Important for CPG competitive analysis, pricing strategy evaluation, and understanding price positioning impact on sales performance across categories.",
    business_synonyms: ["Price Index", "Competitive Index", "Price Position", "Market Price Index", "Price Competitiveness", "Relative Price"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 2,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(market_share_points:Column {
    column_id: "COL_MARKET_SHARE_POINTS_FACT_947",
    column_name: "MARKET_SHARE_POINTS",
    table_id: "FACT_SALES",
    ordinal_position: 47,
    business_name: "Market Share Points",
    business_description: "Transaction contribution to market share in basis points. Critical for CPG market share tracking, competitive analysis, and understanding individual transaction impact on overall market position.",
    business_synonyms: ["Share Points", "Market Points", "Share Contribution", "Market Contribution", "Share Basis Points", "Market Impact Points"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 4,
    business_criticality: "medium",
    additive_type: "additive"
}),

// ========================================
// ATTRIBUTION AND MEDIA EFFECTIVENESS
// ========================================

(media_attribution_weight:Column {
    column_id: "COL_MEDIA_ATTRIBUTION_WEIGHT_FACT_948",
    column_name: "MEDIA_ATTRIBUTION_WEIGHT",
    table_id: "FACT_SALES",
    ordinal_position: 48,
    business_name: "Media Attribution Weight",
    business_description: "Statistical weight assigned to media exposure for attribution modeling. Essential for CPG media effectiveness measurement, ROI calculation, and understanding media contribution to sales conversion.",
    business_synonyms: ["Attribution Weight", "Media Weight", "Attribution Factor", "Media Contribution", "Attribution Score", "Media Influence"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 4,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(touchpoint_sequence_number:Column {
    column_id: "COL_TOUCHPOINT_SEQUENCE_FACT_949",
    column_name: "TOUCHPOINT_SEQUENCE_NUMBER",
    table_id: "FACT_SALES",
    ordinal_position: 49,
    business_name: "Touchpoint Sequence Number",
    business_description: "Sequential number of customer touchpoint leading to purchase. Important for CPG customer journey analysis, path-to-purchase optimization, and understanding multi-touch attribution patterns.",
    business_synonyms: ["Journey Sequence", "Touch Sequence", "Customer Journey Step", "Interaction Sequence", "Path Step", "Touchpoint Order"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

// ========================================
// PAYMENT AND TRANSACTION DETAILS
// ========================================

(payment_method:Column {
    column_id: "COL_PAYMENT_METHOD_FACT_950",
    column_name: "PAYMENT_METHOD",
    table_id: "FACT_SALES",
    ordinal_position: 50,
    business_name: "Payment Method",
    business_description: "Method of payment used for transaction (Cash, Credit Card, Debit Card, Digital Wallet, etc.). Important for CPG payment analysis, customer convenience, and understanding payment preference trends across demographics.",
    business_synonyms: ["Payment Type", "Payment Mode", "Tender Type", "Payment Option", "Payment Channel", "Transaction Method"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(transaction_time:Column {
    column_id: "COL_TRANSACTION_TIME_FACT_951",
    column_name: "TRANSACTION_TIME",
    table_id: "FACT_SALES",
    ordinal_position: 51,
    business_name: "Transaction Time",
    business_description: "Time of day when transaction occurred. Essential for CPG daypart analysis, staffing optimization, inventory planning, and understanding shopping behavior patterns throughout the day.",
    business_synonyms: ["Sale Time", "Purchase Time", "Transaction Timestamp", "Sale Timestamp", "Purchase Timestamp", "Time of Sale"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "temporal",
    data_type: "TIME",
    max_length: null,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

// ========================================
// QUALITY AND SATISFACTION METRICS
// ========================================

(product_rating:Column {
    column_id: "COL_PRODUCT_RATING_FACT_952",
    column_name: "PRODUCT_RATING",
    table_id: "FACT_SALES",
    ordinal_position: 52,
    business_name: "Product Rating",
    business_description: "Customer rating or satisfaction score for purchased product. Important for CPG quality monitoring, customer satisfaction tracking, and understanding rating impact on future sales and recommendations.",
    business_synonyms: ["Customer Rating", "Product Score", "Satisfaction Rating", "Quality Rating", "Customer Score", "Product Feedback"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 3,
    scale: 2,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(repeat_purchase_flag:Column {
    column_id: "COL_REPEAT_PURCHASE_FLAG_FACT_953",
    column_name: "REPEAT_PURCHASE_FLAG",
    table_id: "FACT_SALES",
    ordinal_position: 53,
    business_name: "Repeat Purchase Flag",
    business_description: "Indicates customer has purchased this product previously. Critical for CPG loyalty analysis, repeat purchase behavior, customer retention measurement, and understanding product satisfaction and loyalty patterns.",
    business_synonyms: ["Repeat Flag", "Loyalty Flag", "Return Customer", "Repeat Buyer", "Loyalty Purchase", "Return Purchase"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "high",
    additive_type: "non_additive"
}),

// ========================================
// METADATA AND AUDIT INFORMATION
// ========================================

(data_source:Column {
    column_id: "COL_DATA_SOURCE_FACT_954",
    column_name: "DATA_SOURCE",
    table_id: "FACT_SALES",
    ordinal_position: 54,
    business_name: "Data Source",
    business_description: "Source system or platform that generated sales data (POS, E-commerce, ERP, etc.). Important for CPG data quality management, source system reconciliation, and understanding data lineage for audit and accuracy.",
    business_synonyms: ["Source System", "Data Origin", "System Source", "Source Platform", "Data System", "Source Application"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(record_created_timestamp:Column {
    column_id: "COL_RECORD_CREATED_TIMESTAMP_FACT_955",
    column_name: "RECORD_CREATED_TIMESTAMP",
    table_id: "FACT_SALES",
    ordinal_position: 55,
    business_name: "Record Created Timestamp",
    business_description: "System timestamp when sales record was created in data warehouse. Used for audit trails, data lineage tracking, and data governance. Immutable after creation. Part of comprehensive data governance framework.",
    business_synonyms: ["Creation Time", "Insert Timestamp", "Created Date", "Record Creation", "Entry Timestamp", "Load Timestamp"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    max_length: null,
    business_criticality: "low",
    additive_type: "non_additive"
}),

(record_modified_timestamp:Column {
    column_id: "COL_RECORD_MODIFIED_TIMESTAMP_FACT_956",
    column_name: "RECORD_MODIFIED_TIMESTAMP",
    table_id: "FACT_SALES",
    ordinal_position: 56,
    business_name: "Record Last Modified Timestamp",
    business_description: "Most recent update timestamp for sales record. Tracks data freshness, change frequency, and maintenance activities. Updated automatically with any field modification for data governance compliance.",
    business_synonyms: ["Update Time", "Last Modified", "Change Timestamp", "Modified Date", "Last Updated", "Revision Timestamp"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    max_length: null,
    business_criticality: "low",
    additive_type: "non_additive"
});

// ========================================
// CREATE TABLE-COLUMN RELATIONSHIPS
// ========================================

MATCH (t:Table {table_id: "FACT_SALES"})
MATCH (c:Column {table_id: "FACT_SALES"})
CREATE (t)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"]
}]->(c);

// ========================================
// VERIFICATION QUERIES
// ========================================

// Count total columns created
MATCH (c:Column {table_id: "FACT_SALES"})
RETURN count(c) AS total_columns;

// Verify columns by business criticality
MATCH (c:Column {table_id: "FACT_SALES"})
RETURN c.business_criticality AS criticality_level, 
       count(c) AS column_count
ORDER BY criticality_level;

// List critical columns
MATCH (c:Column {table_id: "FACT_SALES"})
WHERE c.business_criticality = "critical"
RETURN c.business_name AS critical_column, 
       c.business_description
ORDER BY c.ordinal_position;

// Check domain-specific columns
MATCH (c:Column {table_id: "FACT_SALES"})
WHERE c.domain_specific = true
RETURN c.business_name AS domain_specific_column, 
       c.applicable_domains AS specific_domains,
       c.regulatory_relevance AS regulations
ORDER BY c.ordinal_position;

// Check columns by category
MATCH (c:Column {table_id: "FACT_SALES"})
RETURN 
    CASE 
        WHEN c.ordinal_position <= 7 THEN "Core Transaction Identifiers & Foreign Keys"
        WHEN c.ordinal_position <= 11 THEN "Core Sales Quantity Measures"
        WHEN c.ordinal_position <= 15 THEN "Core Financial Measures - Gross Sales"
        WHEN c.ordinal_position <= 20 THEN "Discount & Allowance Breakdown"
        WHEN c.ordinal_position <= 23 THEN "Cost & Profitability Measures"
        WHEN c.ordinal_position <= 26 THEN "Tax & Regulatory Amounts"
        WHEN c.ordinal_position <= 29 THEN "Returns & Adjustments"
        WHEN c.ordinal_position <= 33 THEN "Advanced Analytics Measures"
        WHEN c.ordinal_position <= 36 THEN "Digital & Channel Specific"
        WHEN c.ordinal_position <= 40 THEN "Domain-Specific Regulatory"
        WHEN c.ordinal_position <= 42 THEN "Seasonal & Temporal Analysis"
        WHEN c.ordinal_position <= 45 THEN "Inventory & Supply Chain"
        WHEN c.ordinal_position <= 47 THEN "Competitive & Market Context"
        WHEN c.ordinal_position <= 49 THEN "Attribution & Media Effectiveness"
        WHEN c.ordinal_position <= 51 THEN "Payment & Transaction Details"
        WHEN c.ordinal_position <= 53 THEN "Quality & Satisfaction"
        ELSE "Metadata & Audit"
    END AS category,
    count(c) AS column_count
ORDER BY category;

// Verify semantic types distribution
MATCH (c:Column {table_id: "FACT_SALES"})
RETURN c.semantic_type AS semantic_type, 
       count(c) AS column_count
ORDER BY column_count DESC;

// Check additive types for measures
MATCH (c:Column {table_id: "FACT_SALES"})
WHERE c.additive_type IS NOT NULL
RETURN c.additive_type AS additive_type,
       count(c) AS column_count
ORDER BY column_count DESC;

// Check for primary key
MATCH (c:Column {table_id: "FACT_SALES"})
WHERE c.is_primary_key = true
RETURN c.business_name AS primary_key_column, 
       c.column_name AS column_name;

// Verify foreign key relationships
MATCH (c:Column {table_id: "FACT_SALES"})
WHERE c.is_foreign_key = true
RETURN c.business_name AS foreign_key_column,
       c.column_name AS column_name,
       c.business_description
ORDER BY c.ordinal_position;

// Check regulatory relevance columns
MATCH (c:Column {table_id: "FACT_SALES"})
WHERE c.regulatory_relevance IS NOT NULL
RETURN c.business_name AS regulatory_column, 
       c.regulatory_relevance AS regulations,
       c.applicable_domains AS domains
ORDER BY c.ordinal_position;

// Verify financial measures
MATCH (c:Column {table_id: "FACT_SALES"})
WHERE c.semantic_type = "measure" AND c.data_type = "DECIMAL"
RETURN c.business_name AS financial_measure,
       c.precision AS precision,
       c.scale AS scale,
       c.additive_type AS additive_type
ORDER BY c.ordinal_position;

// Check flag/boolean columns
MATCH (c:Column {table_id: "FACT_SALES"})
WHERE c.data_type = "BOOLEAN"
RETURN c.business_name AS boolean_column,
       c.business_description
ORDER BY c.ordinal_position;

// Verify core sales measures
MATCH (c:Column {table_id: "FACT_SALES"})
WHERE c.business_name CONTAINS "Sales" OR c.business_name CONTAINS "Quantity" OR c.business_name CONTAINS "Amount"
RETURN c.business_name AS sales_measure,
       c.data_type AS data_type,
       c.additive_type AS additive_type,
       c.business_criticality AS criticality
ORDER BY c.ordinal_position;

// Check domain-specific measures by domain
MATCH (c:Column {table_id: "FACT_SALES"})
WHERE c.domain_specific = true
RETURN c.applicable_domains[0] AS primary_domain,
       collect(c.business_name) AS domain_columns
ORDER BY primary_domain;

// ========================================
// END OF FACT_SALES COLUMN CREATION
// ========================================