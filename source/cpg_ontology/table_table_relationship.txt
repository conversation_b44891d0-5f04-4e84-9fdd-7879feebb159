// ========================================
// CREATE TABLE-TO-TABLE RELATIONSHIPS
// Execute after all tables are created
// Creates direct relationships between fact and dimension tables
// ========================================

// ========================================
// FACT_SALES TABLE RELATIONSHIPS
// ========================================

// FACT_SALES -> DIM_DATE
MATCH (fs:Table {table_id: "FACT_SALES"})
MATCH (dd:Table {table_id: "DIM_DATE"})
CREATE (fs)-[:RELATES_TO {
    relationship_type: "fact_to_dimension",
    relationship_name: "Sales by Date",
    cardinality: "many_to_one",
    join_type: "inner",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_purpose: "Enables time-series analysis of sales performance",
    common_analytics: ["Daily/Weekly/Monthly sales trends", "YoY comparisons", "Seasonal analysis"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dd);

// FACT_SALES -> DIM_STORE
MATCH (fs:Table {table_id: "FACT_SALES"})
MATCH (ds:Table {table_id: "DIM_STORE"})
CREATE (fs)-[:RELATES_TO {
    relationship_type: "fact_to_dimension",
    relationship_name: "Sales by Store",
    cardinality: "many_to_one",
    join_type: "inner",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_purpose: "Enables location-based sales analysis and store performance tracking",
    common_analytics: ["Store performance", "Geographic analysis", "Channel analytics"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(ds);

// FACT_SALES -> DIM_PRODUCT_HIERARCHY
MATCH (fs:Table {table_id: "FACT_SALES"})
MATCH (dp:Table {table_id: "DIM_PRODUCT_HIERARCHY"})
CREATE (fs)-[:RELATES_TO {
    relationship_type: "fact_to_dimension",
    relationship_name: "Sales by Product",
    cardinality: "many_to_one",
    join_type: "inner",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_purpose: "Enables product performance analysis and category management",
    common_analytics: ["Product mix analysis", "Category performance", "SKU rationalization"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dp);

// FACT_SALES -> DIM_CUSTOMER
MATCH (fs:Table {table_id: "FACT_SALES"})
MATCH (dc:Table {table_id: "DIM_CUSTOMER"})
CREATE (fs)-[:RELATES_TO {
    relationship_type: "fact_to_dimension",
    relationship_name: "Sales by Customer",
    cardinality: "many_to_one",
    join_type: "left",
    is_mandatory: false,
    relationship_strength: 0.8,
    business_purpose: "Enables customer analytics for identified shoppers",
    common_analytics: ["Customer segmentation", "Loyalty analysis", "Personalization"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dc);

// FACT_SALES -> DIM_PROMOTION
MATCH (fs:Table {table_id: "FACT_SALES"})
MATCH (dp:Table {table_id: "DIM_PROMOTION"})
CREATE (fs)-[:RELATES_TO {
    relationship_type: "fact_to_dimension",
    relationship_name: "Sales by Promotion",
    cardinality: "many_to_one",
    join_type: "left",
    is_mandatory: false,
    relationship_strength: 0.6,
    business_purpose: "Enables promotional effectiveness analysis",
    common_analytics: ["Promotional lift", "ROI analysis", "Baseline vs incremental"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dp);

// ========================================
// FACT_REGULATORY_COMPLIANCE TABLE RELATIONSHIPS
// ========================================

// FACT_REGULATORY_COMPLIANCE -> DIM_DATE
MATCH (fr:Table {table_id: "FACT_REGULATORY_COMPLIANCE"})
MATCH (dd:Table {table_id: "DIM_DATE"})
CREATE (fr)-[:RELATES_TO {
    relationship_type: "fact_to_dimension",
    relationship_name: "Compliance Events by Date",
    cardinality: "many_to_one",
    join_type: "inner",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_purpose: "Tracks compliance events and violations over time",
    common_analytics: ["Compliance trends", "Violation patterns", "Audit schedules"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dd);

// FACT_REGULATORY_COMPLIANCE -> DIM_STORE
MATCH (fr:Table {table_id: "FACT_REGULATORY_COMPLIANCE"})
MATCH (ds:Table {table_id: "DIM_STORE"})
CREATE (fr)-[:RELATES_TO {
    relationship_type: "fact_to_dimension",
    relationship_name: "Compliance by Location",
    cardinality: "many_to_one",
    join_type: "inner",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_purpose: "Tracks compliance issues by facility and location",
    common_analytics: ["Facility compliance", "Geographic patterns", "Store risk assessment"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(ds);

// FACT_REGULATORY_COMPLIANCE -> DIM_PRODUCT_HIERARCHY
MATCH (fr:Table {table_id: "FACT_REGULATORY_COMPLIANCE"})
MATCH (dp:Table {table_id: "DIM_PRODUCT_HIERARCHY"})
CREATE (fr)-[:RELATES_TO {
    relationship_type: "fact_to_dimension",
    relationship_name: "Compliance by Product",
    cardinality: "many_to_one",
    join_type: "left",
    is_mandatory: false,
    relationship_strength: 0.8,
    business_purpose: "Links compliance events to specific products when applicable",
    common_analytics: ["Product recalls", "Safety issues", "Quality violations"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dp);

// FACT_REGULATORY_COMPLIANCE -> DIM_CUSTOMER
MATCH (fr:Table {table_id: "FACT_REGULATORY_COMPLIANCE"})
MATCH (dc:Table {table_id: "DIM_CUSTOMER"})
CREATE (fr)-[:RELATES_TO {
    relationship_type: "fact_to_dimension",
    relationship_name: "Compliance by Customer",
    cardinality: "many_to_one",
    join_type: "left",
    is_mandatory: false,
    relationship_strength: 0.5,
    business_purpose: "Links compliance events to customer complaints or incidents",
    common_analytics: ["Customer complaints", "Safety incidents", "Consumer reports"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dc);

// ========================================
// FACT_PROMOTIONAL_SPEND TABLE RELATIONSHIPS
// ========================================

// FACT_PROMOTIONAL_SPEND -> DIM_DATE
MATCH (fp:Table {table_id: "FACT_PROMOTIONAL_SPEND"})
MATCH (dd:Table {table_id: "DIM_DATE"})
CREATE (fp)-[:RELATES_TO {
    relationship_type: "fact_to_dimension",
    relationship_name: "Promotional Spend by Date",
    cardinality: "many_to_one",
    join_type: "inner",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_purpose: "Tracks promotional investments over time",
    common_analytics: ["Spend trends", "Budget tracking", "Period comparisons"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dd);

// FACT_PROMOTIONAL_SPEND -> DIM_STORE
MATCH (fp:Table {table_id: "FACT_PROMOTIONAL_SPEND"})
MATCH (ds:Table {table_id: "DIM_STORE"})
CREATE (fp)-[:RELATES_TO {
    relationship_type: "fact_to_dimension",
    relationship_name: "Promotional Spend by Store",
    cardinality: "many_to_one",
    join_type: "inner",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_purpose: "Allocates promotional spend to specific locations",
    common_analytics: ["Store-level ROI", "Channel spend analysis", "Location effectiveness"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(ds);

// FACT_PROMOTIONAL_SPEND -> DIM_PRODUCT_HIERARCHY
MATCH (fp:Table {table_id: "FACT_PROMOTIONAL_SPEND"})
MATCH (dp:Table {table_id: "DIM_PRODUCT_HIERARCHY"})
CREATE (fp)-[:RELATES_TO {
    relationship_type: "fact_to_dimension",
    relationship_name: "Promotional Spend by Product",
    cardinality: "many_to_one",
    join_type: "inner",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_purpose: "Tracks promotional investments by product",
    common_analytics: ["Product ROI", "Brand investment", "Category spend analysis"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dp);

// FACT_PROMOTIONAL_SPEND -> DIM_CUSTOMER
MATCH (fp:Table {table_id: "FACT_PROMOTIONAL_SPEND"})
MATCH (dc:Table {table_id: "DIM_CUSTOMER"})
CREATE (fp)-[:RELATES_TO {
    relationship_type: "fact_to_dimension",
    relationship_name: "Promotional Spend by Customer",
    cardinality: "many_to_one",
    join_type: "left",
    is_mandatory: false,
    relationship_strength: 0.7,
    business_purpose: "Tracks customer-specific promotional investments",
    common_analytics: ["Targeted promotions", "Personalized offers", "Customer ROI"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dc);

// FACT_PROMOTIONAL_SPEND -> DIM_PROMOTION
MATCH (fp:Table {table_id: "FACT_PROMOTIONAL_SPEND"})
MATCH (dpr:Table {table_id: "DIM_PROMOTION"})
CREATE (fp)-[:RELATES_TO {
    relationship_type: "fact_to_dimension",
    relationship_name: "Promotional Spend by Campaign",
    cardinality: "many_to_one",
    join_type: "inner",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_purpose: "Links spend to specific promotional campaigns",
    common_analytics: ["Campaign ROI", "Promotion effectiveness", "Spend efficiency"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dpr);

// ========================================
// FACT_INVENTORY TABLE RELATIONSHIPS
// ========================================

// FACT_INVENTORY -> DIM_DATE
MATCH (fi:Table {table_id: "FACT_INVENTORY"})
MATCH (dd:Table {table_id: "DIM_DATE"})
CREATE (fi)-[:RELATES_TO {
    relationship_type: "fact_to_dimension",
    relationship_name: "Inventory by Date",
    cardinality: "many_to_one",
    join_type: "inner",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_purpose: "Tracks inventory levels and movements over time",
    common_analytics: ["Stock trends", "Inventory turns", "Days of supply"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dd);

// FACT_INVENTORY -> DIM_STORE
MATCH (fi:Table {table_id: "FACT_INVENTORY"})
MATCH (ds:Table {table_id: "DIM_STORE"})
CREATE (fi)-[:RELATES_TO {
    relationship_type: "fact_to_dimension",
    relationship_name: "Inventory by Location",
    cardinality: "many_to_one",
    join_type: "inner",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_purpose: "Tracks inventory positions by location",
    common_analytics: ["Stock balancing", "Distribution optimization", "OOS analysis"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(ds);

// FACT_INVENTORY -> DIM_PRODUCT_HIERARCHY
MATCH (fi:Table {table_id: "FACT_INVENTORY"})
MATCH (dp:Table {table_id: "DIM_PRODUCT_HIERARCHY"})
CREATE (fi)-[:RELATES_TO {
    relationship_type: "fact_to_dimension",
    relationship_name: "Inventory by Product",
    cardinality: "many_to_one",
    join_type: "inner",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_purpose: "Tracks inventory levels by product",
    common_analytics: ["SKU availability", "Product turns", "Obsolescence tracking"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dp);

// FACT_INVENTORY -> DIM_MANUFACTURER (as Supplier)
MATCH (fi:Table {table_id: "FACT_INVENTORY"})
MATCH (dm:Table {table_id: "DIM_MANUFACTURER"})
CREATE (fi)-[:RELATES_TO {
    relationship_type: "fact_to_dimension",
    relationship_name: "Inventory by Supplier",
    cardinality: "many_to_one",
    join_type: "left",
    is_mandatory: false,
    relationship_strength: 0.8,
    business_purpose: "Tracks vendor-managed inventory and supplier performance",
    common_analytics: ["VMI analysis", "Supplier performance", "Lead time tracking"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dm);

// ========================================
// FACT_ECOMMERCE_SALES TABLE RELATIONSHIPS
// ========================================

// FACT_ECOMMERCE_SALES -> DIM_DATE
MATCH (fe:Table {table_id: "FACT_ECOMMERCE_SALES"})
MATCH (dd:Table {table_id: "DIM_DATE"})
CREATE (fe)-[:RELATES_TO {
    relationship_type: "fact_to_dimension",
    relationship_name: "E-commerce Sales by Date",
    cardinality: "many_to_one",
    join_type: "inner",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_purpose: "Tracks online sales over time",
    common_analytics: ["Digital trends", "Peak periods", "Conversion tracking"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dd);

// FACT_ECOMMERCE_SALES -> DIM_STORE
MATCH (fe:Table {table_id: "FACT_ECOMMERCE_SALES"})
MATCH (ds:Table {table_id: "DIM_STORE"})
CREATE (fe)-[:RELATES_TO {
    relationship_type: "fact_to_dimension",
    relationship_name: "E-commerce Sales by Channel",
    cardinality: "many_to_one",
    join_type: "inner",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_purpose: "Tracks sales by digital storefront/marketplace",
    common_analytics: ["Channel performance", "Marketplace analysis", "Platform comparison"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(ds);

// FACT_ECOMMERCE_SALES -> DIM_PRODUCT_HIERARCHY
MATCH (fe:Table {table_id: "FACT_ECOMMERCE_SALES"})
MATCH (dp:Table {table_id: "DIM_PRODUCT_HIERARCHY"})
CREATE (fe)-[:RELATES_TO {
    relationship_type: "fact_to_dimension",
    relationship_name: "E-commerce Sales by Product",
    cardinality: "many_to_one",
    join_type: "inner",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_purpose: "Tracks online sales by product",
    common_analytics: ["Digital product mix", "Online performance", "Digital shelf analysis"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dp);

// FACT_ECOMMERCE_SALES -> DIM_CUSTOMER
MATCH (fe:Table {table_id: "FACT_ECOMMERCE_SALES"})
MATCH (dc:Table {table_id: "DIM_CUSTOMER"})
CREATE (fe)-[:RELATES_TO {
    relationship_type: "fact_to_dimension",
    relationship_name: "E-commerce Sales by Customer",
    cardinality: "many_to_one",
    join_type: "inner",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_purpose: "Tracks identified online customer purchases",
    common_analytics: ["Customer journey", "Digital behavior", "Personalization"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dc);

// FACT_ECOMMERCE_SALES -> DIM_PROMOTION
MATCH (fe:Table {table_id: "FACT_ECOMMERCE_SALES"})
MATCH (dp:Table {table_id: "DIM_PROMOTION"})
CREATE (fe)-[:RELATES_TO {
    relationship_type: "fact_to_dimension",
    relationship_name: "E-commerce Sales by Promotion",
    cardinality: "many_to_one",
    join_type: "left",
    is_mandatory: false,
    relationship_strength: 0.7,
    business_purpose: "Links online sales to digital promotions",
    common_analytics: ["Digital promo effectiveness", "Coupon redemption", "Campaign attribution"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dp);

// ========================================
// FACT_SYNDICATED_PANEL TABLE RELATIONSHIPS
// ========================================

// FACT_SYNDICATED_PANEL -> DIM_DATE
MATCH (fsp:Table {table_id: "FACT_SYNDICATED_PANEL"})
MATCH (dd:Table {table_id: "DIM_DATE"})
CREATE (fsp)-[:RELATES_TO {
    relationship_type: "fact_to_dimension",
    relationship_name: "Panel Data by Period",
    cardinality: "many_to_one",
    join_type: "inner",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_purpose: "Tracks market share and competitive metrics over time",
    common_analytics: ["Market share trends", "Competitive analysis", "Category dynamics"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dd);

// FACT_SYNDICATED_PANEL -> DIM_PRODUCT_HIERARCHY
MATCH (fsp:Table {table_id: "FACT_SYNDICATED_PANEL"})
MATCH (dp:Table {table_id: "DIM_PRODUCT_HIERARCHY"})
CREATE (fsp)-[:RELATES_TO {
    relationship_type: "fact_to_dimension",
    relationship_name: "Panel Data by Product",
    cardinality: "many_to_one",
    join_type: "inner",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_purpose: "Tracks competitive product performance",
    common_analytics: ["Share analysis", "Competitive benchmarking", "Category performance"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dp);

// ========================================
// FACT_PRICING TABLE RELATIONSHIPS
// ========================================

// FACT_PRICING -> DIM_DATE
MATCH (fpr:Table {table_id: "FACT_PRICING"})
MATCH (dd:Table {table_id: "DIM_DATE"})
CREATE (fpr)-[:RELATES_TO {
    relationship_type: "fact_to_dimension",
    relationship_name: "Pricing by Date",
    cardinality: "many_to_one",
    join_type: "inner",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_purpose: "Tracks price changes and trends over time",
    common_analytics: ["Price trends", "Inflation tracking", "Competitive pricing"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dd);

// FACT_PRICING -> DIM_STORE
MATCH (fpr:Table {table_id: "FACT_PRICING"})
MATCH (ds:Table {table_id: "DIM_STORE"})
CREATE (fpr)-[:RELATES_TO {
    relationship_type: "fact_to_dimension",
    relationship_name: "Pricing by Location",
    cardinality: "many_to_one",
    join_type: "inner",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_purpose: "Tracks location-specific pricing strategies",
    common_analytics: ["Zone pricing", "Competitive pricing", "Price optimization"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(ds);

// FACT_PRICING -> DIM_PRODUCT_HIERARCHY
MATCH (fpr:Table {table_id: "FACT_PRICING"})
MATCH (dp:Table {table_id: "DIM_PRODUCT_HIERARCHY"})
CREATE (fpr)-[:RELATES_TO {
    relationship_type: "fact_to_dimension",
    relationship_name: "Pricing by Product",
    cardinality: "many_to_one",
    join_type: "inner",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_purpose: "Tracks product-specific pricing",
    common_analytics: ["Price ladders", "SKU pricing", "Category pricing strategy"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dp);

// FACT_PRICING -> DIM_CUSTOMER
MATCH (fpr:Table {table_id: "FACT_PRICING"})
MATCH (dc:Table {table_id: "DIM_CUSTOMER"})
CREATE (fpr)-[:RELATES_TO {
    relationship_type: "fact_to_dimension",
    relationship_name: "Pricing by Customer",
    cardinality: "many_to_one",
    join_type: "left",
    is_mandatory: false,
    relationship_strength: 0.6,
    business_purpose: "Tracks customer-specific pricing and contracts",
    common_analytics: ["Contract pricing", "B2B pricing", "Customer profitability"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dc);

// ========================================
// INTER-DIMENSIONAL TABLE RELATIONSHIPS
// ========================================

// DIM_PRODUCT_HIERARCHY -> DIM_BRAND_MASTER
MATCH (ph:Table {table_id: "DIM_PRODUCT_HIERARCHY"})
MATCH (b:Table {table_id: "DIM_BRAND_MASTER"})
CREATE (ph)-[:RELATES_TO {
    relationship_type: "dimension_to_dimension",
    relationship_name: "Products to Brands",
    cardinality: "many_to_one",
    join_type: "inner",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_purpose: "Links products to their parent brands",
    common_analytics: ["Brand portfolio analysis", "Brand performance", "Brand extensions"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(b);

// DIM_PRODUCT_HIERARCHY -> DIM_CATEGORY
MATCH (ph:Table {table_id: "DIM_PRODUCT_HIERARCHY"})
MATCH (c:Table {table_id: "DIM_CATEGORY"})
CREATE (ph)-[:RELATES_TO {
    relationship_type: "dimension_to_dimension",
    relationship_name: "Products to Categories",
    cardinality: "many_to_one",
    join_type: "inner",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_purpose: "Classifies products into categories",
    common_analytics: ["Category management", "Category performance", "Assortment planning"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(c);

// DIM_PRODUCT_HIERARCHY -> DIM_MANUFACTURER
MATCH (ph:Table {table_id: "DIM_PRODUCT_HIERARCHY"})
MATCH (m:Table {table_id: "DIM_MANUFACTURER"})
CREATE (ph)-[:RELATES_TO {
    relationship_type: "dimension_to_dimension",
    relationship_name: "Products to Manufacturers",
    cardinality: "many_to_one",
    join_type: "inner",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_purpose: "Links products to their manufacturers",
    common_analytics: ["Supplier performance", "Vendor analytics", "Manufacturing analysis"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(m);

// DIM_PROMOTION -> DIM_MEDIA
MATCH (p:Table {table_id: "DIM_PROMOTION"})
MATCH (m:Table {table_id: "DIM_MEDIA"})
CREATE (p)-[:RELATES_TO {
    relationship_type: "dimension_to_dimension",
    relationship_name: "Promotions to Media",
    cardinality: "many_to_many",
    join_type: "left",
    is_mandatory: false,
    relationship_strength: 0.6,
    business_purpose: "Links promotions to supporting media campaigns",
    common_analytics: ["Integrated marketing", "Media effectiveness", "Campaign ROI"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(m);

// DIM_BRAND_MASTER -> DIM_MANUFACTURER
MATCH (b:Table {table_id: "DIM_BRAND_MASTER"})
MATCH (m:Table {table_id: "DIM_MANUFACTURER"})
CREATE (b)-[:RELATES_TO {
    relationship_type: "dimension_to_dimension",
    relationship_name: "Brands to Manufacturers",
    cardinality: "many_to_one",
    join_type: "inner",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_purpose: "Links brands to their owning manufacturers",
    common_analytics: ["Brand ownership", "Manufacturer portfolio", "M&A analysis"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(m);

// ========================================
// VALIDATION QUERIES
// ========================================

// Count table relationships by type
// MATCH (t1:Table)-[r:RELATES_TO]->(t2:Table)
// RETURN r.relationship_type as rel_type, COUNT(r) as count
// ORDER BY rel_type;

// List all star schema relationships (fact to dimension)
// MATCH (f:Table {table_type: "fact"})-[r:RELATES_TO]->(d:Table {table_type: "dimension"})
// RETURN f.table_name as fact_table, d.table_name as dimension_table, r.relationship_name
// ORDER BY f.table_name, d.table_name;

// Find orphaned tables (no relationships)
// MATCH (t:Table)
// WHERE NOT EXISTS((t)-[:RELATES_TO]-(:Table)) AND NOT EXISTS((:Table)-[:RELATES_TO]-(t))
// RETURN t.table_name as orphaned_table;

// ========================================
// END OF TABLE-TO-TABLE RELATIONSHIPS
// ========================================