// ========================================
// CPG DOMAIN ONTOLOGY - TABLE CREATION WITH BUSINESS SYNONYMS
// Complete table node creation with enhanced synonym lists
// ========================================

// Clear existing tables (optional)
// MATCH (t:Table) DETACH DELETE t;

// ========================================
// DIMENSION TABLES
// ========================================

// Product Dimension Table
CREATE (dim_product:Table {
    table_id: "DIM_PRODUCT_MASTER",
    table_name: "DIM_PRODUCT_MASTER",
    schema_name: "DIMENSIONS",
    database_name: "CPG_ENTERPRISE_DW",
    business_name: "Product Dimension",
    business_description: "Master product dimension serving as the foundational reference for all CPG products across six domains. Contains comprehensive product attributes including regulatory compliance fields, syndicated data classifications, and e-commerce enablement flags. This dimension supports cross-domain analytics, planogram optimization, and serves as the single source of truth for product master data management (MDM). Critical for linking scanner data, panel data, and internal shipment data for holistic product performance analysis.",
    business_domain: "Product",
    business_subdomain: "Multi-Domain Product Management",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    business_concepts: ["Product Identity", "Cross-Domain SKU Management", "Regulatory Compliance", "Safety Standards", "Planogram Management", "E-commerce Enablement"],
    business_synonyms: [
        "Product Catalog",
        "Multi-Domain Item Master",
        "CPG Product Registry",
        "SKU Master",
        "Universal Product Reference",
        "Item Dimension",
        "Product Reference Data",
        "Master SKU List",
        "Global Product Repository",
        "Product MDM",
        "Item Registry",
        "Product Information Management",
        "PIM",
        "Central Product Database",
        "SKU Dimension",
        "Product Authority File",
        "Item Catalog",
        "Product Master File",
        "SKU Registry",
        "Product Data Hub"
    ],
    table_type: "dimension",
    data_pattern: "slowly_changing_dimension",
    granularity: "one_row_per_sku_all_domains",
    business_criticality: "critical",
    regulatory_scope: ["FDA", "TTB", "CPSC", "DEA", "USDA", "FTC"]
});

// Brand Dimension Table
CREATE (dim_brand:Table {
    table_id: "DIM_BRAND_MASTER",
    table_name: "DIM_BRAND_MASTER",
    schema_name: "DIMENSIONS",
    database_name: "CPG_ENTERPRISE_DW",
    business_name: "Brand Dimension",
    business_description: "Comprehensive brand portfolio dimension encompassing all CPG domains with brand equity metrics, market positioning, and syndicated data mappings. Tracks brand hierarchies, sub-brands, and cross-domain brand extensions. Essential for brand performance analytics, market share tracking, and competitive intelligence. Integrates with Nielsen and IRI brand families for consistent syndicated data reporting and supports brand health KPIs including awareness, consideration, and loyalty metrics.",
    business_domain: "Product",
    business_subdomain: "Multi-Domain Brand Management",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    business_concepts: ["Brand Portfolio", "Cross-Domain Brand Strategy", "Regulatory Brand Management", "Brand Equity", "Market Positioning"],
    business_synonyms: [
        "Brand Portfolio",
        "Brand Registry",
        "Brand Hierarchy",
        "Brand Catalog",
        "Brand Reference Data",
        "Brand Family Dimension",
        "Master Brand List",
        "Brand Equity Database",
        "Brand Management System",
        "Brand Authority File",
        "Marketing Brand Repository",
        "Brand Architecture",
        "Brand Master File",
        "Brand Directory",
        "Brand Asset Registry",
        "Brand Taxonomy",
        "Brand Information System",
        "Brand MDM",
        "Brand Data Hub"
    ],
    table_type: "dimension",
    data_pattern: "slowly_changing_dimension",
    business_criticality: "high"
});

// Manufacturer Dimension Table
CREATE (dim_manufacturer:Table {
    table_id: "DIM_MANUFACTURER",
    table_name: "DIM_MANUFACTURER",
    schema_name: "DIMENSIONS",
    database_name: "CPG_ENTERPRISE_DW",
    business_name: "Manufacturer Dimension",
    business_description: "Manufacturer and supplier dimension containing comprehensive vendor master data with domain-specific regulatory licenses, quality certifications, and compliance tracking. Supports multi-tier supplier relationships, contract manufacturer management, and co-packing arrangements. Critical for supply chain analytics, vendor scorecarding, and regulatory compliance reporting across FDA, DEA, TTB, and other regulatory bodies. Includes sustainability ratings and diversity supplier classifications.",
    business_domain: "Supplier",
    business_subdomain: "Multi-Domain Supplier Management",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    business_concepts: ["Supplier Management", "Regulatory Compliance", "Quality Certifications", "Vendor Scorecarding"],
    business_synonyms: [
        "Vendor Master",
        "Supplier Registry",
        "Manufacturer Database",
        "Vendor Dimension",
        "Supplier Authority File",
        "Producer Registry",
        "Vendor Management System",
        "Manufacturer Reference",
        "Supplier MDM",
        "Vendor Catalog",
        "Trading Partner Directory",
        "Supplier Information Management",
        "Vendor Database",
        "Supplier Master File",
        "Manufacturer Directory",
        "Vendor Registry",
        "Supply Partner Catalog",
        "Producer Database",
        "Vendor Authority System"
    ],
    table_type: "dimension",
    data_pattern: "slowly_changing_dimension",
    business_criticality: "high"
});

// Category Dimension Table
CREATE (dim_category:Table {
    table_id: "DIM_CATEGORY",
    table_name: "DIM_CATEGORY",
    schema_name: "DIMENSIONS",
    database_name: "CPG_ENTERPRISE_DW",
    business_name: "Category Dimension",
    business_description: "Multi-level category hierarchy supporting both internal category management and external syndicated data classifications. Maintains mappings to Nielsen product modules, IRI categories, and retailer-specific planogram categories. Essential for category management, shelf space optimization, and cross-category cannibalization analysis. Includes category roles (destination, routine, convenience, seasonal) and growth strategies.",
    business_domain: "Product",
    business_subdomain: "Multi-Domain Category Management",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    business_concepts: ["Category Management", "Product Taxonomy", "Cross-Domain Classification", "Planogram Categories"],
    business_synonyms: [
        "Product Category Hierarchy",
        "Category Tree",
        "Product Classification",
        "Category Management System",
        "Product Taxonomy",
        "Category Structure",
        "Merchandising Hierarchy",
        "Product Grouping",
        "Category Reference",
        "Department Class Subclass",
        "Product Type Classification",
        "Category Architecture",
        "Product Category Master",
        "Category Dimension",
        "Merchandise Classification",
        "Product Group Hierarchy",
        "Category Registry",
        "Product Category Tree",
        "Category MDM"
    ],
    table_type: "dimension",
    data_pattern: "hierarchy",
    business_criticality: "high"
});

// Store Dimension Table
CREATE (dim_store:Table {
    table_id: "DIM_STORE",
    table_name: "DIM_STORE",
    schema_name: "DIMENSIONS",
    database_name: "CPG_ENTERPRISE_DW",
    business_name: "Store Dimension",
    business_description: "Comprehensive retail location dimension encompassing physical stores, e-commerce platforms, and omnichannel touchpoints. Contains store attributes, trading area demographics, competitive density analysis, and syndicated universe flags. Supports location-based analytics, store clustering, space-to-sales optimization, and geo-spatial competitive analysis. Includes e-commerce fulfillment capabilities and last-mile delivery options.",
    business_domain: "Channel",
    business_subdomain: "Multi-Domain Retail Management",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    business_concepts: ["Retail Management", "Channel Strategy", "License Management", "Store Clustering", "Omnichannel Analytics"],
    business_synonyms: [
        "Location Master",
        "Retail Location Directory",
        "Store Registry",
        "Location Dimension",
        "Site Master",
        "Retail Outlet Database",
        "Store Directory",
        "Location Reference",
        "Channel Location Master",
        "Retail Site Catalog",
        "Store Information System",
        "Location MDM",
        "Store Master File",
        "Retail Location Registry",
        "Site Directory",
        "Store Database",
        "Location Authority File",
        "Retail Site Master",
        "Store Location Catalog"
    ],
    table_type: "dimension",
    data_pattern: "slowly_changing_dimension",
    business_criticality: "high"
});

// Date Dimension Table
CREATE (dim_date:Table {
    table_id: "DIM_DATE",
    table_name: "DIM_DATE",
    schema_name: "DIMENSIONS",
    database_name: "CPG_ENTERPRISE_DW",
    business_name: "Date Dimension",
    business_description: "Enterprise date dimension with comprehensive calendar intelligence supporting multiple fiscal calendars, syndicated data periods (Nielsen/IRI weeks), and domain-specific seasonality. Includes promotional calendar integration, regulatory filing deadlines, and cultural events impacting CPG sales. Essential for time-series analysis, period-over-period comparisons, and seasonal decomposition. Supports 4-4-5, 4-5-4, and custom retail calendars.",
    business_domain: "Time",
    business_subdomain: "Multi-Domain Calendar Management",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    business_concepts: ["Time Intelligence", "Regulatory Calendar", "Seasonal Events", "Promotional Calendar", "Syndicated Periods"],
    business_synonyms: [
        "Calendar Dimension",
        "Time Dimension",
        "Date Reference",
        "Temporal Dimension",
        "Calendar Master",
        "Date Hierarchy",
        "Time Intelligence",
        "Period Calendar",
        "Fiscal Calendar",
        "Date Registry",
        "Time Reference Table",
        "Calendar Intelligence",
        "Date Master",
        "Time Period Dimension",
        "Calendar Reference",
        "Temporal Reference",
        "Date/Time Dimension",
        "Calendar Table",
        "Time Series Reference"
    ],
    table_type: "dimension",
    data_pattern: "time_dimension",
    business_criticality: "critical"
});

// Customer Dimension Table
CREATE (dim_customer:Table {
    table_id: "DIM_CUSTOMER",
    table_name: "DIM_CUSTOMER",
    schema_name: "DIMENSIONS",
    database_name: "CPG_ENTERPRISE_DW",
    business_name: "Customer Dimension",
    business_description: "Unified customer dimension supporting both B2C shopper analytics and B2B account management. Integrates loyalty program data, household panel demographics, and digital engagement metrics. Enables customer segmentation, lifetime value analysis, and personalized marketing. Includes privacy-compliant PII handling and GDPR/CCPA consent management. Links to syndicated household panel data for market basket and brand switching analysis.",
    business_domain: "Customer",
    business_subdomain: "Customer Analytics and Segmentation",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    business_concepts: ["Customer Segmentation", "Loyalty Analytics", "Household Demographics", "Privacy Management"],
    business_synonyms: [
        "Customer Master",
        "Consumer Registry",
        "Shopper Database",
        "Customer MDM",
        "Account Master",
        "Consumer Dimension",
        "Customer 360",
        "Shopper Profile Database",
        "Customer Information File",
        "Consumer Authority File",
        "Customer Data Platform",
        "Shopper Registry",
        "Customer Registry",
        "Consumer Master File",
        "Shopper Master",
        "Customer Profile Repository",
        "Consumer Database",
        "Customer Authority System",
        "Shopper Information System"
    ],
    table_type: "dimension",
    data_pattern: "slowly_changing_dimension",
    business_criticality: "high"
});

// Promotion Dimension Table
CREATE (dim_promotion:Table {
    table_id: "DIM_PROMOTION",
    table_name: "DIM_PROMOTION",
    schema_name: "DIMENSIONS",
    database_name: "CPG_ENTERPRISE_DW",
    business_name: "Promotion Dimension",
    business_description: "Master promotion dimension tracking all promotional mechanics, campaign details, and trade investment strategies. Includes temporary price reductions (TPR), coupons, BOGO offers, display agreements, and digital promotions. Essential for ROI analysis, lift measurement, and trade spend optimization. Integrates with retailer promotion calendars and supports promotional effectiveness modeling.",
    business_domain: "Marketing",
    business_subdomain: "Trade Promotion Management",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    business_concepts: ["Trade Promotion", "Promotional ROI", "Campaign Management", "Discount Mechanics"],
    business_synonyms: [
        "Promotion Registry",
        "Campaign Master",
        "Promotional Event Database",
        "Trade Promotion Master",
        "Marketing Campaign Catalog",
        "Promotion Calendar",
        "Deal Registry",
        "Promotional Activity Database",
        "Campaign Dimension",
        "Trade Investment Master",
        "Promotion Management System",
        "Deal Sheet Repository",
        "Promotional Master File",
        "Campaign Registry",
        "Trade Deal Database",
        "Promotion Authority File",
        "Marketing Event Registry",
        "Promotional Calendar Master"
    ],
    table_type: "dimension",
    data_pattern: "slowly_changing_dimension",
    business_criticality: "high"
});

// Product Hierarchy Dimension Table
CREATE (dim_product_hierarchy:Table {
    table_id: "DIM_PRODUCT_HIERARCHY",
    table_name: "DIM_PRODUCT_HIERARCHY",
    schema_name: "DIMENSIONS",
    database_name: "CPG_ENTERPRISE_DW",
    business_name: "Product Hierarchy Dimension",
    business_description: "Multi-level product hierarchy supporting various classification schemes including internal hierarchies, GS1 Global Product Classification, and retailer-specific taxonomies. Enables drill-down/roll-up analytics, product family groupings, and cross-category analysis. Critical for assortment planning, category management, and executive reporting at various aggregation levels.",
    business_domain: "Product",
    business_subdomain: "Product Classification and Taxonomy",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    business_concepts: ["Product Taxonomy", "Hierarchy Management", "Classification Systems", "Aggregation Levels"],
    business_synonyms: [
        "Product Classification Tree",
        "Item Hierarchy",
        "Product Grouping Structure",
        "SKU Hierarchy",
        "Product Tree Structure",
        "Merchandise Hierarchy",
        "Product Rollup Dimension",
        "Category Tree Structure",
        "Product Family Tree",
        "Item Classification System",
        "Product Aggregation Levels",
        "Assortment Hierarchy",
        "Product Level Structure",
        "Item Grouping Hierarchy",
        "Product Classification System",
        "SKU Tree Structure",
        "Product Hierarchy Master",
        "Item Tree Registry"
    ],
    table_type: "dimension",
    data_pattern: "hierarchy",
    business_criticality: "high"
});

// Media Dimension Table
CREATE (dim_media:Table {
    table_id: "DIM_MEDIA",
    table_name: "DIM_MEDIA",
    schema_name: "DIMENSIONS",
    database_name: "CPG_ENTERPRISE_DW",
    business_name: "Media Dimension",
    business_description: "Integrated media campaign dimension tracking traditional and digital marketing efforts including TV, digital display, social media, influencer partnerships, and retail media networks. Supports media mix modeling, attribution analysis, and ROAS measurement. Includes creative assets, flight dates, targeting parameters, and performance KPIs.",
    business_domain: "Marketing",
    business_subdomain: "Media and Advertising",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    business_concepts: ["Media Planning", "Campaign Management", "Attribution Modeling", "Retail Media Networks"],
    business_synonyms: [
        "Media Campaign Registry",
        "Advertising Master",
        "Marketing Media Catalog",
        "Campaign Creative Database",
        "Media Investment Registry",
        "Advertising Campaign Master",
        "Media Channel Directory",
        "Marketing Communications Database",
        "Creative Asset Registry",
        "Media Plan Repository",
        "Advertising Registry",
        "Media Master File",
        "Marketing Media Database",
        "Creative Campaign Catalog",
        "Media Planning System",
        "Advertising Asset Library",
        "Media Campaign Master"
    ],
    table_type: "dimension",
    data_pattern: "slowly_changing_dimension",
    business_criticality: "medium"
});

// ========================================
// FACT TABLES
// ========================================

// Sales Fact Table
CREATE (fact_sales:Table {
    table_id: "FACT_SALES",
    table_name: "FACT_SALES",
    schema_name: "FACTS",
    database_name: "CPG_ENTERPRISE_DW",
    business_name: "Sales Fact",
    business_description: "Core sales fact table capturing point-of-sale transactions, shipment data, and syndicated sales measurements across all CPG domains. Integrates scanner data, panel data, and internal shipments for comprehensive sales analytics. Includes baseline/incremental decomposition, price elasticity metrics, and competitive intelligence. Supports daily, weekly, and period-level aggregations with promotional lift analysis and cannibalization tracking.",
    business_domain: "Sales",
    business_subdomain: "Multi-Domain Sales Analytics",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    business_concepts: ["Sales Performance", "Cross-Domain Analytics", "Regulatory Compliance Tracking", "Baseline Analytics", "Promotional Lift"],
    business_synonyms: [
        "Sales Transactions",
        "POS Data",
        "Revenue Fact",
        "Sales Activity",
        "Transaction Detail",
        "Sales Performance Data",
        "Revenue Transactions",
        "Sales Metrics",
        "POS Transaction Log",
        "Sales Journal",
        "Revenue Registry",
        "Transactional Sales Data",
        "Sales Activity Log",
        "Revenue Journal",
        "POS Registry",
        "Sales Transaction Table",
        "Revenue Detail",
        "Sales Fact Table",
        "Transaction Registry"
    ],
    table_type: "fact",
    data_pattern: "accumulating_snapshot",
    granularity: "daily_product_store_all_domains",
    business_criticality: "critical"
});

// Regulatory Compliance Fact Table
CREATE (fact_regulatory:Table {
    table_id: "FACT_REGULATORY_COMPLIANCE",
    table_name: "FACT_REGULATORY_COMPLIANCE",
    schema_name: "FACTS",
    database_name: "CPG_COMPLIANCE_DW",
    business_name: "Regulatory Compliance Fact",
    business_description: "Cross-domain regulatory compliance tracking including violations, certifications, audits, and corrective actions. Monitors compliance with FDA, TTB, CPSC, DEA, USDA, and state-level regulations. Includes recall tracking, safety incident reporting, and certification renewal management. Critical for risk management, compliance reporting, and maintaining licenses to operate across regulated CPG domains.",
    business_domain: "Compliance",
    business_subdomain: "Multi-Domain Regulatory Management",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    business_concepts: ["Regulatory Compliance", "Safety Standards", "Quality Assurance", "Risk Management", "Audit Tracking"],
    business_synonyms: [
        "Compliance Tracking",
        "Regulatory Event Log",
        "Compliance Registry",
        "Audit Trail",
        "Regulatory Violations Log",
        "Compliance Incident Database",
        "Regulatory Activity",
        "Compliance Metrics",
        "Regulatory Tracking System",
        "Compliance Journal",
        "Regulatory Event Repository",
        "Compliance Activity Log",
        "Regulatory Audit Trail",
        "Compliance Transaction Log",
        "Regulatory Incident Registry",
        "Compliance Event Database",
        "Regulatory Compliance Journal"
    ],
    table_type: "fact",
    data_pattern: "accumulating_snapshot",
    business_criticality: "critical"
});

// Promotional Spend Fact Table
CREATE (fact_promo_spend:Table {
    table_id: "FACT_PROMOTIONAL_SPEND",
    table_name: "FACT_PROMOTIONAL_SPEND",
    schema_name: "FACTS",
    database_name: "CPG_ENTERPRISE_DW",
    business_name: "Promotional Spend Fact",
    business_description: "Comprehensive trade promotion and marketing spend tracking across all promotional vehicles. Captures planned vs actual spend, bill-backs, scan-backs, MCBs, accruals, and deductions. Enables trade ROI analysis, promotional effectiveness measurement, and spending optimization. Integrates with retailer deduction systems and supports promotional settlement workflows.",
    business_domain: "Marketing",
    business_subdomain: "Trade Spend Analytics",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    business_concepts: ["Trade Spend", "Promotional ROI", "Marketing Investment", "Deduction Management"],
    business_synonyms: [
        "Trade Spend Transactions",
        "Promotional Investment Log",
        "Marketing Spend Registry",
        "Trade Funding Database",
        "Promotional Expense Journal",
        "Deal Funding Transactions",
        "Trade Investment Ledger",
        "Promotional Cost Tracking",
        "Marketing Investment Detail",
        "Trade Allowance Registry",
        "Promotional Funding Log",
        "Trade Spend Journal",
        "Marketing Expense Database",
        "Promotional Payment Registry",
        "Trade Investment Transactions",
        "Deal Funding Journal",
        "Promotional Spend Ledger"
    ],
    table_type: "fact",
    data_pattern: "accumulating_snapshot",
    granularity: "promotion_product_store",
    business_criticality: "high"
});

// Inventory Fact Table
CREATE (fact_inventory:Table {
    table_id: "FACT_INVENTORY",
    table_name: "FACT_INVENTORY",
    schema_name: "FACTS",
    database_name: "CPG_ENTERPRISE_DW",
    business_name: "Inventory Fact",
    business_description: "Multi-echelon inventory tracking across retail stores, distribution centers, and in-transit. Monitors stock levels, turns, days of supply, and out-of-stock incidents. Critical for supply chain optimization, demand planning, and service level management. Includes safety stock analysis, obsolescence tracking, and phantom inventory identification.",
    business_domain: "Supply Chain",
    business_subdomain: "Inventory Management",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    business_concepts: ["Inventory Optimization", "Stock Management", "Service Levels", "Supply Chain Analytics"],
    business_synonyms: [
        "Stock Position",
        "Inventory Snapshots",
        "Stock Level Registry",
        "Inventory Transactions",
        "Stock on Hand",
        "Inventory Position Log",
        "Stock Movement Journal",
        "Inventory Metrics",
        "Stock Availability",
        "Inventory Balance Sheet",
        "Stock Level Tracking",
        "Warehouse Inventory Log",
        "Inventory Position Table",
        "Stock Registry",
        "Inventory Status Log",
        "Stock Balance Journal",
        "Inventory Snapshot Table",
        "Stock Position Registry"
    ],
    table_type: "fact",
    data_pattern: "periodic_snapshot",
    granularity: "daily_product_location",
    business_criticality: "high"
});

// E-commerce Sales Fact Table
CREATE (fact_ecommerce:Table {
    table_id: "FACT_ECOMMERCE_SALES",
    table_name: "FACT_ECOMMERCE_SALES",
    schema_name: "FACTS",
    database_name: "CPG_ENTERPRISE_DW",
    business_name: "E-commerce Sales Fact",
    business_description: "Digital commerce transactions with enhanced metrics for online sales including click-through rates, conversion funnels, digital shelf analytics, and marketplace performance. Tracks pure-play e-commerce, omnichannel orders, and click-and-collect transactions. Includes digital marketing attribution, subscription commerce metrics, and competitive digital shelf monitoring.",
    business_domain: "Sales",
    business_subdomain: "Digital Commerce Analytics",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    business_concepts: ["Digital Commerce", "Conversion Analytics", "Digital Shelf", "Marketplace Performance"],
    business_synonyms: [
        "Digital Sales Transactions",
        "Online Order Detail",
        "E-commerce Transactions",
        "Digital Commerce Log",
        "Online Sales Registry",
        "Web Sales Data",
        "Digital Order Journal",
        "E-commerce Activity",
        "Online Transaction Detail",
        "Digital Channel Sales",
        "Web Order Registry",
        "E-tail Transaction Log",
        "Online Sales Journal",
        "Digital Commerce Registry",
        "Web Transaction Database",
        "E-commerce Order Log",
        "Digital Sales Activity",
        "Online Commerce Transactions"
    ],
    table_type: "fact",
    data_pattern: "transaction",
    granularity: "order_line_item",
    business_criticality: "high"
});

// Syndicated Panel Fact Table
CREATE (fact_panel:Table {
    table_id: "FACT_SYNDICATED_PANEL",
    table_name: "FACT_SYNDICATED_PANEL",
    schema_name: "FACTS",
    database_name: "CPG_ENTERPRISE_DW",
    business_name: "Syndicated Panel Fact",
    business_description: "Household panel data from Nielsen Homescan and IRI Consumer Network providing shopper behavior insights, brand switching analysis, and market basket intelligence. Tracks purchase occasions, trip missions, basket composition, and cross-category purchasing. Enables consumer segmentation, loyalty analysis, and new buyer tracking with demographic overlays.",
    business_domain: "Consumer Analytics",
    business_subdomain: "Panel Data Analytics",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    business_concepts: ["Household Panel", "Shopping Behavior", "Brand Switching", "Market Basket Analysis"],
    business_synonyms: [
        "Panel Purchase Data",
        "Household Shopping Log",
        "Consumer Panel Transactions",
        "Shopper Panel Activity",
        "Household Purchase Registry",
        "Panel Scan Data",
        "Consumer Behavior Log",
        "Household Transaction Detail",
        "Panel Shopping Basket",
        "Consumer Purchase Journal",
        "Household Panel Registry",
        "Panel Transaction Database",
        "Shopper Behavior Log",
        "Household Scan Data",
        "Consumer Panel Registry",
        "Panel Purchase Journal",
        "Household Shopping Database"
    ],
    table_type: "fact",
    data_pattern: "transaction",
    granularity: "household_trip_item",
    business_criticality: "high"
});

// Pricing Fact Table
CREATE (fact_pricing:Table {
    table_id: "FACT_PRICING",
    table_name: "FACT_PRICING",
    schema_name: "FACTS",
    database_name: "CPG_ENTERPRISE_DW",
    business_name: "Pricing Fact",
    business_description: "Comprehensive pricing intelligence tracking regular prices, promotional prices, competitor prices, and price elasticity metrics. Captures shelf prices, was-now savings, competitive price gaps, and price ladder positioning. Supports pricing strategy optimization, competitive monitoring, and margin analysis. Includes MAP compliance tracking and dynamic pricing metrics.",
    business_domain: "Pricing",
    business_subdomain: "Price Analytics and Intelligence",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    business_concepts: ["Price Optimization", "Competitive Intelligence", "Price Elasticity", "Margin Management"],
    business_synonyms: [
        "Price Point Registry",
        "Pricing Transactions",
        "Price Change Log",
        "Competitive Pricing Data",
        "Price Intelligence",
        "Pricing Activity Journal",
        "Price Point Tracking",
        "Pricing Metrics",
        "Price Movement Log",
        "Competitive Price Registry",
        "Price Elasticity Data",
        "Market Pricing Intelligence",
        "Pricing History Log",
        "Price Point Database",
        "Competitive Intelligence Registry",
        "Price Tracking Journal",
        "Market Price Database",
        "Pricing Transaction Log"
    ],
    table_type: "fact",
    data_pattern: "periodic_snapshot",
    granularity: "weekly_product_store",
    business_criticality: "high"
});

// ========================================
// END OF TABLE CREATION WITH BUSINESS SYNONYMS
// ========================================


