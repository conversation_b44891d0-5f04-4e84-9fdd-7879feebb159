// ========================================
// DIM_STORE - COMPREHENSIVE COLUMN CREATION
// Complete store/location dimension columns for all CPG domains
// ========================================

// Clear existing columns for DIM_STORE (optional)
MATCH (c:Column {table_id: "DIM_STORE"}) DETACH DELETE c;

// ========================================
// CORE STORE IDENTIFIERS AND ATTRIBUTES
// ========================================

CREATE 
(store_id:Column {
    column_id: "COL_STORE_ID_DIM_300",
    column_name: "STORE_ID",
    table_id: "DIM_STORE",
    ordinal_position: 1,
    business_name: "Store ID",
    business_description: "Unique identifier for retail locations including physical stores, e-commerce sites, and distribution centers. Format includes retailer prefix for multi-retailer environments. Critical for location-based analytics and territory management.",
    business_synonyms: ["Location ID", "Store Number", "Site ID", "Store Key", "Outlet ID", "Location Code", "Store Code", "Branch ID", "Retail ID"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 20,
    is_primary_key: true,
    business_criticality: "critical"
}),
(store_number:Column {
    column_id: "COL_STORE_NUMBER_301",
    column_name: "STORE_NUMBER",
    table_id: "DIM_STORE",
    ordinal_position: 2,
    business_name: "Store Number",
    business_description: "Retailer's internal store number used in operations and communications. May differ from Store ID. Critical for field teams and vendor communications. Often displayed at store entrance.",
    business_synonyms: ["Store Num", "Branch Number", "Location Number", "Site Number", "Outlet Number", "Store #", "Internal Number"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "high"
}),
(store_name:Column {
    column_id: "COL_STORE_NAME_302",
    column_name: "STORE_NAME",
    table_id: "DIM_STORE",
    ordinal_position: 3,
    business_name: "Store Name",
    business_description: "Common store name or number used by retailer and field teams. May include location identifiers (Store #1234 - Main St). Used in reporting and field communications. Must be unique within retailer.",
    business_synonyms: ["Location Name", "Store Description", "Branch Name", "Site Name", "Store Label", "Outlet Name", "Store Title"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "high"
}),
(retailer_id:Column {
    column_id: "COL_RETAILER_ID_303",
    column_name: "RETAILER_ID",
    table_id: "DIM_STORE",
    ordinal_position: 4,
    business_name: "Retailer ID",
    business_description: "Parent retailer identifier linking to retailer master. Critical for chain-level analysis and corporate account management. Handles multi-banner retailers and acquisitions.",
    business_synonyms: ["Chain ID", "Customer ID", "Account ID", "Retailer Code", "Parent ID", "Corporate ID", "Chain Code"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 20,
    is_foreign_key: true,
    business_criticality: "critical"
}),
(retailer_name:Column {
    column_id: "COL_RETAILER_NAME_304",
    column_name: "RETAILER_NAME",
    table_id: "DIM_STORE",
    ordinal_position: 5,
    business_name: "Retailer Name",
    business_description: "Parent retailer or chain name for multi-banner organizations. Critical for corporate negotiations and chain-wide analytics. May include sub-banners (Kroger/Ralphs/Fred Meyer). Drives account team structure.",
    business_synonyms: ["Chain Name", "Retailer", "Customer Name", "Account Name", "Company Name", "Banner Name", "Corporate Name"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "high"
}),
(banner_name:Column {
    column_id: "COL_BANNER_NAME_305",
    column_name: "BANNER_NAME",
    table_id: "DIM_STORE",
    ordinal_position: 6,
    business_name: "Banner Name",
    business_description: "Specific retail banner under parent company (e.g., Ralphs under Kroger). May have different positioning and demographics than parent. Important for banner-specific programs.",
    business_synonyms: ["Sub-Banner", "Division Name", "Operating Banner", "Store Banner", "Brand Name", "Fascia", "Trading Name"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "medium"
}),

// ========================================
// STORE FORMAT AND CHARACTERISTICS
// ========================================

(store_format:Column {
    column_id: "COL_STORE_FORMAT_306",
    column_name: "STORE_FORMAT",
    table_id: "DIM_STORE",
    ordinal_position: 7,
    business_name: "Store Format",
    business_description: "Retail format classification determining assortment, pricing, and shopper demographics. Critical for space planning and format-specific strategies. E-commerce includes pure-play and omnichannel. Drives promotional tactics.",
    business_synonyms: ["Format Type", "Store Type", "Channel Format", "Retail Format", "Store Class", "Format Classification", "Channel Type"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    acceptable_values: ["SUPERMARKET", "DRUG", "MASS", "CLUB", "CONVENIENCE", "ONLINE", "SUPERCENTER", "DOLLAR", "SPECIALTY"],
    business_criticality: "high"
}),
(store_sub_format:Column {
    column_id: "COL_STORE_SUB_FORMAT_307",
    column_name: "STORE_SUB_FORMAT",
    table_id: "DIM_STORE",
    ordinal_position: 8,
    business_name: "Store Sub-Format",
    business_description: "Detailed format classification (Premium Supermarket, Small Format Drug, etc.). Enables refined targeting and assortment strategies. Growing importance with format proliferation.",
    business_synonyms: ["Sub-Type", "Detailed Format", "Format Detail", "Store Subclass", "Secondary Format", "Format Variant"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "medium"
}),
(store_size_sqft:Column {
    column_id: "COL_STORE_SIZE_SQFT_308",
    column_name: "STORE_SIZE_SQFT",
    table_id: "DIM_STORE",
    ordinal_position: 9,
    business_name: "Store Size Square Feet",
    business_description: "Total retail square footage for physical stores. Key factor in assortment decisions and sales potential modeling. Correlates with SKU count capacity. NULL for pure e-commerce. Used in space-to-sales productivity metrics.",
    business_synonyms: ["Square Footage", "Store Size", "Retail Space", "Floor Space", "Selling Area", "Total SQFT", "Store Area"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    business_criticality: "medium"
}),
(selling_area_sqft:Column {
    column_id: "COL_SELLING_AREA_SQFT_309",
    column_name: "SELLING_AREA_SQFT",
    table_id: "DIM_STORE",
    ordinal_position: 10,
    business_name: "Selling Area Square Feet",
    business_description: "Net selling space excluding backroom and service areas. More accurate for productivity metrics than total size. Used for space allocation and department sizing decisions.",
    business_synonyms: ["Sales Floor", "Retail Area", "Shopping Area", "Net Selling Space", "Customer Area", "Floor Space"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    business_criticality: "medium"
}),
(checkout_lanes_count:Column {
    column_id: "COL_CHECKOUT_LANES_COUNT_310",
    column_name: "CHECKOUT_LANES_COUNT",
    table_id: "DIM_STORE",
    ordinal_position: 11,
    business_name: "Checkout Lanes Count",
    business_description: "Number of checkout registers including self-checkout. Indicator of store traffic capacity and peak volume handling. Important for front-end merchandising and impulse categories.",
    business_synonyms: ["Register Count", "Checkout Count", "Lane Count", "POS Count", "Till Count", "Cashier Stations"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    business_criticality: "low"
}),

// ========================================
// LOCATION AND GEOGRAPHY
// ========================================

(store_address:Column {
    column_id: "COL_STORE_ADDRESS_311",
    column_name: "STORE_ADDRESS",
    table_id: "DIM_STORE",
    ordinal_position: 12,
    business_name: "Store Street Address",
    business_description: "Physical street address for store location. Critical for delivery routing, field visits, and local marketing. Must be standardized for geocoding accuracy.",
    business_synonyms: ["Street Address", "Location Address", "Physical Address", "Store Location", "Site Address", "Mailing Address"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 200,
    business_criticality: "high"
}),
(city:Column {
    column_id: "COL_CITY_312",
    column_name: "CITY",
    table_id: "DIM_STORE",
    ordinal_position: 13,
    business_name: "City",
    business_description: "Store city location for geographic analysis and field routing. Critical for local marketing and competitive density analysis. Must be standardized for aggregation. Links to demographic and weather data.",
    business_synonyms: ["Town", "Municipality", "City Name", "Locality", "Urban Area", "Metro Area"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "medium"
}),
(state_province:Column {
    column_id: "COL_STATE_PROVINCE_313",
    column_name: "STATE_PROVINCE",
    table_id: "DIM_STORE",
    ordinal_position: 14,
    business_name: "State or Province",
    business_description: "State or province code for regional analysis and regulatory compliance. Determines applicable taxes, deposits, and selling restrictions. Critical for territory definitions and state-specific regulations. Uses standard 2-letter codes.",
    business_synonyms: ["State", "Province", "Region", "State Code", "Provincial Code", "Administrative Division"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high"
}),
(zip_code:Column {
    column_id: "COL_ZIP_CODE_314",
    column_name: "ZIP_CODE",
    table_id: "DIM_STORE",
    ordinal_position: 15,
    business_name: "ZIP Code",
    business_description: "Postal code enabling demographic overlays and trade area analysis. Links to census data for income, ethnicity, and household composition. Critical for targeted marketing and assortment localization. 5 or 9 digit format.",
    business_synonyms: ["Postal Code", "ZIP", "Post Code", "Mailing Code", "ZIP+4", "Postal District"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 10,
    business_criticality: "medium"
}),
(country:Column {
    column_id: "COL_COUNTRY_315",
    column_name: "COUNTRY",
    table_id: "DIM_STORE",
    ordinal_position: 16,
    business_name: "Country",
    business_description: "Country location for international retailers. Determines currency, regulations, and cultural considerations. Critical for global supply chain and pricing strategies.",
    business_synonyms: ["Nation", "Country Code", "Country Name", "Territory", "National Market"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "high"
}),
(latitude:Column {
    column_id: "COL_LATITUDE_316",
    column_name: "LATITUDE",
    table_id: "DIM_STORE",
    ordinal_position: 17,
    business_name: "Latitude",
    business_description: "Geographic latitude coordinate enabling spatial analysis and mapping. Critical for trade area analysis, competitive proximity calculations, and delivery routing. Precision to 6 decimal places for 0.1m accuracy.",
    business_synonyms: ["Lat", "Y Coordinate", "Latitude Coordinate", "Geographic Latitude", "GPS Latitude"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 6,
    business_criticality: "medium"
}),
(longitude:Column {
    column_id: "COL_LONGITUDE_317",
    column_name: "LONGITUDE",
    table_id: "DIM_STORE",
    ordinal_position: 18,
    business_name: "Longitude",
    business_description: "Geographic longitude coordinate for spatial analysis. Paired with latitude for complete positioning. Enables competitor distance calculations and territory optimization. Essential for location-based mobile marketing.",
    business_synonyms: ["Long", "Lng", "X Coordinate", "Longitude Coordinate", "Geographic Longitude", "GPS Longitude"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 11,
    scale: 6,
    business_criticality: "medium"
}),
(time_zone:Column {
    column_id: "COL_TIME_ZONE_318",
    column_name: "TIME_ZONE",
    table_id: "DIM_STORE",
    ordinal_position: 19,
    business_name: "Time Zone",
    business_description: "Store time zone for operational hours and promotional timing. Critical for coordinating national promotions and e-commerce order cutoffs. Affects field team scheduling.",
    business_synonyms: ["TZ", "Zone", "Time Region", "Clock Zone", "Local Time Zone", "Store Time Zone"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "medium"
}),

// ========================================
// TRADING AREA DEMOGRAPHICS
// ========================================

(trading_area_radius:Column {
    column_id: "COL_TRADING_AREA_RADIUS_319",
    column_name: "TRADING_AREA_RADIUS",
    table_id: "DIM_STORE",
    ordinal_position: 20,
    business_name: "Trading Area Radius Miles",
    business_description: "Primary trading area radius in miles. Varies by format and urban/rural location. Defines catchment for demographic analysis and competitive overlap calculations.",
    business_synonyms: ["Trade Radius", "Catchment Area", "Service Radius", "Market Area", "Coverage Radius", "Draw Area"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "medium"
}),
(trading_area_households:Column {
    column_id: "COL_TRADING_AREA_HH_320",
    column_name: "TRADING_AREA_HOUSEHOLDS",
    table_id: "DIM_STORE",
    ordinal_position: 21,
    business_name: "Trading Area Households",
    business_description: "Number of households within primary trading area (typically 1-3 miles urban, 5-10 miles rural). Key metric for market penetration and share of wallet calculations. Updated with census data. Adjusted for competitive overlap.",
    business_synonyms: ["Area Households", "HH Count", "Catchment Households", "Market Households", "Service Area HH", "Local Households"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    business_criticality: "medium"
}),
(population_density:Column {
    column_id: "COL_POPULATION_DENSITY_321",
    column_name: "POPULATION_DENSITY",
    table_id: "DIM_STORE",
    ordinal_position: 22,
    business_name: "Population Density",
    business_description: "People per square mile in trading area. Affects format viability, delivery economics, and competitive intensity. Urban stores have very different dynamics than rural.",
    business_synonyms: ["Density", "Pop Density", "People per SqMi", "Area Density", "Population Concentration", "Demographic Density"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 2,
    business_criticality: "medium"
}),
(median_household_income:Column {
    column_id: "COL_MEDIAN_HH_INCOME_322",
    column_name: "MEDIAN_HOUSEHOLD_INCOME",
    table_id: "DIM_STORE",
    ordinal_position: 23,
    business_name: "Median Household Income",
    business_description: "Median household income for store trading area affecting price sensitivity and premium product potential. Critical for assortment and pricing decisions. Links to census and demographic data providers. Updated annually.",
    business_synonyms: ["Median Income", "HH Income", "Area Income", "Income Level", "Economic Level", "Household Income"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 2,
    business_criticality: "medium"
}),
(average_household_size:Column {
    column_id: "COL_AVERAGE_HH_SIZE_323",
    column_name: "AVERAGE_HOUSEHOLD_SIZE",
    table_id: "DIM_STORE",
    ordinal_position: 24,
    business_name: "Average Household Size",
    business_description: "Mean number of people per household in trading area. Affects package size preferences and family-oriented product performance. Important for volume merchandising decisions.",
    business_synonyms: ["HH Size", "Family Size", "Household Members", "People per HH", "Average Family Size", "HH Composition"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 4,
    scale: 2,
    business_criticality: "low"
}),
(college_educated_percent:Column {
    column_id: "COL_COLLEGE_EDUCATED_PCT_324",
    column_name: "COLLEGE_EDUCATED_PERCENT",
    table_id: "DIM_STORE",
    ordinal_position: 25,
    business_name: "College Educated Percentage",
    business_description: "Percentage of adults with college degree in trading area. Correlates with premium product acceptance and health-conscious purchasing. Important for sophisticated product lines.",
    business_synonyms: ["Education Level", "College Percent", "Educated Population", "Degree Holders", "Higher Education", "Education Rate"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "low"
}),
(hispanic_percent:Column {
    column_id: "COL_HISPANIC_PERCENT_325",
    column_name: "HISPANIC_PERCENT",
    table_id: "DIM_STORE",
    ordinal_position: 26,
    business_name: "Hispanic Population Percentage",
    business_description: "Percentage of Hispanic/Latino population in trading area. Critical for ethnic product assortment and bilingual packaging decisions. Growing demographic in many markets.",
    business_synonyms: ["Latino Percent", "Hispanic Population", "Latino Demographics", "Hispanic Composition", "Ethnic Mix", "Latino Share"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "medium"
}),
(age_median:Column {
    column_id: "COL_AGE_MEDIAN_326",
    column_name: "AGE_MEDIAN",
    table_id: "DIM_STORE",
    ordinal_position: 27,
    business_name: "Median Age",
    business_description: "Median age of population in trading area. Affects category priorities (baby vs senior products). Important for age-restricted products and generational marketing.",
    business_synonyms: ["Median Age", "Average Age", "Population Age", "Age Profile", "Demographic Age", "Area Age"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 4,
    scale: 1,
    business_criticality: "medium"
}),

// ========================================
// COMPETITIVE LANDSCAPE
// ========================================

(competitive_store_count:Column {
    column_id: "COL_COMPETITIVE_STORES_327",
    column_name: "COMPETITIVE_STORE_COUNT",
    table_id: "DIM_STORE",
    ordinal_position: 28,
    business_name: "Competitive Store Count",
    business_description: "Number of direct competitor stores within trading area. Affects pricing strategy, promotional intensity, and market share potential. Includes same format and alternative format competition. Critical for site selection.",
    business_synonyms: ["Competitor Count", "Competition Level", "Competitive Density", "Rival Stores", "Market Competition", "Competitive Presence"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    business_criticality: "medium"
}),
(nearest_competitor_distance:Column {
    column_id: "COL_NEAREST_COMP_DISTANCE_328",
    column_name: "NEAREST_COMPETITOR_DISTANCE",
    table_id: "DIM_STORE",
    ordinal_position: 29,
    business_name: "Nearest Competitor Distance Miles",
    business_description: "Distance to nearest direct format competitor in miles. Closer competition requires more aggressive pricing and differentiation. Affects customer loyalty and price checks.",
    business_synonyms: ["Competitor Distance", "Nearest Rival", "Competition Distance", "Closest Competitor", "Proximity to Competition"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 2,
    business_criticality: "medium"
}),
(market_share_index:Column {
    column_id: "COL_MARKET_SHARE_INDEX_329",
    column_name: "MARKET_SHARE_INDEX",
    table_id: "DIM_STORE",
    ordinal_position: 30,
    business_name: "Local Market Share Index",
    business_description: "Store's share of local market sales indexed to chain average (100=average). Values >100 indicate strong local position. Used for store potential assessment.",
    business_synonyms: ["Share Index", "Local Share", "Market Position", "Competitive Index", "Share Performance", "Market Strength"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 2,
    business_criticality: "medium"
}),

// ========================================
// OPERATIONAL CAPABILITIES
// ========================================

(store_manager:Column {
    column_id: "COL_STORE_MANAGER_330",
    column_name: "STORE_MANAGER",
    table_id: "DIM_STORE",
    ordinal_position: 31,
    business_name: "Store Manager Name",
    business_description: "Current store manager for relationship building and local execution. Key contact for vendor partners and promotional compliance. Changes tracked for continuity.",
    business_synonyms: ["Manager Name", "Store Lead", "General Manager", "Store Director", "Branch Manager", "Location Manager"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "low"
}),
(store_phone:Column {
    column_id: "COL_STORE_PHONE_331",
    column_name: "STORE_PHONE",
    table_id: "DIM_STORE",
    ordinal_position: 32,
    business_name: "Store Phone Number",
    business_description: "Main store phone number for customer and vendor communications. Used for delivery coordination and issue resolution. May include department extensions.",
    business_synonyms: ["Phone Number", "Contact Phone", "Store Contact", "Main Phone", "Business Phone", "Store Number"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "medium"
}),
(store_email:Column {
    column_id: "COL_STORE_EMAIL_332",
    column_name: "STORE_EMAIL",
    table_id: "DIM_STORE",
    ordinal_position: 33,
    business_name: "Store Email Address",
    business_description: "General store email for business communications. May be monitored centrally. Used for promotional notifications and operational updates.",
    business_synonyms: ["Email Address", "Store Email", "Contact Email", "Business Email", "Store Contact Email"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "low"
}),
(operating_hours:Column {
    column_id: "COL_OPERATING_HOURS_333",
    column_name: "OPERATING_HOURS",
    table_id: "DIM_STORE",
    ordinal_position: 34,
    business_name: "Operating Hours",
    business_description: "Standard store operating hours affecting labor scheduling and delivery windows. Extended hours correlate with higher sales. 24-hour stores have different dynamics.",
    business_synonyms: ["Store Hours", "Business Hours", "Open Hours", "Hours of Operation", "Trading Hours", "Opening Times"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 200,
    business_criticality: "medium"
}),
(days_open_per_week:Column {
    column_id: "COL_DAYS_OPEN_PER_WEEK_334",
    column_name: "DAYS_OPEN_PER_WEEK",
    table_id: "DIM_STORE",
    ordinal_position: 35,
    business_name: "Days Open Per Week",
    business_description: "Number of days store operates weekly. Less than 7 affects sales potential and inventory turnover. Some states restrict Sunday alcohol sales.",
    business_synonyms: ["Operating Days", "Weekly Days", "Days Open", "Business Days", "Trading Days", "Open Days"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    business_criticality: "medium"
}),

// ========================================
// STORE CLUSTERING AND PERFORMANCE
// ========================================

(store_cluster:Column {
    column_id: "COL_STORE_CLUSTER_335",
    column_name: "STORE_CLUSTER",
    table_id: "DIM_STORE",
    ordinal_position: 36,
    business_name: "Store Cluster",
    business_description: "Statistical clustering based on sales patterns, demographics, and competitive environment. Typically 6-10 clusters per retailer. Drives assortment decisions and test/control store selection. Updated annually with performance data.",
    business_synonyms: ["Cluster Group", "Store Group", "Cluster Assignment", "Store Segment", "Performance Cluster", "Store Type"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "high"
}),
(volume_tier:Column {
    column_id: "COL_VOLUME_TIER_336",
    column_name: "VOLUME_TIER",
    table_id: "DIM_STORE",
    ordinal_position: 37,
    business_name: "Volume Tier",
    business_description: "Sales volume classification (A/B/C/D) based on total store sales. 'A' stores get priority for new items and promotions. Affects service frequency and inventory levels.",
    business_synonyms: ["Sales Tier", "Volume Class", "Store Tier", "Performance Tier", "Sales Classification", "ABC Tier"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 10,
    business_criticality: "high"
}),
(comp_store_flag:Column {
    column_id: "COL_COMP_STORE_FLAG_337",
    column_name: "COMP_STORE_FLAG",
    table_id: "DIM_STORE",
    ordinal_position: 38,
    business_name: "Comparable Store Flag",
    business_description: "Indicates store qualifies for same-store sales comparisons (typically open 13+ months). Critical metric for Wall Street and management. Excludes relocated and remodeled stores temporarily.",
    business_synonyms: ["Comp Store", "Same Store", "Comparable Flag", "SSS Flag", "Comp Status", "Like-for-Like"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "high"
}),
(sales_per_sqft:Column {
    column_id: "COL_SALES_PER_SQFT_338",
    column_name: "SALES_PER_SQFT",
    table_id: "DIM_STORE",
    ordinal_position: 39,
    business_name: "Sales per Square Foot",
    business_description: "Annual sales divided by selling area indicating space productivity. Key metric for real estate decisions and remodel priorities. Varies significantly by format and location.",
    business_synonyms: ["Space Productivity", "Sales Density", "Revenue per SQFT", "Productivity Metric", "Space Performance", "$/SQFT"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 2,
    business_criticality: "high"
}),

// ========================================
// SYNDICATED DATA PARTICIPATION
// ========================================

(nielsen_universe_flag:Column {
    column_id: "COL_NIELSEN_UNIVERSE_FLAG_339",
    column_name: "NIELSEN_UNIVERSE_FLAG",
    table_id: "DIM_STORE",
    ordinal_position: 40,
    business_name: "Nielsen Universe Flag",
    business_description: "Indicates store participation in Nielsen's measured universe for syndicated data. Critical for understanding coverage gaps and projection accuracy. Affects market share calculations. May vary by category (food vs drug).",
    business_synonyms: ["Nielsen Coverage", "Nielsen Flag", "Syndicated Coverage", "Nielsen Measured", "Market Track Flag", "Nielsen Participating"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "high"
}),
(iri_universe_flag:Column {
    column_id: "COL_IRI_UNIVERSE_FLAG_340",
    column_name: "IRI_UNIVERSE_FLAG",
    table_id: "DIM_STORE",
    ordinal_position: 41,
    business_name: "IRI Universe Flag",
    business_description: "Store inclusion in IRI syndicated data universe. May differ from Nielsen coverage especially in drug and convenience channels. Important for data source selection and reconciliation. Updated annually with panel changes.",
    business_synonyms: ["IRI Coverage", "IRI Flag", "Symphony Coverage", "IRI Measured", "IRI Participating", "IRI Panel"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "high"
}),
(scan_data_available:Column {
    column_id: "COL_SCAN_DATA_AVAILABLE_341",
    column_name: "SCAN_DATA_AVAILABLE",
    table_id: "DIM_STORE",
    ordinal_position: 42,
    business_name: "Scan Data Available Flag",
    business_description: "Indicates availability of item-level POS data from retailer. Enables more accurate analysis than syndicated estimates. May require data fees or volume commitments.",
    business_synonyms: ["POS Data", "Scanner Data", "Scan Availability", "POS Available", "Direct Data", "Retailer Data"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "medium"
}),

// ========================================
// E-COMMERCE AND OMNICHANNEL
// ========================================

(ecommerce_enabled:Column {
    column_id: "COL_ECOMMERCE_ENABLED_342",
    column_name: "ECOMMERCE_ENABLED",
    table_id: "DIM_STORE",
    ordinal_position: 43,
    business_name: "E-commerce Enabled Flag",
    business_description: "Store participates in retailer's e-commerce platform for online ordering. May include home delivery and/or pickup options. Growing importance for omnichannel strategies.",
    business_synonyms: ["Online Enabled", "Digital Enabled", "E-comm Flag", "Web Enabled", "Digital Store", "Online Participating"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "high"
}),
(pickup_available:Column {
    column_id: "COL_PICKUP_AVAILABLE_343",
    column_name: "PICKUP_AVAILABLE",
    table_id: "DIM_STORE",
    ordinal_position: 44,
    business_name: "Pickup Available Flag",
    business_description: "Store offers buy-online-pickup-in-store (BOPIS) or curbside pickup. Requires dedicated staging area and trained staff. Key convenience factor driving online conversion.",
    business_synonyms: ["BOPIS", "Curbside Pickup", "Click & Collect", "Store Pickup", "Pickup Service", "Collection Available"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "high"
}),
(delivery_available:Column {
    column_id: "COL_DELIVERY_AVAILABLE_344",
    column_name: "DELIVERY_AVAILABLE",
    table_id: "DIM_STORE",
    ordinal_position: 45,
    business_name: "Delivery Available Flag",
    business_description: "Store offers home delivery service either directly or through third-party partners. May have radius and minimum order restrictions. Critical for competitive parity.",
    business_synonyms: ["Home Delivery", "Delivery Service", "Ship from Store", "Local Delivery", "Delivery Enabled", "Last Mile"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "high"
}),
(ship_from_store:Column {
    column_id: "COL_SHIP_FROM_STORE_345",
    column_name: "SHIP_FROM_STORE",
    table_id: "DIM_STORE",
    ordinal_position: 46,
    business_name: "Ship from Store Capability",
    business_description: "Store serves as mini fulfillment center for online orders beyond local area. Requires backroom setup and shipping integration. Improves inventory utilization.",
    business_synonyms: ["SFS", "Store Fulfillment", "Mini DC", "Store Shipping", "Fulfillment Node", "Ship Capability"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "medium"
}),
(digital_screens_count:Column {
    column_id: "COL_DIGITAL_SCREENS_COUNT_346",
    column_name: "DIGITAL_SCREENS_COUNT",
    table_id: "DIM_STORE",
    ordinal_position: 47,
    business_name: "Digital Screens Count",
    business_description: "Number of digital displays for dynamic pricing and promotions. Enables real-time updates and targeted messaging. Part of store modernization initiatives.",
    business_synonyms: ["Digital Displays", "Screen Count", "Digital Signage", "Electronic Displays", "Dynamic Screens", "Digital POS"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    business_criticality: "low"
}),

// ========================================
// DOMAIN-SPECIFIC LICENSING AND FEATURES
// ========================================

(liquor_license_flag:Column {
    column_id: "COL_LIQUOR_LICENSE_FLAG_347",
    column_name: "LIQUOR_LICENSE_FLAG",
    table_id: "DIM_STORE",
    ordinal_position: 48,
    business_name: "Liquor License Flag",
    business_description: "Indicates active license to sell alcoholic beverages. Varies by state (beer/wine only vs full liquor). Critical for distribution eligibility and compliance. Must be current for sales transactions. Affects product assortment.",
    business_synonyms: ["Alcohol License", "Liquor Permit", "Beer Wine License", "Alcohol Authorization", "Liquor Authority", "ABC License"],
    applicable_domains: ["alcoholic_beverages"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "critical",
    regulatory_relevance: ["TTB", "STATE_LIQUOR_BOARDS"]
}),
(liquor_license_type:Column {
    column_id: "COL_LIQUOR_LICENSE_TYPE_348",
    column_name: "LIQUOR_LICENSE_TYPE",
    table_id: "DIM_STORE",
    ordinal_position: 49,
    business_name: "Liquor License Type",
    business_description: "Specific type of alcohol license (Beer Only, Beer/Wine, Full Liquor, Sunday Sales). Determines eligible product range and selling hours. State-specific variations.",
    business_synonyms: ["License Type", "Alcohol Permit Type", "License Classification", "Liquor Authority Type", "ABC License Type"],
    applicable_domains: ["alcoholic_beverages"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high"
}),
(pharmacy_flag:Column {
    column_id: "COL_PHARMACY_FLAG_349",
    column_name: "PHARMACY_FLAG",
    table_id: "DIM_STORE",
    ordinal_position: 50,
    business_name: "Pharmacy Flag",
    business_description: "Indicates licensed pharmacy operations within store. Required for prescription drug sales and determines OTC placement. Affects product mix and margin structure. Must maintain DEA registration for controlled substances.",
    business_synonyms: ["Pharmacy Present", "Rx Department", "Pharmacy License", "Drug Store", "Prescription Service", "Pharmacy Operations"],
    applicable_domains: ["pharmaceuticals"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "critical",
    regulatory_relevance: ["FDA", "DEA", "STATE_BOARDS_PHARMACY"]
}),
(pharmacy_hours:Column {
    column_id: "COL_PHARMACY_HOURS_350",
    column_name: "PHARMACY_HOURS",
    table_id: "DIM_STORE",
    ordinal_position: 51,
    business_name: "Pharmacy Hours",
    business_description: "Pharmacy department operating hours, often different from store hours. Affects prescription pickup patterns and pharmacist scheduling. May include 24-hour service.",
    business_synonyms: ["Rx Hours", "Pharmacy Schedule", "Prescription Hours", "Pharmacy Operating Hours", "Rx Department Hours"],
    applicable_domains: ["pharmaceuticals"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 200,
    business_criticality: "medium"
}),
(pharmacy_drive_thru:Column {
    column_id: "COL_PHARMACY_DRIVE_THRU_351",
    column_name: "PHARMACY_DRIVE_THRU",
    table_id: "DIM_STORE",
    ordinal_position: 52,
    business_name: "Pharmacy Drive-Thru Flag",
    business_description: "Pharmacy offers drive-thru service for prescription pickup. Major convenience factor affecting store selection. Particularly important for mobility-impaired patients.",
    business_synonyms: ["Drive Thru Pharmacy", "Rx Drive Thru", "Drive Up Window", "Car Service", "Drive Through Rx"],
    applicable_domains: ["pharmaceuticals"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "medium"
}),
(gas_station_flag:Column {
    column_id: "COL_GAS_STATION_FLAG_352",
    column_name: "GAS_STATION_FLAG",
    table_id: "DIM_STORE",
    ordinal_position: 53,
    business_name: "Gas Station Flag",
    business_description: "Store has affiliated gas station/fuel center. Drives traffic and enables fuel discount programs. Affects convenience positioning and trip consolidation.",
    business_synonyms: ["Fuel Center", "Gas Pumps", "Fuel Station", "Petrol Station", "Gas Available", "Fuel Service"],
    applicable_domains: ["alcoholic_beverages", "battery", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "medium"
}),
(fresh_departments_flag:Column {
    column_id: "COL_FRESH_DEPARTMENTS_FLAG_353",
    column_name: "FRESH_DEPARTMENTS_FLAG",
    table_id: "DIM_STORE",
    ordinal_position: 54,
    business_name: "Fresh Departments Flag",
    business_description: "Store has fresh departments (produce, meat, deli, bakery). Indicates full-service supermarket vs limited assortment. Drives shopping frequency and basket size.",
    business_synonyms: ["Fresh Foods", "Perishables", "Fresh Offering", "Full Service", "Fresh Departments", "Service Departments"],
    applicable_domains: ["food_beverage"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "medium"
}),

// ========================================
// FINANCIAL AND LEASE INFORMATION
// ========================================

(annual_sales_estimate:Column {
    column_id: "COL_ANNUAL_SALES_ESTIMATE_354",
    column_name: "ANNUAL_SALES_ESTIMATE",
    table_id: "DIM_STORE",
    ordinal_position: 55,
    business_name: "Annual Sales Estimate",
    business_description: "Estimated total store annual sales across all departments. Used for market sizing and potential calculations. May be modeled for non-reporting retailers.",
    business_synonyms: ["Sales Estimate", "Annual Revenue", "Store Sales", "Revenue Estimate", "Annual Volume", "Sales Projection"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 2,
    business_criticality: "medium"
}),
(rent_type:Column {
    column_id: "COL_RENT_TYPE_355",
    column_name: "RENT_TYPE",
    table_id: "DIM_STORE",
    ordinal_position: 56,
    business_name: "Rent Type",
    business_description: "Store ownership structure (Owned, Leased, Franchised). Affects closure flexibility and capital requirements. Leased stores may have percentage rent clauses.",
    business_synonyms: ["Lease Type", "Ownership Type", "Property Type", "Real Estate Type", "Store Ownership", "Tenure Type"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "low"
}),
(lease_expiration_date:Column {
    column_id: "COL_LEASE_EXPIRATION_DATE_356",
    column_name: "LEASE_EXPIRATION_DATE",
    table_id: "DIM_STORE",
    ordinal_position: 57,
    business_name: "Lease Expiration Date",
    business_description: "Lease end date for rented locations. Critical for renewal negotiations and closure planning. Near-term expirations may affect long-term promotional commitments.",
    business_synonyms: ["Lease End Date", "Expiry Date", "Lease Term End", "Renewal Date", "Contract End Date", "Lease Maturity"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    business_criticality: "medium"
}),

// ========================================
// OPERATIONAL DATES AND STATUS
// ========================================

(store_open_date:Column {
    column_id: "COL_STORE_OPEN_DATE_357",
    column_name: "STORE_OPEN_DATE",
    table_id: "DIM_STORE",
    ordinal_position: 58,
    business_name: "Store Open Date",
    business_description: "Original store opening date for lifecycle analysis and comp store calculations. Stores typically excluded from comps for first 12-15 months. Remodels may reset comp eligibility. Critical for same-store sales analysis.",
    business_synonyms: ["Opening Date", "Grand Opening", "Launch Date", "Start Date", "Inception Date", "First Day"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    business_criticality: "medium"
}),
(last_remodel_date:Column {
    column_id: "COL_LAST_REMODEL_DATE_358",
    column_name: "LAST_REMODEL_DATE",
    table_id: "DIM_STORE",
    ordinal_position: 59,
    business_name: "Last Remodel Date",
    business_description: "Most recent major store renovation date. Remodels typically boost sales 5-15%. May temporarily exclude from comp store base. Indicates store investment level.",
    business_synonyms: ["Remodel Date", "Renovation Date", "Refresh Date", "Update Date", "Refurbishment Date", "Modernization Date"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    business_criticality: "low"
}),
(store_status:Column {
    column_id: "COL_STORE_STATUS_359",
    column_name: "STORE_STATUS",
    table_id: "DIM_STORE",
    ordinal_position: 60,
    business_name: "Store Status",
    business_description: "Current operational status (Open, Closed, Temporarily Closed, Under Construction). Affects all planning and execution. Temporary closures may be for remodels or disasters.",
    business_synonyms: ["Status", "Operational Status", "Store State", "Current Status", "Active Status", "Operating Status"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "critical"
}),
(closure_date:Column {
    column_id: "COL_CLOSURE_DATE_360",
    column_name: "CLOSURE_DATE",
    table_id: "DIM_STORE",
    ordinal_position: 61,
    business_name: "Store Closure Date",
    business_description: "Permanent closure date if applicable. Triggers inventory liquidation and customer transfer strategies. Historical stores retained for analysis.",
    business_synonyms: ["Close Date", "Shutdown Date", "Final Date", "End Date", "Termination Date", "Last Day"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    business_criticality: "medium"
}),

// ========================================
// METADATA AND GOVERNANCE
// ========================================

(district_number:Column {
    column_id: "COL_DISTRICT_NUMBER_361",
    column_name: "DISTRICT_NUMBER",
    table_id: "DIM_STORE",
    ordinal_position: 62,
    business_name: "District Number",
    business_description: "Retailer's district assignment for operational management. Typically 8-15 stores per district. District managers oversee execution and performance.",
    business_synonyms: ["District", "District ID", "District Code", "Management District", "Admin District", "District Assignment"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "medium"
}),
(region_name:Column {
    column_id: "COL_REGION_NAME_362",
    column_name: "REGION_NAME",
    table_id: "DIM_STORE",
    ordinal_position: 63,
    business_name: "Region Name",
    business_description: "Higher level geographic grouping above district. Aligns with sales territories and VP responsibilities. Used for regional programs and analysis.",
    business_synonyms: ["Region", "Regional Division", "Geographic Region", "Sales Region", "Territory", "Regional Group"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "medium"
}),
(division_name:Column {
    column_id: "COL_DIVISION_NAME_363",
    column_name: "DIVISION_NAME",
    table_id: "DIM_STORE",
    ordinal_position: 64,
    business_name: "Division Name",
    business_description: "Highest operational grouping below corporate. May align with acquired chains or geographic regions. Presidents typically manage divisions.",
    business_synonyms: ["Division", "Operating Division", "Business Division", "Corporate Division", "Divisional Group", "Operating Unit"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "medium"
}),
(created_timestamp:Column {
    column_id: "COL_CREATED_TIMESTAMP_364",
    column_name: "CREATED_TIMESTAMP",
    table_id: "DIM_STORE",
    ordinal_position: 65,
    business_name: "Record Created Timestamp",
    business_description: "System timestamp when store record was created. Used for audit trails and data lineage. Immutable after creation. Part of data governance.",
    business_synonyms: ["Creation Time", "Insert Timestamp", "Created Date", "Record Creation", "Entry Timestamp", "Initial Load Time"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    business_criticality: "low"
}),
(modified_timestamp:Column {
    column_id: "COL_MODIFIED_TIMESTAMP_365",
    column_name: "MODIFIED_TIMESTAMP",
    table_id: "DIM_STORE",
    ordinal_position: 66,
    business_name: "Last Modified Timestamp",
    business_description: "Most recent update timestamp for store record. Tracks data freshness and change frequency. Updated automatically with any field modification.",
    business_synonyms: ["Update Time", "Last Modified", "Change Timestamp", "Modified Date", "Last Updated", "Revision Timestamp"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    business_criticality: "low"
});

// ========================================
// CREATE TABLE-COLUMN RELATIONSHIPS
// ========================================

MATCH (t:Table {table_id: "DIM_STORE"})
MATCH (c:Column {table_id: "DIM_STORE"})
CREATE (t)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(c);

// ========================================
// VERIFICATION QUERIES
// ========================================

// Count total columns created
MATCH (c:Column {table_id: "DIM_STORE"})
RETURN count(c) AS total_columns;

// Verify columns by domain specificity
MATCH (c:Column {table_id: "DIM_STORE"})
RETURN c.domain_specific AS is_domain_specific, 
       count(c) AS column_count
ORDER BY is_domain_specific;

// List domain-specific columns with their applicable domains
MATCH (c:Column {table_id: "DIM_STORE"})
WHERE c.domain_specific = true
RETURN c.business_name AS column_name, 
       c.applicable_domains AS domains
ORDER BY c.ordinal_position;

// Verify critical columns
MATCH (c:Column {table_id: "DIM_STORE"})
WHERE c.business_criticality = "critical"
RETURN c.business_name AS critical_column, 
       c.business_criticality
ORDER BY c.ordinal_position;

// Check columns by category
MATCH (c:Column {table_id: "DIM_STORE"})
RETURN 
    CASE 
        WHEN c.ordinal_position <= 6 THEN "Core Identifiers"
        WHEN c.ordinal_position <= 11 THEN "Store Format & Characteristics"
        WHEN c.ordinal_position <= 19 THEN "Location & Geography"
        WHEN c.ordinal_position <= 27 THEN "Trading Area Demographics"
        WHEN c.ordinal_position <= 30 THEN "Competitive Landscape"
        WHEN c.ordinal_position <= 35 THEN "Operational Capabilities"
        WHEN c.ordinal_position <= 39 THEN "Clustering & Performance"
        WHEN c.ordinal_position <= 42 THEN "Syndicated Data"
        WHEN c.ordinal_position <= 47 THEN "E-commerce & Omnichannel"
        WHEN c.ordinal_position <= 54 THEN "Domain-Specific Features"
        WHEN c.ordinal_position <= 57 THEN "Financial & Lease"
        WHEN c.ordinal_position <= 61 THEN "Operational Status"
        ELSE "Metadata"
    END AS category,
    count(c) AS column_count
ORDER BY category;

// ========================================
// END OF DIM_STORE COLUMN CREATION
// ========================================