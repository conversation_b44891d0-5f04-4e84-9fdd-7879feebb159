// ========================================
// CREATE SEMANTIC SIMILARITY RELATIONSHIPS
// Execute after all tables and columns are created
// Links columns with similar business meaning across tables
// ========================================

// ========================================
// DATE KEY SIMILARITIES ACROSS ALL FACT TABLES
// ========================================

// Date Key in FACT_SALES to FACT_REGULATORY_COMPLIANCE
MATCH (ds:Column {table_id: "FACT_SALES", column_name: "DATE_KEY"})
MATCH (dr:Column {table_id: "FACT_REGULATORY_COMPLIANCE", column_name: "DATE_KEY"})
CREATE (ds)-[:SEMANTICALLY_SIMILAR {
    similarity_score: 0.98,
    similarity_type: "exact_match",
    similarity_basis: ["name", "format", "purpose", "data_type"],
    algorithm_used: "business_rule_based",
    confidence_level: 0.99,
    business_validation: true,
    validator: "<EMAIL>",
    validation_date: date("2025-01-15"),
    validation_notes: "Standard date key format used across all fact tables for consistent temporal analysis",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    auto_detected: false,
    detection_method: "manual_mapping"
}]->(dr);

// Date Key in FACT_SALES to FACT_PROMOTIONAL_SPEND
MATCH (ds:Column {table_id: "FACT_SALES", column_name: "DATE_KEY"})
MATCH (dp:Column {table_id: "FACT_PROMOTIONAL_SPEND", column_name: "DATE_KEY"})
CREATE (ds)-[:SEMANTICALLY_SIMILAR {
    similarity_score: 0.98,
    similarity_type: "exact_match",
    similarity_basis: ["name", "format", "purpose", "data_type"],
    business_validation: true,
    validation_notes: "Consistent date key enables integrated sales and promotional analysis",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dp);

// Date Key in FACT_SALES to FACT_INVENTORY
MATCH (ds:Column {table_id: "FACT_SALES", column_name: "DATE_KEY"})
MATCH (di:Column {table_id: "FACT_INVENTORY", column_name: "DATE_KEY"})
CREATE (ds)-[:SEMANTICALLY_SIMILAR {
    similarity_score: 0.98,
    similarity_type: "exact_match",
    similarity_basis: ["name", "format", "purpose", "data_type"],
    business_validation: true,
    validation_notes: "Unified date key enables sales-inventory correlation analysis",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(di);

// Date Key in FACT_SALES to FACT_PRICING
MATCH (ds:Column {table_id: "FACT_SALES", column_name: "DATE_KEY"})
MATCH (dpr:Column {table_id: "FACT_PRICING", column_name: "DATE_KEY"})
CREATE (ds)-[:SEMANTICALLY_SIMILAR {
    similarity_score: 0.98,
    similarity_type: "exact_match",
    similarity_basis: ["name", "format", "purpose", "data_type"],
    business_validation: true,
    validation_notes: "Common date key enables price-volume analysis across time",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dpr);

// ========================================
// STORE KEY SIMILARITIES ACROSS FACT TABLES
// ========================================

// Store Key in FACT_SALES to FACT_INVENTORY
MATCH (ss:Column {table_id: "FACT_SALES", column_name: "STORE_ID"})
MATCH (si:Column {table_id: "FACT_INVENTORY", column_name: "STORE_ID"})
CREATE (ss)-[:SEMANTICALLY_SIMILAR {
    similarity_score: 0.97,
    similarity_type: "exact_match",
    similarity_basis: ["identifier", "reference", "business_entity"],
    business_validation: true,
    validation_notes: "Store identifier enables location-based sales and inventory analysis",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(si);

// Store Key in FACT_SALES to FACT_PRICING
MATCH (ss:Column {table_id: "FACT_SALES", column_name: "STORE_ID"})
MATCH (sp:Column {table_id: "FACT_PRICING", column_name: "STORE_ID"})
CREATE (ss)-[:SEMANTICALLY_SIMILAR {
    similarity_score: 0.97,
    similarity_type: "exact_match",
    similarity_basis: ["identifier", "reference", "business_entity"],
    business_validation: true,
    validation_notes: "Consistent store key enables location-specific pricing analysis",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(sp);

// ========================================
// PRODUCT HIERARCHY KEY SIMILARITIES
// ========================================

// Product Hierarchy Key in FACT_SALES to FACT_INVENTORY
MATCH (ps:Column {table_id: "FACT_SALES", column_name: "PRODUCT_HIERARCHY_ID"})
MATCH (pi:Column {table_id: "FACT_INVENTORY", column_name: "PRODUCT_HIERARCHY_ID"})
CREATE (ps)-[:SEMANTICALLY_SIMILAR {
    similarity_score: 0.96,
    similarity_type: "exact_match",
    similarity_basis: ["name", "description", "business_purpose"],
    business_validation: true,
    validation_notes: "Universal product hierarchy key enables integrated product performance analysis",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(pi);

// Product Hierarchy Key in FACT_SALES to FACT_PRICING
MATCH (ps:Column {table_id: "FACT_SALES", column_name: "PRODUCT_HIERARCHY_ID"})
MATCH (pp:Column {table_id: "FACT_PRICING", column_name: "PRODUCT_HIERARCHY_ID"})
CREATE (ps)-[:SEMANTICALLY_SIMILAR {
    similarity_score: 0.96,
    similarity_type: "exact_match",
    similarity_basis: ["name", "description", "business_purpose"],
    business_validation: true,
    validation_notes: "Common product key enables price-volume elasticity analysis",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(pp);

// ========================================
// CUSTOMER KEY SIMILARITIES
// ========================================

// Customer Key in FACT_SALES to FACT_PRICING
MATCH (cs:Column {table_id: "FACT_SALES", column_name: "CUSTOMER_ID"})
MATCH (cp:Column {table_id: "FACT_PRICING", column_name: "CUSTOMER_ID"})
CREATE (cs)-[:SEMANTICALLY_SIMILAR {
    similarity_score: 0.95,
    similarity_type: "exact_match",
    similarity_basis: ["identifier", "reference", "business_entity"],
    business_validation: true,
    validation_notes: "Customer key enables customer-specific pricing and purchase analysis",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(cp);

// Customer Key in FACT_SALES to FACT_PROMOTIONAL_SPEND
MATCH (cs:Column {table_id: "FACT_SALES", column_name: "CUSTOMER_ID"})
MATCH (cps:Column {table_id: "FACT_PROMOTIONAL_SPEND", column_name: "CUSTOMER_ID"})
CREATE (cs)-[:SEMANTICALLY_SIMILAR {
    similarity_score: 0.95,
    similarity_type: "exact_match",
    similarity_basis: ["identifier", "reference", "business_entity"],
    business_validation: true,
    validation_notes: "Common customer key enables targeted promotion effectiveness analysis",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(cps);

// ========================================
// REVENUE/SALES AMOUNT SIMILARITIES
// ========================================

// Sales Revenue in FACT_SALES to Syndicated Dollars Sold in FACT_SYNDICATED_PANEL
MATCH (sr:Column {table_id: "FACT_SALES", column_name: "SALES_REVENUE"})
MATCH (sds:Column {table_id: "FACT_SYNDICATED_PANEL", column_name: "SYNDICATED_DOLLARS_SOLD"})
CREATE (sr)-[:SEMANTICALLY_SIMILAR {
    similarity_score: 0.88,
    similarity_type: "similar_concept",
    similarity_basis: ["business_purpose", "measurement_type", "aggregation_level"],
    business_validation: true,
    validation_notes: "Both measure dollar sales but at different aggregation levels - internal vs market view",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(sds);

// Sales Revenue to Incremental Revenue in Promotional Spend
MATCH (sr:Column {table_id: "FACT_SALES", column_name: "SALES_REVENUE"})
MATCH (ir:Column {table_id: "FACT_PROMOTIONAL_SPEND", column_name: "INCREMENTAL_REVENUE"})
CREATE (sr)-[:SEMANTICALLY_SIMILAR {
    similarity_score: 0.82,
    similarity_type: "related_concept",
    similarity_basis: ["measurement_unit", "business_metric"],
    business_validation: true,
    validation_notes: "Sales revenue includes incremental revenue from promotions",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(ir);

// ========================================
// UNIT/QUANTITY SIMILARITIES
// ========================================

// Sales Units in FACT_SALES to On Hand Quantity in FACT_INVENTORY
MATCH (su:Column {table_id: "FACT_SALES", column_name: "SALES_UNITS"})
MATCH (ohq:Column {table_id: "FACT_INVENTORY", column_name: "ON_HAND_QUANTITY"})
CREATE (su)-[:SEMANTICALLY_SIMILAR {
    similarity_score: 0.78,
    similarity_type: "related_concept",
    similarity_basis: ["measurement_unit", "product_tracking"],
    business_validation: true,
    validation_notes: "Sales units deplete on-hand inventory quantities",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(ohq);

// Sales Units to Syndicated Units Sold
MATCH (su:Column {table_id: "FACT_SALES", column_name: "SALES_UNITS"})
MATCH (sus:Column {table_id: "FACT_SYNDICATED_PANEL", column_name: "SYNDICATED_UNITS_SOLD"})
CREATE (su)-[:SEMANTICALLY_SIMILAR {
    similarity_score: 0.86,
    similarity_type: "similar_concept",
    similarity_basis: ["measurement_type", "business_purpose"],
    business_validation: true,
    validation_notes: "Both measure unit volume but from different data sources",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(sus);

// ========================================
// COST/MARGIN SIMILARITIES
// ========================================

// Unit Cost in FACT_SALES to Unit Cost in FACT_INVENTORY
MATCH (ucs:Column {table_id: "FACT_SALES", column_name: "UNIT_COST"})
MATCH (uci:Column {table_id: "FACT_INVENTORY", column_name: "UNIT_COST"})
CREATE (ucs)-[:SEMANTICALLY_SIMILAR {
    similarity_score: 0.94,
    similarity_type: "exact_match",
    similarity_basis: ["name", "calculation_method", "business_purpose"],
    business_validation: true,
    validation_notes: "Standard unit cost used for margin calculations and inventory valuation",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(uci);

// Gross Margin in FACT_SALES to Gross Margin in FACT_PRICING
MATCH (gms:Column {table_id: "FACT_SALES", column_name: "GROSS_MARGIN"})
MATCH (gmp:Column {table_id: "FACT_PRICING", column_name: "GROSS_MARGIN_AMOUNT"})
CREATE (gms)-[:SEMANTICALLY_SIMILAR {
    similarity_score: 0.92,
    similarity_type: "similar_concept",
    similarity_basis: ["calculation_method", "business_metric"],
    business_validation: true,
    validation_notes: "Both calculate gross margin but at different granularities",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(gmp);

// ========================================
// PROMOTIONAL SIMILARITIES
// ========================================

// Promotion Key in FACT_SALES to Promotion Key in FACT_PROMOTIONAL_SPEND
MATCH (pks:Column {table_id: "FACT_SALES", column_name: "PROMOTION_ID"})
MATCH (pkp:Column {table_id: "FACT_PROMOTIONAL_SPEND", column_name: "PROMOTION_ID"})
CREATE (pks)-[:SEMANTICALLY_SIMILAR {
    similarity_score: 0.97,
    similarity_type: "exact_match",
    similarity_basis: ["identifier", "reference", "business_entity"],
    business_validation: true,
    validation_notes: "Common promotion key links sales results to promotional investments",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(pkp);

// Discount Amount in FACT_SALES to Discount Amount in FACT_PRICING
MATCH (das:Column {table_id: "FACT_SALES", column_name: "DISCOUNT_AMOUNT"})
MATCH (dap:Column {table_id: "FACT_PRICING", column_name: "DISCOUNT_AMOUNT"})
CREATE (das)-[:SEMANTICALLY_SIMILAR {
    similarity_score: 0.93,
    similarity_type: "exact_match",
    similarity_basis: ["name", "calculation_method", "business_purpose"],
    business_validation: true,
    validation_notes: "Discount amounts tracked consistently across transactions and pricing",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dap);

// ========================================
// DOMAIN-SPECIFIC SIMILARITIES - ALCOHOLIC BEVERAGES
// ========================================

// Excise Tax in FACT_SALES to Excise Tax in FACT_PRICING
MATCH (ets:Column {table_id: "FACT_SALES", column_name: "EXCISE_TAX_AMOUNT"})
MATCH (etp:Column {table_id: "FACT_PRICING", column_name: "EXCISE_TAX_AMOUNT"})
CREATE (ets)-[:SEMANTICALLY_SIMILAR {
    similarity_score: 0.96,
    similarity_type: "exact_match",
    similarity_basis: ["name", "regulatory_requirement", "calculation_method"],
    business_validation: true,
    validation_notes: "Consistent excise tax tracking for alcoholic beverage compliance",
    applicable_domains: ["alcoholic_beverages"],
    regulatory_relevance: ["TTB", "STATE_TAX_AUTHORITY"]
}]->(etp);

// Bonded Quantity in FACT_INVENTORY to License Type in FACT_REGULATORY_COMPLIANCE
MATCH (bq:Column {table_id: "FACT_INVENTORY", column_name: "BONDED_INVENTORY_QUANTITY"})
MATCH (lt:Column {table_id: "FACT_REGULATORY_COMPLIANCE", column_name: "LICENSE_VIOLATION_TYPE"})
CREATE (bq)-[:SEMANTICALLY_SIMILAR {
    similarity_score: 0.72,
    similarity_type: "related_concept",
    similarity_basis: ["regulatory_domain", "compliance_tracking"],
    business_validation: true,
    validation_notes: "Both relate to alcoholic beverage regulatory compliance",
    applicable_domains: ["alcoholic_beverages"]
}]->(lt);

// ========================================
// DOMAIN-SPECIFIC SIMILARITIES - PHARMACEUTICALS
// ========================================

// Lot Number across tables
MATCH (lns:Column {table_id: "FACT_SALES", column_name: "LOT_NUMBER"})
MATCH (lni:Column {table_id: "FACT_INVENTORY", column_name: "LOT_NUMBER"})
CREATE (lns)-[:SEMANTICALLY_SIMILAR {
    similarity_score: 0.98,
    similarity_type: "exact_match",
    similarity_basis: ["name", "format", "regulatory_requirement"],
    business_validation: true,
    validation_notes: "Lot tracking required for pharmaceutical traceability",
    applicable_domains: ["pharmaceuticals", "food_beverage", "cosmetics"],
    regulatory_relevance: ["FDA", "EMA"]
}]->(lni);

// Controlled Substance Flag similarities
MATCH (csf:Column {table_id: "FACT_SALES", column_name: "CONTROLLED_SUBSTANCE_FLAG"})
MATCH (csi:Column {table_id: "FACT_INVENTORY", column_name: "CONTROLLED_SUBSTANCE_FLAG"})
CREATE (csf)-[:SEMANTICALLY_SIMILAR {
    similarity_score: 0.97,
    similarity_type: "exact_match",
    similarity_basis: ["name", "regulatory_requirement", "business_purpose"],
    business_validation: true,
    validation_notes: "DEA controlled substance tracking across sales and inventory",
    applicable_domains: ["pharmaceuticals"],
    regulatory_relevance: ["DEA", "FDA"]
}]->(csi);

// ========================================
// MARKET SHARE AND COMPETITIVE SIMILARITIES
// ========================================

// Market Share Units to Market Share Dollars
MATCH (msu:Column {table_id: "FACT_SYNDICATED_PANEL", column_name: "MARKET_SHARE_UNITS"})
MATCH (msd:Column {table_id: "FACT_SYNDICATED_PANEL", column_name: "MARKET_SHARE_DOLLARS"})
CREATE (msu)-[:SEMANTICALLY_SIMILAR {
    similarity_score: 0.89,
    similarity_type: "related_concept",
    similarity_basis: ["measurement_type", "competitive_metric"],
    business_validation: true,
    validation_notes: "Unit and dollar share provide complementary competitive views",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(msd);

// Competitor Price to Market Price Average
MATCH (cp:Column {table_id: "FACT_PRICING", column_name: "COMPETITOR_PRICE"})
MATCH (mpa:Column {table_id: "FACT_PRICING", column_name: "MARKET_PRICE_AVERAGE"})
CREATE (cp)-[:SEMANTICALLY_SIMILAR {
    similarity_score: 0.84,
    similarity_type: "related_concept",
    similarity_basis: ["price_tracking", "competitive_intelligence"],
    business_validation: true,
    validation_notes: "Individual competitor prices aggregate to market average",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(mpa);

// ========================================
// COMPLIANCE AND QUALITY SIMILARITIES
// ========================================

// Quality Hold Quantity to Quarantine Quantity
MATCH (qhq:Column {table_id: "FACT_INVENTORY", column_name: "QUALITY_HOLD_QUANTITY"})
MATCH (qq:Column {table_id: "FACT_INVENTORY", column_name: "QUARANTINE_QUANTITY"})
CREATE (qhq)-[:SEMANTICALLY_SIMILAR {
    similarity_score: 0.86,
    similarity_type: "related_concept",
    similarity_basis: ["inventory_status", "quality_management"],
    business_validation: true,
    validation_notes: "Both represent inventory unavailable due to quality concerns",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(qq);

// Violation Count to Critical Violation Count
MATCH (vc:Column {table_id: "FACT_REGULATORY_COMPLIANCE", column_name: "VIOLATION_COUNT"})
MATCH (cvc:Column {table_id: "FACT_REGULATORY_COMPLIANCE", column_name: "CRITICAL_VIOLATION_COUNT"})
CREATE (vc)-[:SEMANTICALLY_SIMILAR {
    similarity_score: 0.91,
    similarity_type: "hierarchical_relationship",
    similarity_basis: ["subset_relationship", "severity_classification"],
    business_validation: true,
    validation_notes: "Critical violations are a subset of total violations",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(cvc);

// ========================================
// TIME-BASED SIMILARITIES
// ========================================

// Days Until Expiration similarities across tables
MATCH (due_inv:Column {table_id: "FACT_INVENTORY", column_name: "DAYS_UNTIL_EXPIRATION"})
MATCH (due_reg:Column {table_id: "FACT_REGULATORY_COMPLIANCE", column_name: "CERTIFICATE_VALIDITY_DAYS"})
CREATE (due_inv)-[:SEMANTICALLY_SIMILAR {
    similarity_score: 0.75,
    similarity_type: "similar_concept",
    similarity_basis: ["time_measurement", "expiration_tracking"],
    business_validation: true,
    validation_notes: "Both track days until expiration - products vs certificates",
    applicable_domains: ["pharmaceuticals", "food_beverage", "cosmetics"]
}]->(due_reg);

// Days at Current Price to Days On Hand
MATCH (dacp:Column {table_id: "FACT_PRICING", column_name: "DAYS_AT_CURRENT_PRICE"})
MATCH (doh:Column {table_id: "FACT_INVENTORY", column_name: "DAYS_ON_HAND"})
CREATE (dacp)-[:SEMANTICALLY_SIMILAR {
    similarity_score: 0.73,
    similarity_type: "similar_measurement",
    similarity_basis: ["time_duration", "business_metric"],
    business_validation: true,
    validation_notes: "Both measure duration in days for different business purposes",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(doh);

// ========================================
// PERFORMANCE SCORE SIMILARITIES
// ========================================

// Various Score Columns
MATCH (cs:Column {table_id: "FACT_REGULATORY_COMPLIANCE", column_name: "COMPLIANCE_SCORE"})
MATCH (vs:Column {table_id: "FACT_INVENTORY", column_name: "VELOCITY_SCORE"})
CREATE (cs)-[:SEMANTICALLY_SIMILAR {
    similarity_score: 0.71,
    similarity_type: "similar_metric_type",
    similarity_basis: ["scoring_methodology", "0_100_scale"],
    business_validation: true,
    validation_notes: "Both use 0-100 scoring for performance measurement",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(vs);

// ========================================
// FINANCIAL METRIC SIMILARITIES
// ========================================

// Total Compliance Cost to Total Promotional Spend
MATCH (tcc:Column {table_id: "FACT_REGULATORY_COMPLIANCE", column_name: "TOTAL_COMPLIANCE_COST"})
MATCH (gps:Column {table_id: "FACT_PROMOTIONAL_SPEND", column_name: "GROSS_PROMOTIONAL_SPEND"})
CREATE (tcc)-[:SEMANTICALLY_SIMILAR {
    similarity_score: 0.74,
    similarity_type: "similar_metric_type",
    similarity_basis: ["cost_tracking", "business_expense"],
    business_validation: true,
    validation_notes: "Both track business costs but for different purposes",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(gps);

// ========================================
// E-COMMERCE SPECIFIC SIMILARITIES
// ========================================

// Digital Channel Flag to E-commerce Order Count
MATCH (dcf:Column {table_id: "FACT_SALES", column_name: "DIGITAL_CHANNEL_FLAG"})
MATCH (eoc:Column {table_id: "FACT_ECOMMERCE_SALES", column_name: "ORDER_ID"})
CREATE (dcf)-[:SEMANTICALLY_SIMILAR {
    similarity_score: 0.79,
    similarity_type: "related_concept",
    similarity_basis: ["channel_identification", "digital_commerce"],
    business_validation: true,
    validation_notes: "Digital channel flag in sales indicates e-commerce transactions",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(eoc);

// ========================================
// VALIDATION QUERIES
// ========================================

// Count semantic relationships by similarity type
// MATCH ()-[r:SEMANTICALLY_SIMILAR]->()
// RETURN r.similarity_type as type, COUNT(r) as count
// ORDER BY count DESC;

// Find columns with multiple semantic relationships
// MATCH (c:Column)-[r:SEMANTICALLY_SIMILAR]-()
// WITH c, COUNT(r) as rel_count
// WHERE rel_count > 1
// RETURN c.table_id, c.column_name, rel_count
// ORDER BY rel_count DESC;

// List high-confidence semantic relationships
// MATCH (c1:Column)-[r:SEMANTICALLY_SIMILAR]->(c2:Column)
// WHERE r.similarity_score >= 0.90
// RETURN c1.table_id + "." + c1.column_name as column1,
//        c2.table_id + "." + c2.column_name as column2,
//        r.similarity_score,
//        r.validation_notes
// ORDER BY r.similarity_score DESC;

// ========================================
// END OF SEMANTIC SIMILARITY RELATIONSHIPS
// ========================================