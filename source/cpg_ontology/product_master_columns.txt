// ========================================
// DIM_PRODUCT_MASTER - COMPREHENSIVE COLUMN CREATION
// Complete product dimension columns for all CPG domains
// ========================================

// Clear existing columns for DIM_PRODUCT_MASTER (optional)
MATCH (c:Column {table_id: "DIM_PRODUCT_MASTER"}) DETACH DELETE c;

// ========================================
// CORE PRODUCT IDENTIFIERS AND ATTRIBUTES
// ========================================

CREATE 
(product_id:Column {
    column_id: "COL_PRODUCT_ID_001",
    column_name: "PRODUCT_ID",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 1,
    business_name: "Product ID",
    business_description: "Universal unique identifier for each product SKU across all CPG domains. System-generated surrogate key ensuring uniqueness across mergers, acquisitions, and system integrations. Immutable once assigned, surviving product transitions and regulatory changes. Critical for maintaining historical continuity in analytics.",
    business_synonyms: ["SKU ID", "Item ID", "Product Key", "Material Number", "Article Number", "Product Code", "Item Number", "SKU Key", "Product Identifier", "Universal Product ID"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 25,
    is_primary_key: true,
    business_criticality: "critical"
}),
(product_name:Column {
    column_id: "COL_PRODUCT_NAME_002",
    column_name: "PRODUCT_NAME",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 2,
    business_name: "Product Name",
    business_description: "Consumer-facing product name as it appears on packaging and in marketing materials. Must comply with regulatory naming conventions per domain (FDA, TTB, CPSC). Includes brand name, sub-brand, variant, and size. Used in all consumer communications and POS displays.",
    business_synonyms: ["Item Description", "Product Description", "Article Name", "Product Title", "SKU Description", "Item Name", "Product Label", "Marketing Name", "Consumer Name"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 500,
    business_criticality: "critical"
}),
(upc_code:Column {
    column_id: "COL_UPC_CODE_003",
    column_name: "UPC_CODE",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 3,
    business_name: "UPC Code",
    business_description: "12-digit Universal Product Code for retail scanning and inventory management. Must be GS1 compliant and unique per consumer selling unit. Critical for POS systems, e-commerce listings, and syndicated data matching. Includes check digit validation.",
    business_synonyms: ["Barcode", "GTIN-12", "UPC-A", "Scan Code", "Product Barcode", "Retail Code", "Scanner Code", "12-Digit Code", "POS Code"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 12,
    business_criticality: "critical"
}),
(ean_code:Column {
    column_id: "COL_EAN_CODE_004",
    column_name: "EAN_CODE",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 4,
    business_name: "EAN Code",
    business_description: "13-digit European Article Number for international product identification. Required for global markets outside North America. Enables cross-border e-commerce and international supply chain tracking. Interoperable with UPC through GTIN standards.",
    business_synonyms: ["EAN-13", "GTIN-13", "International Article Number", "European Barcode", "Global Trade Item Number", "International Product Code", "13-Digit Barcode"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 13,
    business_criticality: "high"
}),
(gtin_14:Column {
    column_id: "COL_GTIN_14_005",
    column_name: "GTIN_14",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 5,
    business_name: "GTIN-14 Case Code",
    business_description: "14-digit Global Trade Item Number for case/carton level identification. Essential for B2B transactions, warehouse management, and supply chain visibility. Links consumer units to shipping units. Includes packaging level indicator.",
    business_synonyms: ["Case Code", "Carton Code", "SCC-14", "Shipping Container Code", "ITF-14", "Case GTIN", "Master Carton Code", "Distribution Unit Code"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 14,
    business_criticality: "high"
}),
(brand_id:Column {
    column_id: "COL_BRAND_ID_006",
    column_name: "BRAND_ID",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 6,
    business_name: "Brand ID",
    business_description: "Foreign key to brand dimension enabling brand-level analytics and portfolio management. Tracks brand ownership changes through M&A activity. Critical for brand performance measurement, loyalty analysis, and competitive intelligence.",
    business_synonyms: ["Brand Key", "Brand Code", "Brand Reference", "Brand Identifier", "Parent Brand ID", "Master Brand Code", "Brand Number"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 20,
    is_foreign_key: true,
    business_criticality: "critical"
}),
(sub_brand:Column {
    column_id: "COL_SUB_BRAND_007",
    column_name: "SUB_BRAND",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 7,
    business_name: "Sub-Brand",
    business_description: "Secondary brand designation for product lines, flavor platforms, or regional variants. Enables sub-brand performance tracking and cannibalization analysis. Important for innovation tracking and portfolio optimization within master brands.",
    business_synonyms: ["Product Line", "Brand Variant", "Flavor Platform", "Sub-Brand Name", "Product Family", "Brand Extension", "Line Extension"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "medium"
}),
(category_code:Column {
    column_id: "COL_CATEGORY_CODE_008",
    column_name: "CATEGORY_CODE",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 8,
    business_name: "Category Code",
    business_description: "Primary product category classification driving merchandising, space planning, and buyer responsibility. Aligns with retailer category management structures and syndicated data classifications. Critical for category performance analysis and shelf placement.",
    business_synonyms: ["Product Category", "Category ID", "Department Code", "Product Class", "Merchandise Category", "Category Number", "Product Group Code"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "high"
}),
(subcategory_code:Column {
    column_id: "COL_SUBCATEGORY_CODE_009",
    column_name: "SUBCATEGORY_CODE",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 9,
    business_name: "Subcategory Code",
    business_description: "Detailed product classification within category enabling granular analysis and micro-merchandising. Supports targeted promotions, assortment optimization, and competitive benchmarking at segment level.",
    business_synonyms: ["Sub-Category", "Product Subcategory", "Class Code", "Segment Code", "Sub-Class", "Product Segment", "Detailed Category"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "medium"
}),
(package_size:Column {
    column_id: "COL_PACKAGE_SIZE_010",
    column_name: "PACKAGE_SIZE",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 10,
    business_name: "Package Size",
    business_description: "Consumer selling unit size with unit of measure (oz, ml, count, etc.). Critical for price comparisons, value calculations, and regulatory compliance. Must match package declarations and serving size regulations.",
    business_synonyms: ["Pack Size", "Unit Size", "Package Quantity", "Selling Unit Size", "Consumer Unit Size", "Product Size", "Net Content"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high"
}),
(package_size_numeric:Column {
    column_id: "COL_PACKAGE_SIZE_NUMERIC_011",
    column_name: "PACKAGE_SIZE_NUMERIC",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 11,
    business_name: "Package Size Numeric",
    business_description: "Numeric portion of package size enabling mathematical calculations for unit pricing, shipping calculations, and size optimization analysis. Standardized to consistent unit of measure within category.",
    business_synonyms: ["Size Value", "Numeric Size", "Size Quantity", "Package Amount", "Unit Amount", "Size Number"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 3,
    business_criticality: "high"
}),
(package_size_uom:Column {
    column_id: "COL_PACKAGE_SIZE_UOM_012",
    column_name: "PACKAGE_SIZE_UOM",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 12,
    business_name: "Package Size Unit of Measure",
    business_description: "Standardized unit of measure for package size (OZ, ML, LB, KG, CT, EA). Must comply with regulatory standards and enable cross-product comparisons. Critical for unit price calculations and inventory management.",
    business_synonyms: ["Size UOM", "Unit Type", "Measure Unit", "Size Unit", "UOM", "Package Unit", "Measurement Type"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 10,
    business_criticality: "high"
}),
(product_weight:Column {
    column_id: "COL_PRODUCT_WEIGHT_013",
    column_name: "PRODUCT_WEIGHT",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 13,
    business_name: "Product Weight",
    business_description: "Gross weight of individual selling unit including packaging. Critical for shipping calculations, freight planning, and e-commerce delivery options. Affects distribution costs and carbon footprint calculations.",
    business_synonyms: ["Unit Weight", "Gross Weight", "Shipping Weight", "Product Mass", "Item Weight", "Package Weight"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 3,
    business_criticality: "medium"
}),
(product_dimensions:Column {
    column_id: "COL_PRODUCT_DIMENSIONS_014",
    column_name: "PRODUCT_DIMENSIONS",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 14,
    business_name: "Product Dimensions",
    business_description: "Physical dimensions (LxWxH) of selling unit for shelf space planning, shipping optimization, and e-commerce packaging. Critical for planogram development and cubic volume calculations.",
    business_synonyms: ["Package Dimensions", "Product Size Dimensions", "Physical Dimensions", "Box Dimensions", "Unit Dimensions", "Package LWH"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "medium"
}),
(fda_ndc_number:Column {
    column_id: "COL_FDA_NDC_NUMBER_015",
    column_name: "FDA_NDC_NUMBER",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 15,
    business_name: "FDA NDC Number",
    business_description: "National Drug Code for pharmaceutical products. Required for prescription and OTC drugs. Format: labeler-product-package (5-4-2 or 5-3-2). Critical for pharmacy systems, insurance claims, and regulatory reporting.",
    business_synonyms: ["NDC", "National Drug Code", "Drug Code", "Pharma Code", "FDA Drug Number", "NDC11", "Medication Code"],
    applicable_domains: ["pharmaceuticals"],
    domain_specific: true,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 11,
    business_criticality: "critical"
}),
(ttb_cola_number:Column {
    column_id: "COL_TTB_COLA_NUMBER_016",
    column_name: "TTB_COLA_NUMBER",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 16,
    business_name: "TTB COLA Number",
    business_description: "Certificate of Label Approval from TTB for alcoholic beverages. Required before interstate commerce. Links to approved label image and formula. Must be current for legal sale. Format: YYYY/NNNNN.",
    business_synonyms: ["COLA Number", "Label Approval Number", "TTB Certificate", "Alcohol Label Number", "COLA ID", "TTB Approval"],
    applicable_domains: ["alcoholic_beverages"],
    domain_specific: true,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "critical"
}),
(cpsc_certificate:Column {
    column_id: "COL_CPSC_CERTIFICATE_017",
    column_name: "CPSC_CERTIFICATE",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 17,
    business_name: "CPSC Certificate Number",
    business_description: "Consumer Product Safety Commission certification for toys and children's products. Mandatory for products intended for children 12 and under. Links to testing reports and compliance documentation.",
    business_synonyms: ["Safety Certificate", "CPSC Cert", "Toy Safety Number", "Children's Product Certificate", "CPC Number", "Safety Compliance ID"],
    applicable_domains: ["toys"],
    domain_specific: true,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "critical"
}),
(fda_registration:Column {
    column_id: "COL_FDA_REGISTRATION_018",
    column_name: "FDA_REGISTRATION",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 18,
    business_name: "FDA Registration Number",
    business_description: "FDA facility registration for food, cosmetics, and dietary supplements. Links to manufacturing facility compliance. Required for import/export. Renewed biennially.",
    business_synonyms: ["FDA Reg Number", "Facility Registration", "FDA Facility ID", "Food Facility Number", "Cosmetic Registration"],
    applicable_domains: ["food_beverage", "cosmetics"],
    domain_specific: true,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "high"
}),
(product_status:Column {
    column_id: "COL_PRODUCT_STATUS_019",
    column_name: "PRODUCT_STATUS",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 19,
    business_name: "Product Status",
    business_description: "Current lifecycle status determining availability for ordering and promotion. Values include Active, Discontinued, Seasonal, Limited Edition, Phase Out. Drives inventory strategies and markdown timing.",
    business_synonyms: ["Item Status", "Product State", "Lifecycle Status", "SKU Status", "Product Lifecycle Stage", "Status Code", "Active Flag"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "critical"
}),
(launch_date:Column {
    column_id: "COL_LAUNCH_DATE_020",
    column_name: "LAUNCH_DATE",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 20,
    business_name: "Product Launch Date",
    business_description: "Official market introduction date for new product tracking and innovation metrics. Critical for new item velocity analysis, launch effectiveness measurement, and innovation pipeline reporting.",
    business_synonyms: ["Introduction Date", "Market Launch", "First Ship Date", "Release Date", "Go-Live Date", "New Item Date", "Product Start Date"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    business_criticality: "high"
});

// Continue with additional columns in separate CREATE statements to avoid syntax issues
CREATE 
(discontinue_date:Column {
    column_id: "COL_DISCONTINUE_DATE_021",
    column_name: "DISCONTINUE_DATE",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 21,
    business_name: "Discontinue Date",
    business_description: "Planned or actual product discontinuation date triggering inventory depletion strategies, markdown processes, and substitute product communications. Critical for working capital management.",
    business_synonyms: ["End Date", "Termination Date", "Phase Out Date", "Delisting Date", "Product End Date", "Retirement Date", "Sunset Date"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    business_criticality: "medium"
}),
(manufacturer_list_price:Column {
    column_id: "COL_MANUFACTURER_LIST_PRICE_022",
    column_name: "MANUFACTURER_LIST_PRICE",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 22,
    business_name: "Manufacturer's Suggested Retail Price",
    business_description: "MSRP serving as pricing anchor for discount calculations and MAP policy enforcement. Used in promotional communications and competitive price indexing. May vary by channel or region.",
    business_synonyms: ["MSRP", "List Price", "Suggested Retail", "SRP", "Recommended Price", "Catalog Price", "Base Retail Price"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 2,
    business_criticality: "high"
}),
(wholesale_price:Column {
    column_id: "COL_WHOLESALE_PRICE_023",
    column_name: "WHOLESALE_PRICE",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 23,
    business_name: "Wholesale Price",
    business_description: "Standard distributor/retailer acquisition price before deals and allowances. Base for margin calculations and deal discount measurements. Critical for profitability analysis and price negotiations.",
    business_synonyms: ["Trade Price", "Distributor Price", "Dealer Cost", "Base Wholesale", "Invoice Price", "Trade Cost", "B2B Price"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 2,
    business_criticality: "high"
}),
(unit_cost:Column {
    column_id: "COL_UNIT_COST_024",
    column_name: "UNIT_COST",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 24,
    business_name: "Unit Cost",
    business_description: "Fully loaded product cost including materials, labor, and overhead allocation. Foundation for gross margin analysis and profitability reporting. Updated with standard cost revisions.",
    business_synonyms: ["Standard Cost", "Product Cost", "COGS", "Unit COGS", "Manufacturing Cost", "Full Cost", "Landed Cost"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 4,
    business_criticality: "critical"
}),
(manufacturer_id:Column {
    column_id: "COL_MANUFACTURER_ID_025",
    column_name: "MANUFACTURER_ID",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 25,
    business_name: "Manufacturer ID",
    business_description: "Link to manufacturer/supplier dimension for vendor analytics, quality tracking, and supply chain risk management. Enables vendor scorecarding and concentration analysis.",
    business_synonyms: ["Supplier ID", "Vendor ID", "Producer ID", "Manufacturer Code", "Supplier Number", "Vendor Code", "Source ID"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 20,
    is_foreign_key: true,
    business_criticality: "high"
}),
(country_of_origin:Column {
    column_id: "COL_COUNTRY_OF_ORIGIN_026",
    column_name: "COUNTRY_OF_ORIGIN",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 26,
    business_name: "Country of Origin",
    business_description: "Manufacturing country for customs, duties, and country-of-origin labeling requirements. Impacts tariffs, trade agreements, and consumer preferences. Must comply with FTC marking requirements.",
    business_synonyms: ["COO", "Origin Country", "Made In Country", "Manufacturing Country", "Source Country", "Production Country"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "high"
}),
(lead_time_days:Column {
    column_id: "COL_LEAD_TIME_DAYS_027",
    column_name: "LEAD_TIME_DAYS",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 27,
    business_name: "Lead Time Days",
    business_description: "Standard procurement lead time from PO to receipt. Critical for inventory planning, reorder point calculations, and out-of-stock prevention. Varies by source location and transportation mode.",
    business_synonyms: ["Order Lead Time", "Procurement Time", "Delivery Time", "Supply Lead Time", "Replenishment Time", "Order Cycle Time"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    business_criticality: "high"
}),
(minimum_order_quantity:Column {
    column_id: "COL_MINIMUM_ORDER_QTY_028",
    column_name: "MINIMUM_ORDER_QUANTITY",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 28,
    business_name: "Minimum Order Quantity",
    business_description: "Supplier-imposed minimum order size in selling units. Affects inventory investment, storage requirements, and product freshness. Critical for new items and slow movers.",
    business_synonyms: ["MOQ", "Min Order", "Minimum Purchase", "Order Minimum", "Min Qty", "Supplier Minimum", "Batch Size"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    business_criticality: "medium"
}),
(case_pack_quantity:Column {
    column_id: "COL_CASE_PACK_QTY_029",
    column_name: "CASE_PACK_QUANTITY",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 29,
    business_name: "Case Pack Quantity",
    business_description: "Number of selling units per case/carton. Critical for order quantity calculations, warehouse slotting, and retail replenishment. Affects picking efficiency and transportation costs.",
    business_synonyms: ["Units Per Case", "Case Qty", "Pack Quantity", "Carton Pack", "Master Pack", "Case Count", "Inner Pack"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    business_criticality: "high"
}),
(flavor_variant:Column {
    column_id: "COL_FLAVOR_VARIANT_030",
    column_name: "FLAVOR_VARIANT",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 30,
    business_name: "Flavor Variant",
    business_description: "Specific flavor, scent, or variant within product line. Critical for assortment decisions, velocity comparisons, and new flavor performance tracking. Major driver of consumer choice.",
    business_synonyms: ["Flavor", "Variant", "Product Variant", "Taste Profile", "Scent", "Product Flavor", "Variety", "SKU Variant"],
    applicable_domains: ["alcoholic_beverages", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "medium"
}),
(color_variant:Column {
    column_id: "COL_COLOR_VARIANT_031",
    column_name: "COLOR_VARIANT",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 31,
    business_name: "Color Variant",
    business_description: "Product color specification important for cosmetics, toys, and batteries. Affects consumer preference, inventory complexity, and markdown risks. Critical for fashion-oriented categories.",
    business_synonyms: ["Color", "Product Color", "Color Option", "Shade", "Color Code", "Color Name", "Variant Color"],
    applicable_domains: ["battery", "toys", "cosmetics"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "medium"
}),
(package_type:Column {
    column_id: "COL_PACKAGE_TYPE_032",
    column_name: "PACKAGE_TYPE",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 32,
    business_name: "Package Type",
    business_description: "Primary packaging format (bottle, can, box, blister, tube, etc.). Affects shelf presentation, sustainability metrics, and consumer convenience. Critical for e-commerce shipping decisions.",
    business_synonyms: ["Packaging Type", "Container Type", "Package Format", "Pack Type", "Packaging Format", "Container Style"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "medium"
}),
(material_composition:Column {
    column_id: "COL_MATERIAL_COMPOSITION_033",
    column_name: "MATERIAL_COMPOSITION",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 33,
    business_name: "Material Composition",
    business_description: "Primary materials used in product and packaging for sustainability reporting, recycling programs, and regulatory compliance. Critical for environmental impact assessments and circular economy initiatives.",
    business_synonyms: ["Materials", "Composition", "Product Materials", "Package Materials", "Material Type", "Substrate", "Material Content"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "TEXT",
    business_criticality: "medium"
}),
(ingredients_list:Column {
    column_id: "COL_INGREDIENTS_LIST_034",
    column_name: "INGREDIENTS_LIST",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 34,
    business_name: "Ingredients List",
    business_description: "Complete ingredient declaration as required by FDA/USDA regulations. Must be in descending order by weight. Critical for allergen management, clean label claims, and regulatory compliance.",
    business_synonyms: ["Ingredient Declaration", "Components", "Formula Ingredients", "Product Ingredients", "Ingredient Statement", "Contents"],
    applicable_domains: ["food_beverage", "cosmetics", "pharmaceuticals"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "TEXT",
    business_criticality: "critical"
}),
(allergen_statement:Column {
    column_id: "COL_ALLERGEN_STATEMENT_035",
    column_name: "ALLERGEN_STATEMENT",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 35,
    business_name: "Allergen Statement",
    business_description: "FDA-required allergen declarations including major allergens and cross-contamination warnings. Critical for consumer safety, regulatory compliance, and e-commerce product data. Must be prominently displayed.",
    business_synonyms: ["Allergen Info", "Allergen Declaration", "Contains Statement", "Allergen Warning", "Allergy Information", "Allergen Alert"],
    applicable_domains: ["food_beverage", "cosmetics"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "TEXT",
    business_criticality: "critical"
}),
(nutrition_facts_panel:Column {
    column_id: "COL_NUTRITION_FACTS_036",
    column_name: "NUTRITION_FACTS_PANEL",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 36,
    business_name: "Nutrition Facts Panel",
    business_description: "Structured nutritional information per FDA format including calories, macronutrients, vitamins, and minerals. Required for most food products. Enables nutrition-based consumer targeting and health claims.",
    business_synonyms: ["Nutrition Label", "Nutrition Info", "Nutritional Content", "Nutrition Panel", "Nutrition Data", "Nutritional Values"],
    applicable_domains: ["food_beverage"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "JSON",
    business_criticality: "high"
}),
(shelf_life_days:Column {
    column_id: "COL_SHELF_LIFE_DAYS_037",
    column_name: "SHELF_LIFE_DAYS",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 37,
    business_name: "Shelf Life Days",
    business_description: "Total shelf life from manufacture to expiration. Critical for inventory rotation, markdown timing, and fresh product guarantees. Drives FEFO logic and influences safety stock levels.",
    business_synonyms: ["Product Life", "Expiration Period", "Best By Period", "Product Shelf Life", "Freshness Window", "Usable Life"],
    applicable_domains: ["pharmaceuticals", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    business_criticality: "high"
}),
(temperature_requirements:Column {
    column_id: "COL_TEMPERATURE_REQ_038",
    column_name: "TEMPERATURE_REQUIREMENTS",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 38,
    business_name: "Temperature Requirements",
    business_description: "Storage and transportation temperature requirements (frozen, refrigerated, ambient). Drives supply chain costs, equipment needs, and quality compliance. Critical for cold chain management.",
    business_synonyms: ["Storage Temperature", "Temperature Control", "Storage Requirements", "Climate Control", "Temperature Range", "Storage Conditions"],
    applicable_domains: ["pharmaceuticals", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high"
}),
(shelf_placement_code:Column {
    column_id: "COL_SHELF_PLACEMENT_039",
    column_name: "SHELF_PLACEMENT_CODE",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 39,
    business_name: "Shelf Placement Code",
    business_description: "Recommended shelf position (eye level, top shelf, bottom shelf) based on category strategy and consumer shopping behavior. Influences planogram development and space productivity.",
    business_synonyms: ["Shelf Position", "Placement Code", "Shelf Location", "Merchandising Position", "Shelf Level", "Display Position"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "medium"
}),
(facings_recommended:Column {
    column_id: "COL_FACINGS_RECOMMENDED_040",
    column_name: "FACINGS_RECOMMENDED",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 40,
    business_name: "Recommended Facings",
    business_description: "Optimal number of product facings for shelf presentation based on velocity and visual impact. Balances space productivity with adequate inventory. Input to planogram optimization.",
    business_synonyms: ["Shelf Facings", "Facing Count", "Recommended Faces", "Optimal Facings", "Display Facings", "Shelf Faces"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    business_criticality: "medium"
});

// Continue with remaining columns
CREATE 
(ecommerce_enabled:Column {
    column_id: "COL_ECOMMERCE_ENABLED_041",
    column_name: "ECOMMERCE_ENABLED",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 41,
    business_name: "E-commerce Enabled Flag",
    business_description: "Indicates product availability for online sale considering shipping restrictions, hazmat status, and channel agreements. Drives digital assortment and fulfillment strategies.",
    business_synonyms: ["Online Enabled", "Digital Available", "Web Enabled", "E-comm Flag", "Online Selling Flag", "Digital Commerce Flag"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "high"
}),
(amazon_asin:Column {
    column_id: "COL_AMAZON_ASIN_042",
    column_name: "AMAZON_ASIN",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 42,
    business_name: "Amazon ASIN",
    business_description: "Amazon Standard Identification Number for marketplace selling and buy box competition. Critical for Amazon vendor and seller central operations. Enables price monitoring and content syndication.",
    business_synonyms: ["ASIN", "Amazon ID", "Amazon Product ID", "Amazon Number", "Amazon SKU", "Amazon Identifier"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 10,
    business_criticality: "medium"
}),
(product_images_url:Column {
    column_id: "COL_PRODUCT_IMAGES_URL_043",
    column_name: "PRODUCT_IMAGES_URL",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 43,
    business_name: "Product Images URL",
    business_description: "Links to high-resolution product images for e-commerce, marketing materials, and digital shelf optimization. Must meet retailer image specifications. Critical for conversion rates.",
    business_synonyms: ["Image URLs", "Product Photos", "Digital Assets", "Product Pictures", "Image Links", "Media URLs"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "TEXT",
    business_criticality: "medium"
}),
(digital_shelf_content:Column {
    column_id: "COL_DIGITAL_SHELF_CONTENT_044",
    column_name: "DIGITAL_SHELF_CONTENT",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 44,
    business_name: "Digital Shelf Content",
    business_description: "Enhanced product content including features, benefits, and usage instructions optimized for e-commerce conversion. Includes A+ content elements and SEO optimization.",
    business_synonyms: ["Enhanced Content", "Product Description", "Digital Content", "E-commerce Content", "Online Description", "Web Content"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "TEXT",
    business_criticality: "medium"
}),
(recyclable_packaging:Column {
    column_id: "COL_RECYCLABLE_PACKAGING_045",
    column_name: "RECYCLABLE_PACKAGING",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 45,
    business_name: "Recyclable Packaging Flag",
    business_description: "Indicates if product packaging is recyclable according to How2Recycle standards. Critical for sustainability reporting, consumer communications, and retailer scorecards.",
    business_synonyms: ["Recyclable", "Recycling Flag", "Sustainable Packaging", "Eco-Friendly Package", "Green Packaging", "Recyclability"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "medium"
}),
(carbon_footprint:Column {
    column_id: "COL_CARBON_FOOTPRINT_046",
    column_name: "CARBON_FOOTPRINT",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 46,
    business_name: "Carbon Footprint",
    business_description: "Product carbon footprint in CO2 equivalents from cradle to shelf. Increasingly required by retailers and regulations. Enables carbon labeling and reduction targeting.",
    business_synonyms: ["CO2 Footprint", "Carbon Impact", "GHG Emissions", "Environmental Impact", "Carbon Score", "CO2e"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 3,
    business_criticality: "medium"
}),
(sustainability_certifications:Column {
    column_id: "COL_SUSTAINABILITY_CERTS_047",
    column_name: "SUSTAINABILITY_CERTIFICATIONS",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 47,
    business_name: "Sustainability Certifications",
    business_description: "Third-party sustainability certifications (USDA Organic, Fair Trade, Rainforest Alliance, etc.). Drives premium positioning and conscious consumer targeting. Requires ongoing compliance.",
    business_synonyms: ["Eco Certifications", "Green Certifications", "Sustainability Labels", "Environmental Certs", "Eco Labels", "Green Claims"],
    applicable_domains: ["alcoholic_beverages", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "TEXT",
    business_criticality: "medium"
}),
(quality_grade:Column {
    column_id: "COL_QUALITY_GRADE_048",
    column_name: "QUALITY_GRADE",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 48,
    business_name: "Quality Grade",
    business_description: "Product quality classification (Premium, Standard, Value) affecting pricing strategy, merchandising placement, and promotional tactics. Aligns with brand positioning and margin targets.",
    business_synonyms: ["Product Grade", "Quality Tier", "Grade Level", "Quality Class", "Product Tier", "Quality Rating"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "medium"
}),
(hazmat_classification:Column {
    column_id: "COL_HAZMAT_CLASS_049",
    column_name: "HAZMAT_CLASSIFICATION",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 49,
    business_name: "Hazmat Classification",
    business_description: "DOT hazardous material classification affecting shipping methods, costs, and e-commerce eligibility. Critical for batteries, aerosols, and flammable products. Drives fulfillment constraints.",
    business_synonyms: ["Hazmat Code", "Dangerous Goods Class", "DOT Classification", "Hazard Class", "Shipping Classification", "DG Code"],
    applicable_domains: ["battery", "cosmetics"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "high"
}),
(recall_history:Column {
    column_id: "COL_RECALL_HISTORY_050",
    column_name: "RECALL_HISTORY",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 50,
    business_name: "Recall History",
    business_description: "Historical record of product recalls including dates, reasons, and affected lots. Critical for risk assessment, insurance, and vendor negotiations. Links to CPSC, FDA recall databases.",
    business_synonyms: ["Recall Record", "Safety Recalls", "Product Recalls", "Recall Status", "Recall Information", "Safety History"],
    applicable_domains: ["pharmaceuticals", "toys", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "TEXT",
    business_criticality: "high"
}),
(price_tier:Column {
    column_id: "COL_PRICE_TIER_051",
    column_name: "PRICE_TIER",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 51,
    business_name: "Price Tier",
    business_description: "Price-based product segmentation (Premium, Mid-tier, Value, Opening Price Point) for competitive analysis and portfolio optimization. Drives promotional strategy and margin management.",
    business_synonyms: ["Price Segment", "Price Point Tier", "Price Range", "Price Classification", "Value Tier", "Price Band"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "high"
}),
(velocity_rank:Column {
    column_id: "COL_VELOCITY_RANK_052",
    column_name: "VELOCITY_RANK",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 52,
    business_name: "Velocity Rank",
    business_description: "Sales velocity ranking within category (A, B, C, D) driving inventory strategies, promotional priorities, and distribution decisions. Updated quarterly based on unit movement.",
    business_synonyms: ["Sales Rank", "Movement Rank", "Velocity Class", "ABC Classification", "Turn Rank", "Sales Velocity Tier"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 10,
    business_criticality: "high"
}),
(innovation_type:Column {
    column_id: "COL_INNOVATION_TYPE_053",
    column_name: "INNOVATION_TYPE",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 53,
    business_name: "Innovation Type",
    business_description: "Classification of product innovation (New to World, Line Extension, Package Innovation, Formulation Update) for innovation metrics and success tracking.",
    business_synonyms: ["Innovation Class", "New Product Type", "Innovation Category", "NPD Type", "Launch Type", "Innovation Level"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "medium"
}),
(alcohol_by_volume:Column {
    column_id: "COL_ALCOHOL_BY_VOLUME_054",
    column_name: "ALCOHOL_BY_VOLUME",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 54,
    business_name: "Alcohol by Volume (ABV)",
    business_description: "Alcohol content percentage critical for tax calculations, regulatory compliance, and consumer information. Determines distribution restrictions and pricing tiers. Must match label exactly.",
    business_synonyms: ["ABV", "Alcohol Content", "Proof/2", "Alcohol Percentage", "Alcohol Strength", "ABV%"],
    applicable_domains: ["alcoholic_beverages"],
    domain_specific: true,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "critical"
}),
(drug_schedule:Column {
    column_id: "COL_DRUG_SCHEDULE_055",
    column_name: "DRUG_SCHEDULE",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 55,
    business_name: "DEA Drug Schedule",
    business_description: "DEA controlled substance schedule (I-V) determining distribution restrictions, security requirements, and reporting obligations. Critical for compliance and inventory management.",
    business_synonyms: ["DEA Schedule", "Controlled Schedule", "Drug Class", "Control Level", "DEA Classification", "Schedule Number"],
    applicable_domains: ["pharmaceuticals"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 10,
    business_criticality: "critical"
}),
(age_grading:Column {
    column_id: "COL_AGE_GRADING_056",
    column_name: "AGE_GRADING",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 56,
    business_name: "Age Grading",
    business_description: "Minimum recommended age for toys determining safety requirements, warnings, and marketing restrictions. Critical for CPSC compliance and retail placement. Format: 'Ages X+'.",
    business_synonyms: ["Age Range", "Age Recommendation", "Minimum Age", "Age Rating", "Age Appropriate", "Age Group"],
    applicable_domains: ["toys"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "critical"
}),
(battery_type:Column {
    column_id: "COL_BATTERY_TYPE_057",
    column_name: "BATTERY_TYPE",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 57,
    business_name: "Battery Type",
    business_description: "Battery chemistry and format (Alkaline AA, Lithium-Ion, Lead-Acid) affecting shipping regulations, recycling programs, and merchandising placement. Critical for hazmat compliance.",
    business_synonyms: ["Battery Chemistry", "Cell Type", "Battery Technology", "Power Cell Type", "Battery Category", "Battery Format"],
    applicable_domains: ["battery"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high"
}),
(spf_rating:Column {
    column_id: "COL_SPF_RATING_058",
    column_name: "SPF_RATING",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 58,
    business_name: "SPF Rating",
    business_description: "Sun Protection Factor for sunscreens and cosmetics with UV protection. FDA-regulated claim requiring specific testing. Major purchase driver and premium indicator.",
    business_synonyms: ["Sun Protection Factor", "SPF Level", "SPF Number", "UV Protection Rating", "Sunscreen Factor", "SPF Value"],
    applicable_domains: ["cosmetics"],
    domain_specific: true,
    semantic_type: "measure",
    data_type: "INTEGER",
    business_criticality: "high"
}),
(organic_certified:Column {
    column_id: "COL_ORGANIC_CERTIFIED_059",
    column_name: "ORGANIC_CERTIFIED",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 59,
    business_name: "Organic Certified Flag",
    business_description: "USDA Organic certification status enabling premium pricing and targeted marketing. Requires supply chain verification and annual recertification. Major growth driver in food categories.",
    business_synonyms: ["Organic Flag", "USDA Organic", "Certified Organic", "Organic Status", "Organic Certification", "Organic Product"],
    applicable_domains: ["food_beverage"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "high"
}),
(created_date:Column {
    column_id: "COL_CREATED_DATE_060",
    column_name: "CREATED_DATE",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 60,
    business_name: "Record Created Date",
    business_description: "Date when product record was first created in master data system. Used for data lineage, audit trails, and new product adoption tracking. Immutable after creation.",
    business_synonyms: ["Creation Date", "Insert Date", "Record Date", "Entry Date", "Setup Date", "Initial Load Date"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    business_criticality: "medium"
}),
(last_updated_date:Column {
    column_id: "COL_LAST_UPDATED_DATE_061",
    column_name: "LAST_UPDATED_DATE",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 61,
    business_name: "Last Updated Date",
    business_description: "Most recent modification timestamp for change tracking and data freshness monitoring. Critical for identifying stale data and sync issues. Updated with every attribute change.",
    business_synonyms: ["Modified Date", "Update Date", "Last Modified", "Change Date", "Revision Date", "Last Changed"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    business_criticality: "medium"
}),
(data_source_system:Column {
    column_id: "COL_DATA_SOURCE_SYSTEM_062",
    column_name: "DATA_SOURCE_SYSTEM",
    table_id: "DIM_PRODUCT_MASTER",
    ordinal_position: 62,
    business_name: "Data Source System",
    business_description: "Originating system for product master data (ERP, PLM, MDM, etc.). Used for data quality investigations, reconciliation, and system integration mapping. Critical for merger integrations.",
    business_synonyms: ["Source System", "Origin System", "Data Source", "System of Record", "Master System", "Source Application"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "medium"
});

// ========================================
// CREATE TABLE-COLUMN RELATIONSHIPS
// ========================================

MATCH (t:Table {table_id: "DIM_PRODUCT_MASTER"})
MATCH (c:Column {table_id: "DIM_PRODUCT_MASTER"})
CREATE (t)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(c);

// ========================================
// VERIFICATION QUERIES
// ========================================

// Count total columns created
MATCH (c:Column {table_id: "DIM_PRODUCT_MASTER"})
RETURN count(c) AS total_columns;

// Verify columns by domain specificity
MATCH (c:Column {table_id: "DIM_PRODUCT_MASTER"})
RETURN c.domain_specific AS is_domain_specific, 
       count(c) AS column_count
ORDER BY is_domain_specific;

// List domain-specific columns with their applicable domains
MATCH (c:Column {table_id: "DIM_PRODUCT_MASTER"})
WHERE c.domain_specific = true
RETURN c.business_name AS column_name, 
       c.applicable_domains AS domains
ORDER BY c.ordinal_position;

// Verify critical columns
MATCH (c:Column {table_id: "DIM_PRODUCT_MASTER"})
WHERE c.business_criticality = "critical"
RETURN c.business_name AS critical_column, 
       c.applicable_domains AS domains
ORDER BY c.ordinal_position;

// Check column data types distribution
MATCH (c:Column {table_id: "DIM_PRODUCT_MASTER"})
RETURN c.data_type AS data_type, 
       count(c) AS count
ORDER BY count DESC;

// ========================================
// END OF DIM_PRODUCT_MASTER COLUMN CREATION
// ========================================