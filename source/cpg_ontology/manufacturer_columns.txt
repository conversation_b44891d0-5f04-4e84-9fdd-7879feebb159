// ========================================
// DIM_MANUFACTURER - COMPREHENSIVE COLUMN CREATION
// Complete manufacturer/supplier dimension columns for all CPG domains
// ========================================

// Clear existing columns for DIM_MANUFACTURER (optional)
MATCH (c:Column {table_id: "DIM_MANUFACTURER"}) DETACH DELETE c;

// ========================================
// CORE MANUFACTURER IDENTIFIERS AND ATTRIBUTES
// ========================================

CREATE 
(manufacturer_id:Column {
    column_id: "COL_MANUFACTURER_ID_DIM_150",
    column_name: "MANUFACTURER_ID",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 1,
    business_name: "Manufacturer ID",
    business_description: "Unique identifier for each manufacturer, supplier, or co-packer across the supply chain network. Survives vendor mergers and acquisitions. Critical for vendor master data management, purchase order processing, and quality tracking. Links to regulatory licenses and compliance records.",
    business_synonyms: ["Vendor ID", "Supplier ID", "Vendor Number", "Supplier Code", "Manufacturer Code", "Vendor Key", "Partner ID", "Producer ID", "Supplier Number"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 20,
    is_primary_key: true,
    business_criticality: "critical"
}),
(manufacturer_name:Column {
    column_id: "COL_MANUFACTURER_NAME_151",
    column_name: "MANUFACTURER_NAME",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 2,
    business_name: "Manufacturer Name",
    business_description: "Legal entity name of manufacturer as registered for business operations. Must match tax records, contracts, and regulatory filings. Used in purchase orders, quality certificates, and compliance documentation. Critical for payment processing and legal agreements.",
    business_synonyms: ["Vendor Name", "Supplier Name", "Company Name", "Legal Name", "Business Name", "Vendor Legal Name", "Manufacturer Legal Entity", "Producer Name"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 300,
    business_criticality: "critical"
}),
(doing_business_as:Column {
    column_id: "COL_DOING_BUSINESS_AS_152",
    column_name: "DOING_BUSINESS_AS",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 3,
    business_name: "Doing Business As (DBA)",
    business_description: "Trade name or DBA used in commercial operations. May differ from legal entity name. Important for brand recognition and marketing materials. Used on product packaging and promotional materials.",
    business_synonyms: ["DBA", "Trade Name", "Commercial Name", "Operating Name", "Business Alias", "Trading As", "Brand Name"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 300,
    business_criticality: "medium"
}),
(duns_number:Column {
    column_id: "COL_DUNS_NUMBER_153",
    column_name: "DUNS_NUMBER",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 4,
    business_name: "DUNS Number",
    business_description: "Dun & Bradstreet unique 9-digit identifier for business credit and risk assessment. Required for government contracts and large retailer onboarding. Links to financial ratings and payment history. Critical for supply chain finance programs.",
    business_synonyms: ["D-U-N-S", "D&B Number", "DUNS ID", "Dun Number", "Credit ID", "D&B Identifier", "Business Credit Number"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 9,
    business_criticality: "high"
}),
(tax_id_number:Column {
    column_id: "COL_TAX_ID_NUMBER_154",
    column_name: "TAX_ID_NUMBER",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 5,
    business_name: "Tax ID Number",
    business_description: "Federal tax identification number (EIN in US) for payment processing and 1099 reporting. Required for accounts payable setup and tax compliance. Must be validated before first payment. Confidential information requiring secure handling.",
    business_synonyms: ["EIN", "Federal Tax ID", "TIN", "Employer ID", "Tax Number", "VAT Number", "Business Tax ID", "Federal ID"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "critical"
}),
(manufacturer_type:Column {
    column_id: "COL_MANUFACTURER_TYPE_155",
    column_name: "MANUFACTURER_TYPE",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 6,
    business_name: "Manufacturer Type",
    business_description: "Classification of supplier relationship (OEM, Contract Manufacturer, Co-Packer, Raw Material Supplier, Packaging Supplier). Determines quality requirements, audit frequency, and contract terms. Critical for supply chain risk assessment.",
    business_synonyms: ["Vendor Type", "Supplier Type", "Partner Type", "Vendor Category", "Supplier Classification", "Business Type", "Relationship Type"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high"
}),

// ========================================
// COMPANY PROFILE AND CAPABILITIES
// ========================================

(company_size:Column {
    column_id: "COL_COMPANY_SIZE_156",
    column_name: "COMPANY_SIZE",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 7,
    business_name: "Company Size Classification",
    business_description: "Size classification based on revenue and employee count (Small, Medium, Large, Enterprise). Affects payment terms, minimum orders, and supplier diversity programs. Important for risk assessment and business continuity planning.",
    business_synonyms: ["Business Size", "Company Scale", "Enterprise Size", "Vendor Size", "Organization Size", "Business Classification"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "medium"
}),
(annual_revenue:Column {
    column_id: "COL_ANNUAL_REVENUE_157",
    column_name: "ANNUAL_REVENUE",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 8,
    business_name: "Annual Revenue",
    business_description: "Manufacturer's total annual revenue indicating financial stability and scale. Used for credit decisions, payment terms, and strategic partnership evaluation. Updated annually from financial reports or credit agencies.",
    business_synonyms: ["Yearly Revenue", "Annual Sales", "Total Revenue", "Company Revenue", "Business Revenue", "Annual Turnover"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 2,
    business_criticality: "high"
}),
(employee_count:Column {
    column_id: "COL_EMPLOYEE_COUNT_158",
    column_name: "EMPLOYEE_COUNT",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 9,
    business_name: "Employee Count",
    business_description: "Total number of employees indicating operational scale and capacity. Important for business continuity assessment and production flexibility. Affects ability to scale operations and manage surge demand.",
    business_synonyms: ["Staff Count", "Workforce Size", "Employee Number", "Headcount", "Personnel Count", "Staff Size"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    business_criticality: "medium"
}),
(year_established:Column {
    column_id: "COL_YEAR_ESTABLISHED_159",
    column_name: "YEAR_ESTABLISHED",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 10,
    business_name: "Year Established",
    business_description: "Year company was founded indicating business maturity and experience. Longer tenure often correlates with stability and reliability. Important for risk assessment and heritage brand manufacturing.",
    business_synonyms: ["Founded Year", "Establishment Year", "Company Start Year", "Business Founded", "Inception Year", "Foundation Year"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "INTEGER",
    business_criticality: "low"
}),
(ownership_type:Column {
    column_id: "COL_OWNERSHIP_TYPE_160",
    column_name: "OWNERSHIP_TYPE",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 11,
    business_name: "Ownership Type",
    business_description: "Corporate structure (Public, Private, Family-Owned, Private Equity). Affects financial transparency, decision-making speed, and long-term stability. Important for strategic partnership decisions.",
    business_synonyms: ["Company Type", "Business Structure", "Corporate Type", "Ownership Structure", "Entity Type", "Organization Type"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "medium"
}),

// ========================================
// GEOGRAPHIC AND FACILITY INFORMATION
// ========================================

(headquarters_address:Column {
    column_id: "COL_HEADQUARTERS_ADDRESS_161",
    column_name: "HEADQUARTERS_ADDRESS",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 12,
    business_name: "Headquarters Address",
    business_description: "Primary business address for legal correspondence and executive contacts. Used for contracts, legal notices, and corporate communications. Must be kept current for compliance requirements.",
    business_synonyms: ["HQ Address", "Main Office", "Corporate Address", "Head Office", "Primary Address", "Legal Address"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 500,
    business_criticality: "high"
}),
(headquarters_country:Column {
    column_id: "COL_HEADQUARTERS_COUNTRY_162",
    column_name: "HEADQUARTERS_COUNTRY",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 13,
    business_name: "Headquarters Country",
    business_description: "Country of headquarters determining primary regulatory jurisdiction and business culture. Affects international trade requirements, currency exposure, and geopolitical risk. Critical for supply chain diversification strategies.",
    business_synonyms: ["HQ Country", "Home Country", "Country of Origin", "Base Country", "Primary Country", "Corporate Country"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "high"
}),
(manufacturing_locations:Column {
    column_id: "COL_MANUFACTURING_LOCATIONS_163",
    column_name: "MANUFACTURING_LOCATIONS",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 14,
    business_name: "Manufacturing Locations",
    business_description: "List of all production facilities with addresses and capabilities. Critical for supply chain planning, quality audits, and business continuity. Each location may have different certifications and capabilities.",
    business_synonyms: ["Production Sites", "Plant Locations", "Facility List", "Manufacturing Sites", "Production Facilities", "Factory Locations"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "TEXT",
    business_criticality: "high"
}),
(geographic_coverage:Column {
    column_id: "COL_GEOGRAPHIC_COVERAGE_164",
    column_name: "GEOGRAPHIC_COVERAGE",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 15,
    business_name: "Geographic Coverage",
    business_description: "Regions or countries where manufacturer can supply products. Important for global sourcing strategies and regional supply chain design. Affects lead times and logistics costs.",
    business_synonyms: ["Service Area", "Coverage Area", "Supply Regions", "Distribution Coverage", "Service Territory", "Supply Geography"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "TEXT",
    business_criticality: "medium"
}),

// ========================================
// REGULATORY AND COMPLIANCE
// ========================================

(fda_facility_registration:Column {
    column_id: "COL_FDA_FACILITY_REG_165",
    column_name: "FDA_FACILITY_REGISTRATION",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 16,
    business_name: "FDA Facility Registration",
    business_description: "FDA establishment registration number for facilities producing FDA-regulated products. Required for food, drugs, devices, and cosmetics. Must be renewed biennially. Links to inspection history and compliance status.",
    business_synonyms: ["FDA Registration", "FDA Number", "Facility FDA ID", "FDA Establishment Number", "FDA Reg Number", "FDA FEI"],
    applicable_domains: ["pharmaceuticals", "food_beverage", "cosmetics"],
    domain_specific: true,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "critical"
}),
(dea_registration:Column {
    column_id: "COL_DEA_REGISTRATION_166",
    column_name: "DEA_REGISTRATION",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 17,
    business_name: "DEA Registration",
    business_description: "Drug Enforcement Administration registration for handling controlled substances. Required for manufacturing scheduled pharmaceuticals. Strictly controlled with regular audits. Determines which products can be manufactured.",
    business_synonyms: ["DEA Number", "DEA License", "Controlled Substance License", "DEA Reg", "DEA ID", "CS Registration"],
    applicable_domains: ["pharmaceuticals"],
    domain_specific: true,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "critical"
}),
(ttb_permit_number:Column {
    column_id: "COL_TTB_PERMIT_NUMBER_167",
    column_name: "TTB_PERMIT_NUMBER",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 18,
    business_name: "TTB Permit Number",
    business_description: "Alcohol and Tobacco Tax and Trade Bureau permit for producing alcoholic beverages. Specifies authorized products and production capacity. Required for legal production and interstate commerce. Links to bonded premises.",
    business_synonyms: ["TTB License", "Alcohol Permit", "Distillery Permit", "Brewery License", "Winery Permit", "TTB Number"],
    applicable_domains: ["alcoholic_beverages"],
    domain_specific: true,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "critical"
}),
(iso_certifications:Column {
    column_id: "COL_ISO_CERTIFICATIONS_168",
    column_name: "ISO_CERTIFICATIONS",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 19,
    business_name: "ISO Certifications",
    business_description: "List of International Organization for Standardization certifications (ISO 9001, ISO 14001, ISO 45001, etc.). Indicates quality management maturity and process standardization. Important for vendor selection and risk management.",
    business_synonyms: ["ISO Standards", "Quality Certifications", "ISO Certs", "International Standards", "ISO Compliance", "Quality Standards"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "TEXT",
    business_criticality: "high"
}),
(gmp_certified:Column {
    column_id: "COL_GMP_CERTIFIED_169",
    column_name: "GMP_CERTIFIED",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 20,
    business_name: "GMP Certified Flag",
    business_description: "Good Manufacturing Practices certification status. Mandatory for pharmaceuticals and increasingly required for food and cosmetics. Ensures consistent quality and regulatory compliance. Subject to regular audits.",
    business_synonyms: ["GMP Status", "Good Manufacturing", "GMP Compliance", "cGMP Certified", "GMP Flag", "Manufacturing Standards"],
    applicable_domains: ["pharmaceuticals", "food_beverage", "cosmetics"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "critical"
}),
(haccp_certified:Column {
    column_id: "COL_HACCP_CERTIFIED_170",
    column_name: "HACCP_CERTIFIED",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 21,
    business_name: "HACCP Certified Flag",
    business_description: "Hazard Analysis Critical Control Points certification for food safety. Required by many retailers for food manufacturers. Demonstrates systematic approach to food safety hazards. Annual recertification required.",
    business_synonyms: ["HACCP Status", "Food Safety Cert", "HACCP Compliance", "HACCP Flag", "Food Safety Standard"],
    applicable_domains: ["food_beverage"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "critical"
}),
(organic_certified:Column {
    column_id: "COL_ORGANIC_CERTIFIED_171",
    column_name: "ORGANIC_CERTIFIED",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 22,
    business_name: "Organic Certified Flag",
    business_description: "USDA Organic certification for manufacturing facilities. Enables production of certified organic products. Requires separate processing lines and detailed record keeping. Premium capability commanding higher margins.",
    business_synonyms: ["Organic Cert", "USDA Organic", "Organic Status", "Organic Facility", "Organic Compliance", "Organic License"],
    applicable_domains: ["food_beverage", "cosmetics"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "high"
}),

// ========================================
// QUALITY AND PERFORMANCE METRICS
// ========================================

(quality_score:Column {
    column_id: "COL_QUALITY_SCORE_172",
    column_name: "QUALITY_SCORE",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 23,
    business_name: "Quality Score",
    business_description: "Composite quality metric based on defect rates, audit scores, and customer complaints. Updated quarterly from quality data. Key factor in supplier scorecarding and sourcing decisions. Triggers improvement plans when below threshold.",
    business_synonyms: ["Quality Rating", "Quality Index", "Performance Score", "Quality Metric", "Supplier Score", "Quality KPI"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high"
}),
(on_time_delivery_rate:Column {
    column_id: "COL_ON_TIME_DELIVERY_173",
    column_name: "ON_TIME_DELIVERY_RATE",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 24,
    business_name: "On-Time Delivery Rate",
    business_description: "Percentage of orders delivered within agreed timeframe. Critical supply chain KPI affecting inventory levels and customer service. Industry benchmark is 95%+. Lower rates trigger performance improvement discussions.",
    business_synonyms: ["OTD Rate", "Delivery Performance", "OTIF Rate", "Delivery Score", "Fulfillment Rate", "Service Level"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high"
}),
(defect_rate_ppm:Column {
    column_id: "COL_DEFECT_RATE_PPM_174",
    column_name: "DEFECT_RATE_PPM",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 25,
    business_name: "Defect Rate PPM",
    business_description: "Quality defects measured in parts per million. Industry-standard quality metric enabling benchmarking across suppliers. Lower is better with world-class being under 50 PPM. Drives continuous improvement initiatives.",
    business_synonyms: ["PPM", "Defect Rate", "Quality PPM", "Failure Rate", "Defect Level", "Quality Defects"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    business_criticality: "high"
}),
(last_audit_date:Column {
    column_id: "COL_LAST_AUDIT_DATE_175",
    column_name: "LAST_AUDIT_DATE",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 26,
    business_name: "Last Audit Date",
    business_description: "Date of most recent quality or compliance audit. Triggers for re-audit based on risk level and performance. Required annually for critical suppliers. Links to detailed audit reports and corrective actions.",
    business_synonyms: ["Audit Date", "Last Inspection", "Recent Audit", "Quality Audit Date", "Inspection Date", "Assessment Date"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    business_criticality: "high"
}),
(last_audit_score:Column {
    column_id: "COL_LAST_AUDIT_SCORE_176",
    column_name: "LAST_AUDIT_SCORE",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 27,
    business_name: "Last Audit Score",
    business_description: "Score from most recent facility audit (0-100). Minimum acceptable score varies by product criticality. Scores below 80 typically require corrective action plans. Affects approved supplier status.",
    business_synonyms: ["Audit Score", "Audit Rating", "Inspection Score", "Quality Audit Score", "Assessment Score", "Audit Result"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high"
}),
(recall_count_5yr:Column {
    column_id: "COL_RECALL_COUNT_5YR_177",
    column_name: "RECALL_COUNT_5YR",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 28,
    business_name: "5-Year Recall Count",
    business_description: "Number of product recalls in past 5 years indicating quality system effectiveness. Major factor in risk assessment and insurance costs. Zero recalls preferred but one minor recall acceptable. Multiple recalls trigger supplier review.",
    business_synonyms: ["Recall History", "Recall Number", "Safety Recalls", "Product Recalls", "Recall Incidents", "Historical Recalls"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    business_criticality: "high"
}),

// ========================================
// FINANCIAL AND COMMERCIAL TERMS
// ========================================

(payment_terms:Column {
    column_id: "COL_PAYMENT_TERMS_178",
    column_name: "PAYMENT_TERMS",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 29,
    business_name: "Payment Terms",
    business_description: "Standard payment terms negotiated with supplier (Net 30, 2/10 Net 30, etc.). Affects cash flow and working capital. Better terms available for strategic suppliers. May include early payment discounts.",
    business_synonyms: ["Terms", "Payment Net", "Credit Terms", "Invoice Terms", "Payment Period", "Credit Period"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high"
}),
(credit_limit:Column {
    column_id: "COL_CREDIT_LIMIT_179",
    column_name: "CREDIT_LIMIT",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 30,
    business_name: "Credit Limit",
    business_description: "Maximum credit exposure approved for supplier based on financial assessment. Limits financial risk from supplier bankruptcy. Reviewed annually or with significant orders. May require parent company guarantees.",
    business_synonyms: ["Credit Line", "Credit Maximum", "Exposure Limit", "Credit Cap", "Risk Limit", "Credit Ceiling"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 2,
    business_criticality: "medium"
}),
(credit_rating:Column {
    column_id: "COL_CREDIT_RATING_180",
    column_name: "CREDIT_RATING",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 31,
    business_name: "Credit Rating",
    business_description: "Financial credit rating from D&B or internal assessment. Indicates financial stability and payment risk. Affects payment terms and credit limits. Monitored for early warning of financial distress.",
    business_synonyms: ["Credit Score", "Financial Rating", "D&B Rating", "Risk Rating", "Credit Grade", "Financial Score"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 10,
    business_criticality: "medium"
}),
(currency_code:Column {
    column_id: "COL_CURRENCY_CODE_181",
    column_name: "CURRENCY_CODE",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 32,
    business_name: "Primary Currency Code",
    business_description: "Primary transaction currency for supplier payments. Determines foreign exchange exposure and hedging requirements. Critical for accurate cost calculations and financial planning.",
    business_synonyms: ["Currency", "Payment Currency", "Transaction Currency", "Base Currency", "Billing Currency", "Invoice Currency"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 3,
    business_criticality: "high"
}),
(incoterms:Column {
    column_id: "COL_INCOTERMS_182",
    column_name: "INCOTERMS",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 33,
    business_name: "Incoterms",
    business_description: "International commercial terms defining shipping responsibilities and risk transfer point (FOB, CIF, DDP, etc.). Critical for landed cost calculations and insurance requirements. Must align with contract terms.",
    business_synonyms: ["Shipping Terms", "Trade Terms", "Delivery Terms", "International Terms", "Freight Terms", "Commercial Terms"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 10,
    business_criticality: "high"
}),

// ========================================
// SUPPLY CHAIN CAPABILITIES
// ========================================

(production_capacity:Column {
    column_id: "COL_PRODUCTION_CAPACITY_183",
    column_name: "PRODUCTION_CAPACITY",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 34,
    business_name: "Annual Production Capacity",
    business_description: "Maximum annual production capacity in relevant units. Critical for capacity planning and new product launches. Determines ability to support growth and seasonal peaks. May require capacity reservations.",
    business_synonyms: ["Capacity", "Production Volume", "Manufacturing Capacity", "Annual Capacity", "Max Production", "Capacity Limit"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 2,
    business_criticality: "high"
}),
(capacity_utilization:Column {
    column_id: "COL_CAPACITY_UTILIZATION_184",
    column_name: "CAPACITY_UTILIZATION",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 35,
    business_name: "Capacity Utilization Rate",
    business_description: "Current production capacity utilization percentage. High utilization may limit flexibility and increase lead times. Low utilization may indicate financial stress. Sweet spot is 75-85% for most manufacturers.",
    business_synonyms: ["Utilization Rate", "Capacity Usage", "Production Utilization", "Factory Utilization", "Capacity Load", "Utilization Percent"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "medium"
}),
(lead_time_standard:Column {
    column_id: "COL_LEAD_TIME_STANDARD_185",
    column_name: "LEAD_TIME_STANDARD",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 36,
    business_name: "Standard Lead Time Days",
    business_description: "Normal production and delivery lead time in days. Critical for inventory planning and reorder point calculations. Includes manufacturing time plus transit. Varies by product complexity and order size.",
    business_synonyms: ["Lead Time", "Delivery Time", "Production Time", "Order Lead Time", "Standard Lead", "Cycle Time"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    business_criticality: "high"
}),
(minimum_order_value:Column {
    column_id: "COL_MINIMUM_ORDER_VALUE_186",
    column_name: "MINIMUM_ORDER_VALUE",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 37,
    business_name: "Minimum Order Value",
    business_description: "Minimum order value required by supplier in base currency. Affects order frequency and inventory investment. Can be barrier for new products or small markets. May be waived for strategic partners.",
    business_synonyms: ["MOV", "Min Order Value", "Order Minimum", "Minimum Purchase", "Order Threshold", "Min Order Amount"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 2,
    business_criticality: "medium"
}),
(expedite_capable:Column {
    column_id: "COL_EXPEDITE_CAPABLE_187",
    column_name: "EXPEDITE_CAPABLE",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 38,
    business_name: "Expedite Capable Flag",
    business_description: "Indicates ability to expedite orders for urgent needs. Important for product launches and stockout recovery. Usually involves premium charges. Requires flexible production scheduling.",
    business_synonyms: ["Rush Capable", "Fast Track", "Expedite Flag", "Rush Order", "Priority Capable", "Quick Ship"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "medium"
}),

// ========================================
// TECHNOLOGY AND INNOVATION
// ========================================

(edi_capable:Column {
    column_id: "COL_EDI_CAPABLE_188",
    column_name: "EDI_CAPABLE",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 39,
    business_name: "EDI Capable Flag",
    business_description: "Electronic Data Interchange capability for automated order processing. Reduces errors and processing time. Required by many large retailers. Includes support for 850, 855, 856, 810 transactions.",
    business_synonyms: ["EDI Flag", "Electronic Orders", "EDI Enabled", "System Integration", "Automated Ordering", "EDI Ready"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "medium"
}),
(api_integration:Column {
    column_id: "COL_API_INTEGRATION_189",
    column_name: "API_INTEGRATION",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 40,
    business_name: "API Integration Available",
    business_description: "Modern API integration for real-time inventory visibility and order management. Enables better supply chain collaboration and visibility. Preferred over traditional EDI for agility.",
    business_synonyms: ["API Available", "System API", "Integration API", "Real-time Integration", "Modern Integration", "API Capability"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "medium"
}),
(blockchain_enabled:Column {
    column_id: "COL_BLOCKCHAIN_ENABLED_190",
    column_name: "BLOCKCHAIN_ENABLED",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 41,
    business_name: "Blockchain Enabled",
    business_description: "Participation in blockchain for supply chain traceability and authenticity. Important for high-value products and regulatory compliance. Enables track-and-trace capabilities and counterfeit prevention.",
    business_synonyms: ["Blockchain Ready", "DLT Enabled", "Traceability Enabled", "Blockchain Participant", "Chain of Custody", "Digital Ledger"],
    applicable_domains: ["pharmaceuticals", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "low"
}),
(innovation_partner:Column {
    column_id: "COL_INNOVATION_PARTNER_191",
    column_name: "INNOVATION_PARTNER",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 42,
    business_name: "Innovation Partner Flag",
    business_description: "Designated as strategic innovation partner for co-development projects. Provides early access to new technologies and exclusive products. Requires IP agreements and deeper collaboration.",
    business_synonyms: ["R&D Partner", "Development Partner", "Innovation Flag", "Strategic Partner", "Tech Partner", "Co-development"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "medium"
}),

// ========================================
// SUSTAINABILITY AND ESG
// ========================================

(sustainability_score:Column {
    column_id: "COL_SUSTAINABILITY_SCORE_192",
    column_name: "SUSTAINABILITY_SCORE",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 43,
    business_name: "Sustainability Score",
    business_description: "Composite ESG score covering environmental impact, labor practices, and governance. Increasingly important for supplier selection and reporting. Based on third-party assessments and self-reporting.",
    business_synonyms: ["ESG Score", "Green Score", "Sustainability Rating", "Environmental Score", "ESG Rating", "Sustainability Index"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "medium"
}),
(carbon_neutral_operations:Column {
    column_id: "COL_CARBON_NEUTRAL_OPS_193",
    column_name: "CARBON_NEUTRAL_OPERATIONS",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 44,
    business_name: "Carbon Neutral Operations",
    business_description: "Achieved carbon neutrality in manufacturing operations through reduction and offsets. Important for scope 3 emissions reporting and sustainability commitments. Requires third-party verification.",
    business_synonyms: ["Net Zero", "Carbon Neutral", "Climate Neutral", "Zero Emissions", "Carbon Neutral Flag", "Net Zero Operations"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "medium"
}),
(renewable_energy_percent:Column {
    column_id: "COL_RENEWABLE_ENERGY_PCT_194",
    column_name: "RENEWABLE_ENERGY_PERCENT",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 45,
    business_name: "Renewable Energy Percentage",
    business_description: "Percentage of energy from renewable sources in manufacturing operations. Key sustainability metric affecting supplier scorecards. Target of 100% renewable increasingly common.",
    business_synonyms: ["Green Energy", "Renewable Percent", "Clean Energy", "Sustainable Energy", "Renewable Power", "Green Power Percent"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "medium"
}),
(waste_diversion_rate:Column {
    column_id: "COL_WASTE_DIVERSION_RATE_195",
    column_name: "WASTE_DIVERSION_RATE",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 46,
    business_name: "Waste Diversion Rate",
    business_description: "Percentage of waste diverted from landfill through recycling, composting, or energy recovery. Zero waste to landfill increasingly expected. Affects sustainability scorecards and reporting.",
    business_synonyms: ["Recycling Rate", "Diversion Rate", "Waste Recovery", "Landfill Diversion", "Zero Waste Rate", "Recovery Rate"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "low"
}),

// ========================================
// RISK AND COMPLIANCE
// ========================================

(risk_score:Column {
    column_id: "COL_RISK_SCORE_196",
    column_name: "RISK_SCORE",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 47,
    business_name: "Supply Risk Score",
    business_description: "Composite risk assessment including financial, operational, geographic, and compliance risks. Drives supplier diversification strategies and contingency planning. Updated quarterly with market intelligence.",
    business_synonyms: ["Risk Rating", "Supplier Risk", "Risk Assessment", "Risk Level", "Supply Risk", "Vendor Risk Score"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high"
}),
(insurance_coverage:Column {
    column_id: "COL_INSURANCE_COVERAGE_197",
    column_name: "INSURANCE_COVERAGE",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 48,
    business_name: "Insurance Coverage Amount",
    business_description: "Product liability and general insurance coverage amount. Must meet minimum requirements based on product risk. Verified annually through certificates of insurance. Critical for risk management.",
    business_synonyms: ["Insurance Limit", "Coverage Amount", "Liability Coverage", "Insurance Value", "Policy Limit", "Coverage Level"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 2,
    business_criticality: "high"
}),
(business_continuity_plan:Column {
    column_id: "COL_BUSINESS_CONTINUITY_198",
    column_name: "BUSINESS_CONTINUITY_PLAN",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 49,
    business_name: "Business Continuity Plan",
    business_description: "Documented business continuity and disaster recovery plan. Required for critical suppliers. Includes alternate production sites and emergency contacts. Tested annually through exercises.",
    business_synonyms: ["BCP", "Continuity Plan", "Disaster Recovery", "Emergency Plan", "Recovery Plan", "Contingency Plan"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "high"
}),
(cybersecurity_assessment:Column {
    column_id: "COL_CYBERSECURITY_ASSESS_199",
    column_name: "CYBERSECURITY_ASSESSMENT",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 50,
    business_name: "Cybersecurity Assessment Date",
    business_description: "Date of last cybersecurity assessment for suppliers with system integration. Increasingly important with ransomware risks. Required for EDI and API connected suppliers. Annual assessment minimum.",
    business_synonyms: ["Cyber Assessment", "Security Audit", "IT Security Review", "Cyber Audit Date", "Security Assessment", "InfoSec Review"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    business_criticality: "medium"
}),

// ========================================
// RELATIONSHIP MANAGEMENT
// ========================================

(strategic_supplier:Column {
    column_id: "COL_STRATEGIC_SUPPLIER_200",
    column_name: "STRATEGIC_SUPPLIER",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 51,
    business_name: "Strategic Supplier Flag",
    business_description: "Designated strategic supplier receiving enhanced collaboration, longer contracts, and joint planning. Limited to top 20% of spend. Requires executive sponsorship and quarterly business reviews.",
    business_synonyms: ["Strategic Partner", "Key Supplier", "Strategic Vendor", "Preferred Supplier", "Tier 1 Supplier", "Strategic Flag"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "high"
}),
(preferred_supplier:Column {
    column_id: "COL_PREFERRED_SUPPLIER_201",
    column_name: "PREFERRED_SUPPLIER",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 52,
    business_name: "Preferred Supplier Status",
    business_description: "First choice supplier for specific categories based on performance and capabilities. Receives RFQ priority and volume commitments. Status reviewed annually based on scorecards.",
    business_synonyms: ["Preferred Vendor", "Preferred Status", "Primary Supplier", "First Choice", "Preferred Partner", "Priority Supplier"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "medium"
}),
(contract_expiry_date:Column {
    column_id: "COL_CONTRACT_EXPIRY_DATE_202",
    column_name: "CONTRACT_EXPIRY_DATE",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 53,
    business_name: "Master Contract Expiry Date",
    business_description: "Expiration date of master supply agreement. Triggers renegotiation process 6 months prior. Critical for ensuring continuous supply and updated terms. May auto-renew unless terminated.",
    business_synonyms: ["Contract End Date", "Agreement Expiry", "Contract Termination", "Agreement End", "Contract Renewal Date"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    business_criticality: "high"
}),
(annual_spend_amount:Column {
    column_id: "COL_ANNUAL_SPEND_AMOUNT_203",
    column_name: "ANNUAL_SPEND_AMOUNT",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 54,
    business_name: "Annual Spend Amount",
    business_description: "Total annual procurement spend with supplier. Key metric for negotiation leverage and supplier importance. Drives strategic supplier designation and payment term negotiations.",
    business_synonyms: ["Annual Purchase", "Yearly Spend", "Annual Volume", "Purchase Amount", "Spend Value", "Annual Procurement"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 2,
    business_criticality: "high"
}),

// ========================================
// CONTACTS AND COMMUNICATION
// ========================================

(primary_contact_name:Column {
    column_id: "COL_PRIMARY_CONTACT_NAME_204",
    column_name: "PRIMARY_CONTACT_NAME",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 55,
    business_name: "Primary Contact Name",
    business_description: "Main business contact for operational matters. Usually account manager or sales representative. First point of contact for orders and issues. Updated with personnel changes.",
    business_synonyms: ["Contact Name", "Account Manager", "Sales Contact", "Main Contact", "Business Contact", "Key Contact"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 200,
    business_criticality: "medium"
}),
(primary_contact_email:Column {
    column_id: "COL_PRIMARY_CONTACT_EMAIL_205",
    column_name: "PRIMARY_CONTACT_EMAIL",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 56,
    business_name: "Primary Contact Email",
    business_description: "Email address for primary business contact. Used for automated communications and order confirmations. Must be kept current for business continuity. May have multiple contacts for redundancy.",
    business_synonyms: ["Contact Email", "Email Address", "Business Email", "Contact Email Address", "Primary Email"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 200,
    business_criticality: "medium"
}),
(primary_contact_phone:Column {
    column_id: "COL_PRIMARY_CONTACT_PHONE_206",
    column_name: "PRIMARY_CONTACT_PHONE",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 57,
    business_name: "Primary Contact Phone",
    business_description: "Phone number for primary business contact including country code. Critical for urgent issues and expedite requests. Should include mobile for 24/7 critical suppliers.",
    business_synonyms: ["Contact Phone", "Phone Number", "Business Phone", "Contact Number", "Primary Phone"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "medium"
}),

// ========================================
// DIVERSITY AND INCLUSION
// ========================================

(diversity_classification:Column {
    column_id: "COL_DIVERSITY_CLASS_207",
    column_name: "DIVERSITY_CLASSIFICATION",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 58,
    business_name: "Diversity Classification",
    business_description: "Supplier diversity certification (MBE, WBE, VBE, SBE, etc.). Important for diversity spend reporting and goals. May provide access to special programs and priorities. Requires annual certification.",
    business_synonyms: ["Diversity Status", "Minority Business", "Diversity Certification", "Diverse Supplier", "Diversity Type", "MBE/WBE Status"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "medium"
}),
(women_owned_flag:Column {
    column_id: "COL_WOMEN_OWNED_FLAG_208",
    column_name: "WOMEN_OWNED_FLAG",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 59,
    business_name: "Women-Owned Business Flag",
    business_description: "Certified women-owned business enterprise (WBE). Requires 51% or more female ownership and control. Contributes to supplier diversity goals. May receive preferential consideration.",
    business_synonyms: ["WBE", "Women Owned", "Female Owned", "Women Business", "WBE Certified", "Women Enterprise"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "medium"
}),
(minority_owned_flag:Column {
    column_id: "COL_MINORITY_OWNED_FLAG_209",
    column_name: "MINORITY_OWNED_FLAG",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 60,
    business_name: "Minority-Owned Business Flag",
    business_description: "Certified minority-owned business enterprise (MBE). Requires 51% or more minority ownership and control. Important for supplier diversity metrics and corporate social responsibility.",
    business_synonyms: ["MBE", "Minority Owned", "Minority Business", "MBE Certified", "Diverse Ownership", "Minority Enterprise"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "medium"
}),

// ========================================
// METADATA AND GOVERNANCE
// ========================================

(supplier_since_date:Column {
    column_id: "COL_SUPPLIER_SINCE_DATE_210",
    column_name: "SUPPLIER_SINCE_DATE",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 61,
    business_name: "Supplier Since Date",
    business_description: "Date when supplier relationship began. Indicates relationship tenure and stability. Long relationships suggest reliability. Used for supplier recognition programs.",
    business_synonyms: ["Relationship Start", "Vendor Since", "Partnership Date", "Start Date", "First Order Date", "Onboard Date"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    business_criticality: "low"
}),
(record_status:Column {
    column_id: "COL_RECORD_STATUS_211",
    column_name: "RECORD_STATUS",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 62,
    business_name: "Record Status",
    business_description: "Current status of supplier record (Active, Inactive, Suspended, Terminated). Controls ability to place orders. Suspended may indicate quality or compliance issues. Historical records retained for analysis.",
    business_synonyms: ["Supplier Status", "Vendor Status", "Active Status", "Record State", "Account Status", "Master Status"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "critical"
}),
(created_timestamp:Column {
    column_id: "COL_CREATED_TIMESTAMP_212",
    column_name: "CREATED_TIMESTAMP",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 63,
    business_name: "Record Created Timestamp",
    business_description: "System timestamp when manufacturer record was created. Used for audit trails and data lineage. Immutable after creation. May differ from supplier relationship start date.",
    business_synonyms: ["Creation Time", "Insert Timestamp", "Created Date", "Record Creation", "Entry Timestamp", "Initial Load Time"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    business_criticality: "low"
}),
(modified_timestamp:Column {
    column_id: "COL_MODIFIED_TIMESTAMP_213",
    column_name: "MODIFIED_TIMESTAMP",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 64,
    business_name: "Last Modified Timestamp",
    business_description: "Most recent update timestamp for manufacturer record. Tracks data freshness and change frequency. Updated automatically with any field modification. Used for incremental processing.",
    business_synonyms: ["Update Time", "Last Modified", "Change Timestamp", "Modified Date", "Last Updated", "Revision Timestamp"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    business_criticality: "low"
}),
(data_source:Column {
    column_id: "COL_DATA_SOURCE_214",
    column_name: "DATA_SOURCE",
    table_id: "DIM_MANUFACTURER",
    ordinal_position: 65,
    business_name: "Data Source System",
    business_description: "Source system for manufacturer master data (ERP, Supplier Portal, MDM). Used for data quality troubleshooting and reconciliation. Critical during system migrations and integrations.",
    business_synonyms: ["Source System", "Origin System", "Master Source", "Data Origin", "Source Application", "MDM Source"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "low"
});

// ========================================
// CREATE TABLE-COLUMN RELATIONSHIPS
// ========================================

MATCH (t:Table {table_id: "DIM_MANUFACTURER"})
MATCH (c:Column {table_id: "DIM_MANUFACTURER"})
CREATE (t)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(c);

// ========================================
// VERIFICATION QUERIES
// ========================================

// Count total columns created
MATCH (c:Column {table_id: "DIM_MANUFACTURER"})
RETURN count(c) AS total_columns;

// Verify columns by domain specificity
MATCH (c:Column {table_id: "DIM_MANUFACTURER"})
RETURN c.domain_specific AS is_domain_specific, 
       count(c) AS column_count
ORDER BY is_domain_specific;

// List domain-specific columns with their applicable domains
MATCH (c:Column {table_id: "DIM_MANUFACTURER"})
WHERE c.domain_specific = true
RETURN c.business_name AS column_name, 
       c.applicable_domains AS domains
ORDER BY c.ordinal_position;

// Verify critical columns
MATCH (c:Column {table_id: "DIM_MANUFACTURER"})
WHERE c.business_criticality = "critical"
RETURN c.business_name AS critical_column, 
       c.business_criticality
ORDER BY c.ordinal_position;

// Check columns by category
MATCH (c:Column {table_id: "DIM_MANUFACTURER"})
RETURN 
    CASE 
        WHEN c.ordinal_position <= 6 THEN "Core Identifiers"
        WHEN c.ordinal_position <= 11 THEN "Company Profile"
        WHEN c.ordinal_position <= 15 THEN "Geographic/Facility"
        WHEN c.ordinal_position <= 22 THEN "Regulatory/Compliance"
        WHEN c.ordinal_position <= 28 THEN "Quality/Performance"
        WHEN c.ordinal_position <= 33 THEN "Financial/Commercial"
        WHEN c.ordinal_position <= 38 THEN "Supply Chain"
        WHEN c.ordinal_position <= 42 THEN "Technology/Innovation"
        WHEN c.ordinal_position <= 46 THEN "Sustainability/ESG"
        WHEN c.ordinal_position <= 50 THEN "Risk/Compliance"
        WHEN c.ordinal_position <= 54 THEN "Relationship Mgmt"
        WHEN c.ordinal_position <= 57 THEN "Contacts"
        WHEN c.ordinal_position <= 60 THEN "Diversity"
        ELSE "Metadata"
    END AS category,
    count(c) AS column_count
ORDER BY category;

// ========================================
// END OF DIM_MANUFACTURER COLUMN CREATION
// ========================================