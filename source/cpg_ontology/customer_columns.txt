// ========================================
// DIM_CUSTOMER - COMPREHENSIVE COLUMN CREATION
// Complete customer dimension columns for all CPG domains
// ========================================

// Clear existing columns for DIM_CUSTOMER (optional)
MATCH (c:Column {table_id: "DIM_CUSTOMER"}) DETACH DELETE c;

// ========================================
// CORE CUSTOMER IDENTIFIERS AND BASIC ATTRIBUTES
// ========================================

CREATE 
(customer_id:Column {
    column_id: "COL_CUSTOMER_ID_DIM_501",
    column_name: "CUSTOMER_ID",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 1,
    business_name: "Customer ID",
    business_description: "Unique identifier for individual customers across all touchpoints and channels. Critical for CPG customer analytics, personalized marketing, loyalty program management, and cross-category purchase behavior analysis. Enables 360-degree customer view across all product categories.",
    business_synonyms: ["Customer Key", "Customer Number", "Consumer ID", "Shopper ID", "Customer Reference", "Customer Code", "Customer SK", "Customer Index"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_primary_key: true,
    business_criticality: "critical"
}),

(customer_number:Column {
    column_id: "COL_CUSTOMER_NUMBER_502",
    column_name: "CUSTOMER_NUMBER",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 2,
    business_name: "Customer Number",
    business_description: "Sequential or formatted customer number used in customer service and loyalty programs. May differ from Customer ID. Critical for customer service interactions, loyalty card programs, and customer communications in CPG retail environments.",
    business_synonyms: ["Customer Num", "Account Number", "Membership Number", "Customer Code", "Loyalty Number", "Consumer Number", "Customer #"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "high"
}),

// ========================================
// PERSONAL INFORMATION
// ========================================

(first_name:Column {
    column_id: "COL_FIRST_NAME_503",
    column_name: "FIRST_NAME",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 3,
    business_name: "First Name",
    business_description: "Customer's first name for personalization and customer service. Essential for CPG personalized marketing campaigns, customer service interactions, and creating personalized shopping experiences across all product categories. Enables name-based segmentation.",
    business_synonyms: ["Given Name", "First", "Personal Name", "Christian Name", "Forename", "Primary Name"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "medium"
}),

(last_name:Column {
    column_id: "COL_LAST_NAME_504",
    column_name: "LAST_NAME",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 4,
    business_name: "Last Name",
    business_description: "Customer's last name for identification and personalization. Important for CPG customer service, account management, and household-level analysis. Enables family unit identification and household product consumption patterns analysis.",
    business_synonyms: ["Surname", "Last", "Family Name", "Lastname", "Family Surname", "Household Name"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "medium"
}),

(full_name:Column {
    column_id: "COL_FULL_NAME_505",
    column_name: "FULL_NAME",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 5,
    business_name: "Full Name",
    business_description: "Customer's complete name for display and reporting purposes. Used in CPG executive reports, customer service systems, and personalized communications. Critical for customer recognition and account management.",
    business_synonyms: ["Complete Name", "Display Name", "Customer Name", "Full Customer Name", "Name Display", "Customer Full Name"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "medium"
}),

(email_address:Column {
    column_id: "COL_EMAIL_ADDRESS_506",
    column_name: "EMAIL_ADDRESS",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 6,
    business_name: "Email Address",
    business_description: "Primary email address for digital marketing and communications. Critical for CPG email marketing campaigns, product recalls, promotional offers, and digital engagement across all product categories. Key channel for customer retention.",
    business_synonyms: ["Email", "E-mail", "Electronic Mail", "Email Contact", "Digital Contact", "Online Contact", "Email ID"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 150,
    business_criticality: "high"
}),

(phone_number:Column {
    column_id: "COL_PHONE_NUMBER_507",
    column_name: "PHONE_NUMBER",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 7,
    business_name: "Phone Number",
    business_description: "Primary phone number for customer service and emergency communications. Essential for CPG customer service, product recalls, loyalty program support, and SMS marketing campaigns. Critical communication channel for urgent notifications.",
    business_synonyms: ["Phone", "Telephone", "Contact Number", "Mobile Number", "Cell Phone", "Primary Phone", "Phone Contact"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "high"
}),

(date_of_birth:Column {
    column_id: "COL_DATE_OF_BIRTH_508",
    column_name: "DATE_OF_BIRTH",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 8,
    business_name: "Date of Birth",
    business_description: "Customer's birth date for age-based segmentation and lifecycle marketing. Critical for CPG age-appropriate product recommendations, lifecycle stage analysis, and compliance with age-restricted products like alcoholic beverages and pharmaceuticals.",
    business_synonyms: ["Birth Date", "DOB", "Birthday", "Birth Day", "Date Born", "Birth Date", "Age Date"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    max_length: null,
    business_criticality: "medium"
}),

(age:Column {
    column_id: "COL_AGE_509",
    column_name: "AGE",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 9,
    business_name: "Current Age",
    business_description: "Customer's current age calculated from birth date. Essential for CPG age-based marketing, product recommendations (baby products, senior care), age-restricted categories (alcohol, pharmaceuticals), and generational analysis across product categories.",
    business_synonyms: ["Age Years", "Customer Age", "Years Old", "Current Age", "Age in Years", "Chronological Age"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "high"
}),

(gender:Column {
    column_id: "COL_GENDER_510",
    column_name: "GENDER",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 10,
    business_name: "Gender",
    business_description: "Customer's gender for demographic analysis and targeted marketing. Important for CPG gender-specific products (cosmetics, personal care), marketing personalization, and understanding consumption patterns across categories like health supplements and personal care.",
    business_synonyms: ["Sex", "Gender Identity", "Gender Classification", "M/F", "Gender Type", "Gender Category"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "medium"
}),

// ========================================
// GEOGRAPHIC INFORMATION
// ========================================

(address_line_1:Column {
    column_id: "COL_ADDRESS_LINE_1_511",
    column_name: "ADDRESS_LINE_1",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 11,
    business_name: "Address Line 1",
    business_description: "Primary street address for customer location and delivery. Critical for CPG geographic analysis, delivery services, local marketing, trade area analysis, and understanding regional product preferences across all categories.",
    business_synonyms: ["Street Address", "Primary Address", "Address 1", "Street Line 1", "Physical Address", "Home Address", "Mailing Address"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 200,
    business_criticality: "high"
}),

(address_line_2:Column {
    column_id: "COL_ADDRESS_LINE_2_512",
    column_name: "ADDRESS_LINE_2",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 12,
    business_name: "Address Line 2",
    business_description: "Secondary address information (apartment, suite, unit number). Important for CPG delivery accuracy and customer service. Helps ensure proper delivery of products and promotional materials.",
    business_synonyms: ["Address 2", "Secondary Address", "Apartment Number", "Suite", "Unit Number", "Additional Address", "Address Supplement"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "low"
}),

(city:Column {
    column_id: "COL_CITY_513",
    column_name: "CITY",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 13,
    business_name: "City",
    business_description: "Customer's city for geographic segmentation and local marketing. Essential for CPG regional analysis, city-level promotions, understanding urban vs suburban preferences, and aligning with local store marketing strategies.",
    business_synonyms: ["Town", "Municipality", "City Name", "Locality", "Urban Area", "Metro Area", "Customer City"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "medium"
}),

(state_province:Column {
    column_id: "COL_STATE_PROVINCE_514",
    column_name: "STATE_PROVINCE",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 14,
    business_name: "State or Province",
    business_description: "Customer's state or province for regional analysis and regulatory compliance. Critical for CPG state-specific regulations (alcohol laws, tax rates), regional preferences analysis, and territory management across all product categories.",
    business_synonyms: ["State", "Province", "Region", "State Code", "Provincial Code", "Administrative Division", "Customer State"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high"
}),

(zip_code:Column {
    column_id: "COL_ZIP_CODE_515",
    column_name: "ZIP_CODE",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 15,
    business_name: "ZIP Code",
    business_description: "Customer's postal code for demographic overlays and micro-targeting. Enables CPG demographic analysis, income-based segmentation, trade area analysis, and targeted promotions based on local demographics and preferences.",
    business_synonyms: ["Postal Code", "ZIP", "Post Code", "Mailing Code", "ZIP+4", "Postal District", "Customer ZIP"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 15,
    business_criticality: "medium"
}),

(country:Column {
    column_id: "COL_COUNTRY_516",
    column_name: "COUNTRY",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 16,
    business_name: "Country",
    business_description: "Customer's country for international analysis and regulatory compliance. Essential for CPG global operations, country-specific regulations, cultural preferences, and international marketing strategies across all product categories.",
    business_synonyms: ["Nation", "Country Code", "Country Name", "Territory", "National Market", "Customer Country"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "high"
}),

(latitude:Column {
    column_id: "COL_LATITUDE_517",
    column_name: "LATITUDE",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 17,
    business_name: "Latitude",
    business_description: "Geographic latitude coordinate for spatial analysis and location-based services. Enables CPG proximity analysis to stores, delivery route optimization, location-based marketing, and understanding geographic consumption patterns.",
    business_synonyms: ["Lat", "Y Coordinate", "Latitude Coordinate", "Geographic Latitude", "GPS Latitude", "Customer Latitude"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 6,
    business_criticality: "low"
}),

(longitude:Column {
    column_id: "COL_LONGITUDE_518",
    column_name: "LONGITUDE",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 18,
    business_name: "Longitude",
    business_description: "Geographic longitude coordinate for spatial analysis and mapping. Paired with latitude for complete customer positioning, delivery optimization, store proximity analysis, and location-based promotions in CPG marketing.",
    business_synonyms: ["Long", "Lng", "X Coordinate", "Longitude Coordinate", "Geographic Longitude", "GPS Longitude", "Customer Longitude"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 11,
    scale: 6,
    business_criticality: "low"
}),

// ========================================
// CUSTOMER CLASSIFICATION AND SEGMENTATION
// ========================================

(customer_type:Column {
    column_id: "COL_CUSTOMER_TYPE_519",
    column_name: "CUSTOMER_TYPE",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 19,
    business_name: "Customer Type",
    business_description: "Primary customer classification (Individual, Business, Healthcare Provider, etc.). Critical for CPG B2B vs B2C segmentation, especially important for pharmaceuticals (healthcare providers), food service (business customers), and retail vs institutional sales.",
    business_synonyms: ["Customer Class", "Customer Category", "Account Type", "Customer Classification", "Customer Segment Type", "Customer Group"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "high"
}),

(customer_segment:Column {
    column_id: "COL_CUSTOMER_SEGMENT_520",
    column_name: "CUSTOMER_SEGMENT",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 20,
    business_name: "Customer Segment",
    business_description: "Strategic customer segmentation based on behavior, value, and preferences. Essential for CPG targeted marketing, product development, pricing strategies, and resource allocation across premium, value, and mass market segments.",
    business_synonyms: ["Market Segment", "Customer Group", "Behavioral Segment", "Value Segment", "Customer Cluster", "Target Segment"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high"
}),

(customer_tier:Column {
    column_id: "COL_CUSTOMER_TIER_521",
    column_name: "CUSTOMER_TIER",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 21,
    business_name: "Customer Tier",
    business_description: "Value-based customer tier (Platinum, Gold, Silver, Bronze) for service level and benefits. Important for CPG loyalty programs, personalized offers, priority customer service, and premium product access across all categories.",
    business_synonyms: ["Value Tier", "Loyalty Tier", "Customer Level", "Service Tier", "VIP Level", "Membership Tier", "Customer Rank"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "medium"
}),

(lifecycle_stage:Column {
    column_id: "COL_LIFECYCLE_STAGE_522",
    column_name: "LIFECYCLE_STAGE",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 22,
    business_name: "Customer Lifecycle Stage",
    business_description: "Current stage in customer lifecycle (Prospect, New, Active, At-Risk, Inactive, Churned). Critical for CPG retention strategies, win-back campaigns, and lifecycle-appropriate product recommendations across all categories.",
    business_synonyms: ["Lifecycle Phase", "Customer Stage", "Relationship Stage", "Customer Status", "Engagement Stage", "Journey Stage"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "high"
}),

// ========================================
// SHOPPING BEHAVIOR AND PREFERENCES
// ========================================

(preferred_channel:Column {
    column_id: "COL_PREFERRED_CHANNEL_523",
    column_name: "PREFERRED_CHANNEL",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 23,
    business_name: "Preferred Shopping Channel",
    business_description: "Customer's primary shopping channel (Online, In-Store, Mobile, Mixed). Essential for CPG omnichannel strategy, channel-specific promotions, inventory allocation, and understanding digital vs traditional shopping preferences.",
    business_synonyms: ["Primary Channel", "Shopping Channel", "Purchase Channel", "Preferred Store Type", "Channel Preference", "Shopping Method"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "high"
}),

(shopping_frequency:Column {
    column_id: "COL_SHOPPING_FREQUENCY_524",
    column_name: "SHOPPING_FREQUENCY",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 24,
    business_name: "Shopping Frequency",
    business_description: "How often customer shops (Daily, Weekly, Monthly, Occasionally). Important for CPG inventory planning, promotional timing, stock-up deals, and understanding purchase patterns across fast-moving vs durable categories.",
    business_synonyms: ["Purchase Frequency", "Shopping Pattern", "Buy Frequency", "Visit Frequency", "Shopping Cadence", "Purchase Pattern"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "medium"
}),

(price_sensitivity:Column {
    column_id: "COL_PRICE_SENSITIVITY_525",
    column_name: "PRICE_SENSITIVITY",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 25,
    business_name: "Price Sensitivity Level",
    business_description: "Customer's price sensitivity classification (High, Medium, Low). Critical for CPG pricing strategies, promotional targeting, private label recommendations, and premium vs value product positioning across all categories.",
    business_synonyms: ["Price Elasticity", "Deal Sensitivity", "Discount Sensitivity", "Value Orientation", "Price Consciousness", "Budget Sensitivity"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 15,
    business_criticality: "high"
}),

(brand_loyalty_level:Column {
    column_id: "COL_BRAND_LOYALTY_LEVEL_526",
    column_name: "BRAND_LOYALTY_LEVEL",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 26,
    business_name: "Brand Loyalty Level",
    business_description: "Customer's overall brand loyalty classification (Highly Loyal, Moderately Loyal, Switch-Prone, Price-Driven). Essential for CPG brand strategy, competitive defense, new product introductions, and loyalty-building programs.",
    business_synonyms: ["Loyalty Level", "Brand Affinity", "Switching Behavior", "Brand Stickiness", "Loyalty Index", "Brand Commitment"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "high"
}),

// ========================================
// LOYALTY PROGRAM AND ENGAGEMENT
// ========================================

(loyalty_program_member:Column {
    column_id: "COL_LOYALTY_PROGRAM_MEMBER_527",
    column_name: "LOYALTY_PROGRAM_MEMBER",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 27,
    business_name: "Loyalty Program Member Flag",
    business_description: "Indicates customer participation in loyalty programs. Critical for CPG personalized marketing, targeted promotions, reward redemption analysis, and member-exclusive benefits across all product categories.",
    business_synonyms: ["Loyalty Member", "Rewards Member", "Program Member", "Loyalty Participant", "Member Status", "Loyalty Flag"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "high"
}),

(loyalty_member_since:Column {
    column_id: "COL_LOYALTY_MEMBER_SINCE_528",
    column_name: "LOYALTY_MEMBER_SINCE",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 28,
    business_name: "Loyalty Member Since Date",
    business_description: "Date customer joined loyalty program for tenure analysis and anniversary marketing. Important for CPG member lifecycle analysis, tenure-based benefits, and understanding program adoption patterns.",
    business_synonyms: ["Member Since", "Join Date", "Enrollment Date", "Signup Date", "Registration Date", "Program Start Date"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    max_length: null,
    business_criticality: "medium"
}),

(loyalty_points_balance:Column {
    column_id: "COL_LOYALTY_POINTS_BALANCE_529",
    column_name: "LOYALTY_POINTS_BALANCE",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 29,
    business_name: "Loyalty Points Balance",
    business_description: "Current loyalty points balance for redemption analysis and engagement measurement. Essential for CPG reward programs, points expiration management, and incentivizing purchases across categories.",
    business_synonyms: ["Points Balance", "Rewards Balance", "Current Points", "Available Points", "Point Total", "Reward Points"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "medium"
}),

(engagement_score:Column {
    column_id: "COL_ENGAGEMENT_SCORE_530",
    column_name: "ENGAGEMENT_SCORE",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 30,
    business_name: "Customer Engagement Score",
    business_description: "Composite score measuring customer engagement across all touchpoints (0-100). Critical for CPG customer health monitoring, churn prediction, engagement campaigns, and prioritizing marketing investments.",
    business_synonyms: ["Engagement Index", "Customer Score", "Activity Score", "Interaction Score", "Engagement Rating", "Customer Health Score"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high"
}),

// ========================================
// PURCHASE HISTORY METRICS
// ========================================

(total_lifetime_value:Column {
    column_id: "COL_TOTAL_LIFETIME_VALUE_531",
    column_name: "TOTAL_LIFETIME_VALUE",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 31,
    business_name: "Total Lifetime Value",
    business_description: "Cumulative customer value across all purchases and categories. Essential for CPG customer prioritization, acquisition cost justification, retention investment decisions, and high-value customer identification.",
    business_synonyms: ["Customer LTV", "CLV", "Lifetime Revenue", "Total Value", "Customer Worth", "Lifetime Spend", "Total Customer Value"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 2,
    business_criticality: "critical"
}),

(average_order_value:Column {
    column_id: "COL_AVERAGE_ORDER_VALUE_532",
    column_name: "AVERAGE_ORDER_VALUE",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 32,
    business_name: "Average Order Value",
    business_description: "Customer's average transaction amount across all purchases. Important for CPG basket building strategies, cross-selling opportunities, pricing strategies, and identifying upselling potential across categories.",
    business_synonyms: ["AOV", "Average Basket", "Mean Order Size", "Average Purchase", "Average Transaction", "Basket Value"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 2,
    business_criticality: "high"
}),

(purchase_frequency_annual:Column {
    column_id: "COL_PURCHASE_FREQUENCY_ANNUAL_533",
    column_name: "PURCHASE_FREQUENCY_ANNUAL",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 33,
    business_name: "Annual Purchase Frequency",
    business_description: "Number of purchases per year for frequency-based segmentation. Critical for CPG replenishment programs, subscription services, inventory planning, and understanding consumption patterns across categories.",
    business_synonyms: ["Annual Frequency", "Yearly Purchases", "Purchase Rate", "Buy Frequency", "Transaction Frequency", "Annual Transactions"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 2,
    business_criticality: "medium"
}),

(last_purchase_date:Column {
    column_id: "COL_LAST_PURCHASE_DATE_534",
    column_name: "LAST_PURCHASE_DATE",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 34,
    business_name: "Last Purchase Date",
    business_description: "Date of customer's most recent purchase for recency analysis and churn identification. Essential for CPG win-back campaigns, reactivation programs, and identifying at-risk customers across all categories.",
    business_synonyms: ["Most Recent Purchase", "Latest Purchase", "Last Buy Date", "Recent Transaction", "Last Order Date", "Final Purchase"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    max_length: null,
    business_criticality: "high"
}),

(first_purchase_date:Column {
    column_id: "COL_FIRST_PURCHASE_DATE_535",
    column_name: "FIRST_PURCHASE_DATE",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 35,
    business_name: "First Purchase Date",
    business_description: "Date of customer's initial purchase for tenure analysis and customer acquisition tracking. Important for CPG customer lifecycle analysis, acquisition channel effectiveness, and new customer onboarding programs.",
    business_synonyms: ["Initial Purchase", "First Buy Date", "Original Purchase", "Acquisition Date", "First Transaction", "First Order"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    max_length: null,
    business_criticality: "medium"
}),

// ========================================
// DEMOGRAPHICS AND LIFESTYLE
// ========================================

(household_income:Column {
    column_id: "COL_HOUSEHOLD_INCOME_536",
    column_name: "HOUSEHOLD_INCOME",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 36,
    business_name: "Household Income",
    business_description: "Annual household income for economic segmentation and product targeting. Critical for CPG premium vs value positioning, price point optimization, and understanding purchasing power across different income segments.",
    business_synonyms: ["HH Income", "Annual Income", "Family Income", "Economic Level", "Income Level", "Financial Status", "Household Earnings"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 2,
    business_criticality: "medium"
}),

(income_bracket:Column {
    column_id: "COL_INCOME_BRACKET_537",
    column_name: "INCOME_BRACKET",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 37,
    business_name: "Income Bracket",
    business_description: "Income range classification for simplified segmentation. Important for CPG market sizing, product positioning strategies, and understanding economic accessibility across all product categories.",
    business_synonyms: ["Income Range", "Income Segment", "Economic Bracket", "Income Category", "Economic Class", "Income Tier"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "medium"
}),

(education_level:Column {
    column_id: "COL_EDUCATION_LEVEL_538",
    column_name: "EDUCATION_LEVEL",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 38,
    business_name: "Education Level",
    business_description: "Highest education level achieved for demographic analysis and product positioning. Important for CPG premium product acceptance, health-conscious choices, sustainability awareness, and sophisticated product messaging.",
    business_synonyms: ["Educational Attainment", "Education Background", "Academic Level", "School Level", "Educational Status", "Learning Level"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "low"
}),

(marital_status:Column {
    column_id: "COL_MARITAL_STATUS_539",
    column_name: "MARITAL_STATUS",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 39,
    business_name: "Marital Status",
    business_description: "Customer's marital status for household composition analysis. Important for CPG family-size packaging, household product needs, gift-giving occasions, and understanding family-oriented purchase patterns.",
    business_synonyms: ["Marriage Status", "Relationship Status", "Civil Status", "Family Status", "Partnership Status", "Marital State"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "low"
}),

(household_size:Column {
    column_id: "COL_HOUSEHOLD_SIZE_540",
    column_name: "HOUSEHOLD_SIZE",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 40,
    business_name: "Household Size",
    business_description: "Number of people in household for package sizing and volume targeting. Critical for CPG family pack sizing, bulk offerings, portion control products, and understanding consumption volume needs.",
    business_synonyms: ["Family Size", "HH Size", "Household Members", "People in Household", "Family Members", "Household Count"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "medium"
}),

(children_in_household:Column {
    column_id: "COL_CHILDREN_IN_HOUSEHOLD_541",
    column_name: "CHILDREN_IN_HOUSEHOLD",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 41,
    business_name: "Children in Household",
    business_description: "Number of children in household for family-oriented product targeting. Essential for CPG categories like baby products, toys, family snacks, child-safe products, and family-oriented marketing messages.",
    business_synonyms: ["Kids in House", "Number of Children", "Children Count", "Kids Count", "Family Children", "Household Kids"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "medium"
}),

(life_stage:Column {
    column_id: "COL_LIFE_STAGE_542",
    column_name: "LIFE_STAGE",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 42,
    business_name: "Life Stage",
    business_description: "Current life stage classification (Young Adult, Family Formation, Established Family, Empty Nester, Retiree). Critical for CPG lifecycle marketing, age-appropriate products, and understanding changing needs across categories.",
    business_synonyms: ["Lifecycle Stage", "Life Phase", "Demographic Stage", "Age Stage", "Family Stage", "Life Cycle"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "high"
}),

// ========================================
// DIGITAL ENGAGEMENT AND COMMUNICATION PREFERENCES
// ========================================

(email_opt_in:Column {
    column_id: "COL_EMAIL_OPT_IN_543",
    column_name: "EMAIL_OPT_IN",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 43,
    business_name: "Email Opt-In Flag",
    business_description: "Customer consent for email marketing communications. Critical for CPG email campaigns, product announcements, recall notifications, and digital marketing compliance across all product categories.",
    business_synonyms: ["Email Consent", "Email Permission", "Email Subscribe", "Email Agree", "Email Marketing Opt-In", "Email Flag"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "high"
}),

(sms_opt_in:Column {
    column_id: "COL_SMS_OPT_IN_544",
    column_name: "SMS_OPT_IN",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 44,
    business_name: "SMS Opt-In Flag",
    business_description: "Customer consent for SMS/text message marketing. Important for CPG mobile marketing campaigns, urgent product recalls, flash promotions, and immediate communication needs.",
    business_synonyms: ["Text Opt-In", "SMS Consent", "Mobile Opt-In", "Text Permission", "SMS Subscribe", "Mobile Marketing Flag"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium"
}),

(mobile_app_user:Column {
    column_id: "COL_MOBILE_APP_USER_545",
    column_name: "MOBILE_APP_USER",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 45,
    business_name: "Mobile App User Flag",
    business_description: "Indicates customer uses company mobile application. Essential for CPG mobile engagement, app-specific promotions, mobile commerce, and understanding digital adoption patterns.",
    business_synonyms: ["App User", "Mobile User", "App Member", "Digital User", "Mobile App Member", "App Flag"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium"
}),

(social_media_connected:Column {
    column_id: "COL_SOCIAL_MEDIA_CONNECTED_546",
    column_name: "SOCIAL_MEDIA_CONNECTED",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 46,
    business_name: "Social Media Connected Flag",
    business_description: "Customer has connected social media accounts for enhanced engagement. Important for CPG social media marketing, influencer programs, user-generated content, and social commerce initiatives.",
    business_synonyms: ["Social Connected", "Social Media Flag", "Social User", "Social Engagement", "Social Media Member", "Connected Flag"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "low"
}),

// ========================================
// CPG-SPECIFIC PREFERENCES AND BEHAVIORS
// ========================================

(organic_preference:Column {
    column_id: "COL_ORGANIC_PREFERENCE_547",
    column_name: "ORGANIC_PREFERENCE",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 47,
    business_name: "Organic Product Preference",
    business_description: "Customer preference for organic products across categories. Critical for CPG organic product targeting, premium positioning, health-conscious segments, and sustainability-focused marketing especially in food, personal care, and baby products.",
    business_synonyms: ["Organic Interest", "Natural Preference", "Organic Affinity", "Clean Label Preference", "Organic Buyer", "Health Preference"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "medium"
}),

(health_wellness_focus:Column {
    column_id: "COL_HEALTH_WELLNESS_FOCUS_548",
    column_name: "HEALTH_WELLNESS_FOCUS",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 48,
    business_name: "Health and Wellness Focus",
    business_description: "Customer's health and wellness orientation level. Essential for CPG health-focused products, functional foods, supplements, natural cosmetics, and wellness-oriented marketing across applicable categories.",
    business_synonyms: ["Wellness Focus", "Health Interest", "Wellness Orientation", "Health Conscious", "Wellness Priority", "Health Preference"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "medium"
}),

(sustainability_concern:Column {
    column_id: "COL_SUSTAINABILITY_CONCERN_549",
    column_name: "SUSTAINABILITY_CONCERN",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 49,
    business_name: "Sustainability Concern Level",
    business_description: "Customer's environmental and sustainability consciousness level. Important for CPG eco-friendly products, sustainable packaging, corporate responsibility messaging, and environmentally conscious segments across all categories.",
    business_synonyms: ["Environmental Concern", "Eco Consciousness", "Green Preference", "Sustainability Interest", "Environmental Priority", "Eco Friendly"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "medium"
}),

(premium_buyer_flag:Column {
    column_id: "COL_PREMIUM_BUYER_FLAG_550",
    column_name: "PREMIUM_BUYER_FLAG",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 50,
    business_name: "Premium Buyer Flag",
    business_description: "Indicates customer purchases premium/luxury products regularly. Critical for CPG premium product targeting, luxury line introductions, premium pricing strategies, and high-value customer identification.",
    business_synonyms: ["Luxury Buyer", "Premium Customer", "High-End Buyer", "Premium Flag", "Luxury Flag", "Upscale Buyer"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium"
}),

// ========================================
// DOMAIN-SPECIFIC ATTRIBUTES
// ========================================

(age_verification_status:Column {
    column_id: "COL_AGE_VERIFICATION_STATUS_551",
    column_name: "AGE_VERIFICATION_STATUS",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 51,
    business_name: "Age Verification Status",
    business_description: "Status of age verification for age-restricted products. Critical for CPG alcoholic beverages and certain pharmaceuticals compliance, ensuring legal sales and regulatory adherence. Must be current for restricted product access.",
    business_synonyms: ["Age Verified", "ID Verified", "Age Confirmation", "Legal Age Status", "Age Check Status", "Verification Flag"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "critical",
    regulatory_relevance: ["TTB", "FDA", "STATE_LIQUOR_BOARDS"]
}),

(prescription_insurance:Column {
    column_id: "COL_PRESCRIPTION_INSURANCE_552",
    column_name: "PRESCRIPTION_INSURANCE",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 52,
    business_name: "Prescription Insurance Flag",
    business_description: "Indicates customer has prescription drug insurance coverage. Important for pharmaceutical product access, pricing strategies, formulary compliance, and understanding healthcare coverage for prescription products.",
    business_synonyms: ["Rx Insurance", "Drug Coverage", "Pharmacy Insurance", "Health Insurance", "Prescription Coverage", "Medical Insurance"],
    applicable_domains: ["pharmaceuticals"],
    domain_specific: true,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "high"
}),

(parent_flag:Column {
    column_id: "COL_PARENT_FLAG_553",
    column_name: "PARENT_FLAG",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 53,
    business_name: "Parent Flag",
    business_description: "Indicates customer is a parent with children. Essential for CPG family-oriented products, toys, baby products, family snacks, child-safe products, and parental-focused marketing messages.",
    business_synonyms: ["Has Children", "Parent Status", "Family Parent", "Child Parent", "Parental Status", "Parent Indicator"],
    applicable_domains: ["toys", "baby_products", "food_beverage", "snacks", "dairy", "household_products", "personal_care"],
    domain_specific: true,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium"
}),

(pet_owner_flag:Column {
    column_id: "COL_PET_OWNER_FLAG_554",
    column_name: "PET_OWNER_FLAG",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 54,
    business_name: "Pet Owner Flag",
    business_description: "Indicates customer owns pets. Critical for pet food and pet care product targeting, cross-selling opportunities, and pet-related promotional campaigns. Enables pet-focused marketing segmentation.",
    business_synonyms: ["Has Pets", "Pet Parent", "Pet Household", "Animal Owner", "Pet Flag", "Pet Parent Flag"],
    applicable_domains: ["pet_food", "household_products"],
    domain_specific: true,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium"
}),

(dietary_restrictions:Column {
    column_id: "COL_DIETARY_RESTRICTIONS_555",
    column_name: "DIETARY_RESTRICTIONS",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 55,
    business_name: "Dietary Restrictions",
    business_description: "Customer's dietary restrictions and preferences (Gluten-Free, Vegan, Kosher, etc.). Essential for CPG food and beverage product targeting, specialized product recommendations, and inclusive product development.",
    business_synonyms: ["Diet Restrictions", "Food Allergies", "Dietary Needs", "Food Preferences", "Diet Requirements", "Nutritional Restrictions"],
    applicable_domains: ["food_beverage", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "medium"
}),

// ========================================
// ACCOUNT MANAGEMENT AND CUSTOMER SERVICE
// ========================================

(account_manager:Column {
    column_id: "COL_ACCOUNT_MANAGER_556",
    column_name: "ACCOUNT_MANAGER",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 56,
    business_name: "Account Manager",
    business_description: "Assigned account manager for high-value or business customers. Important for CPG B2B relationships, key account management, personalized service, and maintaining customer relationships across institutional and business channels.",
    business_synonyms: ["Customer Manager", "Account Rep", "Customer Rep", "Relationship Manager", "Account Executive", "Customer Success Manager"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "low"
}),

(customer_service_tier:Column {
    column_id: "COL_CUSTOMER_SERVICE_TIER_557",
    column_name: "CUSTOMER_SERVICE_TIER",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 57,
    business_name: "Customer Service Tier",
    business_description: "Service level tier for customer support (Standard, Premium, VIP). Determines response times, service channels, and support quality. Important for CPG customer experience differentiation and service resource allocation.",
    business_synonyms: ["Service Level", "Support Tier", "Service Class", "Support Level", "Service Priority", "Customer Care Level"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "medium"
}),

// ========================================
// PRIVACY AND CONSENT MANAGEMENT
// ========================================

(data_sharing_consent:Column {
    column_id: "COL_DATA_SHARING_CONSENT_558",
    column_name: "DATA_SHARING_CONSENT",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 58,
    business_name: "Data Sharing Consent",
    business_description: "Customer consent for data sharing with partners and third parties. Critical for CPG compliance with privacy regulations (GDPR, CCPA), partner marketing programs, and data monetization initiatives.",
    business_synonyms: ["Data Consent", "Privacy Consent", "Sharing Permission", "Data Permission", "Privacy Agreement", "Consent Flag"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "high"
}),

(privacy_preference:Column {
    column_id: "COL_PRIVACY_PREFERENCE_559",
    column_name: "PRIVACY_PREFERENCE",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 59,
    business_name: "Privacy Preference Level",
    business_description: "Customer's privacy preference setting (High, Medium, Low) affecting data usage and marketing. Important for CPG personalization balance, marketing frequency, and respecting customer privacy choices.",
    business_synonyms: ["Privacy Level", "Privacy Setting", "Data Privacy", "Privacy Choice", "Privacy Control", "Privacy Option"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 15,
    business_criticality: "medium"
}),

// ========================================
// CUSTOMER ACQUISITION AND JOURNEY
// ========================================

(acquisition_source:Column {
    column_id: "COL_ACQUISITION_SOURCE_560",
    column_name: "ACQUISITION_SOURCE",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 60,
    business_name: "Customer Acquisition Source",
    business_description: "Original channel or method customer was acquired (Organic Search, Social Media, Referral, Store Visit, etc.). Critical for CPG marketing ROI analysis, channel optimization, and understanding acquisition effectiveness.",
    business_synonyms: ["Acquisition Channel", "Source Channel", "Origin Source", "Customer Source", "Acquisition Method", "First Touch"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high"
}),

(referral_source:Column {
    column_id: "COL_REFERRAL_SOURCE_561",
    column_name: "REFERRAL_SOURCE",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 61,
    business_name: "Referral Source",
    business_description: "Specific referral source if customer was referred (Friend, Family, Influencer, etc.). Important for CPG word-of-mouth marketing, referral programs, and understanding social influence on customer acquisition.",
    business_synonyms: ["Referred By", "Referrer", "Recommendation Source", "Word of Mouth", "Referral Channel", "Referred From"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "medium"
}),

// ========================================
// CUSTOMER STATUS AND ACCOUNT INFORMATION
// ========================================

(customer_status:Column {
    column_id: "COL_CUSTOMER_STATUS_562",
    column_name: "CUSTOMER_STATUS",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 62,
    business_name: "Customer Account Status",
    business_description: "Current customer account status (Active, Inactive, Suspended, Closed). Critical for CPG customer management, marketing eligibility, service access, and account lifecycle management across all touchpoints.",
    business_synonyms: ["Account Status", "Customer State", "Account State", "Customer Active Status", "Account Active", "Customer Health"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "critical"
}),

(credit_standing:Column {
    column_id: "COL_CREDIT_STANDING_563",
    column_name: "CREDIT_STANDING",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 63,
    business_name: "Credit Standing",
    business_description: "Customer's credit worthiness classification for payment terms and credit limits. Important for CPG B2B customers, payment method eligibility, credit line approvals, and financial risk management.",
    business_synonyms: ["Credit Rating", "Credit Score", "Credit Status", "Credit Worthiness", "Financial Standing", "Credit Risk"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "medium"
}),

// ========================================
// METADATA AND AUDIT INFORMATION
// ========================================

(customer_created_date:Column {
    column_id: "COL_CUSTOMER_CREATED_DATE_564",
    column_name: "CUSTOMER_CREATED_DATE",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 64,
    business_name: "Customer Record Created Date",
    business_description: "Date when customer record was first created in the system. Used for customer tenure analysis, acquisition tracking, and understanding customer lifecycle from initial registration.",
    business_synonyms: ["Registration Date", "Signup Date", "Account Creation", "Customer Since", "Created Date", "Account Start Date"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    max_length: null,
    business_criticality: "medium"
}),

(created_timestamp:Column {
    column_id: "COL_CREATED_TIMESTAMP_565",
    column_name: "CREATED_TIMESTAMP",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 65,
    business_name: "Record Created Timestamp",
    business_description: "System timestamp when customer record was created. Used for audit trails, data lineage tracking, and data governance. Immutable after creation. Part of comprehensive data governance framework.",
    business_synonyms: ["Creation Time", "Insert Timestamp", "Created Date", "Record Creation", "Entry Timestamp", "Initial Load Time"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    max_length: null,
    business_criticality: "low"
}),

(modified_timestamp:Column {
    column_id: "COL_MODIFIED_TIMESTAMP_566",
    column_name: "MODIFIED_TIMESTAMP",
    table_id: "DIM_CUSTOMER",
    ordinal_position: 66,
    business_name: "Last Modified Timestamp",
    business_description: "Most recent update timestamp for customer record. Tracks data freshness, change frequency, and maintenance activities. Updated automatically with any field modification for data governance compliance.",
    business_synonyms: ["Update Time", "Last Modified", "Change Timestamp", "Modified Date", "Last Updated", "Revision Timestamp"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    max_length: null,
    business_criticality: "low"
});

// ========================================
// CREATE TABLE-COLUMN RELATIONSHIPS
// ========================================

MATCH (t:Table {table_id: "DIM_CUSTOMER"})
MATCH (c:Column {table_id: "DIM_CUSTOMER"})
CREATE (t)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"]
}]->(c);

// ========================================
// VERIFICATION QUERIES
// ========================================

// Count total columns created
MATCH (c:Column {table_id: "DIM_CUSTOMER"})
RETURN count(c) AS total_columns;

// Verify columns by business criticality
MATCH (c:Column {table_id: "DIM_CUSTOMER"})
RETURN c.business_criticality AS criticality_level, 
       count(c) AS column_count
ORDER BY criticality_level;

// List critical columns
MATCH (c:Column {table_id: "DIM_CUSTOMER"})
WHERE c.business_criticality = "critical"
RETURN c.business_name AS critical_column, 
       c.business_description
ORDER BY c.ordinal_position;

// Check domain-specific columns
MATCH (c:Column {table_id: "DIM_CUSTOMER"})
WHERE c.domain_specific = true
RETURN c.business_name AS domain_specific_column, 
       c.applicable_domains AS specific_domains,
       c.business_description
ORDER BY c.ordinal_position;

// Check columns by category
MATCH (c:Column {table_id: "DIM_CUSTOMER"})
RETURN 
    CASE 
        WHEN c.ordinal_position <= 2 THEN "Core Identifiers"
        WHEN c.ordinal_position <= 10 THEN "Personal Information"
        WHEN c.ordinal_position <= 18 THEN "Geographic Information"
        WHEN c.ordinal_position <= 22 THEN "Customer Classification"
        WHEN c.ordinal_position <= 26 THEN "Shopping Behavior & Preferences"
        WHEN c.ordinal_position <= 30 THEN "Loyalty & Engagement"
        WHEN c.ordinal_position <= 35 THEN "Purchase History Metrics"
        WHEN c.ordinal_position <= 42 THEN "Demographics & Lifestyle"
        WHEN c.ordinal_position <= 46 THEN "Digital Engagement"
        WHEN c.ordinal_position <= 50 THEN "CPG-Specific Preferences"
        WHEN c.ordinal_position <= 55 THEN "Domain-Specific Attributes"
        WHEN c.ordinal_position <= 57 THEN "Account Management"
        WHEN c.ordinal_position <= 59 THEN "Privacy & Consent"
        WHEN c.ordinal_position <= 61 THEN "Acquisition & Journey"
        WHEN c.ordinal_position <= 63 THEN "Customer Status & Account"
        ELSE "Metadata & Audit"
    END AS category,
    count(c) AS column_count
ORDER BY category;

// Verify semantic types distribution
MATCH (c:Column {table_id: "DIM_CUSTOMER"})
RETURN c.semantic_type AS semantic_type, 
       count(c) AS column_count
ORDER BY column_count DESC;

// Check for primary key
MATCH (c:Column {table_id: "DIM_CUSTOMER"})
WHERE c.is_primary_key = true
RETURN c.business_name AS primary_key_column, 
       c.column_name AS column_name;

// Verify regulatory relevance columns
MATCH (c:Column {table_id: "DIM_CUSTOMER"})
WHERE c.regulatory_relevance IS NOT NULL
RETURN c.business_name AS regulatory_column, 
       c.regulatory_relevance AS regulations,
       c.applicable_domains AS domains
ORDER BY c.ordinal_position;

// Check flag/boolean columns
MATCH (c:Column {table_id: "DIM_CUSTOMER"})
WHERE c.data_type = "BOOLEAN"
RETURN c.business_name AS boolean_column,
       c.business_description
ORDER BY c.ordinal_position;

// ========================================
// END OF DIM_CUSTOMER COLUMN CREATION
// ========================================