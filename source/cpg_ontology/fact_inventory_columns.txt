// ========================================
// FACT_INVENTORY - COMPREHENSIVE COLUMN CREATION
// Complete inventory fact table columns for all CPG domains
// ========================================

// Clear existing columns for FACT_INVENTORY (optional)
MATCH (c:Column {table_id: "FACT_INVENTORY"}) DETACH DELETE c;

// ========================================
// CORE INVENTORY IDENTIFIERS AND FOREIGN KEYS
// ========================================

CREATE 
(inventory_snapshot_id:Column {
    column_id: "COL_INVENTORY_SNAPSHOT_ID_FACT_3001",
    column_name: "INVENTORY_SNAPSHOT_ID",
    table_id: "FACT_INVENTORY",
    ordinal_position: 1,
    business_name: "Inventory Snapshot ID",
    business_description: "Unique identifier for inventory snapshot records capturing stock levels at specific points in time across all CPG categories. Critical for inventory tracking, stock reconciliation, audit trails, and managing inventory positions across alcoholic beverages, pharmaceuticals, toys, cosmetics, and all consumer product categories.",
    business_synonyms: ["Snapshot ID", "Inventory ID", "Stock Record ID", "Inventory Key", "Snapshot Key", "Stock Position ID", "Inventory Reference"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_primary_key: true,
    business_criticality: "critical"
}),

(date_key:Column {
    column_id: "COL_DATE_KEY_FACT_3002",
    column_name: "DATE_KEY",
    table_id: "FACT_INVENTORY",
    ordinal_position: 2,
    business_name: "Date Key",
    business_description: "Foreign key to date dimension for temporal inventory analysis and stock level tracking. Essential for CPG inventory trend analysis, seasonality patterns, aging calculations, and understanding inventory dynamics over time across all product categories.",
    business_synonyms: ["Inventory Date Key", "Snapshot Date Key", "Date FK", "Date Reference", "Temporal Key", "Stock Date Key"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "INTEGER",
    max_length: null,
    is_foreign_key: true,
    business_criticality: "critical"
}),

(store_key:Column {
    column_id: "COL_STORE_KEY_FACT_3003",
    column_name: "STORE_KEY",
    table_id: "FACT_INVENTORY",
    ordinal_position: 3,
    business_name: "Store Key",
    business_description: "Foreign key to store dimension for location-based inventory tracking and warehouse management. Critical for CPG distributed inventory management, store-level stock optimization, regional inventory patterns, and facility-specific inventory control.",
    business_synonyms: ["Location Key", "Warehouse Key", "Store FK", "Site Key", "Location Reference", "Facility Key"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_foreign_key: true,
    business_criticality: "critical"
}),

(product_hierarchy_key:Column {
    column_id: "COL_PRODUCT_HIERARCHY_KEY_FACT_3004",
    column_name: "PRODUCT_HIERARCHY_KEY",
    table_id: "FACT_INVENTORY",
    ordinal_position: 4,
    business_name: "Product Hierarchy Key",
    business_description: "Foreign key to product hierarchy dimension for product-specific inventory tracking and SKU-level stock management. Essential for CPG product availability, SKU optimization, category inventory patterns, and brand stock management.",
    business_synonyms: ["Product Key", "SKU Key", "Product FK", "Item Key", "Product Reference", "Hierarchy FK"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_foreign_key: true,
    business_criticality: "critical"
}),

(supplier_key:Column {
    column_id: "COL_SUPPLIER_KEY_FACT_3005",
    column_name: "SUPPLIER_KEY",
    table_id: "FACT_INVENTORY",
    ordinal_position: 5,
    business_name: "Supplier Key",
    business_description: "Foreign key to supplier dimension for vendor-specific inventory tracking and supply chain management. Important for CPG supplier inventory monitoring, vendor managed inventory, consignment stock tracking, and supply chain collaboration.",
    business_synonyms: ["Vendor Key", "Supplier FK", "Vendor Reference", "Supplier ID FK", "Partner Key", "Source Key"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_foreign_key: true,
    business_criticality: "high"
}),

// ========================================
// CORE INVENTORY QUANTITY MEASURES
// ========================================

(on_hand_quantity:Column {
    column_id: "COL_ON_HAND_QUANTITY_FACT_3006",
    column_name: "ON_HAND_QUANTITY",
    table_id: "FACT_INVENTORY",
    ordinal_position: 6,
    business_name: "On Hand Quantity",
    business_description: "Physical inventory quantity currently in stock and on hand. Critical for CPG stock availability, inventory valuation, replenishment decisions, and ensuring product availability across all categories.",
    business_synonyms: ["Stock On Hand", "Physical Inventory", "Current Stock", "Available Inventory", "In Stock Quantity", "Physical Count"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 3,
    business_criticality: "critical",
    additive_type: "additive"
}),

(available_quantity:Column {
    column_id: "COL_AVAILABLE_QUANTITY_FACT_3007",
    column_name: "AVAILABLE_QUANTITY",
    table_id: "FACT_INVENTORY",
    ordinal_position: 7,
    business_name: "Available Quantity",
    business_description: "Inventory quantity available for sale after accounting for allocations and holds. Essential for CPG order promising, available-to-promise calculations, customer service levels, and preventing overselling.",
    business_synonyms: ["Available to Sell", "ATP Quantity", "Sellable Quantity", "Free Stock", "Unreserved Quantity", "Available Stock"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 3,
    business_criticality: "critical",
    additive_type: "additive"
}),

(allocated_quantity:Column {
    column_id: "COL_ALLOCATED_QUANTITY_FACT_3008",
    column_name: "ALLOCATED_QUANTITY",
    table_id: "FACT_INVENTORY",
    ordinal_position: 8,
    business_name: "Allocated Quantity",
    business_description: "Inventory quantity allocated to customer orders but not yet shipped. Important for CPG order fulfillment tracking, commitment management, and understanding true available inventory.",
    business_synonyms: ["Reserved Quantity", "Committed Stock", "Order Allocation", "Reserved Inventory", "Committed Quantity", "Allocated Stock"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 3,
    business_criticality: "high",
    additive_type: "additive"
}),

(in_transit_quantity:Column {
    column_id: "COL_IN_TRANSIT_QUANTITY_FACT_3009",
    column_name: "IN_TRANSIT_QUANTITY",
    table_id: "FACT_INVENTORY",
    ordinal_position: 9,
    business_name: "In Transit Quantity",
    business_description: "Inventory quantity currently in transit between locations. Critical for CPG supply chain visibility, total inventory management, transfer tracking, and pipeline inventory monitoring.",
    business_synonyms: ["Transit Stock", "Pipeline Inventory", "Shipping Quantity", "Transfer Quantity", "Moving Stock", "Transit Inventory"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 3,
    business_criticality: "high",
    additive_type: "additive"
}),

// ========================================
// INVENTORY VALUATION MEASURES
// ========================================

(inventory_value:Column {
    column_id: "COL_INVENTORY_VALUE_FACT_3010",
    column_name: "INVENTORY_VALUE",
    table_id: "FACT_INVENTORY",
    ordinal_position: 10,
    business_name: "Inventory Value",
    business_description: "Total monetary value of inventory at standard cost. Fundamental for CPG working capital management, financial reporting, inventory investment tracking, and balance sheet valuation.",
    business_synonyms: ["Stock Value", "Inventory Worth", "Total Value", "Stock Valuation", "Inventory Cost", "Asset Value"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 18,
    scale: 2,
    business_criticality: "critical",
    additive_type: "additive"
}),

(unit_cost:Column {
    column_id: "COL_UNIT_COST_FACT_3011",
    column_name: "UNIT_COST",
    table_id: "FACT_INVENTORY",
    ordinal_position: 11,
    business_name: "Unit Cost",
    business_description: "Standard cost per unit of inventory. Essential for CPG margin analysis, pricing decisions, cost tracking, and inventory valuation calculations.",
    business_synonyms: ["Standard Cost", "Per Unit Cost", "Item Cost", "Product Cost", "Unit Price", "Cost Per Unit"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 4,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(landed_cost:Column {
    column_id: "COL_LANDED_COST_FACT_3012",
    column_name: "LANDED_COST",
    table_id: "FACT_INVENTORY",
    ordinal_position: 12,
    business_name: "Landed Cost",
    business_description: "Total cost including product, freight, duties, and handling. Important for CPG true cost analysis, international trade management, margin calculations, and total cost of ownership.",
    business_synonyms: ["Total Cost", "Full Cost", "All-in Cost", "Complete Cost", "Delivered Cost", "True Cost"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 4,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(inventory_carrying_cost:Column {
    column_id: "COL_INVENTORY_CARRYING_COST_FACT_3013",
    column_name: "INVENTORY_CARRYING_COST",
    table_id: "FACT_INVENTORY",
    ordinal_position: 13,
    business_name: "Inventory Carrying Cost",
    business_description: "Cost of holding inventory including storage, insurance, and capital costs. Critical for CPG inventory optimization, working capital efficiency, cost reduction initiatives, and inventory investment decisions.",
    business_synonyms: ["Holding Cost", "Storage Cost", "Carrying Charge", "Inventory Cost", "Warehousing Cost", "Capital Cost"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 2,
    business_criticality: "medium",
    additive_type: "additive"
}),

// ========================================
// INVENTORY TURNOVER AND VELOCITY MEASURES
// ========================================

(inventory_turns:Column {
    column_id: "COL_INVENTORY_TURNS_FACT_3014",
    column_name: "INVENTORY_TURNS",
    table_id: "FACT_INVENTORY",
    ordinal_position: 14,
    business_name: "Inventory Turns",
    business_description: "Annual inventory turnover rate calculated as COGS divided by average inventory. Fundamental metric for CPG inventory efficiency, working capital management, product velocity analysis, and operational performance.",
    business_synonyms: ["Turnover Rate", "Inventory Turnover", "Stock Turns", "Rotation Rate", "Velocity Rate", "Turn Rate"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 8,
    scale: 2,
    business_criticality: "critical",
    additive_type: "non_additive"
}),

(days_on_hand:Column {
    column_id: "COL_DAYS_ON_HAND_FACT_3015",
    column_name: "DAYS_ON_HAND",
    table_id: "FACT_INVENTORY",
    ordinal_position: 15,
    business_name: "Days On Hand",
    business_description: "Number of days current inventory will last based on average daily usage. Essential for CPG stock coverage analysis, reorder planning, stockout prevention, and inventory optimization.",
    business_synonyms: ["DOH", "Days of Supply", "Inventory Days", "Stock Cover", "Days Coverage", "Supply Days"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 8,
    scale: 1,
    business_criticality: "critical",
    additive_type: "non_additive"
}),

(velocity_score:Column {
    column_id: "COL_VELOCITY_SCORE_FACT_3016",
    column_name: "VELOCITY_SCORE",
    table_id: "FACT_INVENTORY",
    ordinal_position: 16,
    business_name: "Velocity Score",
    business_description: "Composite score indicating product movement velocity (0-100). Important for CPG assortment decisions, slow-mover identification, inventory stratification, and product lifecycle management.",
    business_synonyms: ["Movement Score", "Activity Score", "Flow Score", "Speed Score", "Turnover Score", "Performance Score"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(sell_through_rate:Column {
    column_id: "COL_SELL_THROUGH_RATE_FACT_3017",
    column_name: "SELL_THROUGH_RATE",
    table_id: "FACT_INVENTORY",
    ordinal_position: 17,
    business_name: "Sell Through Rate",
    business_description: "Percentage of inventory sold within measurement period. Critical for CPG merchandising effectiveness, product performance, inventory productivity, and markdown planning.",
    business_synonyms: ["Sell Through %", "Sales Rate", "Movement Rate", "Depletion Rate", "Sales Velocity", "Throughput Rate"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

// ========================================
// STOCK STATUS AND AGING MEASURES
// ========================================

(safety_stock_quantity:Column {
    column_id: "COL_SAFETY_STOCK_QUANTITY_FACT_3018",
    column_name: "SAFETY_STOCK_QUANTITY",
    table_id: "FACT_INVENTORY",
    ordinal_position: 18,
    business_name: "Safety Stock Quantity",
    business_description: "Minimum inventory level maintained to prevent stockouts. Essential for CPG service level maintenance, risk mitigation, demand variability management, and customer satisfaction.",
    business_synonyms: ["Buffer Stock", "Minimum Stock", "Reserve Inventory", "Safety Buffer", "Protection Stock", "Minimum Level"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 3,
    business_criticality: "high",
    additive_type: "additive"
}),

(reorder_point_quantity:Column {
    column_id: "COL_REORDER_POINT_QUANTITY_FACT_3019",
    column_name: "REORDER_POINT_QUANTITY",
    table_id: "FACT_INVENTORY",
    ordinal_position: 19,
    business_name: "Reorder Point Quantity",
    business_description: "Inventory level triggering replenishment orders. Important for CPG automated ordering, supply chain efficiency, stockout prevention, and optimal inventory levels.",
    business_synonyms: ["ROP", "Order Point", "Trigger Level", "Reorder Level", "Order Trigger", "Replenishment Point"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 3,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(maximum_stock_quantity:Column {
    column_id: "COL_MAXIMUM_STOCK_QUANTITY_FACT_3020",
    column_name: "MAXIMUM_STOCK_QUANTITY",
    table_id: "FACT_INVENTORY",
    ordinal_position: 20,
    business_name: "Maximum Stock Quantity",
    business_description: "Maximum inventory level allowed based on storage capacity and policy. Critical for CPG warehouse utilization, overstock prevention, space optimization, and inventory control.",
    business_synonyms: ["Max Stock", "Upper Limit", "Stock Ceiling", "Maximum Level", "Capacity Limit", "Stock Maximum"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 3,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(stockout_flag:Column {
    column_id: "COL_STOCKOUT_FLAG_FACT_3021",
    column_name: "STOCKOUT_FLAG",
    table_id: "FACT_INVENTORY",
    ordinal_position: 21,
    business_name: "Stockout Flag",
    business_description: "Indicates product is currently out of stock. Essential for CPG availability tracking, lost sales analysis, service level monitoring, and supply chain performance measurement.",
    business_synonyms: ["Out of Stock", "OOS Flag", "Zero Stock", "Stock Depletion", "Availability Flag", "No Stock"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "critical",
    additive_type: "non_additive"
}),

// ========================================
// INVENTORY AGING AND EXPIRATION
// ========================================

(inventory_age_days:Column {
    column_id: "COL_INVENTORY_AGE_DAYS_FACT_3022",
    column_name: "INVENTORY_AGE_DAYS",
    table_id: "FACT_INVENTORY",
    ordinal_position: 22,
    business_name: "Inventory Age Days",
    business_description: "Number of days inventory has been in stock since receipt. Critical for CPG freshness management, FIFO compliance, obsolescence risk, and inventory quality control.",
    business_synonyms: ["Stock Age", "Aging Days", "Days in Stock", "Inventory Duration", "Age in Days", "Stock Duration"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(days_until_expiration:Column {
    column_id: "COL_DAYS_UNTIL_EXPIRATION_FACT_3023",
    column_name: "DAYS_UNTIL_EXPIRATION",
    table_id: "FACT_INVENTORY",
    ordinal_position: 23,
    business_name: "Days Until Expiration",
    business_description: "Number of days until product expiration or best-by date. Essential for perishable CPG management, food safety, pharmaceutical compliance, and waste prevention.",
    business_synonyms: ["Expiry Days", "Shelf Life Remaining", "Days to Expire", "Remaining Life", "Expiration Countdown", "Life Days"],
    applicable_domains: ["pharmaceuticals", "food_beverage", "personal_care", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products", "cosmetics"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "critical",
    additive_type: "non_additive"
}),

(expiration_risk_flag:Column {
    column_id: "COL_EXPIRATION_RISK_FLAG_FACT_3024",
    column_name: "EXPIRATION_RISK_FLAG",
    table_id: "FACT_INVENTORY",
    ordinal_position: 24,
    business_name: "Expiration Risk Flag",
    business_description: "Indicates inventory at risk of expiring within critical timeframe. Important for CPG waste reduction, markdown planning, donation programs, and proactive inventory management.",
    business_synonyms: ["Expiry Risk", "Short Dated", "Near Expiry", "Aging Risk", "Expiration Alert", "Risk Flag"],
    applicable_domains: ["pharmaceuticals", "food_beverage", "personal_care", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products", "cosmetics"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(obsolete_inventory_value:Column {
    column_id: "COL_OBSOLETE_INVENTORY_VALUE_FACT_3025",
    column_name: "OBSOLETE_INVENTORY_VALUE",
    table_id: "FACT_INVENTORY",
    ordinal_position: 25,
    business_name: "Obsolete Inventory Value",
    business_description: "Value of inventory deemed obsolete or unsaleable. Critical for CPG write-off planning, working capital impact, inventory quality metrics, and financial provision calculations.",
    business_synonyms: ["Dead Stock Value", "Obsolete Value", "Write-off Value", "Unsaleable Value", "Dead Inventory", "Obsolescence Value"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 2,
    business_criticality: "high",
    additive_type: "additive"
}),

// ========================================
// WAREHOUSE AND LOCATION MEASURES
// ========================================

(warehouse_capacity_utilization:Column {
    column_id: "COL_WAREHOUSE_CAPACITY_UTIL_FACT_3026",
    column_name: "WAREHOUSE_CAPACITY_UTILIZATION",
    table_id: "FACT_INVENTORY",
    ordinal_position: 26,
    business_name: "Warehouse Capacity Utilization",
    business_description: "Percentage of warehouse capacity currently utilized by inventory. Essential for CPG space optimization, capacity planning, expansion decisions, and operational efficiency.",
    business_synonyms: ["Space Utilization", "Capacity Usage", "Warehouse Fill Rate", "Storage Utilization", "Space Usage", "Capacity %"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(location_count:Column {
    column_id: "COL_LOCATION_COUNT_FACT_3027",
    column_name: "LOCATION_COUNT",
    table_id: "FACT_INVENTORY",
    ordinal_position: 27,
    business_name: "Location Count",
    business_description: "Number of distinct storage locations holding this inventory. Important for CPG inventory fragmentation analysis, picking efficiency, consolidation opportunities, and warehouse optimization.",
    business_synonyms: ["Storage Locations", "Bin Count", "Location Quantity", "Storage Points", "Pick Locations", "Storage Count"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "low",
    additive_type: "additive"
}),

(cross_dock_quantity:Column {
    column_id: "COL_CROSS_DOCK_QUANTITY_FACT_3028",
    column_name: "CROSS_DOCK_QUANTITY",
    table_id: "FACT_INVENTORY",
    ordinal_position: 28,
    business_name: "Cross Dock Quantity",
    business_description: "Inventory quantity designated for cross-docking without storage. Critical for CPG supply chain velocity, reduced handling costs, faster fulfillment, and operational efficiency.",
    business_synonyms: ["Flow Through Quantity", "Direct Ship", "Cross Dock Stock", "Transit Quantity", "Flow Through", "Direct Transfer"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 3,
    business_criticality: "medium",
    additive_type: "additive"
}),

// ========================================
// DOMAIN-SPECIFIC INVENTORY - ALCOHOLIC BEVERAGES
// ========================================

(bonded_inventory_quantity:Column {
    column_id: "COL_BONDED_INVENTORY_QUANTITY_FACT_3029",
    column_name: "BONDED_INVENTORY_QUANTITY",
    table_id: "FACT_INVENTORY",
    ordinal_position: 29,
    business_name: "Bonded Inventory Quantity",
    business_description: "Inventory held in bonded warehouses pending tax payment. Critical for alcoholic beverage tax management, cash flow optimization, duty deferral, and regulatory compliance.",
    business_synonyms: ["Tax Suspended Stock", "Bonded Stock", "Duty Free Inventory", "Tax Deferred", "Bonded Warehouse Stock", "Excise Suspended"],
    applicable_domains: ["alcoholic_beverages"],
    domain_specific: true,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 3,
    business_criticality: "high",
    additive_type: "additive",
    regulatory_relevance: ["TTB", "CUSTOMS"]
}),

(vintage_year:Column {
    column_id: "COL_VINTAGE_YEAR_FACT_3030",
    column_name: "VINTAGE_YEAR",
    table_id: "FACT_INVENTORY",
    ordinal_position: 30,
    business_name: "Vintage Year",
    business_description: "Production year for wine and aged spirits inventory. Essential for alcoholic beverage portfolio management, pricing strategy, aging programs, and premium product tracking.",
    business_synonyms: ["Production Year", "Harvest Year", "Wine Year", "Aging Year", "Bottling Year", "Distillation Year"],
    applicable_domains: ["alcoholic_beverages"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(proof_gallons:Column {
    column_id: "COL_PROOF_GALLONS_FACT_3031",
    column_name: "PROOF_GALLONS",
    table_id: "FACT_INVENTORY",
    ordinal_position: 31,
    business_name: "Proof Gallons",
    business_description: "Inventory measured in proof gallons for tax calculation. Important for alcoholic beverage tax reporting, TTB compliance, excise tax calculations, and regulatory inventory reporting.",
    business_synonyms: ["Tax Gallons", "Proof Volume", "Alcohol Volume", "Tax Units", "Proof Measure", "Excise Units"],
    applicable_domains: ["alcoholic_beverages"],
    domain_specific: true,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 3,
    business_criticality: "critical",
    additive_type: "additive",
    regulatory_relevance: ["TTB", "IRS"]
}),

// ========================================
// DOMAIN-SPECIFIC INVENTORY - PHARMACEUTICALS
// ========================================

(lot_number:Column {
    column_id: "COL_LOT_NUMBER_FACT_3032",
    column_name: "LOT_NUMBER",
    table_id: "FACT_INVENTORY",
    ordinal_position: 32,
    business_name: "Lot Number",
    business_description: "Manufacturing lot or batch number for traceability. Critical for pharmaceutical recalls, quality tracking, GMP compliance, and supply chain traceability across regulated products.",
    business_synonyms: ["Batch Number", "Lot Code", "Production Lot", "Manufacturing Batch", "Batch ID", "Lot Identifier"],
    applicable_domains: ["pharmaceuticals", "food_beverage", "cosmetics", "personal_care", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "critical",
    regulatory_relevance: ["FDA", "EMA"]
}),

(controlled_substance_flag:Column {
    column_id: "COL_CONTROLLED_SUBSTANCE_FLAG_FACT_3033",
    column_name: "CONTROLLED_SUBSTANCE_FLAG",
    table_id: "FACT_INVENTORY",
    ordinal_position: 33,
    business_name: "Controlled Substance Flag",
    business_description: "Indicates inventory contains DEA controlled substances. Essential for pharmaceutical security requirements, DEA reporting, special handling procedures, and regulatory compliance.",
    business_synonyms: ["DEA Controlled", "Scheduled Drug", "Controlled Drug", "Regulated Substance", "DEA Flag", "Schedule Flag"],
    applicable_domains: ["pharmaceuticals"],
    domain_specific: true,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "critical",
    additive_type: "non_additive",
    regulatory_relevance: ["DEA", "FDA"]
}),

(cold_chain_flag:Column {
    column_id: "COL_COLD_CHAIN_FLAG_FACT_3034",
    column_name: "COLD_CHAIN_FLAG",
    table_id: "FACT_INVENTORY",
    ordinal_position: 34,
    business_name: "Cold Chain Flag",
    business_description: "Indicates inventory requires cold chain temperature control. Important for pharmaceutical efficacy, vaccine management, biologics handling, and temperature-sensitive product integrity.",
    business_synonyms: ["Temperature Controlled", "Refrigerated", "Cold Storage", "Temperature Sensitive", "Climate Controlled", "Cooler Required"],
    applicable_domains: ["pharmaceuticals", "food_beverage", "dairy", "frozen_foods", "health_supplements"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "critical",
    additive_type: "non_additive"
}),

// ========================================
// QUALITY AND COMPLIANCE MEASURES
// ========================================

(quality_hold_quantity:Column {
    column_id: "COL_QUALITY_HOLD_QUANTITY_FACT_3035",
    column_name: "QUALITY_HOLD_QUANTITY",
    table_id: "FACT_INVENTORY",
    ordinal_position: 35,
    business_name: "Quality Hold Quantity",
    business_description: "Inventory quantity on hold for quality inspection or issues. Critical for CPG quality assurance, product safety, compliance management, and preventing defective product distribution.",
    business_synonyms: ["QA Hold", "Inspection Hold", "Quality Block", "Testing Hold", "QC Hold", "Hold Quantity"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 3,
    business_criticality: "high",
    additive_type: "additive"
}),

(quarantine_quantity:Column {
    column_id: "COL_QUARANTINE_QUANTITY_FACT_3036",
    column_name: "QUARANTINE_QUANTITY",
    table_id: "FACT_INVENTORY",
    ordinal_position: 36,
    business_name: "Quarantine Quantity",
    business_description: "Inventory isolated due to quality, safety, or regulatory concerns. Essential for CPG risk management, regulatory compliance, consumer safety, and contamination prevention.",
    business_synonyms: ["Isolated Stock", "Blocked Inventory", "Segregated Stock", "Restricted Inventory", "Isolation Quantity", "Blocked Quantity"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 3,
    business_criticality: "critical",
    additive_type: "additive"
}),

(damaged_quantity:Column {
    column_id: "COL_DAMAGED_QUANTITY_FACT_3037",
    column_name: "DAMAGED_QUANTITY",
    table_id: "FACT_INVENTORY",
    ordinal_position: 37,
    business_name: "Damaged Quantity",
    business_description: "Inventory quantity identified as damaged or defective. Important for CPG shrinkage tracking, quality metrics, claims management, and inventory accuracy.",
    business_synonyms: ["Defective Quantity", "Damaged Stock", "Broken Units", "Defect Quantity", "Damage Count", "Unsaleable Units"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 3,
    business_criticality: "medium",
    additive_type: "additive"
}),

// ========================================
// SUPPLY CHAIN AND REPLENISHMENT MEASURES
// ========================================

(lead_time_days:Column {
    column_id: "COL_LEAD_TIME_DAYS_FACT_3038",
    column_name: "LEAD_TIME_DAYS",
    table_id: "FACT_INVENTORY",
    ordinal_position: 38,
    business_name: "Lead Time Days",
    business_description: "Standard supplier lead time for inventory replenishment. Critical for CPG order planning, safety stock calculation, supply chain optimization, and service level management.",
    business_synonyms: ["Supplier Lead Time", "Order Lead Time", "Delivery Time", "Replenishment Time", "Supply Time", "Order Duration"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(on_order_quantity:Column {
    column_id: "COL_ON_ORDER_QUANTITY_FACT_3039",
    column_name: "ON_ORDER_QUANTITY",
    table_id: "FACT_INVENTORY",
    ordinal_position: 39,
    business_name: "On Order Quantity",
    business_description: "Quantity currently on purchase order but not yet received. Essential for CPG pipeline visibility, total supply planning, inbound tracking, and inventory projection.",
    business_synonyms: ["Open Orders", "Purchase Orders", "Incoming Stock", "Order Pipeline", "Pending Receipt", "PO Quantity"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 3,
    business_criticality: "high",
    additive_type: "additive"
}),

(backorder_quantity:Column {
    column_id: "COL_BACKORDER_QUANTITY_FACT_3040",
    column_name: "BACKORDER_QUANTITY",
    table_id: "FACT_INVENTORY",
    ordinal_position: 40,
    business_name: "Backorder Quantity",
    business_description: "Customer order quantity waiting for inventory availability. Important for CPG service level impact, lost sales risk, customer satisfaction, and replenishment priority.",
    business_synonyms: ["Back Orders", "Pending Orders", "Unfilled Orders", "Order Backlog", "Waiting Orders", "Delayed Orders"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 3,
    business_criticality: "critical",
    additive_type: "additive"
}),

// ========================================
// FORECAST AND PLANNING MEASURES
// ========================================

(forecast_quantity:Column {
    column_id: "COL_FORECAST_QUANTITY_FACT_3041",
    column_name: "FORECAST_QUANTITY",
    table_id: "FACT_INVENTORY",
    ordinal_position: 41,
    business_name: "Forecast Quantity",
    business_description: "Forecasted demand quantity for planning period. Critical for CPG demand planning, inventory optimization, production scheduling, and supply chain coordination.",
    business_synonyms: ["Demand Forecast", "Projected Demand", "Forecast Demand", "Planned Demand", "Expected Demand", "Forecast Units"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 3,
    business_criticality: "high",
    additive_type: "additive"
}),

(forecast_accuracy:Column {
    column_id: "COL_FORECAST_ACCURACY_FACT_3042",
    column_name: "FORECAST_ACCURACY",
    table_id: "FACT_INVENTORY",
    ordinal_position: 42,
    business_name: "Forecast Accuracy",
    business_description: "Percentage accuracy of demand forecast vs actual. Essential for CPG planning improvement, forecast model refinement, inventory optimization, and supply chain efficiency.",
    business_synonyms: ["Forecast Precision", "Planning Accuracy", "Forecast Performance", "Prediction Accuracy", "Forecast Quality", "Accuracy %"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(seasonal_index:Column {
    column_id: "COL_SEASONAL_INDEX_FACT_3043",
    column_name: "SEASONAL_INDEX",
    table_id: "FACT_INVENTORY",
    ordinal_position: 43,
    business_name: "Seasonal Index",
    business_description: "Index indicating seasonal demand patterns (100 = average). Important for CPG seasonal planning, inventory buildup, promotional timing, and capacity planning.",
    business_synonyms: ["Seasonality Factor", "Seasonal Factor", "Demand Index", "Seasonal Coefficient", "Season Index", "Seasonality Index"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 2,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

// ========================================
// INVENTORY ACCURACY AND COUNTING
// ========================================

(cycle_count_variance:Column {
    column_id: "COL_CYCLE_COUNT_VARIANCE_FACT_3044",
    column_name: "CYCLE_COUNT_VARIANCE",
    table_id: "FACT_INVENTORY",
    ordinal_position: 44,
    business_name: "Cycle Count Variance",
    business_description: "Difference between system and physical count quantities. Critical for CPG inventory accuracy, shrinkage identification, process improvement, and data integrity.",
    business_synonyms: ["Count Variance", "Physical Variance", "Inventory Variance", "Count Difference", "Accuracy Variance", "Stock Variance"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 3,
    business_criticality: "high",
    additive_type: "additive"
}),

(inventory_accuracy_percentage:Column {
    column_id: "COL_INVENTORY_ACCURACY_PCT_FACT_3045",
    column_name: "INVENTORY_ACCURACY_PERCENTAGE",
    table_id: "FACT_INVENTORY",
    ordinal_position: 45,
    business_name: "Inventory Accuracy Percentage",
    business_description: "Percentage accuracy of inventory records vs physical count. Essential for CPG operational excellence, system reliability, audit compliance, and continuous improvement.",
    business_synonyms: ["Record Accuracy", "Count Accuracy", "System Accuracy", "Inventory Precision", "Accuracy Rate", "Data Accuracy"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(last_count_date:Column {
    column_id: "COL_LAST_COUNT_DATE_FACT_3046",
    column_name: "LAST_COUNT_DATE",
    table_id: "FACT_INVENTORY",
    ordinal_position: 46,
    business_name: "Last Count Date",
    business_description: "Date of most recent physical inventory count. Important for CPG count scheduling, accuracy maintenance, audit requirements, and inventory control procedures.",
    business_synonyms: ["Count Date", "Physical Count Date", "Last Counted", "Cycle Count Date", "Verification Date", "Count Timestamp"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    max_length: null,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

// ========================================
// FINANCIAL AND COST MEASURES
// ========================================

(shrinkage_value:Column {
    column_id: "COL_SHRINKAGE_VALUE_FACT_3047",
    column_name: "SHRINKAGE_VALUE",
    table_id: "FACT_INVENTORY",
    ordinal_position: 47,
    business_name: "Shrinkage Value",
    business_description: "Monetary value of inventory shrinkage from theft, damage, or errors. Critical for CPG loss prevention, security effectiveness, process improvement, and financial impact analysis.",
    business_synonyms: ["Loss Value", "Shrink Value", "Inventory Loss", "Theft Value", "Missing Value", "Shrinkage Cost"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 2,
    business_criticality: "high",
    additive_type: "additive"
}),

(markdown_value:Column {
    column_id: "COL_MARKDOWN_VALUE_FACT_3048",
    column_name: "MARKDOWN_VALUE",
    table_id: "FACT_INVENTORY",
    ordinal_position: 48,
    business_name: "Markdown Value",
    business_description: "Value of price markdowns on slow-moving or expiring inventory. Essential for CPG margin management, clearance planning, obsolescence mitigation, and profitability protection.",
    business_synonyms: ["Clearance Value", "Price Reduction", "Discount Value", "Markdown Amount", "Clearance Cost", "Reduction Value"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 2,
    business_criticality: "medium",
    additive_type: "additive"
}),

(consignment_inventory_value:Column {
    column_id: "COL_CONSIGNMENT_INVENTORY_VALUE_FACT_3049",
    column_name: "CONSIGNMENT_INVENTORY_VALUE",
    table_id: "FACT_INVENTORY",
    ordinal_position: 49,
    business_name: "Consignment Inventory Value",
    business_description: "Value of vendor-owned consignment inventory. Important for CPG cash flow management, vendor relationships, working capital optimization, and inventory financing strategies.",
    business_synonyms: ["Vendor Inventory", "Consigned Value", "VMI Value", "Vendor Stock", "Consignment Stock", "Supplier Inventory"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 2,
    business_criticality: "medium",
    additive_type: "additive"
}),

// ========================================
// SERVICE LEVEL AND PERFORMANCE
// ========================================

(fill_rate_percentage:Column {
    column_id: "COL_FILL_RATE_PERCENTAGE_FACT_3050",
    column_name: "FILL_RATE_PERCENTAGE",
    table_id: "FACT_INVENTORY",
    ordinal_position: 50,
    business_name: "Fill Rate Percentage",
    business_description: "Percentage of customer demand fulfilled from available inventory. Critical for CPG customer satisfaction, service level achievement, inventory effectiveness, and competitive advantage.",
    business_synonyms: ["Service Level", "Fulfillment Rate", "Order Fill Rate", "Availability Rate", "Service Rate", "Fill %"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "critical",
    additive_type: "non_additive"
}),

(perfect_order_percentage:Column {
    column_id: "COL_PERFECT_ORDER_PERCENTAGE_FACT_3051",
    column_name: "PERFECT_ORDER_PERCENTAGE",
    table_id: "FACT_INVENTORY",
    ordinal_position: 51,
    business_name: "Perfect Order Percentage",
    business_description: "Percentage of orders fulfilled completely, on-time, and damage-free. Essential for CPG operational excellence, customer experience, quality metrics, and competitive differentiation.",
    business_synonyms: ["Perfect Order Rate", "Order Quality", "Fulfillment Quality", "Order Excellence", "Perfect Delivery", "Quality Rate"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

// ========================================
// METADATA AND AUDIT INFORMATION
// ========================================

(inventory_planner:Column {
    column_id: "COL_INVENTORY_PLANNER_FACT_3052",
    column_name: "INVENTORY_PLANNER",
    table_id: "FACT_INVENTORY",
    ordinal_position: 52,
    business_name: "Inventory Planner",
    business_description: "Supply chain planner responsible for inventory management. Important for CPG accountability, performance tracking, best practice identification, and planning effectiveness measurement.",
    business_synonyms: ["Planner Name", "Supply Planner", "Inventory Manager", "Planning Owner", "Responsible Planner", "Stock Planner"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "low",
    additive_type: "non_additive"
}),

(inventory_status:Column {
    column_id: "COL_INVENTORY_STATUS_FACT_3053",
    column_name: "INVENTORY_STATUS",
    table_id: "FACT_INVENTORY",
    ordinal_position: 53,
    business_name: "Inventory Status",
    business_description: "Current status of inventory (Active, Hold, Obsolete, Expired). Essential for CPG inventory classification, disposition management, reporting accuracy, and operational decisions.",
    business_synonyms: ["Stock Status", "Inventory State", "Status Code", "Stock Condition", "Inventory Condition", "Current Status"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(abc_classification:Column {
    column_id: "COL_ABC_CLASSIFICATION_FACT_3054",
    column_name: "ABC_CLASSIFICATION",
    table_id: "FACT_INVENTORY",
    ordinal_position: 54,
    business_name: "ABC Classification",
    business_description: "Inventory classification based on value and velocity (A=High, B=Medium, C=Low). Critical for CPG inventory stratification, management focus, counting frequency, and resource allocation.",
    business_synonyms: ["ABC Code", "Inventory Class", "ABC Category", "Value Classification", "Velocity Class", "ABC Rank"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 1,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(record_created_timestamp:Column {
    column_id: "COL_RECORD_CREATED_TIMESTAMP_FACT_3055",
    column_name: "RECORD_CREATED_TIMESTAMP",
    table_id: "FACT_INVENTORY",
    ordinal_position: 55,
    business_name: "Record Created Timestamp",
    business_description: "System timestamp when inventory record was created in data warehouse. Used for audit trails, data lineage tracking, and data governance. Immutable after creation. Part of comprehensive data governance framework.",
    business_synonyms: ["Creation Time", "Insert Timestamp", "Created Date", "Record Creation", "Entry Timestamp", "Load Timestamp"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    max_length: null,
    business_criticality: "low",
    additive_type: "non_additive"
}),

(record_modified_timestamp:Column {
    column_id: "COL_RECORD_MODIFIED_TIMESTAMP_FACT_3056",
    column_name: "RECORD_MODIFIED_TIMESTAMP",
    table_id: "FACT_INVENTORY",
    ordinal_position: 56,
    business_name: "Record Last Modified Timestamp",
    business_description: "Most recent update timestamp for inventory record. Tracks data freshness, change frequency, and maintenance activities. Updated automatically with any field modification for data governance compliance.",
    business_synonyms: ["Update Time", "Last Modified", "Change Timestamp", "Modified Date", "Last Updated", "Revision Timestamp"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    max_length: null,
    business_criticality: "low",
    additive_type: "non_additive"
});

// ========================================
// CREATE TABLE-COLUMN RELATIONSHIPS
// ========================================

MATCH (t:Table {table_id: "FACT_INVENTORY"})
MATCH (c:Column {table_id: "FACT_INVENTORY"})
CREATE (t)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"]
}]->(c);