// ========================================
// DIM_CATEGORY - COMPREHENSIVE COLUMN CREATION
// Complete category dimension columns for all CPG domains
// ========================================

// Clear existing columns for DIM_CATEGORY (optional)
MATCH (c:Column {table_id: "DIM_CATEGORY"}) DETACH DELETE c;

// ========================================
// CORE CATEGORY IDENTIFIERS AND HIERARCHY
// ========================================

CREATE 
(category_id:Column {
    column_id: "COL_CATEGORY_ID_DIM_250",
    column_name: "CATEGORY_ID",
    table_id: "DIM_CATEGORY",
    ordinal_position: 1,
    business_name: "Category ID",
    business_description: "Unique identifier for each product category across the hierarchy. Supports multiple classification schemes and retailer-specific categories. Survives category reorganizations and mergers. Critical for consistent category performance tracking and planogram assignments.",
    business_synonyms: ["Category Key", "Category Code", "Classification ID", "Category Number", "Department ID", "Product Category ID", "Merchandise Category ID"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 20,
    is_primary_key: true,
    business_criticality: "critical"
}),
(category_name:Column {
    column_id: "COL_CATEGORY_NAME_251",
    column_name: "CATEGORY_NAME",
    table_id: "DIM_CATEGORY",
    ordinal_position: 2,
    business_name: "Category Name",
    business_description: "Descriptive name of product category used in reporting and merchandising. Must be unique within hierarchy level. Standardized across retailers where possible. Forms basis for category reviews and buyer responsibilities.",
    business_synonyms: ["Category Description", "Department Name", "Category Label", "Product Category Name", "Merchandise Category", "Category Title"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 200,
    business_criticality: "critical"
}),
(category_level:Column {
    column_id: "COL_CATEGORY_LEVEL_252",
    column_name: "CATEGORY_LEVEL",
    table_id: "DIM_CATEGORY",
    ordinal_position: 3,
    business_name: "Category Level",
    business_description: "Hierarchical level in category tree (1=Department, 2=Category, 3=Subcategory, 4=Segment). Enables drill-down/roll-up analysis. Critical for aggregation rules and reporting consistency. Typically 3-5 levels deep.",
    business_synonyms: ["Hierarchy Level", "Category Tier", "Level Number", "Tree Level", "Classification Level", "Depth Level"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "INTEGER",
    business_criticality: "high"
}),
(parent_category_id:Column {
    column_id: "COL_PARENT_CATEGORY_ID_253",
    column_name: "PARENT_CATEGORY_ID",
    table_id: "DIM_CATEGORY",
    ordinal_position: 4,
    business_name: "Parent Category ID",
    business_description: "Reference to parent category enabling hierarchy navigation. NULL for top-level departments. Critical for roll-up reporting and category tree visualization. Must maintain referential integrity.",
    business_synonyms: ["Parent ID", "Parent Category", "Category Parent", "Parent Node", "Parent Department", "Hierarchy Parent"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 20,
    is_foreign_key: true,
    business_criticality: "high"
}),
(hierarchy_path:Column {
    column_id: "COL_HIERARCHY_PATH_254",
    column_name: "HIERARCHY_PATH",
    table_id: "DIM_CATEGORY",
    ordinal_position: 5,
    business_name: "Category Hierarchy Path",
    business_description: "Complete path from root to current category (e.g., 'Food > Snacks > Salty Snacks > Chips'). Enables breadcrumb navigation and full context understanding. Useful for category migrations and mappings.",
    business_synonyms: ["Category Path", "Full Path", "Hierarchy String", "Category Tree Path", "Navigation Path", "Breadcrumb Path"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 500,
    business_criticality: "medium"
}),
(sort_order:Column {
    column_id: "COL_SORT_ORDER_255",
    column_name: "SORT_ORDER",
    table_id: "DIM_CATEGORY",
    ordinal_position: 6,
    business_name: "Sort Order",
    business_description: "Display sequence within parent category for consistent reporting and user interfaces. Based on sales importance or alphabetical order. Critical for planogram flow and shopping patterns.",
    business_synonyms: ["Display Order", "Sequence Number", "Sort Sequence", "Display Sequence", "Order Number", "Position"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "INTEGER",
    business_criticality: "low"
}),

// ========================================
// CATEGORY MANAGEMENT ATTRIBUTES
// ========================================

(category_role:Column {
    column_id: "COL_CATEGORY_ROLE_256",
    column_name: "CATEGORY_ROLE",
    table_id: "DIM_CATEGORY",
    ordinal_position: 7,
    business_name: "Category Role",
    business_description: "Strategic role in retailer's portfolio (Destination, Routine, Convenience, Seasonal/Fill-in). Drives space allocation, pricing strategy, and promotional intensity. Based on consumer shopping behavior and category importance.",
    business_synonyms: ["Strategic Role", "Category Type", "Portfolio Role", "Category Strategy", "Retail Role", "Category Position"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high"
}),
(category_strategy:Column {
    column_id: "COL_CATEGORY_STRATEGY_257",
    column_name: "CATEGORY_STRATEGY",
    table_id: "DIM_CATEGORY",
    ordinal_position: 8,
    business_name: "Category Strategy",
    business_description: "Growth strategy for category (Traffic Building, Transaction Building, Profit Generating, Cash Generating, Excitement Creating). Guides tactics and resource allocation. Aligned with retailer objectives.",
    business_synonyms: ["Growth Strategy", "Category Tactics", "Strategic Direction", "Category Plan", "Growth Plan", "Category Objective"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "high"
}),
(category_lifecycle:Column {
    column_id: "COL_CATEGORY_LIFECYCLE_258",
    column_name: "CATEGORY_LIFECYCLE",
    table_id: "DIM_CATEGORY",
    ordinal_position: 9,
    business_name: "Category Lifecycle Stage",
    business_description: "Current lifecycle position (Introduction, Growth, Maturity, Decline). Affects innovation requirements, promotional strategies, and space allocation. Critical for portfolio management decisions.",
    business_synonyms: ["Lifecycle Stage", "Category Stage", "Maturity Stage", "Lifecycle Phase", "Growth Stage", "Category Phase"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "medium"
}),
(purchase_frequency:Column {
    column_id: "COL_PURCHASE_FREQUENCY_259",
    column_name: "PURCHASE_FREQUENCY",
    table_id: "DIM_CATEGORY",
    ordinal_position: 10,
    business_name: "Purchase Frequency",
    business_description: "Average purchase cycle in days for category. Critical for inventory planning and promotional calendaring. Varies by consumer segment. Drives replenishment algorithms and stock levels.",
    business_synonyms: ["Buy Cycle", "Purchase Cycle", "Frequency Days", "Repurchase Rate", "Shopping Frequency", "Category Frequency"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 2,
    business_criticality: "high"
}),
(basket_penetration:Column {
    column_id: "COL_BASKET_PENETRATION_260",
    column_name: "BASKET_PENETRATION",
    table_id: "DIM_CATEGORY",
    ordinal_position: 11,
    business_name: "Basket Penetration Rate",
    business_description: "Percentage of shopping baskets containing category. Key metric for category importance and cross-merchandising opportunities. Higher penetration indicates routine purchase behavior.",
    business_synonyms: ["Basket Percent", "Penetration Rate", "Basket Inclusion", "Category Penetration", "Trip Penetration", "Basket Share"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high"
}),

// ========================================
// SYNDICATED DATA MAPPINGS
// ========================================

(nielsen_category_code:Column {
    column_id: "COL_NIELSEN_CATEGORY_261",
    column_name: "NIELSEN_CATEGORY_CODE",
    table_id: "DIM_CATEGORY",
    ordinal_position: 12,
    business_name: "Nielsen Category Code",
    business_description: "Nielsen product module code for syndicated data mapping. Required for market share calculations and competitive benchmarking. May aggregate multiple internal categories. Updated with Nielsen reorganizations.",
    business_synonyms: ["Nielsen Code", "Nielsen Module", "Syndicated Category", "Nielsen Classification", "Nielsen ID", "Market Track Code"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "high"
}),
(iri_category_code:Column {
    column_id: "COL_IRI_CATEGORY_262",
    column_name: "IRI_CATEGORY_CODE",
    table_id: "DIM_CATEGORY",
    ordinal_position: 13,
    business_name: "IRI Category Code",
    business_description: "IRI category classification for syndicated data integration. May differ from Nielsen definitions. Critical for multi-source data reconciliation. Enables competitive intelligence across data providers.",
    business_synonyms: ["IRI Code", "IRI Category", "IRI Classification", "Symphony Category", "IRI Module", "IRI ID"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "high"
}),
(retailer_category_code:Column {
    column_id: "COL_RETAILER_CATEGORY_263",
    column_name: "RETAILER_CATEGORY_CODE",
    table_id: "DIM_CATEGORY",
    ordinal_position: 14,
    business_name: "Retailer Category Code",
    business_description: "Retailer-specific category code for planogram and ordering systems. Varies by retailer requiring complex mapping tables. Critical for EDI transactions and automated replenishment.",
    business_synonyms: ["Retailer Code", "Customer Category", "Chain Category", "Retailer Classification", "Customer Code", "Planogram Category"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high"
}),
(gpc_brick_code:Column {
    column_id: "COL_GPC_BRICK_CODE_264",
    column_name: "GPC_BRICK_CODE",
    table_id: "DIM_CATEGORY",
    ordinal_position: 15,
    business_name: "GPC Brick Code",
    business_description: "Global Product Classification brick code for international standardization. GS1 standard enabling global data exchange. Required for GDSN participation. Links to attribute schema.",
    business_synonyms: ["GPC Code", "GS1 Category", "Global Classification", "GDSN Category", "Brick Code", "GPC Classification"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 10,
    business_criticality: "medium"
}),

// ========================================
// PERFORMANCE AND FINANCIAL METRICS
// ========================================

(category_revenue_ltm:Column {
    column_id: "COL_CATEGORY_REVENUE_LTM_265",
    column_name: "CATEGORY_REVENUE_LTM",
    table_id: "DIM_CATEGORY",
    ordinal_position: 16,
    business_name: "Category Revenue Last 12 Months",
    business_description: "Total category sales in trailing twelve months across measured channels. Key metric for category importance and resource allocation. Used for market sizing and growth tracking.",
    business_synonyms: ["Annual Revenue", "Category Sales", "LTM Sales", "Annual Category Value", "12-Month Revenue", "Category Size"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 2,
    business_criticality: "high"
}),
(category_growth_rate:Column {
    column_id: "COL_CATEGORY_GROWTH_RATE_266",
    column_name: "CATEGORY_GROWTH_RATE",
    table_id: "DIM_CATEGORY",
    ordinal_position: 17,
    business_name: "Category Growth Rate YoY",
    business_description: "Year-over-year growth rate indicating category momentum. Benchmark for brand performance evaluation. Negative growth triggers portfolio review. Drives innovation priorities.",
    business_synonyms: ["Growth Rate", "YoY Growth", "Category Trend", "Annual Growth", "Market Growth", "Category Momentum"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 8,
    scale: 2,
    business_criticality: "high"
}),
(average_unit_price:Column {
    column_id: "COL_AVERAGE_UNIT_PRICE_267",
    column_name: "AVERAGE_UNIT_PRICE",
    table_id: "DIM_CATEGORY",
    ordinal_position: 18,
    business_name: "Average Unit Price",
    business_description: "Mean price per unit in category establishing price expectations. Used for price gap analysis and value positioning. Varies by channel and geography. Inflation tracking metric.",
    business_synonyms: ["Avg Price", "Category Price", "Mean Price", "Unit Price", "Average Retail", "Price Point"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 2,
    business_criticality: "high"
}),
(gross_margin_percent:Column {
    column_id: "COL_GROSS_MARGIN_PCT_268",
    column_name: "GROSS_MARGIN_PERCENT",
    table_id: "DIM_CATEGORY",
    ordinal_position: 19,
    business_name: "Category Gross Margin Percent",
    business_description: "Average gross margin for category affecting retailer interest and space allocation. Higher margins drive preferential treatment. Benchmark for supplier negotiations and private label decisions.",
    business_synonyms: ["Margin Percent", "Category Margin", "Gross Profit", "Profit Margin", "GM%", "Category Profitability"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high"
}),
(private_label_share:Column {
    column_id: "COL_PRIVATE_LABEL_SHARE_269",
    column_name: "PRIVATE_LABEL_SHARE",
    table_id: "DIM_CATEGORY",
    ordinal_position: 20,
    business_name: "Private Label Share",
    business_description: "Percentage of category sales from store brands. Indicates commoditization risk and price pressure. Higher share suggests opportunity for differentiation. Affects national brand strategies.",
    business_synonyms: ["Store Brand Share", "Private Brand Share", "PL Share", "Own Label Share", "Generic Share", "Store Brand Percent"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high"
}),

// ========================================
// SHELF SPACE AND MERCHANDISING
// ========================================

(average_shelf_footage:Column {
    column_id: "COL_AVG_SHELF_FOOTAGE_270",
    column_name: "AVERAGE_SHELF_FOOTAGE",
    table_id: "DIM_CATEGORY",
    ordinal_position: 21,
    business_name: "Average Shelf Footage",
    business_description: "Mean linear feet allocated to category across store base. Critical for space-to-sales analysis and planogram development. Varies by store format. Benchmark for space negotiations.",
    business_synonyms: ["Shelf Space", "Linear Footage", "Category Space", "Shelf Feet", "Space Allocation", "Linear Space"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 8,
    scale: 2,
    business_criticality: "high"
}),
(recommended_facings:Column {
    column_id: "COL_RECOMMENDED_FACINGS_271",
    column_name: "RECOMMENDED_FACINGS",
    table_id: "DIM_CATEGORY",
    ordinal_position: 22,
    business_name: "Recommended Total Facings",
    business_description: "Optimal number of SKU facings for category based on variety needs and turnover. Balances consumer choice with operational efficiency. Input to assortment optimization models.",
    business_synonyms: ["Total Facings", "Category Facings", "Optimal Facings", "Facing Count", "SKU Facings", "Recommended SKUs"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    business_criticality: "medium"
}),
(planogram_complexity:Column {
    column_id: "COL_PLANOGRAM_COMPLEXITY_272",
    column_name: "PLANOGRAM_COMPLEXITY",
    table_id: "DIM_CATEGORY",
    ordinal_position: 23,
    business_name: "Planogram Complexity Score",
    business_description: "Measure of merchandising complexity based on SKU count, sizes, and brands. Higher complexity increases labor costs and out-of-stocks. Drives simplification initiatives.",
    business_synonyms: ["Complexity Score", "Merch Complexity", "POG Complexity", "Shelf Complexity", "Category Complexity", "Assortment Complexity"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "medium"
}),
(shelf_location_preference:Column {
    column_id: "COL_SHELF_LOCATION_PREF_273",
    column_name: "SHELF_LOCATION_PREFERENCE",
    table_id: "DIM_CATEGORY",
    ordinal_position: 24,
    business_name: "Shelf Location Preference",
    business_description: "Preferred store location for category (Perimeter, Center Store, Checkout, Endcap). Based on shopping patterns and adjacencies. Critical for traffic flow and impulse purchases.",
    business_synonyms: ["Store Location", "Category Placement", "Preferred Location", "Store Position", "Department Location", "Shelf Position"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "medium"
}),

// ========================================
// CONSUMER BEHAVIOR METRICS
// ========================================

(household_penetration:Column {
    column_id: "COL_HOUSEHOLD_PENETRATION_274",
    column_name: "HOUSEHOLD_PENETRATION",
    table_id: "DIM_CATEGORY",
    ordinal_position: 25,
    business_name: "Household Penetration Rate",
    business_description: "Percentage of households purchasing category annually. Key indicator of category reach and growth potential. Low penetration suggests expansion opportunity. Based on panel data.",
    business_synonyms: ["HH Penetration", "Consumer Penetration", "Market Penetration", "Household Percent", "Buyer Penetration", "Category Reach"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high"
}),
(buy_rate_per_buyer:Column {
    column_id: "COL_BUY_RATE_PER_BUYER_275",
    column_name: "BUY_RATE_PER_BUYER",
    table_id: "DIM_CATEGORY",
    ordinal_position: 26,
    business_name: "Buy Rate per Buyer",
    business_description: "Average annual category spend per buying household. Indicates engagement level and wallet share opportunity. Growth lever through increased consumption. Varies by demographics.",
    business_synonyms: ["Spend per Buyer", "Buy Rate", "Dollar per Buyer", "Average Spend", "Buyer Value", "Category Spend"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 2,
    business_criticality: "high"
}),
(trips_per_buyer:Column {
    column_id: "COL_TRIPS_PER_BUYER_276",
    column_name: "TRIPS_PER_BUYER",
    table_id: "DIM_CATEGORY",
    ordinal_position: 27,
    business_name: "Shopping Trips per Buyer",
    business_description: "Average annual shopping trips with category purchase. Indicates purchase frequency and loyalty. Higher trips suggest habitual purchasing. Used for trip driver strategies.",
    business_synonyms: ["Purchase Trips", "Shopping Frequency", "Trip Count", "Buyer Trips", "Purchase Occasions", "Category Trips"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 2,
    business_criticality: "medium"
}),
(units_per_trip:Column {
    column_id: "COL_UNITS_PER_TRIP_277",
    column_name: "UNITS_PER_TRIP",
    table_id: "DIM_CATEGORY",
    ordinal_position: 28,
    business_name: "Units per Shopping Trip",
    business_description: "Average units purchased per category shopping trip. Indicates stock-up behavior and price sensitivity. Higher units suggest bulk buying or multi-unit promotions working.",
    business_synonyms: ["Units per Purchase", "Basket Size", "Purchase Size", "Trip Size", "Units per Occasion", "Average Units"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 2,
    business_criticality: "medium"
}),

// ========================================
// PROMOTIONAL EFFECTIVENESS
// ========================================

(promotional_sensitivity:Column {
    column_id: "COL_PROMOTIONAL_SENSITIVITY_278",
    column_name: "PROMOTIONAL_SENSITIVITY",
    table_id: "DIM_CATEGORY",
    ordinal_position: 29,
    business_name: "Promotional Sensitivity Index",
    business_description: "Measure of sales responsiveness to promotions (0-100). Higher values indicate deal-driven categories. Affects promotional frequency and depth decisions. Based on lift analysis.",
    business_synonyms: ["Promo Sensitivity", "Deal Sensitivity", "Promotion Response", "Promo Index", "Deal Response", "Price Sensitivity"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high"
}),
(optimal_discount_depth:Column {
    column_id: "COL_OPTIMAL_DISCOUNT_279",
    column_name: "OPTIMAL_DISCOUNT_DEPTH",
    table_id: "DIM_CATEGORY",
    ordinal_position: 30,
    business_name: "Optimal Discount Depth",
    business_description: "Discount percentage generating best ROI based on elasticity curves. Typically 20-40% depending on category. Over-discounting destroys value. Critical for margin management.",
    business_synonyms: ["Best Discount", "Optimal Promo", "Target Discount", "Ideal Discount", "ROI Discount", "Optimal Depth"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high"
}),
(feature_ad_effectiveness:Column {
    column_id: "COL_FEATURE_AD_EFFECT_280",
    column_name: "FEATURE_AD_EFFECTIVENESS",
    table_id: "DIM_CATEGORY",
    ordinal_position: 31,
    business_name: "Feature Ad Effectiveness",
    business_description: "Lift multiplier for feature advertising in category. Categories with high visual appeal show better response. Used for ad space allocation and ROI projections.",
    business_synonyms: ["Ad Effectiveness", "Feature Lift", "Ad Response", "Circular Impact", "Feature Performance", "Ad Lift"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "medium"
}),

// ========================================
// SEASONALITY AND TRENDS
// ========================================

(seasonality_index:Column {
    column_id: "COL_SEASONALITY_INDEX_281",
    column_name: "SEASONALITY_INDEX",
    table_id: "DIM_CATEGORY",
    ordinal_position: 32,
    business_name: "Seasonality Index",
    business_description: "Measure of sales variation across year (0=no seasonality, 100=extreme peaks). High seasonality requires careful inventory planning. Affects promotional calendars and space flex programs.",
    business_synonyms: ["Seasonal Index", "Seasonality Score", "Seasonal Variation", "Peak Index", "Seasonal Factor", "Seasonality Measure"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high"
}),
(peak_season_period:Column {
    column_id: "COL_PEAK_SEASON_PERIOD_282",
    column_name: "PEAK_SEASON_PERIOD",
    table_id: "DIM_CATEGORY",
    ordinal_position: 33,
    business_name: "Peak Season Period",
    business_description: "Primary selling season for category (Q4 Holiday, Summer, Back-to-School, etc.). Drives inventory build and promotional planning. May have multiple peaks. Critical for supply chain.",
    business_synonyms: ["Peak Season", "High Season", "Primary Season", "Busy Period", "Peak Period", "Seasonal Peak"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high"
}),
(weather_sensitivity:Column {
    column_id: "COL_WEATHER_SENSITIVITY_283",
    column_name: "WEATHER_SENSITIVITY",
    table_id: "DIM_CATEGORY",
    ordinal_position: 34,
    business_name: "Weather Sensitivity Flag",
    business_description: "Indicates significant sales correlation with weather patterns. Categories like ice cream, sunscreen, hot soup show strong correlations. Enables weather-based forecasting and promotions.",
    business_synonyms: ["Weather Impact", "Climate Sensitive", "Weather Driven", "Temperature Sensitive", "Weather Correlation", "Climate Impact"],
    applicable_domains: ["alcoholic_beverages", "battery", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "medium"
}),

// ========================================
// INNOVATION AND TRENDS
// ========================================

(innovation_rate:Column {
    column_id: "COL_INNOVATION_RATE_284",
    column_name: "INNOVATION_RATE",
    table_id: "DIM_CATEGORY",
    ordinal_position: 35,
    business_name: "Category Innovation Rate",
    business_description: "Percentage of sales from products launched in past 2 years. Indicates category dynamism and competitive intensity. High rates require agile supply chains. Drives shelf reset frequency.",
    business_synonyms: ["New Product Rate", "Innovation Percent", "NPD Rate", "Launch Rate", "New Item Percent", "Innovation Index"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high"
}),
(trend_growth_index:Column {
    column_id: "COL_TREND_GROWTH_INDEX_285",
    column_name: "TREND_GROWTH_INDEX",
    table_id: "DIM_CATEGORY",
    ordinal_position: 36,
    business_name: "Trend Growth Index",
    business_description: "Forward-looking growth indicator based on consumer trends, demographics, and innovation pipeline. Values >100 indicate above-average growth potential. Used for investment prioritization.",
    business_synonyms: ["Growth Index", "Trend Index", "Future Growth", "Growth Potential", "Trend Score", "Opportunity Index"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 2,
    business_criticality: "medium"
}),
(digital_engagement_level:Column {
    column_id: "COL_DIGITAL_ENGAGEMENT_286",
    column_name: "DIGITAL_ENGAGEMENT_LEVEL",
    table_id: "DIM_CATEGORY",
    ordinal_position: 37,
    business_name: "Digital Engagement Level",
    business_description: "Consumer digital research and purchasing behavior for category (Low/Medium/High). High engagement categories benefit from enhanced content and ratings. Drives digital shelf strategies.",
    business_synonyms: ["Digital Level", "Online Engagement", "Digital Activity", "E-commerce Level", "Digital Intensity", "Online Behavior"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "medium"
}),

// ========================================
// COMPETITIVE DYNAMICS
// ========================================

(market_concentration:Column {
    column_id: "COL_MARKET_CONCENTRATION_287",
    column_name: "MARKET_CONCENTRATION",
    table_id: "DIM_CATEGORY",
    ordinal_position: 38,
    business_name: "Market Concentration HHI",
    business_description: "Herfindahl-Hirschman Index measuring competitive concentration. Higher values indicate oligopoly with few dominant brands. Affects competitive strategies and antitrust considerations.",
    business_synonyms: ["HHI", "Concentration Index", "Market HHI", "Competition Level", "Concentration Score", "Market Structure"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    business_criticality: "medium"
}),
(top_3_brand_share:Column {
    column_id: "COL_TOP_3_BRAND_SHARE_288",
    column_name: "TOP_3_BRAND_SHARE",
    table_id: "DIM_CATEGORY",
    ordinal_position: 39,
    business_name: "Top 3 Brands Share",
    business_description: "Combined market share of three largest brands indicating competitive dynamics. High concentration limits new entry. Used for competitive response strategies.",
    business_synonyms: ["Brand Concentration", "Top 3 Share", "Leader Share", "Top Brands Share", "Market Leaders", "Concentration Ratio"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "medium"
}),
(brand_count_active:Column {
    column_id: "COL_BRAND_COUNT_ACTIVE_289",
    column_name: "BRAND_COUNT_ACTIVE",
    table_id: "DIM_CATEGORY",
    ordinal_position: 40,
    business_name: "Active Brand Count",
    business_description: "Number of brands with measurable sales in category. Indicates competitive intensity and consumer choice. Too many brands creates confusion and inefficiency.",
    business_synonyms: ["Brand Count", "Number of Brands", "Active Brands", "Brand Variety", "Competitor Count", "Brand Options"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    business_criticality: "medium"
}),

// ========================================
// DOMAIN-SPECIFIC ATTRIBUTES
// ========================================

(alcohol_abv_range:Column {
    column_id: "COL_ALCOHOL_ABV_RANGE_290",
    column_name: "ALCOHOL_ABV_RANGE",
    table_id: "DIM_CATEGORY",
    ordinal_position: 41,
    business_name: "Alcohol ABV Range",
    business_description: "Typical alcohol by volume range for category (e.g., Beer 3-8%, Wine 11-15%). Determines tax rates and regulatory requirements. Affects pricing tiers and consumer preferences.",
    business_synonyms: ["ABV Range", "Alcohol Range", "Strength Range", "Alcohol Content Range", "ABV Bracket", "Alcohol Level"],
    applicable_domains: ["alcoholic_beverages"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "high"
}),
(controlled_substance_flag:Column {
    column_id: "COL_CONTROLLED_SUBSTANCE_291",
    column_name: "CONTROLLED_SUBSTANCE_FLAG",
    table_id: "DIM_CATEGORY",
    ordinal_position: 42,
    business_name: "Controlled Substance Category",
    business_description: "Indicates category contains DEA scheduled drugs requiring special handling, security, and reporting. Affects distribution channels and inventory management. Higher compliance costs.",
    business_synonyms: ["Controlled Flag", "DEA Scheduled", "Restricted Category", "Controlled Drugs", "Scheduled Drugs", "Regulated Category"],
    applicable_domains: ["pharmaceuticals"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "critical"
}),
(age_restricted_flag:Column {
    column_id: "COL_AGE_RESTRICTED_FLAG_292",
    column_name: "AGE_RESTRICTED_FLAG",
    table_id: "DIM_CATEGORY",
    ordinal_position: 43,
    business_name: "Age Restricted Category",
    business_description: "Category requires age verification for purchase (alcohol, tobacco, mature toys). Affects e-commerce eligibility and checkout processes. Requires staff training and compliance monitoring.",
    business_synonyms: ["Age Restriction", "Age Gated", "Adult Only", "Age Verified", "Restricted Purchase", "Age Limited"],
    applicable_domains: ["alcoholic_beverages", "toys"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "high"
}),
(battery_recycling_required:Column {
    column_id: "COL_BATTERY_RECYCLING_293",
    column_name: "BATTERY_RECYCLING_REQUIRED",
    table_id: "DIM_CATEGORY",
    ordinal_position: 44,
    business_name: "Battery Recycling Required",
    business_description: "Category requires battery recycling programs per state regulations. Affects reverse logistics and store operations. May include take-back programs and recycling fees.",
    business_synonyms: ["Recycling Required", "Take-Back Program", "Battery Recycling", "Recycling Mandate", "Collection Required", "Environmental Program"],
    applicable_domains: ["battery"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "medium"
}),
(organic_subcategory:Column {
    column_id: "COL_ORGANIC_SUBCATEGORY_294",
    column_name: "ORGANIC_SUBCATEGORY",
    table_id: "DIM_CATEGORY",
    ordinal_position: 45,
    business_name: "Organic Subcategory Flag",
    business_description: "Indicates significant organic product presence requiring separate merchandising and higher price points. Growing importance for younger consumers. Premium positioning opportunity.",
    business_synonyms: ["Organic Category", "Natural Category", "Organic Presence", "Organic Segment", "Natural/Organic", "Organic Flag"],
    applicable_domains: ["food_beverage", "cosmetics"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "medium"
}),
(beauty_prestige_flag:Column {
    column_id: "COL_BEAUTY_PRESTIGE_FLAG_295",
    column_name: "BEAUTY_PRESTIGE_FLAG",
    table_id: "DIM_CATEGORY",
    ordinal_position: 46,
    business_name: "Prestige Beauty Category",
    business_description: "High-end beauty category requiring specialized fixtures, training, and brand partnerships. Higher margins but selective distribution. Attracts affluent shoppers and requires different merchandising.",
    business_synonyms: ["Prestige Category", "Luxury Beauty", "High-End Category", "Premium Beauty", "Prestige Flag", "Luxury Segment"],
    applicable_domains: ["cosmetics"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "medium"
}),

// ========================================
// RETAIL EXECUTION
// ========================================

(reset_frequency:Column {
    column_id: "COL_RESET_FREQUENCY_296",
    column_name: "RESET_FREQUENCY",
    table_id: "DIM_CATEGORY",
    ordinal_position: 47,
    business_name: "Planogram Reset Frequency",
    business_description: "Number of major planogram resets per year. Higher frequency for fashion and innovation-driven categories. Each reset requires labor investment and temporary sales disruption.",
    business_synonyms: ["Reset Count", "POG Frequency", "Planogram Changes", "Reset Schedule", "Shelf Reset", "Annual Resets"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    business_criticality: "medium"
}),
(adjacency_categories:Column {
    column_id: "COL_ADJACENCY_CATEGORIES_297",
    column_name: "ADJACENCY_CATEGORIES",
    table_id: "DIM_CATEGORY",
    ordinal_position: 48,
    business_name: "Adjacency Categories",
    business_description: "Preferred adjacent categories for cross-merchandising and traffic flow. Based on basket analysis and shopping missions. Critical for store layout and impulse purchases.",
    business_synonyms: ["Adjacent Categories", "Category Adjacencies", "Next-To Categories", "Companion Categories", "Flow Categories", "Related Categories"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "TEXT",
    business_criticality: "medium"
}),
(space_to_sales_index:Column {
    column_id: "COL_SPACE_TO_SALES_INDEX_298",
    column_name: "SPACE_TO_SALES_INDEX",
    table_id: "DIM_CATEGORY",
    ordinal_position: 49,
    business_name: "Space to Sales Index",
    business_description: "Ratio of space share to sales share (100 = proportional). Values >100 indicate over-spaced, <100 under-spaced. Key metric for space optimization and reallocation decisions.",
    business_synonyms: ["Space Productivity", "Space Index", "Space Efficiency", "Space/Sales Ratio", "Productivity Index", "Space Performance"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 2,
    business_criticality: "high"
}),

// ========================================
// BUYER AND MANAGEMENT
// ========================================

(category_buyer:Column {
    column_id: "COL_CATEGORY_BUYER_299",
    column_name: "CATEGORY_BUYER",
    table_id: "DIM_CATEGORY",
    ordinal_position: 50,
    business_name: "Category Buyer",
    business_description: "Retailer buyer responsible for category decisions. Key relationship for negotiations and program approval. Changes with retailer reorganizations. Varies by retailer.",
    business_synonyms: ["Buyer Name", "Category Manager", "Merchant", "Retail Buyer", "Category Contact", "Buyer Contact"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 200,
    business_criticality: "medium"
}),
(category_captain:Column {
    column_id: "COL_CATEGORY_CAPTAIN_300",
    column_name: "CATEGORY_CAPTAIN",
    table_id: "DIM_CATEGORY",
    ordinal_position: 51,
    business_name: "Category Captain Brand",
    business_description: "Lead manufacturer providing category management expertise to retailer. Privileged position with data access and influence. Typically largest share brand. Rotates periodically.",
    business_synonyms: ["Captain Brand", "Lead Vendor", "Category Leader", "Captain Supplier", "Category Advisor", "Lead Manufacturer"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "medium"
}),

// ========================================
// METADATA AND GOVERNANCE
// ========================================

(category_status:Column {
    column_id: "COL_CATEGORY_STATUS_301",
    column_name: "CATEGORY_STATUS",
    table_id: "DIM_CATEGORY",
    ordinal_position: 52,
    business_name: "Category Status",
    business_description: "Current status of category (Active, Phasing Out, Discontinued, Seasonal). Affects ranging decisions and inventory strategies. Discontinued categories may be consolidated.",
    business_synonyms: ["Status", "Category State", "Active Status", "Current Status", "Category Flag", "Lifecycle Status"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "high"
}),
(effective_start_date:Column {
    column_id: "COL_EFFECTIVE_START_DATE_302",
    column_name: "EFFECTIVE_START_DATE",
    table_id: "DIM_CATEGORY",
    ordinal_position: 53,
    business_name: "Effective Start Date",
    business_description: "Date when category definition became active. Important for historical analysis and reorganization tracking. New categories may split or merge existing ones.",
    business_synonyms: ["Start Date", "Effective Date", "Active From", "Valid From", "Category Start", "Begin Date"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    business_criticality: "medium"
}),
(effective_end_date:Column {
    column_id: "COL_EFFECTIVE_END_DATE_303",
    column_name: "EFFECTIVE_END_DATE",
    table_id: "DIM_CATEGORY",
    ordinal_position: 54,
    business_name: "Effective End Date",
    business_description: "Date when category definition expired or changed. NULL for current definitions. Enables point-in-time analysis and tracks category evolution. Historical records retained.",
    business_synonyms: ["End Date", "Expiry Date", "Valid Until", "Termination Date", "Category End", "Inactive Date"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    business_criticality: "medium"
}),
(last_review_date:Column {
    column_id: "COL_LAST_REVIEW_DATE_304",
    column_name: "LAST_REVIEW_DATE",
    table_id: "DIM_CATEGORY",
    ordinal_position: 55,
    business_name: "Last Category Review Date",
    business_description: "Date of most recent formal category review with retailer. Triggers for space reallocation and assortment changes. Best practice is annual reviews. Includes performance assessment.",
    business_synonyms: ["Review Date", "Category Review", "Last Assessment", "Review Cycle", "Category Analysis Date", "Performance Review"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    business_criticality: "medium"
}),
(created_timestamp:Column {
    column_id: "COL_CREATED_TIMESTAMP_305",
    column_name: "CREATED_TIMESTAMP",
    table_id: "DIM_CATEGORY",
    ordinal_position: 56,
    business_name: "Record Created Timestamp",
    business_description: "System timestamp when category record was created. Used for audit trails and data lineage. Immutable after creation. Part of data governance framework.",
    business_synonyms: ["Creation Time", "Insert Timestamp", "Created Date", "Record Creation", "Entry Timestamp", "Initial Load Time"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    business_criticality: "low"
}),
(modified_timestamp:Column {
    column_id: "COL_MODIFIED_TIMESTAMP_306",
    column_name: "MODIFIED_TIMESTAMP",
    table_id: "DIM_CATEGORY",
    ordinal_position: 57,
    business_name: "Last Modified Timestamp",
    business_description: "Most recent update timestamp for category record. Tracks data freshness and change frequency. Updated automatically with any field modification. Used for incremental loads.",
    business_synonyms: ["Update Time", "Last Modified", "Change Timestamp", "Modified Date", "Last Updated", "Revision Timestamp"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    business_criticality: "low"
});

// ========================================
// CREATE TABLE-COLUMN RELATIONSHIPS
// ========================================

MATCH (t:Table {table_id: "DIM_CATEGORY"})
MATCH (c:Column {table_id: "DIM_CATEGORY"})
CREATE (t)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(c);

// ========================================
// VERIFICATION QUERIES
// ========================================

// Count total columns created
MATCH (c:Column {table_id: "DIM_CATEGORY"})
RETURN count(c) AS total_columns;

// Verify columns by domain specificity
MATCH (c:Column {table_id: "DIM_CATEGORY"})
RETURN c.domain_specific AS is_domain_specific, 
       count(c) AS column_count
ORDER BY is_domain_specific;

// List domain-specific columns with their applicable domains
MATCH (c:Column {table_id: "DIM_CATEGORY"})
WHERE c.domain_specific = true
RETURN c.business_name AS column_name, 
       c.applicable_domains AS domains
ORDER BY c.ordinal_position;

// Verify critical columns
MATCH (c:Column {table_id: "DIM_CATEGORY"})
WHERE c.business_criticality = "critical"
RETURN c.business_name AS critical_column, 
       c.business_criticality
ORDER BY c.ordinal_position;

// Check columns by category
MATCH (c:Column {table_id: "DIM_CATEGORY"})
RETURN 
    CASE 
        WHEN c.ordinal_position <= 6 THEN "Core Identifiers & Hierarchy"
        WHEN c.ordinal_position <= 11 THEN "Category Management"
        WHEN c.ordinal_position <= 15 THEN "Syndicated Data Mappings"
        WHEN c.ordinal_position <= 20 THEN "Performance & Financial"
        WHEN c.ordinal_position <= 24 THEN "Shelf Space & Merchandising"
        WHEN c.ordinal_position <= 28 THEN "Consumer Behavior"
        WHEN c.ordinal_position <= 31 THEN "Promotional Effectiveness"
        WHEN c.ordinal_position <= 34 THEN "Seasonality & Trends"
        WHEN c.ordinal_position <= 37 THEN "Innovation & Digital"
        WHEN c.ordinal_position <= 40 THEN "Competitive Dynamics"
        WHEN c.ordinal_position <= 46 THEN "Domain Specific"
        WHEN c.ordinal_position <= 49 THEN "Retail Execution"
        WHEN c.ordinal_position <= 51 THEN "Buyer & Management"
        ELSE "Metadata"
    END AS category,
    count(c) AS column_count
ORDER BY category;

// ========================================
// END OF DIM_CATEGORY COLUMN CREATION
// ========================================