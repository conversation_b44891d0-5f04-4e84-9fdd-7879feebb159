// ========================================
// DIM_PRODUCT_HIERARCHY - COMPREHENSIVE COLUMN CREATION
// Complete product hierarchy dimension columns for all CPG domains
// ========================================

// Clear existing columns for DIM_PRODUCT_HIERARCHY (optional)
MATCH (c:Column {table_id: "DIM_PRODUCT_HIERARCHY"}) DETACH DELETE c;

// ========================================
// CORE PRODUCT IDENTIFIERS AND BASIC ATTRIBUTES
// ========================================

CREATE 
(product_hierarchy_id:Column {
    column_id: "COL_PRODUCT_HIERARCHY_ID_DIM_701",
    column_name: "PRODUCT_HIERARCHY_ID",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 1,
    business_name: "Product Hierarchy ID",
    business_description: "Unique identifier for product hierarchy node across all CPG categories and organizational levels. Critical for product performance tracking, category management, brand portfolio analysis, and hierarchical reporting across alcoholic beverages, pharmaceuticals, toys, cosmetics, and all consumer product categories.",
    business_synonyms: ["Product Key", "Hierarchy Key", "Product Node ID", "Product Reference", "Product Code", "Hierarchy Reference", "Product Index"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_primary_key: true,
    business_criticality: "critical"
}),

(product_level:Column {
    column_id: "COL_PRODUCT_LEVEL_702",
    column_name: "PRODUCT_LEVEL",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 2,
    business_name: "Product Hierarchy Level",
    business_description: "Hierarchical level indicator (Company, Business Unit, Category, Subcategory, Brand, Sub-brand, Product Line, Product, SKU). Essential for CPG hierarchical analysis, drill-down reporting, and understanding organizational product structure across all categories.",
    business_synonyms: ["Hierarchy Level", "Product Level", "Organization Level", "Classification Level", "Hierarchy Depth", "Product Tier"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "critical"
}),

(parent_product_id:Column {
    column_id: "COL_PARENT_PRODUCT_ID_703",
    column_name: "PARENT_PRODUCT_ID",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 3,
    business_name: "Parent Product ID",
    business_description: "Identifier of parent node in product hierarchy for tree structure navigation. Critical for CPG roll-up analytics, parent-child relationships, hierarchical aggregations, and enabling drill-down/roll-up capabilities across product structure.",
    business_synonyms: ["Parent ID", "Parent Node", "Parent Reference", "Hierarchy Parent", "Parent Key", "Superior ID"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_foreign_key: true,
    business_criticality: "high"
}),

// ========================================
// COMPANY AND BUSINESS UNIT LEVEL
// ========================================

(company_name:Column {
    column_id: "COL_COMPANY_NAME_704",
    column_name: "COMPANY_NAME",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 4,
    business_name: "Company Name",
    business_description: "Top-level company name for corporate reporting and multi-entity analysis. Essential for CPG consolidated reporting, corporate performance tracking, and managing product portfolios across multiple companies or acquired entities.",
    business_synonyms: ["Corporation", "Corporate Entity", "Parent Company", "Organization", "Enterprise", "Corporate Name"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "high"
}),

(business_unit:Column {
    column_id: "COL_BUSINESS_UNIT_705",
    column_name: "BUSINESS_UNIT",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 5,
    business_name: "Business Unit",
    business_description: "Operating business unit or division responsible for product line. Critical for CPG P&L responsibility, resource allocation, strategic planning, and understanding business unit performance across different product portfolios.",
    business_synonyms: ["Division", "Business Division", "Operating Unit", "BU", "Organizational Unit", "Business Segment"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "high"
}),

(business_unit_code:Column {
    column_id: "COL_BUSINESS_UNIT_CODE_706",
    column_name: "BUSINESS_UNIT_CODE",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 6,
    business_name: "Business Unit Code",
    business_description: "Abbreviated code for business unit used in systems and reporting. Important for CPG system integration, abbreviated reporting, and consistent business unit identification across platforms and regions.",
    business_synonyms: ["BU Code", "Division Code", "Unit Code", "Business Code", "Organizational Code", "Unit Abbreviation"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "medium"
}),

// ========================================
// CATEGORY MANAGEMENT HIERARCHY
// ========================================

(category_level_1:Column {
    column_id: "COL_CATEGORY_LEVEL_1_707",
    column_name: "CATEGORY_LEVEL_1",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 7,
    business_name: "Category Level 1",
    business_description: "Top-level product category classification (Food & Beverage, Personal Care, Healthcare, etc.). Essential for CPG high-level category management, strategic planning, and understanding major category performance across the portfolio.",
    business_synonyms: ["Major Category", "Primary Category", "Top Category", "Category L1", "Main Category", "Super Category"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "high"
}),

(category_level_2:Column {
    column_id: "COL_CATEGORY_LEVEL_2_708",
    column_name: "CATEGORY_LEVEL_2",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 8,
    business_name: "Category Level 2",
    business_description: "Secondary category classification for more detailed grouping (Beverages > Soft Drinks, Personal Care > Skincare, etc.). Important for CPG category management, competitive analysis, and detailed category performance tracking.",
    business_synonyms: ["Sub Category", "Category L2", "Secondary Category", "Category Subdivision", "Detailed Category", "Category Group"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "high"
}),

(category_level_3:Column {
    column_id: "COL_CATEGORY_LEVEL_3_709",
    column_name: "CATEGORY_LEVEL_3",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 9,
    business_name: "Category Level 3",
    business_description: "Tertiary category classification for granular product grouping (Soft Drinks > Cola, Skincare > Facial Care, etc.). Critical for CPG detailed category analysis, space management, and understanding specific category dynamics.",
    business_synonyms: ["Micro Category", "Category L3", "Tertiary Category", "Detailed Sub-category", "Granular Category", "Specific Category"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "medium"
}),

(category_manager:Column {
    column_id: "COL_CATEGORY_MANAGER_710",
    column_name: "CATEGORY_MANAGER",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 10,
    business_name: "Category Manager",
    business_description: "Person responsible for category strategy and performance. Important for CPG accountability, category expertise, performance responsibility, and understanding category management effectiveness by individual or team.",
    business_synonyms: ["Category Lead", "Category Owner", "Category Director", "Category Specialist", "Category Head", "Product Category Manager"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "low"
}),

// ========================================
// BRAND HIERARCHY AND MANAGEMENT
// ========================================

(brand_name:Column {
    column_id: "COL_BRAND_NAME_711",
    column_name: "BRAND_NAME",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 11,
    business_name: "Brand Name",
    business_description: "Primary brand name for brand-level analysis and management. Essential for CPG brand performance tracking, brand equity measurement, portfolio optimization, and understanding brand contribution across all product categories.",
    business_synonyms: ["Brand", "Product Brand", "Master Brand", "Primary Brand", "Brand Identity", "Brand Label"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "critical"
}),

(sub_brand:Column {
    column_id: "COL_SUB_BRAND_712",
    column_name: "SUB_BRAND",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 12,
    business_name: "Sub-Brand",
    business_description: "Sub-brand or brand variant for detailed brand architecture analysis. Important for CPG brand extension tracking, sub-brand performance, portfolio complexity management, and understanding brand hierarchy effectiveness.",
    business_synonyms: ["Brand Variant", "Sub Brand", "Brand Extension", "Brand Line", "Secondary Brand", "Brand Family"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "medium"
}),

(brand_tier:Column {
    column_id: "COL_BRAND_TIER_713",
    column_name: "BRAND_TIER",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 13,
    business_name: "Brand Tier",
    business_description: "Strategic brand tier classification (Premium, Mass, Value, etc.). Critical for CPG pricing strategy, market positioning, resource allocation, and understanding brand portfolio balance across price segments.",
    business_synonyms: ["Brand Level", "Brand Segment", "Brand Position", "Brand Class", "Brand Category", "Brand Positioning"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "high"
}),

(brand_owner:Column {
    column_id: "COL_BRAND_OWNER_714",
    column_name: "BRAND_OWNER",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 14,
    business_name: "Brand Owner",
    business_description: "Legal owner or licensor of the brand for licensing and ownership tracking. Important for CPG licensing management, royalty tracking, brand partnership analysis, and understanding owned vs licensed brand performance.",
    business_synonyms: ["Brand Licensor", "Brand Holder", "Brand Company", "Trademark Owner", "Brand Entity", "Brand Rights Holder"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "medium"
}),

(brand_manager:Column {
    column_id: "COL_BRAND_MANAGER_715",
    column_name: "BRAND_MANAGER",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 15,
    business_name: "Brand Manager",
    business_description: "Person responsible for brand strategy and performance. Essential for CPG brand accountability, brand expertise, performance responsibility, and understanding brand management effectiveness by individual or team.",
    business_synonyms: ["Brand Lead", "Brand Director", "Brand Owner", "Brand Specialist", "Brand Marketing Manager", "Product Manager"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "low"
}),

// ========================================
// PRODUCT LINE AND PRODUCT DETAILS
// ========================================

(product_line:Column {
    column_id: "COL_PRODUCT_LINE_716",
    column_name: "PRODUCT_LINE",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 16,
    business_name: "Product Line",
    business_description: "Specific product line within brand for product line performance analysis. Important for CPG product line management, innovation tracking, line extension planning, and understanding product line contribution to brand performance.",
    business_synonyms: ["Product Series", "Product Family", "Product Range", "Line", "Product Collection", "Product Group"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "medium"
}),

(product_name:Column {
    column_id: "COL_PRODUCT_NAME_717",
    column_name: "PRODUCT_NAME",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 17,
    business_name: "Product Name",
    business_description: "Specific product name for individual product tracking and analysis. Critical for CPG product performance monitoring, consumer recognition, marketing effectiveness, and detailed product-level analytics across categories.",
    business_synonyms: ["Product Title", "Product Description", "Product Label", "Item Name", "Product Designation", "Product Identity"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 200,
    business_criticality: "high"
}),

(sku_code:Column {
    column_id: "COL_SKU_CODE_718",
    column_name: "SKU_CODE",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 18,
    business_name: "SKU Code",
    business_description: "Stock Keeping Unit code for item-level tracking and inventory management. Essential for CPG inventory control, supply chain management, sales tracking, and operational efficiency across all distribution channels.",
    business_synonyms: ["SKU", "Item Code", "Product Code", "Stock Code", "Item Number", "Product SKU", "Item SKU"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "critical"
}),

(upc_code:Column {
    column_id: "COL_UPC_CODE_719",
    column_name: "UPC_CODE",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 19,
    business_name: "UPC Code",
    business_description: "Universal Product Code for retail scanning and industry-wide product identification. Critical for CPG retail operations, syndicated data matching, POS tracking, and cross-retailer product identification.",
    business_synonyms: ["UPC", "Barcode", "Universal Product Code", "Retail Code", "Scanner Code", "Product Barcode", "EAN Code"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "high"
}),

(gtin_code:Column {
    column_id: "COL_GTIN_CODE_720",
    column_name: "GTIN_CODE",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 20,
    business_name: "GTIN Code",
    business_description: "Global Trade Item Number for global product identification and supply chain tracking. Important for CPG international operations, supply chain transparency, and global product standardization across markets.",
    business_synonyms: ["GTIN", "Global Trade Item Number", "International Code", "Global Product Code", "Trade Item Number", "Global Identifier"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "medium"
}),

// ========================================
// PRODUCT ATTRIBUTES AND VARIANTS
// ========================================

(product_size:Column {
    column_id: "COL_PRODUCT_SIZE_721",
    column_name: "PRODUCT_SIZE",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 21,
    business_name: "Product Size",
    business_description: "Product size or volume specification for size-based analysis and packaging optimization. Essential for CPG packaging strategy, price per unit analysis, consumer preference tracking, and size performance across categories.",
    business_synonyms: ["Size", "Volume", "Weight", "Package Size", "Unit Size", "Container Size", "Pack Size"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high"
}),

(size_category:Column {
    column_id: "COL_SIZE_CATEGORY_722",
    column_name: "SIZE_CATEGORY",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 22,
    business_name: "Size Category",
    business_description: "Standardized size classification (Travel, Regular, Family, Bulk, etc.). Important for CPG size strategy analysis, consumer behavior understanding, and optimizing size mix across different channels and occasions.",
    business_synonyms: ["Size Classification", "Size Type", "Size Segment", "Package Classification", "Size Tier", "Volume Category"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "medium"
}),

(flavor_variant:Column {
    column_id: "COL_FLAVOR_VARIANT_723",
    column_name: "FLAVOR_VARIANT",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 23,
    business_name: "Flavor Variant",
    business_description: "Specific flavor, scent, or variant specification for variant analysis. Critical for CPG flavor performance tracking, consumer preference analysis, innovation opportunities, and variant optimization across categories.",
    business_synonyms: ["Flavor", "Variant", "Scent", "Variety", "Type", "Style", "Formulation"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "medium"
}),

(package_type:Column {
    column_id: "COL_PACKAGE_TYPE_724",
    column_name: "PACKAGE_TYPE",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 24,
    business_name: "Package Type",
    business_description: "Type of packaging used for product (Bottle, Can, Tube, Jar, Box, etc.). Important for CPG packaging strategy, sustainability initiatives, cost optimization, and understanding packaging impact on performance.",
    business_synonyms: ["Packaging", "Container Type", "Package Format", "Packaging Format", "Container", "Package Material"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "medium"
}),

(multi_pack_flag:Column {
    column_id: "COL_MULTI_PACK_FLAG_725",
    column_name: "MULTI_PACK_FLAG",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 25,
    business_name: "Multi-Pack Flag",
    business_description: "Indicates product is sold in multi-unit packages. Essential for CPG packaging strategy, bulk purchasing analysis, family size optimization, and understanding multi-pack versus single unit performance.",
    business_synonyms: ["Multi Pack", "Bundle Flag", "Multi Unit", "Pack Flag", "Bulk Pack", "Family Pack"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium"
}),

(units_per_pack:Column {
    column_id: "COL_UNITS_PER_PACK_726",
    column_name: "UNITS_PER_PACK",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 26,
    business_name: "Units per Pack",
    business_description: "Number of individual units in package for multi-pack analysis and value calculation. Important for CPG unit economics, value proposition analysis, and understanding optimal pack size across categories.",
    business_synonyms: ["Pack Count", "Unit Count", "Package Quantity", "Pack Size Count", "Units Count", "Quantity per Pack"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "medium"
}),

// ========================================
// BUSINESS CLASSIFICATION AND STRATEGY
// ========================================

(strategic_importance:Column {
    column_id: "COL_STRATEGIC_IMPORTANCE_727",
    column_name: "STRATEGIC_IMPORTANCE",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 27,
    business_name: "Strategic Importance",
    business_description: "Strategic classification of product importance (Core, Growth, Emerging, Harvest). Critical for CPG resource allocation, investment prioritization, portfolio optimization, and strategic planning across product categories.",
    business_synonyms: ["Strategic Priority", "Product Priority", "Strategic Classification", "Business Priority", "Portfolio Priority", "Strategic Tier"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "high"
}),

(lifecycle_stage:Column {
    column_id: "COL_LIFECYCLE_STAGE_728",
    column_name: "LIFECYCLE_STAGE",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 28,
    business_name: "Product Lifecycle Stage",
    business_description: "Current stage in product lifecycle (Introduction, Growth, Maturity, Decline). Essential for CPG lifecycle management, marketing strategy adaptation, investment decisions, and understanding lifecycle-specific performance patterns.",
    business_synonyms: ["Lifecycle Phase", "Product Stage", "Development Stage", "Market Stage", "Life Phase", "Product Lifecycle"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "high"
}),

(innovation_type:Column {
    column_id: "COL_INNOVATION_TYPE_729",
    column_name: "INNOVATION_TYPE",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 29,
    business_name: "Innovation Type",
    business_description: "Type of innovation represented by product (New-to-World, New-to-Company, Line Extension, Reformulation, etc.). Important for CPG innovation tracking, R&D effectiveness, and understanding innovation impact on performance.",
    business_synonyms: ["Innovation Classification", "Innovation Category", "NPD Type", "Development Type", "Innovation Level", "Product Innovation"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "medium"
}),

(launch_year:Column {
    column_id: "COL_LAUNCH_YEAR_730",
    column_name: "LAUNCH_YEAR",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 30,
    business_name: "Product Launch Year",
    business_description: "Year when product was first launched for age analysis and vintage tracking. Critical for CPG product age analysis, lifecycle timing, innovation velocity tracking, and understanding product longevity across categories.",
    business_synonyms: ["Introduction Year", "Year Launched", "Market Entry Year", "First Year", "Debut Year", "Launch Date"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "temporal",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "medium"
}),

// ========================================
// FINANCIAL AND PRICING ATTRIBUTES
// ========================================

(price_tier:Column {
    column_id: "COL_PRICE_TIER_731",
    column_name: "PRICE_TIER",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 31,
    business_name: "Price Tier",
    business_description: "Price tier classification for pricing strategy analysis (Premium, Mid-Tier, Value, Economy). Essential for CPG pricing strategy, competitive positioning, and understanding price tier performance across categories.",
    business_synonyms: ["Pricing Tier", "Price Level", "Price Segment", "Price Classification", "Price Band", "Price Range"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "high"
}),

(suggested_retail_price:Column {
    column_id: "COL_SUGGESTED_RETAIL_PRICE_732",
    column_name: "SUGGESTED_RETAIL_PRICE",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 32,
    business_name: "Suggested Retail Price",
    business_description: "Manufacturer's suggested retail price for pricing analysis and margin calculation. Important for CPG pricing strategy, margin analysis, price point optimization, and understanding price-performance relationships.",
    business_synonyms: ["MSRP", "List Price", "Retail Price", "Recommended Price", "Standard Price", "Base Price"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 2,
    business_criticality: "high"
}),

(cost_of_goods:Column {
    column_id: "COL_COST_OF_GOODS_733",
    column_name: "COST_OF_GOODS",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 33,
    business_name: "Cost of Goods Sold",
    business_description: "Product cost for margin analysis and profitability tracking. Critical for CPG profitability analysis, cost management, pricing decisions, and understanding cost-performance relationships across categories.",
    business_synonyms: ["COGS", "Product Cost", "Unit Cost", "Manufacturing Cost", "Production Cost", "Direct Cost"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 2,
    business_criticality: "high"
}),

(margin_classification:Column {
    column_id: "COL_MARGIN_CLASSIFICATION_734",
    column_name: "MARGIN_CLASSIFICATION",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 34,
    business_name: "Margin Classification",
    business_description: "Profit margin tier classification (High, Medium, Low) for profitability analysis. Essential for CPG portfolio optimization, product mix decisions, and understanding margin contribution across categories.",
    business_synonyms: ["Margin Tier", "Profitability Class", "Margin Level", "Profit Classification", "Margin Segment", "Profitability Tier"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "medium"
}),

// ========================================
// MARKET AND COMPETITIVE POSITIONING
// ========================================

(market_position:Column {
    column_id: "COL_MARKET_POSITION_735",
    column_name: "MARKET_POSITION",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 35,
    business_name: "Market Position",
    business_description: "Competitive market position classification (Leader, Challenger, Follower, Niche). Important for CPG competitive strategy, market dynamics understanding, and positioning optimization across categories.",
    business_synonyms: ["Competitive Position", "Market Standing", "Competitive Rank", "Market Rank", "Position Classification", "Market Status"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "medium"
}),

(competitive_set:Column {
    column_id: "COL_COMPETITIVE_SET_736",
    column_name: "COMPETITIVE_SET",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 36,
    business_name: "Competitive Set",
    business_description: "Defined competitive set for direct competition analysis. Critical for CPG competitive intelligence, market share tracking, benchmark analysis, and understanding competitive dynamics by category.",
    business_synonyms: ["Competition Group", "Competitive Frame", "Competitive Universe", "Benchmark Set", "Competitor Group", "Competitive Landscape"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "medium"
}),

(target_consumer:Column {
    column_id: "COL_TARGET_CONSUMER_737",
    column_name: "TARGET_CONSUMER",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 37,
    business_name: "Target Consumer",
    business_description: "Primary target consumer segment for marketing and positioning. Essential for CPG consumer strategy, marketing effectiveness, channel selection, and understanding consumer-product fit across categories.",
    business_synonyms: ["Target Market", "Consumer Target", "Target Audience", "Consumer Segment", "Primary Consumer", "Target Demo"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "medium"
}),

// ========================================
// REGULATORY AND COMPLIANCE ATTRIBUTES
// ========================================

(regulatory_classification:Column {
    column_id: "COL_REGULATORY_CLASSIFICATION_738",
    column_name: "REGULATORY_CLASSIFICATION",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 38,
    business_name: "Regulatory Classification",
    business_description: "Regulatory category classification for compliance tracking. Critical for CPG regulatory compliance, especially important for pharmaceuticals, alcoholic beverages, children's products, and ensuring appropriate regulatory management.",
    business_synonyms: ["Regulatory Category", "Compliance Class", "Regulatory Type", "Legal Classification", "Regulatory Status", "Compliance Category"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high"
}),

(fda_classification:Column {
    column_id: "COL_FDA_CLASSIFICATION_739",
    column_name: "FDA_CLASSIFICATION",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 39,
    business_name: "FDA Classification",
    business_description: "FDA regulatory classification for food, drug, and cosmetic products. Essential for pharmaceutical and health supplement compliance, food safety requirements, and ensuring proper regulatory categorization.",
    business_synonyms: ["FDA Category", "FDA Class", "Drug Classification", "Food Classification", "FDA Type", "Federal Classification"],
    applicable_domains: ["pharmaceuticals", "food_beverage", "cosmetics", "health_supplements", "baby_products", "personal_care"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "critical",
    regulatory_relevance: ["FDA"]
}),

(age_restriction:Column {
    column_id: "COL_AGE_RESTRICTION_740",
    column_name: "AGE_RESTRICTION",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 40,
    business_name: "Age Restriction",
    business_description: "Age restrictions for product sales and marketing. Critical for CPG compliance, especially alcoholic beverages (21+), age-appropriate toys, and ensuring legal age-related compliance across categories.",
    business_synonyms: ["Age Limit", "Age Requirement", "Minimum Age", "Age Compliance", "Legal Age", "Age Eligibility"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "toys", "baby_products"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "high",
    regulatory_relevance: ["TTB", "CPSC", "STATE_REGULATIONS"]
}),

// ========================================
// SUPPLY CHAIN AND MANUFACTURING
// ========================================

(manufacturing_location:Column {
    column_id: "COL_MANUFACTURING_LOCATION_741",
    column_name: "MANUFACTURING_LOCATION",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 41,
    business_name: "Manufacturing Location",
    business_description: "Primary manufacturing facility or region for supply chain analysis. Important for CPG supply chain optimization, cost analysis, quality tracking, and understanding manufacturing footprint across product portfolio.",
    business_synonyms: ["Production Location", "Manufacturing Site", "Production Facility", "Manufacturing Plant", "Production Site", "Factory Location"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "medium"
}),

(supplier_type:Column {
    column_id: "COL_SUPPLIER_TYPE_742",
    column_name: "SUPPLIER_TYPE",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 42,
    business_name: "Supplier Type",
    business_description: "Type of supplier relationship (Owned, Contract Manufacturing, Co-Packer, etc.). Essential for CPG supply chain strategy, cost structure analysis, and understanding supplier mix across product categories.",
    business_synonyms: ["Manufacturing Type", "Production Type", "Supplier Category", "Manufacturing Model", "Production Model", "Sourcing Type"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "medium"
}),

(lead_time_days:Column {
    column_id: "COL_LEAD_TIME_DAYS_743",
    column_name: "LEAD_TIME_DAYS",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 43,
    business_name: "Manufacturing Lead Time Days",
    business_description: "Standard manufacturing lead time in days for production planning and inventory management. Important for CPG supply chain planning, inventory optimization, and production scheduling across categories.",
    business_synonyms: ["Lead Time", "Production Time", "Manufacturing Time", "Production Lead Time", "Supply Lead Time", "Order Lead Time"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "medium"
}),

// ========================================
// PRODUCT STATUS AND LIFECYCLE MANAGEMENT
// ========================================

(product_status:Column {
    column_id: "COL_PRODUCT_STATUS_744",
    column_name: "PRODUCT_STATUS",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 44,
    business_name: "Product Status",
    business_description: "Current product status (Active, Discontinued, Seasonal, Limited Edition, etc.). Critical for CPG portfolio management, inventory planning, sales forecasting, and understanding active product universe.",
    business_synonyms: ["Status", "Product State", "Active Status", "Availability Status", "Product Availability", "Current Status"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "critical"
}),

(seasonal_product_flag:Column {
    column_id: "COL_SEASONAL_PRODUCT_FLAG_745",
    column_name: "SEASONAL_PRODUCT_FLAG",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 45,
    business_name: "Seasonal Product Flag",
    business_description: "Indicates product has seasonal availability or demand patterns. Important for CPG seasonal planning, inventory management, and understanding seasonal vs year-round product performance.",
    business_synonyms: ["Seasonal Flag", "Seasonal Product", "Holiday Product", "Seasonal Item", "Seasonal Availability", "Time-Limited"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium"
}),

(limited_edition_flag:Column {
    column_id: "COL_LIMITED_EDITION_FLAG_746",
    column_name: "LIMITED_EDITION_FLAG",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 46,
    business_name: "Limited Edition Flag",
    business_description: "Indicates product is a limited or special edition. Essential for CPG special product tracking, exclusivity marketing, scarcity-driven demand analysis, and limited-time offer performance.",
    business_synonyms: ["Limited Edition", "Special Edition", "Exclusive Product", "Limited Release", "Special Release", "Collector Edition"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium"
}),

(discontinuation_date:Column {
    column_id: "COL_DISCONTINUATION_DATE_747",
    column_name: "DISCONTINUATION_DATE",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 47,
    business_name: "Product Discontinuation Date",
    business_description: "Date when product was or will be discontinued for lifecycle analysis. Important for CPG portfolio cleanup, inventory liquidation planning, and understanding product lifespan across categories.",
    business_synonyms: ["End Date", "Discontinue Date", "Phase Out Date", "Retirement Date", "Withdrawal Date", "Exit Date"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    max_length: null,
    business_criticality: "medium"
}),

// ========================================
// QUALITY AND CERTIFICATION ATTRIBUTES
// ========================================

(quality_grade:Column {
    column_id: "COL_QUALITY_GRADE_748",
    column_name: "QUALITY_GRADE",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 48,
    business_name: "Quality Grade",
    business_description: "Quality grade or tier classification for quality management and positioning. Important for CPG quality assurance, premium positioning, and understanding quality-performance relationships across categories.",
    business_synonyms: ["Quality Level", "Grade", "Quality Rating", "Quality Class", "Quality Tier", "Quality Standard"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "medium"
}),

(organic_certified_flag:Column {
    column_id: "COL_ORGANIC_CERTIFIED_FLAG_749",
    column_name: "ORGANIC_CERTIFIED_FLAG",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 49,
    business_name: "Organic Certified Flag",
    business_description: "Indicates product has organic certification. Critical for organic food, personal care, and baby product positioning, premium pricing, health-conscious targeting, and regulatory compliance.",
    business_synonyms: ["Organic Flag", "USDA Organic", "Certified Organic", "Organic Product", "Natural Flag", "Organic Certification"],
    applicable_domains: ["food_beverage", "personal_care", "baby_products", "pet_food", "snacks", "dairy"],
    domain_specific: true,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium",
    regulatory_relevance: ["USDA", "ORGANIC_CERTIFICATION"]
}),

(non_gmo_flag:Column {
    column_id: "COL_NON_GMO_FLAG_750",
    column_name: "NON_GMO_FLAG",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 50,
    business_name: "Non-GMO Flag",
    business_description: "Indicates product is certified non-GMO. Important for food and beverage products, health-conscious consumers, clean label positioning, and meeting consumer preference for non-GMO products.",
    business_synonyms: ["Non GMO", "GMO Free", "Non-GMO Verified", "GMO-Free", "Non-Genetically Modified", "Clean Label"],
    applicable_domains: ["food_beverage", "snacks", "dairy", "baby_products", "pet_food"],
    domain_specific: true,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium"
}),

(gluten_free_flag:Column {
    column_id: "COL_GLUTEN_FREE_FLAG_751",
    column_name: "GLUTEN_FREE_FLAG",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 51,
    business_name: "Gluten-Free Flag",
    business_description: "Indicates product is certified gluten-free. Essential for food and beverage products, dietary restriction compliance, health-focused positioning, and serving gluten-sensitive consumers.",
    business_synonyms: ["Gluten Free", "GF", "Gluten-Free Certified", "Celiac Safe", "Wheat Free", "Gluten-Free Product"],
    applicable_domains: ["food_beverage", "snacks", "dairy", "baby_products"],
    domain_specific: true,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium"
}),

// ========================================
// DOMAIN-SPECIFIC ATTRIBUTES
// ========================================

(alcohol_content:Column {
    column_id: "COL_ALCOHOL_CONTENT_752",
    column_name: "ALCOHOL_CONTENT",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 52,
    business_name: "Alcohol Content Percentage",
    business_description: "Alcohol by volume percentage for alcoholic beverages. Critical for alcoholic beverage classification, regulatory compliance, tax calculations, and consumer information requirements.",
    business_synonyms: ["ABV", "Alcohol by Volume", "Alcohol Percentage", "Alcohol Strength", "Proof", "Alcohol Level"],
    applicable_domains: ["alcoholic_beverages"],
    domain_specific: true,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "critical",
    regulatory_relevance: ["TTB", "STATE_LIQUOR_BOARDS"]
}),

(prescription_drug_flag:Column {
    column_id: "COL_PRESCRIPTION_DRUG_FLAG_753",
    column_name: "PRESCRIPTION_DRUG_FLAG",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 53,
    business_name: "Prescription Drug Flag",
    business_description: "Indicates product requires prescription for dispensing. Critical for pharmaceutical products, regulatory compliance, professional targeting, and ensuring appropriate distribution channels.",
    business_synonyms: ["Rx Flag", "Prescription Only", "Prescription Required", "Rx Product", "Professional Use", "Healthcare Provider"],
    applicable_domains: ["pharmaceuticals"],
    domain_specific: true,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "critical",
    regulatory_relevance: ["FDA", "DEA"]
}),

(therapeutic_class:Column {
    column_id: "COL_THERAPEUTIC_CLASS_754",
    column_name: "THERAPEUTIC_CLASS",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 54,
    business_name: "Therapeutic Class",
    business_description: "Therapeutic classification for pharmaceutical and health supplement products. Essential for drug classification, therapeutic area analysis, medical targeting, and regulatory categorization.",
    business_synonyms: ["Drug Class", "Therapeutic Category", "Medical Class", "Pharmacological Class", "Treatment Category", "Drug Category"],
    applicable_domains: ["pharmaceuticals", "health_supplements"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "high",
    regulatory_relevance: ["FDA"]
}),

(age_appropriate_rating:Column {
    column_id: "COL_AGE_APPROPRIATE_RATING_755",
    column_name: "AGE_APPROPRIATE_RATING",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 55,
    business_name: "Age Appropriate Rating",
    business_description: "Age appropriateness rating for toys and children's products. Critical for toy safety compliance, appropriate targeting, parental guidance, and meeting regulatory safety standards.",
    business_synonyms: ["Age Rating", "Safety Rating", "Age Guidance", "Child Safety", "Age Suitability", "Safety Classification"],
    applicable_domains: ["toys", "baby_products"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "high",
    regulatory_relevance: ["CPSC", "ASTM"]
}),

(battery_type:Column {
    column_id: "COL_BATTERY_TYPE_756",
    column_name: "BATTERY_TYPE",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 56,
    business_name: "Battery Type",
    business_description: "Specific battery chemistry and type classification (Alkaline, Lithium, Rechargeable, etc.). Essential for battery product categorization, performance characteristics, environmental compliance, and consumer application matching.",
    business_synonyms: ["Battery Chemistry", "Battery Technology", "Cell Type", "Battery Classification", "Power Type", "Energy Type"],
    applicable_domains: ["battery"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high"
}),

(skin_type_target:Column {
    column_id: "COL_SKIN_TYPE_TARGET_757",
    column_name: "SKIN_TYPE_TARGET",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 57,
    business_name: "Target Skin Type",
    business_description: "Target skin type for cosmetics and personal care products (Oily, Dry, Combination, Sensitive, etc.). Important for product positioning, consumer targeting, and personalized product recommendations.",
    business_synonyms: ["Skin Type", "Skin Classification", "Skin Condition", "Dermatological Type", "Skin Category", "Beauty Type"],
    applicable_domains: ["cosmetics", "personal_care"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "medium"
}),

// ========================================
// SUSTAINABILITY AND ENVIRONMENTAL
// ========================================

(sustainable_packaging_flag:Column {
    column_id: "COL_SUSTAINABLE_PACKAGING_FLAG_758",
    column_name: "SUSTAINABLE_PACKAGING_FLAG",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 58,
    business_name: "Sustainable Packaging Flag",
    business_description: "Indicates product uses sustainable or eco-friendly packaging. Important for CPG sustainability initiatives, environmental positioning, cost analysis, and meeting consumer environmental preferences.",
    business_synonyms: ["Eco Packaging", "Green Packaging", "Sustainable Package", "Environmental Packaging", "Eco-Friendly", "Recyclable Packaging"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium"
}),

(recyclable_flag:Column {
    column_id: "COL_RECYCLABLE_FLAG_759",
    column_name: "RECYCLABLE_FLAG",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 59,
    business_name: "Recyclable Flag",
    business_description: "Indicates product packaging is recyclable. Essential for CPG environmental compliance, sustainability reporting, and meeting consumer environmental expectations across all categories.",
    business_synonyms: ["Recyclable", "Recycling", "Recyclable Package", "Recycle Flag", "Environmental Flag", "Waste Reduction"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium"
}),

// ========================================
// METADATA AND AUDIT INFORMATION
// ========================================

(created_by:Column {
    column_id: "COL_CREATED_BY_760",
    column_name: "CREATED_BY",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 60,
    business_name: "Created By",
    business_description: "User who created the product hierarchy record. Important for CPG audit trails, accountability tracking, user activity monitoring, and understanding product creation patterns by individual or team.",
    business_synonyms: ["Creator", "Author", "Created User", "Originated By", "Product Creator", "Record Creator"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "low"
}),

(effective_start_date:Column {
    column_id: "COL_EFFECTIVE_START_DATE_761",
    column_name: "EFFECTIVE_START_DATE",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 61,
    business_name: "Effective Start Date",
    business_description: "Date when product hierarchy record becomes effective for slowly changing dimension tracking. Critical for CPG historical analysis, change tracking, and maintaining product hierarchy history over time.",
    business_synonyms: ["Start Date", "Valid From", "Effective From", "Begin Date", "Activation Date", "Valid Start"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    max_length: null,
    business_criticality: "medium"
}),

(effective_end_date:Column {
    column_id: "COL_EFFECTIVE_END_DATE_762",
    column_name: "EFFECTIVE_END_DATE",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 62,
    business_name: "Effective End Date",
    business_description: "Date when product hierarchy record expires for slowly changing dimension tracking. Important for CPG historical accuracy, version control, and maintaining clean product hierarchy transitions.",
    business_synonyms: ["End Date", "Valid To", "Effective To", "Expiry Date", "Deactivation Date", "Valid End"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    max_length: null,
    business_criticality: "medium"
}),

(created_timestamp:Column {
    column_id: "COL_CREATED_TIMESTAMP_763",
    column_name: "CREATED_TIMESTAMP",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 63,
    business_name: "Record Created Timestamp",
    business_description: "System timestamp when product hierarchy record was created. Used for audit trails, data lineage tracking, and data governance. Immutable after creation. Part of comprehensive data governance framework.",
    business_synonyms: ["Creation Time", "Insert Timestamp", "Created Date", "Record Creation", "Entry Timestamp", "Initial Load Time"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    max_length: null,
    business_criticality: "low"
}),

(modified_timestamp:Column {
    column_id: "COL_MODIFIED_TIMESTAMP_764",
    column_name: "MODIFIED_TIMESTAMP",
    table_id: "DIM_PRODUCT_HIERARCHY",
    ordinal_position: 64,
    business_name: "Last Modified Timestamp",
    business_description: "Most recent update timestamp for product hierarchy record. Tracks data freshness, change frequency, and maintenance activities. Updated automatically with any field modification for data governance compliance.",
    business_synonyms: ["Update Time", "Last Modified", "Change Timestamp", "Modified Date", "Last Updated", "Revision Timestamp"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    max_length: null,
    business_criticality: "low"
});

// ========================================
// CREATE TABLE-COLUMN RELATIONSHIPS
// ========================================

MATCH (t:Table {table_id: "DIM_PRODUCT_HIERARCHY"})
MATCH (c:Column {table_id: "DIM_PRODUCT_HIERARCHY"})
CREATE (t)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"]
}]->(c);

// ========================================
// VERIFICATION QUERIES
// ========================================

// Count total columns created
MATCH (c:Column {table_id: "DIM_PRODUCT_HIERARCHY"})
RETURN count(c) AS total_columns;

// Verify columns by business criticality
MATCH (c:Column {table_id: "DIM_PRODUCT_HIERARCHY"})
RETURN c.business_criticality AS criticality_level, 
       count(c) AS column_count
ORDER BY criticality_level;

// List critical columns
MATCH (c:Column {table_id: "DIM_PRODUCT_HIERARCHY"})
WHERE c.business_criticality = "critical"
RETURN c.business_name AS critical_column, 
       c.business_description
ORDER BY c.ordinal_position;

// Check domain-specific columns
MATCH (c:Column {table_id: "DIM_PRODUCT_HIERARCHY"})
WHERE c.domain_specific = true
RETURN c.business_name AS domain_specific_column, 
       c.applicable_domains AS specific_domains,
       c.regulatory_relevance AS regulations
ORDER BY c.ordinal_position;

// Check columns by category
MATCH (c:Column {table_id: "DIM_PRODUCT_HIERARCHY"})
RETURN 
    CASE 
        WHEN c.ordinal_position <= 3 THEN "Core Identifiers"
        WHEN c.ordinal_position <= 6 THEN "Company & Business Unit"
        WHEN c.ordinal_position <= 10 THEN "Category Management"
        WHEN c.ordinal_position <= 15 THEN "Brand Hierarchy"
        WHEN c.ordinal_position <= 20 THEN "Product Line & Details"
        WHEN c.ordinal_position <= 26 THEN "Product Attributes & Variants"
        WHEN c.ordinal_position <= 30 THEN "Business Classification"
        WHEN c.ordinal_position <= 34 THEN "Financial & Pricing"
        WHEN c.ordinal_position <= 37 THEN "Market & Competitive"
        WHEN c.ordinal_position <= 40 THEN "Regulatory & Compliance"
        WHEN c.ordinal_position <= 43 THEN "Supply Chain & Manufacturing"
        WHEN c.ordinal_position <= 47 THEN "Status & Lifecycle"
        WHEN c.ordinal_position <= 51 THEN "Quality & Certification"
        WHEN c.ordinal_position <= 57 THEN "Domain-Specific Attributes"
        WHEN c.ordinal_position <= 59 THEN "Sustainability & Environmental"
        ELSE "Metadata & Audit"
    END AS category,
    count(c) AS column_count
ORDER BY category;

// Verify semantic types distribution
MATCH (c:Column {table_id: "DIM_PRODUCT_HIERARCHY"})
RETURN c.semantic_type AS semantic_type, 
       count(c) AS column_count
ORDER BY column_count DESC;

// Check for primary key
MATCH (c:Column {table_id: "DIM_PRODUCT_HIERARCHY"})
WHERE c.is_primary_key = true
RETURN c.business_name AS primary_key_column, 
       c.column_name AS column_name;

// Verify regulatory relevance columns
MATCH (c:Column {table_id: "DIM_PRODUCT_HIERARCHY"})
WHERE c.regulatory_relevance IS NOT NULL
RETURN c.business_name AS regulatory_column, 
       c.regulatory_relevance AS regulations,
       c.applicable_domains AS domains
ORDER BY c.ordinal_position;

// Check flag/boolean columns
MATCH (c:Column {table_id: "DIM_PRODUCT_HIERARCHY"})
WHERE c.data_type = "BOOLEAN"
RETURN c.business_name AS boolean_column,
       c.business_description
ORDER BY c.ordinal_position;

// Verify hierarchical structure columns
MATCH (c:Column {table_id: "DIM_PRODUCT_HIERARCHY"})
WHERE c.business_name CONTAINS "Level" OR c.business_name CONTAINS "Hierarchy" OR c.business_name CONTAINS "Parent"
RETURN c.business_name AS hierarchy_column,
       c.business_criticality AS criticality
ORDER BY c.ordinal_position;

// Check brand and category management columns
MATCH (c:Column {table_id: "DIM_PRODUCT_HIERARCHY"})
WHERE c.business_name CONTAINS "Brand" OR c.business_name CONTAINS "Category" OR c.business_name CONTAINS "Manager"
RETURN c.business_name AS management_column,
       c.semantic_type AS type
ORDER BY c.ordinal_position;

// Verify financial and pricing columns
MATCH (c:Column {table_id: "DIM_PRODUCT_HIERARCHY"})
WHERE c.business_name CONTAINS "Price" OR c.business_name CONTAINS "Cost" OR c.business_name CONTAINS "Margin"
RETURN c.business_name AS financial_column,
       c.data_type AS data_type,
       c.precision AS precision,
       c.scale AS scale
ORDER BY c.ordinal_position;

// ========================================
// END OF DIM_PRODUCT_HIERARCHY COLUMN CREATION
// ========================================