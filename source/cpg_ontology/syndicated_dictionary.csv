﻿column_name,description,column_category,table_type,
SALES_COUNTRY,Country where the sales data is recorded,Market Identifiers,DIMENSION,
DISTRIBUTION_CHANNEL,Distribution channel or market coverage area,Market Identifiers,DIMENSION,
MARKET_ID,Market dimension identifier tag linking to market hierarchy structure | Unified mapping: Local market unique identifier tag used for market segmentation,Dimension Keys,DIMENSION,
MARKET_SHORT_NAME,Short local market name/code for the Italian market,Market Identifiers,DIMENSION,
MARKET_FULL_NAME,Long descriptive local market name for the Italian market,Market Identifiers,DIMENSION,
SALES_REGION,Regional classification for geographical grouping,Market Identifiers,DIMENSION,
MARKET_DISPLAY_ORDER,Sequential ordering number for display purposes (Range: 1-227),Market Structure,DIMENSION,
HIERARCHY_LEVEL_NAME,Descriptive name of the hierarchical level or specific market grouping,Market Structure,DIMENSION,
HIERARCHY_LEVEL_NUMBER,Numeric level within the market hierarchy (1=National/Channel 2=Regional/City),Market Structure,DIMENSION,
<PERSON><PERSON><PERSON><PERSON>HY_NAME,Hierarchy name identifier combining numeric code with market description,Market Structure,DIMENSION,
HIERARCHY_NUMBER,Hierarchy number identifier for market tree organization (Range: 1-193),Market Structure,DIMENSION,
PARENT_HIERARCHY_ID,Reference to the parent market node's TAG (for Level 2 records only),Market Structure,DIMENSION,
MARKET_HIERARCHY_ID,Unique market hierarchy identifier tag for each market/channel node,Market Identifiers,DIMENSION,
GLOBAL_BRAND,Global standardized brand name,Product Dimensions Global,DIMENSION,
LOCAL_BRAND,Local brand name as appears in the Italian market,Product Dimensions Local,DIMENSION,
CATEGORY,Product category classification,Product Dimensions Global,DIMENSION,
GLOBAL_CHEMISTRY,Global standardized battery chemistry/technology type,Product Dimensions Global,DIMENSION,
LOCAL_CHEMISTRY,Local chemistry/technology type of the battery in Italian terminology,Product Dimensions Local,DIMENSION,
HIERARCHY_GLOBAL_LEVEL,Global standardized hierarchical level name,Product Hierarchy,DIMENSION,
HIERARCHY_LEVEL_NUMBER,Hierarchical level number indicating the aggregation level (3=Manufacturer 4=Brand 9=Item/SKU),Product Hierarchy,DIMENSION,
MANUFACTURER_GLOBAL_NAME,Global standardized manufacturer name,Product Dimensions Global,DIMENSION,
MANUFACTURER_LOCAL_NAME,Local manufacturer name as appears in the Italian market,Product Dimensions Local,DIMENSION,
PACKAGE_UNIT_COUNT,Number of units contained within the package | Unified mapping: Global standardized pack count (number of units per package),Product Attributes,DIMENSION,
PACKAGE_UNIT_COUNT_LOCAL,Local pack count indicating number of units in the package,Product Dimensions Local,DIMENSION,
PACKAGE_TYPE,Packaging type classification (individual multipacks etc.),Product Attributes,DIMENSION,
PRODUCT_ID,Product dimension identifier tag linking to product hierarchy structure | Unified mapping: Local product unique identifier tag for product tracking,Dimension Keys,DIMENSION,
PRODUCT_SHORT_NAME,Short local product name with basic product attributes,Product Dimensions Local,DIMENSION,
PRODUCT_FULL_NAME,Long descriptive local product name with full product specifications,Product Dimensions Local,DIMENSION,
BRAND_OWNER_COMPANY,Brand ownership identification for competitive analysis,Product Attributes,DIMENSION,
EQUIVALENT_UNIT_TYPE,Equivalent unit measurement for normalized analysis,Product Attributes,DIMENSION,
EQUIVALENT_UNIT_SIZE,Equivalent unit size for standardized comparison across products,Product Attributes,DIMENSION,
PHYSICAL_UNIT_MEASURE,Standard physical unit of measurement for the product,Product Attributes,DIMENSION,
PRICE_SEGMENT,Price positioning segment within the market (Premium Value etc.),Product Attributes,DIMENSION,
MARKET_SEGMENT,Market segment classification for the product category,Product Attributes,DIMENSION,
SIZE_SPECIFICATION,Product size specification in standard units,Product Attributes,DIMENSION,
SIZE_LOCAL,Local size designation or model number of the battery,Product Dimensions Local,DIMENSION,
SUB_BRAND_GLOBAL,Global standardized sub-brand or product line name,Product Dimensions Global,DIMENSION,
SUB_BRAND_LOCAL,Local sub-brand or product line name within the main brand,Product Dimensions Local,DIMENSION,
SUB_SEGMENT,Sub-segment classification within the broader market segment,Product Attributes,DIMENSION,
TECHNOLOGY_TYPE,Technology type or manufacturing process classification,Product Attributes,DIMENSION,
TIER_CLASSIFICATION,Product tier classification indicating market positioning,Product Dimensions Global,DIMENSION,
INDIVIDUAL_UNIT_SIZE,Individual unit size within the package,Product Attributes,DIMENSION,
UNIVERSAL_PRODUCT_CODE,Universal Product Code for unique product identification,Product Identifiers,DIMENSION,
SEGMENT,Market segment classification for the product category,Product Attributes,DIMENSION,
SIZE,Product size specification in standard units,Product Attributes,DIMENSION,
SIZE_LOCAL,Local size designation or model number of the battery,Product Dimensions Local,DIMENSION,
SUB_BRAND,Global standardized sub-brand or product line name,Product Dimensions Global,DIMENSION,
SUB_BRAND_LOCAL,Local sub-brand or product line name within the main brand,Product Dimensions Local,DIMENSION,
SUBSEGMENT,Sub-segment classification within the broader market segment,Product Attributes,DIMENSION,
TECHNOLOGY,Technology type or manufacturing process classification,Product Attributes,DIMENSION,
TIER,Product tier classification indicating market positioning,Product Dimensions Global,DIMENSION,
UNIT_SIZE,Individual unit size within the package,Product Attributes,DIMENSION,
UPC,Universal Product Code for unique product identification,Product Identifiers,DIMENSION,
SALES_PERIOD_DATE,Date period in standard date format (MM/DD/YYYY),Time Dimensions,DIMENSION,
SALES_PERIOD_WEEK_FORMAT,Local period identifier in week format (WYYYYYY format - W+Year+Week),Time Dimensions,DIMENSION,
TIME_PERIOD_ID,Period dimension identifier tag linking to time period structure,Dimension Keys,DIMENSION,
SALES_YEAR,Calendar year of the sales data (Range: 2020-2023),Time Dimensions,DIMENSION,
PERIOD,Date period in standard date format (MM/DD/YYYY),Time Dimensions,DIMENSION,
PERIOD_LOCAL,Local period identifier in week format (WYYYYYY format - W+Year+Week),Time Dimensions,DIMENSION,
PER_TAG,Period dimension identifier tag linking to time period structure,Dimension Keys,DIMENSION,
YEAR,Calendar year of the sales data (Range: 2020-2023),Time Dimensions,DIMENSION,
OUT_OF_STOCK_GAP_4_WEEK_NUMERIC,4-week numeric distribution out-of-stock gap (currently not populated),Distribution Metrics,FACT,
NUMERIC_DISTRIBUTION_SELLING,Numeric distribution of selling stores,Distribution Metrics,FACT,
NUMERIC_DISTRIBUTION_OUT_OF_STOCK,Numeric distribution of stores with out-of-stock,Distribution Metrics,FACT,
NUMERIC_DISTRIBUTION_PURCHASING,Numeric distribution of purchasing stores (currently not populated),Distribution Metrics,FACT,
WEIGHTED_DISTRIBUTION_SELLING,Weighted distribution of selling stores,Distribution Metrics,FACT,
WEIGHTED_DISTRIBUTION_OUT_OF_STOCK,Weighted distribution of out-of-stock occurrences,Distribution Metrics,FACT,
WEIGHTED_DISTRIBUTION_PURCHASING,Weighted distribution of purchasing stores (currently not populated),Distribution Metrics,FACT,
NUMERIC_DISTRIBUTION_HANDLING,Numeric distribution - count of stores handling the product,Distribution Metrics,FACT,
NUMERIC_DISTRIBUTION_PROMOTIONAL,Numeric distribution during promotional periods,Distribution Metrics,FACT,
NUMERIC_DISTRIBUTION_POP_MATERIAL,Numeric distribution with special POP material,Distribution Metrics,FACT,
NUMERIC_DISTRIBUTION_SPECIAL_OFFERS,Numeric distribution with special offers,Distribution Metrics,FACT,
NUMERIC_DISTRIBUTION_SPECIAL_DISPLAY,Numeric distribution in special display locations,Distribution Metrics,FACT,
AVERAGE_ITEMS_PER_STORE,Average number of items per store carrying the product (Range: 10-36),Distribution Metrics,FACT,
NUMERIC_DISTRIBUTION_SELLING_COUNT,Numeric distribution selling count metric (Range: 0-17),Distribution Metrics,FACT,
WEIGHTED_DISTRIBUTION_PERCENTAGE,Weighted distribution - percentage of sales handled by stores stocking the product,Distribution Metrics,FACT,
WEIGHTED_DISTRIBUTION_PROMOTIONAL,Weighted distribution during promotional periods,Distribution Metrics,FACT,
WEIGHTED_DISTRIBUTION_POP_MATERIAL,Weighted distribution with special POP material,Distribution Metrics,FACT,
WEIGHTED_DISTRIBUTION_SPECIAL_OFFERS,Weighted distribution with special offers,Distribution Metrics,FACT,
WEIGHTED_DISTRIBUTION_SPECIAL_DISPLAY,Weighted distribution in special display locations,Distribution Metrics,FACT,
WEIGHTED_DISTRIBUTION_SELLING_METRIC,Weighted distribution selling metric (Range: 0-23),Distribution Metrics,FACT,
OUT_OF_STOCK_GAP_4_WEEK,4-week numeric distribution out-of-stock gap (currently not populated),Distribution Metrics,FACT,
SELLING_STORES_NUMERIC,Numeric distribution of selling stores,Distribution Metrics,FACT,
OUT_OF_STOCK_NUMERIC,Numeric distribution of stores with out-of-stock,Distribution Metrics,FACT,
PURCHASING_NUMERIC,Numeric distribution of purchasing stores (currently not populated),Distribution Metrics,FACT,
SELLING_STORES_WEIGHTED,Weighted distribution of selling stores,Distribution Metrics,FACT,
OUT_OF_STOCK_WEIGHTED,Weighted distribution of out-of-stock occurrences,Distribution Metrics,FACT,
PURCHASING_WEIGHTED,Weighted distribution of purchasing stores (currently not populated),Distribution Metrics,FACT,
STORE_COUNT_NUMERIC,Numeric distribution - count of stores handling the product,Distribution Metrics,FACT,
PROMOTIONAL_PERIOD_NUMERIC,Numeric distribution during promotional periods,Distribution Metrics,FACT,
SPECIAL_POP_MATERIALS_NUMERIC,Numeric distribution with special POP material,Distribution Metrics,FACT,
SPECIAL_OFFERS_NUMERIC,Numeric distribution with special offers,Distribution Metrics,FACT,
SPECIAL_DISPLAY_LOCATIONS_NUMERIC,Numeric distribution in special display locations,Distribution Metrics,FACT,
AVG_ITEMS_PER_STORE,Average number of items per store carrying the product (Range: 10-36),Distribution Metrics,FACT,
SELLING_COUNT_NUMERIC,Numeric distribution selling count metric (Range: 0-17),Distribution Metrics,FACT,
SALES_PERCENTAGE_WEIGHTED,Weighted distribution - percentage of sales handled by stores stocking the product,Distribution Metrics,FACT,
PROMOTIONAL_PERIOD_WEIGHTED,Weighted distribution during promotional periods,Distribution Metrics,FACT,
SPECIAL_POP_MATERIALS_WEIGHTED,Weighted distribution with special POP material,Distribution Metrics,FACT,
SPECIAL_OFFERS_WEIGHTED,Weighted distribution with special offers,Distribution Metrics,FACT,
SPECIAL_DISPLAY_LOCATIONS_WEIGHTED,Weighted distribution in special display locations,Distribution Metrics,FACT,
SELLING_COUNT_WEIGHTED,Weighted distribution selling metric (Range: 0-23),Distribution Metrics,FACT,
AVERAGE_INVENTORY_PER_STORE,Average inventory per store in equivalent units,Inventory Metrics,FACT,
ACTIVE_INVENTORY_EQUIVALENT_UNITS,Active non-cold inventory in equivalent units (thousands),Inventory Metrics,FACT,
TOTAL_INVENTORY_EQUIVALENT_UNITS,Total inventory in equivalent units (thousands),Inventory Metrics,FACT,
AVG_INVENTORY_STORE,Average inventory per store in equivalent units,Inventory Metrics,FACT,
NON_COLD_INVENTORY_ACTIVE,Active non-cold inventory in equivalent units (thousands),Inventory Metrics,FACT,
TOTAL_INVENTORY,Total inventory in equivalent units (thousands),Inventory Metrics,FACT,
ACV_SALES_GAP_4_WEEK_WEIGHTED,4-week ACV sales gap - weighted distribution (currently not populated),Sales Gap Metrics,FACT,
SALES_GAP_VALUE_4_WEEK_WEIGHTED,4-week sales gap value - weighted distribution (currently not populated),Sales Gap Metrics,FACT,
MARKET_SHARE_BY_VOLUME,Market share by equivalent units (basis points 10000=100%),Share Metrics,FACT,
MARKET_SHARE_FAST_MOVING_INVENTORY,Share of active (fast-moving) inventory by equivalent units,Share Metrics,FACT,
MARKET_SHARE_PURCHASES_VOLUME,Share of total purchases by equivalent units (currently not populated),Share Metrics,FACT,
MARKET_SHARE_TOTAL_INVENTORY,Share of total inventory by equivalent units,Share Metrics,FACT,
MARKET_SHARE_BY_VALUE,Market share by value (basis points 10000=100%),Share Metrics,FACT,
ACV_SALES_GAP_4_WEEK,4-week ACV sales gap - weighted distribution (currently not populated),Sales Gap Metrics,FACT,
SALES_GAP_4_WEEK,4-week sales gap value - weighted distribution (currently not populated),Sales Gap Metrics,FACT,
MARKET_SAHRE_EQUIVALENT,Market share by equivalent units (basis points 10000=100%),Share Metrics,FACT,
FAST_MOVING_INVENTORY_ACTIVE,Share of active (fast-moving) inventory by equivalent units,Share Metrics,FACT,
TOTAL_PURCHASES_EQUIVALENT,Share of total purchases by equivalent units (currently not populated),Share Metrics,FACT,
TOTAL_INVENTORY_EQUIVALENT,Share of total inventory by equivalent units,Share Metrics,FACT,
MARKET_SHARE_VALUE,Market share by value (basis points 10000=100%),Share Metrics,FACT,
AVERAGE_PRICE_PER_EQUIVALENT_UNIT,Average price per equivalent unit in Colombian pesos,Price Metrics,FACT,
PRICE_PER_INDIVIDUAL_UNIT,Price per individual unit (currently all values are 0),Sales Metrics,FACT,
PRICE_PER_VOLUME_UNIT,Price per volume unit (currently all values are 0),Sales Metrics,FACT,
AVG_PRICE_PER_UNIT,Average price per equivalent unit in Colombian pesos,Price Metrics,FACT,
PRICE_PER_UNIT,Price per individual unit (currently all values are 0),Sales Metrics,FACT,
PRICE_PER_VOLUME,Price per volume unit (currently all values are 0),Sales Metrics,FACT,
PROMOTIONAL_SALES_VALUE_EUR,Sales value from any promotional activities in local currency (Range: 0-224363.6),Promotional Metrics,FACT,
PROMOTIONAL_SALES_VALUE_USD,Promotional sales value converted to USD (Range: 0-237488.87),Promotional Metrics,FACT,
PROMOTIONAL_SALES_VOLUME,Sales volume from any promotional activities (Range: 0-429943.4),Promotional Metrics,FACT,
PURCHASES_EQUIVALENT_UNITS,Purchases in equivalent units (currently not populated),Purchase Metrics,FACT,
AVERAGE_PURCHASES_PER_STORE,Average purchases per store in equivalent units (currently not populated),Purchase Metrics,FACT,
DIRECT_PURCHASES_EQUIVALENT_UNITS,Direct purchases in equivalent units (currently not populated),Purchase Metrics,FACT,
AVERAGE_SALES_PER_STORE,Average sales per store in equivalent units,Sales Metrics,FACT,
SALES_VOLUME_EQUIVALENT_UNITS,Sales in equivalent units (thousands) - Core volume metric,Sales Metrics,FACT,
CURRENCY_EXCHANGE_RATE_EUR_USD,Foreign exchange rate for EUR to USD conversion (Range: 0.9832-1.217),Sales Metrics,FACT,
SALES_QUANTITY_UNITS,Number of individual units sold (Range: 11-118914 units),Sales Metrics,FACT,
SALES_VALUE_EUR,Total sales value in local currency (EUR) (Range: 15.9-298589.6),Sales Metrics,FACT,
SALES_VALUE_USD,Total sales value converted to USD using FOREX rate (Range: 18.24-363174.53),Sales Metrics,FACT,
SALES_VOLUME_STANDARDIZED,Total sales volume in standardized units (Range: 11.6-1239125.9),Sales Metrics,FACT,
SALES_VALUE_LOCAL_CURRENCY,Sales value in local currency (millions of Colombian pesos) - Core value metric,Sales Metrics,FACT,
SALES_VALUE_ANYPROMO,Sales value from any promotional activities in local currency (Range: 0-224363.6),Promotional Metrics,FACT,
SALES_VALUE_ANYPROMO_USD,Promotional sales value converted to USD (Range: 0-237488.87),Promotional Metrics,FACT,
SALES_VOLUME_ANYPROMO,Sales volume from any promotional activities (Range: 0-429943.4),Promotional Metrics,FACT,
PURCHASE_UNITS,Purchases in equivalent units (currently not populated),Purchase Metrics,FACT,
AVG_PURCHASE_STORE,Average purchases per store in equivalent units (currently not populated),Purchase Metrics,FACT,
DIRECT_PURCHASE,Direct purchases in equivalent units (currently not populated),Purchase Metrics,FACT,
AVG_SALES_PER_STORE,Average sales per store in equivalent units,Sales Metrics,FACT,
SALES_UNIT_EQUIVALENT,Sales in equivalent units (thousands) - Core volume metric,Sales Metrics,FACT,
FOREX,Foreign exchange rate for EUR to USD conversion (Range: 0.9832-1.217),Sales Metrics,FACT,
SALES_UNITS,Number of individual units sold (Range: 11-118914 units),Sales Metrics,FACT,
SALES_VALUE,Total sales value in local currency (EUR) (Range: 15.9-298589.6),Sales Metrics,FACT,
SALES_VALUE_USD,Total sales value converted to USD using FOREX rate (Range: 18.24-363174.53),Sales Metrics,FACT,
SALES_VOLUME,Total sales volume in standardized units (Range: 11.6-1239125.9),Sales Metrics,FACT,
SALES_VALUE_LOCAL_CURRENCY,Sales value in local currency (millions of Colombian pesos) - Core value metric,Sales Metrics,FACT,