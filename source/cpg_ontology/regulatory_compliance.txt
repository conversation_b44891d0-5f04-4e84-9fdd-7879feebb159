// ========================================
// FACT_REGULATORY_COMPLIANCE - COMPREHENSIVE COLUMN CREATION
// Complete regulatory compliance fact table columns for all CPG domains
// ========================================

// Clear existing columns for FACT_REGULATORY_COMPLIANCE (optional)
MATCH (c:Column {table_id: "FACT_REGULATORY_COMPLIANCE"}) DETACH DELETE c;

// ========================================
// CORE COMPLIANCE EVENT IDENTIFIERS AND FOREIGN KEYS
// ========================================

CREATE 
(compliance_event_id:Column {
    column_id: "COL_COMPLIANCE_EVENT_ID_FACT_1001",
    column_name: "COMPLIANCE_EVENT_ID",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 1,
    business_name: "Compliance Event ID",
    business_description: "Unique identifier for regulatory compliance events, inspections, violations, and certifications across all CPG categories. Critical for compliance tracking, audit trails, regulatory reporting, and managing compliance activities across alcoholic beverages, pharmaceuticals, toys, cosmetics, and all consumer product categories.",
    business_synonyms: ["Event Key", "Compliance ID", "Regulatory Event ID", "Compliance Reference", "Event Number", "Compliance Case ID", "Regulatory Reference"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_primary_key: true,
    business_criticality: "critical"
}),

(date_key:Column {
    column_id: "COL_DATE_KEY_FACT_1002",
    column_name: "DATE_KEY",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 2,
    business_name: "Date Key",
    business_description: "Foreign key to date dimension for temporal compliance analysis and regulatory timeline tracking. Essential for CPG compliance trend analysis, inspection scheduling, regulatory deadline management, and understanding compliance patterns over time across all product categories.",
    business_synonyms: ["Event Date Key", "Compliance Date Key", "Date FK", "Date Reference", "Temporal Key", "Regulatory Date Key"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "INTEGER",
    max_length: null,
    is_foreign_key: true,
    business_criticality: "critical"
}),

(store_key:Column {
    column_id: "COL_STORE_KEY_FACT_1003",
    column_name: "STORE_KEY",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 3,
    business_name: "Store Key",
    business_description: "Foreign key to store dimension for location-based compliance tracking and facility-specific regulatory management. Critical for CPG facility compliance, location-specific violations, retail compliance monitoring, and understanding geographic compliance patterns.",
    business_synonyms: ["Location Key", "Facility Key", "Store FK", "Site Key", "Location Reference", "Facility Reference"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_foreign_key: true,
    business_criticality: "critical"
}),

(product_hierarchy_key:Column {
    column_id: "COL_PRODUCT_HIERARCHY_KEY_FACT_1004",
    column_name: "PRODUCT_HIERARCHY_KEY",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 4,
    business_name: "Product Hierarchy Key",
    business_description: "Foreign key to product hierarchy dimension for product-specific compliance tracking and category-level regulatory analysis. Essential for CPG product compliance, SKU-level violations, category regulatory patterns, and brand compliance management.",
    business_synonyms: ["Product Key", "SKU Key", "Product FK", "Item Key", "Product Reference", "Hierarchy FK"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_foreign_key: true,
    business_criticality: "high"
}),

(customer_key:Column {
    column_id: "COL_CUSTOMER_KEY_FACT_1005",
    column_name: "CUSTOMER_KEY",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 5,
    business_name: "Customer Key",
    business_description: "Foreign key to customer dimension for customer-related compliance events and consumer complaint tracking. Important for CPG consumer safety incidents, customer complaint resolution, adverse event reporting, and customer-specific compliance issues.",
    business_synonyms: ["Customer FK", "Consumer Key", "Customer Reference", "Consumer FK", "Customer ID FK", "Consumer Reference"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_foreign_key: true,
    business_criticality: "medium"
}),

// ========================================
// COMPLIANCE EVENT CLASSIFICATION
// ========================================

(compliance_event_type:Column {
    column_id: "COL_COMPLIANCE_EVENT_TYPE_FACT_1006",
    column_name: "COMPLIANCE_EVENT_TYPE",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 6,
    business_name: "Compliance Event Type",
    business_description: "Primary classification of compliance event (Inspection, Violation, Certification, Audit, Incident, etc.). Critical for CPG compliance categorization, event type analysis, regulatory reporting, and understanding compliance activity patterns across all categories.",
    business_synonyms: ["Event Type", "Compliance Type", "Regulatory Event Type", "Event Classification", "Compliance Category", "Event Category"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "critical"
}),

(compliance_subtype:Column {
    column_id: "COL_COMPLIANCE_SUBTYPE_FACT_1007",
    column_name: "COMPLIANCE_SUBTYPE",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 7,
    business_name: "Compliance Event Subtype",
    business_description: "Detailed compliance event classification (Routine Inspection, Follow-up Audit, Safety Violation, Labeling Issue, etc.). Essential for CPG granular compliance analysis, specific violation tracking, and detailed regulatory categorization.",
    business_synonyms: ["Event Subtype", "Detailed Type", "Compliance Subcategory", "Event Detail", "Specific Type", "Detailed Classification"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "high"
}),

(regulatory_body:Column {
    column_id: "COL_REGULATORY_BODY_FACT_1008",
    column_name: "REGULATORY_BODY",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 8,
    business_name: "Regulatory Body",
    business_description: "Regulatory agency or authority responsible for compliance oversight (FDA, TTB, CPSC, EPA, OSHA, etc.). Critical for CPG regulatory management, agency-specific reporting, jurisdiction tracking, and understanding regulatory landscape complexity.",
    business_synonyms: ["Regulatory Agency", "Governing Body", "Regulatory Authority", "Compliance Agency", "Oversight Agency", "Regulatory Entity"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "critical"
}),

(regulation_reference:Column {
    column_id: "COL_REGULATION_REFERENCE_FACT_1009",
    column_name: "REGULATION_REFERENCE",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 9,
    business_name: "Regulation Reference",
    business_description: "Specific regulation, code, or standard referenced in compliance event (21 CFR 211, CPSIA Section 101, TTB 27 CFR, etc.). Essential for CPG regulatory traceability, specific requirement tracking, and compliance documentation.",
    business_synonyms: ["Regulation Code", "Regulatory Reference", "Legal Reference", "Code Reference", "Standard Reference", "Regulation Citation"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 200,
    business_criticality: "high"
}),

// ========================================
// COMPLIANCE STATUS AND SEVERITY MEASURES
// ========================================

(compliance_status:Column {
    column_id: "COL_COMPLIANCE_STATUS_FACT_1010",
    column_name: "COMPLIANCE_STATUS",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 10,
    business_name: "Compliance Status",
    business_description: "Current status of compliance event (Open, In Progress, Resolved, Closed, Escalated). Critical for CPG compliance management, resolution tracking, ongoing issue monitoring, and compliance workflow management across all categories.",
    business_synonyms: ["Event Status", "Compliance State", "Resolution Status", "Case Status", "Compliance Condition", "Event State"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "critical"
}),

(severity_level:Column {
    column_id: "COL_SEVERITY_LEVEL_FACT_1011",
    column_name: "SEVERITY_LEVEL",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 11,
    business_name: "Compliance Severity Level",
    business_description: "Severity classification of compliance issue (Critical, High, Medium, Low, Informational). Essential for CPG risk prioritization, resource allocation, escalation procedures, and understanding compliance impact across different severity levels.",
    business_synonyms: ["Severity Rating", "Risk Level", "Priority Level", "Severity Classification", "Impact Level", "Severity Grade"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "critical"
}),

(risk_score:Column {
    column_id: "COL_RISK_SCORE_FACT_1012",
    column_name: "RISK_SCORE",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 12,
    business_name: "Compliance Risk Score",
    business_description: "Numerical risk assessment score for compliance event (0-100 scale). Important for CPG quantitative risk analysis, compliance prioritization, predictive risk modeling, and understanding relative risk across different compliance events.",
    business_synonyms: ["Risk Rating", "Risk Index", "Compliance Score", "Risk Assessment", "Risk Value", "Risk Metric"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(compliance_score:Column {
    column_id: "COL_COMPLIANCE_SCORE_FACT_1013",
    column_name: "COMPLIANCE_SCORE",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 13,
    business_name: "Overall Compliance Score",
    business_description: "Overall compliance performance score for event or inspection (0-100 scale). Critical for CPG compliance benchmarking, performance tracking, regulatory relationship management, and continuous improvement measurement.",
    business_synonyms: ["Compliance Rating", "Performance Score", "Compliance Grade", "Compliance Index", "Compliance Performance", "Regulatory Score"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

// ========================================
// VIOLATION AND PENALTY MEASURES
// ========================================

(violation_count:Column {
    column_id: "COL_VIOLATION_COUNT_FACT_1014",
    column_name: "VIOLATION_COUNT",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 14,
    business_name: "Violation Count",
    business_description: "Number of violations identified in compliance event. Fundamental measure for CPG violation tracking, compliance performance measurement, trend analysis, and understanding violation frequency across categories and locations.",
    business_synonyms: ["Number of Violations", "Violation Quantity", "Issue Count", "Non-Compliance Count", "Infraction Count", "Deficiency Count"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "critical",
    additive_type: "additive"
}),

(critical_violation_count:Column {
    column_id: "COL_CRITICAL_VIOLATION_COUNT_FACT_1015",
    column_name: "CRITICAL_VIOLATION_COUNT",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 15,
    business_name: "Critical Violation Count",
    business_description: "Number of critical or severe violations identified. Essential for CPG high-priority issue tracking, immediate action requirements, escalation triggers, and understanding critical compliance failures across categories.",
    business_synonyms: ["Critical Issues", "Severe Violations", "High Priority Count", "Critical Deficiencies", "Major Violations", "Serious Issues"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "critical",
    additive_type: "additive"
}),

(monetary_penalty_amount:Column {
    column_id: "COL_MONETARY_PENALTY_AMOUNT_FACT_1016",
    column_name: "MONETARY_PENALTY_AMOUNT",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 16,
    business_name: "Monetary Penalty Amount",
    business_description: "Financial penalties and fines assessed for compliance violations. Critical for CPG financial impact analysis, compliance cost tracking, penalty trend analysis, and understanding financial consequences of non-compliance.",
    business_synonyms: ["Fine Amount", "Penalty Fee", "Financial Penalty", "Regulatory Fine", "Violation Fine", "Compliance Penalty"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 2,
    business_criticality: "high",
    additive_type: "additive"
}),

(remediation_cost:Column {
    column_id: "COL_REMEDIATION_COST_FACT_1017",
    column_name: "REMEDIATION_COST",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 17,
    business_name: "Remediation Cost",
    business_description: "Cost of corrective actions and remediation efforts. Important for CPG compliance investment tracking, total cost of non-compliance analysis, and understanding remediation expenses across different violation types.",
    business_synonyms: ["Correction Cost", "Fix Cost", "Remedial Cost", "Corrective Action Cost", "Resolution Cost", "Compliance Cost"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 2,
    business_criticality: "high",
    additive_type: "additive"
}),

(total_compliance_cost:Column {
    column_id: "COL_TOTAL_COMPLIANCE_COST_FACT_1018",
    column_name: "TOTAL_COMPLIANCE_COST",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 18,
    business_name: "Total Compliance Cost",
    business_description: "Total cost including penalties, remediation, and administrative expenses. Essential for CPG total cost of compliance analysis, budgeting, cost trend analysis, and understanding complete financial impact of compliance events.",
    business_synonyms: ["Total Cost", "Complete Cost", "Full Compliance Cost", "Aggregate Cost", "Combined Cost", "Overall Cost"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 2,
    business_criticality: "high",
    additive_type: "additive"
}),

// ========================================
// INSPECTION AND AUDIT MEASURES
// ========================================

(inspection_duration_hours:Column {
    column_id: "COL_INSPECTION_DURATION_HOURS_FACT_1019",
    column_name: "INSPECTION_DURATION_HOURS",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 19,
    business_name: "Inspection Duration Hours",
    business_description: "Total hours spent on inspection or audit activity. Important for CPG inspection burden analysis, resource planning, regulatory relationship management, and understanding inspection complexity across facilities and categories.",
    business_synonyms: ["Inspection Time", "Audit Hours", "Inspection Length", "Review Duration", "Audit Time", "Inspection Period"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 8,
    scale: 2,
    business_criticality: "medium",
    additive_type: "additive"
}),

(areas_inspected_count:Column {
    column_id: "COL_AREAS_INSPECTED_COUNT_FACT_1020",
    column_name: "AREAS_INSPECTED_COUNT",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 20,
    business_name: "Areas Inspected Count",
    business_description: "Number of different areas, processes, or systems examined during inspection. Essential for CPG inspection scope analysis, coverage measurement, and understanding inspection thoroughness across different facility types.",
    business_synonyms: ["Inspection Areas", "Systems Reviewed", "Process Count", "Area Count", "Inspection Scope", "Review Areas"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "medium",
    additive_type: "additive"
}),

(inspector_count:Column {
    column_id: "COL_INSPECTOR_COUNT_FACT_1021",
    column_name: "INSPECTOR_COUNT",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 21,
    business_name: "Inspector Count",
    business_description: "Number of inspectors or auditors involved in compliance event. Important for CPG inspection resource tracking, inspector relationship management, and understanding inspection team size impact on findings and duration.",
    business_synonyms: ["Number of Inspectors", "Auditor Count", "Inspector Team Size", "Review Team Count", "Inspection Team", "Auditor Team"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "low",
    additive_type: "additive"
}),

// ========================================
// CERTIFICATION AND LICENSING MEASURES
// ========================================

(certificate_validity_days:Column {
    column_id: "COL_CERTIFICATE_VALIDITY_DAYS_FACT_1022",
    column_name: "CERTIFICATE_VALIDITY_DAYS",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 22,
    business_name: "Certificate Validity Days",
    business_description: "Number of days certificate or license remains valid. Critical for CPG certification management, renewal planning, expiration tracking, and ensuring continuous compliance across all regulated categories and operations.",
    business_synonyms: ["Validity Period", "Certificate Duration", "License Period", "Certification Term", "Valid Days", "License Duration"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(renewal_fee_amount:Column {
    column_id: "COL_RENEWAL_FEE_AMOUNT_FACT_1023",
    column_name: "RENEWAL_FEE_AMOUNT",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 23,
    business_name: "Renewal Fee Amount",
    business_description: "Cost of certificate or license renewal. Important for CPG regulatory cost management, budget planning, renewal cost tracking, and understanding ongoing compliance expenses across different certifications and jurisdictions.",
    business_synonyms: ["Renewal Cost", "License Fee", "Certification Fee", "Renewal Expense", "License Cost", "Certificate Cost"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 2,
    business_criticality: "medium",
    additive_type: "additive"
}),

(certification_score:Column {
    column_id: "COL_CERTIFICATION_SCORE_FACT_1024",
    column_name: "CERTIFICATION_SCORE",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 24,
    business_name: "Certification Score",
    business_description: "Performance score achieved during certification process. Essential for CPG certification performance tracking, improvement measurement, benchmarking against standards, and maintaining high certification standings.",
    business_synonyms: ["Certification Rating", "Cert Score", "Performance Score", "Assessment Score", "Evaluation Score", "Certification Grade"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

// ========================================
// TIMELINE AND RESOLUTION TRACKING
// ========================================

(days_to_resolution:Column {
    column_id: "COL_DAYS_TO_RESOLUTION_FACT_1025",
    column_name: "DAYS_TO_RESOLUTION",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 25,
    business_name: "Days to Resolution",
    business_description: "Number of days from event identification to complete resolution. Critical for CPG compliance efficiency measurement, resolution time tracking, process improvement, and understanding compliance response effectiveness.",
    business_synonyms: ["Resolution Time", "Time to Close", "Resolution Days", "Closure Time", "Response Time", "Resolution Period"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(overdue_days:Column {
    column_id: "COL_OVERDUE_DAYS_FACT_1026",
    column_name: "OVERDUE_DAYS",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 26,
    business_name: "Overdue Days",
    business_description: "Number of days past required resolution deadline. Important for CPG deadline management, escalation triggers, regulatory relationship impact, and identifying process bottlenecks in compliance resolution.",
    business_synonyms: ["Days Overdue", "Late Days", "Past Due Days", "Overdue Period", "Deadline Overrun", "Delay Days"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(corrective_action_count:Column {
    column_id: "COL_CORRECTIVE_ACTION_COUNT_FACT_1027",
    column_name: "CORRECTIVE_ACTION_COUNT",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 27,
    business_name: "Corrective Action Count",
    business_description: "Number of corrective actions required or implemented. Essential for CPG remediation tracking, action plan monitoring, compliance improvement measurement, and understanding corrective action complexity.",
    business_synonyms: ["Action Count", "Correction Count", "Remedial Actions", "Fix Count", "Improvement Actions", "CAPA Count"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "high",
    additive_type: "additive"
}),

(preventive_action_count:Column {
    column_id: "COL_PREVENTIVE_ACTION_COUNT_FACT_1028",
    column_name: "PREVENTIVE_ACTION_COUNT",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 28,
    business_name: "Preventive Action Count",
    business_description: "Number of preventive actions implemented to avoid future issues. Important for CPG proactive compliance management, prevention effectiveness, risk mitigation, and continuous improvement measurement.",
    business_synonyms: ["Prevention Count", "Preventative Actions", "Proactive Actions", "Prevention Measures", "Risk Mitigation Actions", "Prevention Steps"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "medium",
    additive_type: "additive"
}),

// ========================================
// DOMAIN-SPECIFIC ALCOHOLIC BEVERAGE COMPLIANCE
// ========================================

(alcohol_tax_amount:Column {
    column_id: "COL_ALCOHOL_TAX_AMOUNT_FACT_1029",
    column_name: "ALCOHOL_TAX_AMOUNT",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 29,
    business_name: "Alcohol Tax Amount",
    business_description: "Federal and state alcohol taxes assessed or under review. Critical for alcoholic beverage compliance, tax obligation tracking, TTB compliance, and understanding alcohol-specific tax implications and penalties.",
    business_synonyms: ["Alcohol Duty", "Excise Tax", "Federal Tax", "Alcohol Levy", "Liquor Tax", "Beverage Tax"],
    applicable_domains: ["alcoholic_beverages"],
    domain_specific: true,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 2,
    business_criticality: "critical",
    additive_type: "additive",
    regulatory_relevance: ["TTB", "IRS", "STATE_LIQUOR_BOARDS"]
}),

(license_violation_type:Column {
    column_id: "COL_LICENSE_VIOLATION_TYPE_FACT_1030",
    column_name: "LICENSE_VIOLATION_TYPE",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 30,
    business_name: "License Violation Type",
    business_description: "Type of alcoholic beverage license violation (Age Verification, Hours of Sale, Location, etc.). Essential for alcohol compliance categorization, violation pattern analysis, and license risk management.",
    business_synonyms: ["License Infraction", "Alcohol Violation", "License Issue", "Permit Violation", "License Non-Compliance", "Authorization Violation"],
    applicable_domains: ["alcoholic_beverages"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "critical",
    regulatory_relevance: ["TTB", "STATE_LIQUOR_BOARDS"]
}),

(responsible_service_training_flag:Column {
    column_id: "COL_RESPONSIBLE_SERVICE_TRAINING_FACT_1031",
    column_name: "RESPONSIBLE_SERVICE_TRAINING_FLAG",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 31,
    business_name: "Responsible Service Training Flag",
    business_description: "Indicates completion of responsible alcohol service training. Important for alcohol compliance verification, staff training tracking, and meeting responsible service requirements across retail locations.",
    business_synonyms: ["Training Compliance", "Service Training", "Alcohol Training", "Staff Training", "Responsible Service", "Training Certification"],
    applicable_domains: ["alcoholic_beverages"],
    domain_specific: true,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "high",
    additive_type: "non_additive"
}),

// ========================================
// DOMAIN-SPECIFIC PHARMACEUTICAL COMPLIANCE
// ========================================

(adverse_event_count:Column {
    column_id: "COL_ADVERSE_EVENT_COUNT_FACT_1032",
    column_name: "ADVERSE_EVENT_COUNT",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 32,
    business_name: "Adverse Event Count",
    business_description: "Number of adverse events reported for pharmaceutical products. Critical for pharmaceutical safety monitoring, FDA reporting requirements, post-market surveillance, and drug safety analysis.",
    business_synonyms: ["Safety Events", "Drug Reactions", "Side Effects", "Safety Reports", "ADR Count", "Safety Incidents"],
    applicable_domains: ["pharmaceuticals", "health_supplements"],
    domain_specific: true,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "critical",
    additive_type: "additive",
    regulatory_relevance: ["FDA", "EMA"]
}),

(dea_schedule_class:Column {
    column_id: "COL_DEA_SCHEDULE_CLASS_FACT_1033",
    column_name: "DEA_SCHEDULE_CLASS",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 33,
    business_name: "DEA Schedule Class",
    business_description: "DEA controlled substance schedule classification (I-V) for pharmaceutical compliance. Essential for controlled substance tracking, DEA compliance, inventory monitoring, and prescription drug security requirements.",
    business_synonyms: ["Drug Schedule", "Controlled Substance Class", "DEA Class", "Schedule Category", "Controlled Class", "Drug Classification"],
    applicable_domains: ["pharmaceuticals"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 10,
    business_criticality: "critical",
    regulatory_relevance: ["DEA", "FDA"]
}),

(gmp_compliance_score:Column {
    column_id: "COL_GMP_COMPLIANCE_SCORE_FACT_1034",
    column_name: "GMP_COMPLIANCE_SCORE",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 34,
    business_name: "GMP Compliance Score",
    business_description: "Good Manufacturing Practice compliance assessment score. Critical for pharmaceutical quality management, FDA compliance, manufacturing standards, and maintaining pharmaceutical production authorization.",
    business_synonyms: ["GMP Score", "Manufacturing Score", "Quality Score", "Production Compliance", "Manufacturing Rating", "GMP Rating"],
    applicable_domains: ["pharmaceuticals", "health_supplements"],
    domain_specific: true,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "critical",
    additive_type: "non_additive",
    regulatory_relevance: ["FDA"]
}),

// ========================================
// DOMAIN-SPECIFIC FOOD SAFETY COMPLIANCE
// ========================================

(haccp_compliance_flag:Column {
    column_id: "COL_HACCP_COMPLIANCE_FLAG_FACT_1035",
    column_name: "HACCP_COMPLIANCE_FLAG",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 35,
    business_name: "HACCP Compliance Flag",
    business_description: "Indicates compliance with Hazard Analysis Critical Control Points requirements. Essential for food safety management, FDA compliance, and ensuring food safety standards across all food and beverage categories.",
    business_synonyms: ["HACCP Flag", "Food Safety Flag", "Critical Control Points", "HACCP Status", "Food Safety Compliance", "Safety System Flag"],
    applicable_domains: ["food_beverage", "snacks", "dairy", "frozen_foods", "beverages", "pet_food", "baby_products"],
    domain_specific: true,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "critical",
    additive_type: "non_additive",
    regulatory_relevance: ["FDA", "USDA", "FSIS"]
}),

(pathogen_detection_count:Column {
    column_id: "COL_PATHOGEN_DETECTION_COUNT_FACT_1036",
    column_name: "PATHOGEN_DETECTION_COUNT",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 36,
    business_name: "Pathogen Detection Count",
    business_description: "Number of pathogenic organisms detected in food safety testing. Critical for food safety incident tracking, outbreak prevention, recall risk assessment, and maintaining food safety standards.",
    business_synonyms: ["Pathogen Count", "Contamination Count", "Microbial Detection", "Safety Violations", "Contamination Events", "Pathogen Incidents"],
    applicable_domains: ["food_beverage", "snacks", "dairy", "frozen_foods", "beverages", "pet_food", "baby_products"],
    domain_specific: true,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "critical",
    additive_type: "additive",
    regulatory_relevance: ["FDA", "USDA", "CDC"]
}),

(recall_units_affected:Column {
    column_id: "COL_RECALL_UNITS_AFFECTED_FACT_1037",
    column_name: "RECALL_UNITS_AFFECTED",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 37,
    business_name: "Recall Units Affected",
    business_description: "Number of product units affected by safety recall. Essential for CPG recall management, consumer safety impact assessment, financial impact calculation, and supply chain disruption analysis.",
    business_synonyms: ["Recalled Units", "Affected Products", "Recall Quantity", "Withdrawn Units", "Recall Volume", "Product Recall Count"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "critical",
    additive_type: "additive"
}),

// ========================================
// DOMAIN-SPECIFIC TOY AND CHILD PRODUCT COMPLIANCE
// ========================================

(cpsia_test_results:Column {
    column_id: "COL_CPSIA_TEST_RESULTS_FACT_1038",
    column_name: "CPSIA_TEST_RESULTS",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 38,
    business_name: "CPSIA Test Results",
    business_description: "Consumer Product Safety Improvement Act test results and compliance status. Critical for toy and children's product safety, lead testing compliance, age-appropriate safety standards, and CPSC regulatory compliance.",
    business_synonyms: ["Safety Test Results", "CPSC Tests", "Child Safety Tests", "Lead Testing", "Safety Compliance", "Product Safety Results"],
    applicable_domains: ["toys", "baby_products"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 200,
    business_criticality: "critical",
    regulatory_relevance: ["CPSC", "ASTM"]
}),

(choking_hazard_flag:Column {
    column_id: "COL_CHOKING_HAZARD_FLAG_FACT_1039",
    column_name: "CHOKING_HAZARD_FLAG",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 39,
    business_name: "Choking Hazard Flag",
    business_description: "Indicates product identified as potential choking hazard for children. Essential for child product safety, age-appropriate warnings, recall risk assessment, and ensuring child safety compliance.",
    business_synonyms: ["Choking Risk", "Safety Hazard", "Child Safety Risk", "Small Parts Hazard", "Safety Warning", "Hazard Flag"],
    applicable_domains: ["toys", "baby_products"],
    domain_specific: true,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "critical",
    additive_type: "non_additive",
    regulatory_relevance: ["CPSC"]
}),

// ========================================
// ENVIRONMENTAL COMPLIANCE MEASURES
// ========================================

(environmental_impact_score:Column {
    column_id: "COL_ENVIRONMENTAL_IMPACT_SCORE_FACT_1040",
    column_name: "ENVIRONMENTAL_IMPACT_SCORE",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 40,
    business_name: "Environmental Impact Score",
    business_description: "Environmental compliance and impact assessment score. Important for CPG sustainability compliance, EPA regulations, environmental responsibility tracking, and green certification maintenance.",
    business_synonyms: ["Environmental Score", "Sustainability Score", "Green Score", "Eco Impact", "Environmental Rating", "Sustainability Rating"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(waste_disposal_violations:Column {
    column_id: "COL_WASTE_DISPOSAL_VIOLATIONS_FACT_1041",
    column_name: "WASTE_DISPOSAL_VIOLATIONS",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 41,
    business_name: "Waste Disposal Violations",
    business_description: "Number of waste disposal and environmental violations. Essential for CPG environmental compliance, EPA violation tracking, waste management improvements, and environmental responsibility measurement.",
    business_synonyms: ["Waste Violations", "Environmental Violations", "Disposal Issues", "Waste Management Violations", "Environmental Non-Compliance", "Disposal Infractions"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "medium",
    additive_type: "additive"
}),

// ========================================
// LABELING AND MARKETING COMPLIANCE
// ========================================

(labeling_violations_count:Column {
    column_id: "COL_LABELING_VIOLATIONS_COUNT_FACT_1042",
    column_name: "LABELING_VIOLATIONS_COUNT",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 42,
    business_name: "Labeling Violations Count",
    business_description: "Number of product labeling and marketing compliance violations. Critical for CPG labeling compliance, marketing claim validation, consumer protection, and regulatory messaging compliance across all categories.",
    business_synonyms: ["Label Violations", "Marketing Violations", "Claim Violations", "Labeling Issues", "Marketing Non-Compliance", "Label Infractions"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "high",
    additive_type: "additive"
}),

(false_claim_flag:Column {
    column_id: "COL_FALSE_CLAIM_FLAG_FACT_1043",
    column_name: "FALSE_CLAIM_FLAG",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 43,
    business_name: "False Claim Flag",
    business_description: "Indicates identification of false or misleading marketing claims. Essential for CPG marketing compliance, claim substantiation, consumer protection, and avoiding deceptive advertising violations.",
    business_synonyms: ["Misleading Claim", "Deceptive Marketing", "False Advertisement", "Claim Violation", "Marketing Misrepresentation", "Advertising Violation"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "high",
    additive_type: "non_additive"
}),

// ========================================
// TRAINING AND COMPETENCY MEASURES
// ========================================

(staff_training_hours:Column {
    column_id: "COL_STAFF_TRAINING_HOURS_FACT_1044",
    column_name: "STAFF_TRAINING_HOURS",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 44,
    business_name: "Staff Training Hours",
    business_description: "Total hours of compliance training completed by staff. Important for CPG compliance competency tracking, training effectiveness measurement, regulatory requirement fulfillment, and maintaining trained workforce standards.",
    business_synonyms: ["Training Hours", "Compliance Training", "Education Hours", "Training Time", "Staff Development", "Competency Training"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 8,
    scale: 2,
    business_criticality: "medium",
    additive_type: "additive"
}),

(certification_expiry_risk_flag:Column {
    column_id: "COL_CERT_EXPIRY_RISK_FLAG_FACT_1045",
    column_name: "CERTIFICATION_EXPIRY_RISK_FLAG",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 45,
    business_name: "Certification Expiry Risk Flag",
    business_description: "Indicates certifications at risk of expiring within critical timeframe. Essential for CPG proactive certification management, renewal planning, business continuity, and avoiding compliance gaps.",
    business_synonyms: ["Expiry Risk", "Renewal Risk", "Certification Risk", "Expiration Alert", "Renewal Alert", "Compliance Risk"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "high",
    additive_type: "non_additive"
}),

// ========================================
// INCIDENT RESPONSE AND COMMUNICATION
// ========================================

(incident_response_time_hours:Column {
    column_id: "COL_INCIDENT_RESPONSE_TIME_FACT_1046",
    column_name: "INCIDENT_RESPONSE_TIME_HOURS",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 46,
    business_name: "Incident Response Time Hours",
    business_description: "Time elapsed from incident detection to initial response. Critical for CPG incident management, emergency response effectiveness, regulatory reporting timeliness, and crisis management performance measurement.",
    business_synonyms: ["Response Time", "Reaction Time", "Initial Response", "Alert Response", "Emergency Response", "Incident Reaction"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 8,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(regulatory_notification_flag:Column {
    column_id: "COL_REGULATORY_NOTIFICATION_FLAG_FACT_1047",
    column_name: "REGULATORY_NOTIFICATION_FLAG",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 47,
    business_name: "Regulatory Notification Flag",
    business_description: "Indicates whether regulatory authorities were properly notified of compliance event. Essential for CPG regulatory relationship management, notification compliance, and maintaining transparent regulatory communication.",
    business_synonyms: ["Authority Notified", "Regulatory Alert", "Official Notification", "Agency Notification", "Compliance Notification", "Regulatory Communication"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "high",
    additive_type: "non_additive"
}),

// ========================================
// EXTERNAL STAKEHOLDER IMPACT
// ========================================

(media_exposure_flag:Column {
    column_id: "COL_MEDIA_EXPOSURE_FLAG_FACT_1048",
    column_name: "MEDIA_EXPOSURE_FLAG",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 48,
    business_name: "Media Exposure Flag",
    business_description: "Indicates compliance event received media attention or public coverage. Important for CPG reputation risk management, crisis communication, brand protection, and understanding public relations impact of compliance issues.",
    business_synonyms: ["Public Exposure", "Media Coverage", "Public Attention", "Press Coverage", "Media Risk", "Public Relations Impact"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(customer_complaints_count:Column {
    column_id: "COL_CUSTOMER_COMPLAINTS_COUNT_FACT_1049",
    column_name: "CUSTOMER_COMPLAINTS_COUNT",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 49,
    business_name: "Customer Complaints Count",
    business_description: "Number of customer complaints related to compliance event. Essential for CPG consumer impact assessment, quality issue tracking, customer satisfaction measurement, and understanding consumer-facing compliance issues.",
    business_synonyms: ["Consumer Complaints", "Customer Issues", "Consumer Reports", "Customer Concerns", "Consumer Feedback", "Quality Complaints"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "medium",
    additive_type: "additive"
}),

// ========================================
// CONTINUOUS IMPROVEMENT MEASURES
// ========================================

(repeat_violation_flag:Column {
    column_id: "COL_REPEAT_VIOLATION_FLAG_FACT_1050",
    column_name: "REPEAT_VIOLATION_FLAG",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 50,
    business_name: "Repeat Violation Flag",
    business_description: "Indicates violation is a repeat of previous compliance issue. Critical for CPG systematic issue identification, corrective action effectiveness, compliance pattern analysis, and preventing recurring violations.",
    business_synonyms: ["Recurring Violation", "Repeat Issue", "Systematic Problem", "Recurring Non-Compliance", "Pattern Violation", "Repeat Offense"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(improvement_opportunity_count:Column {
    column_id: "COL_IMPROVEMENT_OPPORTUNITY_COUNT_FACT_1051",
    column_name: "IMPROVEMENT_OPPORTUNITY_COUNT",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 51,
    business_name: "Improvement Opportunity Count",
    business_description: "Number of improvement opportunities identified during compliance review. Important for CPG continuous improvement, proactive compliance enhancement, best practice identification, and operational excellence.",
    business_synonyms: ["Opportunities", "Improvement Areas", "Enhancement Opportunities", "Best Practice Opportunities", "Optimization Areas", "Development Areas"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "medium",
    additive_type: "additive"
}),

// ========================================
// METADATA AND AUDIT INFORMATION
// ========================================

(inspector_name:Column {
    column_id: "COL_INSPECTOR_NAME_FACT_1052",
    column_name: "INSPECTOR_NAME",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 52,
    business_name: "Inspector Name",
    business_description: "Name of lead inspector or auditor conducting compliance review. Important for CPG inspector relationship management, inspection consistency tracking, and understanding inspector-specific patterns and approaches.",
    business_synonyms: ["Lead Inspector", "Auditor Name", "Compliance Officer", "Review Lead", "Inspection Lead", "Regulatory Officer"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "low",
    additive_type: "non_additive"
}),

(compliance_officer:Column {
    column_id: "COL_COMPLIANCE_OFFICER_FACT_1053",
    column_name: "COMPLIANCE_OFFICER",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 53,
    business_name: "Internal Compliance Officer",
    business_description: "Company compliance officer responsible for managing compliance event. Essential for CPG internal accountability, compliance ownership tracking, performance evaluation, and ensuring proper internal compliance management.",
    business_synonyms: ["Compliance Manager", "Regulatory Manager", "Compliance Lead", "Internal Officer", "Compliance Responsible", "Regulatory Lead"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(documentation_completeness_score:Column {
    column_id: "COL_DOCUMENTATION_COMPLETENESS_FACT_1054",
    column_name: "DOCUMENTATION_COMPLETENESS_SCORE",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 54,
    business_name: "Documentation Completeness Score",
    business_description: "Score measuring completeness and quality of compliance documentation (0-100). Critical for CPG audit readiness, documentation quality improvement, regulatory preparation, and compliance program effectiveness.",
    business_synonyms: ["Documentation Score", "Record Completeness", "Documentation Quality", "File Completeness", "Record Quality", "Documentation Rating"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(record_created_timestamp:Column {
    column_id: "COL_RECORD_CREATED_TIMESTAMP_FACT_1055",
    column_name: "RECORD_CREATED_TIMESTAMP",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 55,
    business_name: "Record Created Timestamp",
    business_description: "System timestamp when compliance record was created in data warehouse. Used for audit trails, data lineage tracking, and data governance. Immutable after creation. Part of comprehensive data governance framework.",
    business_synonyms: ["Creation Time", "Insert Timestamp", "Created Date", "Record Creation", "Entry Timestamp", "Load Timestamp"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    max_length: null,
    business_criticality: "low",
    additive_type: "non_additive"
}),

(record_modified_timestamp:Column {
    column_id: "COL_RECORD_MODIFIED_TIMESTAMP_FACT_1056",
    column_name: "RECORD_MODIFIED_TIMESTAMP",
    table_id: "FACT_REGULATORY_COMPLIANCE",
    ordinal_position: 56,
    business_name: "Record Last Modified Timestamp",
    business_description: "Most recent update timestamp for compliance record. Tracks data freshness, change frequency, and maintenance activities. Updated automatically with any field modification for data governance compliance.",
    business_synonyms: ["Update Time", "Last Modified", "Change Timestamp", "Modified Date", "Last Updated", "Revision Timestamp"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    max_length: null,
    business_criticality: "low",
    additive_type: "non_additive"
});

// ========================================
// CREATE TABLE-COLUMN RELATIONSHIPS
// ========================================

MATCH (t:Table {table_id: "FACT_REGULATORY_COMPLIANCE"})
MATCH (c:Column {table_id: "FACT_REGULATORY_COMPLIANCE"})
CREATE (t)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"]
}]->(c);

// ========================================
// VERIFICATION QUERIES
// ========================================

// Count total columns created
MATCH (c:Column {table_id: "FACT_REGULATORY_COMPLIANCE"})
RETURN count(c) AS total_columns;

// Verify columns by business criticality
MATCH (c:Column {table_id: "FACT_REGULATORY_COMPLIANCE"})
RETURN c.business_criticality AS criticality_level, 
       count(c) AS column_count
ORDER BY criticality_level;

// List critical columns
MATCH (c:Column {table_id: "FACT_REGULATORY_COMPLIANCE"})
WHERE c.business_criticality = "critical"
RETURN c.business_name AS critical_column, 
       c.business_description
ORDER BY c.ordinal_position;

// Check domain-specific columns
MATCH (c:Column {table_id: "FACT_REGULATORY_COMPLIANCE"})
WHERE c.domain_specific = true
RETURN c.business_name AS domain_specific_column, 
       c.applicable_domains AS specific_domains,
       c.regulatory_relevance AS regulations
ORDER BY c.ordinal_position;

// Check columns by category
MATCH (c:Column {table_id: "FACT_REGULATORY_COMPLIANCE"})
RETURN 
    CASE 
        WHEN c.ordinal_position <= 5 THEN "Core Compliance Event Identifiers & Foreign Keys"
        WHEN c.ordinal_position <= 9 THEN "Compliance Event Classification"
        WHEN c.ordinal_position <= 13 THEN "Compliance Status & Severity Measures"
        WHEN c.ordinal_position <= 18 THEN "Violation & Penalty Measures"
        WHEN c.ordinal_position <= 21 THEN "Inspection & Audit Measures"
        WHEN c.ordinal_position <= 24 THEN "Certification & Licensing Measures"
        WHEN c.ordinal_position <= 28 THEN "Timeline & Resolution Tracking"
        WHEN c.ordinal_position <= 31 THEN "Domain-Specific Alcoholic Beverage"
        WHEN c.ordinal_position <= 34 THEN "Domain-Specific Pharmaceutical"
        WHEN c.ordinal_position <= 37 THEN "Domain-Specific Food Safety"
        WHEN c.ordinal_position <= 39 THEN "Domain-Specific Toy & Child Product"
        WHEN c.ordinal_position <= 41 THEN "Environmental Compliance"
        WHEN c.ordinal_position <= 43 THEN "Labeling & Marketing Compliance"
        WHEN c.ordinal_position <= 45 THEN "Training & Competency"
        WHEN c.ordinal_position <= 47 THEN "Incident Response & Communication"
        WHEN c.ordinal_position <= 49 THEN "External Stakeholder Impact"
        WHEN c.ordinal_position <= 51 THEN "Continuous Improvement"
        ELSE "Metadata & Audit"
    END AS category,
    count(c) AS column_count
ORDER BY category;

// Verify additive types for measures
MATCH (c:Column {table_id: "FACT_REGULATORY_COMPLIANCE"})
WHERE c.additive_type IS NOT NULL
RETURN c.additive_type AS additive_type,
       count(c) AS column_count
ORDER BY column_count DESC;

// Check for primary key
MATCH (c:Column {table_id: "FACT_REGULATORY_COMPLIANCE"})
WHERE c.is_primary_key = true
RETURN c.business_name AS primary_key_column, 
       c.column_name AS column_name;

// Verify foreign key relationships
MATCH (c:Column {table_id: "FACT_REGULATORY_COMPLIANCE"})
WHERE c.is_foreign_key = true
RETURN c.business_name AS foreign_key_column,
       c.column_name AS column_name,
       c.business_description
ORDER BY c.ordinal_position;

// Check regulatory relevance columns
MATCH (c:Column {table_id: "FACT_REGULATORY_COMPLIANCE"})
WHERE c.regulatory_relevance IS NOT NULL
RETURN c.business_name AS regulatory_column, 
       c.regulatory_relevance AS regulations,
       c.applicable_domains AS domains
ORDER BY c.ordinal_position;

// Verify compliance measures by semantic type
MATCH (c:Column {table_id: "FACT_REGULATORY_COMPLIANCE"})
WHERE c.semantic_type = "measure"
RETURN c.business_name AS compliance_measure,
       c.data_type AS data_type,
       c.additive_type AS additive_type,
       c.business_criticality AS criticality
ORDER BY c.ordinal_position;

// Check flag/boolean columns
MATCH (c:Column {table_id: "FACT_REGULATORY_COMPLIANCE"})
WHERE c.data_type = "BOOLEAN"
RETURN c.business_name AS boolean_column,
       c.business_description
ORDER BY c.ordinal_position;

// Verify financial impact measures
MATCH (c:Column {table_id: "FACT_REGULATORY_COMPLIANCE"})
WHERE c.business_name CONTAINS "Cost" OR c.business_name CONTAINS "Penalty" OR c.business_name CONTAINS "Fee"
RETURN c.business_name AS financial_measure,
       c.data_type AS data_type,
       c.precision AS precision,
       c.scale AS scale
ORDER BY c.ordinal_position;

// Check compliance scoring measures
MATCH (c:Column {table_id: "FACT_REGULATORY_COMPLIANCE"})
WHERE c.business_name CONTAINS "Score" OR c.business_name CONTAINS "Rating"
RETURN c.business_name AS scoring_measure,
       c.additive_type AS additive_type,
       c.business_criticality AS criticality
ORDER BY c.ordinal_position;

// Verify domain-specific measures by regulatory body
MATCH (c:Column {table_id: "FACT_REGULATORY_COMPLIANCE"})
WHERE c.regulatory_relevance IS NOT NULL
RETURN c.regulatory_relevance[0] AS primary_regulator,
       collect(c.business_name) AS regulated_columns
ORDER BY primary_regulator;

// ========================================
// END OF FACT_REGULATORY_COMPLIANCE COLUMN CREATION
// ========================================