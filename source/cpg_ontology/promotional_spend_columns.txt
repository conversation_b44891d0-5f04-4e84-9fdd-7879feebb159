// ========================================
// FACT_PROMOTIONAL_SPEND - COMPREHENSIVE COLUMN CREATION
// Complete promotional spend fact table columns for all CPG domains
// ========================================

// Clear existing columns for FACT_PROMOTIONAL_SPEND (optional)
MATCH (c:Column {table_id: "FACT_PROMOTIONAL_SPEND"}) DETACH DELETE c;

// ========================================
// CORE PROMOTIONAL EVENT IDENTIFIERS AND FOREIGN KEYS
// ========================================

CREATE 
(promotional_event_id:Column {
    column_id: "COL_PROMOTIONAL_EVENT_ID_FACT_2001",
    column_name: "PROMOTIONAL_EVENT_ID",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 1,
    business_name: "Promotional Event ID",
    business_description: "Unique identifier for promotional campaigns, marketing events, trade promotions, and advertising activities across all CPG categories. Critical for campaign tracking, spend attribution, ROI analysis, and managing promotional activities across alcoholic beverages, pharmaceuticals, toys, cosmetics, and all consumer product categories.",
    business_synonyms: ["Promo ID", "Campaign ID", "Promotion Key", "Marketing Event ID", "Promotional Reference", "Campaign Key", "Promo Event Key"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_primary_key: true,
    business_criticality: "critical"
}),

(date_key:Column {
    column_id: "COL_DATE_KEY_FACT_2002",
    column_name: "DATE_KEY",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 2,
    business_name: "Date Key",
    business_description: "Foreign key to date dimension for temporal promotional analysis and campaign timeline tracking. Essential for CPG promotional trend analysis, seasonal campaign planning, promotional calendar management, and understanding promotional patterns over time across all product categories.",
    business_synonyms: ["Promo Date Key", "Campaign Date Key", "Date FK", "Date Reference", "Temporal Key", "Promotional Date Key"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "INTEGER",
    max_length: null,
    is_foreign_key: true,
    business_criticality: "critical"
}),

(store_key:Column {
    column_id: "COL_STORE_KEY_FACT_2003",
    column_name: "STORE_KEY",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 3,
    business_name: "Store Key",
    business_description: "Foreign key to store dimension for location-based promotional tracking and store-specific campaign management. Critical for CPG retail promotions, local marketing effectiveness, geographic promotional patterns, and store-level ROI analysis.",
    business_synonyms: ["Location Key", "Retail Key", "Store FK", "Site Key", "Location Reference", "Outlet Key"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_foreign_key: true,
    business_criticality: "critical"
}),

(product_hierarchy_key:Column {
    column_id: "COL_PRODUCT_HIERARCHY_KEY_FACT_2004",
    column_name: "PRODUCT_HIERARCHY_KEY",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 4,
    business_name: "Product Hierarchy Key",
    business_description: "Foreign key to product hierarchy dimension for product-specific promotional tracking and category-level campaign analysis. Essential for CPG product promotion effectiveness, SKU-level spend attribution, category promotional patterns, and brand campaign management.",
    business_synonyms: ["Product Key", "SKU Key", "Product FK", "Item Key", "Product Reference", "Hierarchy FK"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_foreign_key: true,
    business_criticality: "high"
}),

(customer_key:Column {
    column_id: "COL_CUSTOMER_KEY_FACT_2005",
    column_name: "CUSTOMER_KEY",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 5,
    business_name: "Customer Key",
    business_description: "Foreign key to customer dimension for customer-targeted promotional activities and personalized campaign tracking. Important for CPG customer-specific promotions, loyalty program campaigns, targeted marketing effectiveness, and customer segment promotional analysis.",
    business_synonyms: ["Customer FK", "Consumer Key", "Customer Reference", "Consumer FK", "Customer ID FK", "Target Customer Key"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_foreign_key: true,
    business_criticality: "medium"
}),

// ========================================
// PROMOTIONAL EVENT CLASSIFICATION
// ========================================

(promotion_type:Column {
    column_id: "COL_PROMOTION_TYPE_FACT_2006",
    column_name: "PROMOTION_TYPE",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 6,
    business_name: "Promotion Type",
    business_description: "Primary classification of promotional activity (Price Reduction, BOGO, Coupon, Display, Advertising, etc.). Critical for CPG promotional mix analysis, campaign type effectiveness, spend allocation optimization, and understanding promotional strategy patterns.",
    business_synonyms: ["Promo Type", "Campaign Type", "Marketing Type", "Promotional Category", "Activity Type", "Campaign Category"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "critical"
}),

(promotion_subtype:Column {
    column_id: "COL_PROMOTION_SUBTYPE_FACT_2007",
    column_name: "PROMOTION_SUBTYPE",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 7,
    business_name: "Promotion Subtype",
    business_description: "Detailed promotional classification (Temporary Price Reduction, Multi-Buy, Digital Coupon, End Cap Display, etc.). Essential for CPG granular promotional analysis, specific tactic effectiveness, and detailed campaign categorization.",
    business_synonyms: ["Promo Subtype", "Detailed Type", "Campaign Subcategory", "Tactic Type", "Specific Type", "Detailed Classification"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "high"
}),

(campaign_name:Column {
    column_id: "COL_CAMPAIGN_NAME_FACT_2008",
    column_name: "CAMPAIGN_NAME",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 8,
    business_name: "Campaign Name",
    business_description: "Marketing campaign name or promotional event title. Critical for CPG campaign identification, cross-channel coordination, marketing communication alignment, and campaign performance tracking.",
    business_synonyms: ["Promotion Name", "Marketing Campaign", "Event Name", "Campaign Title", "Promotional Event", "Marketing Initiative"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 200,
    business_criticality: "medium"
}),

(promotional_channel:Column {
    column_id: "COL_PROMOTIONAL_CHANNEL_FACT_2009",
    column_name: "PROMOTIONAL_CHANNEL",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 9,
    business_name: "Promotional Channel",
    business_description: "Channel through which promotion is delivered (In-Store, Online, TV, Radio, Social Media, Print, etc.). Essential for CPG omnichannel strategy, channel effectiveness comparison, media mix optimization, and channel ROI analysis.",
    business_synonyms: ["Marketing Channel", "Media Channel", "Promotion Medium", "Campaign Channel", "Distribution Channel", "Media Type"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high"
}),

// ========================================
// CORE PROMOTIONAL SPEND MEASURES
// ========================================

(gross_promotional_spend:Column {
    column_id: "COL_GROSS_PROMOTIONAL_SPEND_FACT_2010",
    column_name: "GROSS_PROMOTIONAL_SPEND",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 10,
    business_name: "Gross Promotional Spend",
    business_description: "Total gross amount spent on promotional activities before any rebates or reimbursements. Critical for CPG marketing budget tracking, total promotional investment analysis, spend trend monitoring, and understanding complete promotional costs.",
    business_synonyms: ["Total Spend", "Gross Spend", "Marketing Spend", "Promotional Investment", "Campaign Cost", "Total Investment"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 2,
    business_criticality: "critical",
    additive_type: "additive"
}),

(net_promotional_spend:Column {
    column_id: "COL_NET_PROMOTIONAL_SPEND_FACT_2011",
    column_name: "NET_PROMOTIONAL_SPEND",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 11,
    business_name: "Net Promotional Spend",
    business_description: "Net promotional spend after vendor funding, co-op reimbursements, and rebates. Essential for CPG true cost analysis, net investment tracking, actual spend monitoring, and understanding effective promotional costs.",
    business_synonyms: ["Net Spend", "Effective Spend", "Actual Spend", "Net Investment", "After Rebate Spend", "True Cost"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 2,
    business_criticality: "critical",
    additive_type: "additive"
}),

(discount_amount:Column {
    column_id: "COL_DISCOUNT_AMOUNT_FACT_2012",
    column_name: "DISCOUNT_AMOUNT",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 12,
    business_name: "Discount Amount",
    business_description: "Total discount value provided to customers through promotional activity. Important for CPG margin impact analysis, discount depth tracking, price investment monitoring, and understanding promotional price erosion.",
    business_synonyms: ["Price Discount", "Reduction Amount", "Price Cut", "Discount Value", "Price Reduction", "Savings Amount"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 2,
    business_criticality: "high",
    additive_type: "additive"
}),

(vendor_funding_amount:Column {
    column_id: "COL_VENDOR_FUNDING_AMOUNT_FACT_2013",
    column_name: "VENDOR_FUNDING_AMOUNT",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 13,
    business_name: "Vendor Funding Amount",
    business_description: "Amount of promotional funding received from vendors or manufacturers. Critical for CPG co-op management, vendor partnership tracking, funding optimization, and understanding promotional cost sharing.",
    business_synonyms: ["Co-op Funding", "Vendor Support", "Manufacturer Funding", "Partner Funding", "Vendor Contribution", "Co-op Amount"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 2,
    business_criticality: "high",
    additive_type: "additive"
}),

// ========================================
// PROMOTIONAL PERFORMANCE MEASURES
// ========================================

(incremental_revenue:Column {
    column_id: "COL_INCREMENTAL_REVENUE_FACT_2014",
    column_name: "INCREMENTAL_REVENUE",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 14,
    business_name: "Incremental Revenue",
    business_description: "Additional revenue generated directly attributable to promotional activity. Fundamental measure for CPG promotional effectiveness, lift analysis, incremental impact assessment, and ROI calculation.",
    business_synonyms: ["Revenue Lift", "Additional Revenue", "Promotional Lift", "Incremental Sales", "Revenue Impact", "Sales Lift"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 2,
    business_criticality: "critical",
    additive_type: "additive"
}),

(incremental_units:Column {
    column_id: "COL_INCREMENTAL_UNITS_FACT_2015",
    column_name: "INCREMENTAL_UNITS",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 15,
    business_name: "Incremental Units",
    business_description: "Additional units sold directly attributable to promotional activity. Essential for CPG volume lift analysis, unit effectiveness measurement, inventory impact assessment, and promotional volume planning.",
    business_synonyms: ["Unit Lift", "Additional Units", "Volume Lift", "Incremental Volume", "Unit Impact", "Extra Units"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "critical",
    additive_type: "additive"
}),

(promotional_roi:Column {
    column_id: "COL_PROMOTIONAL_ROI_FACT_2016",
    column_name: "PROMOTIONAL_ROI",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 16,
    business_name: "Promotional ROI",
    business_description: "Return on investment for promotional spend (revenue return per dollar spent). Critical for CPG investment effectiveness, promotional efficiency measurement, spend optimization, and campaign performance comparison.",
    business_synonyms: ["ROI", "Return on Investment", "Promotional Return", "Investment Return", "Spend Efficiency", "Campaign ROI"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 8,
    scale: 2,
    business_criticality: "critical",
    additive_type: "non_additive"
}),

(conversion_rate:Column {
    column_id: "COL_CONVERSION_RATE_FACT_2017",
    column_name: "CONVERSION_RATE",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 17,
    business_name: "Conversion Rate",
    business_description: "Percentage of targeted customers who made purchase during promotion. Important for CPG promotional effectiveness, customer response measurement, targeting accuracy, and campaign optimization.",
    business_synonyms: ["Response Rate", "Purchase Rate", "Take Rate", "Redemption Rate", "Success Rate", "Effectiveness Rate"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

// ========================================
// REACH AND EXPOSURE MEASURES
// ========================================

(customer_reach_count:Column {
    column_id: "COL_CUSTOMER_REACH_COUNT_FACT_2018",
    column_name: "CUSTOMER_REACH_COUNT",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 18,
    business_name: "Customer Reach Count",
    business_description: "Number of unique customers exposed to promotional campaign. Essential for CPG reach analysis, audience sizing, cost per reach calculations, and understanding promotional coverage.",
    business_synonyms: ["Reach", "Audience Size", "Customer Exposure", "Unique Reach", "Campaign Reach", "Total Reach"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(impression_count:Column {
    column_id: "COL_IMPRESSION_COUNT_FACT_2019",
    column_name: "IMPRESSION_COUNT",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 19,
    business_name: "Impression Count",
    business_description: "Total number of promotional impressions or views. Important for CPG advertising effectiveness, frequency analysis, exposure measurement, and digital marketing performance tracking.",
    business_synonyms: ["Impressions", "Views", "Ad Views", "Exposures", "Display Count", "View Count"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "BIGINT",
    max_length: null,
    business_criticality: "medium",
    additive_type: "additive"
}),

(store_participation_count:Column {
    column_id: "COL_STORE_PARTICIPATION_COUNT_FACT_2020",
    column_name: "STORE_PARTICIPATION_COUNT",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 20,
    business_name: "Store Participation Count",
    business_description: "Number of stores participating in promotional campaign. Critical for CPG retail execution tracking, promotional coverage analysis, distribution effectiveness, and store compliance measurement.",
    business_synonyms: ["Participating Stores", "Store Count", "Location Count", "Retail Participation", "Store Coverage", "Outlet Count"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "high",
    additive_type: "additive"
}),

// ========================================
// TIMING AND DURATION MEASURES
// ========================================

(promotion_duration_days:Column {
    column_id: "COL_PROMOTION_DURATION_DAYS_FACT_2021",
    column_name: "PROMOTION_DURATION_DAYS",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 21,
    business_name: "Promotion Duration Days",
    business_description: "Total number of days promotional campaign is active. Important for CPG campaign planning, duration optimization, seasonal alignment, and understanding optimal promotional lengths.",
    business_synonyms: ["Campaign Duration", "Promo Length", "Active Days", "Campaign Period", "Promotion Period", "Duration"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(days_until_start:Column {
    column_id: "COL_DAYS_UNTIL_START_FACT_2022",
    column_name: "DAYS_UNTIL_START",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 22,
    business_name: "Days Until Start",
    business_description: "Number of days until promotion begins (negative if already started). Essential for CPG promotional planning, preparation tracking, execution readiness, and launch countdown management.",
    business_synonyms: ["Countdown Days", "Days to Launch", "Pre-Launch Days", "Planning Days", "Lead Time", "Preparation Days"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(promotional_frequency:Column {
    column_id: "COL_PROMOTIONAL_FREQUENCY_FACT_2023",
    column_name: "PROMOTIONAL_FREQUENCY",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 23,
    business_name: "Promotional Frequency",
    business_description: "Average frequency of promotional exposure per customer. Important for CPG frequency capping, optimal exposure planning, wear-out prevention, and marketing effectiveness optimization.",
    business_synonyms: ["Exposure Frequency", "Average Frequency", "Contact Frequency", "Impression Frequency", "Touch Frequency", "Exposure Rate"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 8,
    scale: 2,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

// ========================================
// COST EFFICIENCY MEASURES
// ========================================

(cost_per_impression:Column {
    column_id: "COL_COST_PER_IMPRESSION_FACT_2024",
    column_name: "COST_PER_IMPRESSION",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 24,
    business_name: "Cost Per Impression",
    business_description: "Average cost per promotional impression (CPM/1000). Critical for CPG media efficiency, cost benchmarking, channel comparison, and advertising spend optimization.",
    business_synonyms: ["CPM", "Impression Cost", "Cost Per View", "Media Cost", "Ad Cost", "View Cost"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 4,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(cost_per_conversion:Column {
    column_id: "COL_COST_PER_CONVERSION_FACT_2025",
    column_name: "COST_PER_CONVERSION",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 25,
    business_name: "Cost Per Conversion",
    business_description: "Average promotional cost per customer conversion. Essential for CPG acquisition cost analysis, conversion efficiency, campaign optimization, and customer acquisition benchmarking.",
    business_synonyms: ["Conversion Cost", "Acquisition Cost", "CPA", "Cost Per Sale", "Customer Cost", "Sale Cost"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(spend_per_store:Column {
    column_id: "COL_SPEND_PER_STORE_FACT_2026",
    column_name: "SPEND_PER_STORE",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 26,
    business_name: "Spend Per Store",
    business_description: "Average promotional spend per participating store. Important for CPG store-level investment analysis, retail partner negotiations, store tier optimization, and location-based spend efficiency.",
    business_synonyms: ["Store Spend", "Location Spend", "Average Store Spend", "Per Store Investment", "Store Investment", "Location Investment"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 2,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

// ========================================
// COMPETITIVE AND MARKET MEASURES
// ========================================

(competitive_spend_index:Column {
    column_id: "COL_COMPETITIVE_SPEND_INDEX_FACT_2027",
    column_name: "COMPETITIVE_SPEND_INDEX",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 27,
    business_name: "Competitive Spend Index",
    business_description: "Index comparing promotional spend to competitive average (100 = average). Critical for CPG competitive positioning, share of voice analysis, promotional intensity benchmarking, and market presence assessment.",
    business_synonyms: ["Competitive Index", "Spend Index", "Market Index", "SOV Index", "Competitive Position", "Market Position"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(market_share_impact:Column {
    column_id: "COL_MARKET_SHARE_IMPACT_FACT_2028",
    column_name: "MARKET_SHARE_IMPACT",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 28,
    business_name: "Market Share Impact",
    business_description: "Change in market share points attributable to promotional activity. Essential for CPG competitive impact analysis, share gain measurement, promotional effectiveness on market position.",
    business_synonyms: ["Share Impact", "Market Share Change", "Share Point Impact", "Competitive Impact", "Share Movement", "Position Change"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

// ========================================
// DOMAIN-SPECIFIC ALCOHOLIC BEVERAGE PROMOTIONS
// ========================================

(on_premise_spend:Column {
    column_id: "COL_ON_PREMISE_SPEND_FACT_2029",
    column_name: "ON_PREMISE_SPEND",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 29,
    business_name: "On-Premise Promotional Spend",
    business_description: "Promotional spend in on-premise channels (bars, restaurants, clubs). Critical for alcoholic beverage on-premise strategy, venue marketing effectiveness, and hospitality channel ROI analysis.",
    business_synonyms: ["Bar Spend", "Restaurant Spend", "Venue Spend", "On-Trade Spend", "Hospitality Spend", "On-Premise Investment"],
    applicable_domains: ["alcoholic_beverages"],
    domain_specific: true,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 2,
    business_criticality: "high",
    additive_type: "additive"
}),

(tasting_event_count:Column {
    column_id: "COL_TASTING_EVENT_COUNT_FACT_2030",
    column_name: "TASTING_EVENT_COUNT",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 30,
    business_name: "Tasting Event Count",
    business_description: "Number of product tasting or sampling events conducted. Essential for alcoholic beverage experiential marketing, sampling effectiveness, consumer trial generation, and event ROI tracking.",
    business_synonyms: ["Sampling Events", "Tasting Sessions", "Demo Events", "Trial Events", "Sampling Count", "Experience Events"],
    applicable_domains: ["alcoholic_beverages", "food_beverage", "beverages"],
    domain_specific: true,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "medium",
    additive_type: "additive"
}),

(age_gate_compliance_flag:Column {
    column_id: "COL_AGE_GATE_COMPLIANCE_FLAG_FACT_2031",
    column_name: "AGE_GATE_COMPLIANCE_FLAG",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 31,
    business_name: "Age Gate Compliance Flag",
    business_description: "Indicates promotional campaign includes required age verification mechanisms. Important for alcoholic beverage regulatory compliance, responsible marketing practices, and legal requirement adherence.",
    business_synonyms: ["Age Verification", "Age Compliance", "Legal Compliance", "Age Check", "Regulatory Compliance", "Age Restriction"],
    applicable_domains: ["alcoholic_beverages"],
    domain_specific: true,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "critical",
    additive_type: "non_additive"
}),

// ========================================
// DOMAIN-SPECIFIC PHARMACEUTICAL PROMOTIONS
// ========================================

(hcp_reach_count:Column {
    column_id: "COL_HCP_REACH_COUNT_FACT_2032",
    column_name: "HCP_REACH_COUNT",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 32,
    business_name: "Healthcare Professional Reach Count",
    business_description: "Number of healthcare professionals reached by pharmaceutical promotional activities. Critical for pharmaceutical marketing effectiveness, HCP engagement tracking, and professional education ROI.",
    business_synonyms: ["Doctor Reach", "Physician Reach", "Medical Professional Reach", "HCP Count", "Provider Reach", "Prescriber Reach"],
    applicable_domains: ["pharmaceuticals"],
    domain_specific: true,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "critical",
    additive_type: "non_additive"
}),

(medical_education_spend:Column {
    column_id: "COL_MEDICAL_EDUCATION_SPEND_FACT_2033",
    column_name: "MEDICAL_EDUCATION_SPEND",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 33,
    business_name: "Medical Education Spend",
    business_description: "Spend on medical education and continuing education programs. Essential for pharmaceutical knowledge transfer, HCP education effectiveness, and compliance with medical promotion guidelines.",
    business_synonyms: ["Education Spend", "CME Spend", "Training Spend", "Professional Education", "Medical Training", "Educational Investment"],
    applicable_domains: ["pharmaceuticals", "health_supplements"],
    domain_specific: true,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 2,
    business_criticality: "high",
    additive_type: "additive"
}),

(sample_distribution_count:Column {
    column_id: "COL_SAMPLE_DISTRIBUTION_COUNT_FACT_2034",
    column_name: "SAMPLE_DISTRIBUTION_COUNT",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 34,
    business_name: "Sample Distribution Count",
    business_description: "Number of product samples distributed to healthcare professionals or patients. Important for pharmaceutical trial generation, sample ROI tracking, and prescription conversion analysis.",
    business_synonyms: ["Sample Count", "Free Samples", "Trial Samples", "Product Samples", "Sample Units", "Distribution Count"],
    applicable_domains: ["pharmaceuticals", "cosmetics", "personal_care"],
    domain_specific: true,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "high",
    additive_type: "additive"
}),

// ========================================
// DOMAIN-SPECIFIC TOY AND CHILDREN'S PRODUCT PROMOTIONS
// ========================================

(kid_focused_media_spend:Column {
    column_id: "COL_KID_FOCUSED_MEDIA_SPEND_FACT_2035",
    column_name: "KID_FOCUSED_MEDIA_SPEND",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 35,
    business_name: "Kid-Focused Media Spend",
    business_description: "Promotional spend on child-targeted media channels and platforms. Critical for toy marketing effectiveness, age-appropriate advertising, COPPA compliance, and responsible youth marketing.",
    business_synonyms: ["Children's Media", "Youth Marketing", "Kid Advertising", "Child Media", "Youth Spend", "Children's Advertising"],
    applicable_domains: ["toys", "baby_products"],
    domain_specific: true,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 2,
    business_criticality: "high",
    additive_type: "additive"
}),

(parent_targeted_spend:Column {
    column_id: "COL_PARENT_TARGETED_SPEND_FACT_2036",
    column_name: "PARENT_TARGETED_SPEND",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 36,
    business_name: "Parent-Targeted Spend",
    business_description: "Promotional spend targeting parents and caregivers for children's products. Essential for toy purchase decision influencer targeting, parent engagement effectiveness, and family marketing ROI.",
    business_synonyms: ["Parent Marketing", "Caregiver Spend", "Adult Targeted", "Parent Advertising", "Guardian Marketing", "Family Spend"],
    applicable_domains: ["toys", "baby_products"],
    domain_specific: true,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 2,
    business_criticality: "high",
    additive_type: "additive"
}),

(seasonal_promotion_flag:Column {
    column_id: "COL_SEASONAL_PROMOTION_FLAG_FACT_2037",
    column_name: "SEASONAL_PROMOTION_FLAG",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 37,
    business_name: "Seasonal Promotion Flag",
    business_description: "Indicates promotion tied to seasonal events (Holiday, Back-to-School, Summer). Important for CPG seasonal planning, holiday marketing effectiveness, and seasonal inventory management.",
    business_synonyms: ["Holiday Promotion", "Seasonal Campaign", "Event Promotion", "Seasonal Flag", "Holiday Flag", "Event Based"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

// ========================================
// DIGITAL AND SOCIAL MEDIA MEASURES
// ========================================

(digital_spend_amount:Column {
    column_id: "COL_DIGITAL_SPEND_AMOUNT_FACT_2038",
    column_name: "DIGITAL_SPEND_AMOUNT",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 38,
    business_name: "Digital Spend Amount",
    business_description: "Total spend on digital marketing channels (online, mobile, social). Critical for CPG digital transformation, online marketing effectiveness, digital ROI analysis, and channel shift tracking.",
    business_synonyms: ["Online Spend", "Digital Marketing", "Internet Spend", "Digital Investment", "Online Marketing", "Digital Advertising"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 2,
    business_criticality: "high",
    additive_type: "additive"
}),

(social_engagement_count:Column {
    column_id: "COL_SOCIAL_ENGAGEMENT_COUNT_FACT_2039",
    column_name: "SOCIAL_ENGAGEMENT_COUNT",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 39,
    business_name: "Social Media Engagement Count",
    business_description: "Total social media engagements (likes, shares, comments) generated by promotion. Essential for CPG social media effectiveness, viral reach measurement, engagement ROI, and social commerce tracking.",
    business_synonyms: ["Social Interactions", "Engagement Count", "Social Actions", "User Engagement", "Social Response", "Interaction Count"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "BIGINT",
    max_length: null,
    business_criticality: "medium",
    additive_type: "additive"
}),

(influencer_spend:Column {
    column_id: "COL_INFLUENCER_SPEND_FACT_2040",
    column_name: "INFLUENCER_SPEND",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 40,
    business_name: "Influencer Marketing Spend",
    business_description: "Spend on influencer partnerships and creator collaborations. Important for CPG influencer ROI, authentic marketing effectiveness, creator economy engagement, and modern marketing mix optimization.",
    business_synonyms: ["Creator Spend", "Influencer Investment", "Partnership Spend", "Ambassador Spend", "Creator Marketing", "Influencer Fees"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 2,
    business_criticality: "medium",
    additive_type: "additive"
}),

// ========================================
// TRADE PROMOTION MEASURES
// ========================================

(slotting_fee_amount:Column {
    column_id: "COL_SLOTTING_FEE_AMOUNT_FACT_2041",
    column_name: "SLOTTING_FEE_AMOUNT",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 41,
    business_name: "Slotting Fee Amount",
    business_description: "Fees paid to retailers for product placement and shelf space. Critical for CPG retail partnership costs, new product launch expenses, distribution investment tracking, and total trade spend analysis.",
    business_synonyms: ["Placement Fees", "Shelf Fees", "Listing Fees", "Retail Fees", "Distribution Fees", "Space Fees"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 2,
    business_criticality: "high",
    additive_type: "additive"
}),

(display_fee_amount:Column {
    column_id: "COL_DISPLAY_FEE_AMOUNT_FACT_2042",
    column_name: "DISPLAY_FEE_AMOUNT",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 42,
    business_name: "Display Fee Amount",
    business_description: "Fees for special display locations (end caps, checkout, feature displays). Essential for CPG merchandising investment, display ROI analysis, premium placement effectiveness, and visibility cost tracking.",
    business_synonyms: ["Merchandising Fees", "Feature Fees", "End Cap Fees", "Display Cost", "Placement Cost", "Visibility Fees"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 2,
    business_criticality: "medium",
    additive_type: "additive"
}),

(rebate_amount:Column {
    column_id: "COL_REBATE_AMOUNT_FACT_2043",
    column_name: "REBATE_AMOUNT",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 43,
    business_name: "Rebate Amount",
    business_description: "Customer or retailer rebates provided as part of promotion. Important for CPG net pricing analysis, rebate program effectiveness, customer incentive tracking, and true cost determination.",
    business_synonyms: ["Customer Rebate", "Cash Back", "Rebate Value", "Refund Amount", "Return Amount", "Incentive Amount"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 2,
    business_criticality: "high",
    additive_type: "additive"
}),

// ========================================
// PROMOTIONAL EFFECTIVENESS SCORES
// ========================================

(promotional_effectiveness_score:Column {
    column_id: "COL_PROMO_EFFECTIVENESS_SCORE_FACT_2044",
    column_name: "PROMOTIONAL_EFFECTIVENESS_SCORE",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 44,
    business_name: "Promotional Effectiveness Score",
    business_description: "Composite score measuring overall promotional effectiveness (0-100). Critical for CPG campaign comparison, best practice identification, promotional optimization, and performance benchmarking.",
    business_synonyms: ["Effectiveness Score", "Performance Score", "Campaign Score", "Success Score", "Promo Score", "Overall Score"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(brand_lift_percentage:Column {
    column_id: "COL_BRAND_LIFT_PERCENTAGE_FACT_2045",
    column_name: "BRAND_LIFT_PERCENTAGE",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 45,
    business_name: "Brand Lift Percentage",
    business_description: "Percentage increase in brand metrics (awareness, consideration, preference) from promotion. Essential for CPG brand building effectiveness, long-term impact measurement, and brand investment ROI.",
    business_synonyms: ["Brand Impact", "Awareness Lift", "Brand Increase", "Perception Lift", "Brand Growth", "Brand Improvement"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

// ========================================
// BUDGET AND PLANNING MEASURES
// ========================================

(planned_spend_amount:Column {
    column_id: "COL_PLANNED_SPEND_AMOUNT_FACT_2046",
    column_name: "PLANNED_SPEND_AMOUNT",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 46,
    business_name: "Planned Spend Amount",
    business_description: "Originally planned or budgeted promotional spend amount. Important for CPG budget variance analysis, planning accuracy, spend control, and financial discipline measurement.",
    business_synonyms: ["Budgeted Spend", "Planned Budget", "Original Budget", "Forecast Spend", "Budget Amount", "Planned Investment"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 2,
    business_criticality: "high",
    additive_type: "additive"
}),

(spend_variance_amount:Column {
    column_id: "COL_SPEND_VARIANCE_AMOUNT_FACT_2047",
    column_name: "SPEND_VARIANCE_AMOUNT",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 47,
    business_name: "Spend Variance Amount",
    business_description: "Variance between actual and planned promotional spend. Critical for CPG budget management, variance analysis, spend control effectiveness, and financial planning improvement.",
    business_synonyms: ["Budget Variance", "Spend Difference", "Variance Amount", "Budget Deviation", "Spend Gap", "Planning Variance"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 2,
    business_criticality: "high",
    additive_type: "additive"
}),

(approval_lead_time_days:Column {
    column_id: "COL_APPROVAL_LEAD_TIME_DAYS_FACT_2048",
    column_name: "APPROVAL_LEAD_TIME_DAYS",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 48,
    business_name: "Approval Lead Time Days",
    business_description: "Number of days from promotion request to approval. Essential for CPG process efficiency, planning cycle optimization, agility measurement, and promotional responsiveness improvement.",
    business_synonyms: ["Approval Time", "Lead Time", "Processing Time", "Approval Days", "Review Time", "Approval Period"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

// ========================================
// CUSTOMER BEHAVIOR MEASURES
// ========================================

(new_customer_count:Column {
    column_id: "COL_NEW_CUSTOMER_COUNT_FACT_2049",
    column_name: "NEW_CUSTOMER_COUNT",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 49,
    business_name: "New Customer Count",
    business_description: "Number of new customers acquired through promotional activity. Critical for CPG customer acquisition effectiveness, trial generation, market expansion, and new buyer ROI analysis.",
    business_synonyms: ["New Buyers", "First Time Buyers", "New Acquisitions", "Trial Customers", "New Users", "First Purchase"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "high",
    additive_type: "additive"
}),

(repeat_purchase_rate:Column {
    column_id: "COL_REPEAT_PURCHASE_RATE_FACT_2050",
    column_name: "REPEAT_PURCHASE_RATE",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 50,
    business_name: "Repeat Purchase Rate",
    business_description: "Percentage of promotional buyers who make repeat purchases. Essential for CPG loyalty impact assessment, promotional quality measurement, long-term value creation, and sustainable growth tracking.",
    business_synonyms: ["Retention Rate", "Repurchase Rate", "Loyalty Rate", "Return Rate", "Repeat Rate", "Rebuy Rate"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(basket_size_lift:Column {
    column_id: "COL_BASKET_SIZE_LIFT_FACT_2051",
    column_name: "BASKET_SIZE_LIFT",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 51,
    business_name: "Basket Size Lift",
    business_description: "Percentage increase in average transaction size during promotion. Important for CPG cross-sell effectiveness, promotional basket building, total store impact, and incremental revenue optimization.",
    business_synonyms: ["Transaction Lift", "Basket Increase", "Purchase Size Lift", "Cart Size Lift", "Order Lift", "Transaction Growth"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

// ========================================
// METADATA AND AUDIT INFORMATION
// ========================================

(campaign_manager:Column {
    column_id: "COL_CAMPAIGN_MANAGER_FACT_2052",
    column_name: "CAMPAIGN_MANAGER",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 52,
    business_name: "Campaign Manager",
    business_description: "Marketing manager responsible for promotional campaign. Important for CPG accountability tracking, performance attribution, best practice identification, and team effectiveness measurement.",
    business_synonyms: ["Marketing Manager", "Promo Manager", "Campaign Owner", "Responsible Manager", "Campaign Lead", "Marketing Lead"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "low",
    additive_type: "non_additive"
}),

(promotion_status:Column {
    column_id: "COL_PROMOTION_STATUS_FACT_2053",
    column_name: "PROMOTION_STATUS",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 53,
    business_name: "Promotion Status",
    business_description: "Current status of promotional campaign (Planning, Active, Completed, Cancelled). Essential for CPG campaign lifecycle management, active promotion tracking, completion monitoring, and workflow management.",
    business_synonyms: ["Campaign Status", "Promo State", "Activity Status", "Campaign State", "Promotion State", "Current Status"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(data_quality_score:Column {
    column_id: "COL_DATA_QUALITY_SCORE_FACT_2054",
    column_name: "DATA_QUALITY_SCORE",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 54,
    business_name: "Data Quality Score",
    business_description: "Score indicating completeness and accuracy of promotional data (0-100). Critical for CPG analytics reliability, measurement confidence, data governance, and continuous data improvement.",
    business_synonyms: ["Quality Score", "Data Completeness", "Data Accuracy", "Quality Rating", "Data Confidence", "Completeness Score"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(record_created_timestamp:Column {
    column_id: "COL_RECORD_CREATED_TIMESTAMP_FACT_2055",
    column_name: "RECORD_CREATED_TIMESTAMP",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 55,
    business_name: "Record Created Timestamp",
    business_description: "System timestamp when promotional record was created in data warehouse. Used for audit trails, data lineage tracking, and data governance. Immutable after creation. Part of comprehensive data governance framework.",
    business_synonyms: ["Creation Time", "Insert Timestamp", "Created Date", "Record Creation", "Entry Timestamp", "Load Timestamp"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    max_length: null,
    business_criticality: "low",
    additive_type: "non_additive"
}),

(record_modified_timestamp:Column {
    column_id: "COL_RECORD_MODIFIED_TIMESTAMP_FACT_2056",
    column_name: "RECORD_MODIFIED_TIMESTAMP",
    table_id: "FACT_PROMOTIONAL_SPEND",
    ordinal_position: 56,
    business_name: "Record Last Modified Timestamp",
    business_description: "Most recent update timestamp for promotional record. Tracks data freshness, change frequency, and maintenance activities. Updated automatically with any field modification for data governance compliance.",
    business_synonyms: ["Update Time", "Last Modified", "Change Timestamp", "Modified Date", "Last Updated", "Revision Timestamp"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    max_length: null,
    business_criticality: "low",
    additive_type: "non_additive"
});

// ========================================
// CREATE TABLE-COLUMN RELATIONSHIPS
// ========================================

MATCH (t:Table {table_id: "FACT_PROMOTIONAL_SPEND"})
MATCH (c:Column {table_id: "FACT_PROMOTIONAL_SPEND"})
CREATE (t)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"]
}]->(c);