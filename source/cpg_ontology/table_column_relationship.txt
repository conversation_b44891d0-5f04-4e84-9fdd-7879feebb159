// ========================================
// CREATE TABLE-COLUMN RELATIONSHIPS
// Execute after all tables and columns are created
// Links all tables to their respective columns
// ========================================

// ========================================
// DIMENSION TABLE-COLUMN RELATIONSHIPS
// ========================================

// Product Dimension Relationships
MATCH (pt:Table {table_id: "DIM_PRODUCT_MASTER"})
MATCH (c:Column {table_id: "DIM_PRODUCT_MASTER"})
CREATE (pt)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(c);

// Brand Dimension Relationships
MATCH (bt:Table {table_id: "DIM_BRAND_MASTER"})
MATCH (c:Column {table_id: "DIM_BRAND_MASTER"})
CREATE (bt)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(c);

// Manufacturer Dimension Relationships
MATCH (mt:Table {table_id: "DIM_MANUFACTURER"})
MATCH (c:Column {table_id: "DIM_MANUFACTURER"})
CREATE (mt)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(c);

// Category Dimension Relationships
MATCH (ct:Table {table_id: "DIM_CATEGORY"})
MATCH (c:Column {table_id: "DIM_CATEGORY"})
CREATE (ct)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(c);

// Store Dimension Relationships
MATCH (st:Table {table_id: "DIM_STORE"})
MATCH (c:Column {table_id: "DIM_STORE"})
CREATE (st)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(c);

// Date Dimension Relationships
MATCH (dt:Table {table_id: "DIM_DATE"})
MATCH (c:Column {table_id: "DIM_DATE"})
CREATE (dt)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(c);

// Customer Dimension Relationships
MATCH (cut:Table {table_id: "DIM_CUSTOMER"})
MATCH (c:Column {table_id: "DIM_CUSTOMER"})
CREATE (cut)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(c);

// Promotion Dimension Relationships
MATCH (prt:Table {table_id: "DIM_PROMOTION"})
MATCH (c:Column {table_id: "DIM_PROMOTION"})
CREATE (prt)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(c);

// Product Hierarchy Dimension Relationships
MATCH (pht:Table {table_id: "DIM_PRODUCT_HIERARCHY"})
MATCH (c:Column {table_id: "DIM_PRODUCT_HIERARCHY"})
CREATE (pht)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(c);

// Media Dimension Relationships
MATCH (mdt:Table {table_id: "DIM_MEDIA"})
MATCH (c:Column {table_id: "DIM_MEDIA"})
CREATE (mdt)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(c);

// ========================================
// FACT TABLE-COLUMN RELATIONSHIPS
// ========================================

// Sales Fact Relationships
MATCH (sft:Table {table_id: "FACT_SALES"})
MATCH (c:Column {table_id: "FACT_SALES"})
CREATE (sft)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(c);

// Regulatory Compliance Fact Relationships
MATCH (rft:Table {table_id: "FACT_REGULATORY_COMPLIANCE"})
MATCH (c:Column {table_id: "FACT_REGULATORY_COMPLIANCE"})
CREATE (rft)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(c);

// Promotional Spend Fact Relationships
MATCH (psft:Table {table_id: "FACT_PROMOTIONAL_SPEND"})
MATCH (c:Column {table_id: "FACT_PROMOTIONAL_SPEND"})
CREATE (psft)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(c);

// Inventory Fact Relationships
MATCH (ift:Table {table_id: "FACT_INVENTORY"})
MATCH (c:Column {table_id: "FACT_INVENTORY"})
CREATE (ift)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(c);

// E-commerce Sales Fact Relationships
MATCH (eft:Table {table_id: "FACT_ECOMMERCE_SALES"})
MATCH (c:Column {table_id: "FACT_ECOMMERCE_SALES"})
CREATE (eft)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(c);

// Syndicated Panel Fact Relationships
MATCH (spft:Table {table_id: "FACT_SYNDICATED_PANEL"})
MATCH (c:Column {table_id: "FACT_SYNDICATED_PANEL"})
CREATE (spft)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(c);

// Pricing Fact Relationships
MATCH (pft:Table {table_id: "FACT_PRICING"})
MATCH (c:Column {table_id: "FACT_PRICING"})
CREATE (pft)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(c);

// ========================================
// EXTENDED DOMAIN-SPECIFIC RELATIONSHIPS
// For tables that have been enhanced with additional CPG domains
// ========================================

// Note: If columns have been added for additional domains beyond the initial six
// (e.g., personal_care, household_products, pet_food, snacks, dairy, frozen_foods, 
// beverages, health_supplements, baby_products), ensure the applicable_domains 
// array in the relationships above includes all relevant domains.

// For comprehensive domain coverage, you may want to update the relationships like this:
// applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", 
//                     "cosmetics", "food_beverage", "personal_care", "household_products", 
//                     "pet_food", "snacks", "dairy", "frozen_foods", "beverages", 
//                     "health_supplements", "baby_products"]

// ========================================
// VALIDATION QUERIES
// Run these after creating relationships to verify
// ========================================

// Count relationships created per table
// MATCH (t:Table)-[r:CONTAINS]->(c:Column)
// RETURN t.table_name, COUNT(c) as column_count
// ORDER BY t.table_name;

// Verify all tables have relationships
// MATCH (t:Table)
// WHERE NOT EXISTS((t)-[:CONTAINS]->(:Column))
// RETURN t.table_name as tables_without_columns;

// ========================================
// END OF TABLE-COLUMN RELATIONSHIPS
// ========================================