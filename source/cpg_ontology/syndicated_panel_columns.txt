// ========================================
// FACT_SYNDICATED_PANEL - COMPREHENSIVE COLUMN CREATION
// Complete syndicated panel fact table columns for all CPG domains
// ========================================

// Clear existing columns for FACT_SYNDICATED_PANEL (optional)
MATCH (c:Column {table_id: "FACT_SYNDICATED_PANEL"}) DETACH DELETE c;

// ========================================
// CORE PANEL DATA IDENTIFIERS AND FOREIGN KEYS
// ========================================

CREATE 
(panel_observation_id:Column {
    column_id: "COL_PANEL_OBSERVATION_ID_FACT_4001",
    column_name: "PANEL_OBSERVATION_ID",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 1,
    business_name: "Panel Observation ID",
    business_description: "Unique identifier for syndicated panel data observations from market research providers like Nielsen, IRI, and Kantar. Critical for tracking market share, competitive intelligence, consumer behavior insights across alcoholic beverages, pharmaceuticals, toys, cosmetics, and all consumer product categories.",
    business_synonyms: ["Panel ID", "Observation Key", "Syndicated Data ID", "Market Data Key", "Panel Record ID", "Research ID", "Panel Key"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_primary_key: true,
    business_criticality: "critical"
}),

(date_key:Column {
    column_id: "COL_DATE_KEY_FACT_4002",
    column_name: "DATE_KEY",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 2,
    business_name: "Date Key",
    business_description: "Foreign key to date dimension for temporal market analysis and trend tracking. Essential for CPG market share trends, seasonal patterns, competitive dynamics over time, and understanding market evolution across all product categories.",
    business_synonyms: ["Period Date Key", "Panel Date Key", "Date FK", "Date Reference", "Temporal Key", "Market Date Key"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "INTEGER",
    max_length: null,
    is_foreign_key: true,
    business_criticality: "critical"
}),

(market_key:Column {
    column_id: "COL_MARKET_KEY_FACT_4003",
    column_name: "MARKET_KEY",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 3,
    business_name: "Market Key",
    business_description: "Foreign key to market dimension for geographic and channel-based analysis. Critical for CPG regional performance, market-specific strategies, geographic expansion planning, and channel optimization.",
    business_synonyms: ["Geography Key", "Market FK", "Region Key", "Area Key", "Market Reference", "Geographic Key"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_foreign_key: true,
    business_criticality: "critical"
}),

(product_hierarchy_key:Column {
    column_id: "COL_PRODUCT_HIERARCHY_KEY_FACT_4004",
    column_name: "PRODUCT_HIERARCHY_KEY",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 4,
    business_name: "Product Hierarchy Key",
    business_description: "Foreign key to product hierarchy dimension for product-specific market analysis. Essential for CPG brand performance, SKU-level market share, category dynamics, and competitive product tracking.",
    business_synonyms: ["Product Key", "Brand Key", "Product FK", "Item Key", "Product Reference", "Hierarchy FK"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_foreign_key: true,
    business_criticality: "critical"
}),

(competitor_key:Column {
    column_id: "COL_COMPETITOR_KEY_FACT_4005",
    column_name: "COMPETITOR_KEY",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 5,
    business_name: "Competitor Key",
    business_description: "Foreign key to competitor dimension for competitive intelligence tracking. Important for CPG competitive benchmarking, market share battles, pricing strategies, and understanding competitive dynamics.",
    business_synonyms: ["Competitor FK", "Rival Key", "Competition Key", "Competitor Reference", "Competitive Brand Key", "Rival Brand FK"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_foreign_key: true,
    business_criticality: "high"
}),

// ========================================
// PANEL SOURCE AND METHODOLOGY
// ========================================

(panel_provider:Column {
    column_id: "COL_PANEL_PROVIDER_FACT_4006",
    column_name: "PANEL_PROVIDER",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 6,
    business_name: "Panel Provider",
    business_description: "Syndicated data provider name (Nielsen, IRI, Kantar, NPD, etc.). Critical for CPG data source management, methodology understanding, provider comparison, and data quality assessment.",
    business_synonyms: ["Data Provider", "Research Company", "Panel Source", "Syndicated Provider", "Market Research Firm", "Data Source"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high"
}),

(panel_type:Column {
    column_id: "COL_PANEL_TYPE_FACT_4007",
    column_name: "PANEL_TYPE",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 7,
    business_name: "Panel Type",
    business_description: "Type of syndicated panel (Retail Measurement, Consumer Panel, E-commerce, Convenience, etc.). Essential for CPG channel insights, data interpretation, coverage understanding, and appropriate analysis.",
    business_synonyms: ["Data Type", "Panel Category", "Measurement Type", "Research Type", "Panel Classification", "Data Category"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high"
}),

(measurement_period:Column {
    column_id: "COL_MEASUREMENT_PERIOD_FACT_4008",
    column_name: "MEASUREMENT_PERIOD",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 8,
    business_name: "Measurement Period",
    business_description: "Time period covered by panel observation (Weekly, 4-Week, Monthly, Quarterly). Important for CPG period comparisons, trend analysis, seasonal adjustments, and data aggregation.",
    business_synonyms: ["Period Type", "Time Period", "Reporting Period", "Data Period", "Measurement Interval", "Period Length"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "high"
}),

(panel_size:Column {
    column_id: "COL_PANEL_SIZE_FACT_4009",
    column_name: "PANEL_SIZE",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 9,
    business_name: "Panel Size",
    business_description: "Number of stores or households in the panel sample. Critical for CPG statistical reliability, projection accuracy, market representation, and confidence in insights.",
    business_synonyms: ["Sample Size", "Panel Count", "Store Count", "Household Count", "Sample Points", "Panel Coverage"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

// ========================================
// CORE MARKET SHARE MEASURES
// ========================================

(market_share_units:Column {
    column_id: "COL_MARKET_SHARE_UNITS_FACT_4010",
    column_name: "MARKET_SHARE_UNITS",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 10,
    business_name: "Market Share Units",
    business_description: "Unit-based market share percentage within defined market. Fundamental metric for CPG competitive position, share tracking, performance vs competition, and strategic planning across all categories.",
    business_synonyms: ["Unit Share", "Volume Share", "Unit Market Share", "Share of Units", "Volume Market Share", "Unit %"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 3,
    business_criticality: "critical",
    additive_type: "non_additive"
}),

(market_share_dollars:Column {
    column_id: "COL_MARKET_SHARE_DOLLARS_FACT_4011",
    column_name: "MARKET_SHARE_DOLLARS",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 11,
    business_name: "Market Share Dollars",
    business_description: "Dollar-based market share percentage within defined market. Essential for CPG value share tracking, premium positioning assessment, revenue market position, and pricing strategy effectiveness.",
    business_synonyms: ["Dollar Share", "Value Share", "Revenue Share", "Dollar Market Share", "Value Market Share", "Revenue %"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 3,
    business_criticality: "critical",
    additive_type: "non_additive"
}),

(share_change_units:Column {
    column_id: "COL_SHARE_CHANGE_UNITS_FACT_4012",
    column_name: "SHARE_CHANGE_UNITS",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 12,
    business_name: "Share Change Units",
    business_description: "Period-over-period change in unit market share points. Important for CPG momentum tracking, share gain/loss analysis, competitive dynamics, and strategy effectiveness measurement.",
    business_synonyms: ["Unit Share Change", "Share Point Change", "Volume Share Change", "Share Movement", "Share Delta", "Share Shift"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 3,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(share_change_dollars:Column {
    column_id: "COL_SHARE_CHANGE_DOLLARS_FACT_4013",
    column_name: "SHARE_CHANGE_DOLLARS",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 13,
    business_name: "Share Change Dollars",
    business_description: "Period-over-period change in dollar market share points. Critical for CPG value share momentum, pricing strategy impact, premium positioning changes, and revenue share dynamics.",
    business_synonyms: ["Dollar Share Change", "Value Share Change", "Revenue Share Change", "Dollar Share Movement", "Value Share Delta", "Revenue Share Shift"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 3,
    business_criticality: "high",
    additive_type: "non_additive"
}),

// ========================================
// SALES AND VOLUME MEASURES
// ========================================

(syndicated_units_sold:Column {
    column_id: "COL_SYNDICATED_UNITS_SOLD_FACT_4014",
    column_name: "SYNDICATED_UNITS_SOLD",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 14,
    business_name: "Syndicated Units Sold",
    business_description: "Total units sold as reported by syndicated panel. Fundamental for CPG volume tracking, market sizing, demand analysis, and understanding consumption patterns across categories.",
    business_synonyms: ["Panel Units", "Reported Units", "Market Units", "Volume Sold", "Unit Sales", "Panel Volume"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 3,
    business_criticality: "critical",
    additive_type: "additive"
}),

(syndicated_dollars_sold:Column {
    column_id: "COL_SYNDICATED_DOLLARS_SOLD_FACT_4015",
    column_name: "SYNDICATED_DOLLARS_SOLD",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 15,
    business_name: "Syndicated Dollars Sold",
    business_description: "Total dollar sales as reported by syndicated panel. Essential for CPG revenue analysis, market value sizing, price realization tracking, and category dollar trends.",
    business_synonyms: ["Panel Dollars", "Reported Revenue", "Market Dollars", "Dollar Sales", "Revenue Sold", "Panel Revenue"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 2,
    business_criticality: "critical",
    additive_type: "additive"
}),

(average_selling_price:Column {
    column_id: "COL_AVERAGE_SELLING_PRICE_FACT_4016",
    column_name: "AVERAGE_SELLING_PRICE",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 16,
    business_name: "Average Selling Price",
    business_description: "Average price per unit in market from syndicated data. Critical for CPG pricing analysis, competitive price tracking, price positioning, and elasticity insights.",
    business_synonyms: ["ASP", "Average Price", "Unit Price", "Market Price", "Mean Price", "Price Per Unit"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 4,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(price_index:Column {
    column_id: "COL_PRICE_INDEX_FACT_4017",
    column_name: "PRICE_INDEX",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 17,
    business_name: "Price Index",
    business_description: "Price index relative to market average (100 = market average). Important for CPG price positioning assessment, premium/value classification, competitive pricing strategy, and price gap analysis.",
    business_synonyms: ["Relative Price", "Price Position", "Price Ratio", "Competitive Price Index", "Price Level", "Price Comparison"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

// ========================================
// DISTRIBUTION AND AVAILABILITY MEASURES
// ========================================

(total_distribution_points:Column {
    column_id: "COL_TOTAL_DISTRIBUTION_POINTS_FACT_4018",
    column_name: "TOTAL_DISTRIBUTION_POINTS",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 18,
    business_name: "Total Distribution Points",
    business_description: "Percentage of stores carrying product weighted by store importance. Essential for CPG distribution quality, weighted availability, market coverage effectiveness, and distribution strategy.",
    business_synonyms: ["TDP", "Weighted Distribution", "Total Points Distribution", "Store Coverage", "Distribution Weight", "Weighted Availability"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "critical",
    additive_type: "non_additive"
}),

(all_commodity_volume:Column {
    column_id: "COL_ALL_COMMODITY_VOLUME_FACT_4019",
    column_name: "ALL_COMMODITY_VOLUME",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 19,
    business_name: "All Commodity Volume Distribution",
    business_description: "Distribution weighted by total store sales across all categories. Important for CPG premium outlet focus, high-value store penetration, quality distribution measurement, and strategic placement.",
    business_synonyms: ["ACV", "ACV Distribution", "Dollar Weighted Distribution", "Sales Weighted Distribution", "Premium Distribution", "Value Distribution"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "critical",
    additive_type: "non_additive"
}),

(numeric_distribution:Column {
    column_id: "COL_NUMERIC_DISTRIBUTION_FACT_4020",
    column_name: "NUMERIC_DISTRIBUTION",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 20,
    business_name: "Numeric Distribution",
    business_description: "Percentage of stores carrying product without weighting. Critical for CPG store count tracking, breadth of distribution, market penetration, and expansion opportunities.",
    business_synonyms: ["Store Count Distribution", "Unweighted Distribution", "Physical Distribution", "Store Penetration", "Numeric Reach", "Store Coverage %"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(out_of_stock_percentage:Column {
    column_id: "COL_OUT_OF_STOCK_PERCENTAGE_FACT_4021",
    column_name: "OUT_OF_STOCK_PERCENTAGE",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 21,
    business_name: "Out of Stock Percentage",
    business_description: "Percentage of stores with product out of stock. Essential for CPG availability issues, lost sales quantification, supply chain performance, and service level monitoring.",
    business_synonyms: ["OOS %", "Stockout Rate", "Unavailability Rate", "OOS Percentage", "Stock Shortage", "Availability Gap"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

// ========================================
// CONSUMER BEHAVIOR MEASURES
// ========================================

(household_penetration:Column {
    column_id: "COL_HOUSEHOLD_PENETRATION_FACT_4022",
    column_name: "HOUSEHOLD_PENETRATION",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 22,
    business_name: "Household Penetration",
    business_description: "Percentage of households purchasing product in period. Fundamental for CPG consumer reach, market development, category growth potential, and understanding buyer base size.",
    business_synonyms: ["HH Penetration", "Buyer Penetration", "Consumer Reach", "Household Reach", "Buyer Base", "Consumer Penetration"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "critical",
    additive_type: "non_additive"
}),

(purchase_frequency:Column {
    column_id: "COL_PURCHASE_FREQUENCY_FACT_4023",
    column_name: "PURCHASE_FREQUENCY",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 23,
    business_name: "Purchase Frequency",
    business_description: "Average number of purchase occasions per buying household. Important for CPG loyalty measurement, usage rate analysis, heavy buyer identification, and consumption patterns.",
    business_synonyms: ["Buy Rate", "Purchase Rate", "Buying Frequency", "Repeat Rate", "Purchase Occasions", "Frequency of Purchase"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(buy_rate:Column {
    column_id: "COL_BUY_RATE_FACT_4024",
    column_name: "BUY_RATE",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 24,
    business_name: "Buy Rate",
    business_description: "Average units purchased per buying household per period. Critical for CPG consumption intensity, household demand, volume per buyer analysis, and market development strategies.",
    business_synonyms: ["Volume per Buyer", "Units per HH", "Consumption Rate", "Purchase Intensity", "Buyer Volume", "HH Buy Rate"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 8,
    scale: 3,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(loyalty_index:Column {
    column_id: "COL_LOYALTY_INDEX_FACT_4025",
    column_name: "LOYALTY_INDEX",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 25,
    business_name: "Loyalty Index",
    business_description: "Index measuring brand loyalty vs category average (100 = average). Essential for CPG brand strength assessment, switching behavior, competitive resilience, and loyalty program effectiveness.",
    business_synonyms: ["Brand Loyalty", "Loyalty Score", "Retention Index", "Brand Strength", "Loyalty Measure", "Switching Index"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

// ========================================
// PROMOTIONAL EFFECTIVENESS MEASURES
// ========================================

(promo_units_percentage:Column {
    column_id: "COL_PROMO_UNITS_PERCENTAGE_FACT_4026",
    column_name: "PROMO_UNITS_PERCENTAGE",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 26,
    business_name: "Promotional Units Percentage",
    business_description: "Percentage of units sold on promotion. Important for CPG promotional dependency, baseline vs incremental sales, margin management, and promotional strategy optimization.",
    business_synonyms: ["% Sold on Promo", "Promo Volume %", "Promoted Units", "Deal Volume", "Promo Share", "Promotional %"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(promo_dollars_percentage:Column {
    column_id: "COL_PROMO_DOLLARS_PERCENTAGE_FACT_4027",
    column_name: "PROMO_DOLLARS_PERCENTAGE",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 27,
    business_name: "Promotional Dollars Percentage",
    business_description: "Percentage of dollar sales on promotion. Critical for CPG revenue quality, promotional profit impact, price realization, and understanding true pricing power.",
    business_synonyms: ["% Revenue on Promo", "Promo Dollar %", "Promoted Revenue", "Deal Dollars", "Promo Revenue Share", "Promotional $ %"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(average_promo_discount:Column {
    column_id: "COL_AVERAGE_PROMO_DISCOUNT_FACT_4028",
    column_name: "AVERAGE_PROMO_DISCOUNT",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 28,
    business_name: "Average Promotional Discount",
    business_description: "Average discount percentage when on promotion. Essential for CPG promotional depth analysis, margin erosion tracking, competitive promotional intensity, and price investment monitoring.",
    business_synonyms: ["Promo Depth", "Discount Depth", "Average Discount", "Promo Price Cut", "Deal Depth", "Promotional Discount %"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

// ========================================
// COMPETITIVE INTELLIGENCE MEASURES
// ========================================

(competitive_units_index:Column {
    column_id: "COL_COMPETITIVE_UNITS_INDEX_FACT_4029",
    column_name: "COMPETITIVE_UNITS_INDEX",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 29,
    business_name: "Competitive Units Index",
    business_description: "Unit sales index vs key competitor (100 = parity). Critical for CPG competitive benchmarking, relative performance tracking, market position assessment, and strategic gap analysis.",
    business_synonyms: ["Competitive Index", "vs Competition", "Relative Units", "Competitive Position", "Unit Index", "Competition Index"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(share_of_category_growth:Column {
    column_id: "COL_SHARE_OF_CATEGORY_GROWTH_FACT_4030",
    column_name: "SHARE_OF_CATEGORY_GROWTH",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 30,
    business_name: "Share of Category Growth",
    business_description: "Brand's contribution to total category growth. Essential for CPG growth leadership, category development contribution, innovation impact, and strategic importance to retailers.",
    business_synonyms: ["Growth Contribution", "Category Growth Share", "Growth Leadership", "Development Share", "Growth Capture", "Expansion Share"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(source_of_volume_percentage:Column {
    column_id: "COL_SOURCE_OF_VOLUME_PERCENTAGE_FACT_4031",
    column_name: "SOURCE_OF_VOLUME_PERCENTAGE",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 31,
    business_name: "Source of Volume Percentage",
    business_description: "Percentage of volume sourced from competitive brands. Important for CPG switching analysis, conquest effectiveness, brand vulnerability, and understanding volume dynamics.",
    business_synonyms: ["Volume Source", "Switching Source", "Conquest %", "Volume Origin", "Brand Switching", "Source Analysis"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

// ========================================
// DOMAIN-SPECIFIC MEASURES - ALCOHOLIC BEVERAGES
// ========================================

(on_premise_share:Column {
    column_id: "COL_ON_PREMISE_SHARE_FACT_4032",
    column_name: "ON_PREMISE_SHARE",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 32,
    business_name: "On-Premise Market Share",
    business_description: "Market share in on-premise channel (bars, restaurants). Critical for alcoholic beverage channel strategy, on-premise performance, hospitality sector penetration, and channel mix optimization.",
    business_synonyms: ["Bar Share", "Restaurant Share", "On-Trade Share", "Hospitality Share", "Venue Share", "On-Premise %"],
    applicable_domains: ["alcoholic_beverages"],
    domain_specific: true,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(craft_segment_share:Column {
    column_id: "COL_CRAFT_SEGMENT_SHARE_FACT_4033",
    column_name: "CRAFT_SEGMENT_SHARE",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 33,
    business_name: "Craft Segment Share",
    business_description: "Share within craft/premium segment of alcoholic beverages. Essential for premium positioning, craft competition tracking, segment strategy, and innovation targeting.",
    business_synonyms: ["Craft Share", "Premium Share", "Artisan Share", "Specialty Share", "Craft Market Share", "Premium Segment"],
    applicable_domains: ["alcoholic_beverages"],
    domain_specific: true,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(abv_tier:Column {
    column_id: "COL_ABV_TIER_FACT_4034",
    column_name: "ABV_TIER",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 34,
    business_name: "Alcohol by Volume Tier",
    business_description: "Alcohol content tier classification for market segmentation. Important for alcoholic beverage portfolio analysis, regulatory compliance, consumer preference tracking, and segment performance.",
    business_synonyms: ["Alcohol Tier", "ABV Category", "Strength Tier", "Alcohol Level", "ABV Segment", "Proof Category"],
    applicable_domains: ["alcoholic_beverages"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

// ========================================
// DOMAIN-SPECIFIC MEASURES - PHARMACEUTICALS
// ========================================

(prescription_share:Column {
    column_id: "COL_PRESCRIPTION_SHARE_FACT_4035",
    column_name: "PRESCRIPTION_SHARE",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 35,
    business_name: "Prescription Market Share",
    business_description: "Share of prescriptions written within therapeutic class. Critical for pharmaceutical market position, prescriber influence, formulary success, and competitive dynamics in healthcare.",
    business_synonyms: ["Rx Share", "Script Share", "Prescription %", "Rx Market Share", "Script Market Share", "Prescriber Share"],
    applicable_domains: ["pharmaceuticals"],
    domain_specific: true,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "critical",
    additive_type: "non_additive"
}),

(new_therapy_starts:Column {
    column_id: "COL_NEW_THERAPY_STARTS_FACT_4036",
    column_name: "NEW_THERAPY_STARTS",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 36,
    business_name: "New Therapy Starts",
    business_description: "Number of patients starting therapy with product. Essential for pharmaceutical growth tracking, market development, patient acquisition, and therapy adoption rates.",
    business_synonyms: ["New Patients", "Therapy Initiations", "New Starts", "Patient Starts", "New Prescriptions", "Treatment Starts"],
    applicable_domains: ["pharmaceuticals"],
    domain_specific: true,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "high",
    additive_type: "additive"
}),

(therapy_days_supply:Column {
    column_id: "COL_THERAPY_DAYS_SUPPLY_FACT_4037",
    column_name: "THERAPY_DAYS_SUPPLY",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 37,
    business_name: "Therapy Days Supply",
    business_description: "Total days of therapy dispensed in market. Important for pharmaceutical consumption tracking, therapy duration analysis, compliance indicators, and market volume assessment.",
    business_synonyms: ["Days Supply", "Therapy Days", "Treatment Days", "Supply Days", "Dispensed Days", "Therapy Duration"],
    applicable_domains: ["pharmaceuticals"],
    domain_specific: true,
    semantic_type: "measure",
    data_type: "BIGINT",
    max_length: null,
    business_criticality: "high",
    additive_type: "additive"
}),

// ========================================
// CHANNEL AND RETAILER MEASURES
// ========================================

(channel_share:Column {
    column_id: "COL_CHANNEL_SHARE_FACT_4038",
    column_name: "CHANNEL_SHARE",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 38,
    business_name: "Channel Share",
    business_description: "Market share within specific retail channel (Grocery, Mass, Club, etc.). Critical for CPG channel strategy, resource allocation, channel-specific programs, and go-to-market optimization.",
    business_synonyms: ["Retail Channel Share", "Channel Market Share", "Format Share", "Channel %", "Outlet Share", "Channel Position"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(retailer_share:Column {
    column_id: "COL_RETAILER_SHARE_FACT_4039",
    column_name: "RETAILER_SHARE",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 39,
    business_name: "Retailer Share",
    business_description: "Market share within specific retailer. Essential for CPG account management, retailer negotiations, customer-specific strategies, and understanding account importance.",
    business_synonyms: ["Account Share", "Customer Share", "Retailer Market Share", "Account %", "Store Share", "Retailer Position"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(ecommerce_share:Column {
    column_id: "COL_ECOMMERCE_SHARE_FACT_4040",
    column_name: "ECOMMERCE_SHARE",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 40,
    business_name: "E-commerce Share",
    business_description: "Market share in online/e-commerce channel. Important for CPG digital strategy, online performance, channel shift tracking, and digital transformation progress.",
    business_synonyms: ["Online Share", "Digital Share", "E-com Share", "Internet Share", "Online Market Share", "Digital Commerce Share"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

// ========================================
// INNOVATION AND NEW PRODUCT MEASURES
// ========================================

(new_item_contribution:Column {
    column_id: "COL_NEW_ITEM_CONTRIBUTION_FACT_4041",
    column_name: "NEW_ITEM_CONTRIBUTION",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 41,
    business_name: "New Item Contribution",
    business_description: "Percentage of sales from new products launched in last 52 weeks. Critical for CPG innovation effectiveness, portfolio freshness, new product success, and growth driver analysis.",
    business_synonyms: ["Innovation Contribution", "New Product %", "Launch Contribution", "New SKU %", "Innovation Share", "New Product Sales"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(innovation_index:Column {
    column_id: "COL_INNOVATION_INDEX_FACT_4042",
    column_name: "INNOVATION_INDEX",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 42,
    business_name: "Innovation Index",
    business_description: "Index measuring innovation performance vs market (100 = market rate). Essential for CPG innovation benchmarking, R&D effectiveness, competitive innovation tracking, and portfolio modernization.",
    business_synonyms: ["Innovation Score", "New Product Index", "Launch Index", "Innovation Performance", "NPD Index", "Innovation Rate"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 2,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(trial_rate:Column {
    column_id: "COL_TRIAL_RATE_FACT_4043",
    column_name: "TRIAL_RATE",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 43,
    business_name: "Trial Rate",
    business_description: "Percentage of category buyers trying product for first time. Important for CPG new buyer acquisition, market development, brand building, and growth potential assessment.",
    business_synonyms: ["First Purchase Rate", "New Buyer Rate", "Trial %", "First Time Buyers", "Trial Penetration", "New Trial Rate"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

// ========================================
// QUALITY AND CONFIDENCE MEASURES
// ========================================

(panel_coverage_percentage:Column {
    column_id: "COL_PANEL_COVERAGE_PERCENTAGE_FACT_4044",
    column_name: "PANEL_COVERAGE_PERCENTAGE",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 44,
    business_name: "Panel Coverage Percentage",
    business_description: "Percentage of market covered by panel sample. Critical for CPG data reliability, projection confidence, gap identification, and understanding data limitations.",
    business_synonyms: ["Coverage %", "Market Coverage", "Panel Representation", "Sample Coverage", "Data Coverage", "Market Representation"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(confidence_level:Column {
    column_id: "COL_CONFIDENCE_LEVEL_FACT_4045",
    column_name: "CONFIDENCE_LEVEL",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 45,
    business_name: "Statistical Confidence Level",
    business_description: "Statistical confidence level of panel estimates. Essential for CPG decision confidence, data quality assessment, risk management, and appropriate data interpretation.",
    business_synonyms: ["Confidence %", "Statistical Confidence", "Data Confidence", "Reliability Level", "Confidence Score", "Statistical Reliability"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(projection_factor:Column {
    column_id: "COL_PROJECTION_FACTOR_FACT_4046",
    column_name: "PROJECTION_FACTOR",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 46,
    business_name: "Projection Factor",
    business_description: "Factor used to project panel data to total market. Important for CPG market sizing, total market calculations, expansion factors, and understanding data scaling.",
    business_synonyms: ["Expansion Factor", "Projection Weight", "Market Factor", "Scaling Factor", "Extrapolation Factor", "Market Multiplier"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 4,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

// ========================================
// CATEGORY AND SEGMENT MEASURES
// ========================================

(category_growth_rate:Column {
    column_id: "COL_CATEGORY_GROWTH_RATE_FACT_4047",
    column_name: "CATEGORY_GROWTH_RATE",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 47,
    business_name: "Category Growth Rate",
    business_description: "Year-over-year growth rate of total category. Critical for CPG market dynamics understanding, category health assessment, growth context, and strategic planning baseline.",
    business_synonyms: ["Category Growth", "Market Growth", "Category Expansion", "Market Growth Rate", "Category Trend", "Market Expansion"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(segment_share:Column {
    column_id: "COL_SEGMENT_SHARE_FACT_4048",
    column_name: "SEGMENT_SHARE",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 48,
    business_name: "Segment Share",
    business_description: "Market share within product segment or sub-category. Essential for CPG segment strategy, niche dominance, segment leadership, and focused competition analysis.",
    business_synonyms: ["Sub-category Share", "Segment Market Share", "Niche Share", "Segment %", "Sub-segment Share", "Segment Position"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(premium_index:Column {
    column_id: "COL_PREMIUM_INDEX_FACT_4049",
    column_name: "PREMIUM_INDEX",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 49,
    business_name: "Premium Index",
    business_description: "Index indicating premium positioning vs market average price. Important for CPG portfolio strategy, price tier performance, premiumization tracking, and value positioning.",
    business_synonyms: ["Price Premium Index", "Premium Position", "Price Tier Index", "Premium Score", "Value Index", "Price Position Index"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 2,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

// ========================================
// GEOGRAPHIC AND DEMOGRAPHIC MEASURES
// ========================================

(regional_index:Column {
    column_id: "COL_REGIONAL_INDEX_FACT_4050",
    column_name: "REGIONAL_INDEX",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 50,
    business_name: "Regional Index",
    business_description: "Performance index vs national average (100 = national average). Critical for CPG regional strategies, geographic strengths/weaknesses, local market adaptation, and expansion priorities.",
    business_synonyms: ["Geographic Index", "Regional Performance", "Area Index", "Local Index", "Regional Score", "Geographic Performance"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 2,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(demographic_index:Column {
    column_id: "COL_DEMOGRAPHIC_INDEX_FACT_4051",
    column_name: "DEMOGRAPHIC_INDEX",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 51,
    business_name: "Demographic Index",
    business_description: "Performance index within key demographic segment. Essential for CPG target marketing, demographic strategies, consumer segmentation, and focused marketing effectiveness.",
    business_synonyms: ["Demo Index", "Consumer Segment Index", "Target Index", "Demographic Performance", "Segment Score", "Consumer Index"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 2,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

// ========================================
// METADATA AND AUDIT INFORMATION
// ========================================

(data_collection_method:Column {
    column_id: "COL_DATA_COLLECTION_METHOD_FACT_4052",
    column_name: "DATA_COLLECTION_METHOD",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 52,
    business_name: "Data Collection Method",
    business_description: "Method used to collect panel data (Scanner, Audit, Survey, etc.). Important for CPG data interpretation, methodology understanding, accuracy expectations, and appropriate usage.",
    business_synonyms: ["Collection Method", "Data Method", "Research Method", "Collection Type", "Methodology", "Data Source Method"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(panel_refresh_date:Column {
    column_id: "COL_PANEL_REFRESH_DATE_FACT_4053",
    column_name: "PANEL_REFRESH_DATE",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 53,
    business_name: "Panel Refresh Date",
    business_description: "Date when panel data was last refreshed or updated. Essential for CPG data currency, refresh timing, data availability, and ensuring decisions based on latest information.",
    business_synonyms: ["Data Refresh Date", "Update Date", "Last Refresh", "Data Update", "Refresh Time", "Last Updated"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    max_length: null,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(data_quality_flag:Column {
    column_id: "COL_DATA_QUALITY_FLAG_FACT_4054",
    column_name: "DATA_QUALITY_FLAG",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 54,
    business_name: "Data Quality Flag",
    business_description: "Indicates any data quality issues or limitations. Critical for CPG appropriate data usage, caveat awareness, decision risk management, and analytical confidence.",
    business_synonyms: ["Quality Flag", "Data Flag", "Quality Indicator", "Data Issue Flag", "Quality Alert", "Data Warning"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(record_created_timestamp:Column {
    column_id: "COL_RECORD_CREATED_TIMESTAMP_FACT_4055",
    column_name: "RECORD_CREATED_TIMESTAMP",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 55,
    business_name: "Record Created Timestamp",
    business_description: "System timestamp when panel record was created in data warehouse. Used for audit trails, data lineage tracking, and data governance. Immutable after creation. Part of comprehensive data governance framework.",
    business_synonyms: ["Creation Time", "Insert Timestamp", "Created Date", "Record Creation", "Entry Timestamp", "Load Timestamp"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    max_length: null,
    business_criticality: "low",
    additive_type: "non_additive"
}),

(record_modified_timestamp:Column {
    column_id: "COL_RECORD_MODIFIED_TIMESTAMP_FACT_4056",
    column_name: "RECORD_MODIFIED_TIMESTAMP",
    table_id: "FACT_SYNDICATED_PANEL",
    ordinal_position: 56,
    business_name: "Record Last Modified Timestamp",
    business_description: "Most recent update timestamp for panel record. Tracks data freshness, change frequency, and maintenance activities. Updated automatically with any field modification for data governance compliance.",
    business_synonyms: ["Update Time", "Last Modified", "Change Timestamp", "Modified Date", "Last Updated", "Revision Timestamp"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    max_length: null,
    business_criticality: "low",
    additive_type: "non_additive"
});

// ========================================
// CREATE TABLE-COLUMN RELATIONSHIPS
// ========================================

MATCH (t:Table {table_id: "FACT_SYNDICATED_PANEL"})
MATCH (c:Column {table_id: "FACT_SYNDICATED_PANEL"})
CREATE (t)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"]
}]->(c);