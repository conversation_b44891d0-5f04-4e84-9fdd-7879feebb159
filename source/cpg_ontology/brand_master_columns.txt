// ========================================
// DIM_BRAND_MASTER - COMPREHENSIVE COLUMN CREATION
// Complete brand dimension columns for all CPG domains
// ========================================

// Clear existing columns for DIM_BRAND_MASTER (optional)
MATCH (c:Column {table_id: "DIM_BRAND_MASTER"}) DETACH DELETE c;

// ========================================
// CORE BRAND IDENTIFIERS AND ATTRIBUTES
// ========================================

CREATE 
(brand_id:Column {
    column_id: "COL_BRAND_ID_DIM_050",
    column_name: "BRAND_ID",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 1,
    business_name: "Brand ID",
    business_description: "Unique identifier for each brand across all CPG portfolios. Survives mergers, acquisitions, and divestitures. Links to historical brand performance and enables brand family tree analysis. Critical for multi-brand portfolio management and brand equity tracking.",
    business_synonyms: ["Brand Key", "Brand Code", "Brand Identifier", "Master Brand ID", "Brand Number", "Brand Reference", "Portfolio Brand ID", "Brand UUID"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 20,
    is_primary_key: true,
    business_criticality: "critical"
}),
(brand_name:Column {
    column_id: "COL_BRAND_NAME_051",
    column_name: "BRAND_NAME",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 2,
    business_name: "Brand Name",
    business_description: "Official registered brand name as trademarked and used in consumer communications. Must match legal trademark registration exactly. Forms the foundation of brand identity and consumer recognition. Used across all marketing touchpoints.",
    business_synonyms: ["Brand Title", "Trademark Name", "Brand Label", "Master Brand Name", "Registered Brand", "Brand Marque", "Commercial Name"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 200,
    business_criticality: "critical"
}),
(parent_brand_id:Column {
    column_id: "COL_PARENT_BRAND_ID_052",
    column_name: "PARENT_BRAND_ID",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 3,
    business_name: "Parent Brand ID",
    business_description: "Reference to parent brand for sub-brands and brand extensions. Enables brand hierarchy navigation and portfolio rollup reporting. NULL for master brands. Critical for understanding brand architecture and cannibalization analysis.",
    business_synonyms: ["Master Brand ID", "Parent Brand Key", "Brand Parent", "Umbrella Brand ID", "Primary Brand ID", "Brand Hierarchy Parent"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 20,
    is_foreign_key: true,
    business_criticality: "high"
}),
(brand_level:Column {
    column_id: "COL_BRAND_LEVEL_053",
    column_name: "BRAND_LEVEL",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 4,
    business_name: "Brand Level",
    business_description: "Hierarchical level in brand architecture (Corporate, Master, Sub-brand, Variant). Determines marketing investment allocation and brand strategy. Critical for portfolio optimization and brand extension decisions.",
    business_synonyms: ["Brand Tier", "Hierarchy Level", "Brand Position", "Portfolio Level", "Brand Rank", "Architecture Level"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "high"
}),
(brand_family:Column {
    column_id: "COL_BRAND_FAMILY_054",
    column_name: "BRAND_FAMILY",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 5,
    business_name: "Brand Family",
    business_description: "Grouping of related brands sharing common equity, positioning, or target audience. Enables cross-brand promotion strategies and family-level performance analysis. Critical for brand portfolio rationalization.",
    business_synonyms: ["Brand Group", "Brand Portfolio", "Brand Collection", "Brand Cluster", "Brand Line", "Brand Series"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "medium"
}),
(global_brand_flag:Column {
    column_id: "COL_GLOBAL_BRAND_FLAG_055",
    column_name: "GLOBAL_BRAND_FLAG",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 6,
    business_name: "Global Brand Flag",
    business_description: "Indicates brand presence in multiple international markets. Drives standardized vs localized marketing strategies. Affects regulatory compliance complexity and supply chain design. Critical for global brand management.",
    business_synonyms: ["International Brand", "Multi-Market Brand", "Global Flag", "Worldwide Brand", "Cross-Border Brand", "International Presence"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "high"
}),

// ========================================
// BRAND POSITIONING AND STRATEGY
// ========================================

(brand_positioning:Column {
    column_id: "COL_BRAND_POSITIONING_056",
    column_name: "BRAND_POSITIONING",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 7,
    business_name: "Brand Positioning",
    business_description: "Strategic market position defining brand's unique value proposition and competitive differentiation. Guides product development, pricing strategy, and marketing communications. Validated through consumer research.",
    business_synonyms: ["Market Position", "Brand Strategy", "Positioning Statement", "Brand Promise", "Value Position", "Strategic Position"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "TEXT",
    business_criticality: "high"
}),
(target_consumer_segment:Column {
    column_id: "COL_TARGET_CONSUMER_057",
    column_name: "TARGET_CONSUMER_SEGMENT",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 8,
    business_name: "Target Consumer Segment",
    business_description: "Primary consumer demographic and psychographic profile. Drives media planning, product development, and distribution strategies. Based on consumer segmentation research and validated through purchase data.",
    business_synonyms: ["Target Audience", "Consumer Target", "Primary Segment", "Target Market", "Core Consumer", "Target Demographics"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 200,
    business_criticality: "high"
}),
(price_positioning:Column {
    column_id: "COL_PRICE_POSITIONING_058",
    column_name: "PRICE_POSITIONING",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 9,
    business_name: "Price Positioning",
    business_description: "Brand's price tier strategy (Premium, Mid-tier, Value, Ultra-value). Determines acceptable price ranges, promotional depths, and channel strategies. Critical for margin management and competitive response.",
    business_synonyms: ["Price Tier", "Price Strategy", "Value Position", "Price Point", "Price Segment", "Economic Position"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "high"
}),
(brand_archetype:Column {
    column_id: "COL_BRAND_ARCHETYPE_059",
    column_name: "BRAND_ARCHETYPE",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 10,
    business_name: "Brand Archetype",
    business_description: "Jungian archetype defining brand personality (Hero, Caregiver, Explorer, etc.). Guides creative development, tone of voice, and consumer engagement strategies. Ensures consistent brand expression across touchpoints.",
    business_synonyms: ["Brand Personality", "Archetype Type", "Brand Character", "Personality Type", "Brand Identity", "Brand Persona"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "medium"
}),
(brand_value_proposition:Column {
    column_id: "COL_BRAND_VALUE_PROP_060",
    column_name: "BRAND_VALUE_PROPOSITION",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 11,
    business_name: "Brand Value Proposition",
    business_description: "Core brand promise and key differentiators driving consumer preference. Guides product development, marketing communications, and innovation pipeline. Must be validated through consumer research and consistently activated across touchpoints.",
    business_synonyms: ["Value Prop", "Brand Promise", "Core Benefit", "Unique Value", "Brand Proposition", "Consumer Promise"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "TEXT",
    business_criticality: "high"
}),

// ========================================
// BRAND EQUITY AND PERFORMANCE METRICS
// ========================================

(brand_equity_score:Column {
    column_id: "COL_BRAND_EQUITY_SCORE_061",
    column_name: "BRAND_EQUITY_SCORE",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 12,
    business_name: "Brand Equity Score",
    business_description: "Composite metric measuring brand strength including awareness, consideration, preference, and loyalty. Updated quarterly through brand tracking studies. Critical for marketing ROI assessment and M&A valuations.",
    business_synonyms: ["Equity Index", "Brand Strength", "Brand Health Score", "Equity Metric", "Brand Power Index", "Brand Value Score"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high"
}),
(brand_awareness_percent:Column {
    column_id: "COL_BRAND_AWARENESS_062",
    column_name: "BRAND_AWARENESS_PERCENT",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 13,
    business_name: "Brand Awareness Percentage",
    business_description: "Unaided brand awareness among target consumers. Key metric for marketing effectiveness and category presence. Correlates with market share potential. Tracked through consumer research panels.",
    business_synonyms: ["Awareness Rate", "Brand Recognition", "Unaided Awareness", "Top of Mind", "Brand Recall", "Awareness Level"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high"
}),
(consideration_rate:Column {
    column_id: "COL_CONSIDERATION_RATE_063",
    column_name: "CONSIDERATION_RATE",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 14,
    business_name: "Brand Consideration Rate",
    business_description: "Percentage of aware consumers who would consider purchasing the brand. Key indicator of brand health and conversion potential. Drives trial generation strategies and promotional investments.",
    business_synonyms: ["Purchase Consideration", "Consideration Percent", "Intent Rate", "Preference Rate", "Choice Set Inclusion", "Brand Consideration"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high"
}),
(loyalty_index:Column {
    column_id: "COL_LOYALTY_INDEX_064",
    column_name: "LOYALTY_INDEX",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 15,
    business_name: "Brand Loyalty Index",
    business_description: "Composite measure of repeat purchase rate, share of requirements, and advocacy. Predicts long-term brand sustainability and price premium potential. Higher scores indicate stronger consumer bonds.",
    business_synonyms: ["Loyalty Score", "Retention Index", "Brand Stickiness", "Repeat Rate", "Customer Loyalty", "Brand Affinity"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 2,
    business_criticality: "high"
}),
(net_promoter_score:Column {
    column_id: "COL_NET_PROMOTER_SCORE_065",
    column_name: "NET_PROMOTER_SCORE",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 16,
    business_name: "Net Promoter Score",
    business_description: "NPS measuring likelihood to recommend brand (-100 to +100). Industry standard for brand advocacy and customer satisfaction. Correlates with growth potential and word-of-mouth marketing effectiveness.",
    business_synonyms: ["NPS", "Advocacy Score", "Recommendation Score", "Promoter Score", "Brand NPS", "Customer Advocacy"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    business_criticality: "high"
}),

// ========================================
// FINANCIAL AND MARKET METRICS
// ========================================

(brand_revenue_ltm:Column {
    column_id: "COL_BRAND_REVENUE_LTM_066",
    column_name: "BRAND_REVENUE_LTM",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 17,
    business_name: "Brand Revenue Last 12 Months",
    business_description: "Total net revenue generated by brand in trailing twelve months. Critical for brand portfolio decisions, investment allocation, and performance benchmarking. Updated monthly with financial close.",
    business_synonyms: ["Annual Revenue", "LTM Sales", "Brand Sales", "Revenue TTM", "Annual Brand Revenue", "12-Month Revenue"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 2,
    business_criticality: "critical"
}),
(market_share_percent:Column {
    column_id: "COL_MARKET_SHARE_PERCENT_067",
    column_name: "MARKET_SHARE_PERCENT",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 18,
    business_name: "Market Share Percentage",
    business_description: "Brand's share of total category sales in measured channels. Based on syndicated data (Nielsen/IRI). Critical KPI for competitive positioning and sales presentations. Drives shelf space negotiations.",
    business_synonyms: ["Share of Market", "Category Share", "Market Position", "Share Percent", "Competitive Share", "Brand Share"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "critical"
}),
(growth_rate_yoy:Column {
    column_id: "COL_GROWTH_RATE_YOY_068",
    column_name: "GROWTH_RATE_YOY",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 19,
    business_name: "Year-over-Year Growth Rate",
    business_description: "Annual revenue growth rate versus prior year. Key indicator of brand momentum and market competitiveness. Drives resource allocation and strategic focus. Affects investor communications.",
    business_synonyms: ["YOY Growth", "Annual Growth", "Revenue Growth", "Sales Growth Rate", "Year on Year", "Growth Percentage"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 8,
    scale: 2,
    business_criticality: "high"
}),
(gross_margin_percent:Column {
    column_id: "COL_GROSS_MARGIN_PERCENT_069",
    column_name: "GROSS_MARGIN_PERCENT",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 20,
    business_name: "Gross Margin Percentage",
    business_description: "Average gross margin across brand portfolio. Indicates pricing power and cost structure efficiency. Critical for profitability analysis and investment decisions. Varies by channel and geography.",
    business_synonyms: ["Margin Percent", "Gross Profit Margin", "Brand Margin", "Profitability Rate", "Margin Rate", "GP%"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high"
}),
(marketing_investment_ratio:Column {
    column_id: "COL_MARKETING_INVEST_RATIO_070",
    column_name: "MARKETING_INVESTMENT_RATIO",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 21,
    business_name: "Marketing Investment Ratio",
    business_description: "Marketing spend as percentage of brand revenue. Indicates brand support level and growth ambitions. Benchmarked against category norms. Higher for new brands and during restages.",
    business_synonyms: ["Marketing Ratio", "A&P Ratio", "Marketing Spend Rate", "Investment Rate", "Support Ratio", "Marketing Intensity"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high"
}),

// ========================================
// DIGITAL AND SOCIAL PRESENCE
// ========================================

(digital_engagement_index:Column {
    column_id: "COL_DIGITAL_ENGAGEMENT_071",
    column_name: "DIGITAL_ENGAGEMENT_INDEX",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 22,
    business_name: "Digital Engagement Index",
    business_description: "Composite metric measuring brand digital presence including social followers, engagement rates, and e-commerce contribution. Predictive of brand momentum and younger consumer relevance. Critical for digital-first brand strategies.",
    business_synonyms: ["Digital Index", "Online Engagement", "Digital Score", "Social Index", "Digital Presence", "Online Performance"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 2,
    business_criticality: "medium"
}),
(social_media_followers:Column {
    column_id: "COL_SOCIAL_FOLLOWERS_072",
    column_name: "SOCIAL_MEDIA_FOLLOWERS",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 23,
    business_name: "Social Media Followers",
    business_description: "Total followers across major social platforms (Instagram, Facebook, TikTok, Twitter). Indicates brand reach and engagement potential. Critical for influencer strategies and viral marketing.",
    business_synonyms: ["Social Followers", "Follower Count", "Social Audience", "Fan Base", "Social Reach", "Community Size"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    business_criticality: "medium"
}),
(ecommerce_revenue_percent:Column {
    column_id: "COL_ECOMMERCE_REV_PCT_073",
    column_name: "ECOMMERCE_REVENUE_PERCENT",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 24,
    business_name: "E-commerce Revenue Percentage",
    business_description: "Percentage of brand revenue from digital commerce channels. Indicates digital transformation progress and omnichannel success. Growing importance for direct-to-consumer strategies.",
    business_synonyms: ["Online Revenue Share", "Digital Sales Percent", "E-comm Share", "Online Percentage", "Digital Commerce Rate"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high"
}),
(brand_website_url:Column {
    column_id: "COL_BRAND_WEBSITE_URL_074",
    column_name: "BRAND_WEBSITE_URL",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 25,
    business_name: "Brand Website URL",
    business_description: "Primary brand website for consumer engagement and e-commerce. Central hub for brand storytelling and product information. Critical for SEO and digital marketing campaigns.",
    business_synonyms: ["Website", "Brand URL", "Web Address", "Online Home", "Digital Hub", "Brand Site"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 500,
    business_criticality: "medium"
}),

// ========================================
// INNOVATION AND PRODUCT DEVELOPMENT
// ========================================

(innovation_index:Column {
    column_id: "COL_INNOVATION_INDEX_075",
    column_name: "INNOVATION_INDEX",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 26,
    business_name: "Innovation Index",
    business_description: "Measure of brand innovation vitality including new product success rate, revenue from new products, and innovation pipeline strength. Critical for growth sustainability and competitive advantage.",
    business_synonyms: ["Innovation Score", "NPD Index", "Innovation Rate", "New Product Index", "Innovation Vitality", "R&D Index"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high"
}),
(new_product_revenue_pct:Column {
    column_id: "COL_NEW_PRODUCT_REV_PCT_076",
    column_name: "NEW_PRODUCT_REVENUE_PCT",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 27,
    business_name: "New Product Revenue Percentage",
    business_description: "Percentage of revenue from products launched in last 3 years. Key indicator of innovation success and portfolio freshness. Target varies by category velocity. Critical for growth strategies.",
    business_synonyms: ["NPD Revenue Share", "Innovation Revenue", "New Item Percent", "Launch Revenue", "New Product Share"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high"
}),
(product_count_active:Column {
    column_id: "COL_PRODUCT_COUNT_ACTIVE_077",
    column_name: "PRODUCT_COUNT_ACTIVE",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 28,
    business_name: "Active Product Count",
    business_description: "Number of active SKUs under brand umbrella. Indicates portfolio complexity and operational challenges. Balance needed between variety and efficiency. Affects supply chain costs.",
    business_synonyms: ["SKU Count", "Product Range", "Active SKUs", "Portfolio Size", "Item Count", "Product Variety"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    business_criticality: "medium"
}),

// ========================================
// SUSTAINABILITY AND ESG
// ========================================

(sustainability_commitment_score:Column {
    column_id: "COL_BRAND_SUSTAINABILITY_078",
    column_name: "SUSTAINABILITY_COMMITMENT_SCORE",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 29,
    business_name: "Brand Sustainability Score",
    business_description: "Composite score measuring brand sustainability commitments including carbon neutrality, packaging circularity, and social responsibility. Increasingly important for consumer choice and retailer partnerships. Affects premium positioning and Gen Z appeal.",
    business_synonyms: ["ESG Score", "Sustainability Index", "Green Score", "Environmental Rating", "Eco Score", "Sustainability Rating"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "medium"
}),
(carbon_neutral_flag:Column {
    column_id: "COL_CARBON_NEUTRAL_FLAG_079",
    column_name: "CARBON_NEUTRAL_FLAG",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 30,
    business_name: "Carbon Neutral Flag",
    business_description: "Indicates brand has achieved carbon neutrality through reduction and offset programs. Growing requirement for retailer partnerships and consumer expectations. Requires third-party verification.",
    business_synonyms: ["Net Zero Flag", "Carbon Neutral", "Climate Neutral", "Zero Carbon", "Carbon Neutral Status"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "medium"
}),
(sustainable_packaging_pct:Column {
    column_id: "COL_SUSTAINABLE_PACK_PCT_080",
    column_name: "SUSTAINABLE_PACKAGING_PCT",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 31,
    business_name: "Sustainable Packaging Percentage",
    business_description: "Percentage of brand portfolio using recyclable, compostable, or reusable packaging. Critical for retailer scorecards and regulatory compliance. Target of 100% by 2025 for many companies.",
    business_synonyms: ["Green Packaging Rate", "Eco Pack Percent", "Recyclable Rate", "Sustainable Pack", "Circular Packaging"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "medium"
}),

// ========================================
// REGULATORY AND COMPLIANCE
// ========================================

(trademark_registration:Column {
    column_id: "COL_TRADEMARK_REG_081",
    column_name: "TRADEMARK_REGISTRATION",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 32,
    business_name: "Trademark Registration",
    business_description: "Trademark registration details including number, classes, and territories. Critical for brand protection and licensing agreements. Must be maintained and defended against infringement.",
    business_synonyms: ["TM Registration", "Trademark Number", "Brand Registration", "IP Registration", "Mark Registration", "Legal Registration"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "high"
}),
(trademark_expiry_date:Column {
    column_id: "COL_TRADEMARK_EXPIRY_082",
    column_name: "TRADEMARK_EXPIRY_DATE",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 33,
    business_name: "Trademark Expiry Date",
    business_description: "Expiration date of trademark registration requiring renewal. Critical for maintaining brand rights and avoiding loss of intellectual property. Automated alerts prevent lapses.",
    business_synonyms: ["TM Expiry", "Registration Expiry", "Trademark Renewal Date", "IP Expiry", "Mark Expiration"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    business_criticality: "high"
}),
(regulatory_restrictions:Column {
    column_id: "COL_REGULATORY_RESTRICT_083",
    column_name: "REGULATORY_RESTRICTIONS",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 34,
    business_name: "Regulatory Restrictions",
    business_description: "Domain-specific regulatory constraints on brand marketing, distribution, or claims. Includes advertising restrictions, age-gating requirements, and health claim limitations. Critical for compliance.",
    business_synonyms: ["Legal Restrictions", "Regulatory Constraints", "Compliance Requirements", "Marketing Restrictions", "Legal Limitations"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "TEXT",
    business_criticality: "high"
}),

// ========================================
// COMPETITIVE INTELLIGENCE
// ========================================

(primary_competitor_brands:Column {
    column_id: "COL_PRIMARY_COMPETITORS_084",
    column_name: "PRIMARY_COMPETITOR_BRANDS",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 35,
    business_name: "Primary Competitor Brands",
    business_description: "Top 3-5 direct competitive brands based on consumer switching and market positioning. Drives competitive intelligence monitoring and defensive strategies. Updated through consumer research.",
    business_synonyms: ["Key Competitors", "Main Competition", "Competitive Set", "Direct Competitors", "Brand Competition", "Competitor List"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "TEXT",
    business_criticality: "high"
}),
(competitive_advantage:Column {
    column_id: "COL_COMPETITIVE_ADVANTAGE_085",
    column_name: "COMPETITIVE_ADVANTAGE",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 36,
    business_name: "Competitive Advantage",
    business_description: "Key differentiators providing sustainable competitive advantage. Could be technology, heritage, distribution, or cost structure. Critical for strategic planning and investment focus.",
    business_synonyms: ["Key Advantage", "Differentiation", "Unique Advantage", "Competitive Edge", "Brand Moat", "Strategic Advantage"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "TEXT",
    business_criticality: "high"
}),
(share_of_voice_pct:Column {
    column_id: "COL_SHARE_OF_VOICE_PCT_086",
    column_name: "SHARE_OF_VOICE_PCT",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 37,
    business_name: "Share of Voice Percentage",
    business_description: "Brand's share of total category advertising spend. Indicates competitive marketing pressure and investment adequacy. Should align with share of market objectives. Tracked through media monitoring.",
    business_synonyms: ["SOV", "Media Share", "Advertising Share", "Voice Share", "Marketing Share", "Ad Spend Share"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "medium"
}),

// ========================================
// CHANNEL AND DISTRIBUTION
// ========================================

(primary_distribution_channel:Column {
    column_id: "COL_PRIMARY_CHANNEL_087",
    column_name: "PRIMARY_DISTRIBUTION_CHANNEL",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 38,
    business_name: "Primary Distribution Channel",
    business_description: "Dominant channel for brand distribution (Mass, Drug, Club, E-commerce, Specialty). Drives go-to-market strategies and sales force alignment. Affects margin structure and promotional tactics.",
    business_synonyms: ["Main Channel", "Primary Outlet", "Core Channel", "Distribution Focus", "Channel Strategy", "Key Channel"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high"
}),
(exclusive_retailer_flag:Column {
    column_id: "COL_EXCLUSIVE_RETAILER_088",
    column_name: "EXCLUSIVE_RETAILER_FLAG",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 39,
    business_name: "Exclusive Retailer Flag",
    business_description: "Indicates brand has exclusive distribution agreement with specific retailer. Affects growth potential but may provide guaranteed distribution and marketing support. Common for private label alternatives.",
    business_synonyms: ["Exclusive Distribution", "Retailer Exclusive", "Single Retailer", "Exclusive Flag", "Distribution Exclusive"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "medium"
}),
(international_markets_count:Column {
    column_id: "COL_INTL_MARKETS_COUNT_089",
    column_name: "INTERNATIONAL_MARKETS_COUNT",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 40,
    business_name: "International Markets Count",
    business_description: "Number of countries where brand is actively distributed. Indicator of global reach and complexity. Affects supply chain design and regulatory compliance requirements. Growth opportunity metric.",
    business_synonyms: ["Global Markets", "Country Count", "International Presence", "Market Count", "Geographic Reach", "Global Footprint"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    business_criticality: "medium"
}),

// ========================================
// DOMAIN-SPECIFIC ATTRIBUTES
// ========================================

(alcohol_category:Column {
    column_id: "COL_ALCOHOL_CATEGORY_090",
    column_name: "ALCOHOL_CATEGORY",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 41,
    business_name: "Alcohol Category",
    business_description: "Specific alcoholic beverage category (Beer, Wine, Spirits, RTD/Seltzers). Determines regulatory requirements, tax rates, and distribution restrictions. Critical for TTB compliance and route-to-market.",
    business_synonyms: ["Beverage Type", "Alcohol Type", "Product Category", "TTB Category", "Liquor Type", "Beverage Category"],
    applicable_domains: ["alcoholic_beverages"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "critical"
}),
(therapeutic_area:Column {
    column_id: "COL_THERAPEUTIC_AREA_091",
    column_name: "THERAPEUTIC_AREA",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 42,
    business_name: "Therapeutic Area",
    business_description: "Primary therapeutic category for pharmaceutical brands (Cardiovascular, Oncology, CNS, etc.). Drives R&D investment, sales force structure, and regulatory pathways. Critical for portfolio planning.",
    business_synonyms: ["Therapy Area", "Medical Category", "Drug Category", "Treatment Area", "Pharma Category", "Disease Area"],
    applicable_domains: ["pharmaceuticals"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "critical"
}),
(age_segment_toys:Column {
    column_id: "COL_AGE_SEGMENT_TOYS_092",
    column_name: "AGE_SEGMENT_TOYS",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 43,
    business_name: "Toy Age Segment",
    business_description: "Primary age range for toy brands (Infant/Toddler, Preschool, Kids, Tweens). Determines safety requirements, marketing restrictions, and seasonal patterns. Critical for CPSC compliance.",
    business_synonyms: ["Age Group", "Toy Age Range", "Target Age", "Age Category", "Developmental Stage", "Age Market"],
    applicable_domains: ["toys"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high"
}),
(beauty_category:Column {
    column_id: "COL_BEAUTY_CATEGORY_093",
    column_name: "BEAUTY_CATEGORY",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 44,
    business_name: "Beauty Category",
    business_description: "Cosmetics subcategory (Skincare, Makeup, Fragrance, Haircare). Drives channel strategy, influencer partnerships, and innovation focus. Significant margin and velocity differences by category.",
    business_synonyms: ["Cosmetic Type", "Beauty Segment", "Product Category", "Beauty Type", "Cosmetic Category", "Personal Care Type"],
    applicable_domains: ["cosmetics"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high"
}),
(organic_portfolio_flag:Column {
    column_id: "COL_ORGANIC_PORTFOLIO_094",
    column_name: "ORGANIC_PORTFOLIO_FLAG",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 45,
    business_name: "Organic Portfolio Flag",
    business_description: "Indicates brand portfolio is primarily USDA Organic certified. Enables premium pricing and natural channel distribution. Requires supply chain commitment and certification maintenance.",
    business_synonyms: ["Organic Brand", "Natural Portfolio", "Organic Flag", "Certified Organic Brand", "Organic Line"],
    applicable_domains: ["food_beverage"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "BOOLEAN",
    business_criticality: "high"
}),
(battery_technology_type:Column {
    column_id: "COL_BATTERY_TECH_TYPE_095",
    column_name: "BATTERY_TECHNOLOGY_TYPE",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 46,
    business_name: "Battery Technology Type",
    business_description: "Primary battery technology focus (Alkaline, Lithium, Rechargeable, Specialty). Determines R&D investments, patent portfolio, and competitive positioning. Affects environmental regulations.",
    business_synonyms: ["Battery Tech", "Technology Focus", "Battery Type", "Power Technology", "Cell Technology", "Energy Type"],
    applicable_domains: ["battery"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high"
}),

// ========================================
// BRAND LIFECYCLE AND STATUS
// ========================================

(brand_status:Column {
    column_id: "COL_BRAND_STATUS_096",
    column_name: "BRAND_STATUS",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 47,
    business_name: "Brand Status",
    business_description: "Current lifecycle status (Active, Dormant, Sunset, Divested). Determines marketing support levels and inventory strategies. Critical for portfolio optimization and working capital management.",
    business_synonyms: ["Status", "Brand State", "Lifecycle Status", "Active Status", "Current Status", "Brand Phase"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "critical"
}),
(brand_launch_date:Column {
    column_id: "COL_BRAND_LAUNCH_DATE_097",
    column_name: "BRAND_LAUNCH_DATE",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 48,
    business_name: "Brand Launch Date",
    business_description: "Original brand launch date establishing heritage and market tenure. Important for anniversary marketing and brand storytelling. Historical brands command premium positioning.",
    business_synonyms: ["Launch Date", "Introduction Date", "Brand Start Date", "Founding Date", "Origin Date", "Establishment Date"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    business_criticality: "medium"
}),
(brand_acquisition_date:Column {
    column_id: "COL_BRAND_ACQUISITION_098",
    column_name: "BRAND_ACQUISITION_DATE",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 49,
    business_name: "Brand Acquisition Date",
    business_description: "Date brand was acquired if not organically developed. Important for M&A tracking, earnout calculations, and integration milestones. NULL for organically developed brands.",
    business_synonyms: ["Acquisition Date", "Purchase Date", "M&A Date", "Deal Date", "Transfer Date", "Buyout Date"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    business_criticality: "medium"
}),
(brand_owner_company:Column {
    column_id: "COL_BRAND_OWNER_COMPANY_099",
    column_name: "BRAND_OWNER_COMPANY",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 50,
    business_name: "Brand Owner Company",
    business_description: "Legal entity owning brand intellectual property. Critical for licensing agreements, tax planning, and financial reporting. May differ from operating company in complex structures.",
    business_synonyms: ["Owner Entity", "Brand Owner", "IP Owner", "Legal Owner", "Parent Company", "Holding Company"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 200,
    business_criticality: "high"
}),

// ========================================
// METADATA AND GOVERNANCE
// ========================================

(brand_manager:Column {
    column_id: "COL_BRAND_MANAGER_100",
    column_name: "BRAND_MANAGER",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 51,
    business_name: "Brand Manager",
    business_description: "Assigned brand management leader responsible for P&L and strategic direction. Key contact for cross-functional coordination. Changes with organizational restructuring.",
    business_synonyms: ["Brand Lead", "Brand Owner", "Product Manager", "Brand Director", "Marketing Manager", "Brand Steward"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "medium"
}),
(last_strategic_review_date:Column {
    column_id: "COL_LAST_REVIEW_DATE_101",
    column_name: "LAST_STRATEGIC_REVIEW_DATE",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 52,
    business_name: "Last Strategic Review Date",
    business_description: "Date of most recent comprehensive brand strategy review. Triggers for portfolio rationalization and investment reallocation. Best practice is annual review cycle.",
    business_synonyms: ["Review Date", "Strategy Review", "Assessment Date", "Evaluation Date", "Analysis Date", "Planning Date"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    business_criticality: "medium"
}),
(data_quality_score:Column {
    column_id: "COL_DATA_QUALITY_SCORE_102",
    column_name: "DATA_QUALITY_SCORE",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 53,
    business_name: "Data Quality Score",
    business_description: "Composite score indicating completeness and accuracy of brand master data. Affects reliability of analytics and reporting. Automated scoring based on field completion and validation rules.",
    business_synonyms: ["Quality Score", "Data Completeness", "Master Data Quality", "DQ Score", "Data Integrity Score", "Completeness Index"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "medium"
}),
(created_timestamp:Column {
    column_id: "COL_CREATED_TIMESTAMP_103",
    column_name: "CREATED_TIMESTAMP",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 54,
    business_name: "Record Created Timestamp",
    business_description: "System timestamp when brand record was created. Used for audit trails and data lineage. Immutable after creation. Critical for compliance and change management.",
    business_synonyms: ["Creation Time", "Insert Timestamp", "Created Date", "Record Creation", "Entry Timestamp", "Initial Load Time"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    business_criticality: "medium"
}),
(modified_timestamp:Column {
    column_id: "COL_MODIFIED_TIMESTAMP_104",
    column_name: "MODIFIED_TIMESTAMP",
    table_id: "DIM_BRAND_MASTER",
    ordinal_position: 55,
    business_name: "Last Modified Timestamp",
    business_description: "Most recent update timestamp for brand master record. Tracks data freshness and change frequency. Updated automatically with any field modification. Used for incremental processing.",
    business_synonyms: ["Update Time", "Last Modified", "Change Timestamp", "Modified Date", "Last Updated", "Revision Timestamp"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    business_criticality: "medium"
});

// ========================================
// CREATE TABLE-COLUMN RELATIONSHIPS
// ========================================

MATCH (t:Table {table_id: "DIM_BRAND_MASTER"})
MATCH (c:Column {table_id: "DIM_BRAND_MASTER"})
CREATE (t)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(c);

// ========================================
// VERIFICATION QUERIES
// ========================================

// Count total columns created
MATCH (c:Column {table_id: "DIM_BRAND_MASTER"})
RETURN count(c) AS total_columns;

// Verify columns by domain specificity
MATCH (c:Column {table_id: "DIM_BRAND_MASTER"})
RETURN c.domain_specific AS is_domain_specific, 
       count(c) AS column_count
ORDER BY is_domain_specific;

// List domain-specific columns with their applicable domains
MATCH (c:Column {table_id: "DIM_BRAND_MASTER"})
WHERE c.domain_specific = true
RETURN c.business_name AS column_name, 
       c.applicable_domains AS domains
ORDER BY c.ordinal_position;

// Verify critical columns
MATCH (c:Column {table_id: "DIM_BRAND_MASTER"})
WHERE c.business_criticality = "critical"
RETURN c.business_name AS critical_column, 
       c.applicable_domains AS domains
ORDER BY c.ordinal_position;

// Check columns by category
MATCH (c:Column {table_id: "DIM_BRAND_MASTER"})
RETURN 
    CASE 
        WHEN c.ordinal_position <= 6 THEN "Core Identifiers"
        WHEN c.ordinal_position <= 11 THEN "Brand Positioning"
        WHEN c.ordinal_position <= 16 THEN "Brand Equity"
        WHEN c.ordinal_position <= 21 THEN "Financial Metrics"
        WHEN c.ordinal_position <= 25 THEN "Digital Presence"
        WHEN c.ordinal_position <= 28 THEN "Innovation"
        WHEN c.ordinal_position <= 31 THEN "Sustainability"
        WHEN c.ordinal_position <= 34 THEN "Regulatory"
        WHEN c.ordinal_position <= 37 THEN "Competitive"
        WHEN c.ordinal_position <= 40 THEN "Channel"
        WHEN c.ordinal_position <= 46 THEN "Domain Specific"
        WHEN c.ordinal_position <= 50 THEN "Lifecycle"
        ELSE "Metadata"
    END AS category,
    count(c) AS column_count
ORDER BY category;

// ========================================
// END OF DIM_BRAND_MASTER COLUMN CREATION
// ========================================