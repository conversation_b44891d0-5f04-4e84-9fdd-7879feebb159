// ========================================
// DIM_DATE - COMPREHENSIVE COLUMN CREATION
// Complete date/time dimension columns for all CPG domains
// ========================================

// Clear existing columns for DIM_DATE (optional)
MATCH (c:Column {table_id: "DIM_DATE"}) DETACH DELETE c;

// ========================================
// CORE DATE IDENTIFIERS AND BASIC TEMPORAL ATTRIBUTES
// ========================================

CREATE 
(date_key:Column {
    column_id: "COL_DATE_KEY_DIM_401",
    column_name: "DATE_KEY",
    table_id: "DIM_DATE",
    ordinal_position: 1,
    business_name: "Date Key",
    business_description: "Unique integer identifier for each date, typically in YYYYMMDD format (e.g., 20250708). Primary key for date dimension enabling efficient joins with fact tables for sales, inventory, and promotional analysis in CPG retail environments. Critical for performance optimization in large-scale analytics.",
    business_synonyms: ["Date ID", "Date Surrogate Key", "Date SK", "Time Key", "Calendar Key", "Date Reference", "Date Index", "Temporal Key"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "INTEGER",
    max_length: null,
    is_primary_key: true,
    business_criticality: "critical"
}),

(full_date:Column {
    column_id: "COL_FULL_DATE_DIM_402",
    column_name: "FULL_DATE",
    table_id: "DIM_DATE",
    ordinal_position: 2,
    business_name: "Full Date",
    business_description: "Complete date value in standard date format (YYYY-MM-DD). Essential for time-series analysis of CPG sales patterns, seasonal trends, promotional effectiveness, and inventory planning across all product categories. Natural key for date relationships and human-readable reporting.",
    business_synonyms: ["Calendar Date", "Date Value", "Transaction Date", "Business Date", "Actual Date", "Date Field", "Standard Date", "ISO Date"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    max_length: null,
    is_primary_key: false,
    business_criticality: "critical"
}),

// ========================================
// CALENDAR HIERARCHY - YEAR LEVEL
// ========================================

(year:Column {
    column_id: "COL_YEAR_DIM_403",
    column_name: "YEAR",
    table_id: "DIM_DATE",
    ordinal_position: 3,
    business_name: "Calendar Year",
    business_description: "Four-digit calendar year (e.g., 2025). Critical for annual CPG performance analysis, year-over-year comparisons, long-term trend identification, and strategic planning across all consumer product categories. Foundation for all annual reporting and budgeting processes.",
    business_synonyms: ["Calendar Year", "Reporting Year", "Business Year", "Annual Period", "Year Number", "YYYY", "Yearly Period"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "temporal",
    data_type: "INTEGER",
    max_length: null,
    is_primary_key: false,
    business_criticality: "high"
}),

(year_name:Column {
    column_id: "COL_YEAR_NAME_DIM_404",
    column_name: "YEAR_NAME",
    table_id: "DIM_DATE",
    ordinal_position: 4,
    business_name: "Year Name",
    business_description: "Descriptive year label (e.g., '2025', 'CY2025'). User-friendly format for executive dashboards and financial reports. May include prefixes for fiscal vs calendar year distinction in reporting.",
    business_synonyms: ["Year Label", "Year Description", "Year Display", "Calendar Year Name", "Year Text", "Yearly Label"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "descriptive",
    data_type: "VARCHAR",
    max_length: 10,
    is_primary_key: false,
    business_criticality: "medium"
}),

// ========================================
// CALENDAR HIERARCHY - QUARTER LEVEL
// ========================================

(quarter:Column {
    column_id: "COL_QUARTER_DIM_405",
    column_name: "QUARTER",
    table_id: "DIM_DATE",
    ordinal_position: 5,
    business_name: "Calendar Quarter",
    business_description: "Calendar quarter number (1-4). Essential for CPG quarterly reporting, seasonal analysis especially for toys (Q4 holiday surge), cosmetics (seasonal launches), beverages (summer peaks), and food products with seasonal demand patterns. Foundation for quarterly business reviews.",
    business_synonyms: ["Calendar Quarter", "Quarterly Period", "Quarter Number", "Q1-Q4", "Three Month Period", "Quarterly Segment"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "temporal",
    data_type: "INTEGER",
    max_length: null,
    is_primary_key: false,
    business_criticality: "high"
}),

(quarter_name:Column {
    column_id: "COL_QUARTER_NAME_DIM_406",
    column_name: "QUARTER_NAME",
    table_id: "DIM_DATE",
    ordinal_position: 6,
    business_name: "Quarter Name",
    business_description: "Descriptive quarter name (Q1, Q2, Q3, Q4). Essential for CPG quarterly reporting, executive dashboards, and seasonal performance analysis across all consumer product categories. Standard format for business communications.",
    business_synonyms: ["Quarter Label", "Quarterly Description", "Quarter Abbreviation", "Quarter Code", "Quarterly Name", "Quarter Display"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "descriptive",
    data_type: "VARCHAR",
    max_length: 10,
    is_primary_key: false,
    business_criticality: "medium"
}),

(year_quarter:Column {
    column_id: "COL_YEAR_QUARTER_DIM_407",
    column_name: "YEAR_QUARTER",
    table_id: "DIM_DATE",
    ordinal_position: 7,
    business_name: "Year Quarter",
    business_description: "Combined year and quarter identifier (e.g., '2025Q1', '2025Q2'). Enables unique quarterly identification across multiple years for trend analysis and quarter-over-quarter comparisons in CPG performance tracking.",
    business_synonyms: ["Year-Quarter", "Quarter Year", "YQ", "Quarterly Period", "Year Quarter Code", "Quarter Identifier"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "temporal",
    data_type: "VARCHAR",
    max_length: 10,
    is_primary_key: false,
    business_criticality: "medium"
}),

// ========================================
// CALENDAR HIERARCHY - MONTH LEVEL
// ========================================

(month:Column {
    column_id: "COL_MONTH_DIM_408",
    column_name: "MONTH",
    table_id: "DIM_DATE",
    ordinal_position: 8,
    business_name: "Calendar Month",
    business_description: "Calendar month number (1-12). Vital for CPG monthly sales analysis, inventory planning, promotional calendar alignment, and seasonal demand patterns across all product categories. Critical for understanding monthly buying cycles and promotional effectiveness.",
    business_synonyms: ["Calendar Month", "Month Number", "Monthly Period", "Month of Year", "Monthly Segment", "Month Index"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "temporal",
    data_type: "INTEGER",
    max_length: null,
    is_primary_key: false,
    business_criticality: "high"
}),

(month_name:Column {
    column_id: "COL_MONTH_NAME_DIM_409",
    column_name: "MONTH_NAME",
    table_id: "DIM_DATE",
    ordinal_position: 9,
    business_name: "Month Name",
    business_description: "Full month name (January-December). Essential for CPG reporting and analysis, particularly for seasonal products like toys (December peak), cosmetics (spring launches), beverages (summer peaks), and holiday-driven categories. User-friendly for executive reporting.",
    business_synonyms: ["Month Description", "Calendar Month Name", "Monthly Label", "Month Text", "Full Month Name", "Monthly Description"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "descriptive",
    data_type: "VARCHAR",
    max_length: 15,
    is_primary_key: false,
    business_criticality: "medium"
}),

(month_name_short:Column {
    column_id: "COL_MONTH_NAME_SHORT_DIM_410",
    column_name: "MONTH_NAME_SHORT",
    table_id: "DIM_DATE",
    ordinal_position: 10,
    business_name: "Month Name Short",
    business_description: "Three-letter month abbreviation (Jan, Feb, Mar, etc.). Compact format for dashboard displays and charts where space is limited. Standard abbreviation format for business intelligence tools.",
    business_synonyms: ["Month Abbreviation", "Short Month", "Month Code", "Mon", "Monthly Code", "Month Abbr"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "descriptive",
    data_type: "VARCHAR",
    max_length: 5,
    is_primary_key: false,
    business_criticality: "low"
}),

(year_month:Column {
    column_id: "COL_YEAR_MONTH_DIM_411",
    column_name: "YEAR_MONTH",
    table_id: "DIM_DATE",
    ordinal_position: 11,
    business_name: "Year Month",
    business_description: "Combined year and month identifier (e.g., '2025-01', 'Jan 2025'). Enables unique monthly identification across multiple years for month-over-month and year-over-year monthly comparisons in CPG analytics.",
    business_synonyms: ["Year-Month", "Month Year", "YM", "Monthly Period", "Year Month Code", "Month Identifier"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "temporal",
    data_type: "VARCHAR",
    max_length: 15,
    is_primary_key: false,
    business_criticality: "medium"
}),

// ========================================
// CALENDAR HIERARCHY - WEEK LEVEL
// ========================================

(week:Column {
    column_id: "COL_WEEK_DIM_412",
    column_name: "WEEK",
    table_id: "DIM_DATE",
    ordinal_position: 12,
    business_name: "Week of Year",
    business_description: "Week number within the year (1-53). Critical for CPG weekly sales tracking, promotional effectiveness measurement, and short-term demand forecasting across fast-moving consumer goods. Essential for weekly business rhythm and promotional planning.",
    business_synonyms: ["Week Number", "Week of Year", "Weekly Period", "Calendar Week", "WOY", "Week Index"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "temporal",
    data_type: "INTEGER",
    max_length: null,
    is_primary_key: false,
    business_criticality: "medium"
}),

(iso_week:Column {
    column_id: "COL_ISO_WEEK_DIM_413",
    column_name: "ISO_WEEK",
    table_id: "DIM_DATE",
    ordinal_position: 13,
    business_name: "ISO Week Number",
    business_description: "ISO 8601 week number (1-53) where week starts on Monday and first week contains January 4th. Standard for international CPG companies ensuring consistent week definitions across global markets and aligning with European reporting standards.",
    business_synonyms: ["ISO Week", "International Week", "Monday Week", "Standard Week", "ISO 8601 Week", "Global Week"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "temporal",
    data_type: "INTEGER",
    max_length: null,
    is_primary_key: false,
    business_criticality: "medium"
}),

(week_beginning_date:Column {
    column_id: "COL_WEEK_BEGINNING_DATE_DIM_414",
    column_name: "WEEK_BEGINNING_DATE",
    table_id: "DIM_DATE",
    ordinal_position: 14,
    business_name: "Week Beginning Date",
    business_description: "First date of the week (typically Sunday or Monday based on business convention). Essential for CPG weekly aggregations and weekly promotional period definitions. Critical for consistent week-over-week comparisons.",
    business_synonyms: ["Week Start Date", "Week Begin", "WBD", "Week Start", "Beginning of Week", "Week Commence Date"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    max_length: null,
    is_primary_key: false,
    business_criticality: "medium"
}),

(week_ending_date:Column {
    column_id: "COL_WEEK_ENDING_DATE_DIM_415",
    column_name: "WEEK_ENDING_DATE",
    table_id: "DIM_DATE",
    ordinal_position: 15,
    business_name: "Week Ending Date",
    business_description: "Last date of the week (typically Saturday or Sunday). Used for weekly reporting periods and promotional week definitions. Standard format for weekly sales reports and promotional analysis in CPG industry.",
    business_synonyms: ["Week End Date", "Week End", "WED", "End of Week", "Week Close Date", "Weekly Close"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    max_length: null,
    is_primary_key: false,
    business_criticality: "medium"
}),

// ========================================
// CALENDAR HIERARCHY - DAY LEVEL
// ========================================

(day:Column {
    column_id: "COL_DAY_DIM_416",
    column_name: "DAY",
    table_id: "DIM_DATE",
    ordinal_position: 16,
    business_name: "Day of Month",
    business_description: "Day of the month (1-31). Important for CPG daily sales analysis, inventory turnover calculations, promotional timing optimization, and understanding daily sales patterns across all consumer product categories.",
    business_synonyms: ["Day of Month", "Calendar Day", "Daily Period", "Date Number", "DOM", "Monthly Day"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "temporal",
    data_type: "INTEGER",
    max_length: null,
    is_primary_key: false,
    business_criticality: "medium"
}),

(day_of_week:Column {
    column_id: "COL_DAY_OF_WEEK_DIM_417",
    column_name: "DAY_OF_WEEK",
    table_id: "DIM_DATE",
    ordinal_position: 17,
    business_name: "Day of Week Number",
    business_description: "Day of the week number (1-7, where 1=Sunday). Essential for CPG weekday vs weekend sales analysis, understanding consumer shopping patterns, optimizing promotional strategies across retail channels, and staffing decisions.",
    business_synonyms: ["Weekday Number", "Day Number", "Week Day", "DOW", "Day of Week Index", "Weekly Day Number"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "temporal",
    data_type: "INTEGER",
    max_length: null,
    is_primary_key: false,
    business_criticality: "high"
}),

(day_name:Column {
    column_id: "COL_DAY_NAME_DIM_418",
    column_name: "DAY_NAME",
    table_id: "DIM_DATE",
    ordinal_position: 18,
    business_name: "Day Name",
    business_description: "Full day name (Monday-Sunday). Important for CPG consumer behavior analysis, understanding shopping patterns, optimizing promotional timing across different weekdays and weekends, and creating user-friendly reports.",
    business_synonyms: ["Weekday Name", "Day Description", "Day of Week Name", "Daily Label", "Day Text", "Weekly Day Name"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "descriptive",
    data_type: "VARCHAR",
    max_length: 15,
    is_primary_key: false,
    business_criticality: "medium"
}),

(day_name_short:Column {
    column_id: "COL_DAY_NAME_SHORT_DIM_419",
    column_name: "DAY_NAME_SHORT",
    table_id: "DIM_DATE",
    ordinal_position: 19,
    business_name: "Day Name Short",
    business_description: "Three-letter day abbreviation (Mon, Tue, Wed, etc.). Compact format for dashboard displays and charts where space is limited. Standard abbreviation for business intelligence reporting.",
    business_synonyms: ["Day Abbreviation", "Short Day", "Day Code", "Day Abbr", "Daily Code", "Weekday Abbr"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "descriptive",
    data_type: "VARCHAR",
    max_length: 5,
    is_primary_key: false,
    business_criticality: "low"
}),

(day_of_year:Column {
    column_id: "COL_DAY_OF_YEAR_DIM_420",
    column_name: "DAY_OF_YEAR",
    table_id: "DIM_DATE",
    ordinal_position: 20,
    business_name: "Day of Year",
    business_description: "Sequential day number within the year (1-366). Useful for CPG annual trend analysis, seasonal pattern identification, year-over-year daily comparisons across product categories, and calculating days elapsed in promotional periods.",
    business_synonyms: ["Julian Day", "Annual Day Number", "Day Count", "Year Day", "DOY", "Yearly Day"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "temporal",
    data_type: "INTEGER",
    max_length: null,
    is_primary_key: false,
    business_criticality: "low"
}),

// ========================================
// BUSINESS FLAGS AND INDICATORS
// ========================================

(is_weekend:Column {
    column_id: "COL_IS_WEEKEND_DIM_421",
    column_name: "IS_WEEKEND",
    table_id: "DIM_DATE",
    ordinal_position: 21,
    business_name: "Is Weekend Flag",
    business_description: "Boolean flag indicating weekend days (Saturday/Sunday). Critical for CPG analysis of weekend vs weekday sales patterns, promotional effectiveness, consumer shopping behavior across all product categories, and understanding impulse purchase patterns.",
    business_synonyms: ["Weekend Flag", "Weekend Indicator", "Is Weekend Day", "Weekend Boolean", "Weekend Status", "End of Week Flag"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    is_primary_key: false,
    business_criticality: "high"
}),

(is_weekday:Column {
    column_id: "COL_IS_WEEKDAY_DIM_422",
    column_name: "IS_WEEKDAY",
    table_id: "DIM_DATE",
    ordinal_position: 22,
    business_name: "Is Weekday Flag",
    business_description: "Boolean flag indicating weekdays (Monday-Friday). Complement to weekend flag for business day analysis, understanding work-week shopping patterns, and B2B delivery scheduling in CPG supply chain operations.",
    business_synonyms: ["Weekday Flag", "Business Day Flag", "Work Day Flag", "Monday-Friday Flag", "Weekday Boolean", "Business Day Indicator"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    is_primary_key: false,
    business_criticality: "medium"
}),

(is_holiday:Column {
    column_id: "COL_IS_HOLIDAY_DIM_423",
    column_name: "IS_HOLIDAY",
    table_id: "DIM_DATE",
    ordinal_position: 23,
    business_name: "Is Holiday Flag",
    business_description: "Boolean flag indicating public holidays. Essential for CPG holiday sales analysis, inventory planning for peak periods, understanding seasonal spikes especially for toys, food & beverage, personal care products, and managing supply chain disruptions.",
    business_synonyms: ["Holiday Flag", "Holiday Indicator", "Public Holiday", "Holiday Boolean", "Holiday Status", "Federal Holiday Flag"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    is_primary_key: false,
    business_criticality: "high"
}),

(holiday_name:Column {
    column_id: "COL_HOLIDAY_NAME_DIM_424",
    column_name: "HOLIDAY_NAME",
    table_id: "DIM_DATE",
    ordinal_position: 24,
    business_name: "Holiday Name",
    business_description: "Name of the specific holiday (Christmas, Thanksgiving, Independence Day, etc.). Critical for CPG category-specific holiday analysis - Christmas for toys, Thanksgiving for food & beverage, Valentine's Day for cosmetics and personal care products.",
    business_synonyms: ["Holiday Description", "Holiday Title", "Public Holiday Name", "Federal Holiday", "Holiday Event", "Holiday Label"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "descriptive",
    data_type: "VARCHAR",
    max_length: 50,
    is_primary_key: false,
    business_criticality: "medium"
}),

(is_leap_year:Column {
    column_id: "COL_IS_LEAP_YEAR_DIM_425",
    column_name: "IS_LEAP_YEAR",
    table_id: "DIM_DATE",
    ordinal_position: 25,
    business_name: "Is Leap Year Flag",
    business_description: "Boolean flag indicating leap year (366 days). Important for CPG annual comparisons and ensuring accurate year-over-year analysis accounting for the extra day in February during leap years. Affects daily average calculations.",
    business_synonyms: ["Leap Year Flag", "Leap Year Indicator", "366 Day Year", "Leap Year Boolean", "Bissextile Year", "Leap Year Status"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    is_primary_key: false,
    business_criticality: "low"
}),

// ========================================
// SEASONAL AND BUSINESS PERIODS
// ========================================

(season:Column {
    column_id: "COL_SEASON_DIM_426",
    column_name: "SEASON",
    table_id: "DIM_DATE",
    ordinal_position: 26,
    business_name: "Calendar Season",
    business_description: "Calendar season (Spring, Summer, Fall, Winter). Critical for CPG seasonal analysis, particularly for cosmetics (seasonal color launches), beverages (summer peaks), toys (holiday seasons), personal care seasonal products, and outdoor/indoor category shifts.",
    business_synonyms: ["Calendar Season", "Seasonal Period", "Weather Season", "Quarterly Season", "Natural Season", "Meteorological Season"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "descriptive",
    data_type: "VARCHAR",
    max_length: 15,
    is_primary_key: false,
    business_criticality: "high"
}),

(season_year:Column {
    column_id: "COL_SEASON_YEAR_DIM_427",
    column_name: "SEASON_YEAR",
    table_id: "DIM_DATE",
    ordinal_position: 27,
    business_name: "Season Year",
    business_description: "Combined season and year identifier (e.g., 'Spring 2025', 'Winter 2024-2025'). Enables unique seasonal identification across multiple years for season-over-season comparisons in CPG seasonal category analysis.",
    business_synonyms: ["Seasonal Year", "Season Period", "Annual Season", "Yearly Season", "Season Identifier", "Seasonal Period"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "temporal",
    data_type: "VARCHAR",
    max_length: 20,
    is_primary_key: false,
    business_criticality: "medium"
}),

// ========================================
// FISCAL CALENDAR HIERARCHY
// ========================================

(fiscal_year:Column {
    column_id: "COL_FISCAL_YEAR_DIM_428",
    column_name: "FISCAL_YEAR",
    table_id: "DIM_DATE",
    ordinal_position: 28,
    business_name: "Fiscal Year",
    business_description: "Company fiscal year for financial reporting (may differ from calendar year). Critical for CPG financial analysis, annual performance evaluation, budget tracking, and aligning sales data with corporate fiscal calendars across all consumer product categories.",
    business_synonyms: ["Financial Year", "FY", "Corporate Year", "Accounting Year", "Business Year", "Financial Period"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "temporal",
    data_type: "INTEGER",
    max_length: null,
    is_primary_key: false,
    business_criticality: "high"
}),

(fiscal_quarter:Column {
    column_id: "COL_FISCAL_QUARTER_DIM_429",
    column_name: "FISCAL_QUARTER",
    table_id: "DIM_DATE",
    ordinal_position: 29,
    business_name: "Fiscal Quarter",
    business_description: "Company fiscal quarter (1-4) for financial reporting. Essential for CPG quarterly financial analysis, performance tracking, investor reporting, and aligning operational metrics with corporate fiscal reporting periods.",
    business_synonyms: ["Financial Quarter", "FQ", "Corporate Quarter", "Accounting Quarter", "Business Quarter", "Fiscal Q"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "temporal",
    data_type: "INTEGER",
    max_length: null,
    is_primary_key: false,
    business_criticality: "high"
}),

(fiscal_month:Column {
    column_id: "COL_FISCAL_MONTH_DIM_430",
    column_name: "FISCAL_MONTH",
    table_id: "DIM_DATE",
    ordinal_position: 30,
    business_name: "Fiscal Month",
    business_description: "Company fiscal month (1-12) for financial reporting. Important for CPG monthly financial analysis, budget tracking, variance reporting, and aligning sales performance with corporate fiscal calendar across all product categories.",
    business_synonyms: ["Financial Month", "FM", "Corporate Month", "Accounting Month", "Business Month", "Fiscal Period"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "temporal",
    data_type: "INTEGER",
    max_length: null,
    is_primary_key: false,
    business_criticality: "medium"
}),

(fiscal_week:Column {
    column_id: "COL_FISCAL_WEEK_DIM_431",
    column_name: "FISCAL_WEEK",
    table_id: "DIM_DATE",
    ordinal_position: 31,
    business_name: "Fiscal Week",
    business_description: "Company fiscal week number (1-52/53) within the fiscal year. Used for weekly financial reporting and aligning operational week definitions with corporate fiscal calendar. Critical for consistent weekly performance tracking.",
    business_synonyms: ["Financial Week", "FW", "Corporate Week", "Business Week", "Fiscal Week Number", "Company Week"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "temporal",
    data_type: "INTEGER",
    max_length: null,
    is_primary_key: false,
    business_criticality: "medium"
}),

// ========================================
// RETAIL CALENDAR (4-5-4 CALENDAR)
// ========================================

(retail_year:Column {
    column_id: "COL_RETAIL_YEAR_DIM_432",
    column_name: "RETAIL_YEAR",
    table_id: "DIM_DATE",
    ordinal_position: 32,
    business_name: "Retail Calendar Year",
    business_description: "Retail industry standard 4-5-4 calendar year ensuring comparable periods for retail analysis. Critical for CPG companies selling through retail channels to align with retailer reporting periods and industry benchmarks.",
    business_synonyms: ["4-5-4 Year", "Retail Calendar Year", "NRF Year", "Merchandising Year", "Retail Fiscal Year", "Industry Year"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "temporal",
    data_type: "INTEGER",
    max_length: null,
    is_primary_key: false,
    business_criticality: "high"
}),

(retail_quarter:Column {
    column_id: "COL_RETAIL_QUARTER_DIM_433",
    column_name: "RETAIL_QUARTER",
    table_id: "DIM_DATE",
    ordinal_position: 33,
    business_name: "Retail Calendar Quarter",
    business_description: "Retail 4-5-4 calendar quarter (1-4) with standardized week counts (Q1: 4-5-4 weeks, Q2: 4-5-4 weeks, etc.). Essential for comparable quarterly analysis with retail partners and industry benchmarking.",
    business_synonyms: ["4-5-4 Quarter", "Retail Quarter", "NRF Quarter", "Merchandising Quarter", "Industry Quarter", "Retail Q"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "temporal",
    data_type: "INTEGER",
    max_length: null,
    is_primary_key: false,
    business_criticality: "high"
}),

(retail_month:Column {
    column_id: "COL_RETAIL_MONTH_DIM_434",
    column_name: "RETAIL_MONTH",
    table_id: "DIM_DATE",
    ordinal_position: 34,
    business_name: "Retail Calendar Month",
    business_description: "Retail 4-5-4 calendar month (1-12) with standardized 4 or 5 week periods. Critical for CPG monthly analysis aligned with retail industry standards and ensuring comparable periods for competitive analysis.",
    business_synonyms: ["4-5-4 Month", "Retail Month", "NRF Month", "Merchandising Month", "Industry Month", "Retail Period"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "temporal",
    data_type: "INTEGER",
    max_length: null,
    is_primary_key: false,
    business_criticality: "medium"
}),

(retail_week:Column {
    column_id: "COL_RETAIL_WEEK_DIM_435",
    column_name: "RETAIL_WEEK",
    table_id: "DIM_DATE",
    ordinal_position: 35,
    business_name: "Retail Calendar Week",
    business_description: "Retail 4-5-4 calendar week number (1-52/53) ensuring consistent week definitions across the retail industry. Standard for CPG weekly reporting and promotional planning aligned with retail partners.",
    business_synonyms: ["4-5-4 Week", "Retail Week", "NRF Week", "Merchandising Week", "Industry Week", "Standard Week"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "temporal",
    data_type: "INTEGER",
    max_length: null,
    is_primary_key: false,
    business_criticality: "medium"
}),

// ========================================
// DATE RELATIONSHIPS AND OFFSETS
// ========================================

(previous_day_date:Column {
    column_id: "COL_PREVIOUS_DAY_DATE_DIM_436",
    column_name: "PREVIOUS_DAY_DATE",
    table_id: "DIM_DATE",
    ordinal_position: 36,
    business_name: "Previous Day Date",
    business_description: "Date of the previous day for day-over-day analysis and sequential date calculations. Useful for CPG daily trend analysis, inventory movement tracking, and promotional impact measurement.",
    business_synonyms: ["Prior Day", "Yesterday", "Previous Date", "Day Minus 1", "Preceding Day", "Day Before"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    max_length: null,
    is_primary_key: false,
    business_criticality: "low"
}),

(next_day_date:Column {
    column_id: "COL_NEXT_DAY_DATE_DIM_437",
    column_name: "NEXT_DAY_DATE",
    table_id: "DIM_DATE",
    ordinal_position: 37,
    business_name: "Next Day Date",
    business_description: "Date of the following day for forward-looking analysis and sequential date calculations. Useful for CPG promotional planning, inventory forecasting, and demand prediction analysis.",
    business_synonyms: ["Following Day", "Tomorrow", "Next Date", "Day Plus 1", "Succeeding Day", "Day After"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    max_length: null,
    is_primary_key: false,
    business_criticality: "low"
}),

(same_day_last_year:Column {
    column_id: "COL_SAME_DAY_LAST_YEAR_DIM_438",
    column_name: "SAME_DAY_LAST_YEAR",
    table_id: "DIM_DATE",
    ordinal_position: 38,
    business_name: "Same Day Last Year",
    business_description: "Date exactly one year prior for year-over-year comparisons. Critical for CPG annual trend analysis, seasonal pattern recognition, and understanding year-over-year growth in sales and market share.",
    business_synonyms: ["Year Ago Date", "YOY Date", "Same Date Prior Year", "Annual Comparison Date", "Last Year Date", "Year Over Year"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    max_length: null,
    is_primary_key: false,
    business_criticality: "medium"
}),

// ========================================
// SPECIAL BUSINESS PERIODS
// ========================================

(is_month_end:Column {
    column_id: "COL_IS_MONTH_END_DIM_439",
    column_name: "IS_MONTH_END",
    table_id: "DIM_DATE",
    ordinal_position: 39,
    business_name: "Is Month End Flag",
    business_description: "Boolean flag indicating the last day of the calendar month. Important for CPG monthly closing procedures, inventory counts, financial reporting deadlines, and month-end promotional activities.",
    business_synonyms: ["Month End Flag", "End of Month", "Monthly Close", "Month End Indicator", "EOM Flag", "Monthly End"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    is_primary_key: false,
    business_criticality: "medium"
}),

(is_quarter_end:Column {
    column_id: "COL_IS_QUARTER_END_DIM_440",
    column_name: "IS_QUARTER_END",
    table_id: "DIM_DATE",
    ordinal_position: 40,
    business_name: "Is Quarter End Flag",
    business_description: "Boolean flag indicating the last day of the calendar quarter. Critical for CPG quarterly reporting, financial closes, investor communications, and quarter-end promotional pushes to meet targets.",
    business_synonyms: ["Quarter End Flag", "End of Quarter", "Quarterly Close", "Quarter End Indicator", "EOQ Flag", "Quarterly End"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    is_primary_key: false,
    business_criticality: "high"
}),

(is_year_end:Column {
    column_id: "COL_IS_YEAR_END_DIM_441",
    column_name: "IS_YEAR_END",
    table_id: "DIM_DATE",
    ordinal_position: 41,
    business_name: "Is Year End Flag",
    business_description: "Boolean flag indicating the last day of the calendar year (December 31st). Essential for CPG annual reporting, year-end financial closes, annual inventory counts, and year-end promotional activities.",
    business_synonyms: ["Year End Flag", "End of Year", "Annual Close", "Year End Indicator", "EOY Flag", "Annual End"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    is_primary_key: false,
    business_criticality: "high"
}),

// ========================================
// DATE DISPLAY FORMATS
// ========================================

(date_iso_format:Column {
    column_id: "COL_DATE_ISO_FORMAT_DIM_442",
    column_name: "DATE_ISO_FORMAT",
    table_id: "DIM_DATE",
    ordinal_position: 42,
    business_name: "Date ISO Format",
    business_description: "Date in ISO 8601 format (YYYY-MM-DD). International standard format for CPG companies operating globally, ensuring consistent date formatting across systems and regions.",
    business_synonyms: ["ISO Date", "Standard Date Format", "International Date", "ISO 8601", "Global Date Format", "Universal Date"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "descriptive",
    data_type: "VARCHAR",
    max_length: 15,
    is_primary_key: false,
    business_criticality: "low"
}),

(date_us_format:Column {
    column_id: "COL_DATE_US_FORMAT_DIM_443",
    column_name: "DATE_US_FORMAT",
    table_id: "DIM_DATE",
    ordinal_position: 43,
    business_name: "Date US Format",
    business_description: "Date in US format (MM/DD/YYYY). Standard format for CPG companies and retailers operating in the United States market, used in executive reports and dashboards.",
    business_synonyms: ["US Date", "American Date Format", "MM/DD/YYYY", "US Format", "Domestic Date", "American Format"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "descriptive",
    data_type: "VARCHAR",
    max_length: 15,
    is_primary_key: false,
    business_criticality: "low"
}),

(date_display_name:Column {
    column_id: "COL_DATE_DISPLAY_NAME_DIM_444",
    column_name: "DATE_DISPLAY_NAME",
    table_id: "DIM_DATE",
    ordinal_position: 44,
    business_name: "Date Display Name",
    business_description: "User-friendly date display format (e.g., 'July 8, 2025', 'Tuesday, July 8, 2025'). Human-readable format for executive dashboards, reports, and customer-facing applications in CPG analytics.",
    business_synonyms: ["Display Date", "Friendly Date", "Long Date Format", "Readable Date", "Full Date Display", "Formatted Date"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "descriptive",
    data_type: "VARCHAR",
    max_length: 50,
    is_primary_key: false,
    business_criticality: "low"
}),

// ========================================
// METADATA AND AUDIT COLUMNS
// ========================================

(created_timestamp:Column {
    column_id: "COL_CREATED_TIMESTAMP_DIM_445",
    column_name: "CREATED_TIMESTAMP",
    table_id: "DIM_DATE",
    ordinal_position: 45,
    business_name: "Record Created Timestamp",
    business_description: "System timestamp when date record was created. Used for audit trails, data lineage tracking, and data governance. Immutable after creation. Part of comprehensive data governance framework.",
    business_synonyms: ["Creation Time", "Insert Timestamp", "Created Date", "Record Creation", "Entry Timestamp", "Initial Load Time"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    max_length: null,
    is_primary_key: false,
    business_criticality: "low"
}),

(modified_timestamp:Column {
    column_id: "COL_MODIFIED_TIMESTAMP_DIM_446",
    column_name: "MODIFIED_TIMESTAMP",
    table_id: "DIM_DATE",
    ordinal_position: 46,
    business_name: "Last Modified Timestamp",
    business_description: "Most recent update timestamp for date record. Tracks data freshness, change frequency, and maintenance activities. Updated automatically with any field modification for data governance compliance.",
    business_synonyms: ["Update Time", "Last Modified", "Change Timestamp", "Modified Date", "Last Updated", "Revision Timestamp"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    max_length: null,
    is_primary_key: false,
    business_criticality: "low"
});

// ========================================
// CREATE TABLE-COLUMN RELATIONSHIPS
// ========================================

MATCH (t:Table {table_id: "DIM_DATE"})
MATCH (c:Column {table_id: "DIM_DATE"})
CREATE (t)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"]
}]->(c);

// ========================================
// VERIFICATION QUERIES
// ========================================

// Count total columns created
MATCH (c:Column {table_id: "DIM_DATE"})
RETURN count(c) AS total_columns;

// Verify columns by business criticality
MATCH (c:Column {table_id: "DIM_DATE"})
RETURN c.business_criticality AS criticality_level, 
       count(c) AS column_count
ORDER BY criticality_level;

// List critical columns
MATCH (c:Column {table_id: "DIM_DATE"})
WHERE c.business_criticality = "critical"
RETURN c.business_name AS critical_column, 
       c.business_description
ORDER BY c.ordinal_position;

// Check columns by category
MATCH (c:Column {table_id: "DIM_DATE"})
RETURN 
    CASE 
        WHEN c.ordinal_position <= 2 THEN "Core Identifiers"
        WHEN c.ordinal_position <= 7 THEN "Calendar Hierarchy - Year/Quarter"
        WHEN c.ordinal_position <= 15 THEN "Calendar Hierarchy - Month/Week"
        WHEN c.ordinal_position <= 20 THEN "Calendar Hierarchy - Day"
        WHEN c.ordinal_position <= 25 THEN "Business Flags & Indicators"
        WHEN c.ordinal_position <= 27 THEN "Seasonal Periods"
        WHEN c.ordinal_position <= 31 THEN "Fiscal Calendar"
        WHEN c.ordinal_position <= 35 THEN "Retail Calendar (4-5-4)"
        WHEN c.ordinal_position <= 38 THEN "Date Relationships & Offsets"
        WHEN c.ordinal_position <= 41 THEN "Special Business Periods"
        WHEN c.ordinal_position <= 44 THEN "Date Display Formats"
        ELSE "Metadata & Audit"
    END AS category,
    count(c) AS column_count
ORDER BY category;

// Verify semantic types distribution
MATCH (c:Column {table_id: "DIM_DATE"})
RETURN c.semantic_type AS semantic_type, 
       count(c) AS column_count
ORDER BY column_count DESC;

// Check for primary key
MATCH (c:Column {table_id: "DIM_DATE"})
WHERE c.is_primary_key = true
RETURN c.business_name AS primary_key_column, 
       c.column_name AS column_name;

// Verify all columns have proper CPG domain coverage
MATCH (c:Column {table_id: "DIM_DATE"})
WHERE NOT "food_beverage" IN c.applicable_domains
RETURN c.business_name AS missing_cpg_domain;

// ========================================
// END OF DIM_DATE COLUMN CREATION
// ========================================