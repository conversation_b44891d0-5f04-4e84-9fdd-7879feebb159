// ========================================
// FACT_PRICING - COMPREHENSIVE COLUMN CREATION
// Complete pricing fact table columns for all CPG domains
// ========================================

// Clear existing columns for FACT_PRICING (optional)
MATCH (c:Column {table_id: "FACT_PRICING"}) DETACH DELETE c;

// ========================================
// CORE PRICING IDENTIFIERS AND FOREIGN KEYS
// ========================================

CREATE 
(pricing_event_id:Column {
    column_id: "COL_PRICING_EVENT_ID_FACT_5001",
    column_name: "PRICING_EVENT_ID",
    table_id: "FACT_PRICING",
    ordinal_position: 1,
    business_name: "Pricing Event ID",
    business_description: "Unique identifier for pricing events, changes, and transactions across all CPG categories. Critical for price tracking, audit trails, margin analysis, and managing pricing strategies across alcoholic beverages, pharmaceuticals, toys, cosmetics, and all consumer product categories.",
    business_synonyms: ["Price ID", "Pricing Key", "Price Event Key", "Pricing Reference", "Price Transaction ID", "Pricing Record ID", "Price Change ID"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_primary_key: true,
    business_criticality: "critical"
}),

(date_key:Column {
    column_id: "COL_DATE_KEY_FACT_5002",
    column_name: "DATE_KEY",
    table_id: "FACT_PRICING",
    ordinal_position: 2,
    business_name: "Date Key",
    business_description: "Foreign key to date dimension for temporal pricing analysis and price change tracking. Essential for CPG price trend analysis, seasonal pricing patterns, competitive price monitoring, and understanding pricing dynamics over time across all product categories.",
    business_synonyms: ["Price Date Key", "Pricing Date Key", "Date FK", "Date Reference", "Temporal Key", "Effective Date Key"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "INTEGER",
    max_length: null,
    is_foreign_key: true,
    business_criticality: "critical"
}),

(store_key:Column {
    column_id: "COL_STORE_KEY_FACT_5003",
    column_name: "STORE_KEY",
    table_id: "FACT_PRICING",
    ordinal_position: 3,
    business_name: "Store Key",
    business_description: "Foreign key to store dimension for location-based pricing tracking and zone pricing management. Critical for CPG regional pricing strategies, competitive store pricing, local market dynamics, and price optimization by location.",
    business_synonyms: ["Location Key", "Outlet Key", "Store FK", "Site Key", "Location Reference", "Retail Key"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_foreign_key: true,
    business_criticality: "critical"
}),

(product_hierarchy_key:Column {
    column_id: "COL_PRODUCT_HIERARCHY_KEY_FACT_5004",
    column_name: "PRODUCT_HIERARCHY_KEY",
    table_id: "FACT_PRICING",
    ordinal_position: 4,
    business_name: "Product Hierarchy Key",
    business_description: "Foreign key to product hierarchy dimension for product-specific pricing tracking and SKU-level price management. Essential for CPG product pricing strategy, price architecture, category pricing patterns, and brand price positioning.",
    business_synonyms: ["Product Key", "SKU Key", "Product FK", "Item Key", "Product Reference", "Hierarchy FK"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_foreign_key: true,
    business_criticality: "critical"
}),

(customer_key:Column {
    column_id: "COL_CUSTOMER_KEY_FACT_5005",
    column_name: "CUSTOMER_KEY",
    table_id: "FACT_PRICING",
    ordinal_position: 5,
    business_name: "Customer Key",
    business_description: "Foreign key to customer dimension for customer-specific pricing and negotiated rate tracking. Important for CPG B2B pricing, contract pricing management, customer profitability analysis, and personalized pricing strategies.",
    business_synonyms: ["Customer FK", "Account Key", "Customer Reference", "Client FK", "Customer ID FK", "Account Reference"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_foreign_key: true,
    business_criticality: "high"
}),

// ========================================
// BASE PRICING MEASURES
// ========================================

(list_price:Column {
    column_id: "COL_LIST_PRICE_FACT_5006",
    column_name: "LIST_PRICE",
    table_id: "FACT_PRICING",
    ordinal_position: 6,
    business_name: "List Price",
    business_description: "Manufacturer's suggested retail price (MSRP) or standard list price. Critical for CPG pricing architecture, discount calculations, price positioning, and margin baseline across all categories.",
    business_synonyms: ["MSRP", "Suggested Price", "Standard Price", "Base Price", "Catalog Price", "Published Price"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 4,
    business_criticality: "critical",
    additive_type: "non_additive"
}),

(actual_selling_price:Column {
    column_id: "COL_ACTUAL_SELLING_PRICE_FACT_5007",
    column_name: "ACTUAL_SELLING_PRICE",
    table_id: "FACT_PRICING",
    ordinal_position: 7,
    business_name: "Actual Selling Price",
    business_description: "Actual price paid by customer after all discounts and promotions. Essential for CPG revenue realization, true pricing performance, discount impact analysis, and margin calculations.",
    business_synonyms: ["Net Price", "Transaction Price", "Realized Price", "Actual Price", "Final Price", "Paid Price"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 4,
    business_criticality: "critical",
    additive_type: "non_additive"
}),

(regular_retail_price:Column {
    column_id: "COL_REGULAR_RETAIL_PRICE_FACT_5008",
    column_name: "REGULAR_RETAIL_PRICE",
    table_id: "FACT_PRICING",
    ordinal_position: 8,
    business_name: "Regular Retail Price",
    business_description: "Standard shelf price before temporary promotions. Important for CPG baseline pricing, promotional lift calculations, regular margin tracking, and price stability monitoring.",
    business_synonyms: ["Shelf Price", "Everyday Price", "Base Retail Price", "Non-Promo Price", "Regular Price", "Standard Retail"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 4,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(unit_cost:Column {
    column_id: "COL_UNIT_COST_FACT_5009",
    column_name: "UNIT_COST",
    table_id: "FACT_PRICING",
    ordinal_position: 9,
    business_name: "Unit Cost",
    business_description: "Cost per unit for margin calculations and pricing decisions. Critical for CPG profitability analysis, pricing floor determination, margin management, and cost-plus pricing strategies.",
    business_synonyms: ["Product Cost", "COGS per Unit", "Unit COGS", "Cost Basis", "Per Unit Cost", "Item Cost"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 4,
    business_criticality: "critical",
    additive_type: "non_additive"
}),

// ========================================
// DISCOUNT AND PROMOTION PRICING
// ========================================

(discount_amount:Column {
    column_id: "COL_DISCOUNT_AMOUNT_FACT_5010",
    column_name: "DISCOUNT_AMOUNT",
    table_id: "FACT_PRICING",
    ordinal_position: 10,
    business_name: "Discount Amount",
    business_description: "Dollar amount of discount from list or regular price. Fundamental for CPG discount tracking, promotional investment analysis, price erosion monitoring, and margin impact assessment.",
    business_synonyms: ["Price Reduction", "Discount Value", "Price Cut", "Markdown Amount", "Reduction Amount", "Discount Dollar"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 4,
    business_criticality: "high",
    additive_type: "additive"
}),

(discount_percentage:Column {
    column_id: "COL_DISCOUNT_PERCENTAGE_FACT_5011",
    column_name: "DISCOUNT_PERCENTAGE",
    table_id: "FACT_PRICING",
    ordinal_position: 11,
    business_name: "Discount Percentage",
    business_description: "Percentage discount from list or regular price. Essential for CPG promotional depth analysis, competitive discount monitoring, price elasticity testing, and promotional effectiveness.",
    business_synonyms: ["Discount %", "Price Reduction %", "Markdown %", "Discount Rate", "Promotional Depth", "Price Cut %"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(promotional_price:Column {
    column_id: "COL_PROMOTIONAL_PRICE_FACT_5012",
    column_name: "PROMOTIONAL_PRICE",
    table_id: "FACT_PRICING",
    ordinal_position: 12,
    business_name: "Promotional Price",
    business_description: "Temporary promotional selling price. Important for CPG promotional strategy, competitive response, deal depth analysis, and understanding promotional price points.",
    business_synonyms: ["Promo Price", "Sale Price", "Deal Price", "Special Price", "Temporary Price", "Campaign Price"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 4,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(coupon_value:Column {
    column_id: "COL_COUPON_VALUE_FACT_5013",
    column_name: "COUPON_VALUE",
    table_id: "FACT_PRICING",
    ordinal_position: 13,
    business_name: "Coupon Value",
    business_description: "Value of manufacturer or retailer coupons applied. Critical for CPG coupon strategy effectiveness, redemption cost tracking, targeted discount analysis, and customer acquisition costs.",
    business_synonyms: ["Coupon Amount", "Coupon Discount", "Voucher Value", "Coupon Face Value", "Redemption Value", "Coupon Dollar"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 4,
    business_criticality: "medium",
    additive_type: "additive"
}),

// ========================================
// MARGIN AND PROFITABILITY MEASURES
// ========================================

(gross_margin_amount:Column {
    column_id: "COL_GROSS_MARGIN_AMOUNT_FACT_5014",
    column_name: "GROSS_MARGIN_AMOUNT",
    table_id: "FACT_PRICING",
    ordinal_position: 14,
    business_name: "Gross Margin Amount",
    business_description: "Dollar gross margin (selling price minus cost). Fundamental for CPG profitability tracking, pricing effectiveness, margin optimization, and financial performance measurement.",
    business_synonyms: ["Gross Profit", "Margin Dollars", "Profit Margin", "Gross Margin $", "Contribution", "Margin Amount"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 4,
    business_criticality: "critical",
    additive_type: "additive"
}),

(gross_margin_percentage:Column {
    column_id: "COL_GROSS_MARGIN_PERCENTAGE_FACT_5015",
    column_name: "GROSS_MARGIN_PERCENTAGE",
    table_id: "FACT_PRICING",
    ordinal_position: 15,
    business_name: "Gross Margin Percentage",
    business_description: "Gross margin as percentage of selling price. Essential for CPG margin health monitoring, pricing strategy validation, profitability benchmarking, and category margin management.",
    business_synonyms: ["Margin %", "Gross Margin Rate", "Profit Percentage", "Margin Rate", "GM %", "Profitability %"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "critical",
    additive_type: "non_additive"
}),

(net_margin_amount:Column {
    column_id: "COL_NET_MARGIN_AMOUNT_FACT_5016",
    column_name: "NET_MARGIN_AMOUNT",
    table_id: "FACT_PRICING",
    ordinal_position: 16,
    business_name: "Net Margin Amount",
    business_description: "Net margin after all discounts and allowances. Critical for CPG true profitability, net pricing effectiveness, total margin impact, and understanding actual profit contribution.",
    business_synonyms: ["Net Profit", "Net Margin $", "Actual Margin", "Real Margin", "Final Margin", "True Margin"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 4,
    business_criticality: "critical",
    additive_type: "additive"
}),

(contribution_margin_percentage:Column {
    column_id: "COL_CONTRIBUTION_MARGIN_PCT_FACT_5017",
    column_name: "CONTRIBUTION_MARGIN_PERCENTAGE",
    table_id: "FACT_PRICING",
    ordinal_position: 17,
    business_name: "Contribution Margin Percentage",
    business_description: "Margin percentage after variable costs for pricing decisions. Important for CPG pricing floor analysis, breakeven calculations, volume-profit tradeoffs, and tactical pricing decisions.",
    business_synonyms: ["Variable Margin %", "Contribution %", "CM %", "Variable Profit %", "Direct Margin %", "Contribution Rate"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

// ========================================
// COMPETITIVE PRICING MEASURES
// ========================================

(competitor_price:Column {
    column_id: "COL_COMPETITOR_PRICE_FACT_5018",
    column_name: "COMPETITOR_PRICE",
    table_id: "FACT_PRICING",
    ordinal_position: 18,
    business_name: "Competitor Price",
    business_description: "Key competitor's price for comparable product. Essential for CPG competitive intelligence, price positioning, market pricing dynamics, and competitive response strategies.",
    business_synonyms: ["Competitive Price", "Rival Price", "Market Price", "Comp Price", "Benchmark Price", "Competition Price"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 4,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(price_index_vs_competition:Column {
    column_id: "COL_PRICE_INDEX_VS_COMP_FACT_5019",
    column_name: "PRICE_INDEX_VS_COMPETITION",
    table_id: "FACT_PRICING",
    ordinal_position: 19,
    business_name: "Price Index vs Competition",
    business_description: "Price index relative to key competitors (100 = parity). Critical for CPG price positioning assessment, competitive gaps, premium/value positioning, and pricing strategy validation.",
    business_synonyms: ["Competitive Index", "Price Position Index", "Relative Price Index", "Competition Index", "Price Ratio", "Competitive Position"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(price_gap_amount:Column {
    column_id: "COL_PRICE_GAP_AMOUNT_FACT_5020",
    column_name: "PRICE_GAP_AMOUNT",
    table_id: "FACT_PRICING",
    ordinal_position: 20,
    business_name: "Price Gap Amount",
    business_description: "Dollar difference between our price and competitor price. Important for CPG competitive strategy, price matching decisions, value perception management, and tactical pricing moves.",
    business_synonyms: ["Price Difference", "Competitive Gap", "Price Delta", "Price Spread", "Gap to Competition", "Price Variance"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 4,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(market_price_average:Column {
    column_id: "COL_MARKET_PRICE_AVERAGE_FACT_5021",
    column_name: "MARKET_PRICE_AVERAGE",
    table_id: "FACT_PRICING",
    ordinal_position: 21,
    business_name: "Market Price Average",
    business_description: "Average market price across all competitors. Essential for CPG market positioning, price benchmarking, category pricing trends, and understanding price dynamics.",
    business_synonyms: ["Market Average", "Category Average Price", "Market Price", "Industry Average", "Average Competitor Price", "Market Benchmark"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 4,
    business_criticality: "high",
    additive_type: "non_additive"
}),

// ========================================
// PRICE ELASTICITY AND OPTIMIZATION
// ========================================

(price_elasticity_coefficient:Column {
    column_id: "COL_PRICE_ELASTICITY_COEF_FACT_5022",
    column_name: "PRICE_ELASTICITY_COEFFICIENT",
    table_id: "FACT_PRICING",
    ordinal_position: 22,
    business_name: "Price Elasticity Coefficient",
    business_description: "Measure of demand sensitivity to price changes. Critical for CPG optimal pricing, revenue maximization, volume-value tradeoffs, and understanding consumer price sensitivity.",
    business_synonyms: ["Elasticity", "Price Sensitivity", "Demand Elasticity", "Price Response", "Elasticity Factor", "Sensitivity Coefficient"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 3,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(optimal_price_point:Column {
    column_id: "COL_OPTIMAL_PRICE_POINT_FACT_5023",
    column_name: "OPTIMAL_PRICE_POINT",
    table_id: "FACT_PRICING",
    ordinal_position: 23,
    business_name: "Optimal Price Point",
    business_description: "Analytically derived optimal price for revenue or profit maximization. Important for CPG pricing science, optimization recommendations, strategic pricing, and maximizing financial performance.",
    business_synonyms: ["Optimized Price", "Best Price", "Revenue Maximizing Price", "Optimal Price", "Target Price", "Recommended Price"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 4,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(price_tier:Column {
    column_id: "COL_PRICE_TIER_FACT_5024",
    column_name: "PRICE_TIER",
    table_id: "FACT_PRICING",
    ordinal_position: 24,
    business_name: "Price Tier",
    business_description: "Price tier classification (Premium, Mid-tier, Value, etc.). Critical for CPG portfolio strategy, price architecture, consumer segmentation, and market positioning.",
    business_synonyms: ["Price Level", "Price Segment", "Price Category", "Tier Classification", "Price Band", "Price Range"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(price_confidence_score:Column {
    column_id: "COL_PRICE_CONFIDENCE_SCORE_FACT_5025",
    column_name: "PRICE_CONFIDENCE_SCORE",
    table_id: "FACT_PRICING",
    ordinal_position: 25,
    business_name: "Price Confidence Score",
    business_description: "Confidence score in pricing recommendation or optimization (0-100). Essential for CPG pricing decision support, risk assessment, model reliability, and implementation confidence.",
    business_synonyms: ["Confidence Level", "Price Certainty", "Optimization Confidence", "Reliability Score", "Confidence %", "Certainty Score"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

// ========================================
// CHANNEL AND CUSTOMER PRICING
// ========================================

(wholesale_price:Column {
    column_id: "COL_WHOLESALE_PRICE_FACT_5026",
    column_name: "WHOLESALE_PRICE",
    table_id: "FACT_PRICING",
    ordinal_position: 26,
    business_name: "Wholesale Price",
    business_description: "Price charged to retailers or distributors. Important for CPG channel pricing, trade margins, distributor economics, and understanding channel profitability.",
    business_synonyms: ["Trade Price", "Distributor Price", "B2B Price", "Channel Price", "Dealer Price", "Reseller Price"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 4,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(contract_price:Column {
    column_id: "COL_CONTRACT_PRICE_FACT_5027",
    column_name: "CONTRACT_PRICE",
    table_id: "FACT_PRICING",
    ordinal_position: 27,
    business_name: "Contract Price",
    business_description: "Negotiated contract price for specific customers or channels. Critical for CPG B2B relationships, contract management, customer profitability, and price agreement tracking.",
    business_synonyms: ["Negotiated Price", "Agreement Price", "Customer Price", "Special Price", "Account Price", "Contracted Rate"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 4,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(channel_margin_percentage:Column {
    column_id: "COL_CHANNEL_MARGIN_PERCENTAGE_FACT_5028",
    column_name: "CHANNEL_MARGIN_PERCENTAGE",
    table_id: "FACT_PRICING",
    ordinal_position: 28,
    business_name: "Channel Margin Percentage",
    business_description: "Margin percentage retained by retail channel. Essential for CPG channel economics, retailer profitability, margin sharing, and channel partnership health.",
    business_synonyms: ["Retailer Margin", "Channel Markup", "Trade Margin", "Dealer Margin", "Distributor Margin", "Channel %"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

// ========================================
// DOMAIN-SPECIFIC PRICING - ALCOHOLIC BEVERAGES
// ========================================

(excise_tax_amount:Column {
    column_id: "COL_EXCISE_TAX_AMOUNT_FACT_5029",
    column_name: "EXCISE_TAX_AMOUNT",
    table_id: "FACT_PRICING",
    ordinal_position: 29,
    business_name: "Excise Tax Amount",
    business_description: "Federal and state excise taxes included in price. Critical for alcoholic beverage pricing compliance, tax calculation, pricing transparency, and regulatory reporting.",
    business_synonyms: ["Alcohol Tax", "Sin Tax", "Excise Duty", "Federal Tax", "State Tax", "Alcohol Duty"],
    applicable_domains: ["alcoholic_beverages"],
    domain_specific: true,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 4,
    business_criticality: "critical",
    additive_type: "additive",
    regulatory_relevance: ["TTB", "STATE_TAX_AUTHORITY"]
}),

(minimum_advertised_price:Column {
    column_id: "COL_MINIMUM_ADVERTISED_PRICE_FACT_5030",
    column_name: "MINIMUM_ADVERTISED_PRICE",
    table_id: "FACT_PRICING",
    ordinal_position: 30,
    business_name: "Minimum Advertised Price",
    business_description: "Minimum price allowed for advertising in alcoholic beverage regulations. Essential for compliance with state minimum pricing laws, MAP policies, and regulatory adherence.",
    business_synonyms: ["MAP", "Floor Price", "Minimum Price", "Regulatory Minimum", "Legal Minimum", "Price Floor"],
    applicable_domains: ["alcoholic_beverages"],
    domain_specific: true,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 4,
    business_criticality: "high",
    additive_type: "non_additive",
    regulatory_relevance: ["STATE_LIQUOR_BOARDS"]
}),

(post_off_discount:Column {
    column_id: "COL_POST_OFF_DISCOUNT_FACT_5031",
    column_name: "POST_OFF_DISCOUNT",
    table_id: "FACT_PRICING",
    ordinal_position: 31,
    business_name: "Post-Off Discount",
    business_description: "Distributor post-off discount amount for alcoholic beverages. Important for three-tier system pricing, distributor margins, effective pricing, and channel economics.",
    business_synonyms: ["Distributor Discount", "Post-Off", "Trade Discount", "Wholesale Discount", "Distribution Discount", "Channel Discount"],
    applicable_domains: ["alcoholic_beverages"],
    domain_specific: true,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 4,
    business_criticality: "high",
    additive_type: "additive"
}),

// ========================================
// DOMAIN-SPECIFIC PRICING - PHARMACEUTICALS
// ========================================

(wholesale_acquisition_cost:Column {
    column_id: "COL_WHOLESALE_ACQUISITION_COST_FACT_5032",
    column_name: "WHOLESALE_ACQUISITION_COST",
    table_id: "FACT_PRICING",
    ordinal_position: 32,
    business_name: "Wholesale Acquisition Cost",
    business_description: "WAC price for pharmaceutical products. Critical for pharmaceutical pricing transparency, reimbursement calculations, formulary decisions, and regulatory compliance.",
    business_synonyms: ["WAC", "List Price", "Pharma List Price", "Drug Price", "Acquisition Cost", "WAC Price"],
    applicable_domains: ["pharmaceuticals"],
    domain_specific: true,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 4,
    business_criticality: "critical",
    additive_type: "non_additive",
    regulatory_relevance: ["FDA", "CMS"]
}),

(rebate_amount:Column {
    column_id: "COL_REBATE_AMOUNT_FACT_5033",
    column_name: "REBATE_AMOUNT",
    table_id: "FACT_PRICING",
    ordinal_position: 33,
    business_name: "Rebate Amount",
    business_description: "Pharmaceutical rebates to PBMs, payers, or government programs. Essential for net pricing, formulary negotiations, government pricing programs, and true revenue realization.",
    business_synonyms: ["Pharma Rebate", "PBM Rebate", "Payer Rebate", "Drug Rebate", "Formulary Rebate", "Government Rebate"],
    applicable_domains: ["pharmaceuticals", "health_supplements"],
    domain_specific: true,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 4,
    business_criticality: "critical",
    additive_type: "additive"
}),

(copay_amount:Column {
    column_id: "COL_COPAY_AMOUNT_FACT_5034",
    column_name: "COPAY_AMOUNT",
    table_id: "FACT_PRICING",
    ordinal_position: 34,
    business_name: "Patient Copay Amount",
    business_description: "Patient out-of-pocket copayment amount. Important for pharmaceutical patient affordability, access programs, copay assistance effectiveness, and patient burden assessment.",
    business_synonyms: ["Patient Copay", "Out of Pocket", "Patient Cost", "Copayment", "Patient Share", "OOP Cost"],
    applicable_domains: ["pharmaceuticals"],
    domain_specific: true,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

// ========================================
// PRICE CHANGE AND TIMING MEASURES
// ========================================

(price_change_amount:Column {
    column_id: "COL_PRICE_CHANGE_AMOUNT_FACT_5035",
    column_name: "PRICE_CHANGE_AMOUNT",
    table_id: "FACT_PRICING",
    ordinal_position: 35,
    business_name: "Price Change Amount",
    business_description: "Dollar amount of price change from previous price. Critical for CPG price increase tracking, inflation management, price stability monitoring, and change impact analysis.",
    business_synonyms: ["Price Increase", "Price Movement", "Price Delta", "Change Amount", "Price Adjustment", "Price Shift"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 4,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(price_change_percentage:Column {
    column_id: "COL_PRICE_CHANGE_PERCENTAGE_FACT_5036",
    column_name: "PRICE_CHANGE_PERCENTAGE",
    table_id: "FACT_PRICING",
    ordinal_position: 36,
    business_name: "Price Change Percentage",
    business_description: "Percentage change from previous price. Essential for CPG inflation tracking, price increase analysis, competitive moves, and understanding pricing dynamics.",
    business_synonyms: ["Price Increase %", "Price Change Rate", "Inflation Rate", "Price Growth", "Change Percentage", "Price Movement %"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(days_at_current_price:Column {
    column_id: "COL_DAYS_AT_CURRENT_PRICE_FACT_5037",
    column_name: "DAYS_AT_CURRENT_PRICE",
    table_id: "FACT_PRICING",
    ordinal_position: 37,
    business_name: "Days at Current Price",
    business_description: "Number of days product has been at current price. Important for CPG price stability, promotional cadence, price change frequency, and consumer price perception.",
    business_synonyms: ["Price Duration", "Price Stability", "Days Since Change", "Price Age", "Current Price Days", "Price Period"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

// ========================================
// BUNDLE AND MULTI-PACK PRICING
// ========================================

(bundle_price:Column {
    column_id: "COL_BUNDLE_PRICE_FACT_5038",
    column_name: "BUNDLE_PRICE",
    table_id: "FACT_PRICING",
    ordinal_position: 38,
    business_name: "Bundle Price",
    business_description: "Price for bundled products or multi-packs. Critical for CPG bundle strategy, value perception, basket building, and multi-unit purchase incentives.",
    business_synonyms: ["Multi-pack Price", "Bundle Cost", "Package Price", "Combo Price", "Set Price", "Bundle Value"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 4,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(unit_price_in_bundle:Column {
    column_id: "COL_UNIT_PRICE_IN_BUNDLE_FACT_5039",
    column_name: "UNIT_PRICE_IN_BUNDLE",
    table_id: "FACT_PRICING",
    ordinal_position: 39,
    business_name: "Unit Price in Bundle",
    business_description: "Effective price per unit when purchased in bundle. Essential for CPG value communication, bundle savings calculation, volume incentives, and price-per-unit comparisons.",
    business_synonyms: ["Bundle Unit Price", "Effective Unit Price", "Multi-pack Unit Price", "Per Unit Bundle Price", "Bundle Unit Cost", "Package Unit Price"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 4,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(bundle_savings_percentage:Column {
    column_id: "COL_BUNDLE_SAVINGS_PERCENTAGE_FACT_5040",
    column_name: "BUNDLE_SAVINGS_PERCENTAGE",
    table_id: "FACT_PRICING",
    ordinal_position: 40,
    business_name: "Bundle Savings Percentage",
    business_description: "Percentage savings when purchasing bundle vs individual units. Important for CPG value proposition, consumer incentives, promotional messaging, and bundle effectiveness.",
    business_synonyms: ["Bundle Discount", "Multi-pack Savings", "Bundle Value %", "Package Savings", "Bundle Benefit", "Savings Rate"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

// ========================================
// GEOGRAPHIC AND ZONE PRICING
// ========================================

(zone_price:Column {
    column_id: "COL_ZONE_PRICE_FACT_5041",
    column_name: "ZONE_PRICE",
    table_id: "FACT_PRICING",
    ordinal_position: 41,
    business_name: "Zone Price",
    business_description: "Geographic zone-specific pricing. Critical for CPG regional strategies, market-based pricing, cost-to-serve differences, and local competitive dynamics.",
    business_synonyms: ["Regional Price", "Geographic Price", "Area Price", "Market Price", "Local Price", "Territory Price"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 4,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(zone_price_variance:Column {
    column_id: "COL_ZONE_PRICE_VARIANCE_FACT_5042",
    column_name: "ZONE_PRICE_VARIANCE",
    table_id: "FACT_PRICING",
    ordinal_position: 42,
    business_name: "Zone Price Variance",
    business_description: "Variance from national or standard price by zone. Essential for CPG regional price management, variance control, compliance monitoring, and price harmonization.",
    business_synonyms: ["Regional Variance", "Geographic Variance", "Zone Difference", "Price Deviation", "Regional Gap", "Zone Delta"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 4,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(urban_rural_price_index:Column {
    column_id: "COL_URBAN_RURAL_PRICE_INDEX_FACT_5043",
    column_name: "URBAN_RURAL_PRICE_INDEX",
    table_id: "FACT_PRICING",
    ordinal_position: 43,
    business_name: "Urban/Rural Price Index",
    business_description: "Price index comparing urban vs rural pricing. Important for CPG market-specific strategies, demographic pricing, distribution cost recovery, and market development.",
    business_synonyms: ["Market Type Index", "Urban Rural Index", "Geographic Index", "Market Price Index", "Location Type Index", "Area Type Index"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 2,
    business_criticality: "low",
    additive_type: "non_additive"
}),

// ========================================
// PRICING COMPLIANCE AND VIOLATIONS
// ========================================

(map_violation_flag:Column {
    column_id: "COL_MAP_VIOLATION_FLAG_FACT_5044",
    column_name: "MAP_VIOLATION_FLAG",
    table_id: "FACT_PRICING",
    ordinal_position: 44,
    business_name: "MAP Violation Flag",
    business_description: "Indicates violation of Minimum Advertised Price policy. Critical for CPG brand protection, channel compliance, MAP enforcement, and maintaining pricing integrity.",
    business_synonyms: ["MAP Breach", "Price Violation", "Policy Violation", "MAP Non-Compliance", "Pricing Violation", "Policy Breach"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(price_compliance_score:Column {
    column_id: "COL_PRICE_COMPLIANCE_SCORE_FACT_5045",
    column_name: "PRICE_COMPLIANCE_SCORE",
    table_id: "FACT_PRICING",
    ordinal_position: 45,
    business_name: "Price Compliance Score",
    business_description: "Score measuring adherence to pricing policies (0-100). Essential for CPG channel management, compliance monitoring, partner scorecards, and pricing discipline.",
    business_synonyms: ["Compliance Rate", "Adherence Score", "Policy Compliance", "Pricing Discipline", "Compliance %", "Policy Score"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(unauthorized_discount_flag:Column {
    column_id: "COL_UNAUTHORIZED_DISCOUNT_FLAG_FACT_5046",
    column_name: "UNAUTHORIZED_DISCOUNT_FLAG",
    table_id: "FACT_PRICING",
    ordinal_position: 46,
    business_name: "Unauthorized Discount Flag",
    business_description: "Indicates unauthorized discounting by channel partner. Important for CPG channel control, margin protection, brand value preservation, and partner compliance.",
    business_synonyms: ["Unauthorized Price", "Rogue Discount", "Unapproved Discount", "Channel Violation", "Discount Violation", "Price Breach"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

// ========================================
// DYNAMIC AND PERSONALIZED PRICING
// ========================================

(dynamic_price_flag:Column {
    column_id: "COL_DYNAMIC_PRICE_FLAG_FACT_5047",
    column_name: "DYNAMIC_PRICE_FLAG",
    table_id: "FACT_PRICING",
    ordinal_position: 47,
    business_name: "Dynamic Price Flag",
    business_description: "Indicates price was dynamically adjusted based on algorithms. Critical for CPG pricing innovation, real-time optimization, competitive response, and revenue management.",
    business_synonyms: ["Algorithm Price", "Dynamic Pricing", "Real-time Price", "Automated Price", "Smart Price", "AI Price"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(personalized_price:Column {
    column_id: "COL_PERSONALIZED_PRICE_FACT_5048",
    column_name: "PERSONALIZED_PRICE",
    table_id: "FACT_PRICING",
    ordinal_position: 48,
    business_name: "Personalized Price",
    business_description: "Customer-specific personalized price. Essential for CPG one-to-one marketing, loyalty rewards, targeted offers, and maximizing customer lifetime value.",
    business_synonyms: ["Individual Price", "Custom Price", "Targeted Price", "Personal Offer", "Customer-Specific Price", "1:1 Price"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 4,
    business_criticality: "low",
    additive_type: "non_additive"
}),

(loyalty_price:Column {
    column_id: "COL_LOYALTY_PRICE_FACT_5049",
    column_name: "LOYALTY_PRICE",
    table_id: "FACT_PRICING",
    ordinal_position: 49,
    business_name: "Loyalty Member Price",
    business_description: "Special price for loyalty program members. Important for CPG loyalty program value, member benefits, retention incentives, and program effectiveness measurement.",
    business_synonyms: ["Member Price", "Loyalty Discount Price", "Program Price", "Member Rate", "Loyalty Rate", "VIP Price"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 4,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

// ========================================
// PRICING STRATEGY AND PERFORMANCE
// ========================================

(pricing_strategy_type:Column {
    column_id: "COL_PRICING_STRATEGY_TYPE_FACT_5050",
    column_name: "PRICING_STRATEGY_TYPE",
    table_id: "FACT_PRICING",
    ordinal_position: 50,
    business_name: "Pricing Strategy Type",
    business_description: "Type of pricing strategy employed (Penetration, Skimming, Competitive, Value, etc.). Critical for CPG strategic alignment, performance measurement, strategy effectiveness, and portfolio management.",
    business_synonyms: ["Price Strategy", "Pricing Approach", "Strategy Type", "Pricing Method", "Price Tactic", "Pricing Model"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(price_performance_index:Column {
    column_id: "COL_PRICE_PERFORMANCE_INDEX_FACT_5051",
    column_name: "PRICE_PERFORMANCE_INDEX",
    table_id: "FACT_PRICING",
    ordinal_position: 51,
    business_name: "Price Performance Index",
    business_description: "Index measuring pricing effectiveness vs objectives (100 = target). Essential for CPG pricing scorecard, performance tracking, strategy validation, and continuous improvement.",
    business_synonyms: ["Performance Score", "Pricing Effectiveness", "Price Success Index", "Performance Metric", "Effectiveness Index", "Success Rate"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

// ========================================
// METADATA AND AUDIT INFORMATION
// ========================================

(price_approval_status:Column {
    column_id: "COL_PRICE_APPROVAL_STATUS_FACT_5052",
    column_name: "PRICE_APPROVAL_STATUS",
    table_id: "FACT_PRICING",
    ordinal_position: 52,
    business_name: "Price Approval Status",
    business_description: "Approval status of pricing decision (Pending, Approved, Rejected). Important for CPG pricing governance, approval workflows, compliance tracking, and audit trails.",
    business_synonyms: ["Approval State", "Price Status", "Authorization Status", "Approval Flag", "Price State", "Review Status"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(pricing_analyst:Column {
    column_id: "COL_PRICING_ANALYST_FACT_5053",
    column_name: "PRICING_ANALYST",
    table_id: "FACT_PRICING",
    ordinal_position: 53,
    business_name: "Pricing Analyst",
    business_description: "Analyst responsible for pricing decision or recommendation. Essential for CPG accountability, performance tracking, best practice identification, and expertise development.",
    business_synonyms: ["Price Manager", "Pricing Owner", "Analyst Name", "Price Setter", "Pricing Lead", "Responsible Analyst"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "low",
    additive_type: "non_additive"
}),

(price_effective_date:Column {
    column_id: "COL_PRICE_EFFECTIVE_DATE_FACT_5054",
    column_name: "PRICE_EFFECTIVE_DATE",
    table_id: "FACT_PRICING",
    ordinal_position: 54,
    business_name: "Price Effective Date",
    business_description: "Date when price becomes effective. Critical for CPG price change management, system updates, communication timing, and ensuring price consistency across channels.",
    business_synonyms: ["Effective Date", "Start Date", "Implementation Date", "Active Date", "Launch Date", "Go-Live Date"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "DATE",
    max_length: null,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(record_created_timestamp:Column {
    column_id: "COL_RECORD_CREATED_TIMESTAMP_FACT_5055",
    column_name: "RECORD_CREATED_TIMESTAMP",
    table_id: "FACT_PRICING",
    ordinal_position: 55,
    business_name: "Record Created Timestamp",
    business_description: "System timestamp when pricing record was created in data warehouse. Used for audit trails, data lineage tracking, and data governance. Immutable after creation. Part of comprehensive data governance framework.",
    business_synonyms: ["Creation Time", "Insert Timestamp", "Created Date", "Record Creation", "Entry Timestamp", "Load Timestamp"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    max_length: null,
    business_criticality: "low",
    additive_type: "non_additive"
}),

(record_modified_timestamp:Column {
    column_id: "COL_RECORD_MODIFIED_TIMESTAMP_FACT_5056",
    column_name: "RECORD_MODIFIED_TIMESTAMP",
    table_id: "FACT_PRICING",
    ordinal_position: 56,
    business_name: "Record Last Modified Timestamp",
    business_description: "Most recent update timestamp for pricing record. Tracks data freshness, change frequency, and maintenance activities. Updated automatically with any field modification for data governance compliance.",
    business_synonyms: ["Update Time", "Last Modified", "Change Timestamp", "Modified Date", "Last Updated", "Revision Timestamp"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    max_length: null,
    business_criticality: "low",
    additive_type: "non_additive"
});

// ========================================
// CREATE TABLE-COLUMN RELATIONSHIPS
// ========================================

MATCH (t:Table {table_id: "FACT_PRICING"})
MATCH (c:Column {table_id: "FACT_PRICING"})
CREATE (t)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"]
}]->(c);