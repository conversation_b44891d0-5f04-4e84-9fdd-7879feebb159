// ========================================
// FACT_ECOMMERCE_SALES - COMPREHENSIVE COLUMN CREATION
// Complete e-commerce sales fact table columns for all CPG domains
// ========================================

// Clear existing columns for FACT_ECOMMERCE_SALES (optional)
MATCH (c:Column {table_id: "FACT_ECOMMERCE_SALES"}) DETACH DELETE c;

// ========================================
// CORE E-COMMERCE IDENTIFIERS AND FOREIGN KEYS
// ========================================

CREATE 
(ecommerce_transaction_id:Column {
    column_id: "COL_ECOMMERCE_TRANSACTION_ID_FACT_4001",
    column_name: "ECOMMERCE_TRANSACTION_ID",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 1,
    business_name: "E-Commerce Transaction ID",
    business_description: "Unique identifier for online sales transactions across all digital channels and CPG categories. Critical for order tracking, customer service, returns processing, and managing digital sales across alcoholic beverages, pharmaceuticals, toys, cosmetics, and all consumer product categories.",
    business_synonyms: ["Order ID", "Transaction Key", "Online Order ID", "Digital Transaction ID", "Web Order Number", "E-Commerce Order Key", "Online Sale ID"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_primary_key: true,
    business_criticality: "critical"
}),

(date_key:Column {
    column_id: "COL_DATE_KEY_FACT_4002",
    column_name: "DATE_KEY",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 2,
    business_name: "Date Key",
    business_description: "Foreign key to date dimension for temporal e-commerce analysis and digital sales tracking. Essential for CPG online trend analysis, seasonal e-commerce patterns, digital growth monitoring, and understanding online shopping behavior over time across all product categories.",
    business_synonyms: ["Order Date Key", "Transaction Date Key", "Date FK", "Date Reference", "Temporal Key", "Sales Date Key"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "INTEGER",
    max_length: null,
    is_foreign_key: true,
    business_criticality: "critical"
}),

(customer_key:Column {
    column_id: "COL_CUSTOMER_KEY_FACT_4003",
    column_name: "CUSTOMER_KEY",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 3,
    business_name: "Customer Key",
    business_description: "Foreign key to customer dimension for digital customer tracking and personalization. Critical for CPG customer lifetime value, personalization strategies, retention analysis, and understanding individual shopping patterns in digital channels.",
    business_synonyms: ["Digital Customer Key", "Online Customer FK", "Shopper Key", "Customer Reference", "User Key", "Account Key"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_foreign_key: true,
    business_criticality: "critical"
}),

(product_hierarchy_key:Column {
    column_id: "COL_PRODUCT_HIERARCHY_KEY_FACT_4004",
    column_name: "PRODUCT_HIERARCHY_KEY",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 4,
    business_name: "Product Hierarchy Key",
    business_description: "Foreign key to product hierarchy dimension for SKU-level e-commerce tracking. Essential for CPG digital product performance, online assortment optimization, category e-commerce trends, and brand digital strategy.",
    business_synonyms: ["Product Key", "SKU Key", "Product FK", "Item Key", "Product Reference", "Digital SKU Key"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 50,
    is_foreign_key: true,
    business_criticality: "critical"
}),

(session_id:Column {
    column_id: "COL_SESSION_ID_FACT_4005",
    column_name: "SESSION_ID",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 5,
    business_name: "Session ID",
    business_description: "Unique identifier for customer browsing session. Important for CPG digital behavior analysis, conversion funnel tracking, cart abandonment analysis, and understanding online shopping journeys.",
    business_synonyms: ["Web Session", "Visit ID", "Session Key", "Browse Session", "User Session", "Digital Session"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "identifier",
    data_type: "VARCHAR",
    max_length: 100,
    is_foreign_key: false,
    business_criticality: "high"
}),

// ========================================
// CORE SALES MEASURES
// ========================================

(gross_sales_amount:Column {
    column_id: "COL_GROSS_SALES_AMOUNT_FACT_4006",
    column_name: "GROSS_SALES_AMOUNT",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 6,
    business_name: "Gross Sales Amount",
    business_description: "Total sales value before discounts, returns, and taxes. Critical for CPG e-commerce revenue tracking, digital growth measurement, online market share analysis, and understanding total digital demand.",
    business_synonyms: ["Gross Revenue", "Total Sales", "Gross Amount", "Pre-Discount Sales", "Total Revenue", "Gross Value"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 2,
    business_criticality: "critical",
    additive_type: "additive"
}),

(net_sales_amount:Column {
    column_id: "COL_NET_SALES_AMOUNT_FACT_4007",
    column_name: "NET_SALES_AMOUNT",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 7,
    business_name: "Net Sales Amount",
    business_description: "Sales amount after discounts and promotions but before returns. Essential for CPG e-commerce profitability, true revenue tracking, margin analysis, and understanding effective digital pricing.",
    business_synonyms: ["Net Revenue", "Actual Sales", "Net Amount", "Effective Sales", "Final Sales", "Net Value"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 15,
    scale: 2,
    business_criticality: "critical",
    additive_type: "additive"
}),

(quantity_sold:Column {
    column_id: "COL_QUANTITY_SOLD_FACT_4008",
    column_name: "QUANTITY_SOLD",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 8,
    business_name: "Quantity Sold",
    business_description: "Number of units sold in e-commerce transaction. Fundamental for CPG online volume tracking, digital velocity analysis, inventory planning, and understanding product movement in digital channels.",
    business_synonyms: ["Units Sold", "Sales Quantity", "Order Quantity", "Units", "Volume Sold", "Item Count"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 3,
    business_criticality: "critical",
    additive_type: "additive"
}),

(unit_price:Column {
    column_id: "COL_UNIT_PRICE_FACT_4009",
    column_name: "UNIT_PRICE",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 9,
    business_name: "Unit Price",
    business_description: "Price per unit at time of sale. Important for CPG price realization tracking, competitive pricing analysis, price elasticity measurement, and digital pricing strategy effectiveness.",
    business_synonyms: ["Sales Price", "Per Unit Price", "Item Price", "Product Price", "Selling Price", "Unit Rate"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 4,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(discount_amount:Column {
    column_id: "COL_DISCOUNT_AMOUNT_FACT_4010",
    column_name: "DISCOUNT_AMOUNT",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 10,
    business_name: "Discount Amount",
    business_description: "Total discount applied to transaction including coupons and promotions. Critical for CPG e-commerce margin impact, promotional effectiveness, price optimization, and understanding digital discount strategies.",
    business_synonyms: ["Promo Amount", "Coupon Value", "Price Reduction", "Discount Value", "Savings Amount", "Promotion Value"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 2,
    business_criticality: "high",
    additive_type: "additive"
}),

// ========================================
// DIGITAL COMMERCE METRICS
// ========================================

(cart_value:Column {
    column_id: "COL_CART_VALUE_FACT_4011",
    column_name: "CART_VALUE",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 11,
    business_name: "Shopping Cart Value",
    business_description: "Total value of all items in shopping cart at checkout. Essential for CPG basket analysis, cross-sell effectiveness, average order value optimization, and understanding digital shopping behavior.",
    business_synonyms: ["Basket Value", "Cart Total", "Order Value", "Basket Size", "Cart Amount", "Total Cart Value"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 2,
    business_criticality: "high",
    additive_type: "additive"
}),

(items_per_order:Column {
    column_id: "COL_ITEMS_PER_ORDER_FACT_4012",
    column_name: "ITEMS_PER_ORDER",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 12,
    business_name: "Items Per Order",
    business_description: "Number of distinct items in e-commerce order. Important for CPG basket building strategies, bundling effectiveness, order complexity analysis, and fulfillment optimization.",
    business_synonyms: ["Line Items", "Order Lines", "SKU Count", "Product Count", "Basket Items", "Order Size"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(conversion_rate:Column {
    column_id: "COL_CONVERSION_RATE_FACT_4013",
    column_name: "CONVERSION_RATE",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 13,
    business_name: "Conversion Rate",
    business_description: "Percentage of sessions resulting in purchase. Critical for CPG e-commerce optimization, funnel effectiveness, user experience quality, and digital marketing ROI measurement.",
    business_synonyms: ["Purchase Rate", "Buy Rate", "Session Conversion", "Sales Conversion", "Close Rate", "Success Rate"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "critical",
    additive_type: "non_additive"
}),

(cart_abandonment_flag:Column {
    column_id: "COL_CART_ABANDONMENT_FLAG_FACT_4014",
    column_name: "CART_ABANDONMENT_FLAG",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 14,
    business_name: "Cart Abandonment Flag",
    business_description: "Indicates if customer abandoned cart before completing purchase. Essential for CPG conversion optimization, recovery campaigns, checkout friction analysis, and lost revenue quantification.",
    business_synonyms: ["Abandoned Cart", "Incomplete Purchase", "Cart Drop", "Checkout Abandonment", "Lost Sale", "Abandoned Order"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "high",
    additive_type: "non_additive"
}),

// ========================================
// CUSTOMER BEHAVIOR AND CHANNEL METRICS
// ========================================

(new_customer_flag:Column {
    column_id: "COL_NEW_CUSTOMER_FLAG_FACT_4015",
    column_name: "NEW_CUSTOMER_FLAG",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 15,
    business_name: "New Customer Flag",
    business_description: "Indicates if transaction is from first-time online customer. Critical for CPG customer acquisition tracking, new buyer experience optimization, onboarding effectiveness, and growth metrics.",
    business_synonyms: ["First Time Buyer", "New Buyer", "First Purchase", "New Account", "Acquisition Flag", "New User"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(returning_customer_flag:Column {
    column_id: "COL_RETURNING_CUSTOMER_FLAG_FACT_4016",
    column_name: "RETURNING_CUSTOMER_FLAG",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 16,
    business_name: "Returning Customer Flag",
    business_description: "Indicates repeat purchase from existing customer. Essential for CPG loyalty measurement, retention effectiveness, lifetime value tracking, and repeat purchase behavior analysis.",
    business_synonyms: ["Repeat Customer", "Loyal Customer", "Return Buyer", "Existing Customer", "Repeat Purchase", "Retention Flag"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(device_type:Column {
    column_id: "COL_DEVICE_TYPE_FACT_4017",
    column_name: "DEVICE_TYPE",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 17,
    business_name: "Device Type",
    business_description: "Type of device used for purchase (Mobile, Desktop, Tablet, App). Important for CPG mobile commerce strategies, responsive design optimization, channel preferences, and technology adoption tracking.",
    business_synonyms: ["Platform Type", "Access Device", "Shopping Device", "Device Category", "Platform", "Device Channel"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 30,
    business_criticality: "high"
}),

(traffic_source:Column {
    column_id: "COL_TRAFFIC_SOURCE_FACT_4018",
    column_name: "TRAFFIC_SOURCE",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 18,
    business_name: "Traffic Source",
    business_description: "Source driving customer to e-commerce site (Organic, Paid, Social, Direct, Email). Critical for CPG marketing attribution, channel ROI analysis, acquisition cost optimization, and marketing mix decisions.",
    business_synonyms: ["Referral Source", "Acquisition Channel", "Marketing Source", "Traffic Origin", "Lead Source", "Channel Source"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high"
}),

// ========================================
// FULFILLMENT AND DELIVERY MEASURES
// ========================================

(shipping_amount:Column {
    column_id: "COL_SHIPPING_AMOUNT_FACT_4019",
    column_name: "SHIPPING_AMOUNT",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 19,
    business_name: "Shipping Amount",
    business_description: "Shipping and handling charges for order. Essential for CPG fulfillment cost analysis, free shipping impact, delivery pricing strategies, and total customer cost tracking.",
    business_synonyms: ["Delivery Fee", "Shipping Cost", "Freight Charge", "Delivery Amount", "Shipping Fee", "Transport Cost"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 2,
    business_criticality: "medium",
    additive_type: "additive"
}),

(delivery_method:Column {
    column_id: "COL_DELIVERY_METHOD_FACT_4020",
    column_name: "DELIVERY_METHOD",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 20,
    business_name: "Delivery Method",
    business_description: "Shipping method selected (Standard, Express, Same-Day, Pickup). Important for CPG fulfillment optimization, customer preferences, cost-service trade-offs, and last-mile strategy.",
    business_synonyms: ["Shipping Method", "Fulfillment Type", "Delivery Type", "Shipping Option", "Delivery Service", "Fulfillment Method"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "medium"
}),

(delivery_time_days:Column {
    column_id: "COL_DELIVERY_TIME_DAYS_FACT_4021",
    column_name: "DELIVERY_TIME_DAYS",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 21,
    business_name: "Delivery Time Days",
    business_description: "Number of days from order to delivery. Critical for CPG customer satisfaction, fulfillment performance, competitive positioning, and supply chain efficiency in e-commerce.",
    business_synonyms: ["Shipping Time", "Fulfillment Days", "Delivery Duration", "Transit Time", "Lead Time", "Shipping Days"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 1,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(click_to_ship_hours:Column {
    column_id: "COL_CLICK_TO_SHIP_HOURS_FACT_4022",
    column_name: "CLICK_TO_SHIP_HOURS",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 22,
    business_name: "Click to Ship Hours",
    business_description: "Hours from order placement to shipment. Essential for CPG operational efficiency, warehouse performance, order processing optimization, and meeting delivery promises.",
    business_synonyms: ["Processing Time", "Fulfillment Speed", "Ship Time", "Order Processing", "Warehouse Time", "Pick Pack Time"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 6,
    scale: 1,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

// ========================================
// PAYMENT AND FINANCIAL MEASURES
// ========================================

(payment_method:Column {
    column_id: "COL_PAYMENT_METHOD_FACT_4023",
    column_name: "PAYMENT_METHOD",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 23,
    business_name: "Payment Method",
    business_description: "Payment type used for transaction (Credit Card, PayPal, BNPL, Gift Card). Important for CPG payment optimization, fraud risk assessment, checkout friction analysis, and financial reconciliation.",
    business_synonyms: ["Payment Type", "Payment Option", "Transaction Method", "Payment Mode", "Pay Method", "Payment Channel"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "high"
}),

(tax_amount:Column {
    column_id: "COL_TAX_AMOUNT_FACT_4024",
    column_name: "TAX_AMOUNT",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 24,
    business_name: "Tax Amount",
    business_description: "Total tax collected on transaction. Critical for CPG tax compliance, multi-jurisdictional selling, net revenue calculations, and financial reporting in e-commerce.",
    business_synonyms: ["Sales Tax", "Tax Collected", "Tax Total", "Tax Revenue", "Tax Charge", "Government Tax"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 2,
    business_criticality: "high",
    additive_type: "additive"
}),

(payment_processing_fee:Column {
    column_id: "COL_PAYMENT_PROCESSING_FEE_FACT_4025",
    column_name: "PAYMENT_PROCESSING_FEE",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 25,
    business_name: "Payment Processing Fee",
    business_description: "Transaction fees charged by payment processors. Essential for CPG e-commerce profitability, cost management, payment method optimization, and understanding true transaction costs.",
    business_synonyms: ["Transaction Fee", "Processing Cost", "Payment Fee", "Card Fee", "Transaction Cost", "Processing Charge"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 8,
    scale: 2,
    business_criticality: "medium",
    additive_type: "additive"
}),

(currency_code:Column {
    column_id: "COL_CURRENCY_CODE_FACT_4026",
    column_name: "CURRENCY_CODE",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 26,
    business_name: "Currency Code",
    business_description: "Currency used for transaction (USD, EUR, GBP, etc.). Important for CPG international e-commerce, currency exposure management, global pricing strategies, and multi-market analysis.",
    business_synonyms: ["Transaction Currency", "Payment Currency", "Currency Type", "Money Type", "Currency Symbol", "Currency"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 3,
    business_criticality: "high"
}),

// ========================================
// CUSTOMER EXPERIENCE MEASURES
// ========================================

(page_views_count:Column {
    column_id: "COL_PAGE_VIEWS_COUNT_FACT_4027",
    column_name: "PAGE_VIEWS_COUNT",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 27,
    business_name: "Page Views Count",
    business_description: "Number of pages viewed in session before purchase. Critical for CPG user experience optimization, content effectiveness, navigation efficiency, and understanding digital shopping paths.",
    business_synonyms: ["Pages Viewed", "Page Count", "Browse Depth", "View Count", "Page Visits", "Navigation Count"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "medium",
    additive_type: "additive"
}),

(session_duration_minutes:Column {
    column_id: "COL_SESSION_DURATION_MINUTES_FACT_4028",
    column_name: "SESSION_DURATION_MINUTES",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 28,
    business_name: "Session Duration Minutes",
    business_description: "Time spent on site during shopping session. Essential for CPG engagement measurement, user experience quality, content effectiveness, and identifying friction points in digital journey.",
    business_synonyms: ["Visit Duration", "Time on Site", "Session Time", "Browse Time", "Engagement Time", "Visit Length"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 8,
    scale: 2,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(search_terms_used:Column {
    column_id: "COL_SEARCH_TERMS_USED_FACT_4029",
    column_name: "SEARCH_TERMS_USED",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 29,
    business_name: "Search Terms Used",
    business_description: "Keywords used in site search leading to purchase. Important for CPG SEO optimization, product discovery improvement, search effectiveness, and understanding customer intent.",
    business_synonyms: ["Search Keywords", "Search Query", "Search Terms", "Keywords", "Search Phrase", "Query Terms"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 500,
    business_criticality: "medium"
}),

// ========================================
// DOMAIN-SPECIFIC E-COMMERCE - ALCOHOLIC BEVERAGES
// ========================================

(age_verification_flag:Column {
    column_id: "COL_AGE_VERIFICATION_FLAG_FACT_4030",
    column_name: "AGE_VERIFICATION_FLAG",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 30,
    business_name: "Age Verification Flag",
    business_description: "Indicates successful age verification for alcohol purchase. Critical for alcoholic beverage regulatory compliance, legal requirements, responsible selling practices, and platform liability protection.",
    business_synonyms: ["Age Verified", "Age Check", "Legal Age", "Verification Status", "Age Confirmed", "ID Verified"],
    applicable_domains: ["alcoholic_beverages"],
    domain_specific: true,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "critical",
    additive_type: "non_additive",
    regulatory_relevance: ["TTB", "STATE_LIQUOR_BOARDS"]
}),

(delivery_signature_required:Column {
    column_id: "COL_DELIVERY_SIGNATURE_REQUIRED_FACT_4031",
    column_name: "DELIVERY_SIGNATURE_REQUIRED",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 31,
    business_name: "Delivery Signature Required",
    business_description: "Indicates adult signature required at delivery. Essential for alcoholic beverage compliance, delivery verification, age confirmation at receipt, and regulatory adherence.",
    business_synonyms: ["Signature Required", "Adult Signature", "ID at Delivery", "Signature Flag", "Delivery Verification", "Age at Delivery"],
    applicable_domains: ["alcoholic_beverages"],
    domain_specific: true,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "critical",
    additive_type: "non_additive",
    regulatory_relevance: ["TTB", "STATE_LIQUOR_BOARDS"]
}),

// ========================================
// DOMAIN-SPECIFIC E-COMMERCE - PHARMACEUTICALS
// ========================================

(prescription_required_flag:Column {
    column_id: "COL_PRESCRIPTION_REQUIRED_FLAG_FACT_4032",
    column_name: "PRESCRIPTION_REQUIRED_FLAG",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 32,
    business_name: "Prescription Required Flag",
    business_description: "Indicates order contains prescription medications. Critical for pharmaceutical regulatory compliance, prescription verification, controlled substance management, and pharmacy operations.",
    business_synonyms: ["Rx Required", "Prescription Flag", "Rx Order", "Prescription Medicine", "Controlled Flag", "Rx Status"],
    applicable_domains: ["pharmaceuticals"],
    domain_specific: true,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "critical",
    additive_type: "non_additive",
    regulatory_relevance: ["FDA", "DEA", "STATE_PHARMACY_BOARDS"]
}),

(pharmacy_verification_status:Column {
    column_id: "COL_PHARMACY_VERIFICATION_STATUS_FACT_4033",
    column_name: "PHARMACY_VERIFICATION_STATUS",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 33,
    business_name: "Pharmacy Verification Status",
    business_description: "Status of prescription and insurance verification. Essential for pharmaceutical order processing, compliance tracking, patient safety, and insurance claim management.",
    business_synonyms: ["Rx Verification", "Pharmacy Status", "Verification State", "Rx Approval", "Pharmacy Check", "Medical Verification"],
    applicable_domains: ["pharmaceuticals"],
    domain_specific: true,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "critical",
    regulatory_relevance: ["FDA", "STATE_PHARMACY_BOARDS"]
}),

(insurance_claim_amount:Column {
    column_id: "COL_INSURANCE_CLAIM_AMOUNT_FACT_4034",
    column_name: "INSURANCE_CLAIM_AMOUNT",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 34,
    business_name: "Insurance Claim Amount",
    business_description: "Amount claimed from insurance for pharmaceutical purchase. Important for pharmaceutical net revenue calculation, patient affordability, payer mix analysis, and reimbursement tracking.",
    business_synonyms: ["Insurance Amount", "Claim Value", "Coverage Amount", "Insurance Payment", "Reimbursement Amount", "Payer Amount"],
    applicable_domains: ["pharmaceuticals"],
    domain_specific: true,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 10,
    scale: 2,
    business_criticality: "high",
    additive_type: "additive"
}),

// ========================================
// SUBSCRIPTION AND RECURRING REVENUE
// ========================================

(subscription_flag:Column {
    column_id: "COL_SUBSCRIPTION_FLAG_FACT_4035",
    column_name: "SUBSCRIPTION_FLAG",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 35,
    business_name: "Subscription Flag",
    business_description: "Indicates order is part of subscription or auto-replenishment program. Critical for CPG recurring revenue, customer retention, predictable demand, and subscription business model tracking.",
    business_synonyms: ["Auto-Ship", "Recurring Order", "Subscribe Save", "Subscription Order", "Auto-Replenish", "Recurring Flag"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(subscription_frequency_days:Column {
    column_id: "COL_SUBSCRIPTION_FREQUENCY_DAYS_FACT_4036",
    column_name: "SUBSCRIPTION_FREQUENCY_DAYS",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 36,
    business_name: "Subscription Frequency Days",
    business_description: "Days between subscription deliveries. Essential for CPG inventory planning, customer lifecycle management, subscription optimization, and predictive fulfillment scheduling.",
    business_synonyms: ["Delivery Frequency", "Subscription Interval", "Replenishment Cycle", "Order Frequency", "Delivery Cycle", "Subscription Period"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(subscription_discount_percentage:Column {
    column_id: "COL_SUBSCRIPTION_DISCOUNT_PCT_FACT_4037",
    column_name: "SUBSCRIPTION_DISCOUNT_PERCENTAGE",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 37,
    business_name: "Subscription Discount Percentage",
    business_description: "Discount percentage applied for subscription orders. Important for CPG subscription economics, price incentive effectiveness, margin impact analysis, and customer value optimization.",
    business_synonyms: ["Subscribe Discount", "Auto-Ship Savings", "Subscription Savings", "Recurring Discount", "Member Discount", "Subscription Rate"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

// ========================================
// MOBILE AND APP COMMERCE
// ========================================

(mobile_app_flag:Column {
    column_id: "COL_MOBILE_APP_FLAG_FACT_4038",
    column_name: "MOBILE_APP_FLAG",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 38,
    business_name: "Mobile App Flag",
    business_description: "Indicates purchase made through mobile application. Critical for CPG app adoption tracking, mobile strategy effectiveness, app-specific features usage, and mobile commerce growth.",
    business_synonyms: ["App Purchase", "Mobile App Order", "App Transaction", "Native App", "App Sale", "Mobile Application"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(app_version:Column {
    column_id: "COL_APP_VERSION_FACT_4039",
    column_name: "APP_VERSION",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 39,
    business_name: "App Version",
    business_description: "Version of mobile app used for purchase. Essential for CPG app performance tracking, feature adoption, version-specific issues, and mobile development prioritization.",
    business_synonyms: ["Application Version", "Mobile Version", "App Build", "Version Number", "App Release", "Software Version"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 20,
    business_criticality: "low"
}),

(push_notification_flag:Column {
    column_id: "COL_PUSH_NOTIFICATION_FLAG_FACT_4040",
    column_name: "PUSH_NOTIFICATION_FLAG",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 40,
    business_name: "Push Notification Flag",
    business_description: "Indicates sale influenced by push notification. Important for CPG mobile marketing effectiveness, notification ROI, engagement strategies, and personalized communication impact.",
    business_synonyms: ["Push Influenced", "Notification Sale", "Push Marketing", "App Notification", "Mobile Alert", "Push Campaign"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

// ========================================
// SOCIAL COMMERCE AND INFLUENCER METRICS
// ========================================

(social_commerce_flag:Column {
    column_id: "COL_SOCIAL_COMMERCE_FLAG_FACT_4041",
    column_name: "SOCIAL_COMMERCE_FLAG",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 41,
    business_name: "Social Commerce Flag",
    business_description: "Indicates purchase through social media platform. Critical for CPG social selling strategies, platform effectiveness, social commerce growth, and new channel development.",
    business_synonyms: ["Social Sale", "Social Media Purchase", "Social Shopping", "Platform Sale", "Social Order", "Social Transaction"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(social_platform:Column {
    column_id: "COL_SOCIAL_PLATFORM_FACT_4042",
    column_name: "SOCIAL_PLATFORM",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 42,
    business_name: "Social Platform",
    business_description: "Social media platform driving sale (Instagram, Facebook, TikTok, Pinterest). Essential for CPG platform strategy, social ROI comparison, audience targeting, and channel optimization.",
    business_synonyms: ["Social Channel", "Platform Name", "Social Network", "Social Media", "Platform Source", "Social Site"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "medium"
}),

(influencer_code:Column {
    column_id: "COL_INFLUENCER_CODE_FACT_4043",
    column_name: "INFLUENCER_CODE",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 43,
    business_name: "Influencer Code",
    business_description: "Promotional code used by influencer or affiliate. Important for CPG influencer marketing ROI, partnership effectiveness, commission tracking, and creator economy strategies.",
    business_synonyms: ["Affiliate Code", "Creator Code", "Partner Code", "Referral Code", "Influencer ID", "Ambassador Code"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "medium"
}),

// ========================================
// PERSONALIZATION AND RECOMMENDATION
// ========================================

(personalization_flag:Column {
    column_id: "COL_PERSONALIZATION_FLAG_FACT_4044",
    column_name: "PERSONALIZATION_FLAG",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 44,
    business_name: "Personalization Flag",
    business_description: "Indicates personalized experience or recommendations shown. Critical for CPG personalization effectiveness, AI/ML impact measurement, customer experience optimization, and conversion improvement.",
    business_synonyms: ["Personalized Experience", "Custom Content", "Targeted Display", "Personalized Flag", "AI Driven", "Custom Experience"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(recommendation_flag:Column {
    column_id: "COL_RECOMMENDATION_FLAG_FACT_4045",
    column_name: "RECOMMENDATION_FLAG",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 45,
    business_name: "Recommendation Flag",
    business_description: "Indicates purchase influenced by product recommendations. Essential for CPG recommendation engine ROI, cross-sell effectiveness, AI-driven sales, and algorithmic commerce success.",
    business_synonyms: ["Recommended Product", "Suggested Item", "AI Recommendation", "Algorithm Suggested", "Recommendation Sale", "Suggested Purchase"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

(cross_sell_amount:Column {
    column_id: "COL_CROSS_SELL_AMOUNT_FACT_4046",
    column_name: "CROSS_SELL_AMOUNT",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 46,
    business_name: "Cross Sell Amount",
    business_description: "Revenue from cross-sold or bundled products. Important for CPG basket building strategies, product affinity analysis, merchandising effectiveness, and incremental revenue generation.",
    business_synonyms: ["Bundle Revenue", "Additional Sales", "Cross-Sell Revenue", "Upsell Amount", "Incremental Sales", "Add-On Revenue"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 12,
    scale: 2,
    business_criticality: "high",
    additive_type: "additive"
}),

// ========================================
// RETURNS AND CUSTOMER SATISFACTION
// ========================================

(return_flag:Column {
    column_id: "COL_RETURN_FLAG_FACT_4047",
    column_name: "RETURN_FLAG",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 47,
    business_name: "Return Flag",
    business_description: "Indicates if order was subsequently returned. Critical for CPG return rate analysis, product quality issues, customer satisfaction, and net revenue calculations.",
    business_synonyms: ["Returned Order", "Return Status", "Refund Flag", "Return Indicator", "Returned Flag", "Return Transaction"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(return_reason:Column {
    column_id: "COL_RETURN_REASON_FACT_4048",
    column_name: "RETURN_REASON",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 48,
    business_name: "Return Reason",
    business_description: "Primary reason for product return. Essential for CPG quality improvement, product development feedback, customer experience enhancement, and return reduction strategies.",
    business_synonyms: ["Return Cause", "Refund Reason", "Return Type", "Return Category", "Return Code", "Reason Code"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 100,
    business_criticality: "medium"
}),

(customer_rating:Column {
    column_id: "COL_CUSTOMER_RATING_FACT_4049",
    column_name: "CUSTOMER_RATING",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 49,
    business_name: "Customer Rating",
    business_description: "Customer satisfaction rating for purchase (1-5 stars). Important for CPG customer experience tracking, product feedback, service quality monitoring, and reputation management.",
    business_synonyms: ["Star Rating", "Product Rating", "Review Score", "Satisfaction Score", "Customer Score", "Rating Value"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 3,
    scale: 1,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(review_submitted_flag:Column {
    column_id: "COL_REVIEW_SUBMITTED_FLAG_FACT_4050",
    column_name: "REVIEW_SUBMITTED_FLAG",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 50,
    business_name: "Review Submitted Flag",
    business_description: "Indicates customer submitted product review. Critical for CPG social proof generation, review solicitation effectiveness, content creation, and customer engagement tracking.",
    business_synonyms: ["Review Flag", "Feedback Submitted", "Review Posted", "Rating Submitted", "Review Status", "Feedback Flag"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "flag",
    data_type: "BOOLEAN",
    max_length: null,
    business_criticality: "medium",
    additive_type: "non_additive"
}),

// ========================================
// LOYALTY AND REWARDS
// ========================================

(loyalty_points_earned:Column {
    column_id: "COL_LOYALTY_POINTS_EARNED_FACT_4051",
    column_name: "LOYALTY_POINTS_EARNED",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 51,
    business_name: "Loyalty Points Earned",
    business_description: "Number of loyalty points earned from purchase. Essential for CPG loyalty program effectiveness, customer retention strategies, reward optimization, and lifetime value enhancement.",
    business_synonyms: ["Reward Points", "Points Earned", "Loyalty Rewards", "Member Points", "Program Points", "Earned Points"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "medium",
    additive_type: "additive"
}),

(loyalty_points_redeemed:Column {
    column_id: "COL_LOYALTY_POINTS_REDEEMED_FACT_4052",
    column_name: "LOYALTY_POINTS_REDEEMED",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 52,
    business_name: "Loyalty Points Redeemed",
    business_description: "Number of loyalty points used for purchase discount. Important for CPG reward redemption patterns, program liability, customer value perception, and loyalty economics.",
    business_synonyms: ["Points Used", "Redeemed Points", "Points Applied", "Reward Redemption", "Used Points", "Point Payment"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "INTEGER",
    max_length: null,
    business_criticality: "medium",
    additive_type: "additive"
}),

// ========================================
// METADATA AND AUDIT INFORMATION
// ========================================

(order_source_system:Column {
    column_id: "COL_ORDER_SOURCE_SYSTEM_FACT_4053",
    column_name: "ORDER_SOURCE_SYSTEM",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 53,
    business_name: "Order Source System",
    business_description: "E-commerce platform or system generating order (Shopify, Magento, Custom, etc.). Essential for CPG multi-platform management, system integration, platform performance comparison, and technical operations.",
    business_synonyms: ["Platform System", "Source Platform", "Order System", "E-Commerce Platform", "Tech Stack", "System Source"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "attribute",
    data_type: "VARCHAR",
    max_length: 50,
    business_criticality: "medium"
}),

(fraud_risk_score:Column {
    column_id: "COL_FRAUD_RISK_SCORE_FACT_4054",
    column_name: "FRAUD_RISK_SCORE",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 54,
    business_name: "Fraud Risk Score",
    business_description: "Risk score for potential fraudulent transaction (0-100). Critical for CPG fraud prevention, chargeback reduction, payment security, and financial loss mitigation.",
    business_synonyms: ["Risk Score", "Fraud Score", "Security Score", "Risk Rating", "Fraud Probability", "Risk Index"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "measure",
    data_type: "DECIMAL",
    precision: 5,
    scale: 2,
    business_criticality: "high",
    additive_type: "non_additive"
}),

(record_created_timestamp:Column {
    column_id: "COL_RECORD_CREATED_TIMESTAMP_FACT_4055",
    column_name: "RECORD_CREATED_TIMESTAMP",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 55,
    business_name: "Record Created Timestamp",
    business_description: "System timestamp when e-commerce record was created in data warehouse. Used for audit trails, data lineage tracking, and data governance. Immutable after creation. Part of comprehensive data governance framework.",
    business_synonyms: ["Creation Time", "Insert Timestamp", "Created Date", "Record Creation", "Entry Timestamp", "Load Timestamp"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    max_length: null,
    business_criticality: "low",
    additive_type: "non_additive"
}),

(record_modified_timestamp:Column {
    column_id: "COL_RECORD_MODIFIED_TIMESTAMP_FACT_4056",
    column_name: "RECORD_MODIFIED_TIMESTAMP",
    table_id: "FACT_ECOMMERCE_SALES",
    ordinal_position: 56,
    business_name: "Record Last Modified Timestamp",
    business_description: "Most recent update timestamp for e-commerce record. Tracks data freshness, change frequency, and maintenance activities. Updated automatically with any field modification for data governance compliance.",
    business_synonyms: ["Update Time", "Last Modified", "Change Timestamp", "Modified Date", "Last Updated", "Revision Timestamp"],
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"],
    domain_specific: false,
    semantic_type: "date",
    data_type: "TIMESTAMP",
    max_length: null,
    business_criticality: "low",
    additive_type: "non_additive"
});

// ========================================
// CREATE TABLE-COLUMN RELATIONSHIPS
// ========================================

MATCH (t:Table {table_id: "FACT_ECOMMERCE_SALES"})
MATCH (c:Column {table_id: "FACT_ECOMMERCE_SALES"})
CREATE (t)-[:CONTAINS {
    relationship_type: "table_column",
    cardinality: "1..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage", "personal_care", "household_products", "pet_food", "snacks", "dairy", "frozen_foods", "beverages", "health_supplements", "baby_products"]
}]->(c);