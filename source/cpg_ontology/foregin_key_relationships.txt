// ========================================
// CREATE FOREIGN KEY RELATIONSHIPS
// Execute after all tables and columns are created
// Creates REFERENCES relationships between fact and dimension tables
// ========================================

// ========================================
// FACT_SALES FOREIGN KEY RELATIONSHIPS
// ========================================

// FACT_SALES -> DIM_DATE
MATCH (fs:Table {table_id: "FACT_SALES"})
MATCH (dd:Table {table_id: "DIM_DATE"})
MATCH (fk:Column {table_id: "FACT_SALES", column_name: "DATE_KEY"})
MATCH (pk:Column {table_id: "DIM_DATE", column_name: "DATE_KEY"})
CREATE (fs)-[:REFERENCES {
    relationship_type: "foreign_key",
    foreign_key_column: "DATE_KEY",
    primary_key_column: "DATE_KEY",
    cardinality: "*..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_rule: "Every sales transaction must have a valid date",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dd);

// FACT_SALES -> DIM_STORE
MATCH (fs:Table {table_id: "FACT_SALES"})
MATCH (ds:Table {table_id: "DIM_STORE"})
MATCH (fk:Column {table_id: "FACT_SALES", column_name: "STORE_KEY"})
MATCH (pk:Column {table_id: "DIM_STORE", column_name: "STORE_ID"})
CREATE (fs)-[:REFERENCES {
    relationship_type: "foreign_key",
    foreign_key_column: "STORE_KEY",
    primary_key_column: "STORE_ID",
    cardinality: "*..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_rule: "Every sales transaction must occur at a valid store location",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(ds);

// FACT_SALES -> DIM_PRODUCT_HIERARCHY
MATCH (fs:Table {table_id: "FACT_SALES"})
MATCH (dp:Table {table_id: "DIM_PRODUCT_HIERARCHY"})
MATCH (fk:Column {table_id: "FACT_SALES", column_name: "PRODUCT_HIERARCHY_KEY"})
MATCH (pk:Column {table_id: "DIM_PRODUCT_HIERARCHY", column_name: "PRODUCT_HIERARCHY_ID"})
CREATE (fs)-[:REFERENCES {
    relationship_type: "foreign_key",
    foreign_key_column: "PRODUCT_HIERARCHY_KEY",
    primary_key_column: "PRODUCT_HIERARCHY_ID",
    cardinality: "*..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_rule: "Every sales transaction must be for a valid product",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dp);

// FACT_SALES -> DIM_CUSTOMER
MATCH (fs:Table {table_id: "FACT_SALES"})
MATCH (dc:Table {table_id: "DIM_CUSTOMER"})
MATCH (fk:Column {table_id: "FACT_SALES", column_name: "CUSTOMER_KEY"})
MATCH (pk:Column {table_id: "DIM_CUSTOMER", column_name: "CUSTOMER_ID"})
CREATE (fs)-[:REFERENCES {
    relationship_type: "foreign_key",
    foreign_key_column: "CUSTOMER_KEY",
    primary_key_column: "CUSTOMER_ID",
    cardinality: "*..*",
    is_mandatory: false,
    relationship_strength: 0.8,
    business_rule: "Sales transactions may have identified customers (loyalty members)",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dc);

// FACT_SALES -> DIM_PROMOTION
MATCH (fs:Table {table_id: "FACT_SALES"})
MATCH (dp:Table {table_id: "DIM_PROMOTION"})
MATCH (fk:Column {table_id: "FACT_SALES", column_name: "PROMOTION_KEY"})
MATCH (pk:Column {table_id: "DIM_PROMOTION", column_name: "PROMOTION_ID"})
CREATE (fs)-[:REFERENCES {
    relationship_type: "foreign_key",
    foreign_key_column: "PROMOTION_KEY",
    primary_key_column: "PROMOTION_ID",
    cardinality: "*..*",
    is_mandatory: false,
    relationship_strength: 0.6,
    business_rule: "Sales transactions may be associated with promotional campaigns",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dp);

// ========================================
// FACT_REGULATORY_COMPLIANCE FOREIGN KEY RELATIONSHIPS
// ========================================

// FACT_REGULATORY_COMPLIANCE -> DIM_DATE
MATCH (fr:Table {table_id: "FACT_REGULATORY_COMPLIANCE"})
MATCH (dd:Table {table_id: "DIM_DATE"})
MATCH (fk:Column {table_id: "FACT_REGULATORY_COMPLIANCE", column_name: "DATE_KEY"})
MATCH (pk:Column {table_id: "DIM_DATE", column_name: "DATE_KEY"})
CREATE (fr)-[:REFERENCES {
    relationship_type: "foreign_key",
    foreign_key_column: "DATE_KEY",
    primary_key_column: "DATE_KEY",
    cardinality: "*..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_rule: "Every compliance event must have a date",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dd);

// FACT_REGULATORY_COMPLIANCE -> DIM_STORE
MATCH (fr:Table {table_id: "FACT_REGULATORY_COMPLIANCE"})
MATCH (ds:Table {table_id: "DIM_STORE"})
MATCH (fk:Column {table_id: "FACT_REGULATORY_COMPLIANCE", column_name: "STORE_KEY"})
MATCH (pk:Column {table_id: "DIM_STORE", column_name: "STORE_ID"})
CREATE (fr)-[:REFERENCES {
    relationship_type: "foreign_key",
    foreign_key_column: "STORE_KEY",
    primary_key_column: "STORE_ID",
    cardinality: "*..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_rule: "Compliance events occur at specific locations",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(ds);

// FACT_REGULATORY_COMPLIANCE -> DIM_PRODUCT_HIERARCHY
MATCH (fr:Table {table_id: "FACT_REGULATORY_COMPLIANCE"})
MATCH (dp:Table {table_id: "DIM_PRODUCT_HIERARCHY"})
MATCH (fk:Column {table_id: "FACT_REGULATORY_COMPLIANCE", column_name: "PRODUCT_HIERARCHY_KEY"})
MATCH (pk:Column {table_id: "DIM_PRODUCT_HIERARCHY", column_name: "PRODUCT_HIERARCHY_ID"})
CREATE (fr)-[:REFERENCES {
    relationship_type: "foreign_key",
    foreign_key_column: "PRODUCT_HIERARCHY_KEY",
    primary_key_column: "PRODUCT_HIERARCHY_ID",
    cardinality: "*..*",
    is_mandatory: false,
    relationship_strength: 0.8,
    business_rule: "Compliance events may be product-specific",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dp);

// FACT_REGULATORY_COMPLIANCE -> DIM_CUSTOMER
MATCH (fr:Table {table_id: "FACT_REGULATORY_COMPLIANCE"})
MATCH (dc:Table {table_id: "DIM_CUSTOMER"})
MATCH (fk:Column {table_id: "FACT_REGULATORY_COMPLIANCE", column_name: "CUSTOMER_KEY"})
MATCH (pk:Column {table_id: "DIM_CUSTOMER", column_name: "CUSTOMER_ID"})
CREATE (fr)-[:REFERENCES {
    relationship_type: "foreign_key",
    foreign_key_column: "CUSTOMER_KEY",
    primary_key_column: "CUSTOMER_ID",
    cardinality: "*..*",
    is_mandatory: false,
    relationship_strength: 0.5,
    business_rule: "Compliance events may involve specific customers (complaints, incidents)",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dc);

// ========================================
// FACT_PROMOTIONAL_SPEND FOREIGN KEY RELATIONSHIPS
// ========================================

// FACT_PROMOTIONAL_SPEND -> DIM_DATE
MATCH (fp:Table {table_id: "FACT_PROMOTIONAL_SPEND"})
MATCH (dd:Table {table_id: "DIM_DATE"})
MATCH (fk:Column {table_id: "FACT_PROMOTIONAL_SPEND", column_name: "DATE_KEY"})
MATCH (pk:Column {table_id: "DIM_DATE", column_name: "DATE_KEY"})
CREATE (fp)-[:REFERENCES {
    relationship_type: "foreign_key",
    foreign_key_column: "DATE_KEY",
    primary_key_column: "DATE_KEY",
    cardinality: "*..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_rule: "Every promotional spend must have a date",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dd);

// FACT_PROMOTIONAL_SPEND -> DIM_STORE
MATCH (fp:Table {table_id: "FACT_PROMOTIONAL_SPEND"})
MATCH (ds:Table {table_id: "DIM_STORE"})
MATCH (fk:Column {table_id: "FACT_PROMOTIONAL_SPEND", column_name: "STORE_KEY"})
MATCH (pk:Column {table_id: "DIM_STORE", column_name: "STORE_ID"})
CREATE (fp)-[:REFERENCES {
    relationship_type: "foreign_key",
    foreign_key_column: "STORE_KEY",
    primary_key_column: "STORE_ID",
    cardinality: "*..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_rule: "Promotional spend is tracked by location",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(ds);

// FACT_PROMOTIONAL_SPEND -> DIM_PRODUCT_HIERARCHY
MATCH (fp:Table {table_id: "FACT_PROMOTIONAL_SPEND"})
MATCH (dp:Table {table_id: "DIM_PRODUCT_HIERARCHY"})
MATCH (fk:Column {table_id: "FACT_PROMOTIONAL_SPEND", column_name: "PRODUCT_HIERARCHY_KEY"})
MATCH (pk:Column {table_id: "DIM_PRODUCT_HIERARCHY", column_name: "PRODUCT_HIERARCHY_ID"})
CREATE (fp)-[:REFERENCES {
    relationship_type: "foreign_key",
    foreign_key_column: "PRODUCT_HIERARCHY_KEY",
    primary_key_column: "PRODUCT_HIERARCHY_ID",
    cardinality: "*..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_rule: "Promotional spend is allocated to specific products",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dp);

// FACT_PROMOTIONAL_SPEND -> DIM_CUSTOMER
MATCH (fp:Table {table_id: "FACT_PROMOTIONAL_SPEND"})
MATCH (dc:Table {table_id: "DIM_CUSTOMER"})
MATCH (fk:Column {table_id: "FACT_PROMOTIONAL_SPEND", column_name: "CUSTOMER_KEY"})
MATCH (pk:Column {table_id: "DIM_CUSTOMER", column_name: "CUSTOMER_ID"})
CREATE (fp)-[:REFERENCES {
    relationship_type: "foreign_key",
    foreign_key_column: "CUSTOMER_KEY",
    primary_key_column: "CUSTOMER_ID",
    cardinality: "*..*",
    is_mandatory: false,
    relationship_strength: 0.7,
    business_rule: "Promotional spend may target specific customers",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dc);

// FACT_PROMOTIONAL_SPEND -> DIM_PROMOTION
MATCH (fp:Table {table_id: "FACT_PROMOTIONAL_SPEND"})
MATCH (dp:Table {table_id: "DIM_PROMOTION"})
MATCH (fk:Column {table_id: "FACT_PROMOTIONAL_SPEND", column_name: "PROMOTIONAL_EVENT_ID"})
MATCH (pk:Column {table_id: "DIM_PROMOTION", column_name: "PROMOTION_ID"})
CREATE (fp)-[:REFERENCES {
    relationship_type: "foreign_key",
    foreign_key_column: "PROMOTIONAL_EVENT_ID",
    primary_key_column: "PROMOTION_ID",
    cardinality: "*..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_rule: "Promotional spend must be linked to promotion campaigns",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dp);

// ========================================
// FACT_INVENTORY FOREIGN KEY RELATIONSHIPS
// ========================================

// FACT_INVENTORY -> DIM_DATE
MATCH (fi:Table {table_id: "FACT_INVENTORY"})
MATCH (dd:Table {table_id: "DIM_DATE"})
MATCH (fk:Column {table_id: "FACT_INVENTORY", column_name: "DATE_KEY"})
MATCH (pk:Column {table_id: "DIM_DATE", column_name: "DATE_KEY"})
CREATE (fi)-[:REFERENCES {
    relationship_type: "foreign_key",
    foreign_key_column: "DATE_KEY",
    primary_key_column: "DATE_KEY",
    cardinality: "*..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_rule: "Every inventory snapshot must have a date",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dd);

// FACT_INVENTORY -> DIM_STORE
MATCH (fi:Table {table_id: "FACT_INVENTORY"})
MATCH (ds:Table {table_id: "DIM_STORE"})
MATCH (fk:Column {table_id: "FACT_INVENTORY", column_name: "STORE_KEY"})
MATCH (pk:Column {table_id: "DIM_STORE", column_name: "STORE_ID"})
CREATE (fi)-[:REFERENCES {
    relationship_type: "foreign_key",
    foreign_key_column: "STORE_KEY",
    primary_key_column: "STORE_ID",
    cardinality: "*..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_rule: "Inventory is tracked by location",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(ds);

// FACT_INVENTORY -> DIM_PRODUCT_HIERARCHY
MATCH (fi:Table {table_id: "FACT_INVENTORY"})
MATCH (dp:Table {table_id: "DIM_PRODUCT_HIERARCHY"})
MATCH (fk:Column {table_id: "FACT_INVENTORY", column_name: "PRODUCT_HIERARCHY_KEY"})
MATCH (pk:Column {table_id: "DIM_PRODUCT_HIERARCHY", column_name: "PRODUCT_HIERARCHY_ID"})
CREATE (fi)-[:REFERENCES {
    relationship_type: "foreign_key",
    foreign_key_column: "PRODUCT_HIERARCHY_KEY",
    primary_key_column: "PRODUCT_HIERARCHY_ID",
    cardinality: "*..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_rule: "Inventory is tracked by product",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dp);

// FACT_INVENTORY -> DIM_MANUFACTURER (as Supplier)
MATCH (fi:Table {table_id: "FACT_INVENTORY"})
MATCH (dm:Table {table_id: "DIM_MANUFACTURER"})
MATCH (fk:Column {table_id: "FACT_INVENTORY", column_name: "SUPPLIER_KEY"})
MATCH (pk:Column {table_id: "DIM_MANUFACTURER", column_name: "MANUFACTURER_ID"})
CREATE (fi)-[:REFERENCES {
    relationship_type: "foreign_key",
    foreign_key_column: "SUPPLIER_KEY",
    primary_key_column: "MANUFACTURER_ID",
    cardinality: "*..*",
    is_mandatory: false,
    relationship_strength: 0.8,
    business_rule: "Inventory may be tracked by supplier for VMI",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dm);

// ========================================
// FACT_ECOMMERCE_SALES FOREIGN KEY RELATIONSHIPS
// ========================================

// FACT_ECOMMERCE_SALES -> DIM_DATE
MATCH (fe:Table {table_id: "FACT_ECOMMERCE_SALES"})
MATCH (dd:Table {table_id: "DIM_DATE"})
MATCH (fk:Column {table_id: "FACT_ECOMMERCE_SALES", column_name: "DATE_KEY"})
MATCH (pk:Column {table_id: "DIM_DATE", column_name: "DATE_KEY"})
CREATE (fe)-[:REFERENCES {
    relationship_type: "foreign_key",
    foreign_key_column: "DATE_KEY",
    primary_key_column: "DATE_KEY",
    cardinality: "*..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_rule: "Every e-commerce transaction must have a date",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dd);

// FACT_ECOMMERCE_SALES -> DIM_STORE (Digital Storefront)
MATCH (fe:Table {table_id: "FACT_ECOMMERCE_SALES"})
MATCH (ds:Table {table_id: "DIM_STORE"})
MATCH (fk:Column {table_id: "FACT_ECOMMERCE_SALES", column_name: "STORE_KEY"})
MATCH (pk:Column {table_id: "DIM_STORE", column_name: "STORE_ID"})
CREATE (fe)-[:REFERENCES {
    relationship_type: "foreign_key",
    foreign_key_column: "STORE_KEY",
    primary_key_column: "STORE_ID",
    cardinality: "*..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_rule: "E-commerce sales occur through digital storefronts",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(ds);

// FACT_ECOMMERCE_SALES -> DIM_PRODUCT_HIERARCHY
MATCH (fe:Table {table_id: "FACT_ECOMMERCE_SALES"})
MATCH (dp:Table {table_id: "DIM_PRODUCT_HIERARCHY"})
MATCH (fk:Column {table_id: "FACT_ECOMMERCE_SALES", column_name: "PRODUCT_HIERARCHY_KEY"})
MATCH (pk:Column {table_id: "DIM_PRODUCT_HIERARCHY", column_name: "PRODUCT_HIERARCHY_ID"})
CREATE (fe)-[:REFERENCES {
    relationship_type: "foreign_key",
    foreign_key_column: "PRODUCT_HIERARCHY_KEY",
    primary_key_column: "PRODUCT_HIERARCHY_ID",
    cardinality: "*..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_rule: "E-commerce sales are for specific products",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dp);

// FACT_ECOMMERCE_SALES -> DIM_CUSTOMER
MATCH (fe:Table {table_id: "FACT_ECOMMERCE_SALES"})
MATCH (dc:Table {table_id: "DIM_CUSTOMER"})
MATCH (fk:Column {table_id: "FACT_ECOMMERCE_SALES", column_name: "CUSTOMER_KEY"})
MATCH (pk:Column {table_id: "DIM_CUSTOMER", column_name: "CUSTOMER_ID"})
CREATE (fe)-[:REFERENCES {
    relationship_type: "foreign_key",
    foreign_key_column: "CUSTOMER_KEY",
    primary_key_column: "CUSTOMER_ID",
    cardinality: "*..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_rule: "E-commerce sales require customer identification",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dc);

// FACT_ECOMMERCE_SALES -> DIM_PROMOTION
MATCH (fe:Table {table_id: "FACT_ECOMMERCE_SALES"})
MATCH (dp:Table {table_id: "DIM_PROMOTION"})
MATCH (fk:Column {table_id: "FACT_ECOMMERCE_SALES", column_name: "PROMOTION_KEY"})
MATCH (pk:Column {table_id: "DIM_PROMOTION", column_name: "PROMOTION_ID"})
CREATE (fe)-[:REFERENCES {
    relationship_type: "foreign_key",
    foreign_key_column: "PROMOTION_KEY",
    primary_key_column: "PROMOTION_ID",
    cardinality: "*..*",
    is_mandatory: false,
    relationship_strength: 0.7,
    business_rule: "E-commerce sales may use digital promotions",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dp);

// ========================================
// FACT_SYNDICATED_PANEL FOREIGN KEY RELATIONSHIPS
// ========================================

// FACT_SYNDICATED_PANEL -> DIM_DATE
MATCH (fsp:Table {table_id: "FACT_SYNDICATED_PANEL"})
MATCH (dd:Table {table_id: "DIM_DATE"})
MATCH (fk:Column {table_id: "FACT_SYNDICATED_PANEL", column_name: "DATE_KEY"})
MATCH (pk:Column {table_id: "DIM_DATE", column_name: "DATE_KEY"})
CREATE (fsp)-[:REFERENCES {
    relationship_type: "foreign_key",
    foreign_key_column: "DATE_KEY",
    primary_key_column: "DATE_KEY",
    cardinality: "*..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_rule: "Panel data must have period dates",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dd);

// FACT_SYNDICATED_PANEL -> DIM_PRODUCT_HIERARCHY
MATCH (fsp:Table {table_id: "FACT_SYNDICATED_PANEL"})
MATCH (dp:Table {table_id: "DIM_PRODUCT_HIERARCHY"})
MATCH (fk:Column {table_id: "FACT_SYNDICATED_PANEL", column_name: "PRODUCT_HIERARCHY_KEY"})
MATCH (pk:Column {table_id: "DIM_PRODUCT_HIERARCHY", column_name: "PRODUCT_HIERARCHY_ID"})
CREATE (fsp)-[:REFERENCES {
    relationship_type: "foreign_key",
    foreign_key_column: "PRODUCT_HIERARCHY_KEY",
    primary_key_column: "PRODUCT_HIERARCHY_ID",
    cardinality: "*..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_rule: "Panel data tracks specific products",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dp);

// Note: FACT_SYNDICATED_PANEL may also need references to:
// - Market dimension (for geographic markets)
// - Competitor dimension (for competitive products)
// These would need to be created as separate dimension tables

// ========================================
// FACT_PRICING FOREIGN KEY RELATIONSHIPS
// ========================================

// FACT_PRICING -> DIM_DATE
MATCH (fpr:Table {table_id: "FACT_PRICING"})
MATCH (dd:Table {table_id: "DIM_DATE"})
MATCH (fk:Column {table_id: "FACT_PRICING", column_name: "DATE_KEY"})
MATCH (pk:Column {table_id: "DIM_DATE", column_name: "DATE_KEY"})
CREATE (fpr)-[:REFERENCES {
    relationship_type: "foreign_key",
    foreign_key_column: "DATE_KEY",
    primary_key_column: "DATE_KEY",
    cardinality: "*..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_rule: "Pricing must be tracked over time",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dd);

// FACT_PRICING -> DIM_STORE
MATCH (fpr:Table {table_id: "FACT_PRICING"})
MATCH (ds:Table {table_id: "DIM_STORE"})
MATCH (fk:Column {table_id: "FACT_PRICING", column_name: "STORE_KEY"})
MATCH (pk:Column {table_id: "DIM_STORE", column_name: "STORE_ID"})
CREATE (fpr)-[:REFERENCES {
    relationship_type: "foreign_key",
    foreign_key_column: "STORE_KEY",
    primary_key_column: "STORE_ID",
    cardinality: "*..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_rule: "Pricing varies by location",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(ds);

// FACT_PRICING -> DIM_PRODUCT_HIERARCHY
MATCH (fpr:Table {table_id: "FACT_PRICING"})
MATCH (dp:Table {table_id: "DIM_PRODUCT_HIERARCHY"})
MATCH (fk:Column {table_id: "FACT_PRICING", column_name: "PRODUCT_HIERARCHY_KEY"})
MATCH (pk:Column {table_id: "DIM_PRODUCT_HIERARCHY", column_name: "PRODUCT_HIERARCHY_ID"})
CREATE (fpr)-[:REFERENCES {
    relationship_type: "foreign_key",
    foreign_key_column: "PRODUCT_HIERARCHY_KEY",
    primary_key_column: "PRODUCT_HIERARCHY_ID",
    cardinality: "*..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_rule: "Pricing is set for specific products",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dp);

// FACT_PRICING -> DIM_CUSTOMER
MATCH (fpr:Table {table_id: "FACT_PRICING"})
MATCH (dc:Table {table_id: "DIM_CUSTOMER"})
MATCH (fk:Column {table_id: "FACT_PRICING", column_name: "CUSTOMER_KEY"})
MATCH (pk:Column {table_id: "DIM_CUSTOMER", column_name: "CUSTOMER_ID"})
CREATE (fpr)-[:REFERENCES {
    relationship_type: "foreign_key",
    foreign_key_column: "CUSTOMER_KEY",
    primary_key_column: "CUSTOMER_ID",
    cardinality: "*..*",
    is_mandatory: false,
    relationship_strength: 0.6,
    business_rule: "Pricing may be customer-specific (B2B, contracts)",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(dc);

// ========================================
// INTER-DIMENSIONAL RELATIONSHIPS
// ========================================

// DIM_PRODUCT_HIERARCHY -> DIM_BRAND_MASTER
MATCH (ph:Table {table_id: "DIM_PRODUCT_HIERARCHY"})
MATCH (b:Table {table_id: "DIM_BRAND_MASTER"})
MATCH (fk:Column {table_id: "DIM_PRODUCT_HIERARCHY", column_name: "BRAND_KEY"})
MATCH (pk:Column {table_id: "DIM_BRAND_MASTER", column_name: "BRAND_ID"})
CREATE (ph)-[:REFERENCES {
    relationship_type: "foreign_key",
    foreign_key_column: "BRAND_KEY",
    primary_key_column: "BRAND_ID",
    cardinality: "*..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_rule: "Products belong to brands",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(b);

// DIM_PRODUCT_HIERARCHY -> DIM_CATEGORY
MATCH (ph:Table {table_id: "DIM_PRODUCT_HIERARCHY"})
MATCH (c:Table {table_id: "DIM_CATEGORY"})
MATCH (fk:Column {table_id: "DIM_PRODUCT_HIERARCHY", column_name: "CATEGORY_KEY"})
MATCH (pk:Column {table_id: "DIM_CATEGORY", column_name: "CATEGORY_ID"})
CREATE (ph)-[:REFERENCES {
    relationship_type: "foreign_key",
    foreign_key_column: "CATEGORY_KEY",
    primary_key_column: "CATEGORY_ID",
    cardinality: "*..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_rule: "Products are classified into categories",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(c);

// DIM_PRODUCT_HIERARCHY -> DIM_MANUFACTURER
MATCH (ph:Table {table_id: "DIM_PRODUCT_HIERARCHY"})
MATCH (m:Table {table_id: "DIM_MANUFACTURER"})
MATCH (fk:Column {table_id: "DIM_PRODUCT_HIERARCHY", column_name: "MANUFACTURER_KEY"})
MATCH (pk:Column {table_id: "DIM_MANUFACTURER", column_name: "MANUFACTURER_ID"})
CREATE (ph)-[:REFERENCES {
    relationship_type: "foreign_key",
    foreign_key_column: "MANUFACTURER_KEY",
    primary_key_column: "MANUFACTURER_ID",
    cardinality: "*..*",
    is_mandatory: true,
    relationship_strength: 1.0,
    business_rule: "Products are produced by manufacturers",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(m);

// DIM_PROMOTION -> DIM_MEDIA
MATCH (p:Table {table_id: "DIM_PROMOTION"})
MATCH (m:Table {table_id: "DIM_MEDIA"})
MATCH (fk:Column {table_id: "DIM_PROMOTION", column_name: "MEDIA_KEY"})
MATCH (pk:Column {table_id: "DIM_MEDIA", column_name: "MEDIA_ID"})
CREATE (p)-[:REFERENCES {
    relationship_type: "foreign_key",
    foreign_key_column: "MEDIA_KEY",
    primary_key_column: "MEDIA_ID",
    cardinality: "*..*",
    is_mandatory: false,
    relationship_strength: 0.6,
    business_rule: "Promotions may include media campaigns",
    applicable_domains: ["alcoholic_beverages", "pharmaceuticals", "battery", "toys", "cosmetics", "food_beverage"]
}]->(m);

// ========================================
// VALIDATION QUERIES
// ========================================

// Count foreign key relationships created
// MATCH (t1:Table)-[r:REFERENCES]->(t2:Table)
// RETURN t1.table_name as from_table, r.foreign_key_column, t2.table_name as to_table
// ORDER BY t1.table_name;

// Verify referential integrity
// MATCH (ft:Table)-[r:REFERENCES]->(dt:Table)
// WHERE ft.table_type = 'fact' AND dt.table_type = 'dimension'
// RETURN ft.table_name as fact_table, COUNT(r) as fk_count
// ORDER BY ft.table_name;

// ========================================
// END OF FOREIGN KEY RELATIONSHIPS
// ========================================