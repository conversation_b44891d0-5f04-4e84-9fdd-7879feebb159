["test_specialized_agents.py::TestContentAnalyzerAgent::test_apply_content_mutual_exclusion", "test_specialized_agents.py::TestContentAnalyzerAgent::test_extract_content_features", "test_specialized_agents.py::TestDomainExpertAgent::test_apply_domain_mutual_exclusion", "test_specialized_agents.py::TestDomainExpertAgent::test_perform_domain_analysis", "test_specialized_agents.py::TestIntegration::test_error_handling", "test_specialized_agents.py::TestSchemaAnalyzerAgent::test_analyze_file", "test_specialized_agents.py::TestSchemaAnalyzerAgent::test_build_search_query", "test_specialized_agents.py::TestSchemaAnalyzerAgent::test_extract_schema_features", "test_specialized_agents.py::TestVectorDBEnhancedAgent::test_agent_initialization_with_vector_db", "test_specialized_agents.py::TestVectorDBEnhancedAgent::test_agent_initialization_without_vector_db", "test_specialized_agents.py::TestVectorDBEnhancedAgent::test_search_vector_db_disabled", "test_specialized_agents.py::TestVectorDBEnhancedAgent::test_search_vector_db_success"]