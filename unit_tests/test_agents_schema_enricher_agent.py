"""
Unit tests for agents.schema_enricher_agent module
"""
import pytest
import asyncio
from unittest.mock import Mo<PERSON>, AsyncMock, patch
import sys
from pathlib import Path

project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from agents.schema_enricher_agent import SchemaEnricherAgent
    from agents.base_agent import FileContext, AgentDecision
except ImportError as e:
    pytest.skip(f"Required modules not available: {e}", allow_module_level=True)


class TestSchemaEnricherAgent:
    """Test SchemaEnricherAgent class"""
    
    @pytest.fixture
    def agent_config(self):
        """Standard agent configuration"""
        return {
            'enable_vector_search': True,
            'similarity_threshold': 0.7,
            'enrichment_sources': ['vector_db', 'domain_knowledge']
        }
    
    @pytest.fixture
    def sample_schema(self):
        """Sample schema for testing"""
        return {
            'file_name': 'sales_data.csv',
            'schema': {
                'product_id': 'string',
                'sales_amount': 'numeric',
                'store_location': 'string',
                'transaction_date': 'date'
            }
        }
    
    @pytest.fixture
    def sample_file_context(self, sample_schema):
        """Sample file context for testing"""
        return FileContext(
            file_name="sales_data.csv",
            file_path="/path/to/sales_data.csv",
            file_type="csv",
            file_size=3072,
            extracted_content={
                'schema': sample_schema['schema'],
                'sample_data': [
                    {'product_id': 'P001', 'sales_amount': '150.00', 'store_location': 'NYC'},
                    {'product_id': 'P002', 'sales_amount': '200.50', 'store_location': 'LA'}
                ]
            }
        )
    
    def test_agent_initialization(self, agent_config):
        """Test SchemaEnricherAgent initialization"""
        try:
            # Mock ConfigManager
            mock_config_manager = Mock()
            mock_config_manager.get_azure_openai_config.return_value = Mock(
                api_key="test_key",
                api_version="2023-05-15",
                endpoint="https://test.openai.azure.com/",
                deployment="test-deployment",
                timeout=30,
                max_retries=3
            )

            agent = SchemaEnricherAgent(mock_config_manager)

            assert hasattr(agent, 'llm_processor')
            assert hasattr(agent, 'logger')

        except Exception as e:
            pytest.skip(f"SchemaEnricherAgent initialization failed: {e}")
    
    def test_enrich_schema_metadata(self, agent_config, sample_schema):
        """Test schema metadata enrichment"""
        try:
            # Mock ConfigManager
            mock_config_manager = Mock()
            mock_config_manager.get_azure_openai_config.return_value = Mock(
                api_key="test_key",
                api_version="2023-05-15",
                endpoint="https://test.openai.azure.com/",
                deployment="test-deployment",
                timeout=30,
                max_retries=3
            )

            agent = SchemaEnricherAgent(mock_config_manager)
            
            if hasattr(agent, '_enrich_schema_metadata'):
                enriched = agent._enrich_schema_metadata(sample_schema)
                
                assert isinstance(enriched, dict)
                assert 'file_name' in enriched
                assert 'schema' in enriched
                
                # Should have additional metadata
                if 'metadata' in enriched:
                    assert isinstance(enriched['metadata'], dict)
                    
            else:
                pytest.skip("_enrich_schema_metadata method not found")
                
        except Exception as e:
            pytest.skip(f"Schema metadata enrichment test failed: {e}")
    
    def test_add_business_context(self, agent_config, sample_file_context):
        """Test business context addition"""
        try:
            # Mock ConfigManager
            mock_config_manager = Mock()
            mock_config_manager.get_azure_openai_config.return_value = Mock(
                api_key="test_key",
                api_version="2023-05-15",
                endpoint="https://test.openai.azure.com/",
                deployment="test-deployment",
                timeout=30,
                max_retries=3
            )

            agent = SchemaEnricherAgent(mock_config_manager)
            
            if hasattr(agent, '_add_business_context'):
                context = agent._add_business_context(sample_file_context)
                
                assert isinstance(context, dict)
                
                # Should contain business-relevant fields
                expected_fields = ['business_domain', 'data_classification', 'usage_patterns']
                for field in expected_fields:
                    if field in context:
                        assert isinstance(context[field], (str, list, dict))
                        
            else:
                pytest.skip("_add_business_context method not found")
                
        except Exception as e:
            pytest.skip(f"Business context addition test failed: {e}")
    
    def test_enhance_column_definitions(self, agent_config, sample_file_context):
        """Test column definition enhancement"""
        try:
            # Mock ConfigManager
            mock_config_manager = Mock()
            mock_config_manager.get_azure_openai_config.return_value = Mock(
                api_key="test_key",
                api_version="2023-05-15",
                endpoint="https://test.openai.azure.com/",
                deployment="test-deployment",
                timeout=30,
                max_retries=3
            )

            agent = SchemaEnricherAgent(mock_config_manager)
            
            if hasattr(agent, '_enhance_column_definitions'):
                enhanced = agent._enhance_column_definitions(
                    sample_file_context.extracted_content['schema']
                )
                
                assert isinstance(enhanced, dict)
                
                # Each column should have enhanced information
                for column_name, column_info in enhanced.items():
                    if isinstance(column_info, dict):
                        # Should have additional metadata beyond just type
                        expected_fields = ['data_type', 'description', 'business_meaning']
                        for field in expected_fields:
                            if field in column_info:
                                assert isinstance(column_info[field], str)
                                
            else:
                pytest.skip("_enhance_column_definitions method not found")
                
        except Exception as e:
            pytest.skip(f"Column definition enhancement test failed: {e}")
    
    @pytest.mark.asyncio
    async def test_analyze_method(self, agent_config, sample_file_context):
        """Test the analyze method"""
        try:
            # Mock ConfigManager
            mock_config_manager = Mock()
            mock_config_manager.get_azure_openai_config.return_value = Mock(
                api_key="test_key",
                api_version="2023-05-15",
                endpoint="https://test.openai.azure.com/",
                deployment="test-deployment",
                timeout=30,
                max_retries=3
            )

            agent = SchemaEnricherAgent(mock_config_manager)
            
            # Mock any external dependencies
            if hasattr(agent, 'search_vector_db'):
                agent.search_vector_db = AsyncMock(return_value={
                    'confidence_boost': 0.15,
                    'best_matches': [
                        {'similarity_score': 0.8, 'metadata': {'domain': 'retail'}}
                    ]
                })
            
            decision = await agent.analyze(sample_file_context)
            
            assert isinstance(decision, AgentDecision)
            assert decision.agent_id == "enricher_agent"
            assert isinstance(decision.classification, str)
            assert 0 <= decision.confidence <= 1
            assert isinstance(decision.reasoning, str)
            
        except Exception as e:
            pytest.skip(f"Analyze method test failed: {e}")
    
    def test_create_enriched_schema(self, agent_config, sample_file_context):
        """Test enriched schema creation"""
        try:
            # Mock ConfigManager
            mock_config_manager = Mock()
            mock_config_manager.get_azure_openai_config.return_value = Mock(
                api_key="test_key",
                api_version="2023-05-15",
                endpoint="https://test.openai.azure.com/",
                deployment="test-deployment",
                timeout=30,
                max_retries=3
            )

            agent = SchemaEnricherAgent(mock_config_manager)
            
            if hasattr(agent, 'create_enriched_schema'):
                enriched_schema = agent.create_enriched_schema(sample_file_context)
                
                assert isinstance(enriched_schema, dict)
                
                # Should contain original schema plus enrichments
                expected_fields = ['original_schema', 'enriched_schema', 'enrichment_metadata']
                for field in expected_fields:
                    if field in enriched_schema:
                        assert field in enriched_schema
                        
            else:
                pytest.skip("create_enriched_schema method not found")
                
        except Exception as e:
            pytest.skip(f"Enriched schema creation test failed: {e}")