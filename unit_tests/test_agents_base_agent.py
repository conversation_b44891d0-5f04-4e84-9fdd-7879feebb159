"""
Unit tests for agents.base_agent module
"""
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from enum import Enum
from typing import Dict, Any
import sys
from pathlib import Path

project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from agents.base_agent import BaseAgent, AgentType, FileContext, AgentDecision
except ImportError as e:
    pytest.skip(f"Required modules not available: {e}", allow_module_level=True)


class TestAgentType:
    """Test AgentType enum"""
    
    def test_agent_type_values(self):
        """Test that AgentType has expected values"""
        expected_types = ['SCHEMA_ANALYZER', 'CONTENT_ANALYZER', 'DOMAIN_EXPERT']
        
        for expected_type in expected_types:
            if hasattr(AgentType, expected_type):
                assert hasattr(AgentType, expected_type)


class TestFileContext:
    """Test FileContext class"""
    
    def test_file_context_creation(self):
        """Test FileContext initialization"""
        context = FileContext(
            file_name="test.csv",
            file_path="/path/to/test.csv",
            file_type="csv",
            file_size=1024,
            extracted_content={'data': 'sample'}
        )
        
        assert context.file_name == "test.csv"
        assert context.file_path == "/path/to/test.csv"
        assert context.file_type == "csv"
        assert context.file_size == 1024
        assert context.extracted_content == {'data': 'sample'}
    
    def test_file_context_defaults(self):
        """Test FileContext with minimal parameters"""
        context = FileContext(
            file_name="test.csv",
            file_path="/path/to/test.csv",
            file_type="csv",
            file_size=1024,
            extracted_content={}
        )

        assert context.extracted_content == {}


class TestAgentDecision:
    """Test AgentDecision class"""
    
    def test_agent_decision_creation(self):
        """Test AgentDecision initialization"""
        decision = AgentDecision(
            agent_id="test_agent",
            agent_type=AgentType.SCHEMA_ANALYZER if hasattr(AgentType, 'SCHEMA_ANALYZER') else "SCHEMA_ANALYZER",
            classification="syndicated",
            confidence=0.85,
            reasoning="High confidence based on schema patterns",
            processing_time=0.5
        )
        
        assert decision.agent_id == "test_agent"
        assert decision.classification == "syndicated"
        assert decision.confidence == 0.85
        assert decision.reasoning == "High confidence based on schema patterns"
        assert decision.processing_time == 0.5


class ConcreteAgent(BaseAgent):
    """Concrete implementation for testing abstract BaseAgent"""
    
    def get_capabilities(self):
        return ["test_capability"]
    
    async def analyze(self, file_context):
        return AgentDecision(
            agent_id=self.agent_id,
            agent_type=self.agent_type,
            classification="test",
            confidence=0.5,
            reasoning="test reasoning",
            processing_time=0.1
        )


class TestBaseAgent:
    """Test BaseAgent abstract class"""
    
    def test_base_agent_initialization(self):
        """Test BaseAgent initialization"""
        agent_type = AgentType.SCHEMA_ANALYZER if hasattr(AgentType, 'SCHEMA_ANALYZER') else "SCHEMA_ANALYZER"
        agent = ConcreteAgent("test_agent", agent_type, {})
        
        assert agent.agent_id == "test_agent"
        assert agent.agent_type == agent_type
        assert isinstance(agent.config, dict)
    
    def test_get_capabilities(self):
        """Test get_capabilities method"""
        agent_type = AgentType.SCHEMA_ANALYZER if hasattr(AgentType, 'SCHEMA_ANALYZER') else "SCHEMA_ANALYZER"
        agent = ConcreteAgent("test_agent", agent_type, {})
        
        capabilities = agent.get_capabilities()
        assert isinstance(capabilities, list)
        assert "test_capability" in capabilities
    
    @pytest.mark.asyncio
    async def test_analyze_method(self):
        """Test analyze method"""
        agent_type = AgentType.SCHEMA_ANALYZER if hasattr(AgentType, 'SCHEMA_ANALYZER') else "SCHEMA_ANALYZER"
        agent = ConcreteAgent("test_agent", agent_type, {})
        
        file_context = FileContext(
            file_name="test.csv",
            file_path="/path/to/test.csv",
            file_type="csv",
            file_size=1024,
            extracted_content={'schema': {'columns': ['col1', 'col2']}, 'content': 'test data'}
        )
        
        decision = await agent.analyze(file_context)
        
        assert isinstance(decision, AgentDecision)
        assert decision.agent_id == "test_agent"
        assert decision.classification == "test"