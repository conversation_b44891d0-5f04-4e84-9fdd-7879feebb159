"""
Unit tests for core.dataqualityanalysis.main_analyzer module
"""
import pytest
import pandas as pd
from unittest.mock import Mock, patch, MagicMock
import sys
from pathlib import Path

project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from core.dataqualityanalysis.main_analyzer import CSVQualityAnalyzer
except ImportError as e:
    pytest.skip(f"Required modules not available: {e}", allow_module_level=True)


class TestCSVQualityAnalyzer:
    """Test CSVQualityAnalyzer class"""
    
    @pytest.fixture
    def sample_csv_data(self):
        """Sample CSV data for testing"""
        return pd.DataFrame({
            'product_id': ['P001', 'P002', '', 'P004', 'P005'],
            'product_name': ['Widget A', 'Widget B', 'Widget C', '', 'Widget E'],
            'price': [10.50, 20.75, 15.25, 30.00, -5.00],
            'category': ['Electronics', 'Home', 'Electronics', 'Home', 'Electronics'],
            'date_added': ['2024-01-01', '2024-01-02', 'invalid', '2024-01-04', '2024-01-05']
        })
    
    @pytest.fixture
    def analyzer_config(self):
        """Analyzer configuration"""
        return {
            'enable_completeness_check': True,
            'enable_validity_check': True,
            'enable_consistency_check': True,
            'enable_uniqueness_check': True,
            'null_threshold': 0.1,
            'duplicate_threshold': 0.05
        }
    
    def test_analyzer_initialization(self, analyzer_config):
        """Test CSVQualityAnalyzer initialization"""
        try:
            analyzer = CSVQualityAnalyzer(analyzer_config)
            assert isinstance(analyzer, CSVQualityAnalyzer)
            assert hasattr(analyzer, 'config')
        except Exception as e:
            pytest.skip(f"CSVQualityAnalyzer initialization failed: {e}")
    
    def test_analyze_completeness(self, analyzer_config, sample_csv_data):
        """Test completeness analysis"""
        try:
            analyzer = CSVQualityAnalyzer(analyzer_config)
            
            if hasattr(analyzer, 'analyze_completeness'):
                completeness_results = analyzer.analyze_completeness(sample_csv_data)
                
                assert isinstance(completeness_results, dict)
                
                # Should contain completeness metrics
                expected_fields = ['total_rows', 'null_counts', 'completeness_percentage']
                for field in expected_fields:
                    if field in completeness_results:
                        assert field in completeness_results
                        
                # Check null counts
                if 'null_counts' in completeness_results:
                    null_counts = completeness_results['null_counts']
                    assert isinstance(null_counts, dict)
                    
                    # Should have counts for each column
                    for column in sample_csv_data.columns:
                        if column in null_counts:
                            assert isinstance(null_counts[column], (int, float))
                            
            else:
                pytest.skip("analyze_completeness method not found")
                
        except Exception as e:
            pytest.skip(f"Completeness analysis test failed: {e}")
    
    def test_analyze_validity(self, analyzer_config, sample_csv_data):
        """Test validity analysis"""
        try:
            analyzer = CSVQualityAnalyzer(analyzer_config)
            
            if hasattr(analyzer, 'analyze_validity'):
                validity_results = analyzer.analyze_validity(sample_csv_data)
                
                assert isinstance(validity_results, dict)
                
                # Should contain validity metrics
                expected_fields = ['data_type_issues', 'format_violations', 'range_violations']
                for field in expected_fields:
                    if field in validity_results:
                        assert field in validity_results
                        
            else:
                pytest.skip("analyze_validity method not found")
                
        except Exception as e:
            pytest.skip(f"Validity analysis test failed: {e}")
    
    def test_analyze_uniqueness(self, analyzer_config, sample_csv_data):
        """Test uniqueness analysis"""
        try:
            analyzer = CSVQualityAnalyzer(analyzer_config)
            
            if hasattr(analyzer, 'analyze_uniqueness'):
                uniqueness_results = analyzer.analyze_uniqueness(sample_csv_data)
                
                assert isinstance(uniqueness_results, dict)
                
                # Should contain uniqueness metrics
                expected_fields = ['duplicate_counts', 'unique_percentages', 'duplicate_rows']
                for field in expected_fields:
                    if field in uniqueness_results:
                        assert field in uniqueness_results
                        
                # Check duplicate counts
                if 'duplicate_counts' in uniqueness_results:
                    duplicate_counts = uniqueness_results['duplicate_counts']
                    assert isinstance(duplicate_counts, dict)
                    
            else:
                pytest.skip("analyze_uniqueness method not found")
                
        except Exception as e:
            pytest.skip(f"Uniqueness analysis test failed: {e}")
    
    def test_analyze_consistency(self, analyzer_config, sample_csv_data):
        """Test consistency analysis"""
        try:
            analyzer = CSVQualityAnalyzer(analyzer_config)
            
            if hasattr(analyzer, 'analyze_consistency'):
                consistency_results = analyzer.analyze_consistency(sample_csv_data)
                
                assert isinstance(consistency_results, dict)
                
                # Should contain consistency metrics
                expected_fields = ['format_consistency', 'value_consistency', 'pattern_violations']
                for field in expected_fields:
                    if field in consistency_results:
                        assert field in consistency_results
                        
            else:
                pytest.skip("analyze_consistency method not found")
                
        except Exception as e:
            pytest.skip(f"Consistency analysis test failed: {e}")
    
    def test_generate_quality_report(self, analyzer_config, sample_csv_data):
        """Test quality report generation"""
        try:
            analyzer = CSVQualityAnalyzer(analyzer_config)
            
            if hasattr(analyzer, 'generate_quality_report'):
                quality_report = analyzer.generate_quality_report(sample_csv_data)
                
                assert isinstance(quality_report, dict)
                
                # Should contain comprehensive quality metrics
                expected_sections = ['completeness', 'validity', 'uniqueness', 'consistency', 'overall_score']
                for section in expected_sections:
                    if section in quality_report:
                        assert section in quality_report
                        
                # Overall score should be numeric
                if 'overall_score' in quality_report:
                    assert isinstance(quality_report['overall_score'], (int, float))
                    assert 0 <= quality_report['overall_score'] <= 100
                    
            else:
                pytest.skip("generate_quality_report method not found")
                
        except Exception as e:
            pytest.skip(f"Quality report generation test failed: {e}")
    
    def test_detect_outliers(self, analyzer_config, sample_csv_data):
        """Test outlier detection"""
        try:
            analyzer = CSVQualityAnalyzer(analyzer_config)
            
            if hasattr(analyzer, 'detect_outliers'):
                outliers = analyzer.detect_outliers(sample_csv_data)
                
                assert isinstance(outliers, dict)
                
                # Should contain outlier information for numeric columns
                for column in sample_csv_data.select_dtypes(include=['number']).columns:
                    if column in outliers:
                        column_outliers = outliers[column]
                        assert isinstance(column_outliers, (list, dict))
                        
            else:
                pytest.skip("detect_outliers method not found")
                
        except Exception as e:
            pytest.skip(f"Outlier detection test failed: {e}")
    
    def test_calculate_quality_score(self, analyzer_config):
        """Test quality score calculation"""
        try:
            analyzer = CSVQualityAnalyzer(analyzer_config)
            
            if hasattr(analyzer, 'calculate_quality_score'):
                # Mock quality metrics
                quality_metrics = {
                    'completeness': {'score': 85},
                    'validity': {'score': 90},
                    'uniqueness': {'score': 95},
                    'consistency': {'score': 80}
                }
                
                overall_score = analyzer.calculate_quality_score(quality_metrics)
                
                assert isinstance(overall_score, (int, float))
                assert 0 <= overall_score <= 100
                
            else:
                pytest.skip("calculate_quality_score method not found")
                
        except Exception as e:
            pytest.skip(f"Quality score calculation test failed: {e}")
    
    def test_get_quality_recommendations(self, analyzer_config, sample_csv_data):
        """Test quality improvement recommendations"""
        try:
            analyzer = CSVQualityAnalyzer(analyzer_config)
            
            if hasattr(analyzer, 'get_quality_recommendations'):
                recommendations = analyzer.get_quality_recommendations(sample_csv_data)
                
                assert isinstance(recommendations, list)
                
                # Each recommendation should be a dict with relevant fields
                for recommendation in recommendations:
                    if isinstance(recommendation, dict):
                        expected_fields = ['issue_type', 'description', 'severity']
                        for field in expected_fields:
                            if field in recommendation:
                                assert isinstance(recommendation[field], str)
                                
            else:
                pytest.skip("get_quality_recommendations method not found")
                
        except Exception as e:
            pytest.skip(f"Quality recommendations test failed: {e}")