"""
Pytest configuration and fixtures for utils_tests
"""
import pytest
import sys
import os
from pathlib import Path
from unittest.mock import Mo<PERSON>, AsyncMock, MagicMock
import tempfile
import json

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

@pytest.fixture
def temp_dir():
    """Create a temporary directory for tests"""
    with tempfile.TemporaryDirectory() as tmpdir:
        yield tmpdir

@pytest.fixture
def mock_vector_db():
    """Mock vector database for testing"""
    vector_db = Mock()
    vector_db.get_collection_stats = Mock(return_value={
        'cpg_schemas': {'document_count': 100},
        'cpg_content': {'document_count': 50},
        'cpg_domain_knowledge': {'document_count': 75}
    })
    vector_db.query_collection = Mock(return_value={
        'count': 2,
        'matches': [
            {'similarity_score': 0.85, 'metadata': {'classification': 'syndicated'}},
            {'similarity_score': 0.72, 'metadata': {'classification': 'pos'}}
        ]
    })
    vector_db.__iter__ = Mock(return_value=iter(['cpg_schemas', 'cpg_content']))
    return vector_db

@pytest.fixture
def sample_csv_data():
    """Sample CSV data for testing"""
    return [
        {'product_id': 'P001', 'sales_amount': '100.50', 'store_id': 'S001', 'date': '2024-01-01'},
        {'product_id': 'P002', 'sales_amount': '200.75', 'store_id': 'S002', 'date': '2024-01-02'},
        {'product_id': 'P003', 'sales_amount': '150.25', 'store_id': 'S001', 'date': '2024-01-03'}
    ]

@pytest.fixture
def sample_config():
    """Sample configuration for testing"""
    return {
        'database': {
            'host': 'localhost',
            'port': 5432,
            'name': 'test_db'
        },
        'vector_db': {
            'enabled': True,
            'similarity_threshold': 0.6
        },
        'agents': {
            'max_workers': 4,
            'timeout': 30
        }
    }