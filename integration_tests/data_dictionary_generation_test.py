import sys
import os
import json
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))


from utils.csv_extractor import CSVParser
from utils.csv_pattern_generator import AdvancedCSVNormalizer,process_csv_files
from utils.csv_schema_generator import IntelligentSchemaGenerator,generate_intelligent_schema
from utils.simple_schema_detector import SimpleTypeDetector,detect_simple_schema_from_rows
from core.schema_registry.schema_registry_api import SchemaRegistryAPI
from core.config_manager import ConfigManager
from core.dataqualityanalysis.main_analyzer import CSVQualityAnalyzer
from core.validation.validation_rule_manager import ValidationRuleManager
from agents.dictionary_generation_agent import DictionaryGenerationAgent
from agents.schema_enricher_agent import SchemaEnricherAgent
from core.ontology.vector_generator import VectorGenerator

def main():
    # set the pipeline id for this run
    pipeline_id="pipeline-123"
    # Initalize the config manager to read all the required configuration
    config_manager = ConfigManager()

    # Get user intput for the RAW file
    user_input = input("Enter filename: ").strip()
    parser = CSVParser(config_manager)

    # Step 1. Parse and extract few rows of the data
    result = parser.parse_csv_file(user_input,10)
    extracted_data = result['data']

    # Step 2. Generate Data Dictionary
    data_dictionary_agent = DictionaryGenerationAgent(config_manager)
    dictionary_df = data_dictionary_agent.generate_dictionary(extracted_data)
    print(f" Dictionary generated : \n")
    print(dictionary_df)

    # Step 3. Enrich Schema
    schema_enricher_agent = SchemaEnricherAgent(config_manager)
    enriched_df= schema_enricher_agent._process_dataframe(dictionary_df)
    print(f" Schema Enriched : \n")
    print(enriched_df)

    # Step 4. Generate Vector
    vector_generator = VectorGenerator(config_manager)
    enriched_with_vectors = vector_generator.batch_generate_dual_embeddings(enriched_df)
    print("Enriched with Vectors : \n")
    print(enriched_with_vectors)
        
    # Step 5. Get embedding summary
    embedding_summary = vector_generator.get_embedding_summary(enriched_with_vectors)
    print(f"Generated embeddings - Azure OpenAI: {embedding_summary.get('azure_openai_success_rate', 0)}% success, ST: {embedding_summary.get('st_success_rate', 0)}% success")
        
    
if __name__ == "__main__":
    main()