"""
Usage script for SchemaEnricherAgent

This script processes a CSV file to add synonyms and applicable CPG domains
to your database schema dictionary with minimal user interaction.
"""

import os
import sys
from pathlib import Path
import pandas as pd

# Add project root to path (integration_tests is one level down from project root)
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from agents.schema_enricher_agent import SchemaEnricherAgent
from core.config_manager import ConfigManager
from utils.logger import Logger


def main():
    """Main function for schema enrichment"""
    
    # Initialize logger
    logger = Logger.get_logger("schema_enricher")
    
    print("Schema Enricher Agent")
    print("=" * 50)
    print("This tool will add 'synonyms' and 'applicable_domain' columns to your CSV")
    print("Required columns: column_name, description, column_category, table_type")
    print("=" * 50)
    
    try:
        # Get input file
        input_file = get_input_file()
        
        # Get output file
        output_file = get_output_file()
        
        # Initialize and run
        print(f"\nInitializing Schema Enricher Agent...")
        config_manager = ConfigManager()
        agent = SchemaEnricherAgent(config_manager)
        
        print(f"Processing: {input_file}")
        print(f"Output to: {output_file}")
        print("\nStarting enrichment process...")
        
        # Process the file
        enriched_df = agent.enrich_schema(input_file, output_file)
        
        # Show results
        print_results(enriched_df, output_file)
        
        logger.info("Schema enrichment completed successfully")
        
    except KeyboardInterrupt:
        print("\n\nOperation cancelled by user.")
    except Exception as e:
        logger.error(f"Schema enrichment failed: {str(e)}")
        print(f"\nError: {str(e)}")
        print("\nPlease check:")
        print("1. Input file exists and has required columns")
        print("2. Output directory is writable")
        print("3. Azure OpenAI configuration is correct")


def get_input_file():
    """Get and validate input file"""
    while True:
        input_file = input("\nEnter input CSV file path: ").strip()
        
        if not input_file:
            print("Please enter a file path.")
            continue
        
        if not os.path.exists(input_file):
            print(f"File not found: {input_file}")
            continue
        
        # Validate CSV structure
        try:
            df = pd.read_csv(input_file, nrows=1)
            required_columns = ['column_name', 'description', 'column_category', 'table_type']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                print(f"Missing required columns: {missing_columns}")
                print("Required: column_name, description, column_category, table_type")
                continue
                
            return input_file
            
        except Exception as e:
            print(f"Error reading CSV: {str(e)}")
            continue


def get_output_file():
    """Get and validate output file"""
    while True:
        output_file = input("Enter output CSV file path: ").strip()
        
        if not output_file:
            print("Please enter an output file path.")
            continue
        
        # Add .csv extension if missing
        if not output_file.lower().endswith('.csv'):
            output_file += '.csv'
        
        # Create output directory if needed
        output_dir = os.path.dirname(output_file)
        if output_dir and not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir, exist_ok=True)
                print(f"Created directory: {output_dir}")
            except Exception as e:
                print(f"Cannot create directory {output_dir}: {str(e)}")
                continue
        
        return output_file


def print_results(df, output_file):
    """Print summary of results"""
    try:
        total_rows = len(df)
        
        # Count enriched rows
        rows_with_synonyms = 0
        rows_with_domains = 0
        total_synonyms = 0
        total_domains = 0
        
        for _, row in df.iterrows():
            synonyms = row.get('synonyms', [])
            if isinstance(synonyms, str):
                try:
                    import json
                    synonyms = json.loads(synonyms)
                except:
                    synonyms = []
            
            domains = row.get('applicable_domain', [])
            if isinstance(domains, str):
                try:
                    import json
                    domains = json.loads(domains)
                except:
                    domains = []
            
            if len(synonyms) > 0:
                rows_with_synonyms += 1
                total_synonyms += len(synonyms)
            
            if len(domains) > 0:
                rows_with_domains += 1
                total_domains += len(domains)
        
        print(f"\n" + "=" * 50)
        print("ENRICHMENT COMPLETE")
        print("=" * 50)
        print(f"Total rows processed: {total_rows}")
        print(f"Rows with synonyms: {rows_with_synonyms} ({rows_with_synonyms/total_rows*100:.1f}%)")
        print(f"Rows with domains: {rows_with_domains} ({rows_with_domains/total_rows*100:.1f}%)")
        print(f"Total synonyms generated: {total_synonyms}")
        print(f"Total domains mapped: {total_domains}")
        print(f"Average synonyms per row: {total_synonyms/total_rows:.1f}")
        print(f"Average domains per row: {total_domains/total_rows:.1f}")
        print(f"\nOutput saved to: {output_file}")
        
        # Show sample of first 3 rows
        print(f"\nSample results (first 3 rows):")
        print("-" * 50)
        
        for idx, row in df.head(3).iterrows():
            print(f"\nRow {idx + 1}: {row['column_name']}")
            
            # Parse synonyms
            synonyms = row.get('synonyms', [])
            if isinstance(synonyms, str):
                try:
                    import json
                    synonyms = json.loads(synonyms)
                except:
                    synonyms = []
            
            # Parse domains  
            domains = row.get('applicable_domain', [])
            if isinstance(domains, str):
                try:
                    import json
                    domains = json.loads(domains)
                except:
                    domains = []
            
            print(f"  Synonyms: {synonyms}")
            print(f"  Domains: {domains}")
        
        print(f"\nSUCCESS: Enriched schema saved to {output_file}")
        
    except Exception as e:
        print(f"Error displaying results: {str(e)}")
        print(f"But enrichment completed and saved to: {output_file}")


if __name__ == "__main__":
    main()