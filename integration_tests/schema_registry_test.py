import sys
import os
import json
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))


from utils.csv_extractor import CSVParser
from utils.csv_pattern_generator import AdvancedCSVNormalizer,process_csv_files
from utils.csv_schema_generator import IntelligentSchemaGenerator,generate_intelligent_schema
from utils.simple_schema_detector import SimpleTypeDetector,detect_simple_schema_from_rows
from core.schema_registry.schema_registry_api import SchemaRegistryAPI
from core.config_manager import ConfigManager
from core.dataqualityanalysis.main_analyzer import CSVQualityAnalyzer
from core.validation.validation_rule_manager import ValidationRuleManager

def main():
    # set the pipeline id for this run
    pipeline_id="pipeline-123"
    # Initalize the config manager to read all the required configuration
    config_manager = ConfigManager()

    # Get user intput for the RAW file
    user_input = input("Enter filename: ").strip()
    validation_rules = input("Enter validation rules csv : ").strip()
    parser = CSVParser(config_manager)

    # Parse and extract few rows of the data
    result = parser.parse_csv_file(user_input)
    extracted_data = result['data']

    # Normalize the file name
    filename = os.path.basename(user_input)
    result = process_csv_files(filename)
    normalized_filename = result.normalized

    # schema = generate_intelligent_schema(normalized_filename,extracted_data)
    # print(json.dumps(schema, indent=2))

    # Detect schema from the sample rows
    schema = detect_simple_schema_from_rows(normalized_filename,extracted_data)
    print(json.dumps(schema, indent=2))

    # Initialize the schema registry API
    schema_registry_api = SchemaRegistryAPI(config_manager)
    try:
        # Register the schema and check for any drift
        result = schema_registry_api.register_schema(pipeline_id=pipeline_id,
            file_name=normalized_filename,
            schema=schema,
            created_by="intergration_test")
        
        print(f"   Status: {result['status']}")
        print(f"   Version: {result['versions']['current']}")
        print(f"   Message: {result['message']}")

         # Retrieve the schema
        print("\n2. Retrieving registered schema...")
        schema_info = schema_registry_api.get_schema("pipeline-123", normalized_filename)
        print(f"   Current version: {schema_info['version']}")
        print(f"   Created by: {schema_info['created_by']}")
        print(f"   Schema columns: {list(schema_info['schema']['schema'].keys())}")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        schema_registry_api.close()
    
    print ("\n Performing data quality analysis...")
    # Perform data quality analysis
    analyzer = CSVQualityAnalyzer()
    results = analyzer.analyze_file(user_input)
    
    if results['success']:
        print("\n Analysis completed successfully!")
    else:
        print(f"\n Analysis failed: {results.get('error', 'Unknown error')}")
    
    print("Registering validation rules for the file")
    validation_manager = ValidationRuleManager()
    print("Check if the validation rule already exist")
    validation_check = validation_manager.check_validation_rules_exist(pipeline_id=pipeline_id,normalized_file_name=normalized_filename)
    if not validation_check:
        validation_registration_result = validation_manager.define_validation_rules(csv_file_path=validation_rules,pipeline_id=pipeline_id,normalized_file_name=normalized_filename)
        print("Validation rule registration result:\n",validation_registration_result)
    else:
        print("Validation rule already exist, skipping registration...")

    print("Validating the file against the rule defined")
    validation_result = validation_manager.validate_file(pipeline_id=pipeline_id,file_path=user_input,normalized_file_name=normalized_filename)
    print("Validation Result:\n", json.dumps(validation_result,indent=2))

    
    
if __name__ == "__main__":
    main()