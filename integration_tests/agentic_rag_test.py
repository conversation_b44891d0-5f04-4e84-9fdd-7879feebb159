import asyncio
import sys,os
from pathlib import Path

project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from utils.csv_extractor import CSVParser
from utils.csv_pattern_generator import AdvancedCSVNormalizer,process_csv_files
from utils.csv_schema_generator import IntelligentSchemaGenerator,generate_intelligent_schema
from utils.simple_schema_detector import SimpleTypeDetector,detect_simple_schema_from_rows
from core.schema_registry.schema_registry_api import SchemaRegistryAPI
from core.config_manager import ConfigManager
from core.dataqualityanalysis.main_analyzer import CSVQualityAnalyzer
from core.validation.validation_rule_manager import ValidationRuleManager
from agents.dictionary_generation_agent import DictionaryGenerationAgent
from agents.schema_enricher_agent import SchemaEnricherAgent
from core.ontology.vector_generator import VectorGenerator
from agents.matcher_agent import MatcherAgent
from agents.agentic_system import AgenticClassificationSystem, ClassificationRequest
from utils.column_standardizer import standardize_columns
from utils.csv_dataframe_convertor import csv_to_dataframe

async def classify_file(filepath,config_manager,source_df):
    # Initialize system
    system = AgenticClassificationSystem()
    
    # Create classification request
    request = ClassificationRequest(
        file_path=filepath,
        user_context={
            "source": "api_call",
            "business_unit": "rgm_analytics"
        },
        processing_options={
            "include_ai_enhancement": True,
            "detailed_reasoning": True
        }
    )
    
    # Classify file
    response = await system.classify_file(request)
    
    # Process results
    print(f"Classification: {response.classification}")
    print(f"Confidence: {response.confidence:.2%}")
    print(f"Reasoning: {response.reasoning}")
    
    # Show agent details
    agent_details = response.agent_details
    print(f"Consensus: {agent_details['consensus_level']:.2%}")
    print(f"Agents: {len(agent_details['participating_agents'])}")

    # Match with target ontology
    matcher_agent = MatcherAgent(config_manager)
    matcher_df = matcher_agent.match_schemas(source_df,response.classification)


    mapping_df= matcher_df.loc[matcher_df.groupby('source_column')['confidence_score'].idxmax()]
    standarized_result = standardize_columns(filepath,mapping_df)
    print(standarized_result)

    # Shutdown
    system.shutdown()

async def main():
    # set the pipeline id for this run
    pipeline_id="pipeline-123"
    # Initalize the config manager to read all the required configuration
    config_manager = ConfigManager()
    
    user_input = os.path.join(project_root,'data/sample.csv')
    parser = CSVParser(config_manager)

    # Step 1. Parse and extract few rows of the data
    result = parser.parse_csv_file(user_input,10)
    extracted_data = result['data']

    # Step 2. Generate Data Dictionary
    data_dictionary_agent = DictionaryGenerationAgent(config_manager)
    dictionary_df = data_dictionary_agent.generate_dictionary(extracted_data)
    print(f" Dictionary generated : \n")
    print(dictionary_df)

    # Step 3. Enrich Schema
    schema_enricher_agent = SchemaEnricherAgent(config_manager)
    enriched_df= schema_enricher_agent._process_dataframe(dictionary_df)
    print(f" Schema Enriched : \n")
    print(enriched_df)

    # Step 4. Generate Vector
    vector_generator = VectorGenerator(config_manager)
    enriched_with_vectors = vector_generator.batch_generate_dual_embeddings(enriched_df)
    print("Enriched with Vectors : \n")
    print(enriched_with_vectors.columns.to_list)
        
    # Step 5. Get embedding summary
    embedding_summary = vector_generator.get_embedding_summary(enriched_with_vectors)
    print(f"Generated embeddings - Azure OpenAI: {embedding_summary.get('azure_openai_success_rate', 0)}% success, ST: {embedding_summary.get('st_success_rate', 0)}% success")

    # Step 6. Run classification and match with target ontology
    await classify_file(user_input,config_manager,source_df=enriched_with_vectors)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except Exception as e:
        print(f"Error: {e}")