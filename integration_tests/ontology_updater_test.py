"""
Simple Integration Test for Ontology Updater
Tests complete workflow from schema file to Neo4j knowledge graph
"""

import os
import sys
from pathlib import Path

# Add project root to path (integration_tests is one level down from project root)
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.config_manager import ConfigManager
from core.ontology.ontology_usage import OntologyUsage


def main():
    """Simple integration test - asks for filename and table name, then displays results"""
    print("Schema to Neo4j Ontology Integration Test")
    print("=" * 50)
    print("This test will:")
    print("1. Enrich your schema with synonyms and CPG domains")
    print("2. Generate dual vector embeddings (OpenAI + Sentence Transformers)")
    print("3. Update Neo4j knowledge graph with table and column nodes")
    print("4. Display complete results from the graph database")
    print("=" * 50)
    
    # Get filename input
    schema_file = input("\nEnter schema CSV file path: ").strip()
    
    # Remove quotes if user added them
    schema_file = schema_file.strip('"\'')
    
    # Validate file exists
    if not os.path.exists(schema_file):
        print(f"File not found: {schema_file}")
        print("\nPlease ensure:")
        print("- File path is correct")
        print("- File exists and is accessible")
        print("- CSV has required columns: column_name, description, column_category, table_type")
        return
    
    print(f"File found: {schema_file}")
    
    # Get table name input
    table_name = input("Enter table name: ").strip()
    
    # Remove quotes and validate table name
    table_name = table_name.strip('"\'')
    
    if not table_name:
        print("Table name cannot be empty")
        return
    
    # Clean table name (remove special characters, replace spaces/hyphens with underscores)
    clean_table_name = table_name.replace(' ', '_').replace('-', '_')
    clean_table_name = ''.join(c for c in clean_table_name if c.isalnum() or c == '_')
    
    # Ensure it starts with a letter (valid identifier)
    if clean_table_name and clean_table_name[0].isdigit():
        clean_table_name = 'table_' + clean_table_name
    
    if not clean_table_name:
        print("Invalid table name. Please use only letters, numbers, and underscores.")
        return
    
    if clean_table_name != table_name:
        print(f"Table name cleaned: '{table_name}' -> '{clean_table_name}'")
        table_name = clean_table_name
    
    print(f"Table name: {table_name}")
    
    try:
        print("\nStarting complete workflow...")
        
        # Initialize configuration and usage orchestrator
        config_manager = ConfigManager()
        
        # Use context manager for proper cleanup
        with OntologyUsage(config_manager) as usage:
            # Process the complete workflow with user-provided table name
            processed_table_name = process_schema_with_table_name(usage, schema_file, table_name)
            
            # Display comprehensive results
            print(f"\nDisplaying results from Neo4j...")
            usage.display_processed_results(processed_table_name)
            
            # Get and display workflow summary
            summary = usage.get_workflow_summary(processed_table_name)
            if summary:
                print(f"\nWORKFLOW SUMMARY:")
                print(f"{'='*50}")
                print(f"Table processed: {summary.get('table_name')}")
                print(f"Columns processed: {summary.get('total_columns')}")
                print(f"Azure OpenAI embeddings: {summary.get('azure_openai_embeddings_created')}")
                print(f"Sentence Transformer embeddings: {summary.get('st_embeddings_created')}")
                print(f"Processing completed at: {summary.get('updated_at')}")
        
        print(f"\nIntegration test completed successfully!")
        print(f"Table '{processed_table_name}' is now available in your Neo4j knowledge graph.")
        
    except KeyboardInterrupt:
        print("\n\nTest cancelled by user.")
        
    except Exception as e:
        print(f"\nIntegration test failed: {str(e)}")
        print("\nTroubleshooting:")
        print("1. Check config/config.json has correct Neo4j and Azure OpenAI configurations")
        print("2. Ensure Neo4j database is running and accessible")
        print("3. Verify Azure OpenAI API key and endpoint are valid")
        print("4. Check CSV file format and required columns")
        print("5. Review logs for detailed error information")


def process_schema_with_table_name(usage, schema_file_path: str, table_name: str) -> str:
    """Process schema file with user-provided table name"""
    try:
        usage.logger.info(f"Starting workflow for: {schema_file_path} with table: {table_name}")
        
        print(f"Using table name: {table_name}")
        
        # Step 1: Schema Enrichment
        print("Step 1: Enriching schema with synonyms and domains...")
        enriched_df = usage.schema_enricher.enrich_schema(schema_file_path)
        print(f"Enriched {len(enriched_df)} columns with synonyms and domains")
        
        # Step 2: Vector Generation
        print("Step 2: Generating dual vector embeddings...")
        enriched_with_vectors = usage.vector_generator.batch_generate_dual_embeddings(enriched_df)
        
        # Get embedding summary
        embedding_summary = usage.vector_generator.get_embedding_summary(enriched_with_vectors)
        print(f"Generated embeddings - Azure OpenAI: {embedding_summary.get('azure_openai_success_rate', 0)}% success, ST: {embedding_summary.get('st_success_rate', 0)}% success")
        
        # Step 3: Ontology Update
        print("Step 3: Updating Neo4j knowledge graph...")
        usage.ontology_updater.update_table_ontology(table_name, enriched_with_vectors)
        print(f"Updated Neo4j ontology for table: {table_name}")
        
        # Step 4: Create indexes for performance
        try:
            usage.ontology_updater.create_indexes()
            print("Created/verified Neo4j indexes")
        except Exception as e:
            print(f"Index creation warning: {str(e)}")
        
        print("Complete workflow finished successfully!")
        return table_name
        
    except Exception as e:
        usage.logger.error(f"Workflow failed: {str(e)}")
        print(f"❌ Workflow failed: {str(e)}")
        raise


def validate_config():
    """Validate configuration before running test"""
    try:
        config_manager = ConfigManager()
        
        # Test Neo4j config
        neo4j_config = config_manager.get_neo4j_config()
        print(f"Neo4j config loaded: {neo4j_config.uri}")
        
        # Test Azure OpenAI config
        azure_openai_config = config_manager.get_azure_openai_config()
        print(f"Azure OpenAI config loaded: {azure_openai_config.deployment}")
        
        # Test Sentence Transformer config
        st_config = config_manager.get_sentence_transformer_config()
        print(f"Sentence Transformer config: {st_config.model}")
        
        return True
        
    except Exception as e:
        print(f"Configuration validation failed: {str(e)}")
        print("\nPlease check your config/config.json file and ensure it has:")
        print("- neo4j section with uri, username, password, database")
        print("- azure_openai section with api_key, endpoint, deployment, api_version")
        print("- embeddings.sentence_transformer section with model, dimensions, device, batch_size")
        return False


if __name__ == "__main__":
    print("Validating configuration...")
    if validate_config():
        print("Configuration validation passed\n")
        main()
    else:
        print("Configuration validation failed. Please fix config before running test.")