import os,sys
from pathlib import Path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from utils.column_standardizer import standardize_columns
from utils.csv_dataframe_convertor import csv_to_dataframe

def main():
    input_file = os.path.join(project_root,'data/sample.csv')
    mapping_file = os.path.join(project_root,'data/sample_mapping.csv')
    
    mapping_df = csv_to_dataframe(mapping_file)
    standarized_result = standardize_columns(input_file,mapping_df)
    print(standarized_result)
    

if __name__ == "__main__":
    main()