["unit_tests/test_specialized_agents.py::TestContentAnalyzerAgent::test_apply_content_mutual_exclusion", "unit_tests/test_specialized_agents.py::TestContentAnalyzerAgent::test_calculate_completeness_score", "unit_tests/test_specialized_agents.py::TestContentAnalyzerAgent::test_detect_business_terms", "unit_tests/test_specialized_agents.py::TestContentAnalyzerAgent::test_extract_content_features", "unit_tests/test_specialized_agents.py::TestDomainExpertAgent::test_apply_domain_mutual_exclusion", "unit_tests/test_specialized_agents.py::TestDomainExpertAgent::test_apply_pos_rules", "unit_tests/test_specialized_agents.py::TestDomainExpertAgent::test_apply_syndicated_rules", "unit_tests/test_specialized_agents.py::TestDomainExpertAgent::test_perform_domain_analysis", "unit_tests/test_specialized_agents.py::TestIntegration::test_agent_consensus_scenario", "unit_tests/test_specialized_agents.py::TestIntegration::test_error_handling", "unit_tests/test_specialized_agents.py::TestSchemaAnalyzerAgent::test_analyze_file", "unit_tests/test_specialized_agents.py::TestSchemaAnalyzerAgent::test_build_search_query", "unit_tests/test_specialized_agents.py::TestSchemaAnalyzerAgent::test_extract_schema_features", "unit_tests/test_specialized_agents.py::TestVectorDBEnhancedAgent::test_agent_initialization_with_vector_db", "unit_tests/test_specialized_agents.py::TestVectorDBEnhancedAgent::test_agent_initialization_without_vector_db", "unit_tests/test_specialized_agents.py::TestVectorDBEnhancedAgent::test_search_vector_db_disabled", "unit_tests/test_specialized_agents.py::TestVectorDBEnhancedAgent::test_search_vector_db_empty_query", "unit_tests/test_specialized_agents.py::TestVectorDBEnhancedAgent::test_search_vector_db_success"]