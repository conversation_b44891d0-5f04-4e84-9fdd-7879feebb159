{"unit_tests/test_specialized_agents.py::TestVectorDBEnhancedAgent::test_agent_initialization_with_vector_db": true, "unit_tests/test_specialized_agents.py::TestVectorDBEnhancedAgent::test_agent_initialization_without_vector_db": true, "unit_tests/test_specialized_agents.py::TestVectorDBEnhancedAgent::test_search_vector_db_success": true, "unit_tests/test_specialized_agents.py::TestVectorDBEnhancedAgent::test_search_vector_db_disabled": true, "unit_tests/test_specialized_agents.py::TestVectorDBEnhancedAgent::test_search_vector_db_empty_query": true, "unit_tests/test_specialized_agents.py::TestSchemaAnalyzerAgent::test_extract_schema_features": true, "unit_tests/test_specialized_agents.py::TestSchemaAnalyzerAgent::test_analyze_file": true, "unit_tests/test_specialized_agents.py::TestContentAnalyzerAgent::test_extract_content_features": true, "unit_tests/test_specialized_agents.py::TestContentAnalyzerAgent::test_apply_content_mutual_exclusion": true, "unit_tests/test_specialized_agents.py::TestDomainExpertAgent::test_perform_domain_analysis": true, "unit_tests/test_specialized_agents.py::TestDomainExpertAgent::test_apply_domain_mutual_exclusion": true, "unit_tests/test_specialized_agents.py::TestIntegration::test_agent_consensus_scenario": true, "unit_tests/test_specialized_agents.py::TestIntegration::test_error_handling": true}