"""
Command Line Interface for Agentic CPG File Classification System
Provides interactive and batch processing capabilities
"""

import asyncio
import argparse
import json
import os
import sys
import time
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime
import csv

# Add project root to Python path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from agents.agentic_system import AgenticClassificationSystem, ClassificationRequest, ClassificationResponse
from core.config_manager import ConfigManager

class AgenticCLI:
    """Command Line Interface for Agentic Classification System"""
    
    def __init__(self):
        """Initialize CLI"""
        self.system = None
        self.config_manager = ConfigManager()
        self.setup_logging()
        
        # CLI state
        self.session_results = []
        self.batch_mode = False
        self.interactive_mode = True
    
    def setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(project_root / 'agentic_classification.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    async def initialize_system(self):
        """Initialize the agentic classification system"""
        try:
            print("Initializing Agentic Classification System...")
            self.system = AgenticClassificationSystem()
            print("System initialized successfully!")
            return True
        except Exception as e:
            print(f"Failed to initialize system: {e}")
            self.logger.error(f"System initialization failed: {e}")
            return False
    
    def print_banner(self):
        """Print CLI banner"""
        banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║               CPG Agentic Classification System              ║
    ║                                                              ║
    ║   * AI-Powered File Classification for CPG Domain            ║
    ║   * Supports: Syndicated, POS, Product, Margin & More        ║
    ║   * Multi-Agent Voting & Azure OpenAI Integration            ║
    ╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
    
    def print_help(self):
        """Print available commands"""
        help_text = """
    Available Commands:
    ═══════════════════════
    
    File Operations:
      classify <file_path>     - Classify a single file
      batch <directory>        - Classify all files in directory
      upload <file> <type>     - Upload training example
    
    Analysis & Stats:
      stats                    - Show system statistics
      history [limit]          - Show classification history
      feedback                 - Show feedback statistics
    
    System Management:
      test                     - Test system connections
      config                   - Show configuration
      agents                   - Show agent status
    
    Interactive:
      help                     - Show this help
      quit, exit, q            - Exit the system
    
    Examples:
      classify data/sample.csv
      batch data/incoming/
      upload data/training/nielsen.csv syndicated
      stats
        """
        print(help_text)
    
    async def run_interactive(self):
        """Run interactive CLI mode"""
        self.print_banner()
        
        if not await self.initialize_system():
            return
        
        print("\n Welcome to Interactive Mode! Type 'help' for commands.\n")
        
        while self.interactive_mode:
            try:
                command = input("agentic> ").strip()
                
                if not command:
                    continue
                
                await self.process_command(command)
                
            except KeyboardInterrupt:
                break
            except EOFError:
                break
            except Exception as e:
                print(f"Error: {e}")
                self.logger.error(f"Command processing error: {e}")
        
        if self.system:
            self.system.shutdown()
    
    async def process_command(self, command: str):
        """Process a CLI command"""
        parts = command.lower().split()
        
        if not parts:
            return
        
        cmd = parts[0]
        args = parts[1:] if len(parts) > 1 else []
        
        # Command routing
        if cmd in ['help', 'h']:
            self.print_help()
        
        elif cmd in ['quit', 'exit', 'q']:
            self.interactive_mode = False
        
        elif cmd == 'classify':
            await self.cmd_classify(args)
        
        elif cmd == 'batch':
            await self.cmd_batch(args)
        
        elif cmd == 'upload':
            await self.cmd_upload(args)
        
        elif cmd == 'stats':
            await self.cmd_stats()
        
        elif cmd == 'history':
            await self.cmd_history(args)
        
        elif cmd == 'feedback':
            await self.cmd_feedback()
        
        elif cmd == 'test':
            await self.cmd_test()
        
        elif cmd == 'config':
            await self.cmd_config()
        
        elif cmd == 'agents':
            await self.cmd_agents()
        
        else:
            print(f"Unknown command: {cmd}. Type 'help' for available commands.")
    
    async def cmd_classify(self, args: List[str]):
        """Handle classify command"""
        if not args:
            print(" Usage: classify <file_path>")
            return
        
        file_path = args[0]
        
        if not os.path.exists(file_path):
            print(f"File not found: {file_path}")
            return
        
        print(f"\nClassifying: {file_path}")
        print("Processing...")
        
        start_time = time.time()
        
        try:
            # Create classification request
            request = ClassificationRequest(
                file_path=file_path,
                user_context={
                    "source": "cli_interactive",
                    "session_id": datetime.now().strftime("%Y%m%d_%H%M%S")
                },
                processing_options={
                    "include_ai_enhancement": True,
                    "detailed_reasoning": True
                }
            )
            
            # Classify file
            response = await self.system.classify_file(request)
            
            # Display results
            await self.display_classification_result(response, time.time() - start_time)
            
            # Store result
            self.session_results.append({
                "file_path": file_path,
                "response": response,
                "timestamp": datetime.now().isoformat()
            })
            
            # Ask for feedback if classification was successful
            if response.success and response.classification != "unknown":
                await self.collect_user_feedback(response)
            
        except Exception as e:
            print(f"Classification failed: {e}")
            self.logger.error(f"Classification error: {e}")
    
    async def display_classification_result(self, response: ClassificationResponse, processing_time: float):
        """Display classification results in a nice format"""
        print("\n" + "="*60)
        print("CLASSIFICATION RESULTS")
        print("="*60)
        
        if response.success:
            # Main result
            confidence_emoji = "🟢" if response.confidence >= 0.8 else "🟡" if response.confidence >= 0.6 else "🔴"
            print(f"Classification: {response.classification.upper()}")
            print(f"{confidence_emoji} Confidence: {response.confidence:.2%}")
            print(f" Processing Time: {processing_time:.2f}s")
            
            # Reasoning
            print(f"\n Reasoning:")
            print(f"   {response.reasoning}")
            
            # Agent details
            agent_details = response.agent_details
            if agent_details:
                consensus = agent_details.get("consensus_level", 0.0)
                participating = len(agent_details.get("participating_agents", []))
                
                print(f"\n🤖 Agent Analysis:")
                print(f"   Consensus Level: {consensus:.2%}")
                print(f"   Participating Agents: {participating}")
                
                # Individual agent decisions
                individual_decisions = agent_details.get("individual_decisions", [])
                if individual_decisions:
                    print(f"\n   Individual Agent Votes:")
                    for decision in individual_decisions:
                        agent_type = decision.get("agent_type", "unknown")
                        classification = decision.get("classification", "unknown")
                        confidence = decision.get("confidence", 0.0)
                        print(f"     {agent_type}: {classification} ({confidence:.2%})")
            
            # Recommendations
            if response.recommendations:
                print(f"\n💡 Recommendations:")
                for i, rec in enumerate(response.recommendations, 1):
                    print(f"   {i}. {rec}")
            
            # Quality assessment
            quality_score = agent_details.get("quality_score", 0.0) if agent_details else 0.0
            print(f"\nQuality Score: {quality_score:.2%}")
            
        else:
            print(f" Classification failed: {response.error_message}")
        
        print("="*60 + "\n")
    
    async def collect_user_feedback(self, response: ClassificationResponse):
        """Collect user feedback on classification result"""
        try:
            print("\n Feedback (optional - press Enter to skip):")
            
            # Ask if classification is correct
            correct = input(f"   Is '{response.classification}' correct? (y/n/skip): ").strip().lower()
            
            if correct == 'skip' or correct == '':
                return
            
            feedback_data = {}
            
            if correct == 'n':
                # Get correct classification
                print("\n   What is the correct classification?")
                cpg_types = [
                    "syndicated", "pos", "product_attribute", "depletion_data",
                    "margin_data", "numerator_intel", "trace_data", "product_mapping",
                    "geography_mapping", "pvp_mapping", "national_accounts", 
                    "pos_fact", "dimension_data"
                ]
                
                for i, data_type in enumerate(cpg_types, 1):
                    print(f"     {i:2d}. {data_type}")
                
                choice = input("   Enter number or type name: ").strip()
                
                try:
                    if choice.isdigit():
                        choice_idx = int(choice) - 1
                        if 0 <= choice_idx < len(cpg_types):
                            correct_classification = cpg_types[choice_idx]
                        else:
                            print("   Invalid choice")
                            return
                    else:
                        correct_classification = choice.lower()
                        if correct_classification not in cpg_types:
                            print("   Invalid classification type")
                            return
                    
                    feedback_data = {
                        "correct_classification": correct_classification,
                        "reasoning": input("   Why is this correct? (optional): ").strip()
                    }
                    
                except (ValueError, IndexError):
                    print("   Invalid input")
                    return
                    
            elif correct == 'y':
                feedback_data = {"confirm_classification": True}
            
            # Get satisfaction score
            try:
                satisfaction = input("   Satisfaction (1-5, optional): ").strip()
                if satisfaction:
                    feedback_data["satisfaction"] = float(satisfaction)
            except ValueError:
                pass
            
            # Get suggestions
            suggestions = input("   Any suggestions for improvement? (optional): ").strip()
            if suggestions:
                feedback_data["suggestions"] = [suggestions]
            
            # Process feedback through feedback collector agent
            self.logger.info("Calling feedback agent...")
            if hasattr(self.system.supervisor_agent, 'classification_agents'):
                feedback_agent = None
                for agent in self.system.supervisor_agent.classification_agents.values():
                    if hasattr(agent, 'collect_user_feedback'):
                        feedback_agent = agent
                        break
                if feedback_agent:
                    result = await feedback_agent.collect_user_feedback(
                        response.__dict__, feedback_data
                    )
                    if result.get("success"):
                        print("Feedback recorded successfully!")
                    else:
                        print("Failed to record feedback")
            else:
                 self.logger.info("No feedback agent found...")
            
        except Exception as e:
            print(f"Error collecting feedback: {e}")
    
    async def cmd_batch(self, args: List[str]):
        """Handle batch processing command"""
        if not args:
            print("Usage: batch <directory_path>")
            return
        
        directory = args[0]
        
        if not os.path.isdir(directory):
            print(f"Directory not found: {directory}")
            return
        
        # Find supported files
        supported_extensions = ['.csv', '.xlsx', '.xls', '.txt', '.pdf']
        files = []
        
        for ext in supported_extensions:
            files.extend(Path(directory).glob(f"*{ext}"))
            files.extend(Path(directory).glob(f"**/*{ext}"))  # Recursive
        
        if not files:
            print(f"No supported files found in: {directory}")
            return
        
        print(f"\n📁 Found {len(files)} files for batch processing")
        proceed = input("   Proceed? (y/n): ").strip().lower()
        
        if proceed != 'y':
            print("   Batch processing cancelled")
            return
        
        # Process files
        batch_results = []
        print(f"\n⏳ Processing {len(files)} files...")
        
        for i, file_path in enumerate(files, 1):
            print(f"\n[{i}/{len(files)}] {file_path.name}")
            
            try:
                request = ClassificationRequest(
                    file_path=str(file_path),
                    user_context={
                        "source": "cli_batch",
                        "batch_id": datetime.now().strftime("%Y%m%d_%H%M%S")
                    }
                )
                
                response = await self.system.classify_file(request)
                batch_results.append({
                    "file": str(file_path),
                    "classification": response.classification,
                    "confidence": response.confidence,
                    "success": response.success,
                    "error": response.error_message if not response.success else None
                })
                
                # Quick status
                conf_str = f"({response.confidence:.2%})" if response.success else ""
                print(f"{response.classification} {conf_str}")
                
            except Exception as e:
                print(f"Error: {e}")
                batch_results.append({
                    "file": str(file_path),
                    "classification": "error",
                    "confidence": 0.0,
                    "success": False,
                    "error": str(e)
                })
        
        # Save batch results
        await self.save_batch_results(batch_results, directory)
        
        # Summary
        successful = sum(1 for r in batch_results if r["success"])
        print(f"\nBatch Processing Complete!")
        print(f"   Successful: {successful}/{len(batch_results)}")
        print(f"   Results saved to: batch_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
    
    async def save_batch_results(self, results: List[Dict], directory: str):
        """Save batch processing results to CSV"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"batch_results_{timestamp}.csv"
        
        try:
            with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['file', 'classification', 'confidence', 'success', 'error']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for result in results:
                    writer.writerow(result)
            
            print(f"Results saved to: {output_file}")
            
        except Exception as e:
            print(f"Failed to save results: {e}")
    
    async def cmd_upload(self, args: List[str]):
        """Handle upload training example command"""
        if len(args) < 2:
            print("Usage: upload <file_path> <classification_type>")
            print("   Classification types: syndicated, pos, product_attribute, etc.")
            return
        
        file_path = args[0]
        classification_type = args[1]
        description = " ".join(args[2:]) if len(args) > 2 else ""
        
        if not os.path.exists(file_path):
            print(f"File not found: {file_path}")
            return
        
        print(f"\nUploading training example...")
        print(f"   File: {file_path}")
        print(f"   Type: {classification_type}")
        
        try:
            result = await self.system.add_training_example(
                file_path=file_path,
                correct_classification=classification_type,
                description=description
            )
            
            if result.get("success"):
                print("Training example uploaded successfully!")
                print(f"Document ID: {result.get('document_id')}")
            else:
                print(f"Upload failed: {result.get('message')}")
                
        except Exception as e:
            print(f"Upload error: {e}")
    
    async def cmd_stats(self):
        """Show system statistics"""
        print("\nSystem Statistics")
        print("="*50)
        
        try:
            stats = self.system.get_system_statistics()
            
            # Main stats
            print(f"Total Requests: {stats.get('total_requests', 0)}")
            print(f"Successful Classifications: {stats.get('successful_classifications', 0)}")
            print(f"Success Rate: {stats.get('success_rate', 0.0):.2%}")
            print(f"Average Confidence: {stats.get('avg_confidence', 0.0):.2%}")
            print(f"Average Processing Time: {stats.get('avg_processing_time', 0.0):.2f}s")
            
            # Supervisor stats
            supervisor_stats = stats.get('supervisor_stats', {})
            if supervisor_stats:
                print(f"\nSupervisor Agent:")
                print(f"   Classifications: {supervisor_stats.get('total_classifications', 0)}")
                print(f"   Success Rate: {supervisor_stats.get('success_rate', 0.0):.2%}")
                print(f"   Avg Consensus: {supervisor_stats.get('avg_consensus_level', 0.0):.2%}")
            
            # Agent performance
            agent_performance = supervisor_stats.get('agent_performance', {})
            if agent_performance:
                print(f"\nIndividual Agents:")
                for agent_name, perf in agent_performance.items():
                    decisions = perf.get('decisions_made', 0)
                    avg_conf = perf.get('avg_confidence', 0.0)
                    print(f"   {agent_name}: {decisions} decisions, {avg_conf:.2%} avg confidence")
            
            # Knowledge base stats
            kb_stats = stats.get('knowledge_base_stats', {})
            if kb_stats:
                vector_db = kb_stats.get('vector_database', {})
                print(f"\nKnowledge Base:")
                total_docs = sum(coll.get('document_count', 0) for coll in vector_db.values())
                print(f"Total Documents: {total_docs}")
                for collection, info in vector_db.items():
                    count = info.get('document_count', 0)
                    print(f"   {collection}: {count} documents")
            
        except Exception as e:
            print(f"Failed to get statistics: {e}")
    
    async def cmd_history(self, args: List[str]):
        """Show classification history"""
        limit = 10
        if args and args[0].isdigit():
            limit = int(args[0])
        
        print(f"\nClassification History (last {limit})")
        print("="*60)
        
        try:
            history = await self.system.get_classification_history(limit)
            
            if not history:
                print("No classification history found.")
                return
            
            for i, entry in enumerate(history, 1):
                timestamp = entry.get('timestamp', '')
                if timestamp:
                    # Parse and format timestamp
                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    time_str = dt.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    time_str = 'Unknown'
                
                file_name = entry.get('file_name', 'Unknown')
                classification = entry.get('classification', 'Unknown')
                confidence = entry.get('confidence', 0.0)
                
                print(f"{i:2d}. {time_str}")
                print(f"    📁 {file_name}")
                print(f"    🎯 {classification} ({confidence:.2%})")
                print()
                
        except Exception as e:
            print(f"Failed to get history: {e}")
    
    async def cmd_feedback(self):
        """Show feedback statistics"""
        print("\nFeedback Statistics")
        print("="*40)
        
        try:
            # This would need to be implemented in the feedback collector agent
            print("Feedback statistics not yet implemented.")
            print("Coming soon: User satisfaction, correction patterns, etc.")
            
        except Exception as e:
            print(f"Failed to get feedback stats: {e}")
    
    async def cmd_test(self):
        """Test system connections"""
        print("\nTesting System Connections")
        print("="*40)
        
        try:
            # Test Azure OpenAI
            if self.system.azure_openai:
                print("Testing Azure OpenAI...")
                test_result = await self.system.azure_openai.test_connection()
                status = test_result.get("status", "unknown")
                
                if status == "success":
                    print("Azure OpenAI: Connected")
                else:
                    print(f"Azure OpenAI: {test_result.get('message', 'Failed')}")
            else:
                print("Azure OpenAI: Not configured")
            
            # Test Vector Database
            print("Testing Vector Database...")
            if self.system.vector_db:
                try:
                    stats = self.system.vector_db.get_collection_stats()
                    print("Vector Database: Connected")
                    print(f"Collections: {len(stats)}")
                except Exception:
                    print(" Vector Database: Connection failed")
            else:
                print("Vector Database: Not initialized")
            
            # Test Agents
            print("Testing Agents...")
            if self.system.supervisor_agent:
                active_agents = len([a for a in self.system.supervisor_agent.classification_agents.values() if a.is_active])
                total_agents = len(self.system.supervisor_agent.classification_agents)
                print(f"Agents: {active_agents}/{total_agents} active")
            else:
                print("Agents: Supervisor not initialized")
                
        except Exception as e:
            print(f"Test failed: {e}")
    
    async def cmd_config(self):
        """Show configuration"""
        print("\nSystem Configuration")
        print("="*40)
        
        try:
            config = self.config_manager._config
            
            # Show key configuration items
            print(f"Project Root: {self.config_manager.project_root}")
            
            if 'knowledgebase' in config:
                kb_config = config['knowledgebase']
                print(f"\nKnowledge Base:")
                print(f"   Vector DB: {kb_config.get('vector_db', {}).get('persist_directory', 'Not configured')}")
                
            if 'azure_openai' in config:
                ai_config = config['azure_openai']
                print(f"\nAzure OpenAI:")
                print(f"   Endpoint: {ai_config.get('endpoint', 'Not configured')}")
                print(f"   Deployment: {ai_config.get('deployment', 'Not configured')}")
                
            # Show agentic system config
            agentic_config = config.get('agentic_system', {})
            if agentic_config:
                print(f"\nAgentic System:")
                print(f"   Min Consensus: {agentic_config.get('min_consensus_threshold', 0.4):.2f}")
                print(f"   Min Confidence: {agentic_config.get('min_confidence_threshold', 0.3):.2f}")
                
        except Exception as e:
            print(f"Failed to show config: {e}")
    
    async def cmd_agents(self):
        """Show agent status"""
        print("\nAgent Status")
        print("="*30)
        
        try:
            if not self.system.supervisor_agent:
                print("Supervisor agent not initialized")
                return
            
            print(f"Supervisor Agent: {self.system.supervisor_agent.agent_id}")
            print(f"   Status: {'🟢 Active' if self.system.supervisor_agent.is_active else '🔴 Inactive'}")
            
            print(f"\nClassification Agents:")
            for name, agent in self.system.supervisor_agent.classification_agents.items():
                status = "🟢 Active" if agent.is_active else "🔴 Inactive"
                capabilities = len(agent.get_capabilities())
                decisions = agent.performance_metrics.get('decisions_made', 0)
                print(f"   {name}: {status}")
                print(f"      Capabilities: {capabilities}")
                print(f"      Decisions Made: {decisions}")
                
            # Voting coordinator
            if hasattr(self.system.supervisor_agent, 'voting_coordinator'):
                coordinator = self.system.supervisor_agent.voting_coordinator
                status = "🟢 Active" if coordinator.is_active else "🔴 Inactive"
                print(f"\nVoting Coordinator: {status}")
                
        except Exception as e:
            print(f"Failed to show agents: {e}")

def create_cli_parser():
    """Create command line argument parser"""
    parser = argparse.ArgumentParser(
        description="CPG Agentic File Classification System",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    # Mode selection
    parser.add_argument(
        '--mode', 
        choices=['interactive', 'batch', 'single'],
        default='interactive',
        help='CLI mode (default: interactive)'
    )
    
    # Single file classification
    parser.add_argument(
        '--file',
        help='File to classify (for single mode)'
    )
    
    # Batch processing
    parser.add_argument(
        '--directory',
        help='Directory for batch processing'
    )
    
    # Output options
    parser.add_argument(
        '--output',
        help='Output file for results (CSV format)'
    )
    
    # Configuration
    parser.add_argument(
        '--config',
        help='Configuration file path'
    )
    
    # Logging level
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Logging level'
    )
    
    # Training mode
    parser.add_argument(
        '--upload',
        nargs=2,
        metavar=('FILE', 'TYPE'),
        help='Upload training example: file_path classification_type'
    )
    
    return parser

async def main():
    """Main CLI entry point"""
    parser = create_cli_parser()
    args = parser.parse_args()
    
    # Setup logging level
    logging.getLogger().setLevel(getattr(logging, args.log_level))
    
    cli = AgenticCLI()
    
    if args.mode == 'interactive':
        await cli.run_interactive()
    
    elif args.mode == 'single':
        if not args.file:
            print("Single mode requires --file argument")
            return
        
        if not await cli.initialize_system():
            return
        
        await cli.cmd_classify([args.file])
    
    elif args.mode == 'batch':
        if not args.directory:
            print("Batch mode requires --directory argument")
            return
        
        if not await cli.initialize_system():
            return
        
        await cli.cmd_batch([args.directory])
    
    # Handle upload
    if args.upload:
        if not await cli.initialize_system():
            return
        
        await cli.cmd_upload(args.upload)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n")
    except Exception as e:
        print(f"CLI Error: {e}")
        logging.error(f"CLI Error: {e}")
        sys.exit(1)