"""
Azure OpenAI Integration Service for CPG File Classification
Provides AI-powered insights and reasoning enhancement with enhanced classification logic
"""

import asyncio
import json
import logging
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import sys
from pathlib import Path

from openai import AzureOpenAI

# Add project root to Python path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from core.config_manager import AzureOpenAIConfig

@dataclass
class AIInsight:
    """AI-generated insight about file content"""
    insight_type: str
    content: str
    confidence: float
    reasoning: str
    metadata: Dict[str, Any] = None

@dataclass
class AIClassificationHint:
    """AI-generated classification hint"""
    suggested_type: str
    confidence: float
    supporting_evidence: List[str]
    business_reasoning: str

class AzureOpenAIService:
    """Service for integrating Azure OpenAI capabilities with enhanced classification logic"""
    
    def __init__(self, config: AzureOpenAIConfig):
        """Initialize Azure OpenAI service"""
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.client = None
        self._initialize_client()
        
        # Enhanced CPG domain prompts
        self.domain_prompts = self._load_cpg_prompts()
        
        # Enhanced classification keywords with weights and patterns
        self.classification_keywords = self._load_enhanced_keywords()
        
        # Data type patterns for better recognition
        self.data_type_patterns = self._load_data_type_patterns()
        
        # Performance tracking
        self.api_calls = 0
        self.successful_calls = 0
        self.total_tokens = 0
        self.avg_response_time = 0.0
    
    def _initialize_client(self):
        """Initialize Azure OpenAI client"""
        try:
            self.client = AzureOpenAI(
                api_key=self.config.api_key,
                api_version=self.config.api_version,
                azure_endpoint=self.config.endpoint
            )
            self.logger.info("Azure OpenAI client initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize Azure OpenAI client: {e}")
            raise
    
    def _load_enhanced_keywords(self) -> Dict[str, Dict]:
        """Load enhanced classification keywords with weights and negative keywords"""
        return {
            'syndicated': {
                'primary_keywords': ['nielsen', 'iri', 'symphony', 'circana', 'euromonitor'],
                'secondary_keywords': ['tdp', 'acv', 'velocity', 'units_per_million', 'distribution', 
                                     'all_commodity_volume', 'total_distribution_points', 'market_share',
                                     'category_performance', 'retail_measurement', 'scanner_data'],
                'pattern_keywords': ['weekly_data', 'market_measurement', 'retailer_panel', 
                                   'category_analysis', 'brand_performance'],
                'negative_keywords': ['transaction_id', 'upc_scan', 'checkout', 'register', 'receipt',
                                    'product_description', 'attribute_value', 'hierarchy_level'],
                'weight': 1.0
            },
            'pos': {
                'primary_keywords': ['pos', 'point_of_sale', 'transaction', 'checkout', 'register'],
                'secondary_keywords': ['upc', 'sku', 'barcode', 'scan', 'receipt', 'store_id', 
                                     'transaction_id', 'basket', 'purchase', 'sale_amount',
                                     'quantity_sold', 'item_price', 'discount_amount'],
                'pattern_keywords': ['transaction_level', 'store_level', 'daily_sales', 
                                   'hourly_transactions', 'customer_receipt'],
                'negative_keywords': ['tdp', 'acv', 'distribution_points', 'market_share',
                                    'brand_hierarchy', 'category_tree', 'product_attributes'],
                'weight': 1.0
            },
            'product_attribute': {
                'primary_keywords': ['product_hierarchy', 'brand_tree', 'category_master', 
                                   'product_catalog', 'item_master'],
                'secondary_keywords': ['brand_name', 'category_level', 'sub_category', 'segment',
                                     'form_factor', 'package_size', 'flavor', 'variant',
                                     'product_description', 'attribute_value', 'hierarchy_level'],
                'pattern_keywords': ['master_data', 'reference_data', 'lookup_table',
                                   'dimensional_data', 'metadata'],
                'negative_keywords': ['transaction_id', 'store_id', 'sale_date', 'quantity_sold',
                                    'tdp', 'acv', 'velocity', 'market_share', 'distribution'],
                'weight': 0.7  # Reduced weight to prevent over-classification
            },
            'depletion_data': {
                'primary_keywords': ['depletion', 'distributor', 'wholesaler', 'shipment'],
                'secondary_keywords': ['cases_shipped', 'inventory_level', 'stock_movement',
                                     'distributor_sales', 'warehouse', 'fulfillment'],
                'pattern_keywords': ['supply_chain', 'distribution_channel', 'inventory_tracking'],
                'negative_keywords': ['retail_sales', 'consumer_purchase', 'market_measurement'],
                'weight': 1.0
            }
        }
    
    def _load_data_type_patterns(self) -> Dict[str, Dict]:
        """Load data type recognition patterns"""
        return {
            'syndicated': {
                'column_patterns': [
                    ['market', 'category', 'brand', 'week_ending', 'tdp'],
                    ['geography', 'segment', 'manufacturer', 'period', 'acv'],
                    ['channel', 'brand_variant', 'time_period', 'velocity'],
                    ['retailer', 'category_desc', 'brand_desc', 'distribution']
                ],
                'data_granularity': ['weekly', 'monthly', 'quarterly'],
                'typical_metrics': ['tdp_pct', 'acv_pct', 'velocity', 'units_per_million', 'dollar_share']
            },
            'pos': {
                'column_patterns': [
                    ['store_id', 'upc', 'transaction_id', 'sale_date', 'quantity'],
                    ['location', 'barcode', 'receipt_number', 'purchase_date', 'amount'],
                    ['retailer_id', 'sku', 'transaction_date', 'units_sold', 'revenue'],
                    ['store_number', 'item_code', 'sales_date', 'qty', 'price']
                ],
                'data_granularity': ['transaction', 'daily', 'store_level'],
                'typical_metrics': ['quantity_sold', 'sale_amount', 'unit_price', 'discount_amount']
            },
            'product_attribute': {
                'column_patterns': [
                    ['product_id', 'brand', 'category', 'sub_category', 'description'],
                    ['sku', 'brand_name', 'segment', 'form', 'size'],
                    ['item_code', 'manufacturer', 'category_desc', 'variant', 'attributes'],
                    ['product_code', 'brand_hierarchy', 'category_tree', 'specifications']
                ],
                'data_granularity': ['product_level', 'sku_level', 'variant_level'],
                'typical_metrics': ['attribute_value', 'hierarchy_level', 'classification_code']
            }
        }
    
    def _load_cpg_prompts(self) -> Dict[str, str]:
        """Load enhanced CPG domain-specific prompts"""
        return {
            "file_analysis": """
You are a CPG (Consumer Packaged Goods) data expert analyzing file content to determine data type classification.

CRITICAL CLASSIFICATION RULES:

1. SYNDICATED DATA - Third-party market research data:
   - MUST contain market performance metrics like TDP (Total Distribution Points), ACV (All Commodity Volume), velocity
   - Typically from Nielsen, IRI, Symphony, Circana, Euromonitor
   - Shows aggregated market/retailer performance, NOT individual transactions
   - Granularity: Weekly/Monthly market measurements
   - Key indicators: distribution metrics, market share, category performance

2. POS DATA - Point-of-sale transaction data:
   - MUST contain individual transaction details with UPC/SKU, store, transaction IDs
   - Shows actual checkout/register transactions
   - Granularity: Transaction-level or store-daily aggregations
   - Key indicators: transaction_id, upc, barcode, store_id, sale_date, quantity_sold

3. PRODUCT_ATTRIBUTE DATA - Product master/reference data:
   - MUST be primarily descriptive/hierarchical product information
   - Contains brand hierarchies, category trees, product specifications
   - NO transaction data, NO market performance metrics
   - Granularity: Product/SKU level descriptive data
   - Key indicators: product_hierarchy, brand_tree, category_master, specifications

VALIDATION CHECKS:
- If file contains TDP/ACV/velocity metrics → SYNDICATED (NOT product_attribute)
- If file contains transaction_id/upc scan data → POS (NOT product_attribute)  
- If file contains ONLY product descriptions/hierarchies → PRODUCT_ATTRIBUTE
- If file has both transaction data AND market metrics → Likely POS with derived metrics

CPG Data Types (in priority order):
- syndicated: Third-party market research (Nielsen, IRI) with TDP, ACV, velocity metrics
- pos: Point-of-sale transaction data with UPC, store, transaction details
- product_attribute: Product master data with brand, category, specifications ONLY
- depletion_data: Distributor shipment and inventory data
- margin_data: Cost and margin analysis data
- numerator_intel: Promotional and marketing intelligence
- trace_data: Supply chain traceability data
- product_mapping: Product hierarchy mappings
- geography_mapping: Geographic territory definitions
- pvp_mapping: Price-Volume-Pack configurations
- national_accounts: Key account customer analytics
- pos_fact: POS fact table data
- dimension_data: Reference dimension data

Analyze the file content and provide:
1. Primary data type classification with confidence
2. Key business indicators that support classification
3. Data granularity assessment
4. Validation against negative indicators
5. Alternative classifications if ambiguous

IMPORTANT: Do not default to product_attribute unless file contains ONLY descriptive product information with NO transaction or market metrics.
""",
            
            "reasoning_enhancement": """
You are enhancing CPG data classification reasoning with focus on accurate type distinction.

Provide enhanced reasoning that:
1. Clearly explains why this specific classification was chosen over alternatives
2. Identifies key distinguishing characteristics for CPG domain
3. Highlights business value and use cases specific to this data type
4. Addresses any classification ambiguities or edge cases
5. Validates against common misclassification patterns

Special attention to:
- Why NOT syndicated (if not chosen): Missing TDP/ACV/market metrics
- Why NOT pos (if not chosen): Missing transaction-level detail
- Why NOT product_attribute (if not chosen): Contains performance/transaction data

Keep explanation business-focused and actionable for CPG stakeholders.
""",
            
            "quality_assessment": """
You are assessing CPG data quality with classification validation.

Evaluate data quality AND validate classification accuracy:
1. Data completeness and consistency for the identified type
2. Presence of expected metrics/columns for the data type
3. Absence of contradicting indicators
4. Suitability for intended CPG use cases (RGM, DDAI)
5. Classification confidence and alternative possibilities

Provide specific recommendations for:
- Data improvement opportunities
- Classification validation steps
- Risk assessment for automated processing
- Alternative analysis approaches if classification uncertain

Focus on actionable insights for CPG data stewards and analysts.
"""
        }
    
    def _detect_data_type_patterns(self, content: str, context: Dict[str, Any]) -> Dict[str, float]:
        """Enhanced pattern detection for data types"""
        pattern_scores = {}
        content_lower = content.lower()
        
        # Extract potential column names
        columns = self._extract_column_names(content, context)
        
        for data_type, keywords in self.classification_keywords.items():
            score = 0.0
            
            # Primary keyword matching (high weight)
            primary_matches = sum(1 for keyword in keywords['primary_keywords'] 
                                if keyword in content_lower)
            score += primary_matches * 3.0
            
            # Secondary keyword matching (medium weight)
            secondary_matches = sum(1 for keyword in keywords['secondary_keywords'] 
                                  if keyword in content_lower)
            score += secondary_matches * 2.0
            
            # Pattern keyword matching (medium weight)
            pattern_matches = sum(1 for keyword in keywords['pattern_keywords'] 
                                if keyword in content_lower)
            score += pattern_matches * 2.0
            
            # Column pattern matching
            if data_type in self.data_type_patterns:
                pattern_data = self.data_type_patterns[data_type]
                for pattern in pattern_data['column_patterns']:
                    pattern_match_count = sum(1 for col in pattern 
                                            if any(col_part in col_name.lower() 
                                                 for col_name in columns 
                                                 for col_part in col.split('_')))
                    if pattern_match_count >= 3:  # At least 3 columns match pattern
                        score += 5.0
                    elif pattern_match_count >= 2:
                        score += 2.0
            
            # Negative keyword penalty (strong penalty)
            negative_matches = sum(1 for keyword in keywords['negative_keywords'] 
                                 if keyword in content_lower)
            score -= negative_matches * 4.0  # Strong penalty for negative indicators
            
            # Apply data type weight
            score *= keywords['weight']
            
            # Ensure non-negative score
            pattern_scores[data_type] = max(0.0, score)
        
        # Normalize scores
        max_score = max(pattern_scores.values()) if pattern_scores.values() else 1.0
        if max_score > 0:
            pattern_scores = {k: v / max_score for k, v in pattern_scores.items()}
        
        return pattern_scores
    
    def _extract_column_names(self, content: str, context: Dict[str, Any]) -> List[str]:
        """Extract potential column names from content"""
        columns = []
        
        # Try to get columns from context first
        if 'columns' in context:
            columns.extend(context['columns'])
        
        # Extract from content
        lines = content.split('\n')
        for line in lines[:5]:  # Check first 5 lines
            if ',' in line or '|' in line or '\t' in line:
                # Potential header line
                separators = [',', '|', '\t', ';']
                for sep in separators:
                    if sep in line:
                        potential_columns = [col.strip().strip('"\'') for col in line.split(sep)]
                        if len(potential_columns) > 2:  # Valid header likely has multiple columns
                            columns.extend(potential_columns)
                        break
        
        return list(set(columns))  # Remove duplicates
    
    async def analyze_file_content(self, content: str, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Analyze file content using enhanced Azure OpenAI with pattern detection"""
        start_time = time.time()
        self.api_calls += 1
        
        try:
            # First run pattern detection
            pattern_scores = self._detect_data_type_patterns(content, context)
            self.logger.debug(f"Pattern detection scores: {pattern_scores}")
            
            # Prepare the enhanced analysis prompt
            analysis_prompt = self._build_enhanced_analysis_prompt(content, context, pattern_scores)
            
            # Call Azure OpenAI
            response = await self._call_azure_openai(
                prompt=analysis_prompt,
                max_tokens=800,
                temperature=0.1
            )
            
            if response:
                # Parse and structure the response with pattern validation
                insights = self._parse_analysis_response(response, context, pattern_scores)
                self.successful_calls += 1
                
                # Update performance metrics
                self._update_performance_metrics(time.time() - start_time, response)
                
                return insights
            else:
                # Fallback to pattern-based classification
                return self._create_pattern_fallback_response(pattern_scores, content)
                
        except Exception as e:
            self.logger.error(f"Azure OpenAI analysis failed: {e}")
            # Fallback to pattern-based classification
            pattern_scores = self._detect_data_type_patterns(content, context)
            return self._create_pattern_fallback_response(pattern_scores, content)
    
    def _build_enhanced_analysis_prompt(self, content: str, context: Dict[str, Any], 
                                      pattern_scores: Dict[str, float]) -> str:
        """Build enhanced analysis prompt with pattern insights"""
        # Get top pattern matches for context
        sorted_patterns = sorted(pattern_scores.items(), key=lambda x: x[1], reverse=True)[:3]
        pattern_context = ", ".join([f"{dtype}({score:.2f})" for dtype, score in sorted_patterns])
        
        prompt_parts = [
            self.domain_prompts["file_analysis"],
            "",
            "File Information:",
            f"- Name: {context.get('file_name', 'unknown')}",
            f"- Type: {context.get('file_type', 'unknown')}",
            f"- Pattern Analysis: {pattern_context}",
            "",
            "Content to Analyze:",
            content[:2000],  # Limit content length
            "",
            "CLASSIFICATION VALIDATION QUESTIONS:",
            "1. Does this file contain TDP/ACV/velocity metrics? → syndicated",
            "2. Does this file contain transaction_id/UPC scan data? → pos", 
            "3. Does this file contain ONLY product descriptions/hierarchies? → product_attribute",
            "4. What is the primary business purpose of this data?",
            "",
            "Provide analysis in JSON format:",
            """{
                "classification_hints": [
                    {
                        "suggested_type": "string",
                        "confidence": 0.0-1.0,
                        "supporting_evidence": ["evidence1", "evidence2"],
                        "business_reasoning": "explanation",
                        "validation_checks": {
                            "has_transaction_data": boolean,
                            "has_market_metrics": boolean,
                            "has_product_hierarchy": boolean,
                            "primary_purpose": "string"
                        }
                    }
                ],
                "business_terms": ["term1", "term2"],
                "data_granularity": "transaction|store|market|product",
                "use_cases": ["rgm_use_case", "ddai_use_case"],
                "quality_assessment": {
                    "completeness": 0.0-1.0,
                    "consistency": 0.0-1.0,
                    "concerns": ["concern1", "concern2"]
                },
                "confidence": 0.0-1.0,
                "reasoning": "detailed explanation with validation"
            }"""
        ]
        
        return "\n".join(prompt_parts)
    
    def _parse_analysis_response(self, response: str, context: Dict[str, Any], 
                               pattern_scores: Dict[str, float]) -> Dict[str, Any]:
        """Parse and validate Azure OpenAI response with pattern cross-validation"""
        try:
            # Try to extract JSON from response
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                analysis = json.loads(json_str)
                
                # Validate and enhance with pattern validation
                return self._validate_and_enhance_analysis(analysis, pattern_scores)
            else:
                # Fallback: parse text response with pattern validation
                return self._parse_text_response_enhanced(response, pattern_scores)
                
        except json.JSONDecodeError:
            self.logger.warning("Failed to parse JSON response, using enhanced text parsing")
            return self._parse_text_response_enhanced(response, pattern_scores)
        except Exception as e:
            self.logger.error(f"Failed to parse analysis response: {e}")
            return self._create_pattern_fallback_response(pattern_scores, response)
    
    def _validate_and_enhance_analysis(self, analysis: Dict[str, Any], 
                                     pattern_scores: Dict[str, float]) -> Dict[str, Any]:
        """Validate AI analysis against pattern detection and enhance"""
        # Start with validated structure
        validated = self._validate_analysis_response(analysis)
        
        # Cross-validate with pattern scores
        ai_hints = validated.get("classification_hints", [])
        if ai_hints:
            primary_hint = ai_hints[0]
            ai_classification = primary_hint.get("suggested_type", "unknown")
            ai_confidence = primary_hint.get("confidence", 0.0)
            
            # Get pattern-based top classification
            pattern_classification = max(pattern_scores.items(), key=lambda x: x[1]) if pattern_scores else ("unknown", 0.0)
            pattern_type, pattern_confidence = pattern_classification
            
            # Cross-validation logic
            if ai_classification == pattern_type:
                # AI and pattern agree - boost confidence
                boosted_confidence = min(1.0, ai_confidence + (pattern_confidence * 0.3))
                primary_hint["confidence"] = boosted_confidence
                primary_hint["supporting_evidence"].append(f"Pattern analysis confirms classification (score: {pattern_confidence:.2f})")
            
            elif pattern_confidence > 0.6 and ai_confidence < 0.7:
                # Strong pattern evidence, weak AI confidence - suggest pattern classification
                pattern_hint = {
                    "suggested_type": pattern_type,
                    "confidence": min(0.8, pattern_confidence),
                    "supporting_evidence": [f"Strong pattern match (score: {pattern_confidence:.2f})", "Cross-validated with keyword analysis"],
                    "business_reasoning": f"Pattern analysis strongly indicates {pattern_type} based on content structure and keywords"
                }
                validated["classification_hints"].insert(0, pattern_hint)
            
            elif ai_classification == "product_attribute" and pattern_type in ["syndicated", "pos"] and pattern_confidence > 0.4:
                # Prevent product_attribute misclassification when pattern suggests otherwise
                self.logger.warning(f"Potential misclassification: AI suggests product_attribute but pattern suggests {pattern_type}")
                corrected_hint = {
                    "suggested_type": pattern_type,
                    "confidence": max(0.6, pattern_confidence),
                    "supporting_evidence": [f"Pattern analysis correction (score: {pattern_confidence:.2f})", "Prevented product_attribute over-classification"],
                    "business_reasoning": f"Corrected classification based on strong {pattern_type} indicators in content structure"
                }
                validated["classification_hints"].insert(0, corrected_hint)
        
        # Add pattern analysis to metadata
        validated["pattern_analysis"] = {
            "scores": pattern_scores,
            "top_pattern": max(pattern_scores.items(), key=lambda x: x[1]) if pattern_scores else ("unknown", 0.0)
        }
        
        return validated
    
    def _parse_text_response_enhanced(self, response: str, pattern_scores: Dict[str, float]) -> Dict[str, Any]:
        """Enhanced text response parsing with pattern validation"""
        # Get basic text parsing result
        basic_result = self._parse_text_response(response)
        
        # Enhance with pattern analysis
        if pattern_scores:
            top_pattern_type, top_pattern_score = max(pattern_scores.items(), key=lambda x: x[1])
            
            if top_pattern_score > 0.5:
                # Override text parsing with strong pattern match
                enhanced_hint = {
                    "suggested_type": top_pattern_type,
                    "confidence": min(0.8, top_pattern_score),
                    "supporting_evidence": [f"Pattern analysis (score: {top_pattern_score:.2f})", "Enhanced keyword matching"],
                    "business_reasoning": f"Pattern-based classification using enhanced keyword analysis"
                }
                basic_result["classification_hints"] = [enhanced_hint]
        
        # Add pattern analysis
        basic_result["pattern_analysis"] = {
            "scores": pattern_scores,
            "method": "enhanced_text_parsing"
        }
        
        return basic_result
    
    def _create_pattern_fallback_response(self, pattern_scores: Dict[str, float], 
                                        content: str) -> Dict[str, Any]:
        """Create fallback response based on pattern analysis"""
        if not pattern_scores:
            return self._create_fallback_response(content)
        
        # Get best pattern match
        top_type, top_score = max(pattern_scores.items(), key=lambda x: x[1])
        
        # Create classification hint
        classification_hint = {
            "suggested_type": top_type if top_score > 0.3 else "unknown",
            "confidence": min(0.7, top_score) if top_score > 0.3 else 0.2,
            "supporting_evidence": [f"Pattern analysis score: {top_score:.2f}", "Keyword-based classification"],
            "business_reasoning": f"Classification based on enhanced pattern analysis and keyword matching"
        }
        
        return {
            "classification_hints": [classification_hint],
            "business_terms": list(self.classification_keywords.get(top_type, {}).get('secondary_keywords', []))[:5],
            "data_granularity": "unknown",
            "use_cases": [],
            "quality_assessment": {
                "completeness": 0.5,
                "consistency": 0.5,
                "concerns": ["Classification based on pattern analysis only"]
            },
            "confidence": classification_hint["confidence"],
            "reasoning": f"Pattern-based classification using enhanced keyword analysis. Top match: {top_type} (score: {top_score:.2f})",
            "pattern_analysis": {
                "scores": pattern_scores,
                "method": "pattern_fallback"
            }
        }
    
    def _validate_analysis_response(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Enhanced validation with classification business rules"""
        # Start with basic validation
        validated = {
            "classification_hints": [],
            "business_terms": [],
            "data_granularity": "unknown",
            "use_cases": [],
            "quality_assessment": {
                "completeness": 0.5,
                "consistency": 0.5,
                "concerns": []
            },
            "confidence": 0.0,
            "reasoning": ""
        }
        
        # Update with actual values if present
        if isinstance(analysis, dict):
            validated.update(analysis)
        
        # Enhanced validation of classification hints
        hints = validated.get("classification_hints", [])
        valid_hints = []
        
        for hint in hints:
            if isinstance(hint, dict) and "suggested_type" in hint:
                suggested_type = hint.get("suggested_type", "unknown")
                confidence = max(0.0, min(1.0, hint.get("confidence", 0.0)))
                
                # Apply business rules for confidence adjustment
                confidence = self._apply_classification_business_rules(hint, confidence)
                
                valid_hint = {
                    "suggested_type": suggested_type,
                    "confidence": confidence,
                    "supporting_evidence": hint.get("supporting_evidence", []),
                    "business_reasoning": hint.get("business_reasoning", ""),
                    "validation_checks": hint.get("validation_checks", {})
                }
                valid_hints.append(valid_hint)
        
        validated["classification_hints"] = valid_hints
        
        # Ensure confidence is in valid range
        validated["confidence"] = max(0.0, min(1.0, validated.get("confidence", 0.0)))
        
        return validated
    
    def _apply_classification_business_rules(self, hint: Dict[str, Any], confidence: float) -> float:
        """Apply business rules to adjust classification confidence"""
        suggested_type = hint.get("suggested_type", "unknown")
        evidence = hint.get("supporting_evidence", [])
        validation_checks = hint.get("validation_checks", {})
        
        # Rule 1: Product attribute should not be default for transactional data
        if suggested_type == "product_attribute":
            has_transaction_data = validation_checks.get("has_transaction_data", False)
            has_market_metrics = validation_checks.get("has_market_metrics", False)
            
            if has_transaction_data or has_market_metrics:
                # Penalize product_attribute if transaction or market data present
                confidence *= 0.5
                self.logger.warning(f"Reduced product_attribute confidence due to transaction/market data presence")
        
        # Rule 2: Boost syndicated confidence if market metrics present
        elif suggested_type == "syndicated":
            has_market_metrics = validation_checks.get("has_market_metrics", False)
            market_keywords = ['tdp', 'acv', 'velocity', 'distribution', 'market_share']
            market_evidence = any(keyword in ' '.join(evidence).lower() for keyword in market_keywords)
            
            if has_market_metrics or market_evidence:
                confidence = min(1.0, confidence * 1.2)
        
        # Rule 3: Boost POS confidence if transaction data present
        elif suggested_type == "pos":
            has_transaction_data = validation_checks.get("has_transaction_data", False)
            transaction_keywords = ['transaction_id', 'upc', 'barcode', 'store_id', 'checkout']
            transaction_evidence = any(keyword in ' '.join(evidence).lower() for keyword in transaction_keywords)
            
            if has_transaction_data or transaction_evidence:
                confidence = min(1.0, confidence * 1.2)
        
        # Rule 4: Minimum confidence threshold for specific types
        type_min_confidence = {
            "syndicated": 0.4,
            "pos": 0.4,
            "product_attribute": 0.3
        }
        
        min_conf = type_min_confidence.get(suggested_type, 0.2)
        if confidence < min_conf and suggested_type != "unknown":
            self.logger.debug(f"Applied minimum confidence {min_conf} for {suggested_type}")
            confidence = min_conf
        
        return confidence
    
    def _parse_text_response(self, response: str) -> Dict[str, Any]:
        """Enhanced text parsing with better keyword classification"""
        lines = response.lower().split('\n')
        
        # Use enhanced keyword classification
        pattern_scores = {}
        content = response.lower()
        
        for data_type, keywords in self.classification_keywords.items():
            score = 0.0
            matches = []
            
            # Check primary keywords
            for keyword in keywords['primary_keywords']:
                if keyword in content:
                    score += 3.0
                    matches.append(keyword)
            
            # Check secondary keywords
            for keyword in keywords['secondary_keywords']:
                if keyword in content:
                    score += 2.0
                    matches.append(keyword)
            
            # Apply negative keyword penalty
            for keyword in keywords['negative_keywords']:
                if keyword in content:
                    score -= 2.0
            
            # Apply weight
            score *= keywords['weight']
            score = max(0.0, score)
            
            pattern_scores[data_type] = {
                'score': score,
                'matches': matches
            }
        
        # Find best match
        best_type = "unknown"
        best_score = 0.0
        best_matches = []
        
        for data_type, result in pattern_scores.items():
            if result['score'] > best_score:
                best_score = result['score']
                best_type = data_type
                best_matches = result['matches']
        
        # Calculate confidence
        confidence = min(0.8, best_score * 0.1) if best_score > 0 else 0.3
        
        return {
            "classification_hints": [{
                "suggested_type": best_type,
                "confidence": confidence,
                "supporting_evidence": [f"Enhanced keyword analysis found: {', '.join(best_matches[:5])}"] if best_matches else ["No strong keyword matches found"],
                "business_reasoning": f"Classification based on enhanced keyword analysis with score {best_score:.1f}"
            }] if best_score > 0 else [],
            "business_terms": best_matches[:10],
            "data_granularity": "unknown",
            "use_cases": [],
            "quality_assessment": {
                "completeness": 0.5,
                "consistency": 0.5,
                "concerns": ["Classification based on text analysis only"]
            },
            "confidence": confidence,
            "reasoning": f"Enhanced text parsing classified as {best_type} with score {best_score:.1f}",
            "pattern_analysis": {
                "scores": {k: v['score'] for k, v in pattern_scores.items()},
                "method": "enhanced_text_parsing"
            }
        }
    
    def _create_fallback_response(self, response: str) -> Dict[str, Any]:
        """Enhanced fallback response"""
        return {
            "classification_hints": [{
                "suggested_type": "unknown",
                "confidence": 0.1,
                "supporting_evidence": ["No classification patterns detected"],
                "business_reasoning": "Unable to determine data type from available content"
            }],
            "business_terms": [],
            "data_granularity": "unknown", 
            "use_cases": [],
            "quality_assessment": {
                "completeness": 0.0,
                "consistency": 0.0,
                "concerns": ["Failed to analyze content", "Manual classification recommended"]
            },
            "confidence": 0.1,
            "reasoning": "Classification analysis failed - manual review required",
            "raw_response": response[:500] if response else "No response received"
        }
    
    async def _call_azure_openai(self, prompt: str, max_tokens: int = 500, 
                               temperature: float = 0.1) -> Optional[str]:
        """Make API call to Azure OpenAI"""
        try:
            response = await asyncio.wait_for(
                asyncio.to_thread(
                    self.client.chat.completions.create,
                    model=self.config.deployment,
                    messages=[
                        {"role": "system", "content": "You are a CPG data classification expert with enhanced pattern recognition capabilities."},
                        {"role": "user", "content": prompt}
                    ],
                    max_tokens=max_tokens,
                    temperature=temperature,
                    top_p=0.95,
                    frequency_penalty=0,
                    presence_penalty=0
                ),
                timeout=self.config.timeout
            )
            
            if response.choices and response.choices[0].message:
                return response.choices[0].message.content
            else:
                return None
                
        except asyncio.TimeoutError:
            self.logger.warning("Azure OpenAI request timed out")
            return None
        except Exception as e:
            self.logger.error(f"Azure OpenAI API call failed: {e}")
            return None
    
    async def enhance_classification_reasoning(self, classification: str, confidence: float,
                                             original_reasoning: str, 
                                             file_context: Dict[str, Any]) -> Optional[str]:
        """Enhanced classification reasoning with validation"""
        try:
            # Build enhanced reasoning prompt
            prompt = self._build_enhanced_reasoning_prompt(
                classification, confidence, original_reasoning, file_context
            )
            
            # Call Azure OpenAI
            response = await self._call_azure_openai(
                prompt=prompt,
                max_tokens=300,
                temperature=0.2
            )
            
            if response:
                # Clean and validate the enhanced reasoning
                enhanced = response.strip()
                if enhanced and len(enhanced) > 20:
                    # Add validation note if confidence is low
                    if confidence < 0.6:
                        enhanced += f" [Note: Classification confidence is {confidence:.2f} - consider manual validation]"
                    return enhanced
            
            return None
            
        except Exception as e:
            self.logger.error(f"Reasoning enhancement failed: {e}")
            return None
    
    def _build_enhanced_reasoning_prompt(self, classification: str, confidence: float,
                                       original_reasoning: str, file_context: Dict[str, Any]) -> str:
        """Build enhanced reasoning prompt with validation context"""
        prompt_parts = [
            self.domain_prompts["reasoning_enhancement"],
            "",
            f"Classification Decision: {classification}",
            f"Confidence Level: {confidence:.2f}",
            f"Original Reasoning: {original_reasoning}",
            "",
            "File Context:",
            f"- Name: {file_context.get('file_name', 'unknown')}",
            f"- Columns: {', '.join(file_context.get('columns', [])[:10])}",
            f"- Patterns: {', '.join(file_context.get('patterns', []))}",
            "",
            "VALIDATION REQUIREMENTS:",
            f"- Explain why {classification} was chosen over alternatives",
            "- Address potential misclassification concerns",
            "- Validate against common CPG data type confusions",
            "",
            "Provide enhanced business reasoning (2-3 sentences with validation):"
        ]
        
        return "\n".join(prompt_parts)
    
    async def assess_data_quality(self, file_content: str, schema_info: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Enhanced data quality assessment with classification validation"""
        try:
            # Build enhanced quality assessment prompt
            prompt = self._build_enhanced_quality_prompt(file_content, schema_info)
            
            # Call Azure OpenAI
            response = await self._call_azure_openai(
                prompt=prompt,
                max_tokens=400,
                temperature=0.1
            )
            
            if response:
                return self._parse_quality_response(response)
            
            return None
            
        except Exception as e:
            self.logger.error(f"Data quality assessment failed: {e}")
            return None
    
    def _build_enhanced_quality_prompt(self, content: str, schema_info: Dict[str, Any]) -> str:
        """Build enhanced data quality assessment prompt"""
        prompt_parts = [
            self.domain_prompts["quality_assessment"],
            "",
            "Schema Information:",
            f"- Columns: {schema_info.get('column_count', 0)}",
            f"- Rows: {schema_info.get('row_count', 0)}",
            f"- Column Names: {', '.join(schema_info.get('columns', [])[:15])}",
            "",
            "Content Sample:",
            content[:1000],
            "",
            "ENHANCED QUALITY ASSESSMENT:",
            "1. Validate data type consistency",
            "2. Check for classification indicators",
            "3. Assess business use case alignment",
            "4. Identify improvement opportunities",
            "",
            "Provide assessment in JSON format:",
            """{
                "completeness_score": 0.0-1.0,
                "consistency_score": 0.0-1.0,
                "quality_issues": ["issue1", "issue2"],
                "recommendations": ["rec1", "rec2"],
                "automation_readiness": 0.0-1.0,
                "risk_level": "low|medium|high",
                "classification_validation": {
                    "data_type_consistency": boolean,
                    "expected_columns_present": boolean,
                    "business_logic_alignment": boolean
                }
            }"""
        ]
        
        return "\n".join(prompt_parts)
    
    def _parse_quality_response(self, response: str) -> Dict[str, Any]:
        """Enhanced quality assessment response parsing"""
        try:
            # Extract JSON from response
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            
            if json_start >= 0 and json_end > json_start:
                quality_data = json.loads(response[json_start:json_end])
                
                # Validate scores
                for score_field in ['completeness_score', 'consistency_score', 'automation_readiness']:
                    if score_field in quality_data:
                        quality_data[score_field] = max(0.0, min(1.0, quality_data[score_field]))
                
                # Ensure classification validation exists
                if 'classification_validation' not in quality_data:
                    quality_data['classification_validation'] = {
                        "data_type_consistency": True,
                        "expected_columns_present": True,
                        "business_logic_alignment": True
                    }
                
                return quality_data
            
        except (json.JSONDecodeError, ValueError):
            pass
        
        # Enhanced fallback parsing
        return {
            "completeness_score": 0.5,
            "consistency_score": 0.5,
            "quality_issues": ["Unable to parse detailed assessment"],
            "recommendations": ["Manual review recommended", "Validate data type classification"],
            "automation_readiness": 0.3,
            "risk_level": "medium",
            "classification_validation": {
                "data_type_consistency": False,
                "expected_columns_present": False,
                "business_logic_alignment": False
            }
        }
    
    def _update_performance_metrics(self, response_time: float, response: str):
        """Update service performance metrics"""
        # Update average response time
        total_time = self.avg_response_time * (self.successful_calls - 1) + response_time
        self.avg_response_time = total_time / self.successful_calls
        
        # Estimate token usage (rough approximation)
        estimated_tokens = len(response.split()) * 1.3  # Rough token estimate
        self.total_tokens += estimated_tokens
    
    def get_service_statistics(self) -> Dict[str, Any]:
        """Get enhanced service performance statistics"""
        success_rate = self.successful_calls / self.api_calls if self.api_calls > 0 else 0.0
        
        return {
            "total_api_calls": self.api_calls,
            "successful_calls": self.successful_calls,
            "success_rate": success_rate,
            "avg_response_time": self.avg_response_time,
            "total_tokens_used": self.total_tokens,
            "avg_tokens_per_call": self.total_tokens / self.successful_calls if self.successful_calls > 0 else 0,
            "enhancement_features": {
                "pattern_detection": True,
                "keyword_validation": True,
                "business_rules": True,
                "classification_validation": True
            }
        }
    
    async def test_connection(self) -> Dict[str, Any]:
        """Test Azure OpenAI connection with enhanced validation"""
        try:
            test_prompt = "Respond with 'Enhanced connection successful' if you can read this message."
            
            response = await self._call_azure_openai(
                prompt=test_prompt,
                max_tokens=10,
                temperature=0.0
            )
            
            if response and "successful" in response.lower():
                return {
                    "status": "success",
                    "message": "Enhanced Azure OpenAI connection is working",
                    "response": response,
                    "features": "Pattern detection and validation enabled"
                }
            else:
                return {
                    "status": "partial",
                    "message": "Azure OpenAI responded but with unexpected content",
                    "response": response
                }
                
        except Exception as e:
            return {
                "status": "failed",
                "message": f"Enhanced Azure OpenAI connection failed: {str(e)}",
                "error": str(e)
            }

# Usage example with enhanced features
async def example_usage():
    """Example usage of enhanced Azure OpenAI service"""
    from core.config_manager import ConfigManager
    
    # Initialize enhanced service
    config_manager = ConfigManager()
    azure_config = config_manager.get_azure_openai_config()
    ai_service = AzureOpenAIService(azure_config)
    
    # Test connection
    connection_test = await ai_service.test_connection()
    print(f"Enhanced connection test: {connection_test}")
    
    # Analyze sample syndicated content
    syndicated_content = """
    File: nielsen_weekly_data_2024.csv
    Columns: market, category, brand, week_ending, tdp_pct, acv_pct, velocity, units_per_million, dollars
    Content: Weekly syndicated data showing market performance metrics with TDP and ACV measurements
    Sample data: Chicago,Beverages,Coca-Cola,2024-01-07,85.5,78.2,0.45,12500,45000
    """
    
    context = {
        "file_name": "nielsen_weekly_data_2024.csv",
        "file_type": ".csv",
        "cpg_domain": True,
        "columns": ["market", "category", "brand", "week_ending", "tdp_pct", "acv_pct", "velocity"]
    }
    
    insights = await ai_service.analyze_file_content(syndicated_content, context)
    print(f"Enhanced AI Insights: {json.dumps(insights, indent=2)}")
    
    # Test POS content
    pos_content = """
    File: store_pos_transactions.csv
    Columns: store_id, transaction_id, upc, scan_date, quantity_sold, unit_price, total_amount
    Content: Point of sale transaction data from retail stores
    Sample: 12345,TXN98765,012345678901,2024-01-15,2,3.99,7.98
    """
    
    pos_context = {
        "file_name": "store_pos_transactions.csv", 
        "file_type": ".csv",
        "columns": ["store_id", "transaction_id", "upc", "scan_date", "quantity_sold"]
    }
    
    pos_insights = await ai_service.analyze_file_content(pos_content, pos_context)
    print(f"POS Analysis: {json.dumps(pos_insights, indent=2)}")
    
    # Get enhanced statistics
    stats = ai_service.get_service_statistics()
    print(f"Enhanced Service Stats: {stats}")

if __name__ == "__main__":
    asyncio.run(example_usage())