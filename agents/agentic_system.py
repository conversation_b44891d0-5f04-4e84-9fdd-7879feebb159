"""
Main Agentic Classification System for CPG Files with Enhanced Validation
Integrates all agents, Azure OpenAI, vector database, and enhanced classification logic
"""

import os
import sys
import asyncio
import logging
import time
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import json

# Add project root to Python path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

# Import existing knowledge base components
from core.config_manager import ConfigManager
from core.knowledgebase.classification_engine import ClassificationEngine
from core.knowledgebase.document_models import FileDocument
from core.knowledgebase.vector_db_manager import VectorDBManager

# Import agent components
from agents.base_agent import FileContext, AgentDecision, CPGDomainKnowledge
from agents.supervisor_voting_agents import SupervisorAgent
from agents.azure_openai_integration import AzureOpenAIService

@dataclass
class ClassificationRequest:
    """Request for file classification"""
    file_path: str
    user_context: Dict[str, Any] = None
    processing_options: Dict[str, Any] = None
    metadata: Dict[str, Any] = None

@dataclass
class ClassificationResponse:
    """Response from classification system with enhanced validation"""
    success: bool
    classification: str
    confidence: float
    reasoning: str
    evidence: Dict[str, Any]
    processing_time: float
    agent_details: Dict[str, Any]
    recommendations: List[str]
    vector_insights: Dict[str, Any] = None
    validation_results: Dict[str, Any] = None
    error_message: str = ""

class EnhancedClassificationValidator:
    """Enhanced validation logic for CPG classification"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Data type validation rules
        self.validation_rules = {
            'syndicated': {
                'required_indicators': ['tdp', 'acv', 'velocity', 'distribution', 'market_share'],
                'required_patterns': ['market', 'category', 'brand', 'period'],
                'negative_indicators': ['transaction_id', 'upc', 'barcode', 'checkout', 'receipt'],
                'granularity_patterns': ['weekly', 'monthly', 'quarterly', 'market_level'],
                'confidence_threshold': 0.6
            },
            'pos': {
                'required_indicators': ['transaction_id', 'upc', 'barcode', 'store_id', 'checkout'],
                'required_patterns': ['store', 'transaction', 'sale', 'purchase'],
                'negative_indicators': ['tdp', 'acv', 'distribution_points', 'market_share'],
                'granularity_patterns': ['transaction_level', 'store_level', 'daily'],
                'confidence_threshold': 0.6
            },
            'product_attribute': {
                'required_indicators': ['brand_hierarchy', 'category_tree', 'product_description', 'specifications'],
                'required_patterns': ['hierarchy', 'master', 'catalog', 'reference'],
                'negative_indicators': ['transaction_id', 'store_id', 'sale_date', 'tdp', 'acv', 'velocity'],
                'granularity_patterns': ['product_level', 'sku_level', 'master_data'],
                'confidence_threshold': 0.7  # Higher threshold for product_attribute
            },
            'depletion_data': {
                'required_indicators': ['depletion', 'distributor', 'shipment', 'inventory'],
                'required_patterns': ['warehouse', 'distribution', 'supply_chain'],
                'negative_indicators': ['retail_sales', 'consumer_purchase', 'market_measurement'],
                'granularity_patterns': ['distributor_level', 'warehouse_level'],
                'confidence_threshold': 0.5
            }
        }
    
    def validate_classification(self, classification: str, file_context: FileContext, 
                              agent_decision: AgentDecision) -> Dict[str, Any]:
        """Validate classification against business rules"""
        validation_result = {
            'is_valid': True,
            'confidence_adjustment': 0.0,
            'validation_score': 0.0,
            'concerns': [],
            'recommendations': [],
            'alternative_suggestions': []
        }
        
        try:
            # Get content for analysis
            content = self._extract_content_for_validation(file_context)
            
            # Check if classification follows validation rules
            if classification in self.validation_rules:
                rule_result = self._apply_validation_rules(classification, content, file_context)
                validation_result.update(rule_result)
            
            # Special validation for product_attribute over-classification
            if classification == 'product_attribute':
                pa_result = self._validate_product_attribute_classification(content, file_context, agent_decision)
                validation_result.update(pa_result)
            
            # Cross-validate with other possible types
            cross_validation = self._cross_validate_classification(classification, content, file_context)
            validation_result['alternative_suggestions'] = cross_validation
            
            # Calculate final validation score
            validation_result['validation_score'] = self._calculate_validation_score(validation_result)
            
        except Exception as e:
            self.logger.error(f"Classification validation failed: {e}")
            validation_result['is_valid'] = False
            validation_result['concerns'].append(f"Validation error: {str(e)}")
        
        return validation_result
    
    def _extract_content_for_validation(self, file_context: FileContext) -> str:
        """Extract relevant content for validation"""
        content_parts = []
        
        # Add file name
        content_parts.append(file_context.file_name.lower())
        
        # Add column names
        schema = file_context.extracted_content.get('schema', {})
        columns = schema.get('columns', [])
        if columns:
            content_parts.extend([col.lower() for col in columns])
        
        # Add content summary
        content_summary = file_context.extracted_content.get('content', '')
        if content_summary:
            content_parts.append(content_summary.lower())
        
        # Add detected patterns
        patterns = file_context.extracted_content.get('data_patterns', [])
        content_parts.extend([pattern.lower() for pattern in patterns])
        
        return ' '.join(content_parts)
    
    def _apply_validation_rules(self, classification: str, content: str, 
                              file_context: FileContext) -> Dict[str, Any]:
        """Apply validation rules for specific classification"""
        rules = self.validation_rules[classification]
        result = {
            'rule_compliance': {},
            'confidence_adjustment': 0.0,
            'concerns': [],
            'recommendations': []
        }
        
        # Check required indicators
        required_score = 0
        for indicator in rules['required_indicators']:
            if indicator in content:
                required_score += 1
                result['rule_compliance'][f'has_{indicator}'] = True
            else:
                result['rule_compliance'][f'has_{indicator}'] = False
        
        # Check required patterns
        pattern_score = 0
        for pattern in rules['required_patterns']:
            if pattern in content:
                pattern_score += 1
                result['rule_compliance'][f'pattern_{pattern}'] = True
            else:
                result['rule_compliance'][f'pattern_{pattern}'] = False
        
        # Check negative indicators (should NOT be present)
        negative_violations = 0
        for negative in rules['negative_indicators']:
            if negative in content:
                negative_violations += 1
                result['rule_compliance'][f'negative_{negative}'] = True
                result['concerns'].append(f"Found negative indicator '{negative}' for {classification}")
        
        # Calculate confidence adjustment
        required_ratio = required_score / len(rules['required_indicators'])
        pattern_ratio = pattern_score / len(rules['required_patterns'])
        negative_penalty = negative_violations * 0.2
        
        base_score = (required_ratio + pattern_ratio) / 2
        adjusted_score = max(0.0, base_score - negative_penalty)
        
        # Confidence adjustment based on rule compliance
        if adjusted_score >= 0.7:
            result['confidence_adjustment'] = 0.1  # Boost confidence
        elif adjusted_score <= 0.3:
            result['confidence_adjustment'] = -0.3  # Reduce confidence
            result['concerns'].append(f"Low rule compliance for {classification} ({adjusted_score:.2f})")
        
        # Add recommendations
        if required_score == 0:
            result['recommendations'].append(f"No required indicators found for {classification} - consider alternative classification")
        
        if negative_violations > 2:
            result['recommendations'].append(f"Multiple negative indicators suggest {classification} may be incorrect")
        
        return result
    
    def _validate_product_attribute_classification(self, content: str, file_context: FileContext,
                                                 agent_decision: AgentDecision) -> Dict[str, Any]:
        """Special validation for product_attribute to prevent over-classification"""
        result = {
            'product_attribute_validation': {},
            'confidence_adjustment': 0.0,
            'concerns': [],
            'is_valid': True
        }
        
        # Check for transaction data indicators (should NOT be in product_attribute)
        transaction_indicators = ['transaction_id', 'store_id', 'sale_date', 'quantity_sold', 
                                'checkout', 'receipt', 'purchase', 'upc_scan']
        transaction_count = sum(1 for indicator in transaction_indicators if indicator in content)
        
        # Check for market metrics (should NOT be in product_attribute)
        market_indicators = ['tdp', 'acv', 'velocity', 'distribution_points', 'market_share', 
                           'units_per_million', 'dollar_share']
        market_count = sum(1 for indicator in market_indicators if indicator in content)
        
        # Check for actual product attribute indicators
        attribute_indicators = ['brand_hierarchy', 'category_tree', 'product_description', 
                              'specifications', 'master_data', 'catalog', 'reference_data']
        attribute_count = sum(1 for indicator in attribute_indicators if indicator in content)
        
        result['product_attribute_validation'] = {
            'transaction_indicators_found': transaction_count,
            'market_indicators_found': market_count,
            'attribute_indicators_found': attribute_count
        }
        
        # Apply validation logic
        if transaction_count >= 2:
            result['confidence_adjustment'] = -0.5
            result['concerns'].append("Found transaction data - likely POS data, not product_attribute")
            result['is_valid'] = False
            
        elif market_count >= 2:
            result['confidence_adjustment'] = -0.5  
            result['concerns'].append("Found market metrics - likely syndicated data, not product_attribute")
            result['is_valid'] = False
            
        elif attribute_count == 0:
            result['confidence_adjustment'] = -0.3
            result['concerns'].append("No clear product attribute indicators found")
            
        elif transaction_count == 0 and market_count == 0 and attribute_count >= 2:
            result['confidence_adjustment'] = 0.2  # Boost confidence for clear product attributes
        
        # Check agent confidence - if too low for product_attribute, flag it
        if agent_decision.confidence < 0.7:
            result['concerns'].append(f"Low confidence ({agent_decision.confidence:.2f}) for product_attribute classification")
        
        return result
    
    def _cross_validate_classification(self, classification: str, content: str, 
                                     file_context: FileContext) -> List[Dict[str, Any]]:
        """Cross-validate against other possible classifications"""
        alternatives = []
        
        for alt_type, rules in self.validation_rules.items():
            if alt_type == classification:
                continue
                
            # Calculate score for alternative type
            required_matches = sum(1 for indicator in rules['required_indicators'] if indicator in content)
            pattern_matches = sum(1 for pattern in rules['required_patterns'] if pattern in content)
            negative_matches = sum(1 for negative in rules['negative_indicators'] if negative in content)
            
            total_possible = len(rules['required_indicators']) + len(rules['required_patterns'])
            positive_score = (required_matches + pattern_matches) / total_possible if total_possible > 0 else 0
            
            # Penalty for negative indicators
            penalty = negative_matches * 0.1
            final_score = max(0.0, positive_score - penalty)
            
            if final_score > 0.3:  # Significant alternative possibility
                alternatives.append({
                    'type': alt_type,
                    'score': final_score,
                    'confidence': min(0.8, final_score),
                    'reasons': [f"Found {required_matches} required indicators, {pattern_matches} patterns"]
                })
        
        # Sort by score
        alternatives.sort(key=lambda x: x['score'], reverse=True)
        return alternatives[:3]  # Return top 3 alternatives
    
    def _calculate_validation_score(self, validation_result: Dict[str, Any]) -> float:
        """Calculate overall validation score"""
        base_score = 0.5
        
        # Adjust based on confidence adjustment
        base_score += validation_result.get('confidence_adjustment', 0.0)
        
        # Penalty for concerns
        concern_penalty = len(validation_result.get('concerns', [])) * 0.1
        base_score -= concern_penalty
        
        # Boost for high alternative suggestions (indicates confusion)
        alternatives = validation_result.get('alternative_suggestions', [])
        if alternatives and alternatives[0]['score'] > 0.6:
            base_score -= 0.2  # Penalty for strong alternative
        
        return max(0.0, min(1.0, base_score))

class AgenticClassificationSystem:
    """Enhanced agentic classification system for CPG files with improved validation"""
    
    def __init__(self, config_path: str = None):
        """Initialize the enhanced agentic classification system"""
        # Setup enhanced logging first
        self._setup_enhanced_logging()
        self.logger = logging.getLogger(__name__)
        
        # Load configuration
        self.config_manager = ConfigManager(config_path) if config_path else ConfigManager()
        self.config = self.load_system_config()
        
        # Initialize enhanced validator
        self.validator = EnhancedClassificationValidator()
        
        # Initialize Azure OpenAI service
        self.azure_openai = None
        self._initialize_azure_openai()
        
        # Initialize existing knowledge base components
        self.vector_db = None
        self.classification_engine = None
        self._initialize_knowledge_base()
        
        # Initialize supervisor agent with enhanced validation
        self.supervisor_agent = None
        self._initialize_supervisor()
        
        # Enhanced system statistics
        self.system_stats = {
            "total_requests": 0,
            "successful_classifications": 0,
            "failed_classifications": 0,
            "avg_processing_time": 0.0,
            "avg_confidence": 0.0,
            "vector_enhanced_count": 0,
            "avg_vector_boost": 0.0,
            "vector_db_queries": 0,
            "vector_db_successful_matches": 0,
            "validation_corrections": 0,
            "product_attribute_corrections": 0,
            "syndicated_correct_classifications": 0,
            "pos_correct_classifications": 0
        }
        
        self.logger.info("Enhanced Agentic Classification System initialized with validation")
    
    def _setup_enhanced_logging(self):
        """Setup enhanced logging to project_root/logs/ directory"""
        try:
            # Create logs directory
            log_dir = project_root / "logs"
            log_dir.mkdir(exist_ok=True)
            
            # Setup logging configuration
            log_format = '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
            
            # Remove any existing handlers
            root_logger = logging.getLogger()
            for handler in root_logger.handlers[:]:
                root_logger.removeHandler(handler)
            
            # Create file handler for main system log
            main_log_file = log_dir / 'enhanced_agentic_system.log'
            file_handler = logging.FileHandler(main_log_file)
            file_handler.setLevel(logging.INFO)
            file_handler.setFormatter(logging.Formatter(log_format))
            
            # Create file handler for validation operations
            validation_log_file = log_dir / 'classification_validation.log'
            validation_handler = logging.FileHandler(validation_log_file)
            validation_handler.setLevel(logging.DEBUG)
            validation_handler.setFormatter(logging.Formatter(log_format))
            
            # Create console handler
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            console_handler.setFormatter(logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s'
            ))
            
            # Configure root logger
            root_logger.setLevel(logging.DEBUG)
            root_logger.addHandler(file_handler)
            root_logger.addHandler(console_handler)
            
            # Setup validation logger
            validation_logger = logging.getLogger('classification_validation')
            validation_logger.addHandler(validation_handler)
            validation_logger.setLevel(logging.DEBUG)
            
            print(f"Enhanced logging initialized in: {log_dir}")
            
        except Exception as e:
            print(f"Warning: Failed to setup enhanced logging: {e}")
            # Fallback to basic logging
            logging.basicConfig(
                level=logging.INFO,
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
    
    def load_system_config(self) -> Dict[str, Any]:
        """Load system configuration with enhanced validation settings"""
        try:
            # Load base configuration
            base_config = self.config_manager.load_config()
            
            # Add enhanced agentic system configuration
            agentic_config = {
                "min_consensus_threshold": 0.4,
                "min_confidence_threshold": 0.3,
                "min_participating_agents": 2,
                "max_processing_time": 60.0,
                "enable_azure_openai": True,
                "enable_learning": True,
                "parallel_processing": True,
                "enable_vector_search": True,
                "vector_similarity_threshold": 0.6,
                "auto_store_results": True,
                "vector_search_timeout": 10.0,
                "enable_validation": True,
                "validation_threshold": 0.5,
                "product_attribute_threshold": 0.7,
                "prevent_misclassification": True,
                "quality_thresholds": {
                    "high_quality": 0.8,
                    "medium_quality": 0.6,
                    "low_quality": 0.4
                },
                "classification_preferences": {
                    "syndicated": 0.9,  # High preference for syndicated when indicators present
                    "pos": 0.9,         # High preference for POS when indicators present  
                    "product_attribute": 0.6,  # Lower preference to prevent over-classification
                    "depletion_data": 0.8,
                    "unknown": 0.1      # Low preference for unknown
                },
                "vector_enhancements": {
                    "confidence_boost_threshold": 0.7,
                    "similarity_boost_factor": 0.2,
                    "collection_weights": {
                        "cpg_learning_feedback": 0.35,
                        "cpg_uploaded_files": 0.25,
                        "cpg_domain_knowledge": 0.20,
                        "cpg_schemas": 0.15,
                        "cpg_content": 0.05
                    }
                }
            }
            
            # Merge configurations
            base_config.update({"agentic_system": agentic_config})
            return base_config
            
        except Exception as e:
            self.logger.error(f"Failed to load system config: {e}")
            return {"agentic_system": {}}
    
    def _initialize_azure_openai(self):
        """Initialize Azure OpenAI service"""
        try:
            if self.config.get("agentic_system", {}).get("enable_azure_openai", True):
                azure_config = self.config_manager.get_azure_openai_config()
                self.azure_openai = AzureOpenAIService(azure_config)
                self.logger.info("Enhanced Azure OpenAI service initialized")
            else:
                self.logger.info("Azure OpenAI disabled in configuration")
        except Exception as e:
            self.logger.warning(f"Failed to initialize Azure OpenAI: {e}")
            self.azure_openai = None
    
    def _initialize_knowledge_base(self):
        """Initialize existing knowledge base components with vector DB"""
        try:
            # Get knowledge base configuration
            kb_config = self.config.get('knowledgebase', {})
            
            # Initialize vector database
            vector_db_config = self.config_manager.get_vector_db_config()
            self.vector_db = VectorDBManager(vector_db_config)
            
            # Test vector DB connection
            try:
                stats = self.vector_db.get_collection_stats()
                self.logger.info(f"Vector DB initialized with collections: {list(stats.keys())}")
            except Exception as e:
                self.logger.warning(f"Vector DB connection test failed: {e}")
            
            # Initialize classification engine
            classification_config = {
                'vector_db': vector_db_config,
                'classification': self.config_manager.get_classification_config(),
                'file_processing': self.config_manager.get_file_processing_config(),
                'learning': self.config_manager.get_learning_config()
            }
            
            self.classification_engine = ClassificationEngine(classification_config)
            self.logger.info("Knowledge base components initialized with vector DB support")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize knowledge base: {e}")
            raise
    
    def _initialize_supervisor(self):
        """Initialize supervisor agent with enhanced validation"""
        try:
            supervisor_config = self.config.copy()
            supervisor_config.update({
                "azure_openai": self.azure_openai,
                "vector_db": self.vector_db,
                "classification_engine": self.classification_engine,
                "validator": self.validator,
                "enable_vector_search": self.config.get("agentic_system", {}).get("enable_vector_search", True),
                "similarity_threshold": self.config.get("agentic_system", {}).get("vector_similarity_threshold", 0.6),
                "auto_store_results": self.config.get("agentic_system", {}).get("auto_store_results", True),
                "enable_validation": self.config.get("agentic_system", {}).get("enable_validation", True)
            })
            
            self.supervisor_agent = SupervisorAgent("supervisor_enhanced", supervisor_config)
            self.supervisor_agent.activate()
            
            self.logger.info("Supervisor agent initialized with enhanced validation")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize supervisor agent: {e}")
            raise
    
    async def classify_file(self, request: ClassificationRequest) -> ClassificationResponse:
        """Main method to classify a file using the enhanced agentic system"""
        start_time = time.time()
        self.system_stats["total_requests"] += 1
        
        # Log classification request
        self.logger.info(f"Starting enhanced classification for: {request.file_path}")
        validation_logger = logging.getLogger('classification_validation')
        validation_logger.info(f"Classification request received: {request.file_path}")
        
        try:
            # Step 1: Validate request
            validation_result = self._validate_request(request)
            if not validation_result["valid"]:
                return self._create_error_response(validation_result["error"], start_time)
            
            # Step 2: Extract file content using existing classification engine
            file_context = await self._prepare_file_context(request)
            if not file_context:
                return self._create_error_response("Failed to extract file content", start_time)
            
            # Step 3: Enhance context with Azure OpenAI if available
            if self.azure_openai:
                file_context = await self._enhance_context_with_ai(file_context, request)
            
            # Step 4: Run enhanced agentic classification
            agent_decision = await self._run_enhanced_agentic_classification(file_context)
            
            # Step 5: Enhanced validation of classification result
            validation_results = await self._validate_classification_result(agent_decision, file_context)
            
            # Step 6: Apply validation corrections if needed
            corrected_decision = self._apply_validation_corrections(agent_decision, validation_results, file_context)
            
            # Step 7: Extract vector insights from decision
            vector_insights = self._extract_vector_insights(corrected_decision)
            
            # Step 8: Post-process results with enhanced validation
            final_response = await self._post_process_enhanced_results(
                corrected_decision, file_context, request, vector_insights, validation_results
            )
            
            # Step 9: Update enhanced system statistics
            self._update_enhanced_system_stats(final_response, time.time() - start_time, vector_insights, validation_results)
            
            # Step 10: Store results for learning (if enabled and high quality)
            if (self.config.get("agentic_system", {}).get("enable_learning", True) and 
                final_response.confidence > 0.7):
                await self._store_classification_result(request, final_response, corrected_decision)
            
            # Log successful completion
            self.logger.info(f"Enhanced classification completed: {final_response.classification} "
                           f"({final_response.confidence:.2f}) in {final_response.processing_time:.2f}s")
            
            if validation_results.get('corrections_applied'):
                validation_logger.info(f"Validation corrections applied: {validation_results.get('correction_type')}")
            
            return final_response
            
        except Exception as e:
            self.logger.error(f"Enhanced classification failed: {e}")
            self.system_stats["failed_classifications"] += 1
            return self._create_error_response(str(e), start_time)
    
    def _validate_request(self, request: ClassificationRequest) -> Dict[str, Any]:
        """Validate classification request"""
        if not request.file_path:
            return {"valid": False, "error": "File path is required"}
        
        if not os.path.exists(request.file_path):
            return {"valid": False, "error": f"File not found: {request.file_path}"}
        
        # Check file size limits
        file_size = os.path.getsize(request.file_path)
        max_size = self.config.get("file_processing", {}).get("max_file_size", 100 * 1024 * 1024)
        
        if file_size > max_size:
            return {"valid": False, "error": f"File too large: {file_size} bytes (max: {max_size})"}
        
        # Check file type
        supported_extensions = ['.csv', '.xlsx', '.xls', '.txt', '.pdf']
        file_ext = Path(request.file_path).suffix.lower()
        
        if file_ext not in supported_extensions:
            return {"valid": False, "error": f"Unsupported file type: {file_ext}"}
        
        return {"valid": True}
    
    async def _prepare_file_context(self, request: ClassificationRequest) -> Optional[FileContext]:
        """Prepare file context using existing classification engine"""
        try:
            # Use existing classification engine to extract content
            extracted_content = self.classification_engine.file_processor_factory.get_processor(
                request.file_path
            ).extract_content(request.file_path)
            
            if 'error' in extracted_content:
                self.logger.error(f"Content extraction failed: {extracted_content['error']}")
                return None
            
            # Build enhanced file context
            file_context = FileContext(
                file_path=request.file_path,
                file_name=os.path.basename(request.file_path),
                file_type=Path(request.file_path).suffix.lower(),
                file_size=os.path.getsize(request.file_path),
                extracted_content=extracted_content,
                file_features=self.classification_engine._build_analysis_features(extracted_content),
                business_context=request.user_context or {},
                processing_metadata={
                    "extraction_timestamp": time.time(),
                    "user_request": request.metadata or {},
                    "vector_db_enabled": self.config.get("agentic_system", {}).get("enable_vector_search", True),
                    "validation_enabled": self.config.get("agentic_system", {}).get("enable_validation", True)
                }
            )
            
            return file_context
            
        except Exception as e:
            self.logger.error(f"Failed to prepare file context: {e}")
            return None
    
    async def _enhance_context_with_ai(self, file_context: FileContext, 
                                     request: ClassificationRequest) -> FileContext:
        """Enhance file context using Azure OpenAI insights"""
        try:
            if not self.azure_openai:
                return file_context
            
            # Prepare content for AI analysis
            ai_content = self._prepare_ai_content(file_context)
            
            # Get AI insights with enhanced classification hints
            ai_insights = await self.azure_openai.analyze_file_content(
                content=ai_content,
                context={
                    "file_name": file_context.file_name,
                    "file_type": file_context.file_type,
                    "cpg_domain": True,
                    "classification_types": CPGDomainKnowledge.CPG_DATA_TYPES,
                    "vector_enabled": True,
                    "validation_enabled": True,
                    "columns": file_context.extracted_content.get('schema', {}).get('columns', [])
                }
            )
            
            # Enhance file context with AI insights
            if ai_insights:
                file_context.business_context.update({
                    "ai_insights": ai_insights,
                    "ai_classification_hints": ai_insights.get("classification_hints", []),
                    "ai_business_terms": ai_insights.get("business_terms", []),
                    "ai_confidence": ai_insights.get("confidence", 0.0),
                    "pattern_analysis": ai_insights.get("pattern_analysis", {})
                })
                
                self.logger.debug(f"AI enhancement completed with {len(ai_insights.get('classification_hints', []))} hints")
            
            return file_context
            
        except Exception as e:
            self.logger.warning(f"AI enhancement failed: {e}")
            return file_context
    
    def _prepare_ai_content(self, file_context: FileContext) -> str:
        """Prepare content for AI analysis"""
        content_parts = []
        
        # Add file basic info
        content_parts.append(f"File: {file_context.file_name}")
        content_parts.append(f"Type: {file_context.file_type}")
        
        # Add schema information
        schema = file_context.extracted_content.get('schema', {})
        if schema:
            columns = schema.get('columns', [])
            if columns:
                content_parts.append(f"Columns: {', '.join(columns[:20])}")  # First 20 columns
            
            content_parts.append(f"Row count: {schema.get('row_count', 0)}")
            content_parts.append(f"Column count: {schema.get('column_count', 0)}")
        
        # Add content summary
        content_summary = file_context.extracted_content.get('content', '')
        if content_summary:
            # Truncate for AI processing
            content_parts.append(f"Content: {content_summary[:1000]}")
        
        # Add detected patterns
        patterns = file_context.extracted_content.get('data_patterns', [])
        if patterns:
            content_parts.append(f"Patterns: {', '.join(patterns)}")
        
        return " | ".join(content_parts)
    
    async def _run_enhanced_agentic_classification(self, file_context: FileContext) -> AgentDecision:
        """Run the enhanced agentic classification process"""
        try:
            # Set timeout for classification
            max_time = self.config.get("agentic_system", {}).get("max_processing_time", 60.0)
            
            # Log enhanced processing start
            validation_logger = logging.getLogger('classification_validation')
            validation_logger.info(f"Starting enhanced classification with validation: {file_context.file_name}")
            
            # Run supervisor agent classification with enhanced validation
            agent_decision = await asyncio.wait_for(
                self.supervisor_agent.analyze(file_context),
                timeout=max_time
            )
            
            # Log initial classification result
            validation_logger.info(f"Initial classification: {agent_decision.classification} "
                                 f"(confidence: {agent_decision.confidence:.2f})")
            
            return agent_decision
            
        except asyncio.TimeoutError:
            self.logger.error("Enhanced classification timed out")
            return AgentDecision(
                agent_id="timeout_handler",
                agent_type=self.supervisor_agent.agent_type,
                classification="unknown",
                confidence=0.0,
                reasoning="Enhanced classification timed out",
                processing_time=max_time
            )
        except Exception as e:
            self.logger.error(f"Enhanced classification failed: {e}")
            return AgentDecision(
                agent_id="error_handler",
                agent_type=self.supervisor_agent.agent_type,
                classification="unknown",
                confidence=0.0,
                reasoning=f"Enhanced classification error: {str(e)}",
                processing_time=0.0
            )
    
    async def _validate_classification_result(self, agent_decision: AgentDecision, 
                                            file_context: FileContext) -> Dict[str, Any]:
        """Validate classification result using enhanced validation logic"""
        try:
            validation_logger = logging.getLogger('classification_validation')
            validation_logger.info(f"Validating classification: {agent_decision.classification}")
            
            # Use enhanced validator
            validation_result = self.validator.validate_classification(
                agent_decision.classification, file_context, agent_decision
            )
            
            # Add additional context-specific validation
            context_validation = self._validate_with_context(agent_decision, file_context)
            validation_result.update(context_validation)
            
            # Log validation results
            if not validation_result['is_valid']:
                validation_logger.warning(f"Validation failed: {validation_result['concerns']}")
            else:
                validation_logger.info(f"Validation passed with score: {validation_result['validation_score']:.2f}")
            
            return validation_result
            
        except Exception as e:
            self.logger.error(f"Classification validation failed: {e}")
            return {
                'is_valid': False,
                'confidence_adjustment': -0.3,
                'validation_score': 0.0,
                'concerns': [f"Validation error: {str(e)}"],
                'recommendations': ['Manual review required'],
                'alternative_suggestions': []
            }
    
    def _validate_with_context(self, agent_decision: AgentDecision, 
                             file_context: FileContext) -> Dict[str, Any]:
        """Additional context-specific validation"""
        result = {
            'context_validation': {},
            'additional_concerns': [],
            'additional_recommendations': []
        }
        
        # Check filename patterns
        filename_lower = file_context.file_name.lower()
        
        # Syndicated filename patterns
        syndicated_patterns = ['nielsen', 'iri', 'symphony', 'circana', 'market', 'syndicated']
        pos_patterns = ['pos', 'transaction', 'sales', 'checkout', 'register']
        attribute_patterns = ['master', 'catalog', 'hierarchy', 'attribute', 'product_info']
        
        filename_suggests_syndicated = any(pattern in filename_lower for pattern in syndicated_patterns)
        filename_suggests_pos = any(pattern in filename_lower for pattern in pos_patterns)
        filename_suggests_attribute = any(pattern in filename_lower for pattern in attribute_patterns)
        
        result['context_validation']['filename_analysis'] = {
            'suggests_syndicated': filename_suggests_syndicated,
            'suggests_pos': filename_suggests_pos,
            'suggests_attribute': filename_suggests_attribute
        }
        
        # Cross-check with classification
        if agent_decision.classification == 'product_attribute':
            if filename_suggests_syndicated or filename_suggests_pos:
                result['additional_concerns'].append("Filename suggests non-product_attribute data type")
                
        elif agent_decision.classification == 'syndicated':
            if filename_suggests_pos:
                result['additional_concerns'].append("Filename suggests POS data, not syndicated")
                
        elif agent_decision.classification == 'pos':
            if filename_suggests_syndicated:
                result['additional_concerns'].append("Filename suggests syndicated data, not POS")
        
        # Check column count vs data type expectations
        schema = file_context.extracted_content.get('schema', {})
        column_count = schema.get('column_count', 0)
        
        if agent_decision.classification == 'product_attribute' and column_count > 20:
            result['additional_concerns'].append("High column count unusual for product_attribute data")
        
        elif agent_decision.classification in ['syndicated', 'pos'] and column_count < 5:
            result['additional_concerns'].append(f"Low column count unusual for {agent_decision.classification} data")
        
        return result
    
    def _apply_validation_corrections(self, agent_decision: AgentDecision, 
                                    validation_results: Dict[str, Any],
                                    file_context: FileContext) -> AgentDecision:
        """Apply validation corrections to agent decision"""
        validation_logger = logging.getLogger('classification_validation')
        
        corrections_applied = False
        original_classification = agent_decision.classification
        original_confidence = agent_decision.confidence
        
        # Check if validation suggests strong alternative
        alternatives = validation_results.get('alternative_suggestions', [])
        
        if not validation_results['is_valid'] and alternatives:
            # Strong alternative with higher score
            best_alternative = alternatives[0]
            if best_alternative['score'] > 0.6:
                agent_decision.classification = best_alternative['type']
                agent_decision.confidence = min(0.8, best_alternative['confidence'])
                agent_decision.reasoning += f" | Validation correction: Changed from {original_classification} to {best_alternative['type']} based on validation rules"
                corrections_applied = True
                
                validation_logger.info(f"Applied validation correction: {original_classification} -> {best_alternative['type']}")
                self.system_stats["validation_corrections"] += 1
                
                if original_classification == 'product_attribute':
                    self.system_stats["product_attribute_corrections"] += 1
        
        # Apply confidence adjustments
        confidence_adjustment = validation_results.get('confidence_adjustment', 0.0)
        if confidence_adjustment != 0.0:
            new_confidence = max(0.0, min(1.0, agent_decision.confidence + confidence_adjustment))
            
            if abs(new_confidence - agent_decision.confidence) > 0.1:  # Significant adjustment
                validation_logger.info(f"Applied confidence adjustment: {agent_decision.confidence:.2f} -> {new_confidence:.2f}")
                agent_decision.confidence = new_confidence
                corrections_applied = True
        
        # Update metadata with validation info
        if not hasattr(agent_decision, 'metadata') or agent_decision.metadata is None:
            agent_decision.metadata = {}
        
        agent_decision.metadata.update({
            'validation_applied': True,
            'corrections_applied': corrections_applied,
            'original_classification': original_classification,
            'original_confidence': original_confidence,
            'validation_score': validation_results.get('validation_score', 0.0)
        })
        
        return agent_decision
    
    def _extract_vector_insights(self, agent_decision: AgentDecision) -> Dict[str, Any]:
        """Extract vector insights from agent decision"""
        vector_insights = {
            'vector_enhanced': False,
            'vector_boost': 0.0,
            'max_similarity': 0.0,
            'collections_searched': 0,
            'total_matches': 0,
            'agent_vector_contributions': {}
        }
        
        try:
            evidence = agent_decision.evidence or {}
            
            # Check if decision has vector insights from voting
            if 'vector_insights' in evidence:
                voting_vector_insights = evidence['vector_insights']
                vector_insights.update({
                    'vector_enhanced': voting_vector_insights.get('avg_vector_boost', 0) > 0,
                    'vector_boost': voting_vector_insights.get('avg_vector_boost', 0),
                    'max_similarity': voting_vector_insights.get('max_similarity', 0),
                    'collections_searched': len(voting_vector_insights.get('collection_distribution', {})),
                    'total_matches': sum(voting_vector_insights.get('collection_distribution', {}).values()),
                    'agent_vector_contributions': voting_vector_insights.get('agent_vector_results', {})
                })
            
            # Check metadata for vector information
            metadata = agent_decision.metadata or {}
            if metadata.get('vector_enhanced'):
                vector_insights['vector_enhanced'] = True
                vector_insights['vector_boost'] = metadata.get('vector_boost', 0)
            
        except Exception as e:
            self.logger.warning(f"Failed to extract vector insights: {e}")
        
        return vector_insights
    
    async def _post_process_enhanced_results(self, agent_decision: AgentDecision, 
                                           file_context: FileContext,
                                           request: ClassificationRequest,
                                           vector_insights: Dict[str, Any],
                                           validation_results: Dict[str, Any]) -> ClassificationResponse:
        """Post-process classification results with enhanced validation insights"""
        try:
            # Determine quality level with validation consideration
            quality_level = self._assess_enhanced_quality_level(agent_decision, validation_results)
            
            # Generate enhanced recommendations
            recommendations = self._generate_enhanced_recommendations(
                agent_decision, file_context, quality_level, vector_insights, validation_results
            )
            
            # Enhance reasoning with AI if available and needed
            enhanced_reasoning = agent_decision.reasoning
            if self.azure_openai and (quality_level != "high" or validation_results.get('concerns')):
                try:
                    enhanced_reasoning = await self._enhance_reasoning_with_ai(
                        agent_decision, file_context
                    )
                except Exception as e:
                    self.logger.warning(f"AI reasoning enhancement failed: {e}")
            
            # Build enhanced response
            response = ClassificationResponse(
                success=True,
                classification=agent_decision.classification,
                confidence=agent_decision.confidence,
                reasoning=enhanced_reasoning,
                evidence=agent_decision.evidence,
                processing_time=agent_decision.processing_time,
                agent_details=self._extract_enhanced_agent_details(agent_decision),
                recommendations=recommendations,
                vector_insights=vector_insights,
                validation_results=validation_results
            )
            
            return response
            
        except Exception as e:
            self.logger.error(f"Enhanced post-processing failed: {e}")
            return self._create_error_response(str(e), 0.0)
    
    def _assess_enhanced_quality_level(self, agent_decision: AgentDecision, 
                                     validation_results: Dict[str, Any]) -> str:
        """Assess quality level with validation considerations"""
        quality_thresholds = self.config.get("agentic_system", {}).get("quality_thresholds", {})
        
        high_threshold = quality_thresholds.get("high_quality", 0.8)
        medium_threshold = quality_thresholds.get("medium_quality", 0.6)
        
        # Start with agent confidence
        effective_confidence = agent_decision.confidence
        
        # Adjust for validation results
        validation_score = validation_results.get('validation_score', 0.5)
        if validation_score < 0.3:
            effective_confidence *= 0.7  # Reduce for poor validation
        elif validation_score > 0.8:
            effective_confidence = min(1.0, effective_confidence * 1.1)  # Boost for good validation
        
        # Consider vector enhancement
        if hasattr(agent_decision, 'metadata') and agent_decision.metadata:
            vector_boost = agent_decision.metadata.get('vector_boost', 0)
            effective_confidence += vector_boost * 0.3  # Partial credit for vector boost
        
        # Penalty for validation concerns
        concern_count = len(validation_results.get('concerns', []))
        if concern_count > 0:
            effective_confidence -= concern_count * 0.1
        
        # Determine quality level
        if effective_confidence >= high_threshold and validation_results.get('is_valid', True):
            return "high"
        elif effective_confidence >= medium_threshold:
            return "medium"
        else:
            return "low"
    
    def _generate_enhanced_recommendations(self, agent_decision: AgentDecision, 
                                         file_context: FileContext, quality_level: str,
                                         vector_insights: Dict[str, Any],
                                         validation_results: Dict[str, Any]) -> List[str]:
        """Generate enhanced recommendations with validation insights"""
        recommendations = []
        
        # Validation-based recommendations
        if not validation_results.get('is_valid', True):
            recommendations.append("⚠️ Classification validation failed - manual review strongly recommended")
            
            alternatives = validation_results.get('alternative_suggestions', [])
            if alternatives:
                best_alt = alternatives[0]
                recommendations.append(f"Consider alternative classification: {best_alt['type']} (score: {best_alt['score']:.2f})")
        
        # Quality-based recommendations
        if quality_level == "low":
            recommendations.append("Low confidence classification - add more context or metadata")
            recommendations.append("Manual validation recommended before automated processing")
            
            if not vector_insights.get('vector_enhanced'):
                recommendations.append("Upload similar files with correct labels to improve future classification")
        elif quality_level == "medium":
            recommendations.append("Review classification result and provide feedback for system improvement")
        else:
            recommendations.append("✅ High confidence classification - ready for automated processing")
        
        # Validation concerns
        concerns = validation_results.get('concerns', [])
        if concerns:
            recommendations.append(f"Validation concerns identified: {len(concerns)} issues found")
            for concern in concerns[:2]:  # Show first 2 concerns
                recommendations.append(f"  • {concern}")
        
        # Vector enhancement insights
        if vector_insights.get('vector_enhanced'):
            recommendations.append(f"✅ Enhanced by semantic similarity (boost: +{vector_insights.get('vector_boost', 0):.3f})")
            
            if vector_insights.get('max_similarity', 0) > 0.8:
                recommendations.append("Very similar files found in knowledge base - high confidence in classification")
        
        # Classification-specific enhanced recommendations
        classification = agent_decision.classification
        
        if classification == "syndicated":
            recommendations.append("✅ Syndicated data identified - verify data source (Nielsen, IRI, etc.)")
            recommendations.append("Ensure TDP/ACV metrics align with business requirements")
            self.system_stats["syndicated_correct_classifications"] += 1
            
        elif classification == "pos":
            recommendations.append("✅ POS data identified - validate UPC/SKU mapping for downstream analysis")
            recommendations.append("Confirm transaction-level granularity meets analytical needs")
            self.system_stats["pos_correct_classifications"] += 1
            
        elif classification == "product_attribute":
            # Extra validation for product_attribute
            validation_score = validation_results.get('validation_score', 0.5)
            if validation_score < 0.6:
                recommendations.append("⚠️ Product attribute classification has low validation score - verify accuracy")
            recommendations.append("Confirm product hierarchy alignment with master data standards")
            
        elif classification == "unknown":
            recommendations.append("⚠️ Classification uncertain - consider manual classification")
            recommendations.append("Add business context or domain-specific metadata to improve accuracy")
        
        # Evidence-based recommendations
        evidence = agent_decision.evidence or {}
        consensus_level = evidence.get("metadata", {}).get("consensus_level", 0.0)
        
        if consensus_level < 0.6:
            recommendations.append("Low agent consensus detected - consider additional training data")
        
        # Corrections applied recommendations
        if validation_results.get('corrections_applied'):
            recommendations.append("✅ Validation corrections were applied to improve classification accuracy")
        
        return recommendations
    
    def _extract_enhanced_agent_details(self, agent_decision: AgentDecision) -> Dict[str, Any]:
        """Extract enhanced agent details with validation information"""
        # Get voting result from evidence
        voting_result = agent_decision.evidence.get("voting_result", {})
        
        # Handle both dictionary and VotingResult object cases
        if hasattr(voting_result, '__dict__'):
            voting_dict = voting_result.__dict__ if hasattr(voting_result, '__dict__') else {}
        else:
            voting_dict = voting_result
        
        # Extract vector and validation insights
        vector_insights = agent_decision.evidence.get("vector_insights", {})
        validation_metadata = getattr(agent_decision, 'metadata', {})
        
        return {
            "supervisor_agent": agent_decision.agent_id,
            "consensus_level": voting_dict.get("consensus_level", 0.0),
            "participating_agents": voting_dict.get("participating_agents", []),
            "individual_decisions": self._format_individual_decisions(voting_dict.get("agent_decisions", [])),
            "validation_checks": agent_decision.evidence.get("validation_checks", {}),
            "quality_score": agent_decision.evidence.get("quality_score", 0.0),
            "agent_metadata": validation_metadata,
            "vector_contribution": {
                "avg_boost": vector_insights.get('avg_vector_boost', 0),
                "max_similarity": vector_insights.get('max_similarity', 0),
                "collections_used": len(vector_insights.get('collection_distribution', {})),
                "total_matches": sum(vector_insights.get('collection_distribution', {}).values())
            },
            "validation_details": {
                "validation_applied": validation_metadata.get('validation_applied', False),
                "corrections_applied": validation_metadata.get('corrections_applied', False),
                "original_classification": validation_metadata.get('original_classification'),
                "validation_score": validation_metadata.get('validation_score', 0.0)
            }
        }
    
    def _format_individual_decisions(self, agent_decisions: List) -> List[Dict[str, Any]]:
        """Format individual agent decisions with enhanced information"""
        formatted_decisions = []
        
        for decision in agent_decisions:
            if hasattr(decision, 'agent_id'):
                # Extract vector boost for this agent
                vector_boost = 0.0
                if hasattr(decision, 'evidence') and decision.evidence:
                    vector_results = decision.evidence.get('vector_results', {})
                    vector_boost = vector_results.get('confidence_boost', 0)
                
                # Format decision with enhanced information
                formatted_decisions.append({
                    "agent_id": decision.agent_id,
                    "agent_type": decision.agent_type.value if hasattr(decision.agent_type, 'value') else str(decision.agent_type),
                    "classification": decision.classification,
                    "confidence": decision.confidence,
                    "vector_boost": vector_boost,
                    "reasoning": decision.reasoning[:100] + "..." if len(decision.reasoning) > 100 else decision.reasoning,
                    "validation_applied": getattr(decision, 'metadata', {}).get('validation_applied', False)
                })
            elif isinstance(decision, dict):
                # If it's already a dictionary
                formatted_decisions.append({
                    "agent_id": decision.get("agent_id", "unknown"),
                    "agent_type": decision.get("agent_type", "unknown"),
                    "classification": decision.get("classification", "unknown"),
                    "confidence": decision.get("confidence", 0.0),
                    "vector_boost": decision.get("vector_boost", 0.0),
                    "reasoning": decision.get("reasoning", "")[:100] + "..." if len(decision.get("reasoning", "")) > 100 else decision.get("reasoning", ""),
                    "validation_applied": decision.get("validation_applied", False)
                })
        
        return formatted_decisions
    
    async def _enhance_reasoning_with_ai(self, agent_decision: AgentDecision, 
                                       file_context: FileContext) -> str:
        """Enhance reasoning using Azure OpenAI with validation context"""
        try:
            ai_reasoning = await self.azure_openai.enhance_classification_reasoning(
                classification=agent_decision.classification,
                confidence=agent_decision.confidence,
                original_reasoning=agent_decision.reasoning,
                file_context={
                    "file_name": file_context.file_name,
                    "columns": file_context.extracted_content.get('schema', {}).get('columns', []),
                    "patterns": file_context.extracted_content.get('data_patterns', []),
                    "validation_applied": getattr(agent_decision, 'metadata', {}).get('validation_applied', False)
                }
            )
            
            if ai_reasoning:
                return f"{agent_decision.reasoning} | Enhanced AI Analysis: {ai_reasoning}"
            else:
                return agent_decision.reasoning
                
        except Exception as e:
            self.logger.warning(f"AI reasoning enhancement failed: {e}")
            return agent_decision.reasoning
    
    async def _store_classification_result(self, request: ClassificationRequest,
                                         response: ClassificationResponse,
                                         agent_decision: AgentDecision):
        """Store classification result with enhanced validation information"""
        try:
            # Create enhanced learning document
            learning_data = {
                "file_path": request.file_path,
                "classification": response.classification,
                "confidence": response.confidence,
                "processing_time": response.processing_time,
                "agent_details": response.agent_details,
                "user_context": request.user_context,
                "vector_enhanced": response.vector_insights.get('vector_enhanced', False),
                "vector_boost": response.vector_insights.get('vector_boost', 0),
                "validation_applied": response.validation_results.get('validation_applied', False),
                "validation_score": response.validation_results.get('validation_score', 0.0),
                "corrections_applied": response.validation_results.get('corrections_applied', False),
                "timestamp": time.time()
            }
            
            # Store in existing learning system with enhanced metadata
            if hasattr(self.classification_engine, 'learning_engine'):
                self.classification_engine.learning_engine.store_learning_feedback({
                    'file_path': request.file_path,
                    'predicted': response.classification,
                    'correct': response.classification,  # Assuming correct for now
                    'confidence': response.confidence,
                    'learning_type': 'enhanced_training_example',
                    'description': f"Enhanced agentic classification with validation: {response.reasoning}",
                    'file_features': self._safe_dict_convert(agent_decision.evidence),
                    'validation_metadata': response.validation_results
                })
            
            # Log enhanced storage
            validation_logger = logging.getLogger('classification_validation')
            validation_logger.info(f"Stored enhanced classification result: {response.classification} "
                                 f"(validation_score: {response.validation_results.get('validation_score', 0):.2f})")
            
        except Exception as e:
            self.logger.warning(f"Failed to store enhanced classification result: {e}")
    
    def _safe_dict_convert(self, obj: Any) -> Dict[str, Any]:
        """Safely convert an object to dictionary"""
        if isinstance(obj, dict):
            return obj
        elif hasattr(obj, '__dict__'):
            return obj.__dict__
        elif hasattr(obj, '_asdict'):
            return obj._asdict()
        else:
            return {}
    
    def _update_enhanced_system_stats(self, response: ClassificationResponse, processing_time: float,
                                    vector_insights: Dict[str, Any], validation_results: Dict[str, Any]):
        """Update enhanced system statistics"""
        if response.success:
            self.system_stats["successful_classifications"] += 1
            
            # Update average confidence
            total_confidence = (self.system_stats["avg_confidence"] * 
                              (self.system_stats["successful_classifications"] - 1) + 
                              response.confidence)
            self.system_stats["avg_confidence"] = (
                total_confidence / self.system_stats["successful_classifications"]
            )
        else:
            self.system_stats["failed_classifications"] += 1
        
        # Update average processing time
        total_time = (self.system_stats["avg_processing_time"] * 
                     (self.system_stats["total_requests"] - 1) + processing_time)
        self.system_stats["avg_processing_time"] = (
            total_time / self.system_stats["total_requests"]
        )
        
        # Update vector-specific metrics
        if vector_insights.get('vector_enhanced'):
            self.system_stats["vector_enhanced_count"] += 1
        
        # Update vector boost statistics
        vector_boost = vector_insights.get('vector_boost', 0)
        total_vector_boost = (self.system_stats["avg_vector_boost"] * 
                             (self.system_stats["total_requests"] - 1) + vector_boost)
        self.system_stats["avg_vector_boost"] = (
            total_vector_boost / self.system_stats["total_requests"]
        )
        
        # Update vector DB query statistics
        if vector_insights.get('collections_searched', 0) > 0:
            self.system_stats["vector_db_queries"] += 1
            
            if vector_insights.get('total_matches', 0) > 0:
                self.system_stats["vector_db_successful_matches"] += 1
    
    def _create_error_response(self, error_message: str, start_time: float) -> ClassificationResponse:
        """Create enhanced error response"""
        return ClassificationResponse(
            success=False,
            classification="unknown",
            confidence=0.0,
            reasoning=f"Enhanced classification failed: {error_message}",
            evidence={},
            processing_time=time.time() - start_time,
            agent_details={},
            recommendations=["Review file format and try again", "Check system logs for detailed error information", "Consider manual classification"],
            vector_insights={"vector_enhanced": False, "error": error_message},
            validation_results={"is_valid": False, "error": error_message},
            error_message=error_message
        )
    
    def get_system_statistics(self) -> Dict[str, Any]:
        """Get comprehensive enhanced system statistics"""
        stats = self.system_stats.copy()
        
        # Add success rate and enhanced metrics
        if stats["total_requests"] > 0:
            stats["success_rate"] = stats["successful_classifications"] / stats["total_requests"]
            stats["vector_enhancement_rate"] = stats["vector_enhanced_count"] / stats["total_requests"]
            stats["vector_db_hit_rate"] = (stats["vector_db_successful_matches"] / 
                                          max(stats["vector_db_queries"], 1))
            stats["validation_correction_rate"] = stats["validation_corrections"] / stats["total_requests"]
            stats["product_attribute_correction_rate"] = stats["product_attribute_corrections"] / max(stats["total_requests"], 1)
        else:
            stats["success_rate"] = 0.0
            stats["vector_enhancement_rate"] = 0.0
            stats["vector_db_hit_rate"] = 0.0
            stats["validation_correction_rate"] = 0.0
            stats["product_attribute_correction_rate"] = 0.0
        
        # Add supervisor statistics
        if self.supervisor_agent:
            stats["supervisor_stats"] = self.supervisor_agent.get_supervisor_statistics()
        
        # Add knowledge base statistics
        if self.classification_engine:
            stats["knowledge_base_stats"] = self.classification_engine.get_knowledge_base_stats()
        
        # Add vector DB statistics
        if self.vector_db:
            try:
                vector_stats = self.vector_db.get_collection_stats()
                stats["vector_db_collections"] = vector_stats
            except Exception as e:
                stats["vector_db_collections"] = {"error": str(e)}
        
        # Add validation statistics
        stats["validation_stats"] = {
            "total_corrections": stats["validation_corrections"],
            "product_attribute_corrections": stats["product_attribute_corrections"],
            "syndicated_correct": stats["syndicated_correct_classifications"],
            "pos_correct": stats["pos_correct_classifications"]
        }
        
        return stats
    
    async def add_training_example(self, file_path: str, correct_classification: str, 
                                 description: str = "") -> Dict[str, Any]:
        """Add a training example to the enhanced system"""
        try:
            # Upload file to knowledge base
            result = self.classification_engine.upload_file_with_metadata(
                file_path=file_path,
                document_type=correct_classification,
                description=description
            )
            
            if result.get('success'):
                self.logger.info(f"Enhanced training example added: {file_path} -> {correct_classification}")
                
                # Log validation-enhanced vector DB update
                validation_logger = logging.getLogger('classification_validation')
                validation_logger.info(f"Training example stored with enhanced validation: {correct_classification}")
                
                return {
                    "success": True,
                    "message": "Enhanced training example added successfully with validation",
                    "document_id": result.get('document_id')
                }
            else:
                return {
                    "success": False,
                    "message": f"Failed to add enhanced training example: {result.get('error')}"
                }
                
        except Exception as e:
            self.logger.error(f"Failed to add enhanced training example: {e}")
            return {
                "success": False,
                "message": f"Error adding enhanced training example: {str(e)}"
            }
    
    async def get_classification_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get recent classification history with enhanced validation information"""
        try:
            if self.supervisor_agent:
                history = self.supervisor_agent.classification_history[-limit:]
                
                # Add enhanced information to history
                for entry in history:
                    if 'vector_boost' not in entry:
                        entry['vector_boost'] = 0.0
                    if 'vector_enhanced' not in entry:
                        entry['vector_enhanced'] = entry.get('vector_boost', 0) > 0
                    if 'validation_applied' not in entry:
                        entry['validation_applied'] = False
                    if 'corrections_applied' not in entry:
                        entry['corrections_applied'] = False
                
                return history
            else:
                return []
        except Exception as e:
            self.logger.error(f"Failed to get enhanced classification history: {e}")
            return []
    
    def shutdown(self):
        """Shutdown the enhanced agentic system"""
        try:
            if self.supervisor_agent:
                self.supervisor_agent.deactivate()
            
            # Log shutdown with enhanced final statistics
            stats = self.get_system_statistics()
            self.logger.info(f"Enhanced Agentic System shutdown - Final stats: "
                           f"Total: {stats.get('total_requests', 0)}, "
                           f"Success rate: {stats.get('success_rate', 0):.2%}, "
                           f"Validation corrections: {stats.get('validation_corrections', 0)}, "
                           f"Product attribute corrections: {stats.get('product_attribute_corrections', 0)}")
            
            # Log validation-specific final state
            validation_logger = logging.getLogger('classification_validation')
            validation_logger.info(f"Validation final stats: "
                                 f"Correction rate: {stats.get('validation_correction_rate', 0):.2%}, "
                                 f"Syndicated correct: {stats.get('syndicated_correct_classifications', 0)}, "
                                 f"POS correct: {stats.get('pos_correct_classifications', 0)}")
            
            self.logger.info("Enhanced Agentic Classification System shutdown completed")
            
        except Exception as e:
            self.logger.error(f"Error during enhanced shutdown: {e}")


# Enhanced usage example
async def main():
    """Example usage of the enhanced agentic classification system"""
    # Initialize enhanced system
    system = AgenticClassificationSystem()
    
    # Example classification request
    request = ClassificationRequest(
        file_path="sample_syndicated_data.csv",
        user_context={
            "source": "client_upload",
            "business_unit": "rgm_analytics",
            "expected_type": "syndicated"
        },
        processing_options={
            "include_ai_enhancement": True,
            "detailed_reasoning": True,
            "enable_vector_search": True,
            "enable_validation": True
        }
    )
    
    # Classify file with enhanced validation
    response = await system.classify_file(request)
    
    # Print enhanced results
    print(f"Enhanced Classification: {response.classification}")
    print(f"Confidence: {response.confidence:.2f}")
    print(f"Validation Applied: {response.validation_results.get('validation_applied', False)}")
    print(f"Corrections Applied: {response.validation_results.get('corrections_applied', False)}")
    if response.vector_insights.get('vector_enhanced'):
        print(f"Vector Boost: +{response.vector_insights.get('vector_boost', 0):.3f}")
    print(f"Enhanced Reasoning: {response.reasoning}")
    print(f"Enhanced Recommendations: {len(response.recommendations)} items")
    
    # Get enhanced system statistics
    stats = system.get_system_statistics()
    print(f"Enhanced System Stats: Success rate: {stats.get('success_rate', 0):.2%}, "
          f"Validation corrections: {stats.get('validation_corrections', 0)}")
    
    # Shutdown
    system.shutdown()

if __name__ == "__main__":
    asyncio.run(main())