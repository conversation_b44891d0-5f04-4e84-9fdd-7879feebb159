"""
Pattern Recognition and Feedback Collection Agents
Additional specialized agents for enhanced classification capabilities
"""

import time
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from collections import Counter, defaultdict
import re
from datetime import datetime
import asyncio
import sys
from pathlib import Path


# Add project root to Python path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))


from agents.base_agent import BaseAgent, AgentDecision, FileContext, AgentType, CPGDomainKnowledge

class PatternRecognizerAgent(BaseAgent):
    """Agent specialized in recognizing complex data patterns and relationships"""
    
    def __init__(self, agent_id: str, config: Dict[str, Any]):
        super().__init__(agent_id, AgentType.PATTERN_RECOGNIZER, config)
        self.capabilities = [
            "temporal_pattern_recognition",
            "hierarchical_structure_detection",
            "relationship_mapping",
            "anomaly_detection",
            "data_flow_analysis"
        ]
        
        # Pattern libraries
        self.cpg_patterns = self._initialize_cpg_patterns()
        self.temporal_patterns = self._initialize_temporal_patterns()
        self.structural_patterns = self._initialize_structural_patterns()
    
    def _initialize_cpg_patterns(self) -> Dict[str, Any]:
        """Initialize CPG-specific pattern recognition rules"""
        return {
            "syndicated_patterns": {
                "metric_combinations": [
                    ["tdp", "acv", "velocity"],
                    ["units", "dollars", "share"],
                    ["distribution", "velocity", "penetration"]
                ],
                "hierarchy_patterns": [
                    ["total_us", "region", "market"],
                    ["category", "subcategory", "segment"],
                    ["manufacturer", "brand", "variant"]
                ],
                "temporal_indicators": ["week", "period", "cycle"]
            },
            
            "pos_patterns": {
                "transaction_patterns": [
                    ["transaction_id", "upc", "quantity", "amount"],
                    ["store", "date", "item", "sales"],
                    ["register", "time", "product", "price"]
                ],
                "identifier_patterns": [
                    ["upc", "sku", "gtin"],
                    ["store_id", "location_id", "outlet_id"],
                    ["transaction", "receipt", "basket"]
                ],
                "operational_indicators": ["scan", "checkout", "register", "tender"]
            },
            
            "product_patterns": {
                "attribute_patterns": [
                    ["brand", "category", "subcategory"],
                    ["size", "flavor", "variant"],
                    ["package", "format", "type"]
                ],
                "hierarchy_patterns": [
                    ["level_1", "level_2", "level_3"],
                    ["division", "category", "subcategory"],
                    ["global_brand", "local_brand", "variant"]
                ],
                "master_data_indicators": ["master", "reference", "lookup", "dimension"]
            },
            
            "financial_patterns": {
                "cost_patterns": [
                    ["cost", "price", "margin"],
                    ["gross", "net", "profit"],
                    ["rebate", "allowance", "discount"]
                ],
                "pricing_patterns": [
                    ["list_price", "net_price", "invoice_price"],
                    ["msrp", "wholesale", "retail"],
                    ["base_price", "promotional_price", "effective_price"]
                ]
            }
        }
    
    def _initialize_temporal_patterns(self) -> Dict[str, Any]:
        """Initialize temporal pattern recognition rules"""
        return {
            "frequency_patterns": {
                "daily": ["daily", "day", "date"],
                "weekly": ["weekly", "week", "wk"],
                "monthly": ["monthly", "month", "mo"],
                "quarterly": ["quarterly", "quarter", "qtr"],
                "yearly": ["yearly", "year", "annual"]
            },
            "calendar_patterns": {
                "retail_calendar": ["retail_week", "retail_month", "retail_year"],
                "fiscal_calendar": ["fiscal_week", "fiscal_month", "fiscal_year"],
                "promotional_calendar": ["promo_week", "campaign_period", "event_period"]
            },
            "seasonal_patterns": [
                "holiday", "season", "peak", "back_to_school", "summer", "winter"
            ]
        }
    
    def _initialize_structural_patterns(self) -> Dict[str, Any]:
        """Initialize structural pattern recognition rules"""
        return {
            "fact_table_patterns": {
                "indicators": ["fact", "measure", "metric", "kpi"],
                "structure": ["dimension_keys", "measures", "aggregation_level"],
                "naming": ["_fact", "_measures", "_metrics"]
            },
            "dimension_patterns": {
                "indicators": ["dim", "dimension", "lookup", "reference"],
                "structure": ["key", "description", "hierarchy", "attributes"],
                "naming": ["dim_", "_dim", "_lookup", "_ref"]
            },
            "bridge_patterns": {
                "indicators": ["bridge", "mapping", "cross_reference", "xref"],
                "structure": ["source_key", "target_key", "mapping_type"],
                "naming": ["_bridge", "_mapping", "_xref"]
            }
        }
    
    async def analyze(self, file_context: FileContext) -> AgentDecision:
        """Analyze file for complex patterns and relationships"""
        start_time = time.time()
        
        try:
            # Extract comprehensive pattern features
            pattern_analysis = self._perform_pattern_analysis(file_context)
            
            # Detect structural relationships
            structural_analysis = self._analyze_data_structure(file_context)
            
            # Analyze temporal patterns
            temporal_analysis = self._analyze_temporal_patterns(file_context)
            
            # Detect business process patterns
            process_analysis = self._analyze_business_processes(file_context)
            
            # Calculate pattern-based classification scores
            classification_scores = self._calculate_pattern_scores(
                pattern_analysis, structural_analysis, temporal_analysis, process_analysis
            )
            
            best_classification = max(classification_scores.items(), key=lambda x: x[1])
            
            decision = AgentDecision(
                agent_id=self.agent_id,
                agent_type=self.agent_type,
                classification=best_classification[0],
                confidence=best_classification[1],
                reasoning=self._generate_pattern_reasoning(
                    pattern_analysis, structural_analysis, temporal_analysis, classification_scores
                ),
                evidence={
                    "pattern_analysis": pattern_analysis,
                    "structural_analysis": structural_analysis,
                    "temporal_analysis": temporal_analysis,
                    "process_analysis": process_analysis,
                    "classification_scores": classification_scores
                },
                processing_time=time.time() - start_time
            )
            
            self.update_performance_metrics(decision)
            return decision
            
        except Exception as e:
            self.logger.error(f"Pattern recognition failed: {e}")
            return AgentDecision(
                agent_id=self.agent_id,
                agent_type=self.agent_type,
                classification="unknown",
                confidence=0.0,
                reasoning=f"Pattern recognition error: {str(e)}",
                processing_time=time.time() - start_time
            )
    
    def _perform_pattern_analysis(self, file_context: FileContext) -> Dict[str, Any]:
        """Perform comprehensive pattern analysis"""
        schema = file_context.extracted_content.get('schema', {})
        columns = [col.lower() for col in schema.get('columns', [])]
        
        analysis = {
            "detected_patterns": [],
            "pattern_strength": {},
            "column_groupings": {},
            "naming_conventions": {},
            "data_relationships": {}
        }
        
        # Detect CPG-specific patterns
        for category, patterns in self.cpg_patterns.items():
            category_strength = 0.0
            detected_in_category = []
            
            if "metric_combinations" in patterns:
                for combo in patterns["metric_combinations"]:
                    match_count = sum(1 for metric in combo if any(metric in col for col in columns))
                    if match_count >= 2:
                        detected_in_category.append(f"metric_combo_{combo}")
                        category_strength += match_count / len(combo)
            
            if "hierarchy_patterns" in patterns:
                for hierarchy in patterns["hierarchy_patterns"]:
                    match_count = sum(1 for level in hierarchy if any(level in col for col in columns))
                    if match_count >= 2:
                        detected_in_category.append(f"hierarchy_{hierarchy}")
                        category_strength += match_count / len(hierarchy)
            
            if detected_in_category:
                analysis["detected_patterns"].extend(detected_in_category)
                analysis["pattern_strength"][category] = category_strength
        
        # Analyze column groupings
        analysis["column_groupings"] = self._group_related_columns(columns)
        
        # Analyze naming conventions
        analysis["naming_conventions"] = self._analyze_naming_conventions(columns)
        
        return analysis
    
    def _analyze_data_structure(self, file_context: FileContext) -> Dict[str, Any]:
        """Analyze data structure and table type"""
        schema = file_context.extracted_content.get('schema', {})
        columns = [col.lower() for col in schema.get('columns', [])]
        
        structure_analysis = {
            "table_type": "unknown",
            "dimension_indicators": [],
            "fact_indicators": [],
            "key_columns": [],
            "measure_columns": [],
            "attribute_columns": []
        }
        
        # Detect fact table patterns
        fact_indicators = []
        for pattern_type, patterns in self.structural_patterns["fact_table_patterns"].items():
            for pattern in patterns:
                if any(pattern in col for col in columns):
                    fact_indicators.append(pattern)
        
        # Detect dimension table patterns
        dim_indicators = []
        for pattern_type, patterns in self.structural_patterns["dimension_patterns"].items():
            for pattern in patterns:
                if any(pattern in col for col in columns):
                    dim_indicators.append(pattern)
        
        # Classify table type
        if len(fact_indicators) > len(dim_indicators):
            structure_analysis["table_type"] = "fact_table"
        elif len(dim_indicators) > len(fact_indicators):
            structure_analysis["table_type"] = "dimension_table"
        elif any("bridge" in col or "mapping" in col for col in columns):
            structure_analysis["table_type"] = "bridge_table"
        else:
            structure_analysis["table_type"] = "mixed_or_flat"
        
        # Categorize columns
        structure_analysis["key_columns"] = [col for col in columns if self._is_key_column(col)]
        structure_analysis["measure_columns"] = [col for col in columns if self._is_measure_column(col)]
        structure_analysis["attribute_columns"] = [col for col in columns if self._is_attribute_column(col)]
        
        structure_analysis["fact_indicators"] = fact_indicators
        structure_analysis["dimension_indicators"] = dim_indicators
        
        return structure_analysis
    
    def _analyze_temporal_patterns(self, file_context: FileContext) -> Dict[str, Any]:
        """Analyze temporal patterns in the data"""
        schema = file_context.extracted_content.get('schema', {})
        columns = [col.lower() for col in schema.get('columns', [])]
        
        temporal_analysis = {
            "has_temporal_data": False,
            "frequency_indicators": [],
            "calendar_type": "unknown",
            "temporal_columns": [],
            "granularity": "unknown"
        }
        
        # Detect temporal columns
        temporal_columns = []
        for col in columns:
            if any(temp_indicator in col for temp_indicator in ['date', 'time', 'week', 'month', 'year', 'period']):
                temporal_columns.append(col)
        
        if temporal_columns:
            temporal_analysis["has_temporal_data"] = True
            temporal_analysis["temporal_columns"] = temporal_columns
        
        # Detect frequency patterns
        for frequency, indicators in self.temporal_patterns["frequency_patterns"].items():
            for indicator in indicators:
                if any(indicator in col for col in columns):
                    temporal_analysis["frequency_indicators"].append(frequency)
        
        # Detect calendar type
        for calendar_type, indicators in self.temporal_patterns["calendar_patterns"].items():
            for indicator in indicators:
                if any(indicator in col for col in columns):
                    temporal_analysis["calendar_type"] = calendar_type
                    break
        
        # Determine granularity
        if any("day" in col or "date" in col for col in temporal_columns):
            temporal_analysis["granularity"] = "daily"
        elif any("week" in col for col in temporal_columns):
            temporal_analysis["granularity"] = "weekly"
        elif any("month" in col for col in temporal_columns):
            temporal_analysis["granularity"] = "monthly"
        
        return temporal_analysis
    
    def _analyze_business_processes(self, file_context: FileContext) -> Dict[str, Any]:
        """Analyze business process patterns"""
        schema = file_context.extracted_content.get('schema', {})
        columns = [col.lower() for col in schema.get('columns', [])]
        content = file_context.extracted_content.get('content', '').lower()
        
        process_analysis = {
            "identified_processes": [],
            "process_strength": {},
            "data_flow_indicators": [],
            "integration_patterns": []
        }
        
        # Define business process indicators
        process_indicators = {
            "sales_analytics": ["sales", "revenue", "units", "volume", "performance"],
            "trade_promotion": ["promotion", "promo", "tpr", "display", "feature", "campaign"],
            "supply_chain": ["supply", "shipment", "inventory", "depletion", "distribution"],
            "customer_analytics": ["customer", "shopper", "consumer", "demographic", "segment"],
            "product_management": ["product", "item", "brand", "category", "portfolio"],
            "pricing_optimization": ["price", "cost", "margin", "elasticity", "optimization"],
            "market_research": ["market", "research", "survey", "panel", "tracking"],
            "financial_planning": ["budget", "forecast", "plan", "variance", "actual"]
        }
        
        # Score each process
        for process, indicators in process_indicators.items():
            score = 0.0
            matched_indicators = []
            
            for indicator in indicators:
                column_matches = sum(1 for col in columns if indicator in col)
                content_matches = content.count(indicator)
                
                if column_matches > 0 or content_matches > 0:
                    matched_indicators.append(indicator)
                    score += column_matches * 0.5 + min(content_matches * 0.1, 0.5)
            
            if score > 0.5:
                process_analysis["identified_processes"].append(process)
                process_analysis["process_strength"][process] = {
                    "score": score,
                    "matched_indicators": matched_indicators
                }
        
        return process_analysis
    
    def _group_related_columns(self, columns: List[str]) -> Dict[str, List[str]]:
        """Group related columns based on naming patterns"""
        groups = defaultdict(list)
        
        # Common prefixes and suffixes
        prefixes = ["total_", "avg_", "sum_", "max_", "min_", "pct_", "rate_"]
        suffixes = ["_total", "_avg", "_sum", "_max", "_min", "_pct", "_rate", "_id", "_key", "_desc"]
        
        for col in columns:
            # Group by prefix
            for prefix in prefixes:
                if col.startswith(prefix):
                    groups[f"prefix_{prefix}"].append(col)
            
            # Group by suffix
            for suffix in suffixes:
                if col.endswith(suffix):
                    groups[f"suffix_{suffix}"].append(col)
            
            # Group by common words
            words = col.split('_')
            for word in words:
                if len(word) > 3:  # Ignore short words
                    groups[f"word_{word}"].append(col)
        
        # Filter groups with more than one member
        return {k: v for k, v in groups.items() if len(v) > 1}
    
    def _analyze_naming_conventions(self, columns: List[str]) -> Dict[str, Any]:
        """Analyze column naming conventions"""
        conventions = {
            "snake_case": sum(1 for col in columns if '_' in col),
            "camel_case": sum(1 for col in columns if re.search(r'[a-z][A-Z]', col)),
            "upper_case": sum(1 for col in columns if col.isupper()),
            "lower_case": sum(1 for col in columns if col.islower()),
            "contains_numbers": sum(1 for col in columns if any(c.isdigit() for c in col)),
            "avg_length": sum(len(col) for col in columns) / len(columns) if columns else 0
        }
        
        # Determine dominant convention
        dominant_convention = max(conventions.items(), key=lambda x: x[1] if x[0] != "avg_length" else 0)[0]
        
        return {
            "conventions": conventions,
            "dominant_convention": dominant_convention,
            "consistency_score": conventions[dominant_convention] / len(columns) if columns else 0
        }
    
    def _is_key_column(self, column: str) -> bool:
        """Check if column is likely a key column"""
        key_indicators = ["id", "key", "code", "number", "reference", "ref"]
        return any(indicator in column for indicator in key_indicators)
    
    def _is_measure_column(self, column: str) -> bool:
        """Check if column is likely a measure column"""
        measure_indicators = ["amount", "value", "total", "sum", "count", "quantity", "sales", "revenue", "cost", "price"]
        return any(indicator in column for indicator in measure_indicators)
    
    def _is_attribute_column(self, column: str) -> bool:
        """Check if column is likely an attribute column"""
        attribute_indicators = ["name", "description", "desc", "type", "category", "status", "flag"]
        return any(indicator in column for indicator in attribute_indicators)
    
    def _calculate_pattern_scores(self, pattern_analysis: Dict[str, Any], 
                                structural_analysis: Dict[str, Any],
                                temporal_analysis: Dict[str, Any],
                                process_analysis: Dict[str, Any]) -> Dict[str, float]:
        """Calculate classification scores based on pattern analysis"""
        scores = {}
        
        for data_type in CPGDomainKnowledge.CPG_DATA_TYPES:
            score = 0.0
            
            # Pattern-based scoring
            if data_type in ["syndicated"] and "syndicated_patterns" in pattern_analysis.get("pattern_strength", {}):
                score += pattern_analysis["pattern_strength"]["syndicated_patterns"] * 0.4
            
            if data_type in ["pos", "pos_fact"] and "pos_patterns" in pattern_analysis.get("pattern_strength", {}):
                score += pattern_analysis["pattern_strength"]["pos_patterns"] * 0.4
            
            if data_type == "product_attribute" and "product_patterns" in pattern_analysis.get("pattern_strength", {}):
                score += pattern_analysis["pattern_strength"]["product_patterns"] * 0.4
            
            # Structural scoring
            if data_type.endswith("_fact") and structural_analysis.get("table_type") == "fact_table":
                score += 0.3
            
            if data_type.endswith("_mapping") and structural_analysis.get("table_type") == "bridge_table":
                score += 0.3
            
            if "dimension" in data_type and structural_analysis.get("table_type") == "dimension_table":
                score += 0.3
            
            # Temporal scoring
            if temporal_analysis.get("has_temporal_data"):
                if data_type in ["syndicated", "pos"] and temporal_analysis.get("granularity") in ["daily", "weekly"]:
                    score += 0.2
            
            # Process scoring
            process_matches = {
                "syndicated": ["market_research", "sales_analytics"],
                "pos": ["sales_analytics", "customer_analytics"],
                "product_attribute": ["product_management"],
                "margin_data": ["pricing_optimization", "financial_planning"]
            }
            
            if data_type in process_matches:
                for process in process_matches[data_type]:
                    if process in process_analysis.get("identified_processes", []):
                        score += 0.15
            
            scores[data_type] = min(score, 1.0)
        
        return scores
    
    def _generate_pattern_reasoning(self, pattern_analysis: Dict[str, Any],
                                  structural_analysis: Dict[str, Any],
                                  temporal_analysis: Dict[str, Any],
                                  classification_scores: Dict[str, float]) -> str:
        """Generate reasoning based on pattern analysis"""
        reasoning_parts = []
        
        # Pattern insights
        detected_patterns = pattern_analysis.get("detected_patterns", [])
        if detected_patterns:
            reasoning_parts.append(f"Detected patterns: {', '.join(detected_patterns[:3])}")
        
        # Structural insights
        table_type = structural_analysis.get("table_type", "unknown")
        if table_type != "unknown":
            reasoning_parts.append(f"Structure: {table_type}")
        
        # Temporal insights
        if temporal_analysis.get("has_temporal_data"):
            granularity = temporal_analysis.get("granularity", "unknown")
            reasoning_parts.append(f"Temporal: {granularity} granularity")
        
        # Top classifications
        top_scores = sorted(classification_scores.items(), key=lambda x: x[1], reverse=True)[:3]
        reasoning_parts.append(f"Pattern scores: {', '.join([f'{t}({s:.2f})' for t, s in top_scores if s > 0])}")
        
        return "; ".join(reasoning_parts)
    
    def get_capabilities(self) -> List[str]:
        return self.capabilities

class FeedbackCollectorAgent(BaseAgent):
    """Agent for collecting and processing user feedback to improve classification"""
    
    def __init__(self, agent_id: str, config: Dict[str, Any]):
        super().__init__(agent_id, AgentType.FEEDBACK_COLLECTOR, config)
        self.capabilities = [
            "feedback_collection",
            "classification_correction",
            "learning_data_generation",
            "performance_tracking",
            "quality_assessment"
        ]
        
        # Feedback storage
        self.feedback_history = []
        self.correction_patterns = defaultdict(list)
        self.user_preferences = {}
        
        # Learning metrics
        self.learning_stats = {
            "total_feedback": 0,
            "corrections": 0,
            "confirmations": 0,
            "improvement_suggestions": 0,
            "user_satisfaction": 0.0
        }
    
    async def analyze(self, file_context: FileContext) -> AgentDecision:
        """This agent doesn't classify but can provide feedback-based insights"""
        start_time = time.time()
        
        try:
            # Analyze historical feedback for similar files
            feedback_insights = self._analyze_historical_feedback(file_context)
            
            # Generate feedback-based recommendations
            recommendations = self._generate_feedback_recommendations(file_context, feedback_insights)
            
            decision = AgentDecision(
                agent_id=self.agent_id,
                agent_type=self.agent_type,
                classification="feedback_analysis",  # Special classification type
                confidence=feedback_insights.get("confidence_adjustment", 0.0),
                reasoning=self._generate_feedback_reasoning(feedback_insights, recommendations),
                evidence={
                    "feedback_insights": feedback_insights,
                    "recommendations": recommendations,
                    "historical_patterns": self._get_correction_patterns()
                },
                processing_time=time.time() - start_time
            )
            
            return decision
            
        except Exception as e:
            self.logger.error(f"Feedback analysis failed: {e}")
            return AgentDecision(
                agent_id=self.agent_id,
                agent_type=self.agent_type,
                classification="feedback_error",
                confidence=0.0,
                reasoning=f"Feedback analysis error: {str(e)}",
                processing_time=time.time() - start_time
            )
    
    async def collect_user_feedback(self, classification_result: Dict[str, Any], 
                                  user_input: Dict[str, Any]) -> Dict[str, Any]:
        """Collect and process user feedback on classification result"""
        self.logger.info("Inside feedback agent")
        try:
            feedback_entry = {
                "timestamp": datetime.now().isoformat(),
                "file_path": classification_result.get("file_path"),
                "predicted_classification": classification_result.get("classification"),
                "predicted_confidence": classification_result.get("confidence"),
                "user_feedback": user_input,
                "feedback_type": self._determine_feedback_type(user_input),
                "satisfaction_score": user_input.get("satisfaction", 3.0)  # 1-5 scale
            }
            
            # Process different types of feedback
            if feedback_entry["feedback_type"] == "correction":
                await self._process_correction_feedback(feedback_entry, user_input)
            elif feedback_entry["feedback_type"] == "confirmation":
                await self._process_confirmation_feedback(feedback_entry)
            elif feedback_entry["feedback_type"] == "suggestion":
                await self._process_suggestion_feedback(feedback_entry, user_input)
            
            # Store feedback
            self.feedback_history.append(feedback_entry)
            self._update_learning_stats(feedback_entry)
            
            # Generate learning recommendations
            learning_recommendations = self._generate_learning_recommendations(feedback_entry)
            
            return {
                "success": True,
                "feedback_id": len(self.feedback_history),
                "feedback_type": feedback_entry["feedback_type"],
                "learning_recommendations": learning_recommendations,
                "system_improvements": self._suggest_system_improvements(feedback_entry)
            }
            
        except Exception as e:
            self.logger.error(f"Feedback collection failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _determine_feedback_type(self, user_input: Dict[str, Any]) -> str:
        """Determine the type of feedback provided"""
        if user_input.get("correct_classification"):
            return "correction"
        elif user_input.get("confirm_classification", False):
            return "confirmation"
        elif user_input.get("suggestions") or user_input.get("improvements"):
            return "suggestion"
        else:
            return "general"
    
    async def _process_correction_feedback(self, feedback_entry: Dict[str, Any], user_input: Dict[str, Any]):
        """Process correction feedback"""
        predicted = feedback_entry["predicted_classification"]
        correct = user_input["correct_classification"]
        
        # Store correction pattern
        correction_key = f"{predicted}->{correct}"
        self.correction_patterns[correction_key].append({
            "timestamp": feedback_entry["timestamp"],
            "file_path": feedback_entry["file_path"],
            "confidence": feedback_entry["predicted_confidence"],
            "user_reasoning": user_input.get("reasoning", "")
        })
        
        # Update learning statistics
        self.learning_stats["corrections"] += 1
        
        self.logger.info(f"Correction feedback: {predicted} -> {correct}")
    
    async def _process_confirmation_feedback(self, feedback_entry: Dict[str, Any]):
        """Process confirmation feedback"""
        self.learning_stats["confirmations"] += 1
        
        # Increase confidence in this classification pattern
        classification = feedback_entry["predicted_classification"]
        if classification not in self.user_preferences:
            self.user_preferences[classification] = {"confirmations": 0, "avg_confidence": 0.0}
        
        prefs = self.user_preferences[classification]
        prefs["confirmations"] += 1
        
        # Update average confidence
        total_conf = prefs["avg_confidence"] * (prefs["confirmations"] - 1) + feedback_entry["predicted_confidence"]
        prefs["avg_confidence"] = total_conf / prefs["confirmations"]
    
    async def _process_suggestion_feedback(self, feedback_entry: Dict[str, Any], user_input: Dict[str, Any]):
        """Process suggestion feedback"""
        self.learning_stats["improvement_suggestions"] += 1
        
        suggestions = user_input.get("suggestions", [])
        improvements = user_input.get("improvements", [])
        
        # Store suggestions for system improvement
        feedback_entry["system_suggestions"] = suggestions + improvements
        
        self.logger.info(f"Suggestion feedback: {len(suggestions + improvements)} items")
    
    def _analyze_historical_feedback(self, file_context: FileContext) -> Dict[str, Any]:
        """Analyze historical feedback for similar files"""
        insights = {
            "similar_files_feedback": [],
            "common_corrections": [],
            "confidence_adjustment": 0.0,
            "risk_factors": []
        }
        
        # Find similar files based on characteristics
        file_characteristics = self._extract_file_characteristics(file_context)
        
        similar_feedback = []
        for feedback in self.feedback_history:
            if self._is_similar_file(feedback, file_characteristics):
                similar_feedback.append(feedback)
        
        if similar_feedback:
            insights["similar_files_feedback"] = similar_feedback[-5:]  # Last 5 similar
            
            # Analyze correction patterns
            corrections = [f for f in similar_feedback if f["feedback_type"] == "correction"]
            if corrections:
                # Calculate confidence adjustment based on historical corrections
                avg_original_confidence = sum(f["predicted_confidence"] for f in corrections) / len(corrections)
                correction_rate = len(corrections) / len(similar_feedback)
                
                # Adjust confidence downward if high correction rate
                insights["confidence_adjustment"] = -correction_rate * 0.3
                insights["risk_factors"].append(f"High correction rate ({correction_rate:.2f}) for similar files")
        
        return insights
    
    def _extract_file_characteristics(self, file_context: FileContext) -> Dict[str, Any]:
        """Extract characteristics for similarity comparison"""
        schema = file_context.extracted_content.get('schema', {})
        
        return {
            "file_type": file_context.file_type,
            "column_count": schema.get('column_count', 0),
            "row_count_category": self._categorize_row_count(schema.get('row_count', 0)),
            "has_date_columns": any('date' in col.lower() for col in schema.get('columns', [])),
            "has_id_columns": any('id' in col.lower() for col in schema.get('columns', [])),
            "dominant_data_type": self._get_dominant_data_type(schema.get('data_types', []))
        }
    
    def _categorize_row_count(self, row_count: int) -> str:
        """Categorize row count for similarity comparison"""
        if row_count < 1000:
            return "small"
        elif row_count < 50000:
            return "medium"
        elif row_count < 1000000:
            return "large"
        else:
            return "very_large"
    
    def _get_dominant_data_type(self, data_types: List[str]) -> str:
        """Get dominant data type in schema"""
        if not data_types:
            return "unknown"
        
        type_counts = Counter()
        for dtype in data_types:
            if 'int' in str(dtype).lower() or 'float' in str(dtype).lower():
                type_counts['numeric'] += 1
            elif 'str' in str(dtype).lower() or 'object' in str(dtype).lower():
                type_counts['text'] += 1
            else:
                type_counts['other'] += 1
        
        return type_counts.most_common(1)[0][0] if type_counts else "unknown"
    
    def _is_similar_file(self, feedback: Dict[str, Any], characteristics: Dict[str, Any]) -> bool:
        """Check if feedback is from a similar file"""
        # Simple similarity check - can be enhanced with more sophisticated matching
        similarity_score = 0
        
        # Check file type
        if feedback.get("file_type") == characteristics.get("file_type"):
            similarity_score += 1
        
        # Check column count (within range)
        feedback_cols = feedback.get("column_count", 0)
        char_cols = characteristics.get("column_count", 0)
        if abs(feedback_cols - char_cols) <= 5:  # Within 5 columns
            similarity_score += 1
        
        # Check row count category
        if feedback.get("row_count_category") == characteristics.get("row_count_category"):
            similarity_score += 1
        
        return similarity_score >= 2  # At least 2 similarities
    
    def _generate_feedback_recommendations(self, file_context: FileContext, 
                                         feedback_insights: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on feedback analysis"""
        recommendations = []
        
        # Risk-based recommendations
        risk_factors = feedback_insights.get("risk_factors", [])
        if risk_factors:
            recommendations.append("Manual review recommended due to historical correction patterns")
        
        # Confidence adjustment recommendations
        confidence_adj = feedback_insights.get("confidence_adjustment", 0.0)
        if confidence_adj < -0.2:
            recommendations.append("Consider additional validation - similar files often require correction")
        
        # Pattern-based recommendations
        similar_feedback = feedback_insights.get("similar_files_feedback", [])
        if similar_feedback:
            # Find most common corrections
            corrections = [f for f in similar_feedback if f["feedback_type"] == "correction"]
            if corrections:
                # Get most common correction pattern
                correction_patterns = [f"{f['predicted_classification']}->{f['user_feedback'].get('correct_classification')}" 
                                     for f in corrections if f.get('user_feedback', {}).get('correct_classification')]
                if correction_patterns:
                    most_common = Counter(correction_patterns).most_common(1)[0]
                    recommendations.append(f"Consider classification as {most_common[0].split('->')[1]} - common correction pattern")
        
        return recommendations
    
    def _generate_learning_recommendations(self, feedback_entry: Dict[str, Any]) -> List[str]:
        """Generate learning recommendations based on feedback"""
        recommendations = []
        
        feedback_type = feedback_entry["feedback_type"]
        
        if feedback_type == "correction":
            recommendations.append("Add this example to training data")
            recommendations.append("Review similar classification patterns")
            recommendations.append("Update agent weights based on correction")
        
        elif feedback_type == "confirmation":
            recommendations.append("Reinforce current classification patterns")
            recommendations.append("Increase confidence in similar cases")
        
        elif feedback_type == "suggestion":
            suggestions = feedback_entry.get("system_suggestions", [])
            for suggestion in suggestions:
                recommendations.append(f"System improvement: {suggestion}")
        
        return recommendations
    
    def _suggest_system_improvements(self, feedback_entry: Dict[str, Any]) -> List[str]:
        """Suggest system improvements based on feedback"""
        improvements = []
        
        # Analyze correction patterns
        if feedback_entry["feedback_type"] == "correction":
            predicted = feedback_entry["predicted_classification"]
            
            # Check if this is a recurring correction
            correction_key = f"{predicted}->*"
            similar_corrections = sum(1 for pattern in self.correction_patterns 
                                    if pattern.startswith(predicted))
            
            if similar_corrections >= 3:
                improvements.append(f"Review {predicted} classification logic - multiple corrections observed")
        
        # Analyze confidence patterns
        if feedback_entry["predicted_confidence"] > 0.8 and feedback_entry["feedback_type"] == "correction":
            improvements.append("High-confidence misclassification - review confidence calculation")
        
        # Analyze satisfaction scores
        satisfaction = feedback_entry.get("satisfaction_score", 3.0)
        if satisfaction < 2.0:
            improvements.append("Low user satisfaction - review overall classification quality")
        
        return improvements
    
    def _update_learning_stats(self, feedback_entry: Dict[str, Any]):
        """Update learning statistics"""
        self.learning_stats["total_feedback"] += 1
        
        # Update satisfaction average
        satisfaction = feedback_entry.get("satisfaction_score", 3.0)
        total_satisfaction = (self.learning_stats["user_satisfaction"] * 
                            (self.learning_stats["total_feedback"] - 1) + satisfaction)
        self.learning_stats["user_satisfaction"] = total_satisfaction / self.learning_stats["total_feedback"]
    
    def _get_correction_patterns(self) -> Dict[str, Any]:
        """Get summary of correction patterns"""
        patterns = {}
        
        for pattern, corrections in self.correction_patterns.items():
            patterns[pattern] = {
                "count": len(corrections),
                "avg_confidence": sum(c.get("confidence", 0.0) for c in corrections) / len(corrections),
                "recent_occurrences": len([c for c in corrections 
                                         if (datetime.now() - datetime.fromisoformat(c["timestamp"])).days <= 30])
            }
        
        return patterns
    
    def _generate_feedback_reasoning(self, feedback_insights: Dict[str, Any], 
                                   recommendations: List[str]) -> str:
        """Generate reasoning based on feedback analysis"""
        reasoning_parts = []
        
        similar_count = len(feedback_insights.get("similar_files_feedback", []))
        if similar_count > 0:
            reasoning_parts.append(f"Found {similar_count} similar file feedback entries")
        
        confidence_adj = feedback_insights.get("confidence_adjustment", 0.0)
        if confidence_adj != 0.0:
            reasoning_parts.append(f"Confidence adjustment: {confidence_adj:+.2f}")
        
        risk_factors = feedback_insights.get("risk_factors", [])
        if risk_factors:
            reasoning_parts.append(f"Risk factors: {len(risk_factors)} identified")
        
        if recommendations:
            reasoning_parts.append(f"Generated {len(recommendations)} recommendations")
        
        return "; ".join(reasoning_parts) if reasoning_parts else "No significant feedback patterns found"
    
    def get_feedback_statistics(self) -> Dict[str, Any]:
        """Get comprehensive feedback statistics"""
        stats = self.learning_stats.copy()
        
        # Add correction analysis
        total_corrections = sum(len(corrections) for corrections in self.correction_patterns.values())
        stats["correction_patterns"] = len(self.correction_patterns)
        stats["total_corrections"] = total_corrections
        
        # Add recent feedback analysis
        recent_feedback = [f for f in self.feedback_history 
                          if (datetime.now() - datetime.fromisoformat(f["timestamp"])).days <= 7]
        stats["recent_feedback_count"] = len(recent_feedback)
        
        # Add satisfaction trends
        if len(self.feedback_history) >= 10:
            recent_satisfaction = [f.get("satisfaction_score", 3.0) for f in recent_feedback]
            if recent_satisfaction:
                stats["recent_satisfaction"] = sum(recent_satisfaction) / len(recent_satisfaction)
        
        return stats
    
    def get_capabilities(self) -> List[str]:
        return self.capabilities