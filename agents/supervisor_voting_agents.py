"""
Supervisor Agent and Voting System for CPG File Classification with Vector DB Integration
Orchestrates the entire classification process and manages agent voting with semantic search
"""

import asyncio
import time
import statistics
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime
import logging
import sys
from pathlib import Path

project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from agents.base_agent import (
    BaseAgent, AgentDecision, FileContext, AgentType, 
    ConfidenceLevel, AgentMessage, CPGDomainKnowledge
)

@dataclass
class VotingResult:
    """Result of the voting process with vector DB insights"""
    final_classification: str
    final_confidence: float
    consensus_level: float
    participating_agents: List[str]
    agent_decisions: List[AgentDecision]
    voting_summary: Dict[str, Any]
    processing_time: float
    reasoning: str
    vector_insights: Dict[str, Any] = field(default_factory=dict)

@dataclass
class AgentWeight:
    """Weight configuration for agent voting"""
    agent_type: AgentType
    base_weight: float
    performance_multiplier: float = 1.0
    domain_expertise_bonus: float = 0.0
    vector_boost_multiplier: float = 1.0

class VectorEnhancedVotingCoordinatorAgent(BaseAgent):
    """Coordinates voting among classification agents with vector DB enhancement"""
    
    def __init__(self, agent_id: str, config: Dict[str, Any]):
        super().__init__(agent_id, AgentType.VOTING_COORDINATOR, config)
        self.capabilities = [
            "decision_aggregation",
            "consensus_building",
            "confidence_weighting",
            "conflict_resolution",
            "vector_evidence_integration"
        ]
        self.voting_strategies = {
            'weighted_average': self._weighted_average_voting,
            'confidence_weighted': self._confidence_weighted_voting,
            'expert_priority': self._expert_priority_voting,
            'ensemble_learning': self._ensemble_learning_voting,
            'vector_enhanced': self._vector_enhanced_voting
        }
        self.agent_weights = self._initialize_agent_weights()
        self.vector_db = config.get('vector_db')
        self.vector_enabled = config.get('enable_vector_search', True) and self.vector_db is not None
        self.auto_store_results = config.get('auto_store_results', True)
    
    def _initialize_agent_weights(self) -> Dict[AgentType, AgentWeight]:
        """Initialize voting weights for different agent types with vector considerations"""
        return {
            AgentType.SCHEMA_ANALYZER: AgentWeight(
                AgentType.SCHEMA_ANALYZER, 0.25, 
                domain_expertise_bonus=0.1, 
                vector_boost_multiplier=1.1
            ),
            AgentType.CONTENT_ANALYZER: AgentWeight(
                AgentType.CONTENT_ANALYZER, 0.20, 
                domain_expertise_bonus=0.05,
                vector_boost_multiplier=1.2  # Higher for semantic content matching
            ),
            AgentType.PATTERN_RECOGNIZER: AgentWeight(
                AgentType.PATTERN_RECOGNIZER, 0.20,
                vector_boost_multiplier=1.0
            ),
            AgentType.DOMAIN_EXPERT: AgentWeight(
                AgentType.DOMAIN_EXPERT, 0.35, 
                domain_expertise_bonus=0.15,
                vector_boost_multiplier=1.3  # Highest for domain knowledge matching
            ),
            AgentType.ML_CLASSIFIER: AgentWeight(
                AgentType.ML_CLASSIFIER, 0.30, 
                performance_multiplier=1.2,
                vector_boost_multiplier=1.1
            )
        }
    
    async def analyze(self, file_context: FileContext) -> AgentDecision:
        """This method is not used for voting coordinator"""
        raise NotImplementedError("Voting coordinator uses coordinate_voting method")
    
    async def coordinate_voting(self, agent_decisions: List[AgentDecision], 
                              file_context: FileContext) -> VotingResult:
        """Coordinate the voting process among agents with vector enhancement"""
        start_time = time.time()
        
        try:
            # Validate and filter decisions
            valid_decisions = self._validate_decisions(agent_decisions)
            
            if not valid_decisions:
                raise ValueError("No valid agent decisions provided")
            
            # Aggregate vector insights from all agents
            vector_insights = self._aggregate_vector_insights(valid_decisions)
            
            # Apply multiple voting strategies including vector-enhanced
            voting_results = {}
            for strategy_name, strategy_func in self.voting_strategies.items():
                try:
                    result = strategy_func(valid_decisions, file_context, vector_insights)
                    voting_results[strategy_name] = result
                except Exception as e:
                    self.logger.warning(f"Voting strategy {strategy_name} failed: {e}")
            
            # Combine results from different strategies
            final_result = self._combine_voting_strategies(voting_results, valid_decisions, vector_insights)
            
            # Calculate consensus metrics
            consensus_metrics = self._calculate_consensus_metrics(valid_decisions)
            
            # Build final voting result
            voting_result = VotingResult(
                final_classification=final_result['classification'],
                final_confidence=final_result['confidence'],
                consensus_level=consensus_metrics['consensus_level'],
                participating_agents=[d.agent_id for d in valid_decisions],
                agent_decisions=valid_decisions,
                voting_summary={
                    'strategy_results': voting_results,
                    'consensus_metrics': consensus_metrics,
                    'conflict_analysis': self._analyze_conflicts(valid_decisions),
                    'vector_contribution': self._calculate_vector_contribution(vector_insights)
                },
                processing_time=time.time() - start_time,
                reasoning=self._generate_voting_reasoning(valid_decisions, final_result, consensus_metrics, vector_insights),
                vector_insights=vector_insights
            )
            
            # Store voting patterns for future learning
            if self.auto_store_results and voting_result.consensus_level > 0.6:
                await self._store_voting_pattern(voting_result, file_context)
            
            return voting_result
            
        except Exception as e:
            self.logger.error(f"Voting coordination failed: {e}")
            # Return fallback result
            return VotingResult(
                final_classification="unknown",
                final_confidence=0.0,
                consensus_level=0.0,
                participating_agents=[],
                agent_decisions=agent_decisions,
                voting_summary={"error": str(e)},
                processing_time=time.time() - start_time,
                reasoning=f"Voting coordination error: {str(e)}"
            )
    
    def _aggregate_vector_insights(self, decisions: List[AgentDecision]) -> Dict[str, Any]:
        """Aggregate vector insights from all agent decisions"""
        aggregated_insights = {
            'total_vector_boost': 0.0,
            'agent_vector_results': {},
            'combined_similarity_scores': [],
            'classification_consensus': {},
            'best_vector_matches': [],
            'collection_distribution': {}
        }
        
        vector_participating_agents = 0
        
        for decision in decisions:
            evidence = decision.evidence or {}
            vector_results = evidence.get('vector_results', {})
            
            if vector_results and vector_results.get('confidence_boost', 0) > 0:
                vector_participating_agents += 1
                agent_boost = vector_results.get('confidence_boost', 0)
                aggregated_insights['total_vector_boost'] += agent_boost
                
                # Store agent-specific vector results
                aggregated_insights['agent_vector_results'][decision.agent_id] = {
                    'boost': agent_boost,
                    'best_matches': vector_results.get('best_matches', [])[:3],
                    'evidence': vector_results.get('evidence', {})
                }
                
                # Aggregate similarity scores
                best_matches = vector_results.get('best_matches', [])
                for match in best_matches[:3]:
                    similarity = match.get('similarity_score', 0)
                    if similarity > 0:
                        aggregated_insights['combined_similarity_scores'].append(similarity)
                
                # Aggregate classification hints
                classification_hints = vector_results.get('evidence', {}).get('classification_hints', [])
                for hint in classification_hints:
                    classification = hint.get('classification', '')
                    support_count = hint.get('support_count', 0)
                    if classification in aggregated_insights['classification_consensus']:
                        aggregated_insights['classification_consensus'][classification] += support_count
                    else:
                        aggregated_insights['classification_consensus'][classification] = support_count
                
                # Aggregate collection distribution
                collection_results = vector_results.get('collection_results', {})
                for collection, results in collection_results.items():
                    count = results.get('count', 0)
                    if count > 0:
                        if collection in aggregated_insights['collection_distribution']:
                            aggregated_insights['collection_distribution'][collection] += count
                        else:
                            aggregated_insights['collection_distribution'][collection] = count
        
        # Calculate aggregated metrics
        if vector_participating_agents > 0:
            aggregated_insights['avg_vector_boost'] = aggregated_insights['total_vector_boost'] / vector_participating_agents
            aggregated_insights['vector_participation_rate'] = vector_participating_agents / len(decisions)
        else:
            aggregated_insights['avg_vector_boost'] = 0.0
            aggregated_insights['vector_participation_rate'] = 0.0
        
        # Calculate average similarity
        if aggregated_insights['combined_similarity_scores']:
            aggregated_insights['avg_similarity'] = statistics.mean(aggregated_insights['combined_similarity_scores'])
            aggregated_insights['max_similarity'] = max(aggregated_insights['combined_similarity_scores'])
        else:
            aggregated_insights['avg_similarity'] = 0.0
            aggregated_insights['max_similarity'] = 0.0
        
        return aggregated_insights
    
    def _vector_enhanced_voting(self, decisions: List[AgentDecision], 
                               file_context: FileContext,
                               vector_insights: Dict[str, Any]) -> Dict[str, Any]:
        """Vector-enhanced voting strategy that heavily weights vector similarity"""
        classification_scores = {}
        total_weight = 0.0
        
        for decision in decisions:
            # Get base agent weight
            base_weight = self._get_agent_weight(decision.agent_type, decision)
            
            # Apply vector boost multiplier
            agent_weight_config = self.agent_weights.get(decision.agent_type)
            vector_multiplier = agent_weight_config.vector_boost_multiplier if agent_weight_config else 1.0
            
            # Get vector boost for this specific agent
            agent_vector_results = vector_insights.get('agent_vector_results', {})
            agent_boost = agent_vector_results.get(decision.agent_id, {}).get('boost', 0)
            
            # Calculate enhanced weight
            enhanced_weight = base_weight * vector_multiplier * (1 + agent_boost)
            
            if decision.classification not in classification_scores:
                classification_scores[decision.classification] = 0.0
            
            classification_scores[decision.classification] += decision.confidence * enhanced_weight
            total_weight += enhanced_weight
        
        # Apply additional boost based on vector consensus
        vector_consensus = vector_insights.get('classification_consensus', {})
        for classification, support_count in vector_consensus.items():
            if classification in classification_scores:
                consensus_boost = min(support_count * 0.02, 0.1)  # Max 0.1 boost
                classification_scores[classification] += consensus_boost * total_weight
        
        # Normalize scores
        if total_weight > 0:
            for classification in classification_scores:
                classification_scores[classification] /= total_weight
        
        best_classification = max(classification_scores.items(), key=lambda x: x[1])
        
        return {
            'classification': best_classification[0],
            'confidence': best_classification[1],
            'all_scores': classification_scores,
            'strategy': 'vector_enhanced',
            'vector_contribution': vector_insights.get('avg_vector_boost', 0)
        }
    
    def _weighted_average_voting(self, decisions: List[AgentDecision], 
                               file_context: FileContext,
                               vector_insights: Dict[str, Any] = None) -> Dict[str, Any]:
        """Weighted average voting strategy with optional vector enhancement"""
        classification_scores = {}
        total_weight = 0.0
        
        for decision in decisions:
            weight = self._get_agent_weight(decision.agent_type, decision)
            
            if decision.classification not in classification_scores:
                classification_scores[decision.classification] = 0.0
            
            classification_scores[decision.classification] += decision.confidence * weight
            total_weight += weight
        
        # Normalize scores
        if total_weight > 0:
            for classification in classification_scores:
                classification_scores[classification] /= total_weight
        
        best_classification = max(classification_scores.items(), key=lambda x: x[1])
        
        return {
            'classification': best_classification[0],
            'confidence': best_classification[1],
            'all_scores': classification_scores,
            'strategy': 'weighted_average'
        }
    
    def _confidence_weighted_voting(self, decisions: List[AgentDecision], 
                                  file_context: FileContext,
                                  vector_insights: Dict[str, Any] = None) -> Dict[str, Any]:
        """Confidence-weighted voting strategy"""
        classification_scores = {}
        total_confidence = 0.0
        
        for decision in decisions:
            confidence_weight = decision.confidence ** 2  # Square confidence for stronger weighting
            
            if decision.classification not in classification_scores:
                classification_scores[decision.classification] = 0.0
            
            classification_scores[decision.classification] += confidence_weight
            total_confidence += confidence_weight
        
        # Normalize scores
        if total_confidence > 0:
            for classification in classification_scores:
                classification_scores[classification] /= total_confidence
        
        best_classification = max(classification_scores.items(), key=lambda x: x[1])
        
        return {
            'classification': best_classification[0],
            'confidence': best_classification[1],
            'all_scores': classification_scores,
            'strategy': 'confidence_weighted'
        }
    
    def _expert_priority_voting(self, decisions: List[AgentDecision], 
                              file_context: FileContext,
                              vector_insights: Dict[str, Any] = None) -> Dict[str, Any]:
        """Expert priority voting strategy - prioritize domain expert and high-confidence decisions"""
        # Sort decisions by expertise and confidence
        sorted_decisions = sorted(decisions, 
                                key=lambda d: (
                                    1.0 if d.agent_type == AgentType.DOMAIN_EXPERT else 0.5,
                                    d.confidence
                                ), 
                                reverse=True)
        
        # Use top decision if it meets threshold
        top_decision = sorted_decisions[0]
        
        # Check if top decision has strong vector support
        vector_boost = 0.0
        if vector_insights:
            agent_vector_results = vector_insights.get('agent_vector_results', {})
            top_agent_vector = agent_vector_results.get(top_decision.agent_id, {})
            vector_boost = top_agent_vector.get('boost', 0)
        
        effective_confidence = top_decision.confidence + vector_boost
        
        if (effective_confidence >= 0.7 or 
            top_decision.agent_type == AgentType.DOMAIN_EXPERT or
            vector_boost > 0.1):
            return {
                'classification': top_decision.classification,
                'confidence': min(effective_confidence, 1.0),
                'primary_agent': top_decision.agent_id,
                'strategy': 'expert_priority',
                'vector_boost': vector_boost
            }
        else:
            # Fall back to weighted average
            return self._weighted_average_voting(decisions, file_context, vector_insights)
    
    def _ensemble_learning_voting(self, decisions: List[AgentDecision], 
                                file_context: FileContext,
                                vector_insights: Dict[str, Any] = None) -> Dict[str, Any]:
        """Ensemble learning voting strategy with vector enhancement"""
        # Create feature vector from all agent decisions
        features = self._create_ensemble_features(decisions, file_context, vector_insights)
        
        # Apply ensemble logic with vector insights
        classification_scores = {}
        
        # Weight by agent performance, agreement, and vector similarity
        for decision in decisions:
            performance_score = self._get_agent_performance_score(decision.agent_type)
            agreement_score = self._calculate_agreement_score(decision, decisions)
            
            # Get vector boost for this agent
            vector_boost = 0.0
            if vector_insights:
                agent_vector_results = vector_insights.get('agent_vector_results', {})
                agent_vector = agent_vector_results.get(decision.agent_id, {})
                vector_boost = agent_vector.get('boost', 0)
            
            ensemble_weight = (performance_score * 0.4 + 
                             agreement_score * 0.3 + 
                             vector_boost * 0.3) * decision.confidence
            
            if decision.classification not in classification_scores:
                classification_scores[decision.classification] = 0.0
            
            classification_scores[decision.classification] += ensemble_weight
        
        # Normalize
        total_score = sum(classification_scores.values())
        if total_score > 0:
            for classification in classification_scores:
                classification_scores[classification] /= total_score
        
        best_classification = max(classification_scores.items(), key=lambda x: x[1])
        
        return {
            'classification': best_classification[0],
            'confidence': best_classification[1],
            'all_scores': classification_scores,
            'ensemble_features': features,
            'strategy': 'ensemble_learning'
        }
    
    def _create_ensemble_features(self, decisions: List[AgentDecision], 
                                file_context: FileContext,
                                vector_insights: Dict[str, Any] = None) -> Dict[str, Any]:
        """Create feature vector for ensemble learning with vector insights"""
        base_features = {
            'avg_confidence': statistics.mean([d.confidence for d in decisions]),
            'confidence_std': statistics.stdev([d.confidence for d in decisions]) if len(decisions) > 1 else 0,
            'agent_count': len(decisions),
            'classification_diversity': len(set(d.classification for d in decisions)),
            'max_confidence': max(d.confidence for d in decisions),
            'min_confidence': min(d.confidence for d in decisions),
            'has_domain_expert': any(d.agent_type == AgentType.DOMAIN_EXPERT for d in decisions),
            'has_ml_classifier': any(d.agent_type == AgentType.ML_CLASSIFIER for d in decisions)
        }
        
        # Add vector features
        if vector_insights:
            vector_features = {
                'avg_vector_boost': vector_insights.get('avg_vector_boost', 0),
                'max_similarity': vector_insights.get('max_similarity', 0),
                'vector_participation_rate': vector_insights.get('vector_participation_rate', 0),
                'vector_consensus_strength': len(vector_insights.get('classification_consensus', {})),
                'collection_diversity': len(vector_insights.get('collection_distribution', {}))
            }
            base_features.update(vector_features)
        
        return base_features
    
    def _calculate_vector_contribution(self, vector_insights: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate the contribution of vector search to the voting process"""
        return {
            'total_boost': vector_insights.get('total_vector_boost', 0),
            'avg_boost': vector_insights.get('avg_vector_boost', 0),
            'participation_rate': vector_insights.get('vector_participation_rate', 0),
            'max_similarity': vector_insights.get('max_similarity', 0),
            'consensus_classifications': len(vector_insights.get('classification_consensus', {})),
            'active_collections': len(vector_insights.get('collection_distribution', {}))
        }
    
    async def _store_voting_pattern(self, voting_result: VotingResult, file_context: FileContext):
        """Store successful voting patterns in vector DB for future learning"""
        if not self.vector_enabled or not self.auto_store_results:
            return
        
        try:
            from core.knowledgebase.document_models import BaseDocument
            
            # Build content for voting pattern storage
            content_parts = [
                f"Voting pattern: {voting_result.final_classification}",
                f"Consensus: {voting_result.consensus_level:.3f}",
                f"Confidence: {voting_result.final_confidence:.3f}",
                f"Agents: {len(voting_result.participating_agents)}",
                f"File: {file_context.file_name}",
                f"Vector boost: {voting_result.vector_insights.get('avg_vector_boost', 0):.3f}"
            ]
            
            # Add agent decision summary
            for decision in voting_result.agent_decisions:
                content_parts.append(f"{decision.agent_type.value}: {decision.classification} ({decision.confidence:.3f})")
            
            # Create document
            doc = BaseDocument(
                content=" | ".join(content_parts),
                document_type="voting_pattern",
                metadata={
                    'classification': voting_result.final_classification,
                    'consensus_level': voting_result.consensus_level,
                    'confidence': voting_result.final_confidence,
                    'agent_count': len(voting_result.participating_agents),
                    'vector_boost': voting_result.vector_insights.get('avg_vector_boost', 0),
                    'pattern_type': 'consensus_voting',
                    'timestamp': time.time()
                }
            )
            
            # Store in domain_knowledge collection
            success = self.vector_db.add_documents('cpg_domain_knowledge', [doc])
            if success:
                self.logger.debug("Stored voting pattern in vector DB")
        
        except Exception as e:
            self.logger.warning(f"Failed to store voting pattern: {e}")
    
    def _validate_decisions(self, decisions: List[AgentDecision]) -> List[AgentDecision]:
        """Validate and filter agent decisions"""
        valid_decisions = []
        
        for decision in decisions:
            # Basic validation
            if (decision.classification and 
                decision.classification in CPGDomainKnowledge.CPG_DATA_TYPES and
                0.0 <= decision.confidence <= 1.0):
                valid_decisions.append(decision)
            else:
                self.logger.warning(f"Invalid decision from {decision.agent_id}: {decision.classification}")
        
        return valid_decisions
    
    def _get_agent_weight(self, agent_type: AgentType, decision: AgentDecision) -> float:
        """Get voting weight for agent type"""
        if agent_type not in self.agent_weights:
            return 1.0
        
        weight_config = self.agent_weights[agent_type]
        base_weight = weight_config.base_weight
        
        # Apply performance multiplier
        performance_multiplier = weight_config.performance_multiplier
        
        # Apply domain expertise bonus for high-confidence decisions
        domain_bonus = 0.0
        if decision.confidence >= 0.8:
            domain_bonus = weight_config.domain_expertise_bonus
        
        return base_weight * performance_multiplier + domain_bonus
    
    def _get_agent_performance_score(self, agent_type: AgentType) -> float:
        """Get performance score for agent type (placeholder)"""
        # In a real implementation, this would query actual performance metrics
        performance_map = {
            AgentType.DOMAIN_EXPERT: 0.85,
            AgentType.ML_CLASSIFIER: 0.80,
            AgentType.SCHEMA_ANALYZER: 0.75,
            AgentType.CONTENT_ANALYZER: 0.70,
            AgentType.PATTERN_RECOGNIZER: 0.72
        }
        return performance_map.get(agent_type, 0.6)
    
    def _calculate_agreement_score(self, decision: AgentDecision, 
                                 all_decisions: List[AgentDecision]) -> float:
        """Calculate how much this decision agrees with others"""
        if len(all_decisions) <= 1:
            return 1.0
        
        agreement_count = sum(1 for d in all_decisions 
                            if d.classification == decision.classification)
        
        return agreement_count / len(all_decisions)
    
    def _combine_voting_strategies(self, strategy_results: Dict[str, Dict[str, Any]], 
                                 decisions: List[AgentDecision],
                                 vector_insights: Dict[str, Any]) -> Dict[str, Any]:
        """Combine results from multiple voting strategies with vector enhancement"""
        if not strategy_results:
            # Fallback to simple majority
            return self._simple_majority_vote(decisions)
        
        # Weight different strategies with vector considerations
        strategy_weights = {
            'weighted_average': 0.20,
            'confidence_weighted': 0.20,
            'expert_priority': 0.25,
            'ensemble_learning': 0.15,
            'vector_enhanced': 0.20
        }
        
        # Boost vector_enhanced strategy if strong vector signals
        avg_vector_boost = vector_insights.get('avg_vector_boost', 0)
        if avg_vector_boost > 0.1:
            strategy_weights['vector_enhanced'] += 0.1
            # Reduce others proportionally
            for strategy in ['weighted_average', 'confidence_weighted']:
                strategy_weights[strategy] -= 0.05
        
        combined_scores = {}
        total_weight = 0.0
        
        for strategy_name, result in strategy_results.items():
            if strategy_name not in strategy_weights:
                continue
                
            weight = strategy_weights[strategy_name]
            classification = result['classification']
            confidence = result['confidence']
            
            if classification not in combined_scores:
                combined_scores[classification] = 0.0
            
            combined_scores[classification] += confidence * weight
            total_weight += weight
        
        # Normalize
        if total_weight > 0:
            for classification in combined_scores:
                combined_scores[classification] /= total_weight
        
        if combined_scores:
            best_classification = max(combined_scores.items(), key=lambda x: x[1])
            return {
                'classification': best_classification[0],
                'confidence': best_classification[1],
                'combined_scores': combined_scores,
                'vector_enhanced': avg_vector_boost > 0
            }
        else:
            return self._simple_majority_vote(decisions)
    
    def _simple_majority_vote(self, decisions: List[AgentDecision]) -> Dict[str, Any]:
        """Simple majority voting fallback"""
        classification_counts = {}
        
        for decision in decisions:
            classification = decision.classification
            if classification not in classification_counts:
                classification_counts[classification] = 0
            classification_counts[classification] += 1
        
        if classification_counts:
            best_classification = max(classification_counts.items(), key=lambda x: x[1])
            confidence = best_classification[1] / len(decisions)
            
            return {
                'classification': best_classification[0],
                'confidence': confidence,
                'vote_counts': classification_counts
            }
        else:
            return {
                'classification': 'unknown',
                'confidence': 0.0,
                'vote_counts': {}
            }
    
    def _calculate_consensus_metrics(self, decisions: List[AgentDecision]) -> Dict[str, Any]:
        """Calculate consensus and agreement metrics"""
        if not decisions:
            return {'consensus_level': 0.0, 'agreement_score': 0.0}
        
        # Count classifications
        classification_counts = {}
        confidences = []
        
        for decision in decisions:
            classification = decision.classification
            classification_counts[classification] = classification_counts.get(classification, 0) + 1
            confidences.append(decision.confidence)
        
        # Calculate consensus level (how much agents agree)
        max_count = max(classification_counts.values()) if classification_counts else 0
        consensus_level = max_count / len(decisions)
        
        # Calculate confidence metrics
        avg_confidence = statistics.mean(confidences)
        confidence_variance = statistics.variance(confidences) if len(confidences) > 1 else 0
        
        return {
            'consensus_level': consensus_level,
            'agreement_score': consensus_level,
            'avg_confidence': avg_confidence,
            'confidence_variance': confidence_variance,
            'classification_distribution': classification_counts,
            'participating_agents': len(decisions)
        }
    
    def _analyze_conflicts(self, decisions: List[AgentDecision]) -> Dict[str, Any]:
        """Analyze conflicts and disagreements between agents"""
        conflicts = []
        classification_groups = {}
        
        # Group decisions by classification
        for decision in decisions:
            classification = decision.classification
            if classification not in classification_groups:
                classification_groups[classification] = []
            classification_groups[classification].append(decision)
        
        # Identify conflicts
        if len(classification_groups) > 1:
            sorted_groups = sorted(classification_groups.items(), 
                                 key=lambda x: len(x[1]), reverse=True)
            
            majority_class = sorted_groups[0][0]
            minority_classes = [group[0] for group in sorted_groups[1:]]
            
            for minority_class in minority_classes:
                minority_agents = classification_groups[minority_class]
                conflicts.append({
                    'minority_classification': minority_class,
                    'agent_count': len(minority_agents),
                    'agents': [d.agent_id for d in minority_agents],
                    'avg_confidence': statistics.mean([d.confidence for d in minority_agents])
                })
        
        return {
            'has_conflicts': len(classification_groups) > 1,
            'classification_count': len(classification_groups),
            'conflicts': conflicts,
            'strongest_minority': conflicts[0] if conflicts else None
        }
    
    def _generate_voting_reasoning(self, decisions: List[AgentDecision], 
                                 final_result: Dict[str, Any], 
                                 consensus_metrics: Dict[str, Any],
                                 vector_insights: Dict[str, Any]) -> str:
        """Generate comprehensive reasoning for voting result with vector insights"""
        reasoning_parts = []
        
        # Basic stats
        agent_count = len(decisions)
        consensus_level = consensus_metrics.get('consensus_level', 0.0)
        
        reasoning_parts.append(f"Voting with {agent_count} agents")
        reasoning_parts.append(f"Consensus level: {consensus_level:.2f}")
        
        # Classification distribution
        distribution = consensus_metrics.get('classification_distribution', {})
        if distribution:
            dist_str = ', '.join([f"{k}({v})" for k, v in distribution.items()])
            reasoning_parts.append(f"Votes: {dist_str}")
        
        # Vector insights
        avg_vector_boost = vector_insights.get('avg_vector_boost', 0)
        if avg_vector_boost > 0:
            reasoning_parts.append(f"Vector boost: +{avg_vector_boost:.3f}")
            
            max_similarity = vector_insights.get('max_similarity', 0)
            if max_similarity > 0:
                reasoning_parts.append(f"Max similarity: {max_similarity:.3f}")
        
        # Final confidence
        final_confidence = final_result.get('confidence', 0.0)
        reasoning_parts.append(f"Final confidence: {final_confidence:.2f}")
        
        # Conflict analysis
        if consensus_level < 0.6:
            reasoning_parts.append("Low consensus - conflicting agent opinions")
        elif consensus_level >= 0.8:
            reasoning_parts.append("High consensus - strong agent agreement")
        
        # Vector contribution
        vector_participation = vector_insights.get('vector_participation_rate', 0)
        if vector_participation > 0:
            reasoning_parts.append(f"Vector participation: {vector_participation:.0%}")
        
        return "; ".join(reasoning_parts)
    
    def get_capabilities(self) -> List[str]:
        return self.capabilities


class SupervisorAgent(BaseAgent):
    """Main supervisor agent with vector DB integration for orchestrating classification"""
    
    def __init__(self, agent_id: str, config: Dict[str, Any]):
        super().__init__(agent_id, AgentType.SUPERVISOR, config)
        self.capabilities = [
            "process_orchestration",
            "agent_management",
            "quality_control",
            "result_validation",
            "learning_coordination",
            "vector_db_integration"
        ]
        
        # Vector DB and classification components
        self.vector_db = config.get('vector_db')
        self.classification_engine = config.get('classification_engine')
        self.azure_openai = config.get('azure_openai')
        
        # Initialize sub-agents with vector DB access
        self.classification_agents = {}
        self.voting_coordinator = None
        self.initialize_agents()
        
        # Performance tracking
        self.classification_history = []
        self.performance_metrics = {
            "total_classifications": 0,
            "successful_classifications": 0,
            "avg_processing_time": 0.0,
            "avg_consensus_level": 0.0,
            "avg_vector_boost": 0.0,
            "vector_enhanced_classifications": 0
        }
    
    def initialize_agents(self):
        """Initialize all classification agents with vector DB access"""
        try:
            from .specialized_agents import (
                SchemaAnalyzerAgent, ContentAnalyzerAgent, 
                DomainExpertAgent, MLClassifierAgent
            )
            
            # Enhanced config with vector DB access
            enhanced_config = self.config.copy()
            enhanced_config.update({
                'vector_db': self.vector_db,
                'enable_vector_search': True,
                'similarity_threshold': self.config.get('similarity_threshold', 0.6),
                'auto_store_results': self.config.get('auto_store_results', True)
            })
            
            # Create classification agents with vector DB access
            self.classification_agents = {
                'schema_analyzer': SchemaAnalyzerAgent('schema_agent_01', enhanced_config),
                'content_analyzer': ContentAnalyzerAgent('content_agent_01', enhanced_config),
                'domain_expert': DomainExpertAgent('domain_expert_01', enhanced_config),
                'ml_classifier': MLClassifierAgent('ml_classifier_01', enhanced_config)
            }
            
            # Create vector-enhanced voting coordinator
            self.voting_coordinator = VectorEnhancedVotingCoordinatorAgent('voting_coordinator_01', enhanced_config)
            
            # Activate all agents
            for agent in self.classification_agents.values():
                agent.activate()
            self.voting_coordinator.activate()
            
            self.logger.info(f"Initialized {len(self.classification_agents)} classification agents with vector DB integration")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize agents: {e}")
            raise
    
    async def analyze(self, file_context: FileContext) -> AgentDecision:
        """Main classification orchestration method with vector enhancement"""
        start_time = time.time()
        
        try:
            self.logger.info(f"Starting vector-enhanced classification for file: {file_context.file_name}")
            
            # Step 1: Collect decisions from all agents (with vector enhancement)
            agent_decisions = await self._collect_agent_decisions(file_context)
            
            # Step 2: Coordinate voting with vector insights
            voting_result = await self._coordinate_voting(agent_decisions, file_context)
            
            # Step 3: Validate and finalize result
            final_decision = await self._finalize_decision(voting_result, file_context)
            
            # Step 4: Update performance metrics (including vector metrics)
            self._update_supervisor_metrics(final_decision, voting_result, time.time() - start_time)
            
            # Step 5: Store classification history and results
            self._store_classification_history(file_context, voting_result, final_decision)
            
            # Step 6: Auto-store successful classification for learning
            if final_decision.confidence > 0.7:
                await self._store_classification_result(file_context, final_decision, voting_result)
            
            self.logger.info(f"Vector-enhanced classification completed: {final_decision.classification} "
                           f"({final_decision.confidence:.2f}) in {final_decision.processing_time:.2f}s")
            
            return final_decision
            
        except Exception as e:
            self.logger.error(f"Supervisor classification failed: {e}")
            return AgentDecision(
                agent_id=self.agent_id,
                agent_type=self.agent_type,
                classification="unknown",
                confidence=0.0,
                reasoning=f"Supervisor error: {str(e)}",
                processing_time=time.time() - start_time,
                metadata={"error": str(e)}
            )
    
    async def _collect_agent_decisions(self, file_context: FileContext) -> List[AgentDecision]:
        """Collect decisions from all classification agents with vector enhancement"""
        decisions = []
        tasks = []
        
        # Create tasks for parallel execution
        for agent_name, agent in self.classification_agents.items():
            if agent.is_active:
                task = asyncio.create_task(agent.analyze(file_context))
                tasks.append((agent_name, task))
        
        # Wait for all agents to complete
        for agent_name, task in tasks:
            try:
                decision = await asyncio.wait_for(task, timeout=30.0)  # 30 second timeout
                decisions.append(decision)
                
                # Log vector enhancement info
                vector_boost = 0.0
                if hasattr(decision, 'evidence') and decision.evidence:
                    vector_results = decision.evidence.get('vector_results', {})
                    vector_boost = vector_results.get('confidence_boost', 0.0)
                
                self.logger.debug(f"Agent {agent_name} decision: {decision.classification} "
                                f"({decision.confidence:.2f}, vector boost: +{vector_boost:.3f})")
                
            except asyncio.TimeoutError:
                self.logger.warning(f"Agent {agent_name} timed out")
            except Exception as e:
                self.logger.error(f"Agent {agent_name} failed: {e}")
        
        return decisions
    
    async def _coordinate_voting(self, agent_decisions: List[AgentDecision], 
                               file_context: FileContext) -> VotingResult:
        """Coordinate voting process with vector enhancement"""
        try:
            if not agent_decisions:
                raise ValueError("No agent decisions available for voting")
            
            voting_result = await self.voting_coordinator.coordinate_voting(agent_decisions, file_context)
            
            # Log vector contribution
            vector_contribution = voting_result.vector_insights.get('avg_vector_boost', 0)
            self.logger.info(f"Vector-enhanced voting completed: {voting_result.final_classification} "
                           f"(consensus: {voting_result.consensus_level:.2f}, "
                           f"vector boost: +{vector_contribution:.3f})")
            
            return voting_result
            
        except Exception as e:
            self.logger.error(f"Voting coordination failed: {e}")
            # Create fallback voting result
            return self._create_fallback_voting_result(agent_decisions, str(e))
    
    async def _store_classification_result(self, file_context: FileContext,
                                         final_decision: AgentDecision,
                                         voting_result: VotingResult):
        """Store classification result in vector DB for learning"""
        if not self.vector_db:
            return
        
        try:
            from core.knowledgebase.document_models import FileDocument
            
            # Create a comprehensive document for the classification result
            file_doc = FileDocument(
                file_path=file_context.file_path,
                file_type=file_context.file_type,
                file_size=file_context.file_size,
                extracted_content=file_context.extracted_content,
                user_document_type=final_decision.classification,
                user_description=f"Auto-stored successful classification: {final_decision.reasoning}",
                processing_metadata={
                    'supervisor_classification': True,
                    'consensus_level': voting_result.consensus_level,
                    'confidence': final_decision.confidence,
                    'vector_boost': voting_result.vector_insights.get('avg_vector_boost', 0),
                    'participating_agents': len(voting_result.participating_agents),
                    'processing_timestamp': time.time()
                }
            )
            
            # Store in uploaded_files collection
            success = self.vector_db.add_documents('cpg_uploaded_files', [file_doc])
            
            if success:
                self.logger.debug(f"Stored classification result in vector DB: {final_decision.classification}")
            
        except Exception as e:
            self.logger.warning(f"Failed to store classification result in vector DB: {e}")
    
    def _create_fallback_voting_result(self, agent_decisions: List[AgentDecision], 
                                     error_msg: str) -> VotingResult:
        """Create fallback voting result when voting fails"""
        if agent_decisions:
            # Use the decision with highest confidence
            best_decision = max(agent_decisions, key=lambda d: d.confidence)
            return VotingResult(
                final_classification=best_decision.classification,
                final_confidence=best_decision.confidence,
                consensus_level=0.0,
                participating_agents=[d.agent_id for d in agent_decisions],
                agent_decisions=agent_decisions,
                voting_summary={"error": error_msg, "fallback": True},
                processing_time=0.0,
                reasoning=f"Fallback to highest confidence decision due to voting error: {error_msg}"
            )
        else:
            return VotingResult(
                final_classification="unknown",
                final_confidence=0.0,
                consensus_level=0.0,
                participating_agents=[],
                agent_decisions=[],
                voting_summary={"error": error_msg},
                processing_time=0.0,
                reasoning=f"No agent decisions available: {error_msg}"
            )
    
    async def _finalize_decision(self, voting_result: VotingResult, 
                               file_context: FileContext) -> AgentDecision:
        """Finalize and validate the classification decision with vector insights"""
        # Apply supervisor-level validation and quality checks
        validated_result = self._validate_classification_result(voting_result, file_context)
        
        # Create final agent decision with vector insights
        final_decision = AgentDecision(
            agent_id=self.agent_id,
            agent_type=self.agent_type,
            classification=validated_result['classification'],
            confidence=validated_result['confidence'],
            reasoning=self._generate_supervisor_reasoning(voting_result, validated_result),
            evidence={
                "voting_result": voting_result.__dict__,
                "validation_checks": validated_result.get('validation_checks', {}),
                "quality_score": validated_result.get('quality_score', 0.0),
                "vector_insights": voting_result.vector_insights
            },
            processing_time=voting_result.processing_time,
            metadata={
                "consensus_level": voting_result.consensus_level,
                "participating_agents": len(voting_result.participating_agents),
                "supervisor_validation": True,
                "vector_enhanced": voting_result.vector_insights.get('avg_vector_boost', 0) > 0,
                "vector_boost": voting_result.vector_insights.get('avg_vector_boost', 0)
            }
        )
        
        return final_decision
    
    def _validate_classification_result(self, voting_result: VotingResult, 
                                      file_context: FileContext) -> Dict[str, Any]:
        """Apply supervisor-level validation to classification result with vector considerations"""
        validation_checks = {}
        quality_score = 1.0
        
        # Check 1: Consensus threshold
        min_consensus = self.config.get('min_consensus_threshold', 0.4)
        if voting_result.consensus_level < min_consensus:
            validation_checks['low_consensus'] = True
            quality_score *= 0.8
        
        # Check 2: Confidence threshold (adjusted for vector boost)
        min_confidence = self.config.get('min_confidence_threshold', 0.3)
        effective_confidence = voting_result.final_confidence
        vector_boost = voting_result.vector_insights.get('avg_vector_boost', 0)
        
        # Lower threshold if strong vector support
        if vector_boost > 0.1:
            adjusted_threshold = max(min_confidence - 0.1, 0.2)
        else:
            adjusted_threshold = min_confidence
        
        if effective_confidence < adjusted_threshold:
            validation_checks['low_confidence'] = True
            quality_score *= 0.7
        
        # Check 3: Agent participation
        min_agents = self.config.get('min_participating_agents', 2)
        if len(voting_result.participating_agents) < min_agents:
            validation_checks['insufficient_agents'] = True
            quality_score *= 0.6
        
        # Check 4: Domain consistency (with vector support consideration)
        domain_check = self._check_domain_consistency(voting_result, file_context)
        if not domain_check['consistent']:
            # Vector support might override domain inconsistency
            if vector_boost < 0.1:
                validation_checks['domain_inconsistency'] = domain_check
                quality_score *= 0.9
        
        # Check 5: Vector quality indicators
        vector_quality = self._assess_vector_quality(voting_result.vector_insights)
        if vector_quality['quality_score'] > 0.8:
            quality_score *= 1.1  # Boost for high vector quality
        
        # Apply quality-based confidence adjustment
        adjusted_confidence = voting_result.final_confidence * quality_score
        
        return {
            'classification': voting_result.final_classification,
            'confidence': adjusted_confidence,
            'original_confidence': voting_result.final_confidence,
            'quality_score': quality_score,
            'validation_checks': validation_checks,
            'vector_quality': vector_quality
        }
    
    def _assess_vector_quality(self, vector_insights: Dict[str, Any]) -> Dict[str, Any]:
        """Assess the quality of vector search contributions"""
        quality_indicators = {
            'participation_rate': vector_insights.get('vector_participation_rate', 0),
            'avg_similarity': vector_insights.get('avg_similarity', 0),
            'max_similarity': vector_insights.get('max_similarity', 0),
            'collection_diversity': len(vector_insights.get('collection_distribution', {})),
            'consensus_strength': len(vector_insights.get('classification_consensus', {}))
        }
        
        # Calculate overall quality score
        quality_score = 0.0
        quality_score += min(quality_indicators['participation_rate'] * 0.3, 0.3)
        quality_score += min(quality_indicators['avg_similarity'] * 0.25, 0.25)
        quality_score += min(quality_indicators['max_similarity'] * 0.2, 0.2)
        quality_score += min(quality_indicators['collection_diversity'] * 0.05, 0.15)
        quality_score += min(quality_indicators['consensus_strength'] * 0.02, 0.1)
        
        return {
            'quality_score': quality_score,
            'indicators': quality_indicators,
            'high_quality': quality_score > 0.7
        }
    
    def _check_domain_consistency(self, voting_result: VotingResult, 
                                file_context: FileContext) -> Dict[str, Any]:
        """Check if classification is consistent with CPG domain knowledge"""
        classification = voting_result.final_classification
        
        if classification not in CPGDomainKnowledge.CPG_DATA_TYPES:
            return {'consistent': False, 'reason': 'Invalid CPG data type'}
        
        # Use domain knowledge to verify consistency
        domain_score = CPGDomainKnowledge.get_data_type_score(
            classification, 
            file_context.file_features
        )
        
        consistency_threshold = 0.2
        is_consistent = domain_score >= consistency_threshold
        
        return {
            'consistent': is_consistent,
            'domain_score': domain_score,
            'threshold': consistency_threshold,
            'reason': 'Domain knowledge validation'
        }
    
    def _generate_supervisor_reasoning(self, voting_result: VotingResult, 
                                     validated_result: Dict[str, Any]) -> str:
        """Generate comprehensive reasoning from supervisor perspective with vector insights"""
        reasoning_parts = [
            f"Supervisor decision based on {len(voting_result.participating_agents)} agents",
            f"Consensus: {voting_result.consensus_level:.2f}",
            f"Original confidence: {voting_result.final_confidence:.2f}",
            f"Quality-adjusted confidence: {validated_result['confidence']:.2f}"
        ]
        
        # Add vector insights
        vector_insights = voting_result.vector_insights
        avg_vector_boost = vector_insights.get('avg_vector_boost', 0)
        if avg_vector_boost > 0:
            reasoning_parts.append(f"Vector boost: +{avg_vector_boost:.3f}")
            
            max_similarity = vector_insights.get('max_similarity', 0)
            if max_similarity > 0:
                reasoning_parts.append(f"Max similarity: {max_similarity:.3f}")
        
        # Add validation warnings
        validation_checks = validated_result.get('validation_checks', {})
        if validation_checks:
            warnings = []
            if validation_checks.get('low_consensus'):
                warnings.append("low consensus")
            if validation_checks.get('low_confidence'):
                warnings.append("low confidence")
            if validation_checks.get('insufficient_agents'):
                warnings.append("insufficient agents")
            if validation_checks.get('domain_inconsistency'):
                warnings.append("domain inconsistency")
            
            if warnings:
                reasoning_parts.append(f"Warnings: {', '.join(warnings)}")
        
        # Add vector quality assessment
        vector_quality = validated_result.get('vector_quality', {})
        if vector_quality.get('high_quality'):
            reasoning_parts.append("High vector quality")
        
        # Add voting details
        reasoning_parts.append(f"Voting: {voting_result.reasoning}")
        
        return "; ".join(reasoning_parts)
    
    def _update_supervisor_metrics(self, final_decision: AgentDecision, 
                                 voting_result: VotingResult, processing_time: float):
        """Update supervisor performance metrics including vector metrics"""
        self.performance_metrics["total_classifications"] += 1
        
        if final_decision.classification != "unknown":
            self.performance_metrics["successful_classifications"] += 1
        
        # Update average processing time
        total_time = (self.performance_metrics["avg_processing_time"] * 
                     (self.performance_metrics["total_classifications"] - 1) + processing_time)
        self.performance_metrics["avg_processing_time"] = (
            total_time / self.performance_metrics["total_classifications"]
        )
        
        # Update average consensus level
        total_consensus = (self.performance_metrics["avg_consensus_level"] * 
                          (self.performance_metrics["total_classifications"] - 1) + 
                          voting_result.consensus_level)
        self.performance_metrics["avg_consensus_level"] = (
            total_consensus / self.performance_metrics["total_classifications"]
        )
        
        # Update vector metrics
        vector_boost = voting_result.vector_insights.get('avg_vector_boost', 0)
        if vector_boost > 0:
            self.performance_metrics["vector_enhanced_classifications"] += 1
        
        # Update average vector boost
        total_vector_boost = (self.performance_metrics["avg_vector_boost"] * 
                             (self.performance_metrics["total_classifications"] - 1) + vector_boost)
        self.performance_metrics["avg_vector_boost"] = (
            total_vector_boost / self.performance_metrics["total_classifications"]
        )
    
    def _store_classification_history(self, file_context: FileContext, 
                                    voting_result: VotingResult, 
                                    final_decision: AgentDecision):
        """Store classification history for analysis and learning with vector insights"""
        history_entry = {
            'timestamp': datetime.now().isoformat(),
            'file_name': file_context.file_name,
            'file_type': file_context.file_type,
            'classification': final_decision.classification,
            'confidence': final_decision.confidence,
            'consensus_level': voting_result.consensus_level,
            'participating_agents': len(voting_result.participating_agents),
            'processing_time': final_decision.processing_time,
            'vector_enhanced': voting_result.vector_insights.get('avg_vector_boost', 0) > 0,
            'vector_boost': voting_result.vector_insights.get('avg_vector_boost', 0),
            'max_similarity': voting_result.vector_insights.get('max_similarity', 0),
            'agent_decisions': [
                {
                    'agent_id': d.agent_id,
                    'agent_type': d.agent_type.value,
                    'classification': d.classification,
                    'confidence': d.confidence,
                    'vector_boost': d.evidence.get('vector_results', {}).get('confidence_boost', 0) if d.evidence else 0
                }
                for d in voting_result.agent_decisions
            ]
        }
        
        self.classification_history.append(history_entry)
        
        # Keep only last 1000 entries
        if len(self.classification_history) > 1000:
            self.classification_history = self.classification_history[-1000:]
    
    def get_supervisor_statistics(self) -> Dict[str, Any]:
        """Get comprehensive supervisor statistics including vector metrics"""
        stats = self.performance_metrics.copy()
        
        # Calculate success rate
        if stats["total_classifications"] > 0:
            stats["success_rate"] = (stats["successful_classifications"] / 
                                   stats["total_classifications"])
            stats["vector_enhancement_rate"] = (stats["vector_enhanced_classifications"] / 
                                               stats["total_classifications"])
        else:
            stats["success_rate"] = 0.0
            stats["vector_enhancement_rate"] = 0.0
        
        # Add agent statistics
        agent_stats = {}
        for agent_name, agent in self.classification_agents.items():
            agent_stats[agent_name] = agent.get_performance_metrics()
        
        stats["agent_performance"] = agent_stats
        stats["recent_classifications"] = self.classification_history[-10:]  # Last 10
        
        # Add vector DB statistics if available
        if self.vector_db:
            try:
                vector_stats = self.vector_db.get_collection_stats()
                stats["vector_db_stats"] = vector_stats
            except Exception as e:
                self.logger.warning(f"Failed to get vector DB stats: {e}")
                stats["vector_db_stats"] = {"error": str(e)}
        
        return stats
    
    def get_capabilities(self) -> List[str]:
        return self.capabilities