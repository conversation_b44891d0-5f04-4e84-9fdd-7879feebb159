"""
Specialized Agents for CPG File Classification with Vector DB Integration
Enhanced with comprehensive debugging and vector operation tracking
"""

import asyncio
import time
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.ensemble import RandomForestClassifier, VotingClassifier
from sklearn.naive_bayes import MultinomialNB
from sklearn.svm import SVC
from sklearn.neural_network import MLPClassifier
from sklearn.metrics.pairwise import cosine_similarity
import re
import json
import logging

import sys
from pathlib import Path

project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from agents.base_agent import BaseAgent, AgentDecision, FileContext, AgentType, CPGDomainKnowledge

class VectorDBEnhancedAgent(BaseAgent):
    """Base class for agents with vector DB integration and comprehensive debugging"""
    
    def __init__(self, agent_id: str, agent_type: AgentType, config: Dict[str, Any]):
        super().__init__(agent_id, agent_type, config)
        
        # Initialize vector components with enhanced debugging
        self.vector_db = config.get('vector_db')
        self.vector_enabled = config.get('enable_vector_search', True) and self.vector_db is not None
        self.similarity_threshold = config.get('similarity_threshold', 0.6)
        self.auto_store_results = config.get('auto_store_results', True)
        self.logger = logging.getLogger(__name__)
        
        # Enhanced debug logging
        vector_logger = logging.getLogger('vector_operations')
        vector_logger.info(f"Agent {agent_id} ({agent_type.value}) initialization:")
        vector_logger.info(f"  - vector_db present: {self.vector_db is not None}")
        vector_logger.info(f"  - config enable_vector_search: {config.get('enable_vector_search', 'NOT_SET')}")
        vector_logger.info(f"  - final vector_enabled: {self.vector_enabled}")
        vector_logger.info(f"  - similarity_threshold: {self.similarity_threshold}")
        vector_logger.info(f"  - auto_store_results: {self.auto_store_results}")
        
        if self.vector_db is not None:
            vector_logger.info(f"  - vector_db type: {type(self.vector_db).__name__}")
            # Test vector DB connection
            try:
                stats = self.vector_db.get_collection_stats()
                vector_logger.info(f"  - vector_db collections: {list(stats.keys())}")
                total_docs = sum(s.get('document_count', 0) for s in stats.values())
                vector_logger.info(f"  - total documents in vector_db: {total_docs}")
                
                if total_docs == 0:
                    vector_logger.warning(f"  - WARNING: Vector DB has no documents!")
                    
            except Exception as e:
                vector_logger.error(f"  - ERROR: Vector DB connection test failed: {e}")
                self.vector_enabled = False
        else:
            vector_logger.warning(f"  - WARNING: No vector_db provided in config")
            if config.get('enable_vector_search', True):
                vector_logger.warning(f"  - Vector search enabled but no vector_db - this will cause issues")
        
        # Collection weights for aggregating results
        self.collection_weights = {
            'cpg_learning_feedback': 0.35,  # Highest - user corrections
            'cpg_uploaded_files': 0.25,     # Labeled training data
            'cpg_domain_knowledge': 0.20,   # Expert knowledge
            'cpg_schemas': 0.15,            # Structural patterns
            'cpg_content': 0.05             # Supporting content
        }
        
        # Statistics tracking
        self.vector_stats = {
            'total_searches': 0,
            'successful_searches': 0,
            'total_matches': 0,
            'avg_confidence_boost': 0.0,
            'collection_usage': {col: 0 for col in self.collection_weights.keys()}
        }
        
        vector_logger.info(f"Agent {agent_id} initialization complete. Vector ready: {self.vector_enabled}")
    
    async def search_vector_db(self, query_features: Dict[str, Any]) -> Dict[str, Any]:
        """Search all vector DB collections for similar patterns with comprehensive debugging"""
        vector_logger = logging.getLogger('vector_operations')
        
        # Enhanced entry logging
        vector_logger.info(f"=== VECTOR SEARCH START: Agent {self.agent_id} ===")
        vector_logger.info(f"Vector enabled: {self.vector_enabled}")
        vector_logger.info(f"Vector DB present: {self.vector_db is not None}")
        vector_logger.info(f"Query features keys: {list(query_features.keys())}")
        
        self.vector_stats['total_searches'] += 1
        
        if not self.vector_enabled:
            vector_logger.warning(f"Agent {self.agent_id} SKIPPING vector search - not enabled")
            vector_logger.warning(f"  Reason: vector_enabled={self.vector_enabled}")
            vector_logger.warning(f"  vector_db exists: {self.vector_db is not None}")
            return {
                'collection_results': {}, 
                'confidence_boost': 0.0, 
                'best_matches': [], 
                'evidence': {},
                'debug_info': {
                    'skipped': True,
                    'reason': 'vector_not_enabled',
                    'vector_db_present': self.vector_db is not None
                }
            }
        
        try:
            # Build agent-specific search query
            search_query = self._build_search_query(query_features)
            vector_logger.info(f"Agent {self.agent_id} built search query: '{search_query[:200]}...'")
            
            if not search_query or search_query.strip() == "":
                vector_logger.warning(f"Agent {self.agent_id} generated EMPTY search query!")
                return {
                    'collection_results': {}, 
                    'confidence_boost': 0.0, 
                    'best_matches': [], 
                    'evidence': {},
                    'debug_info': {
                        'skipped': True,
                        'reason': 'empty_search_query',
                        'query_features': query_features
                    }
                }
            
            # Search all collections with detailed logging
            all_results = {}
            collections = ['cpg_uploaded_files', 'cpg_learning_feedback', 'cpg_schemas', 'cpg_content', 'cpg_domain_knowledge']
            
            vector_logger.info(f"Agent {self.agent_id} searching {len(collections)} collections...")
            
            for collection in collections:
                try:
                    vector_logger.debug(f"Agent {self.agent_id} querying collection: {collection}")
                    
                    # Perform the search
                    results = self.vector_db.query_collection(collection, search_query, n_results=5)
                    all_results[collection] = results
                    
                    # Log detailed results
                    match_count = results.get('count', 0)
                    matches = results.get('matches', [])
                    
                    vector_logger.info(f"Agent {self.agent_id} - {collection}: {match_count} matches")
                    
                    if match_count > 0:
                        self.vector_stats['collection_usage'][collection] += 1
                        
                        # Log details of top matches
                        for i, match in enumerate(matches[:2]):  # Top 2 matches
                            similarity = match.get('similarity_score', 0)
                            metadata = match.get('metadata', {})
                            doc_type = metadata.get('user_document_type', metadata.get('document_type', 'unknown'))
                            
                            vector_logger.info(f"  Match {i+1}: similarity={similarity:.3f}, type={doc_type}")
                            
                            if similarity > 0.8:
                                vector_logger.info(f"    HIGH SIMILARITY MATCH in {collection}!")
                    else:
                        vector_logger.debug(f"  No matches in {collection}")
                        
                except Exception as e:
                    vector_logger.error(f"Agent {self.agent_id} - {collection} search FAILED: {e}")
                    vector_logger.error(f"  Search query was: '{search_query}'")
                    all_results[collection] = {'matches': [], 'count': 0, 'error': str(e)}
            
            # Weight and aggregate results
            vector_logger.info(f"Agent {self.agent_id} aggregating results...")
            weighted_results = self._weight_collection_results(all_results)
            confidence_boost = self._calculate_confidence_boost(weighted_results)
            evidence = self._extract_evidence(weighted_results)
            
            # Update statistics
            total_matches = sum(r.get('count', 0) for r in all_results.values())
            if total_matches > 0:
                self.vector_stats['successful_searches'] += 1
                self.vector_stats['total_matches'] += total_matches
                
                # Update average confidence boost
                old_avg = self.vector_stats['avg_confidence_boost']
                old_count = self.vector_stats['successful_searches'] - 1
                new_avg = (old_avg * old_count + confidence_boost) / self.vector_stats['successful_searches']
                self.vector_stats['avg_confidence_boost'] = new_avg
            
            # Final logging
            vector_logger.info(f"Agent {self.agent_id} vector search completed:")
            vector_logger.info(f"  - Total matches: {total_matches}")
            vector_logger.info(f"  - Weighted results: {len(weighted_results)}")
            vector_logger.info(f"  - Confidence boost: {confidence_boost:.3f}")
            vector_logger.info(f"  - Evidence extracted: {len(evidence)}")
            
            if confidence_boost > 0:
                vector_logger.info(f"  *** VECTOR ENHANCEMENT APPLIED: +{confidence_boost:.3f} ***")
            else:
                vector_logger.info(f"  No confidence boost applied")
            
            vector_logger.info(f"=== VECTOR SEARCH END: Agent {self.agent_id} ===")
            
            return {
                'collection_results': all_results,
                'confidence_boost': confidence_boost,
                'best_matches': weighted_results,
                'evidence': evidence,
                'search_query': search_query,
                'debug_info': {
                    'skipped': False,
                    'total_collections_searched': len(collections),
                    'collections_with_matches': len([c for c, r in all_results.items() if r.get('count', 0) > 0]),
                    'total_matches': total_matches,
                    'query_length': len(search_query)
                }
            }
            
        except Exception as e:
            vector_logger.error(f"Agent {self.agent_id} vector DB search FAILED with exception: {e}")
            vector_logger.error(f"  Exception type: {type(e).__name__}")
            vector_logger.error(f"  Vector DB type: {type(self.vector_db).__name__ if self.vector_db else 'None'}")
            import traceback
            vector_logger.error(f"  Full traceback: {traceback.format_exc()}")
            
            return {
                'collection_results': {}, 
                'confidence_boost': 0.0, 
                'best_matches': [], 
                'evidence': {},
                'debug_info': {
                    'skipped': True,
                    'reason': 'exception',
                    'error': str(e),
                    'error_type': type(e).__name__
                }
            }
    
    def _build_search_query(self, features: Dict[str, Any]) -> str:
        """Build search query from analysis features - to be overridden by subclasses"""
        # Default implementation with debug logging
        vector_logger = logging.getLogger('vector_operations')
        
        # Try to build a basic query from available features
        query_parts = []
        
        # Add basic file information
        if 'file_name' in features:
            query_parts.append(f"file: {features['file_name']}")
        
        # Add any string features
        for key, value in features.items():
            if isinstance(value, str) and value.strip():
                query_parts.append(f"{key}: {value}")
            elif isinstance(value, (int, float)) and value > 0:
                query_parts.append(f"{key}: {value}")
        
        query = " | ".join(query_parts) if query_parts else "default search query"
        
        vector_logger.debug(f"Agent {self.agent_id} default query build: '{query[:100]}...'")
        return query
    
    def _weight_collection_results(self, all_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Weight and aggregate results from all collections with enhanced logging"""
        vector_logger = logging.getLogger('vector_operations')
        weighted_matches = []
        
        vector_logger.debug(f"Agent {self.agent_id} weighting results from {len(all_results)} collections")
        
        for collection, results in all_results.items():
            weight = self.collection_weights.get(collection, 0.1)
            matches = results.get('matches', [])
            
            vector_logger.debug(f"  Collection {collection}: {len(matches)} matches, weight={weight}")
            
            for match in matches:
                weighted_match = match.copy()
                original_score = match.get('similarity_score', 0)
                weighted_score = original_score * weight
                
                weighted_match.update({
                    'collection': collection,
                    'weighted_score': weighted_score,
                    'collection_weight': weight,
                    'original_similarity': original_score
                })
                weighted_matches.append(weighted_match)
                
                vector_logger.debug(f"    Match: {original_score:.3f} -> {weighted_score:.3f}")
        
        # Sort by weighted score
        weighted_matches.sort(key=lambda x: x['weighted_score'], reverse=True)
        top_matches = weighted_matches[:10]  # Top 10 weighted matches
        
        vector_logger.debug(f"Agent {self.agent_id} weighted results: {len(top_matches)} top matches")
        if top_matches:
            best_score = top_matches[0]['weighted_score']
            vector_logger.debug(f"  Best weighted score: {best_score:.3f}")
        
        return top_matches
    
    def _calculate_confidence_boost(self, weighted_results: List[Dict[str, Any]]) -> float:
        """Calculate confidence boost based on similarity scores with enhanced logic"""
        vector_logger = logging.getLogger('vector_operations')
        
        if not weighted_results:
            vector_logger.debug(f"Agent {self.agent_id} confidence boost: 0.0 (no results)")
            return 0.0
        
        # Get top matches with their original similarity scores
        top_scores = [match['original_similarity'] for match in weighted_results[:3]]
        
        if not top_scores:
            return 0.0
        
        max_score = max(top_scores)
        avg_top_3 = sum(top_scores) / len(top_scores)
        
        vector_logger.debug(f"Agent {self.agent_id} similarity analysis:")
        vector_logger.debug(f"  Top 3 scores: {[f'{s:.3f}' for s in top_scores]}")
        vector_logger.debug(f"  Max score: {max_score:.3f}")
        vector_logger.debug(f"  Avg top 3: {avg_top_3:.3f}")
        
        # Calculate boost based on similarity thresholds
        boost = 0.0
        if max_score > 0.8 and avg_top_3 > 0.7:
            boost = 0.25  # High confidence boost
            vector_logger.debug(f"  Applied HIGH confidence boost: {boost}")
        elif max_score > 0.7 and avg_top_3 > 0.6:
            boost = 0.15  # Medium confidence boost
            vector_logger.debug(f"  Applied MEDIUM confidence boost: {boost}")
        elif max_score > 0.6:
            boost = 0.08  # Low confidence boost
            vector_logger.debug(f"  Applied LOW confidence boost: {boost}")
        else:
            vector_logger.debug(f"  No confidence boost applied (max={max_score:.3f})")
        
        return boost
    
    def _extract_evidence(self, weighted_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Extract evidence from vector search results with enhanced details"""
        if not weighted_results:
            return {}
        
        evidence = {
            'top_matches': [],
            'collection_distribution': {},
            'similarity_stats': {},
            'classification_hints': []
        }
        
        # Top matches for transparency
        for match in weighted_results[:3]:
            evidence['top_matches'].append({
                'collection': match.get('collection'),
                'similarity': match.get('original_similarity', 0),
                'weighted_score': match.get('weighted_score', 0),
                'classification': match.get('metadata', {}).get('user_document_type', 
                                                               match.get('metadata', {}).get('document_type', 'unknown')),
                'source': match.get('metadata', {}).get('source_file', 'unknown')
            })
        
        # Collection distribution
        for match in weighted_results:
            collection = match.get('collection', 'unknown')
            evidence['collection_distribution'][collection] = evidence['collection_distribution'].get(collection, 0) + 1
        
        # Similarity statistics
        similarities = [match.get('original_similarity', 0) for match in weighted_results]
        if similarities:
            evidence['similarity_stats'] = {
                'max': max(similarities),
                'avg': sum(similarities) / len(similarities),
                'min': min(similarities),
                'count': len(similarities)
            }
        
        # Classification hints from top matches
        classifications = {}
        for match in weighted_results[:5]:
            metadata = match.get('metadata', {})
            classification = metadata.get('user_document_type') or metadata.get('document_type', 'unknown')
            if classification != 'unknown':
                classifications[classification] = classifications.get(classification, 0) + 1
        
        evidence['classification_hints'] = [
            {'classification': k, 'support_count': v} 
            for k, v in sorted(classifications.items(), key=lambda x: x[1], reverse=True)
        ]
        
        return evidence
    
    async def store_successful_pattern(self, classification_result: Dict[str, Any], 
                                     vector_evidence: Dict[str, Any]):
        """Store successful classification patterns in vector DB with enhanced logging"""
        if not self.auto_store_results or not self.vector_enabled:
            return
        
        vector_logger = logging.getLogger('vector_operations')
        
        try:
            # Only store high-confidence results
            confidence = classification_result.get('confidence', 0)
            if confidence < 0.7:
                vector_logger.debug(f"Agent {self.agent_id} skipping pattern storage - low confidence: {confidence:.3f}")
                return
            
            vector_logger.info(f"Agent {self.agent_id} storing successful pattern (confidence: {confidence:.3f})")
            
            # Create document for storage based on agent type
            from core.knowledgebase.document_models import BaseDocument
            
            # Store in appropriate collection based on agent type
            if self.agent_type == AgentType.SCHEMA_ANALYZER:
                collection = 'cpg_schemas'
                content = self._build_schema_storage_content(classification_result, vector_evidence)
            elif self.agent_type == AgentType.CONTENT_ANALYZER:
                collection = 'cpg_content'
                content = self._build_content_storage_content(classification_result, vector_evidence)
            elif self.agent_type == AgentType.DOMAIN_EXPERT:
                collection = 'cpg_domain_knowledge'
                content = self._build_domain_storage_content(classification_result, vector_evidence)
            else:
                collection = 'cpg_uploaded_files'
                content = self._build_general_storage_content(classification_result, vector_evidence)
            
            # Create document
            doc = BaseDocument(
                content=content,
                document_type=f"{self.agent_type.value}_pattern",
                metadata={
                    'agent_id': self.agent_id,
                    'agent_type': self.agent_type.value,
                    'classification': classification_result.get('classification'),
                    'confidence': confidence,
                    'pattern_type': 'successful_classification',
                    'timestamp': time.time()
                }
            )
            
            # Store in vector DB
            success = self.vector_db.add_documents(collection, [doc])
            if success:
                vector_logger.info(f"Agent {self.agent_id} successfully stored pattern in {collection} collection")
            else:
                vector_logger.warning(f"Agent {self.agent_id} failed to store pattern in {collection} collection")
            
        except Exception as e:
            vector_logger.error(f"Agent {self.agent_id} failed to store successful pattern: {e}")
    
    def _build_schema_storage_content(self, result: Dict[str, Any], evidence: Dict[str, Any]) -> str:
        """Build content for schema pattern storage"""
        return f"Schema pattern: {result.get('reasoning', '')}"
    
    def _build_content_storage_content(self, result: Dict[str, Any], evidence: Dict[str, Any]) -> str:
        """Build content for content pattern storage"""
        return f"Content pattern: {result.get('reasoning', '')}"
    
    def _build_domain_storage_content(self, result: Dict[str, Any], evidence: Dict[str, Any]) -> str:
        """Build content for domain knowledge storage"""
        return f"Domain pattern: {result.get('reasoning', '')}"
    
    def _build_general_storage_content(self, result: Dict[str, Any], evidence: Dict[str, Any]) -> str:
        """Build content for general pattern storage"""
        return f"Classification pattern: {result.get('reasoning', '')}"
    
    def get_vector_statistics(self) -> Dict[str, Any]:
        """Get vector operation statistics for this agent"""
        return {
            'agent_id': self.agent_id,
            'agent_type': self.agent_type.value,
            'vector_enabled': self.vector_enabled,
            'stats': self.vector_stats.copy()
        }


class SchemaAnalyzerAgent(VectorDBEnhancedAgent):
    """Analyzes file schema and structure patterns with vector DB enhancement - FIXED classification logic"""
    
    def __init__(self, agent_id: str, config: Dict[str, Any]):
        super().__init__(agent_id, AgentType.SCHEMA_ANALYZER, config)
        self.capabilities = [
            "column_analysis",
            "data_type_detection", 
            "schema_pattern_matching",
            "relationship_inference",
            "vector_schema_search"
        ]
    
    async def analyze(self, file_context: FileContext) -> AgentDecision:
        """Analyze file schema and structure with vector DB enhancement"""
        start_time = time.time()
        vector_logger = logging.getLogger('vector_operations')
        
        try:
            vector_logger.info(f"SchemaAnalyzer {self.agent_id} starting analysis")
            
            schema_features = self._extract_schema_features(file_context)
            vector_logger.debug(f"SchemaAnalyzer extracted features: {list(schema_features.keys())}")
            
            # Vector DB search for similar cpg_schemas
            vector_results = await self.search_vector_db(schema_features)
            
            # FIXED: Enhanced schema analysis with proper POS/syndicated detection
            classification_scores = self._analyze_schema_patterns(schema_features)
            vector_logger.debug(f"SchemaAnalyzer traditional scores: {classification_scores}")
            
            # Enhance with vector DB insights
            enhanced_scores = self._enhance_with_vector_insights(classification_scores, vector_results)
            vector_logger.debug(f"SchemaAnalyzer enhanced scores: {enhanced_scores}")
            
            # Get best classification
            best_classification = max(enhanced_scores.items(), key=lambda x: x[1])
            
            decision = AgentDecision(
                agent_id=self.agent_id,
                agent_type=self.agent_type,
                classification=best_classification[0],
                confidence=best_classification[1],
                reasoning=self._generate_enhanced_reasoning(schema_features, enhanced_scores, vector_results),
                evidence={
                    "schema_features": schema_features,
                    "classification_scores": classification_scores,
                    "enhanced_scores": enhanced_scores,
                    "vector_results": vector_results,
                    "column_analysis": self._analyze_columns(file_context)
                },
                processing_time=time.time() - start_time
            )
            
            self.update_performance_metrics(decision)
            
            # Store successful pattern
            if decision.confidence > 0.7:
                await self.store_successful_pattern(decision.__dict__, vector_results)
            
            vector_logger.info(f"SchemaAnalyzer completed: {decision.classification} ({decision.confidence:.3f})")
            return decision
            
        except Exception as e:
            self.logger.error(f"Schema analysis failed: {e}")
            vector_logger.error(f"SchemaAnalyzer analysis failed: {e}")
            return AgentDecision(
                agent_id=self.agent_id,
                agent_type=self.agent_type,
                classification="unknown",
                confidence=0.0,
                reasoning=f"Schema analysis error: {str(e)}",
                processing_time=time.time() - start_time
            )
    
    def _build_search_query(self, features: Dict[str, Any]) -> str:
        """Build schema-specific search query"""
        query_parts = []
        
        # Add column information
        columns = features.get('columns', [])
        if columns:
            query_parts.append(f"columns: {', '.join(columns[:10])}")
        
        # Add data types
        data_types = features.get('data_types', [])
        if data_types:
            unique_types = list(set(data_types))
            query_parts.append(f"data types: {', '.join(unique_types)}")
        
        # Add business column patterns
        business_cols = features.get('business_columns', {})
        for category, cols in business_cols.items():
            if cols:
                query_parts.append(f"{category} columns: {', '.join(cols[:3])}")
        
        # Add schema metadata
        query_parts.append(f"columns: {features.get('column_count', 0)}")
        query_parts.append(f"schema structure: {len(features.get('id_columns', []))} ID columns")
        
        return " | ".join(query_parts)
    
    def _enhance_with_vector_insights(self, traditional_scores: Dict[str, float], 
                                    vector_results: Dict[str, Any]) -> Dict[str, float]:
        """Enhance traditional classification scores with vector insights"""
        enhanced_scores = traditional_scores.copy()
        confidence_boost = vector_results.get('confidence_boost', 0.0)
        
        # Apply confidence boost to all scores
        for classification in enhanced_scores:
            enhanced_scores[classification] += confidence_boost
        
        # Apply specific boosts based on classification hints
        classification_hints = vector_results.get('evidence', {}).get('classification_hints', [])
        for hint in classification_hints:
            classification = hint.get('classification', '')
            support_count = hint.get('support_count', 0)
            
            if classification in enhanced_scores:
                # Boost based on support from similar files
                boost = min(support_count * 0.05, 0.15)  # Max 0.15 boost
                enhanced_scores[classification] += boost
        
        # Normalize to ensure scores don't exceed 1.0
        for classification in enhanced_scores:
            enhanced_scores[classification] = min(enhanced_scores[classification], 1.0)
        
        return enhanced_scores
    
    def _generate_enhanced_reasoning(self, features: Dict[str, Any], 
                                   scores: Dict[str, float], 
                                   vector_results: Dict[str, Any]) -> str:
        """Generate enhanced reasoning including vector insights"""
        traditional_reasoning = self._generate_schema_reasoning(features, scores)
        
        # Add vector insights
        vector_parts = []
        confidence_boost = vector_results.get('confidence_boost', 0.0)
        if confidence_boost > 0:
            vector_parts.append(f"Vector similarity boost: +{confidence_boost:.3f}")
        
        best_matches = vector_results.get('best_matches', [])
        if best_matches:
            top_match = best_matches[0]
            similarity = top_match.get('similarity_score', 0)
            collection = top_match.get('collection', 'unknown')
            vector_parts.append(f"Best match: {similarity:.3f} similarity from {collection}")
        
        classification_hints = vector_results.get('evidence', {}).get('classification_hints', [])
        if classification_hints:
            top_hint = classification_hints[0]
            vector_parts.append(f"Vector suggests: {top_hint['classification']} ({top_hint['support_count']} matches)")
        
        if vector_parts:
            return f"{traditional_reasoning} | Vector insights: {'; '.join(vector_parts)}"
        else:
            return traditional_reasoning
    
    def _extract_schema_features(self, file_context: FileContext) -> Dict[str, Any]:
        """Extract schema-related features"""
        schema = file_context.extracted_content.get('schema', {})
        
        return {
            "column_count": schema.get('column_count', 0),
            "row_count": schema.get('row_count', 0),
            "columns": schema.get('columns', []),
            "data_types": schema.get('data_types', []),
            "numeric_columns": self._count_numeric_columns(schema.get('data_types', [])),
            "date_columns": self._detect_date_columns(schema.get('columns', [])),
            "id_columns": self._detect_id_columns(schema.get('columns', [])),
            "business_columns": self._detect_business_columns(schema.get('columns', []))
        }
    
    def _count_numeric_columns(self, data_types: List[str]) -> int:
        """Count numeric columns"""
        numeric_types = ['int', 'float', 'number', 'numeric']
        return sum(1 for dtype in data_types if any(nt in str(dtype).lower() for nt in numeric_types))
    
    def _detect_date_columns(self, columns: List[str]) -> List[str]:
        """Detect potential date columns"""
        date_patterns = ['date', 'time', 'week', 'month', 'year', 'period', 'day']
        return [col for col in columns if any(pattern in col.lower() for pattern in date_patterns)]
    
    def _detect_id_columns(self, columns: List[str]) -> List[str]:
        """Detect ID/key columns"""
        id_patterns = ['id', 'key', 'code', 'upc', 'sku', 'gtin']
        return [col for col in columns if any(pattern in col.lower() for pattern in id_patterns)]
    
    def _detect_business_columns(self, columns: List[str]) -> Dict[str, List[str]]:
        """Detect business-specific columns"""
        business_patterns = {
            'product': ['product', 'item', 'brand', 'category'],
            'sales': ['sales', 'revenue', 'amount', 'quantity', 'units'],
            'location': ['store', 'market', 'region', 'geography'],
            'customer': ['customer', 'account', 'retailer', 'chain']
        }
        
        result = {}
        for category, patterns in business_patterns.items():
            result[category] = [col for col in columns 
                              if any(pattern in col.lower() for pattern in patterns)]
        return result
    
    def _analyze_schema_patterns(self, schema_features: Dict[str, Any]) -> Dict[str, float]:
        """FIXED: Analyze schema patterns with proper POS/syndicated detection"""
        scores = {}
        columns = [col.lower() for col in schema_features.get('columns', [])]
        
        # FIXED: Strong POS detection patterns
        pos_score = self._detect_pos_schema_patterns(columns, schema_features)
        
        # FIXED: Strong syndicated detection patterns  
        syndicated_score = self._detect_syndicated_schema_patterns(columns, schema_features)
        
        # FIXED: Product attribute with penalties for transaction/market data
        product_attribute_score = self._detect_product_attribute_patterns(columns, schema_features)
        
        # Calculate scores for all data types
        for data_type in CPGDomainKnowledge.CPG_DATA_TYPES:
            if data_type == 'pos':
                scores[data_type] = pos_score
            elif data_type == 'syndicated':
                scores[data_type] = syndicated_score
            elif data_type == 'product_attribute':
                scores[data_type] = product_attribute_score
            else:
                # Generic scoring for other types
                scores[data_type] = self._calculate_schema_score(data_type, schema_features)
        
        # FIXED: Apply mutual exclusion rules
        scores = self._apply_schema_mutual_exclusion(scores, columns)
        
        return scores
    
    def _detect_pos_schema_patterns(self, columns: List[str], features: Dict[str, Any]) -> float:
        """FIXED: Detect strong POS schema patterns"""
        score = 0.0
        
        # Strong POS indicators - if present, should score very high
        strong_pos_indicators = ['transaction_id', 'store_id', 'upc', 'barcode', 'receipt']
        strong_matches = sum(1 for col in columns 
                           if any(indicator in col for indicator in strong_pos_indicators))
        
        # If we have 2+ strong POS indicators, this is almost certainly POS
        if strong_matches >= 2:
            score = 0.90
        elif strong_matches >= 1:
            score = 0.70
        
        # Check for POS combination patterns
        has_transaction = any('transaction' in col for col in columns)
        has_store = any('store' in col for col in columns)  
        has_sales_data = any(term in col for col in columns for term in ['sales', 'amount', 'quantity', 'price'])
        has_product_id = any(term in col for col in columns for term in ['upc', 'sku', 'barcode', 'item_id'])
        
        # Transaction + store/product + sales = definite POS
        if has_transaction and (has_store or has_product_id) and has_sales_data:
            score = max(score, 0.85)
        
        # Store-level sales with product identifiers
        if has_store and has_product_id and has_sales_data:
            score = max(score, 0.75)
        
        # Additional POS indicators
        pos_indicators = ['checkout', 'register', 'scan', 'purchase', 'pos']
        pos_count = sum(1 for col in columns if any(ind in col for ind in pos_indicators))
        if pos_count > 0:
            score += pos_count * 0.1
        
        return min(score, 1.0)
    
    def _detect_syndicated_schema_patterns(self, columns: List[str], features: Dict[str, Any]) -> float:
        """FIXED: Detect strong syndicated schema patterns"""
        score = 0.0
        
        # Strong syndicated indicators
        syndicated_metrics = ['tdp', 'acv', 'velocity', 'distribution', 'market_share', 'units_per_million']
        metric_matches = sum(1 for col in columns 
                           if any(metric in col for metric in syndicated_metrics))
        
        # If we have syndicated metrics, very likely syndicated
        if metric_matches >= 2:
            score = 0.85
        elif metric_matches >= 1:
            score = 0.70
        
        # Check for syndicated patterns
        has_market = any('market' in col for col in columns)
        has_category = any('category' in col for col in columns)
        has_brand = any('brand' in col for col in columns)
        has_period = any(term in col for col in columns for term in ['week', 'month', 'period', 'date'])
        
        # Market + category + brand + period = typical syndicated structure
        syndicated_structure_count = sum([has_market, has_category, has_brand, has_period])
        if syndicated_structure_count >= 3:
            score = max(score, 0.75)
        elif syndicated_structure_count >= 2:
            score = max(score, 0.50)
        
        # Additional syndicated indicators
        syndicated_terms = ['nielsen', 'iri', 'symphony', 'retail_measurement', 'scanner']
        for col in columns:
            for term in syndicated_terms:
                if term in col:
                    score += 0.15
        
        return min(score, 1.0)
    
    def _detect_product_attribute_patterns(self, columns: List[str], features: Dict[str, Any]) -> float:
        """FIXED: Detect product attribute patterns with penalties for transactional data"""
        score = 0.0
        
        # Check for product hierarchy indicators
        hierarchy_indicators = ['product_id', 'brand', 'category', 'sub_category', 'segment', 'description']
        hierarchy_matches = sum(1 for col in columns 
                              if any(ind in col for ind in hierarchy_indicators))
        
        # Product master data indicators
        master_data_indicators = ['hierarchy', 'master', 'catalog', 'attribute', 'specification']
        master_matches = sum(1 for col in columns 
                           if any(ind in col for ind in master_data_indicators))
        
        # Base score for product attributes
        if hierarchy_matches >= 3:
            score = 0.60
        elif hierarchy_matches >= 2:
            score = 0.40
        elif hierarchy_matches >= 1:
            score = 0.30
        
        if master_matches > 0:
            score += master_matches * 0.15
        
        # CRITICAL FIX: Heavy penalties for transaction/market data
        
        # Penalty for transaction indicators (should NOT be in product_attribute)
        transaction_indicators = ['transaction_id', 'store_id', 'sale_date', 'checkout', 'receipt', 'purchase']
        transaction_count = sum(1 for col in columns 
                              if any(ind in col for ind in transaction_indicators))
        
        if transaction_count >= 1:
            score = max(0.0, score - 0.8)  # Heavy penalty for transaction data
        
        # Penalty for market metrics (should NOT be in product_attribute)  
        market_metrics = ['tdp', 'acv', 'velocity', 'market_share', 'distribution']
        market_count = sum(1 for col in columns 
                         if any(metric in col for metric in market_metrics))
        
        if market_count >= 1:
            score = max(0.0, score - 0.7)  # Heavy penalty for market metrics
        
        # Penalty for sales transaction data
        sales_transaction_indicators = ['sales_amount', 'quantity_sold', 'units_sold', 'revenue']
        sales_count = sum(1 for col in columns 
                        if any(ind in col for ind in sales_transaction_indicators))
        
        if sales_count >= 1:
            score = max(0.0, score - 0.6)  # Penalty for sales transaction data
        
        return max(0.0, min(score, 1.0))
    
    def _apply_schema_mutual_exclusion(self, scores: Dict[str, float], columns: List[str]) -> Dict[str, float]:
        """FIXED: Apply mutual exclusion rules between data types"""
        
        # If POS score is high, significantly reduce product_attribute
        if scores.get('pos', 0) > 0.7:
            scores['product_attribute'] = max(0.0, scores['product_attribute'] - 0.7)
        
        # If syndicated score is high, significantly reduce product_attribute
        if scores.get('syndicated', 0) > 0.7:
            scores['product_attribute'] = max(0.0, scores['product_attribute'] - 0.7)
        
        # If clear transaction data exists, product_attribute should be very low
        has_transaction_data = any(ind in col for col in columns 
                                 for ind in ['transaction_id', 'store_id', 'checkout', 'receipt'])
        if has_transaction_data:
            scores['product_attribute'] = min(scores['product_attribute'], 0.15)
        
        # If clear market metrics exist, product_attribute should be very low
        has_market_metrics = any(metric in col for col in columns 
                               for metric in ['tdp', 'acv', 'velocity', 'market_share'])
        if has_market_metrics:
            scores['product_attribute'] = min(scores['product_attribute'], 0.15)
        
        return scores
    
    def _calculate_schema_score(self, data_type: str, features: Dict[str, Any]) -> float:
        """Calculate schema score for other data types"""
        score = 0.0
        
        # Column count heuristics
        col_count = features.get('column_count', 0)
        if data_type == 'depletion_data' and col_count >= 6:
            score += 0.2
        elif data_type == 'margin_data' and col_count >= 5:
            score += 0.2
        
        # Business column analysis
        business_cols = features.get('business_columns', {})
        if data_type == 'depletion_data':
            if business_cols.get('location') and business_cols.get('product'):
                score += 0.3
        elif data_type == 'margin_data':
            if business_cols.get('product') and business_cols.get('sales'):
                score += 0.3
        
        # ID column patterns
        id_cols = features.get('id_columns', [])
        if data_type in ['depletion_data', 'margin_data'] and id_cols:
            score += 0.2
        
        # Date column patterns
        date_cols = features.get('date_columns', [])
        if date_cols:
            score += 0.1
        
        return min(score, 1.0)
    
    def _analyze_columns(self, file_context: FileContext) -> Dict[str, Any]:
        """Detailed column analysis"""
        schema = file_context.extracted_content.get('schema', {})
        columns = schema.get('columns', [])
        
        analysis = {
            "total_columns": len(columns),
            "column_categories": {},
            "naming_patterns": self._analyze_naming_patterns(columns),
            "potential_keys": self._identify_potential_keys(columns)
        }
        
        return analysis
    
    def _analyze_naming_patterns(self, columns: List[str]) -> Dict[str, Any]:
        """Analyze column naming patterns"""
        patterns = {
            "snake_case": sum(1 for col in columns if '_' in col),
            "camel_case": sum(1 for col in columns if re.search(r'[a-z][A-Z]', col)),
            "contains_numbers": sum(1 for col in columns if any(char.isdigit() for char in col)),
            "all_caps": sum(1 for col in columns if col.isupper()),
            "mixed_case": sum(1 for col in columns if col != col.lower() and col != col.upper())
        }
        return patterns
    
    def _identify_potential_keys(self, columns: List[str]) -> List[str]:
        """Identify potential key columns"""
        key_indicators = ['id', 'key', 'code', 'number', 'upc', 'sku', 'gtin']
        return [col for col in columns if any(indicator in col.lower() for indicator in key_indicators)]
    
    def _generate_schema_reasoning(self, features: Dict[str, Any], scores: Dict[str, float]) -> str:
        """Generate human-readable reasoning for schema classification"""
        top_scores = sorted(scores.items(), key=lambda x: x[1], reverse=True)[:3]
        
        reasoning_parts = [
            f"Schema analysis: {features.get('column_count', 0)} columns, {features.get('row_count', 0)} rows"
        ]
        
        if features.get('business_columns'):
            bus_cols = features['business_columns']
            for category, cols in bus_cols.items():
                if cols:
                    reasoning_parts.append(f"{category} columns detected: {len(cols)}")
        
        reasoning_parts.append(f"Top classifications: {', '.join([f'{t}({s:.2f})' for t, s in top_scores])}")
        
        return "; ".join(reasoning_parts)
    
    def get_capabilities(self) -> List[str]:
        return self.capabilities


class ContentAnalyzerAgent(VectorDBEnhancedAgent):
    """Analyzes file content and textual patterns with vector DB enhancement - FIXED classification logic"""
    
    def __init__(self, agent_id: str, config: Dict[str, Any]):
        super().__init__(agent_id, AgentType.CONTENT_ANALYZER, config)
        self.capabilities = [
            "text_analysis",
            "keyword_extraction",
            "business_term_detection",
            "content_classification",
            "semantic_content_search"
        ]
        self.tfidf_vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')
    
    async def analyze(self, file_context: FileContext) -> AgentDecision:
        """Analyze file content and textual patterns with vector enhancement"""
        start_time = time.time()
        vector_logger = logging.getLogger('vector_operations')
        
        try:
            vector_logger.info(f"ContentAnalyzer {self.agent_id} starting analysis")
            
            content_features = self._extract_content_features(file_context)
            vector_logger.debug(f"ContentAnalyzer extracted features: {list(content_features.keys())}")
            
            # Vector DB search for similar content
            vector_results = await self.search_vector_db(content_features)
            
            # FIXED: Enhanced content analysis with proper weighting
            classification_scores = self._analyze_content_patterns(content_features)
            
            # Enhance with vector insights
            enhanced_scores = self._enhance_with_vector_insights(classification_scores, vector_results)
            
            best_classification = max(enhanced_scores.items(), key=lambda x: x[1])
            
            decision = AgentDecision(
                agent_id=self.agent_id,
                agent_type=self.agent_type,
                classification=best_classification[0],
                confidence=best_classification[1],
                reasoning=self._generate_enhanced_reasoning(content_features, enhanced_scores, vector_results),
                evidence={
                    "content_features": content_features,
                    "classification_scores": classification_scores,
                    "enhanced_scores": enhanced_scores,
                    "vector_results": vector_results,
                    "keywords": self._extract_keywords(file_context)
                },
                processing_time=time.time() - start_time
            )
            
            self.update_performance_metrics(decision)
            
            # Store successful pattern
            if decision.confidence > 0.7:
                await self.store_successful_pattern(decision.__dict__, vector_results)
            
            vector_logger.info(f"ContentAnalyzer completed: {decision.classification} ({decision.confidence:.3f})")
            return decision
            
        except Exception as e:
            self.logger.error(f"Content analysis failed: {e}")
            vector_logger.error(f"ContentAnalyzer analysis failed: {e}")
            return AgentDecision(
                agent_id=self.agent_id,
                agent_type=self.agent_type,
                classification="unknown",
                confidence=0.0,
                reasoning=f"Content analysis error: {str(e)}",
                processing_time=time.time() - start_time
            )
    
    def _build_search_query(self, features: Dict[str, Any]) -> str:
        """Build content-specific search query"""
        query_parts = []
        
        # Add business terms
        business_terms = features.get('business_terms', {})
        for category, count in business_terms.items():
            if count > 0:
                query_parts.append(f"{category} terms: {count}")
        
        # Add CPG indicators
        cpg_indicators = features.get('cpg_indicators', {})
        for category, count in cpg_indicators.items():
            if count > 0:
                query_parts.append(f"{category}: {count}")
        
        # Add content summary
        content_length = features.get('content_length', 0)
        word_count = features.get('word_count', 0)
        query_parts.append(f"content: {content_length} chars, {word_count} words")
        
        # Add data quality indicators
        quality_indicators = features.get('data_quality_indicators', {})
        completeness = quality_indicators.get('completeness_score', 0)
        query_parts.append(f"completeness: {completeness:.2f}")
        
        return " | ".join(query_parts)
    
    def _enhance_with_vector_insights(self, traditional_scores: Dict[str, float], 
                                    vector_results: Dict[str, Any]) -> Dict[str, float]:
        """Enhance traditional scores with vector insights"""
        enhanced_scores = traditional_scores.copy()
        confidence_boost = vector_results.get('confidence_boost', 0.0)
        
        # Apply confidence boost
        for classification in enhanced_scores:
            enhanced_scores[classification] += confidence_boost
        
        # Apply classification hints
        classification_hints = vector_results.get('evidence', {}).get('classification_hints', [])
        for hint in classification_hints:
            classification = hint.get('classification', '')
            support_count = hint.get('support_count', 0)
            
            if classification in enhanced_scores:
                boost = min(support_count * 0.04, 0.12)  # Max 0.12 boost
                enhanced_scores[classification] += boost
        
        # Normalize scores
        for classification in enhanced_scores:
            enhanced_scores[classification] = min(enhanced_scores[classification], 1.0)
        
        return enhanced_scores
    
    def _generate_enhanced_reasoning(self, features: Dict[str, Any], 
                                   scores: Dict[str, float], 
                                   vector_results: Dict[str, Any]) -> str:
        """Generate enhanced reasoning with vector insights"""
        traditional_reasoning = self._generate_content_reasoning(features, scores)
        
        # Add vector insights
        vector_parts = []
        confidence_boost = vector_results.get('confidence_boost', 0.0)
        if confidence_boost > 0:
            vector_parts.append(f"Semantic similarity boost: +{confidence_boost:.3f}")
        
        best_matches = vector_results.get('best_matches', [])
        if best_matches:
            semantic_matches = [m for m in best_matches if m.get('collection') in ['content', 'cpg_uploaded_files']]
            if semantic_matches:
                top_match = semantic_matches[0]
                vector_parts.append(f"Similar content: {top_match.get('similarity_score', 0):.3f} similarity")
        
        if vector_parts:
            return f"{traditional_reasoning} | Semantic insights: {'; '.join(vector_parts)}"
        else:
            return traditional_reasoning
    
    def _extract_content_features(self, file_context: FileContext) -> Dict[str, Any]:
        """Extract content-related features"""
        content = file_context.extracted_content.get('content', '')
        
        return {
            "content_length": len(content),
            "word_count": len(content.split()),
            "business_terms": self._detect_business_terms(content),
            "numeric_patterns": self._detect_numeric_patterns(content),
            "cpg_indicators": self._detect_cpg_indicators(content),
            "data_quality_indicators": self._detect_data_quality_indicators(content)
        }
    
    def _detect_business_terms(self, content: str) -> Dict[str, int]:
        """FIXED: Detect business terms with proper weighting"""
        business_terms = {
            'syndicated': ['nielsen', 'iri', 'syndicated', 'market research', 'acv', 'tdp', 'velocity', 'distribution'],
            'pos': ['point of sale', 'pos', 'transaction', 'checkout', 'register', 'upc', 'barcode', 'store_id'],
            'product': ['product', 'brand', 'category', 'sku', 'upc', 'item'],
            'sales': ['sales', 'revenue', 'units', 'volume', 'quantity'],
            'customer': ['customer', 'retailer', 'account', 'chain', 'banner'],
            'geography': ['market', 'region', 'territory', 'store', 'location']
        }
        
        content_lower = content.lower()
        term_counts = {}
        
        for category, terms in business_terms.items():
            count = sum(content_lower.count(term) for term in terms)
            term_counts[category] = count
        
        return term_counts
    
    def _detect_numeric_patterns(self, content: str) -> Dict[str, Any]:
        """Detect numeric patterns in content"""
        patterns = {
            'currency': len(re.findall(r'\$[\d,]+\.?\d*', content)),
            'percentages': len(re.findall(r'\d+\.?\d*%', content)),
            'decimals': len(re.findall(r'\d+\.\d+', content)),
            'large_numbers': len(re.findall(r'\d{4,}', content)),
            'dates': len(re.findall(r'\d{1,2}[/-]\d{1,2}[/-]\d{2,4}', content))
        }
        return patterns
    
    def _detect_cpg_indicators(self, content: str) -> Dict[str, int]:
        """Detect CPG-specific indicators"""
        cpg_terms = {
            'cpg_general': ['consumer packaged goods', 'cpg', 'fmcg', 'retail'],
            'measurement': ['units', 'cases', 'volume', 'weight', 'size'],
            'distribution': ['distribution', 'velocity', 'turns', 'inventory'],
            'promotion': ['promotion', 'promo', 'discount', 'feature', 'display'],
            'margin': ['margin', 'cost', 'profit', 'pricing', 'rebate']
        }
        
        content_lower = content.lower()
        indicator_counts = {}
        
        for category, terms in cpg_terms.items():
            count = sum(content_lower.count(term) for term in terms)
            indicator_counts[category] = count
        
        return indicator_counts
    
    def _detect_data_quality_indicators(self, content: str) -> Dict[str, Any]:
        """Detect data quality indicators"""
        return {
            'has_nulls': 'null' in content.lower() or 'na' in content.lower(),
            'has_duplicates': 'duplicate' in content.lower(),
            'has_errors': 'error' in content.lower() or 'invalid' in content.lower(),
            'completeness_score': self._calculate_completeness_score(content)
        }
    
    def _calculate_completeness_score(self, content: str) -> float:
        """Calculate a simple completeness score"""
        if not content:
            return 0.0
        
        # Simple heuristic based on content richness
        words = content.split()
        unique_words = set(words)
        
        if len(words) == 0:
            return 0.0
        
        diversity_score = len(unique_words) / len(words)
        length_score = min(len(content) / 1000, 1.0)  # Normalize to 1000 chars
        
        return (diversity_score + length_score) / 2
    
    def _analyze_content_patterns(self, content_features: Dict[str, Any]) -> Dict[str, float]:
        """FIXED: Analyze content patterns with proper weighting and penalties"""
        scores = {}
        
        for data_type in CPGDomainKnowledge.CPG_DATA_TYPES:
            score = self._calculate_content_score(data_type, content_features)
            scores[data_type] = score
        
        # FIXED: Apply content-based mutual exclusion
        scores = self._apply_content_mutual_exclusion(scores, content_features)
        
        return scores
    
    def _calculate_content_score(self, data_type: str, features: Dict[str, Any]) -> float:
        """FIXED: Calculate content score with stronger weights and penalties"""
        score = 0.0
        business_terms = features.get('business_terms', {})
        cpg_indicators = features.get('cpg_indicators', {})
        
        # FIXED: Much stronger weights for clear indicators
        if data_type == 'syndicated':
            # Strong boost for syndicated terms
            score += business_terms.get('syndicated', 0) * 0.6  # INCREASED from 0.1
            score += cpg_indicators.get('measurement', 0) * 0.3  # INCREASED from 0.05
            score += cpg_indicators.get('distribution', 0) * 0.4  # NEW
            
        elif data_type == 'pos':
            # Strong boost for POS terms
            score += business_terms.get('pos', 0) * 0.6  # INCREASED from 0.1
            score += business_terms.get('sales', 0) * 0.3  # INCREASED from 0.05
            
            # Additional POS content indicators
            if business_terms.get('pos', 0) > 0 and business_terms.get('sales', 0) > 0:
                score += 0.3  # Combination bonus
                
        elif data_type == 'product_attribute':
            # Base score for product terms
            score += business_terms.get('product', 0) * 0.4  # INCREASED from 0.1
            
            # CRITICAL FIX: Heavy penalties for transaction/market content
            if business_terms.get('pos', 0) > 0:
                score = max(0.0, score - 0.7)  # Heavy penalty for POS terms
            
            if business_terms.get('syndicated', 0) > 0:
                score = max(0.0, score - 0.6)  # Heavy penalty for syndicated terms
            
            # Penalty for sales transaction indicators
            if business_terms.get('sales', 0) > 2:  # Multiple sales mentions
                score = max(0.0, score - 0.4)
                
        elif data_type == 'margin_data':
            score += cpg_indicators.get('margin', 0) * 0.5  # INCREASED from 0.1
        
        # General CPG indicators (lighter weight)
        score += cpg_indicators.get('cpg_general', 0) * 0.1  # INCREASED from 0.02
        
        return min(score, 1.0)
    
    def _apply_content_mutual_exclusion(self, scores: Dict[str, float], features: Dict[str, Any]) -> Dict[str, float]:
        """FIXED: Apply mutual exclusion based on content analysis"""
        business_terms = features.get('business_terms', {})
        
        # If strong POS content indicators, reduce product_attribute
        if business_terms.get('pos', 0) >= 2 or business_terms.get('sales', 0) >= 3:
            scores['product_attribute'] = max(0.0, scores['product_attribute'] - 0.6)
        
        # If strong syndicated content indicators, reduce product_attribute  
        if business_terms.get('syndicated', 0) >= 2:
            scores['product_attribute'] = max(0.0, scores['product_attribute'] - 0.6)
        
        # If POS score is high, reduce product_attribute significantly
        if scores.get('pos', 0) > 0.5:
            scores['product_attribute'] = max(0.0, scores['product_attribute'] - 0.5)
        
        # If syndicated score is high, reduce product_attribute significantly
        if scores.get('syndicated', 0) > 0.5:
            scores['product_attribute'] = max(0.0, scores['product_attribute'] - 0.5)
        
        return scores
    
    def _extract_keywords(self, file_context: FileContext) -> List[str]:
        """Extract key terms from content"""
        content = file_context.extracted_content.get('content', '')
        
        # Simple keyword extraction using frequency
        words = re.findall(r'\b[a-zA-Z]{3,}\b', content.lower())
        word_freq = {}
        
        for word in words:
            word_freq[word] = word_freq.get(word, 0) + 1
        
        # Return top 10 most frequent words
        return sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:10]
    
    def _generate_content_reasoning(self, features: Dict[str, Any], scores: Dict[str, float]) -> str:
        """Generate reasoning for content classification"""
        top_scores = sorted(scores.items(), key=lambda x: x[1], reverse=True)[:3]
        
        reasoning_parts = [
            f"Content analysis: {features.get('word_count', 0)} words"
        ]
        
        business_terms = features.get('business_terms', {})
        top_terms = sorted(business_terms.items(), key=lambda x: x[1], reverse=True)[:3]
        if top_terms and top_terms[0][1] > 0:
            reasoning_parts.append(f"Business terms: {', '.join([f'{t}({c})' for t, c in top_terms if c > 0])}")
        
        reasoning_parts.append(f"Top classifications: {', '.join([f'{t}({s:.2f})' for t, s in top_scores])}")
        
        return "; ".join(reasoning_parts)
    
    def get_capabilities(self) -> List[str]:
        return self.capabilities


class MLClassifierAgent(VectorDBEnhancedAgent):
    """Machine Learning based classification agent with vector DB enhancement - FIXED classification logic"""
    
    def __init__(self, agent_id: str, config: Dict[str, Any]):
        super().__init__(agent_id, AgentType.ML_CLASSIFIER, config)
        self.capabilities = [
            "ensemble_classification",
            "neural_network_analysis",
            "feature_engineering",
            "model_confidence_scoring",
            "vector_enhanced_ml"
        ]
        self.models = self._initialize_models()
        self.feature_vectorizer = TfidfVectorizer(max_features=500)
        self.is_trained = False
    
    def _initialize_models(self) -> Dict[str, Any]:
        """Initialize ML models"""
        return {
            'random_forest': RandomForestClassifier(n_estimators=100, random_state=42),
            'svm': SVC(probability=True, random_state=42),
            'naive_bayes': MultinomialNB(),
            'neural_network': MLPClassifier(hidden_layer_sizes=(100, 50), random_state=42, max_iter=500),
            'ensemble': None  # Will be created after individual models
        }
    
    async def analyze(self, file_context: FileContext) -> AgentDecision:
        """Perform ML-based classification with vector enhancement"""
        start_time = time.time()
        vector_logger = logging.getLogger('vector_operations')
        
        try:
            vector_logger.info(f"MLClassifier {self.agent_id} starting analysis")
            
            # Extract features for ML models
            ml_features = self._extract_ml_features(file_context)
            
            # Vector DB search for similar ML cases
            vector_results = await self.search_vector_db(ml_features)
            
            # FIXED: Enhanced rule-based classification with pattern detection
            if not self.is_trained:
                classification_scores = self._rule_based_classification(ml_features)
            else:
                classification_scores = self._ml_classification(ml_features)
            
            # Enhance with vector insights
            enhanced_scores = self._enhance_with_vector_insights(classification_scores, vector_results)
            
            best_classification = max(enhanced_scores.items(), key=lambda x: x[1])
            
            decision = AgentDecision(
                agent_id=self.agent_id,
                agent_type=self.agent_type,
                classification=best_classification[0],
                confidence=best_classification[1],
                reasoning=self._generate_enhanced_reasoning(ml_features, enhanced_scores, vector_results),
                evidence={
                    "ml_features": ml_features,
                    "classification_scores": classification_scores,
                    "enhanced_scores": enhanced_scores,
                    "vector_results": vector_results,
                    "model_predictions": self._get_individual_predictions(ml_features) if self.is_trained else {}
                },
                processing_time=time.time() - start_time
            )
            
            self.update_performance_metrics(decision)
            
            # Store successful pattern for future ML training
            if decision.confidence > 0.7:
                await self.store_successful_pattern(decision.__dict__, vector_results)
            
            vector_logger.info(f"MLClassifier completed: {decision.classification} ({decision.confidence:.3f})")
            return decision
            
        except Exception as e:
            self.logger.error(f"ML classification failed: {e}")
            vector_logger.error(f"MLClassifier analysis failed: {e}")
            return AgentDecision(
                agent_id=self.agent_id,
                agent_type=self.agent_type,
                classification="unknown",
                confidence=0.0,
                reasoning=f"ML classification error: {str(e)}",
                processing_time=time.time() - start_time
            )
    
    def _build_search_query(self, features: Dict[str, Any]) -> str:
        """Build ML-specific search query"""
        query_parts = []
        numerical = features.get('numerical', {})
        categorical = features.get('categorical', {})
        text = features.get('text', {})
        
        # Add numerical features
        query_parts.append(f"columns: {numerical.get('column_count', 0)}")
        query_parts.append(f"numeric ratio: {numerical.get('numeric_column_ratio', 0):.2f}")
        query_parts.append(f"avg column length: {numerical.get('avg_column_name_length', 0):.1f}")
        
        # Add categorical features
        query_parts.append(f"file type: {categorical.get('file_type', 'unknown')}")
        query_parts.append(f"data type: {categorical.get('dominant_data_type', 'unknown')}")
        query_parts.append(f"naming: {categorical.get('naming_convention', 'unknown')}")
        
        # Add feature flags
        if numerical.get('has_id_columns'):
            query_parts.append("has ID columns")
        if numerical.get('has_date_columns'):
            query_parts.append("has date columns")
        
        # Add business term density
        business_density = numerical.get('business_term_density', 0)
        if business_density > 0:
            query_parts.append(f"business terms: {business_density:.3f}")
        
        return " | ".join(query_parts)
    
    def _enhance_with_vector_insights(self, traditional_scores: Dict[str, float], 
                                    vector_results: Dict[str, Any]) -> Dict[str, float]:
        """Enhance ML scores with vector insights"""
        enhanced_scores = traditional_scores.copy()
        confidence_boost = vector_results.get('confidence_boost', 0.0)
        
        # Apply confidence boost with ML-specific weighting
        for classification in enhanced_scores:
            enhanced_scores[classification] += confidence_boost * 0.8  # Slightly lower for ML
        
        # Special boost from cpg_learning_feedback collection (corrected ML predictions)
        collection_results = vector_results.get('collection_results', {})
        learning_matches = collection_results.get('cpg_learning_feedback', {}).get('matches', [])
        
        for match in learning_matches:
            correct_class = match.get('metadata', {}).get('correct_classification', '')
            similarity = match.get('similarity_score', 0)
            
            if correct_class in enhanced_scores and similarity > 0.7:
                enhanced_scores[correct_class] += 0.1  # Boost from learning feedback
        
        # Normalize scores
        for classification in enhanced_scores:
            enhanced_scores[classification] = min(enhanced_scores[classification], 1.0)
        
        return enhanced_scores
    
    def _generate_enhanced_reasoning(self, features: Dict[str, Any], 
                                   scores: Dict[str, float], 
                                   vector_results: Dict[str, Any]) -> str:
        """Generate enhanced reasoning with ML and vector insights"""
        traditional_reasoning = self._generate_ml_reasoning(features, scores)
        
        # Add vector insights
        vector_parts = []
        confidence_boost = vector_results.get('confidence_boost', 0.0)
        if confidence_boost > 0:
            vector_parts.append(f"ML pattern similarity: +{confidence_boost:.3f}")
        
        # Check for learning feedback matches
        collection_results = vector_results.get('collection_results', {})
        learning_matches = collection_results.get('cpg_learning_feedback', {}).get('count', 0)
        if learning_matches > 0:
            vector_parts.append(f"Learning history: {learning_matches} similar cases")
        
        if vector_parts:
            return f"{traditional_reasoning} | Vector ML insights: {'; '.join(vector_parts)}"
        else:
            return traditional_reasoning
    
    def _extract_ml_features(self, file_context: FileContext) -> Dict[str, Any]:
        """Extract features suitable for ML models"""
        schema = file_context.extracted_content.get('schema', {})
        content = file_context.extracted_content.get('content', '')
        
        # Numerical features
        numerical_features = {
            'column_count': schema.get('column_count', 0),
            'row_count': min(schema.get('row_count', 0), 1000000),  # Cap for normalization
            'content_length': len(content),
            'numeric_column_ratio': self._calculate_numeric_ratio(schema.get('data_types', [])),
            'avg_column_name_length': self._calculate_avg_column_length(schema.get('columns', [])),
            'has_id_columns': int(self._has_id_columns(schema.get('columns', []))),
            'has_date_columns': int(self._has_date_columns(schema.get('columns', []))),
            'business_term_density': self._calculate_business_term_density(content)
        }
        
        # Categorical features (encoded)
        categorical_features = {
            'file_type': file_context.file_type,
            'dominant_data_type': self._get_dominant_data_type(schema.get('data_types', [])),
            'naming_convention': self._detect_naming_convention(schema.get('columns', []))
        }
        
        # Text features
        text_features = {
            'content_text': content,
            'column_names_text': ' '.join(schema.get('columns', [])),
            'combined_text': f"{content} {' '.join(schema.get('columns', []))}"
        }
        
        return {
            'numerical': numerical_features,
            'categorical': categorical_features,
            'text': text_features
        }
    
    def _calculate_numeric_ratio(self, data_types: List[str]) -> float:
        """Calculate ratio of numeric columns"""
        if not data_types:
            return 0.0
        
        numeric_types = ['int', 'float', 'number', 'numeric']
        numeric_count = sum(1 for dtype in data_types 
                          if any(nt in str(dtype).lower() for nt in numeric_types))
        
        return numeric_count / len(data_types)
    
    def _calculate_avg_column_length(self, columns: List[str]) -> float:
        """Calculate average column name length"""
        if not columns:
            return 0.0
        return sum(len(col) for col in columns) / len(columns)
    
    def _has_id_columns(self, columns: List[str]) -> bool:
        """Check if file has ID columns"""
        id_patterns = ['id', 'key', 'code', 'upc', 'sku']
        return any(pattern in col.lower() for col in columns for pattern in id_patterns)
    
    def _has_date_columns(self, columns: List[str]) -> bool:
        """Check if file has date columns"""
        date_patterns = ['date', 'time', 'week', 'month', 'year']
        return any(pattern in col.lower() for col in columns for pattern in date_patterns)
    
    def _calculate_business_term_density(self, content: str) -> float:
        """Calculate density of business terms in content"""
        if not content:
            return 0.0
        
        business_terms = ['sales', 'product', 'customer', 'store', 'market', 'brand', 'category']
        content_lower = content.lower()
        term_count = sum(content_lower.count(term) for term in business_terms)
        
        return term_count / len(content.split()) if content.split() else 0.0
    
    def _get_dominant_data_type(self, data_types: List[str]) -> str:
        """Get dominant data type in schema"""
        if not data_types:
            return 'unknown'
        
        type_counts = {}
        for dtype in data_types:
            simplified_type = str(dtype).lower()
            if 'int' in simplified_type or 'float' in simplified_type:
                type_counts['numeric'] = type_counts.get('numeric', 0) + 1
            elif 'str' in simplified_type or 'object' in simplified_type:
                type_counts['text'] = type_counts.get('text', 0) + 1
            else:
                type_counts['other'] = type_counts.get('other', 0) + 1
        
        return max(type_counts.items(), key=lambda x: x[1])[0] if type_counts else 'unknown'
    
    def _detect_naming_convention(self, columns: List[str]) -> str:
        """Detect column naming convention"""
        if not columns:
            return 'unknown'
        
        snake_case = sum(1 for col in columns if '_' in col)
        camel_case = sum(1 for col in columns if re.search(r'[a-z][A-Z]', col))
        
        if snake_case > len(columns) * 0.5:
            return 'snake_case'
        elif camel_case > len(columns) * 0.5:
            return 'camel_case'
        else:
            return 'mixed'
    
    def _rule_based_classification(self, ml_features: Dict[str, Any]) -> Dict[str, float]:
        """FIXED: Enhanced rule-based classification with pattern detection"""
        scores = {}
        numerical = ml_features['numerical']
        text = ml_features['text']
        columns_text = text['column_names_text'].lower()
        content_text = text['content_text'].lower()
        combined_text = text['combined_text'].lower()
        
        # FIXED: Strong pattern detection for each data type
        for data_type in CPGDomainKnowledge.CPG_DATA_TYPES:
            score = 0.0
            
            if data_type == 'pos':
                score = self._detect_ml_pos_patterns(numerical, columns_text, content_text)
            elif data_type == 'syndicated':
                score = self._detect_ml_syndicated_patterns(numerical, columns_text, content_text)
            elif data_type == 'product_attribute':
                score = self._detect_ml_product_patterns(numerical, columns_text, content_text)
            else:
                # Generic heuristics for other types
                score = self._calculate_generic_ml_score(data_type, numerical, combined_text)
            
            scores[data_type] = min(score, 1.0)
        
        # FIXED: Apply ML-based mutual exclusion
        scores = self._apply_ml_mutual_exclusion(scores, columns_text, content_text)
        
        return scores
    
    def _detect_ml_pos_patterns(self, numerical: Dict[str, Any], columns_text: str, content_text: str) -> float:
        """FIXED: Detect POS patterns using ML features"""
        score = 0.0
        
        # Strong POS column indicators
        pos_column_indicators = ['transaction_id', 'store_id', 'upc', 'barcode', 'checkout', 'receipt']
        pos_column_matches = sum(1 for indicator in pos_column_indicators if indicator in columns_text)
        
        if pos_column_matches >= 2:
            score = 0.90  # Very strong POS signal
        elif pos_column_matches >= 1:
            score = 0.70
        
        # Transaction + sales combination
        has_transaction = 'transaction' in columns_text
        has_sales_cols = any(term in columns_text for term in ['sales', 'amount', 'quantity', 'price'])
        has_store = 'store' in columns_text
        
        if has_transaction and has_sales_cols:
            score = max(score, 0.85)
        elif has_store and has_sales_cols and numerical.get('has_id_columns'):
            score = max(score, 0.70)
        
        # Content analysis for POS
        pos_content_terms = ['point of sale', 'pos system', 'checkout', 'register', 'scan']
        pos_content_matches = sum(1 for term in pos_content_terms if term in content_text)
        if pos_content_matches > 0:
            score += pos_content_matches * 0.1
        
        # Column structure analysis
        col_count = numerical.get('column_count', 0)
        if 5 <= col_count <= 15 and numerical.get('has_id_columns') and numerical.get('has_date_columns'):
            score += 0.2  # Typical POS structure
        
        return min(score, 1.0)
    
    def _detect_ml_syndicated_patterns(self, numerical: Dict[str, Any], columns_text: str, content_text: str) -> float:
        """FIXED: Detect syndicated patterns using ML features"""
        score = 0.0
        
        # Strong syndicated column indicators
        syndicated_metrics = ['tdp', 'acv', 'velocity', 'distribution', 'market_share', 'units_per_million']
        syndicated_matches = sum(1 for metric in syndicated_metrics if metric in columns_text)
        
        if syndicated_matches >= 2:
            score = 0.85  # Very strong syndicated signal
        elif syndicated_matches >= 1:
            score = 0.70
        
        # Syndicated structure pattern
        has_market = 'market' in columns_text
        has_category = 'category' in columns_text
        has_brand = 'brand' in columns_text
        has_period = any(term in columns_text for term in ['week', 'month', 'period', 'date'])
        
        syndicated_structure_count = sum([has_market, has_category, has_brand, has_period])
        if syndicated_structure_count >= 3:
            score = max(score, 0.75)
        
        # Data source indicators
        data_sources = ['nielsen', 'iri', 'symphony', 'circana', 'euromonitor']
        source_matches = sum(1 for source in data_sources if source in content_text or source in columns_text)
        if source_matches > 0:
            score += source_matches * 0.2
        
        # Column count typical for syndicated
        col_count = numerical.get('column_count', 0)
        if col_count >= 8:
            score += 0.1
        
        return min(score, 1.0)
    
    def _detect_ml_product_patterns(self, numerical: Dict[str, Any], columns_text: str, content_text: str) -> float:
        """FIXED: Detect product attribute patterns with heavy penalties"""
        score = 0.0
        
        # Base product attribute indicators
        product_indicators = ['product_id', 'brand', 'category', 'sub_category', 'description', 'hierarchy']
        product_matches = sum(1 for indicator in product_indicators if indicator in columns_text)
        
        if product_matches >= 3:
            score = 0.60
        elif product_matches >= 2:
            score = 0.40
        elif product_matches >= 1:
            score = 0.25
        
        # Master data indicators
        master_indicators = ['master', 'catalog', 'attribute', 'specification', 'reference']
        master_matches = sum(1 for indicator in master_indicators if indicator in columns_text)
        if master_matches > 0:
            score += master_matches * 0.15
        
        # CRITICAL FIX: Heavy penalties for transaction/market data
        
        # Heavy penalty for transaction indicators
        transaction_penalties = ['transaction_id', 'store_id', 'checkout', 'receipt', 'pos', 'sale_date']
        transaction_count = sum(1 for penalty in transaction_penalties if penalty in columns_text)
        if transaction_count >= 1:
            score = max(0.0, score - 0.8)  # Heavy penalty
        
        # Heavy penalty for market metrics
        market_penalties = ['tdp', 'acv', 'velocity', 'market_share', 'distribution']
        market_count = sum(1 for penalty in market_penalties if penalty in columns_text)
        if market_count >= 1:
            score = max(0.0, score - 0.7)  # Heavy penalty
        
        # Penalty for sales transaction columns
        sales_penalties = ['sales_amount', 'quantity_sold', 'units_sold', 'revenue']
        sales_count = sum(1 for penalty in sales_penalties if penalty in columns_text)
        if sales_count >= 1:
            score = max(0.0, score - 0.6)
        
        # Content-based penalties
        if 'transaction' in content_text or 'pos system' in content_text:
            score = max(0.0, score - 0.5)
        
        if any(term in content_text for term in ['nielsen', 'iri', 'market research']):
            score = max(0.0, score - 0.5)
        
        return max(0.0, min(score, 1.0))
    
    def _calculate_generic_ml_score(self, data_type: str, numerical: Dict[str, Any], combined_text: str) -> float:
        """Calculate ML score for other data types"""
        score = 0.0
        
        # Column count heuristics
        col_count = numerical['column_count']
        if data_type == 'depletion_data' and col_count >= 6:
            score += 0.3
        elif data_type == 'margin_data' and col_count >= 5:
            score += 0.3
        
        # Content analysis for specific types
        type_indicators = {
            'depletion_data': ['depletion', 'distributor', 'warehouse', 'shipment'],
            'margin_data': ['margin', 'cost', 'profit', 'pricing'],
            'numerator_intel': ['promotion', 'promo', 'feature', 'display'],
            'trace_data': ['trace', 'supply_chain', 'traceability']
        }
        
        indicators = type_indicators.get(data_type, [])
        matches = sum(1 for indicator in indicators if indicator in combined_text)
        score += matches * 0.2
        
        # Feature-based scoring
        if numerical.get('has_id_columns') and data_type != 'unknown':
            score += 0.1
        
        if numerical.get('has_date_columns') and data_type != 'unknown':
            score += 0.1
        
        return min(score, 1.0)
    
    def _apply_ml_mutual_exclusion(self, scores: Dict[str, float], columns_text: str, content_text: str) -> Dict[str, float]:
        """FIXED: Apply ML-based mutual exclusion rules"""
        
        # If POS score is high, reduce product_attribute significantly
        if scores.get('pos', 0) > 0.6:
            scores['product_attribute'] = max(0.0, scores['product_attribute'] - 0.7)
        
        # If syndicated score is high, reduce product_attribute significantly
        if scores.get('syndicated', 0) > 0.6:
            scores['product_attribute'] = max(0.0, scores['product_attribute'] - 0.7)
        
        # If clear transaction indicators exist, product_attribute should be very low
        has_transaction_indicators = any(indicator in columns_text 
                                       for indicator in ['transaction_id', 'store_id', 'checkout'])
        if has_transaction_indicators:
            scores['product_attribute'] = min(scores['product_attribute'], 0.15)
        
        # If clear market metrics exist, product_attribute should be very low
        has_market_metrics = any(metric in columns_text 
                               for metric in ['tdp', 'acv', 'velocity', 'market_share'])
        if has_market_metrics:
            scores['product_attribute'] = min(scores['product_attribute'], 0.15)
        
        return scores
    
    def _ml_classification(self, ml_features: Dict[str, Any]) -> Dict[str, float]:
        """ML-based classification using trained models"""
        # This would use trained models to predict probabilities
        # For now, return rule-based as placeholder
        return self._rule_based_classification(ml_features)
    
    def _get_individual_predictions(self, ml_features: Dict[str, Any]) -> Dict[str, Any]:
        """Get predictions from individual models"""
        # Placeholder for individual model predictions
        return {
            'random_forest': {'prediction': 'unknown', 'confidence': 0.0},
            'svm': {'prediction': 'unknown', 'confidence': 0.0},
            'neural_network': {'prediction': 'unknown', 'confidence': 0.0}
        }
    
    def _generate_ml_reasoning(self, features: Dict[str, Any], scores: Dict[str, float]) -> str:
        """Generate reasoning for ML classification"""
        numerical = features['numerical']
        top_scores = sorted(scores.items(), key=lambda x: x[1], reverse=True)[:3]
        
        reasoning_parts = [
            f"ML analysis: {numerical['column_count']} columns, {numerical['numeric_column_ratio']:.2f} numeric ratio"
        ]
        
        if numerical['has_id_columns']:
            reasoning_parts.append("ID columns detected")
        
        if numerical['has_date_columns']:
            reasoning_parts.append("Date columns detected")
        
        reasoning_parts.append(f"Top classifications: {', '.join([f'{t}({s:.2f})' for t, s in top_scores])}")
        
        return "; ".join(reasoning_parts)
    
    def get_capabilities(self) -> List[str]:
        return self.capabilities


class DomainExpertAgent(VectorDBEnhancedAgent):
    """CPG domain expert agent with business knowledge and vector DB enhancement - FIXED classification logic"""
    
    def __init__(self, agent_id: str, config: Dict[str, Any]):
        super().__init__(agent_id, AgentType.DOMAIN_EXPERT, config)
        self.capabilities = [
            "cpg_domain_expertise",
            "business_rule_application",
            "industry_pattern_recognition",
            "contextual_analysis",
            "semantic_domain_search"
        ]
        self.domain_rules = self._load_domain_rules()
    
    def _load_domain_rules(self) -> Dict[str, Any]:
        """FIXED: Load enhanced CPG domain business rules with mutual exclusion"""
        return {
            'syndicated_rules': {
                'required_columns': ['market', 'category', 'brand'],
                'typical_metrics': ['tdp', 'acv', 'velocity', 'units', 'dollars'],
                'granularity': 'weekly',
                'sources': ['nielsen', 'iri', 'spins'],
                'strong_indicators': ['tdp', 'acv', 'velocity', 'distribution', 'market_share'],
                'exclusion_indicators': ['transaction_id', 'store_id', 'checkout', 'receipt']
            },
            'pos_rules': {
                'required_columns': ['store', 'upc', 'date'],
                'typical_metrics': ['quantity', 'sales', 'price'],
                'granularity': 'daily',
                'transaction_level': True,
                'strong_indicators': ['transaction_id', 'store_id', 'upc', 'barcode', 'checkout'],
                'exclusion_indicators': ['tdp', 'acv', 'velocity', 'market_share']
            },
            'product_attribute_rules': {
                'required_columns': ['product_id', 'brand', 'category'],
                'typical_attributes': ['size', 'flavor', 'package', 'description'],
                'hierarchical': True,
                'strong_indicators': ['hierarchy', 'master', 'catalog', 'attribute'],
                'exclusion_indicators': ['transaction_id', 'store_id', 'tdp', 'acv', 'sales_amount', 'quantity_sold']
            },
            'margin_rules': {
                'required_columns': ['product', 'cost', 'price'],
                'typical_metrics': ['gross_margin', 'net_margin', 'profit'],
                'financial': True,
                'strong_indicators': ['margin', 'cost', 'profit', 'pricing'],
                'exclusion_indicators': ['transaction_id', 'checkout']
            }
        }
    
    async def analyze(self, file_context: FileContext) -> AgentDecision:
        """Perform domain expert analysis with vector enhancement"""
        start_time = time.time()
        vector_logger = logging.getLogger('vector_operations')
        
        try:
            vector_logger.info(f"DomainExpert {self.agent_id} starting analysis")
            
            domain_analysis = self._perform_domain_analysis(file_context)
            business_context = self._extract_business_context(file_context)
            
            # Vector DB search for domain knowledge and patterns
            vector_results = await self.search_vector_db(domain_analysis)
            
            # FIXED: Enhanced domain rules application with mutual exclusion
            classification_scores = self._apply_domain_rules(domain_analysis, business_context)
            
            # Enhance with vector insights
            enhanced_scores = self._enhance_with_vector_insights(classification_scores, vector_results)
            
            best_classification = max(enhanced_scores.items(), key=lambda x: x[1])
            
            decision = AgentDecision(
                agent_id=self.agent_id,
                agent_type=self.agent_type,
                classification=best_classification[0],
                confidence=best_classification[1],
                reasoning=self._generate_enhanced_reasoning(domain_analysis, business_context, enhanced_scores, vector_results),
                evidence={
                    "domain_analysis": domain_analysis,
                    "business_context": business_context,
                    "classification_scores": classification_scores,
                    "enhanced_scores": enhanced_scores,
                    "vector_results": vector_results,
                    "rule_matches": self._get_rule_matches(file_context)
                },
                processing_time=time.time() - start_time
            )
            
            self.update_performance_metrics(decision)
            
            # Store successful domain pattern
            if decision.confidence > 0.7:
                await self.store_successful_pattern(decision.__dict__, vector_results)
            
            vector_logger.info(f"DomainExpert completed: {decision.classification} ({decision.confidence:.3f})")
            return decision
            
        except Exception as e:
            self.logger.error(f"Domain expert analysis failed: {e}")
            vector_logger.error(f"DomainExpert analysis failed: {e}")
            return AgentDecision(
                agent_id=self.agent_id,
                agent_type=self.agent_type,
                classification="unknown",
                confidence=0.0,
                reasoning=f"Domain expert analysis error: {str(e)}",
                processing_time=time.time() - start_time
            )
    
    def _build_search_query(self, features: Dict[str, Any]) -> str:
        """Build domain-specific search query"""
        query_parts = []
        
        # Add business process information
        business_process = features.get('business_process', 'unknown')
        if business_process != 'general_analytics':
            query_parts.append(f"business process: {business_process}")
        
        # Add granularity information
        granularity = features.get('data_granularity', 'unknown')
        if granularity != 'unknown':
            query_parts.append(f"granularity: {granularity}")
        
        # Add metric types
        metric_types = features.get('metric_types', [])
        if metric_types:
            query_parts.append(f"metrics: {', '.join(metric_types)}")
        
        # Add dimension types
        dimension_types = features.get('dimension_types', [])
        if dimension_types:
            query_parts.append(f"dimensions: {', '.join(dimension_types)}")
        
        # Add data source indicators
        data_sources = features.get('data_source_indicators', {}).get('identified_sources', [])
        if data_sources:
            query_parts.append(f"sources: {', '.join(data_sources)}")
        
        # Add value chain position
        value_chain = features.get('cpg_value_chain_position', 'unknown')
        if value_chain != 'unknown':
            query_parts.append(f"value chain: {value_chain}")
        
        return " | ".join(query_parts)
    
    def _enhance_with_vector_insights(self, traditional_scores: Dict[str, float], 
                                    vector_results: Dict[str, Any]) -> Dict[str, float]:
        """Enhance domain scores with vector insights"""
        enhanced_scores = traditional_scores.copy()
        confidence_boost = vector_results.get('confidence_boost', 0.0)
        
        # Apply confidence boost with domain expert weighting (highest impact)
        for classification in enhanced_scores:
            enhanced_scores[classification] += confidence_boost * 1.2  # Higher weight for domain expert
        
        # Special boost from domain_knowledge collection
        collection_results = vector_results.get('collection_results', {})
        domain_matches = collection_results.get('cpg_domain_knowledge', {}).get('matches', [])
        
        for match in domain_matches:
            metadata = match.get('metadata', {})
            classification = metadata.get('classification', '')
            similarity = match.get('similarity_score', 0)
            
            if classification in enhanced_scores and similarity > 0.6:
                enhanced_scores[classification] += 0.15  # Strong domain knowledge boost
        
        # Boost from expert patterns in learning feedback
        learning_matches = collection_results.get('cpg_learning_feedback', {}).get('matches', [])
        for match in learning_matches:
            metadata = match.get('metadata', {})
            if metadata.get('agent_type') == 'domain_expert':
                classification = metadata.get('correct_classification', '')
                similarity = match.get('similarity_score', 0)
                
                if classification in enhanced_scores and similarity > 0.7:
                    enhanced_scores[classification] += 0.1  # Expert correction boost
        
        # Normalize scores
        for classification in enhanced_scores:
            enhanced_scores[classification] = min(enhanced_scores[classification], 1.0)
        
        return enhanced_scores
    
    def _generate_enhanced_reasoning(self, domain_analysis: Dict[str, Any], 
                                   business_context: Dict[str, Any], 
                                   scores: Dict[str, float],
                                   vector_results: Dict[str, Any]) -> str:
        """Generate enhanced domain reasoning with vector insights"""
        traditional_reasoning = self._generate_domain_reasoning(domain_analysis, business_context, scores)
        
        # Add vector insights
        vector_parts = []
        confidence_boost = vector_results.get('confidence_boost', 0.0)
        if confidence_boost > 0:
            vector_parts.append(f"Domain pattern similarity: +{confidence_boost:.3f}")
        
        # Check for domain knowledge matches
        collection_results = vector_results.get('collection_results', {})
        domain_matches = collection_results.get('cpg_domain_knowledge', {}).get('count', 0)
        if domain_matches > 0:
            vector_parts.append(f"Domain knowledge: {domain_matches} pattern matches")
        
        # Check for expert pattern matches
        learning_matches = collection_results.get('cpg_learning_feedback', {}).get('matches', [])
        expert_matches = [m for m in learning_matches if m.get('metadata', {}).get('agent_type') == 'domain_expert']
        if expert_matches:
            vector_parts.append(f"Expert patterns: {len(expert_matches)} similar decisions")
        
        if vector_parts:
            return f"{traditional_reasoning} | Domain insights: {'; '.join(vector_parts)}"
        else:
            return traditional_reasoning
    
    def _perform_domain_analysis(self, file_context: FileContext) -> Dict[str, Any]:
        """Perform comprehensive domain analysis"""
        schema = file_context.extracted_content.get('schema', {})
        columns = [col.lower() for col in schema.get('columns', [])]
        
        analysis = {
            'data_granularity': self._determine_granularity(columns),
            'metric_types': self._identify_metric_types(columns),
            'dimension_types': self._identify_dimension_types(columns),
            'business_process': self._infer_business_process(columns),
            'data_source_indicators': self._detect_data_source(file_context),
            'cpg_value_chain_position': self._determine_value_chain_position(columns)
        }
        
        return analysis
    
    def _determine_granularity(self, columns: List[str]) -> str:
        """Determine data granularity level"""
        if any(col in ['transaction_id', 'receipt', 'basket'] for col in columns):
            return 'transaction'
        elif any(col in ['store', 'location', 'outlet'] for col in columns):
            return 'store'
        elif any(col in ['market', 'region', 'dma'] for col in columns):
            return 'market'
        elif any(col in ['chain', 'banner', 'retailer'] for col in columns):
            return 'chain'
        else:
            return 'unknown'
    
    def _identify_metric_types(self, columns: List[str]) -> List[str]:
        """Identify types of metrics present"""
        metric_categories = {
            'sales': ['sales', 'revenue', 'dollars', 'amount'],
            'volume': ['units', 'quantity', 'volume', 'cases'],
            'distribution': ['tdp', 'acv', 'distribution', 'velocity'],
            'share': ['share', 'percentage', 'penetration'],
            'pricing': ['price', 'cost', 'margin', 'discount'],
            'promotion': ['promotion', 'feature', 'display', 'tpr']
        }
        
        identified_metrics = []
        for category, keywords in metric_categories.items():
            if any(keyword in col for col in columns for keyword in keywords):
                identified_metrics.append(category)
        
        return identified_metrics
    
    def _identify_dimension_types(self, columns: List[str]) -> List[str]:
        """Identify dimension types present"""
        dimension_categories = {
            'product': ['product', 'brand', 'category', 'sku', 'upc'],
            'geography': ['market', 'region', 'state', 'territory', 'store'],
            'time': ['date', 'week', 'month', 'period', 'year'],
            'customer': ['customer', 'account', 'retailer', 'chain'],
            'channel': ['channel', 'trade', 'banner', 'format']
        }
        
        identified_dimensions = []
        for dimension, keywords in dimension_categories.items():
            if any(keyword in col for col in columns for keyword in keywords):
                identified_dimensions.append(dimension)
        
        return identified_dimensions
    
    def _infer_business_process(self, columns: List[str]) -> str:
        """Infer the business process this data supports"""
        process_indicators = {
            'sales_analytics': ['sales', 'revenue', 'units', 'market'],
            'trade_promotion': ['promotion', 'tpr', 'display', 'feature'],
            'supply_chain': ['shipment', 'inventory', 'depletion', 'warehouse'],
            'customer_analytics': ['customer', 'shopper', 'basket', 'loyalty'],
            'pricing_revenue': ['price', 'margin', 'cost', 'profit'],
            'product_management': ['product', 'brand', 'category', 'attribute']
        }
        
        for process, keywords in process_indicators.items():
            if sum(1 for keyword in keywords if any(keyword in col for col in columns)) >= 2:
                return process
        
        return 'general_analytics'
    
    def _detect_data_source(self, file_context: FileContext) -> Dict[str, Any]:
        """Detect potential data source"""
        content = file_context.extracted_content.get('content', '').lower()
        filename = file_context.file_name.lower()
        
        source_indicators = {
            'nielsen': ['nielsen', 'niq', 'homescan', 'retail measurement'],
            'iri': ['iri', 'information resources', 'infoscan'],
            'spins': ['spins', 'natural products'],
            'internal_pos': ['pos', 'point of sale', 'store system'],
            'dsd': ['dsd', 'direct store delivery', 'distributor'],
            'ecommerce': ['ecommerce', 'online', 'digital', 'web']
        }
        
        detected_sources = []
        for source, keywords in source_indicators.items():
            if any(keyword in content or keyword in filename for keyword in keywords):
                detected_sources.append(source)
        
        return {
            'identified_sources': detected_sources,
            'confidence': len(detected_sources) > 0
        }
    
    def _determine_value_chain_position(self, columns: List[str]) -> str:
        """Determine position in CPG value chain"""
        if any(col in ['manufacturer', 'supplier', 'production'] for col in columns):
            return 'manufacturer'
        elif any(col in ['distributor', 'warehouse', 'depletion'] for col in columns):
            return 'distributor'
        elif any(col in ['retail', 'store', 'pos', 'checkout'] for col in columns):
            return 'retailer'
        elif any(col in ['consumer', 'shopper', 'household'] for col in columns):
            return 'consumer'
        else:
            return 'unknown'
    
    def _extract_business_context(self, file_context: FileContext) -> Dict[str, Any]:
        """Extract business context from file"""
        return {
            'file_size_category': self._categorize_file_size(file_context.file_size),
            'complexity_level': self._assess_complexity(file_context),
            'data_freshness': self._assess_data_freshness(file_context),
            'completeness_indicators': self._assess_completeness(file_context)
        }
    
    def _categorize_file_size(self, file_size: int) -> str:
        """Categorize file size"""
        if file_size < 1024 * 1024:  # < 1MB
            return 'small'
        elif file_size < 100 * 1024 * 1024:  # < 100MB
            return 'medium'
        else:
            return 'large'
    
    def _assess_complexity(self, file_context: FileContext) -> str:
        """Assess data complexity"""
        schema = file_context.extracted_content.get('schema', {})
        col_count = schema.get('column_count', 0)
        
        if col_count < 5:
            return 'simple'
        elif col_count < 15:
            return 'moderate'
        else:
            return 'complex'
    
    def _assess_data_freshness(self, file_context: FileContext) -> str:
        """Assess data freshness indicators"""
        # Simple heuristic based on filename patterns
        filename = file_context.file_name.lower()
        
        if any(term in filename for term in ['2024', '2025', 'current', 'latest']):
            return 'recent'
        elif any(term in filename for term in ['2023', '2022']):
            return 'historical'
        else:
            return 'unknown'
    
    def _assess_completeness(self, file_context: FileContext) -> Dict[str, bool]:
        """Assess data completeness indicators"""
        schema = file_context.extracted_content.get('schema', {})
        columns = schema.get('columns', [])
        
        return {
            'has_time_dimension': any('date' in col.lower() or 'time' in col.lower() for col in columns),
            'has_product_dimension': any('product' in col.lower() or 'brand' in col.lower() for col in columns),
            'has_geography_dimension': any('market' in col.lower() or 'store' in col.lower() for col in columns),
            'has_metrics': any('sales' in col.lower() or 'units' in col.lower() for col in columns)
        }
    
    def _apply_domain_rules(self, domain_analysis: Dict[str, Any], business_context: Dict[str, Any]) -> Dict[str, float]:
        """FIXED: Apply domain-specific business rules with mutual exclusion"""
        scores = {}
        
        for data_type in CPGDomainKnowledge.CPG_DATA_TYPES:
            score = 0.0
            
            # Apply specific enhanced rules based on data type
            if data_type == 'syndicated':
                score = self._apply_syndicated_rules(domain_analysis, business_context)
            elif data_type in ['pos', 'pos_fact']:
                score = self._apply_pos_rules(domain_analysis, business_context)
            elif data_type == 'product_attribute':
                score = self._apply_product_rules(domain_analysis, business_context)
            elif data_type == 'margin_data':
                score = self._apply_margin_rules(domain_analysis, business_context)
            else:
                # Generic scoring for other types
                score = self._apply_generic_rules(data_type, domain_analysis, business_context)
            
            scores[data_type] = min(score, 1.0)
        
        # FIXED: Apply domain-level mutual exclusion rules
        scores = self._apply_domain_mutual_exclusion(scores, domain_analysis)
        
        return scores
    
    def _apply_syndicated_rules(self, domain_analysis: Dict[str, Any], business_context: Dict[str, Any]) -> float:
        """FIXED: Apply enhanced syndicated data rules"""
        score = 0.0
        
        # Check for syndicated-specific metrics (STRONG indicators)
        metrics = domain_analysis.get('metric_types', [])
        if 'distribution' in metrics:
            score += 0.4  # INCREASED from 0.3
        if 'share' in metrics:
            score += 0.3  # INCREASED from 0.2
        
        # Check granularity (syndicated is typically market/chain level)
        granularity = domain_analysis.get('data_granularity')
        if granularity in ['market', 'chain']:
            score += 0.3  # INCREASED from 0.2
        elif granularity == 'transaction':  # Penalty for transaction-level
            score -= 0.5  # NEW penalty
        
        # Check data source (STRONG boost for known syndicated sources)
        sources = domain_analysis.get('data_source_indicators', {}).get('identified_sources', [])
        syndicated_sources = ['nielsen', 'iri', 'spins', 'circana', 'euromonitor']
        source_matches = sum(1 for source in sources if source in syndicated_sources)
        if source_matches > 0:
            score += source_matches * 0.4  # INCREASED from 0.3
        
        # Check business process alignment
        business_process = domain_analysis.get('business_process')
        if business_process == 'sales_analytics':
            score += 0.2
        
        # CRITICAL FIX: Penalty for POS/transaction indicators
        if granularity == 'transaction' or 'transaction' in str(domain_analysis).lower():
            score = max(0.0, score - 0.7)  # Heavy penalty for transaction data
        
        return score
    
    def _apply_pos_rules(self, domain_analysis: Dict[str, Any], business_context: Dict[str, Any]) -> float:
        """FIXED: Apply enhanced POS data rules"""
        score = 0.0
        
        # Check granularity (STRONG indicator for POS)
        granularity = domain_analysis.get('data_granularity')
        if granularity in ['transaction', 'store']:
            score += 0.5  # INCREASED from 0.3
        elif granularity in ['market', 'chain']:  # Penalty for aggregated levels
            score -= 0.3  # NEW penalty
        
        # Check for POS-specific metrics
        metrics = domain_analysis.get('metric_types', [])
        if 'sales' in metrics or 'volume' in metrics:
            score += 0.3  # INCREASED from 0.2
        
        # Check dimensions (transaction-level data needs time + geography)
        dimensions = domain_analysis.get('dimension_types', [])
        if 'geography' in dimensions and 'time' in dimensions:
            score += 0.3  # INCREASED from 0.2
        
        # Check value chain position (STRONG indicator)
        value_chain = domain_analysis.get('cpg_value_chain_position')
        if value_chain == 'retailer':
            score += 0.4  # INCREASED from 0.3
        
        # Check data source
        sources = domain_analysis.get('data_source_indicators', {}).get('identified_sources', [])
        if 'internal_pos' in sources:
            score += 0.3
        
        # CRITICAL FIX: Penalty for syndicated indicators
        if any(source in sources for source in ['nielsen', 'iri', 'spins']):
            score = max(0.0, score - 0.6)  # Heavy penalty for syndicated sources
        
        if 'distribution' in metrics:  # Syndicated metric
            score = max(0.0, score - 0.5)
        
        return score
    
    def _apply_product_rules(self, domain_analysis: Dict[str, Any], business_context: Dict[str, Any]) -> float:
        """FIXED: Apply enhanced product attribute rules with heavy penalties"""
        score = 0.0
        
        # Check dimensions (product attributes should have product dimension)
        dimensions = domain_analysis.get('dimension_types', [])
        if 'product' in dimensions:
            score += 0.5  # INCREASED from 0.4
        
        # Check business process
        business_process = domain_analysis.get('business_process')
        if business_process == 'product_management':
            score += 0.4  # INCREASED from 0.3
        
        # Check complexity (product master data tends to be complex)
        complexity = business_context.get('complexity_level')
        if complexity in ['moderate', 'complex']:
            score += 0.2
        
        # CRITICAL FIX: Heavy penalties for transactional/market data
        
        # Penalty for transaction granularity
        granularity = domain_analysis.get('data_granularity')
        if granularity == 'transaction':
            score = max(0.0, score - 0.8)  # HEAVY penalty
        elif granularity == 'store':
            score = max(0.0, score - 0.6)  # Medium penalty
        
        # Penalty for sales analytics process
        if business_process == 'sales_analytics':
            score = max(0.0, score - 0.7)
        
        # Penalty for retailer value chain position
        value_chain = domain_analysis.get('cpg_value_chain_position')
        if value_chain == 'retailer':
            score = max(0.0, score - 0.6)
        
        # Penalty for POS/syndicated data sources
        sources = domain_analysis.get('data_source_indicators', {}).get('identified_sources', [])
        problematic_sources = ['internal_pos', 'nielsen', 'iri', 'spins']
        if any(source in sources for source in problematic_sources):
            score = max(0.0, score - 0.7)
        
        # Penalty for sales/distribution metrics
        metrics = domain_analysis.get('metric_types', [])
        problematic_metrics = ['sales', 'volume', 'distribution']
        metric_penalty_count = sum(1 for metric in problematic_metrics if metric in metrics)
        if metric_penalty_count > 0:
            score = max(0.0, score - metric_penalty_count * 0.4)
        
        return max(0.0, score)
    
    def _apply_margin_rules(self, domain_analysis: Dict[str, Any], business_context: Dict[str, Any]) -> float:
        """Apply enhanced margin data rules"""
        score = 0.0
        
        # Check for pricing metrics
        metrics = domain_analysis.get('metric_types', [])
        if 'pricing' in metrics:
            score += 0.5  # INCREASED from 0.4
        
        # Check business process
        business_process = domain_analysis.get('business_process')
        if business_process == 'pricing_revenue':
            score += 0.4  # INCREASED from 0.3
        
        # Check value chain position
        value_chain = domain_analysis.get('cpg_value_chain_position')
        if value_chain == 'manufacturer':
            score += 0.3  # INCREASED from 0.2
        
        # Penalty for transaction-level data
        granularity = domain_analysis.get('data_granularity')
        if granularity == 'transaction':
            score = max(0.0, score - 0.4)
        
        return score
    
    def _apply_generic_rules(self, data_type: str, domain_analysis: Dict[str, Any], business_context: Dict[str, Any]) -> float:
        """Apply generic rules for other data types"""
        score = 0.0
        
        # Use CPG domain knowledge for scoring
        cpg_score = CPGDomainKnowledge.get_data_type_score(data_type, {
            'columns': domain_analysis.get('dimension_types', []) + domain_analysis.get('metric_types', []),
            'content_summary': str(domain_analysis)
        })
        
        score += cpg_score * 0.6  # INCREASED from 0.5
        
        return score
    
    def _apply_domain_mutual_exclusion(self, scores: Dict[str, float], domain_analysis: Dict[str, Any]) -> Dict[str, float]:
        """FIXED: Apply domain-level mutual exclusion rules"""
        
        granularity = domain_analysis.get('data_granularity')
        metrics = domain_analysis.get('metric_types', [])
        value_chain = domain_analysis.get('cpg_value_chain_position')
        sources = domain_analysis.get('data_source_indicators', {}).get('identified_sources', [])
        
        # If transaction granularity, heavily favor POS, penalize others
        if granularity == 'transaction':
            scores['product_attribute'] = max(0.0, scores['product_attribute'] - 0.8)
            scores['syndicated'] = max(0.0, scores['syndicated'] - 0.6)
        
        # If syndicated sources detected, heavily favor syndicated, penalize POS/product_attribute
        syndicated_sources = ['nielsen', 'iri', 'spins', 'circana']
        if any(source in sources for source in syndicated_sources):
            scores['pos'] = max(0.0, scores['pos'] - 0.7)
            scores['product_attribute'] = max(0.0, scores['product_attribute'] - 0.7)
        
        # If retailer value chain + store/transaction granularity, favor POS
        if value_chain == 'retailer' and granularity in ['transaction', 'store']:
            scores['product_attribute'] = max(0.0, scores['product_attribute'] - 0.6)
            scores['syndicated'] = max(0.0, scores['syndicated'] - 0.5)
        
        # If distribution metrics present, favor syndicated, penalize POS/product_attribute  
        if 'distribution' in metrics:
            scores['pos'] = max(0.0, scores['pos'] - 0.6)
            scores['product_attribute'] = max(0.0, scores['product_attribute'] - 0.6)
        
        # If sales + volume metrics without distribution metrics, favor POS
        if 'sales' in metrics and 'volume' in metrics and 'distribution' not in metrics:
            scores['product_attribute'] = max(0.0, scores['product_attribute'] - 0.5)
        
        return scores
    
    def _get_rule_matches(self, file_context: FileContext) -> Dict[str, List[str]]:
        """Get specific rule matches for transparency"""
        schema = file_context.extracted_content.get('schema', {})
        columns = [col.lower() for col in schema.get('columns', [])]
        
        matches = {}
        for data_type, rules in self.domain_rules.items():
            type_matches = []
            
            # Check required columns
            required = rules.get('required_columns', [])
            for req_col in required:
                if any(req_col in col for col in columns):
                    type_matches.append(f"Required column: {req_col}")
            
            # Check typical metrics
            metrics = rules.get('typical_metrics', [])
            for metric in metrics:
                if any(metric in col for col in columns):
                    type_matches.append(f"Metric: {metric}")
            
            # FIXED: Check strong indicators
            strong_indicators = rules.get('strong_indicators', [])
            for indicator in strong_indicators:
                if any(indicator in col for col in columns):
                    type_matches.append(f"Strong indicator: {indicator}")
            
            # FIXED: Check exclusion indicators (penalties)
            exclusion_indicators = rules.get('exclusion_indicators', [])
            for exclusion in exclusion_indicators:
                if any(exclusion in col for col in columns):
                    type_matches.append(f"EXCLUSION: {exclusion}")
            
            matches[data_type] = type_matches
        
        return matches
    
    def _generate_domain_reasoning(self, domain_analysis: Dict[str, Any], business_context: Dict[str, Any], scores: Dict[str, float]) -> str:
        """Generate domain expert reasoning"""
        top_scores = sorted(scores.items(), key=lambda x: x[1], reverse=True)[:3]
        
        reasoning_parts = [
            f"Domain analysis: {domain_analysis.get('data_granularity')} granularity"
        ]
        
        if domain_analysis.get('business_process') != 'general_analytics':
            reasoning_parts.append(f"Business process: {domain_analysis.get('business_process')}")
        
        sources = domain_analysis.get('data_source_indicators', {}).get('identified_sources', [])
        if sources:
            reasoning_parts.append(f"Data sources: {', '.join(sources)}")
        
        value_chain = domain_analysis.get('cpg_value_chain_position')
        if value_chain != 'unknown':
            reasoning_parts.append(f"Value chain: {value_chain}")
        
        reasoning_parts.append(f"Top classifications: {', '.join([f'{t}({s:.2f})' for t, s in top_scores])}")
        
        return "; ".join(reasoning_parts)
    
    def get_capabilities(self) -> List[str]:
        return self.capabilities