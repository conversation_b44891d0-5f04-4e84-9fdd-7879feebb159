"""
Dictionary Generation Agent for CPG Domain
Generates data dictionaries using Azure OpenAI with CPG-specific categorization
"""

import pandas as pd
import json
import logging
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass
import sys
import os
from pathlib import Path

project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.config_manager import ConfigManager, AzureOpenAIConfig

try:
    from openai import AzureOpenAI
except ImportError:
    raise ImportError("Please install the openai package: pip install openai")


@dataclass
class ColumnAnalysis:
    """Data class for column analysis results"""
    column_name: str
    sample_values: List[str]
    data_type: str
    null_count: int
    unique_count: int
    is_numeric: bool
    is_categorical: bool


class DictionaryGenerationAgent:
    """
    Generates data dictionaries for CPG domain datasets using Azure OpenAI.
    
    This agent analyzes DataFrame structure and generates contextual descriptions,
    categorizes columns according to CPG taxonomy, and determines table types.
    """
    
    # CPG Domain Categories - Comprehensive Global & Local Coverage
    CPG_CATEGORIES = {
        # Global Market Structure
        "global_market_identifiers": "Global regions, international markets, worldwide territories, global trade zones",
        "global_product_dimensions": "Universal product attributes, global SKU properties, international standards",
        "global_brand_management": "Global brand portfolios, international brand strategies, worldwide brand positioning",
        "global_supply_chain": "International logistics, global sourcing, cross-border supply networks, worldwide distribution",
        "global_trade_compliance": "International trade regulations, customs codes, global import/export requirements",
        "global_financial_metrics": "Multi-currency financials, exchange rates, international pricing, global profitability",
        "global_competitive_landscape": "Worldwide market share, international competitor analysis, global benchmarks",
        
        # Local Market Structure  
        "local_market_identifiers": "Local regions, domestic territories, neighborhood markets, local store networks",
        "local_market_structure": "Regional segments, local channels, domestic trade structures, community networks",
        "local_product_adaptations": "Regional product variations, local customizations, market-specific formulations",
        "local_regulatory_compliance": "Local regulations, regional labeling requirements, domestic safety standards",
        "local_cultural_preferences": "Regional taste preferences, cultural product adaptations, local consumer habits",
        "local_pricing_strategies": "Regional pricing models, local promotional pricing, market-specific discounts",
        "local_distribution_channels": "Regional distributors, local retail networks, community-based channels",
        
        # Product Management
        "product_hierarchy_global": "Global category structures, international product classifications, worldwide taxonomies",
        "product_hierarchy_local": "Regional category adaptations, local product groupings, market-specific classifications",
        "product_attributes_physical": "Physical specifications, packaging details, size variations, material properties",
        "product_attributes_regulatory": "Regulatory classifications, compliance codes, certification requirements",
        "product_attributes_marketing": "Marketing claims, promotional features, consumer-facing attributes",
        "product_lifecycle_management": "Launch dates, discontinuation schedules, lifecycle stages, innovation pipeline",
        
        # Customer & Consumer Insights
        "customer_demographics_global": "International consumer profiles, global demographic trends, worldwide customer segments",
        "customer_demographics_local": "Regional consumer characteristics, local demographic patterns, community profiles",
        "customer_behavior_digital": "Online shopping patterns, digital engagement, e-commerce interactions",
        "customer_behavior_physical": "In-store behavior, shopping journey, retail interaction patterns",
        "customer_loyalty_programs": "Loyalty schemes, rewards programs, customer retention initiatives",
        "customer_feedback_sentiment": "Consumer reviews, satisfaction scores, sentiment analysis, feedback data",
        
        # Sales & Performance
        "sales_metrics_transactional": "Transaction-level sales data, individual purchase records, point-of-sale data",
        "sales_metrics_aggregated": "Summarized sales performance, period totals, aggregated volumes",
        "sales_performance_kpis": "Key performance indicators, sales targets, achievement metrics, growth rates",
        "sales_channel_performance": "Channel-specific sales, omnichannel metrics, distribution performance",
        
        # Marketing & Promotions
        "promotional_campaigns_global": "International campaigns, global marketing initiatives, worldwide promotions",
        "promotional_campaigns_local": "Regional campaigns, local marketing events, community-specific promotions",
        "promotional_mechanics": "Promotion types, discount structures, incentive mechanisms, offer details",
        "promotional_performance": "Campaign effectiveness, promotion ROI, marketing performance metrics",
        "advertising_media": "Media spend, advertising channels, marketing touchpoints, campaign reach",
        
        # Financial Management
        "financial_metrics_revenue": "Revenue streams, sales income, top-line financial performance",
        "financial_metrics_costs": "Cost structures, expenses, operational costs, cost of goods sold",
        "financial_metrics_profitability": "Profit margins, profitability analysis, financial performance ratios",
        "financial_metrics_budgeting": "Budget allocations, financial planning, spend management, cost controls",
        "financial_metrics_taxation": "Tax implications, duty calculations, local tax requirements",
        
        # Supply Chain & Operations
        "inventory_management": "Stock levels, inventory turnover, warehouse management, stock optimization",
        "supply_chain_logistics": "Transportation, shipping, delivery networks, logistics performance",
        "supply_chain_sourcing": "Supplier management, procurement data, sourcing strategies, vendor performance",
        "supply_chain_quality": "Quality control, product safety, compliance monitoring, quality assurance",
        "manufacturing_operations": "Production data, manufacturing efficiency, capacity utilization, operational metrics",
        
        # Time & Seasonality
        "temporal_dimensions_standard": "Standard time periods, fiscal calendars, reporting periods, date hierarchies",
        "temporal_dimensions_seasonal": "Seasonal patterns, holiday periods, weather-related timeframes, cultural seasons",
        "temporal_dimensions_promotional": "Promotional calendars, campaign periods, event-driven timeframes",
        
        # Competitive Intelligence
        "competitive_market_share": "Market share data, competitive positioning, share of voice metrics",
        "competitive_pricing": "Competitor pricing, price comparisons, competitive price positioning",
        "competitive_product_analysis": "Product comparisons, feature analysis, competitive product mapping",
        "competitive_promotional_activity": "Competitor promotions, competitive marketing activities, rival campaigns",
        
        # Digital & Technology
        "digital_commerce_metrics": "E-commerce sales, online performance, digital channel metrics",
        "digital_marketing_performance": "Digital marketing ROI, online advertising effectiveness, social media metrics",
        "technology_integration": "System integrations, data connections, technology performance metrics",
        
        # Sustainability & Social Responsibility
        "sustainability_metrics": "Environmental impact, sustainability scores, carbon footprint, eco-friendly attributes",
        "social_responsibility": "Social impact metrics, community involvement, ethical sourcing, corporate responsibility",
        
        # Risk & Compliance
        "risk_management": "Risk indicators, compliance violations, audit findings, risk assessment data",
        "regulatory_monitoring": "Regulatory changes, compliance updates, legal requirements, policy impacts"
    }
    
    def __init__(self, config_manager: ConfigManager = None):
        """
        Initialize the Dictionary Generation Agent.
        
        Args:
            config_manager: Optional ConfigManager instance. If None, creates a new one.
        """
        self.logger = self._setup_logging()
        
        # Initialize config manager
        if config_manager is None:
            self.config_manager = ConfigManager()
        else:
            self.config_manager = config_manager
            
        # Initialize Azure OpenAI client
        self.azure_client = self._initialize_azure_client()
        self.azure_config = self.config_manager.get_azure_openai_config()
        
        self.logger.info("Dictionary Generation Agent initialized successfully")
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging for the agent"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _convert_category_to_title_case(self, category_key: str) -> str:
        """
        Convert snake_case category key to Title Case display format.
        
        Args:
            category_key: Snake case category key (e.g., 'global_market_identifiers')
            
        Returns:
            Title case display format (e.g., 'Global Market Identifiers')
        """
        return category_key.replace('_', ' ').title()
    
    def _convert_title_case_to_category_key(self, title_case: str) -> str:
        """
        Convert Title Case display format back to snake_case category key.
        
        Args:
            title_case: Title case display format (e.g., 'Global Market Identifiers')
            
        Returns:
            Snake case category key (e.g., 'global_market_identifiers')
        """
        return title_case.lower().replace(' ', '_')
    
    def _get_valid_display_categories(self) -> List[str]:
        """
        Get list of valid category names in Title Case display format.
        
        Returns:
            List of category names in Title Case format
        """
        return [self._convert_category_to_title_case(key) for key in self.CPG_CATEGORIES.keys()]
    
    def _validate_input_data(self, data: Union[pd.DataFrame, List[Dict[str, Any]]]) -> None:
        """
        Validate input data format.
        
        Args:
            data: Input data - either DataFrame or list of dictionaries
            
        Raises:
            ValueError: If data format is invalid
        """
        if data is None:
            raise ValueError("Input data cannot be None")
        
        if isinstance(data, pd.DataFrame):
            if data.empty:
                raise ValueError("Input DataFrame cannot be empty")
            if len(data.columns) == 0:
                raise ValueError("Input DataFrame must have at least one column")
        
        elif isinstance(data, list):
            if len(data) == 0:
                raise ValueError("Input list cannot be empty")
            
            # Check if all items are dictionaries
            if not all(isinstance(item, dict) for item in data):
                raise ValueError("All items in the list must be dictionaries")
            
            # Check if all dictionaries have the same keys (columns)
            if len(data) > 0:
                first_keys = set(data[0].keys())
                if not all(set(item.keys()) == first_keys for item in data):
                    self.logger.warning("Not all dictionaries have the same keys. Missing values will be filled with None.")
        
        else:
            raise ValueError("Input data must be either a pandas DataFrame or a list of dictionaries")
    
    def _convert_list_to_dataframe(self, data: List[Dict[str, Any]]) -> pd.DataFrame:
        """
        Convert list of dictionaries to pandas DataFrame.
        
        Args:
            data: List of dictionaries where each dict represents a row
            
        Returns:
            DataFrame created from the list of dictionaries
        """
        try:
            self.logger.info(f"Converting list of {len(data)} dictionaries to DataFrame")
            
            # Use pandas built-in conversion
            df = pd.DataFrame(data)
            
            self.logger.info(f"Successfully converted to DataFrame with shape {df.shape}")
            self.logger.info(f"Columns: {list(df.columns)}")
            
            return df
            
        except Exception as e:
            self.logger.error(f"Error converting list to DataFrame: {e}")
            raise ValueError(f"Failed to convert list of dictionaries to DataFrame: {e}")
    
    def _prepare_dataframe(self, data: Union[pd.DataFrame, List[Dict[str, Any]]]) -> pd.DataFrame:
        """
        Prepare DataFrame from various input formats.
        
        Args:
            data: Input data - either DataFrame or list of dictionaries
            
        Returns:
            Prepared DataFrame ready for analysis
        """
        # Validate input
        self._validate_input_data(data)
        
        # Convert to DataFrame if needed
        if isinstance(data, list):
            df = self._convert_list_to_dataframe(data)
        else:
            df = data.copy()  # Make a copy to avoid modifying original
        
        # Basic DataFrame validation
        if df.empty:
            raise ValueError("Resulting DataFrame is empty")
        
        # Log basic statistics
        self.logger.info(f"Prepared DataFrame: {df.shape[0]} rows, {df.shape[1]} columns")
        
        return df
    
    def _initialize_azure_client(self) -> AzureOpenAI:
        """Initialize Azure OpenAI client with configuration"""
        try:
            azure_config = self.config_manager.get_azure_openai_config()
            
            client = AzureOpenAI(
                api_key=azure_config.api_key,
                api_version=azure_config.api_version,
                azure_endpoint=azure_config.endpoint,
                timeout=azure_config.timeout,
                max_retries=azure_config.max_retries
            )
            
            self.logger.info("Azure OpenAI client initialized successfully")
            return client
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Azure OpenAI client: {e}")
            raise
    
    def analyze_dataframe(self, df: pd.DataFrame, max_sample_values: int = 5) -> List[ColumnAnalysis]:
        """
        Analyze DataFrame structure and extract column information.
        
        Args:
            df: Input DataFrame to analyze
            max_sample_values: Maximum number of sample values to extract per column
            
        Returns:
            List of ColumnAnalysis objects containing column metadata
        """
        self.logger.info(f"Analyzing DataFrame with {len(df.columns)} columns and {len(df)} rows")
        
        analyses = []
        
        for column in df.columns:
            series = df[column]
            
            # Get sample values (non-null, unique)
            non_null_values = series.dropna()
            unique_values = non_null_values.unique()
            sample_values = [str(val) for val in unique_values[:max_sample_values]]
            
            # Determine data characteristics
            is_numeric = pd.api.types.is_numeric_dtype(series)
            is_categorical = (
                pd.api.types.is_categorical_dtype(series) or 
                pd.api.types.is_object_dtype(series) or
                (is_numeric and len(unique_values) < len(df) * 0.5)  # Low cardinality numeric
            )
            
            analysis = ColumnAnalysis(
                column_name=column,
                sample_values=sample_values,
                data_type=str(series.dtype),
                null_count=series.isnull().sum(),
                unique_count=len(unique_values),
                is_numeric=is_numeric,
                is_categorical=is_categorical
            )
            
            analyses.append(analysis)
        
        self.logger.info(f"Completed analysis of {len(analyses)} columns")
        return analyses
    
    def _create_cpg_prompt(self, column_analyses: List[ColumnAnalysis]) -> str:
        """
        Create a comprehensive prompt for CPG domain dictionary generation.
        
        Args:
            column_analyses: List of column analysis results
            
        Returns:
            Formatted prompt string for Azure OpenAI
        """
        
        # Build column information
        column_info = []
        for analysis in column_analyses:
            sample_str = ", ".join(analysis.sample_values[:5]) if analysis.sample_values else "No samples"
            
            column_info.append(f"""
Column: {analysis.column_name}
- Data Type: {analysis.data_type}
- Sample Values: {sample_str}
- Unique Count: {analysis.unique_count}
- Null Count: {analysis.null_count}
- Is Numeric: {analysis.is_numeric}
- Is Categorical: {analysis.is_categorical}""")
        
        # Create organized categories description with Title Case display names
        categories_by_section = {
            "Global Market Structure": [
                ("Global Market Identifiers", "Global regions, international markets, worldwide territories, global trade zones"),
                ("Global Product Dimensions", "Universal product attributes, global SKU properties, international standards"),
                ("Global Brand Management", "Global brand portfolios, international brand strategies, worldwide brand positioning"),
                ("Global Supply Chain", "International logistics, global sourcing, cross-border supply networks, worldwide distribution"),
                ("Global Trade Compliance", "International trade regulations, customs codes, global import/export requirements"),
                ("Global Financial Metrics", "Multi-currency financials, exchange rates, international pricing, global profitability"),
                ("Global Competitive Landscape", "Worldwide market share, international competitor analysis, global benchmarks")
            ],
            "Local Market Structure": [
                ("Local Market Identifiers", "Local regions, domestic territories, neighborhood markets, local store networks"),
                ("Local Market Structure", "Regional segments, local channels, domestic trade structures, community networks"),
                ("Local Product Adaptations", "Regional product variations, local customizations, market-specific formulations"),
                ("Local Regulatory Compliance", "Local regulations, regional labeling requirements, domestic safety standards"),
                ("Local Cultural Preferences", "Regional taste preferences, cultural product adaptations, local consumer habits"),
                ("Local Pricing Strategies", "Regional pricing models, local promotional pricing, market-specific discounts"),
                ("Local Distribution Channels", "Regional distributors, local retail networks, community-based channels")
            ],
            "Product Management": [
                ("Product Hierarchy Global", "Global category structures, international product classifications, worldwide taxonomies"),
                ("Product Hierarchy Local", "Regional category adaptations, local product groupings, market-specific classifications"),
                ("Product Attributes Physical", "Physical specifications, packaging details, size variations, material properties"),
                ("Product Attributes Regulatory", "Regulatory classifications, compliance codes, certification requirements"),
                ("Product Attributes Marketing", "Marketing claims, promotional features, consumer-facing attributes"),
                ("Product Lifecycle Management", "Launch dates, discontinuation schedules, lifecycle stages, innovation pipeline")
            ],
            "Customer & Consumer Insights": [
                ("Customer Demographics Global", "International consumer profiles, global demographic trends, worldwide customer segments"),
                ("Customer Demographics Local", "Regional consumer characteristics, local demographic patterns, community profiles"),
                ("Customer Behavior Digital", "Online shopping patterns, digital engagement, e-commerce interactions"),
                ("Customer Behavior Physical", "In-store behavior, shopping journey, retail interaction patterns"),
                ("Customer Loyalty Programs", "Loyalty schemes, rewards programs, customer retention initiatives"),
                ("Customer Feedback Sentiment", "Consumer reviews, satisfaction scores, sentiment analysis, feedback data")
            ],
            "Sales & Performance": [
                ("Sales Metrics Transactional", "Transaction-level sales data, individual purchase records, point-of-sale data"),
                ("Sales Metrics Aggregated", "Summarized sales performance, period totals, aggregated volumes"),
                ("Sales Performance Kpis", "Key performance indicators, sales targets, achievement metrics, growth rates"),
                ("Sales Channel Performance", "Channel-specific sales, omnichannel metrics, distribution performance")
            ],
            "Financial Management": [
                ("Financial Metrics Revenue", "Revenue streams, sales income, top-line financial performance"),
                ("Financial Metrics Costs", "Cost structures, expenses, operational costs, cost of goods sold"),
                ("Financial Metrics Profitability", "Profit margins, profitability analysis, financial performance ratios"),
                ("Financial Metrics Budgeting", "Budget allocations, financial planning, spend management, cost controls"),
                ("Financial Metrics Taxation", "Tax implications, duty calculations, local tax requirements")
            ]
        }
        
        categories_desc = ""
        for section, cats in categories_by_section.items():
            categories_desc += f"\n**{section}:**\n"
            for cat, desc in cats:
                categories_desc += f"- {cat}: {desc}\n"
        
        # Add remaining categories with Title Case display names
        remaining_categories = [
            ("Promotional Campaigns Global", "International campaigns, global marketing initiatives, worldwide promotions"),
            ("Promotional Campaigns Local", "Regional campaigns, local marketing events, community-specific promotions"),
            ("Promotional Mechanics", "Promotion types, discount structures, incentive mechanisms, offer details"),
            ("Promotional Performance", "Campaign effectiveness, promotion ROI, marketing performance metrics"),
            ("Advertising Media", "Media spend, advertising channels, marketing touchpoints, campaign reach"),
            ("Inventory Management", "Stock levels, inventory turnover, warehouse management, stock optimization"),
            ("Supply Chain Logistics", "Transportation, shipping, delivery networks, logistics performance"),
            ("Supply Chain Sourcing", "Supplier management, procurement data, sourcing strategies, vendor performance"),
            ("Supply Chain Quality", "Quality control, product safety, compliance monitoring, quality assurance"),
            ("Manufacturing Operations", "Production data, manufacturing efficiency, capacity utilization, operational metrics"),
            ("Temporal Dimensions Standard", "Standard time periods, fiscal calendars, reporting periods, date hierarchies"),
            ("Temporal Dimensions Seasonal", "Seasonal patterns, holiday periods, weather-related timeframes, cultural seasons"),
            ("Temporal Dimensions Promotional", "Promotional calendars, campaign periods, event-driven timeframes"),
            ("Competitive Market Share", "Market share data, competitive positioning, share of voice metrics"),
            ("Competitive Pricing", "Competitor pricing, price comparisons, competitive price positioning"),
            ("Competitive Product Analysis", "Product comparisons, feature analysis, competitive product mapping"),
            ("Competitive Promotional Activity", "Competitor promotions, competitive marketing activities, rival campaigns"),
            ("Digital Commerce Metrics", "E-commerce sales, online performance, digital channel metrics"),
            ("Digital Marketing Performance", "Digital marketing ROI, online advertising effectiveness, social media metrics"),
            ("Technology Integration", "System integrations, data connections, technology performance metrics"),
            ("Sustainability Metrics", "Environmental impact, sustainability scores, carbon footprint, eco-friendly attributes"),
            ("Social Responsibility", "Social impact metrics, community involvement, ethical sourcing, corporate responsibility"),
            ("Risk Management", "Risk indicators, compliance violations, audit findings, risk assessment data"),
            ("Regulatory Monitoring", "Regulatory changes, compliance updates, legal requirements, policy impacts")
        ]
        
        categories_desc += f"\n**Additional Categories:**\n"
        for cat, desc in remaining_categories:
            categories_desc += f"- {cat}: {desc}\n"

        prompt = f"""
You are a CPG (Consumer Packaged Goods) domain expert tasked with generating a comprehensive data dictionary.

## CPG Domain Context
You're analyzing data from the Consumer Packaged Goods industry, which operates at both GLOBAL and LOCAL levels:

**GLOBAL PERSPECTIVE:**
- International product portfolios and brand management
- Cross-border supply chains and trade compliance
- Multi-currency financial operations
- Worldwide competitive landscape analysis
- Global market segmentation and strategy

**LOCAL PERSPECTIVE:**
- Regional market adaptations and local preferences
- Local regulatory compliance and cultural considerations
- Community-based distribution and retail networks
- Regional pricing strategies and promotional activities
- Local customer behavior and demographic patterns

## Column Categories:
{categories_desc}

## Table Type Classification Rules:
- **FACT**: Transactional, measurable, numeric data that represents business events or activities
  - Sales amounts, revenues, costs, quantities, volumes
  - Performance metrics, KPIs, rates, percentages (when measurable)
  - Event-based data with timestamps
  - Inventory movements, financial transactions
  
- **DIMENSION**: Descriptive, categorical, reference data used for analysis context
  - Product information, customer details, geographic locations
  - Time hierarchies, organizational structures, classifications
  - Descriptive attributes, codes, identifiers, names
  - Categorical flags, status indicators, classifications

## Column Information to Analyze:
{chr(10).join(column_info)}

## Task:
Generate a data dictionary with the following format for each column:

## Guidelines for Categorization:
1. **Global vs Local Identification:**
   - Look for keywords like "global", "international", "worldwide", "regional", "local", "domestic"
   - Consider scope: does this apply across markets or is it market-specific?
   
2. **Product Management:**
   - "global" categories for universal product attributes
   - "local" categories for regional adaptations
   - "physical" for tangible product specifications
   - "regulatory" for compliance-related attributes
   - "marketing" for promotional/consumer-facing features

3. **Financial & Sales:**
   - Separate revenue, costs, profitability, and budgeting
   - Distinguish transactional vs aggregated sales data
   - Consider multi-currency vs local currency implications

4. **Customer Insights:**
   - Distinguish demographic data from behavioral data
   - Separate digital vs physical shopping behavior
   - Consider loyalty and feedback as separate categories

5. **Supply Chain:**
   - Separate inventory, logistics, sourcing, quality, and manufacturing
   - Consider the operational focus of each metric

## Output Format:
Return ONLY a valid JSON array with this exact structure:
[
  {{
    "column_name": "column_name_here",
    "description": "Clear business description explaining the column's purpose and business value in CPG context (2-3 sentences)",
    "column_category": "Title_Case_Category_From_The_List_Above",
    "table_type": "FACT_or_DIMENSION"
  }}
]

## Important Notes:
- Use business-friendly language in descriptions
- Consider the full CPG value chain context
- Be specific about global vs local scope when relevant
- Ensure table_type aligns with data nature (measurable = FACT, descriptive = DIMENSION)
- Choose the MOST SPECIFIC category that fits the column's primary purpose
- **CRITICAL**: Use exact Title Case category names from the list above (e.g., "Global Market Identifiers", "Product Attributes Physical")

Generate the JSON response now:
"""
        
        return prompt
    
    def _call_azure_openai(self, prompt: str) -> str:
        """
        Call Azure OpenAI API with the generated prompt.
        
        Args:
            prompt: The formatted prompt string
            
        Returns:
            Response content from Azure OpenAI
        """
        try:
            self.logger.info("Calling Azure OpenAI API...")
            
            response = self.azure_client.chat.completions.create(
                model=self.azure_config.deployment,
                messages=[
                    {
                        "role": "system", 
                        "content": "You are a CPG domain expert specializing in data dictionary generation. Always return valid JSON."
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                temperature=self.azure_config.temperature or 0.1,
                max_tokens=self.azure_config.max_tokens or 4000,
                top_p=0.95,
                frequency_penalty=0,
                presence_penalty=0
            )
            
            content = response.choices[0].message.content
            self.logger.info("Successfully received response from Azure OpenAI")
            return content
            
        except Exception as e:
            self.logger.error(f"Error calling Azure OpenAI API: {e}")
            raise
    
    def _parse_response(self, response_content: str) -> List[Dict[str, Any]]:
        """
        Parse and validate the Azure OpenAI response.
        
        Args:
            response_content: Raw response from Azure OpenAI
            
        Returns:
            List of dictionary entries
        """
        try:
            # Clean the response (remove potential markdown formatting)
            cleaned_content = response_content.strip()
            if cleaned_content.startswith("```json"):
                cleaned_content = cleaned_content.replace("```json", "").replace("```", "").strip()
            elif cleaned_content.startswith("```"):
                cleaned_content = cleaned_content.replace("```", "").strip()
            
            # Parse JSON
            parsed_data = json.loads(cleaned_content)
            
            # Validate structure
            if not isinstance(parsed_data, list):
                raise ValueError("Response must be a JSON array")
            
            required_fields = ["column_name", "description", "column_category", "table_type"]
            valid_display_categories = self._get_valid_display_categories()
            
            for item in parsed_data:
                if not isinstance(item, dict):
                    raise ValueError("Each item must be a dictionary")
                
                missing_fields = [field for field in required_fields if field not in item]
                if missing_fields:
                    raise ValueError(f"Missing required fields: {missing_fields}")
                
                # Validate table_type
                if item["table_type"] not in ["FACT", "DIMENSION"]:
                    raise ValueError(f"Invalid table_type: {item['table_type']}. Must be 'FACT' or 'DIMENSION'")
                
                # Validate column_category using Title Case format
                if item["column_category"] not in valid_display_categories:
                    self.logger.warning(f"Unknown column category: {item['column_category']}. Expected one of: {valid_display_categories}")
            
            self.logger.info(f"Successfully parsed {len(parsed_data)} dictionary entries")
            return parsed_data
            
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse JSON response: {e}")
            self.logger.error(f"Response content: {response_content}")
            raise ValueError(f"Invalid JSON response: {e}")
        except Exception as e:
            self.logger.error(f"Error parsing response: {e}")
            raise
    
    def generate_dictionary(self, data: Union[pd.DataFrame, List[Dict[str, Any]]]) -> pd.DataFrame:
        """
        Generate a complete data dictionary for the input data.
        
        Args:
            data: Input data - either a pandas DataFrame or a list of dictionaries
                  For list format: [{'col1': 'val1', 'col2': 'val2'}, {'col1': 'val3', 'col2': 'val4'}]
            
        Returns:
            DataFrame with columns: column_name, description, column_category, table_type
        """
        try:
            self.logger.info("Starting data dictionary generation process")
            
            # Step 1: Prepare DataFrame from input data
            df = self._prepare_dataframe(data)
            
            # Step 2: Analyze DataFrame structure
            column_analyses = self.analyze_dataframe(df)
            
            # Step 3: Create CPG-specific prompt
            prompt = self._create_cpg_prompt(column_analyses)
            
            # Step 4: Call Azure OpenAI
            response_content = self._call_azure_openai(prompt)
            
            # Step 5: Parse and validate response
            dictionary_entries = self._parse_response(response_content)
            
            # Step 6: Create result DataFrame
            result_df = pd.DataFrame(dictionary_entries)
            
            # Ensure column order
            result_df = result_df[["column_name", "description", "column_category", "table_type"]]
            
            self.logger.info(f"Successfully generated dictionary for {len(result_df)} columns")
            return result_df
            
        except Exception as e:
            self.logger.error(f"Failed to generate data dictionary: {e}")
            raise
    
    def validate_dictionary(self, dictionary_df: pd.DataFrame, original_data: Union[pd.DataFrame, List[Dict[str, Any]]]) -> Tuple[bool, List[str]]:
        """
        Validate the generated dictionary against the original data.
        
        Args:
            dictionary_df: Generated dictionary DataFrame
            original_data: Original input data (DataFrame or list of dictionaries)
            
        Returns:
            Tuple of (is_valid, list_of_issues)
        """
        issues = []
        
        # Prepare original data as DataFrame for comparison
        original_df = self._prepare_dataframe(original_data)
        
        # Check if all columns are covered
        dict_columns = set(dictionary_df['column_name'].tolist())
        original_columns = set(original_df.columns.tolist())
        
        missing_columns = original_columns - dict_columns
        if missing_columns:
            issues.append(f"Missing columns in dictionary: {missing_columns}")
        
        extra_columns = dict_columns - original_columns
        if extra_columns:
            issues.append(f"Extra columns in dictionary: {extra_columns}")
        
        # Check for duplicate column names
        duplicates = dictionary_df['column_name'].duplicated()
        if duplicates.any():
            duplicate_names = dictionary_df.loc[duplicates, 'column_name'].tolist()
            issues.append(f"Duplicate column names in dictionary: {duplicate_names}")
        
        # Check for empty descriptions
        empty_descriptions = dictionary_df['description'].isnull() | (dictionary_df['description'] == '')
        if empty_descriptions.any():
            empty_cols = dictionary_df.loc[empty_descriptions, 'column_name'].tolist()
            issues.append(f"Columns with empty descriptions: {empty_cols}")
        
        is_valid = len(issues) == 0
        
        if is_valid:
            self.logger.info("Dictionary validation passed")
        else:
            self.logger.warning(f"Dictionary validation failed with {len(issues)} issues")
            for issue in issues:
                self.logger.warning(f"  - {issue}")
        
        return is_valid, issues
    
    def get_category_summary(self, dictionary_df: pd.DataFrame) -> pd.DataFrame:
        """
        Generate a summary of column categories and table types.
        
        Args:
            dictionary_df: Generated dictionary DataFrame
            
        Returns:
            Summary DataFrame with category statistics
        """
        summary = dictionary_df.groupby(['column_category', 'table_type']).size().reset_index(name='count')
        summary = summary.sort_values(['table_type', 'count'], ascending=[True, False])
        
        self.logger.info(f"Generated category summary with {len(summary)} category-type combinations")
        return summary
    
    def generate_dictionary_from_list(self, data_list: List[Dict[str, Any]]) -> pd.DataFrame:
        """
        Convenience method specifically for list of dictionaries input.
        
        Args:
            data_list: List of dictionaries where each dict represents a row
                      Example: [{'col1': 'val1', 'col2': 'val2'}, {'col1': 'val3', 'col2': 'val4'}]
            
        Returns:
            DataFrame with columns: column_name, description, column_category, table_type
        """
        return self.generate_dictionary(data_list)
    
    def analyze_list_structure(self, data_list: List[Dict[str, Any]], max_sample_values: int = 5) -> List[ColumnAnalysis]:
        """
        Convenience method to analyze structure of list of dictionaries.
        
        Args:
            data_list: List of dictionaries to analyze
            max_sample_values: Maximum number of sample values to extract per column
            
        Returns:
            List of ColumnAnalysis objects containing column metadata
        """
        df = self._prepare_dataframe(data_list)
        return self.analyze_dataframe(df, max_sample_values)
    
    @staticmethod
    def create_sample_data_list() -> List[Dict[str, Any]]:
        """
        Create sample CPG data in list of dictionaries format for testing.
        
        Returns:
            List of dictionaries representing sample CPG data
        """
        return [
            {
                'product_id': '0001',
                'product_name': 'CocaCola Classic',
                'global_sku': 'GSKU_0001',
                'local_sku_variant': 'LOC_001_US',
                'global_brand_code': 'GB_COLA',
                'local_brand_variant': 'CocaCola_US',
                'global_category_code': 'BEV',
                'local_category_name': 'Soft Drinks',
                'net_weight_grams': 355,
                'package_material': 'Aluminum',
                'transaction_amount': 1.99,
                'units_sold': 150,
                'country_iso': 'USA',
                'store_id': 'ST001',
                'fiscal_quarter': 'Q1',
                'promotion_type': 'None',
                'inventory_level': 500
            },
            {
                'product_id': '0002',
                'product_name': 'Pepsi Cola',
                'global_sku': 'GSKU_0002',
                'local_sku_variant': 'LOC_002_US',
                'global_brand_code': 'GB_PEPSI',
                'local_brand_variant': 'Pepsi_US',
                'global_category_code': 'BEV',
                'local_category_name': 'Soft Drinks',
                'net_weight_grams': 355,
                'package_material': 'Aluminum',
                'transaction_amount': 1.89,
                'units_sold': 120,
                'country_iso': 'USA',
                'store_id': 'ST002',
                'fiscal_quarter': 'Q1',
                'promotion_type': 'Discount',
                'inventory_level': 350
            },
            {
                'product_id': '0003',
                'product_name': 'Sprite Lemon-Lime',
                'global_sku': 'GSKU_0003',
                'local_sku_variant': 'LOC_003_US',
                'global_brand_code': 'GB_SPRITE',
                'local_brand_variant': 'Sprite_US',
                'global_category_code': 'BEV',
                'local_category_name': 'Soft Drinks',
                'net_weight_grams': 355,
                'package_material': 'Aluminum',
                'transaction_amount': 1.95,
                'units_sold': 80,
                'country_iso': 'USA',
                'store_id': 'ST001',
                'fiscal_quarter': 'Q1',
                'promotion_type': 'BOGO',
                'inventory_level': 275
            },
            {
                'product_id': '0004',
                'product_name': 'Fanta Orange',
                'global_sku': 'GSKU_0004',
                'local_sku_variant': 'LOC_004_CA',
                'global_brand_code': 'GB_FANTA',
                'local_brand_variant': 'Fanta_CA',
                'global_category_code': 'BEV',
                'local_category_name': 'Soft Drinks',
                'net_weight_grams': 355,
                'package_material': 'Aluminum',
                'transaction_amount': 2.15,
                'units_sold': 95,
                'country_iso': 'CAN',
                'store_id': 'ST003',
                'fiscal_quarter': 'Q1',
                'promotion_type': 'Coupon',
                'inventory_level': 180
            },
            {
                'product_id': '0005',
                'product_name': 'Diet Coke',
                'global_sku': 'GSKU_0005',
                'local_sku_variant': 'LOC_005_US',
                'global_brand_code': 'GB_COLA',
                'local_brand_variant': 'DietCoke_US',
                'global_category_code': 'BEV',
                'local_category_name': 'Diet Beverages',
                'net_weight_grams': 355,
                'package_material': 'Aluminum',
                'transaction_amount': 2.09,
                'units_sold': 110,
                'country_iso': 'USA',
                'store_id': 'ST004',
                'fiscal_quarter': 'Q1',
                'promotion_type': 'None',
                'inventory_level': 425
            }
        ]
    
    def get_sample_output_format(self) -> List[Dict[str, str]]:
        """
        Get sample output format showing Title Case categories.
        
        Returns:
            List of sample dictionary entries with Title Case categories
        """
        return [
            {
                "column_name": "global_sku",
                "description": "Universal product identifier used across all international markets for consistent global inventory management and cross-border product tracking.",
                "column_category": "Global Product Dimensions",
                "table_type": "DIMENSION"
            },
            {
                "column_name": "transaction_amount",
                "description": "Individual transaction sales amount representing the total value of a single customer purchase at point-of-sale.",
                "column_category": "Sales Metrics Transactional",
                "table_type": "FACT"
            },
            {
                "column_name": "local_brand_variant",
                "description": "Regional brand name customization adapted for local market preferences and cultural considerations in specific geographic markets.",
                "column_category": "Local Product Adaptations",
                "table_type": "DIMENSION"
            }
        ]