"""
Base Agent Framework for CPG File Classification
Core classes and interfaces for the agentic system
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime
import logging
import uuid
from enum import Enum
import asyncio

class AgentType(Enum):
    """Types of agents in the system"""
    SUPERVISOR = "supervisor"
    SCHEMA_ANALYZER = "schema_analyzer"
    CONTENT_ANALYZER = "content_analyzer"
    PATTERN_RECOGNIZER = "pattern_recognizer"
    DOMAIN_EXPERT = "domain_expert"
    ML_CLASSIFIER = "ml_classifier"
    VOTING_COORDINATOR = "voting_coordinator"
    FEEDBACK_COLLECTOR = "feedback_collector"

class ConfidenceLevel(Enum):
    """Confidence levels for agent decisions"""
    VERY_LOW = 0.2
    LOW = 0.4
    MEDIUM = 0.6
    HIGH = 0.8
    VERY_HIGH = 0.95

@dataclass
class AgentDecision:
    """Represents a decision made by an agent"""
    agent_id: str
    agent_type: AgentType
    classification: str
    confidence: float
    reasoning: str
    evidence: Dict[str, Any] = field(default_factory=dict)
    processing_time: float = 0.0
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class FileContext:
    """Context information about the file being classified"""
    file_path: str
    file_name: str
    file_type: str
    file_size: int
    extracted_content: Dict[str, Any]
    file_features: Dict[str, Any] = field(default_factory=dict)
    business_context: Dict[str, Any] = field(default_factory=dict)
    processing_metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class AgentMessage:
    """Message format for agent communication"""
    message_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    sender_id: str = ""
    receiver_id: str = ""
    message_type: str = ""
    content: Dict[str, Any] = field(default_factory=dict)
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())
    requires_response: bool = False

class BaseAgent(ABC):
    """Abstract base class for all agents"""
    
    def __init__(self, agent_id: str, agent_type: AgentType, config: Dict[str, Any]):
        self.agent_id = agent_id
        self.agent_type = agent_type
        self.config = config
        self.logger = logging.getLogger(f"agent.{agent_id}")
        self.is_active = False
        self.message_queue = []
        self.capabilities = []
        self.performance_metrics = {
            "decisions_made": 0,
            "accuracy": 0.0,
            "avg_confidence": 0.0,
            "avg_processing_time": 0.0
        }
    
    @abstractmethod
    async def analyze(self, file_context: FileContext) -> AgentDecision:
        """Analyze the file and return a decision"""
        pass
    
    @abstractmethod
    def get_capabilities(self) -> List[str]:
        """Return list of agent capabilities"""
        pass
    
    async def send_message(self, receiver_id: str, message_type: str, content: Dict[str, Any]) -> str:
        """Send message to another agent"""
        message = AgentMessage(
            sender_id=self.agent_id,
            receiver_id=receiver_id,
            message_type=message_type,
            content=content
        )
        # In a real implementation, this would use a message broker
        self.logger.info(f"Sending message to {receiver_id}: {message_type}")
        return message.message_id
    
    async def receive_message(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Receive and process a message"""
        self.logger.info(f"Received message from {message.sender_id}: {message.message_type}")
        self.message_queue.append(message)
        
        if message.requires_response:
            return await self.process_message(message)
        return None
    
    async def process_message(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Process received message and optionally return response"""
        # Override in subclasses for specific message handling
        return None
    
    def update_performance_metrics(self, decision: AgentDecision, actual_result: str = None):
        """Update agent performance metrics"""
        self.performance_metrics["decisions_made"] += 1
        
        # Update average confidence
        total_confidence = (self.performance_metrics["avg_confidence"] * 
                          (self.performance_metrics["decisions_made"] - 1) + 
                          decision.confidence)
        self.performance_metrics["avg_confidence"] = (
            total_confidence / self.performance_metrics["decisions_made"]
        )
        
        # Update average processing time
        total_time = (self.performance_metrics["avg_processing_time"] * 
                     (self.performance_metrics["decisions_made"] - 1) + 
                     decision.processing_time)
        self.performance_metrics["avg_processing_time"] = (
            total_time / self.performance_metrics["decisions_made"]
        )
        
        # Update accuracy if actual result is provided
        if actual_result and decision.classification == actual_result:
            current_correct = self.performance_metrics["accuracy"] * (self.performance_metrics["decisions_made"] - 1)
            self.performance_metrics["accuracy"] = (current_correct + 1) / self.performance_metrics["decisions_made"]
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics"""
        return self.performance_metrics.copy()
    
    def activate(self):
        """Activate the agent"""
        self.is_active = True
        self.logger.info(f"Agent {self.agent_id} activated")
    
    def deactivate(self):
        """Deactivate the agent"""
        self.is_active = False
        self.logger.info(f"Agent {self.agent_id} deactivated")

class CPGDomainKnowledge:
    """CPG domain knowledge and classification categories"""
    
    # Primary CPG data categories
    CPG_DATA_TYPES = [
        "syndicated",
        "pos",
        "product_attribute",
        "depletion_data",
        "margin_data",
        "numerator_intel",
        "trace_data",
        "product_mapping",
        "geography_mapping",
        "pvp_mapping",
        "national_accounts",
        "pos_fact",
        "dimension_data"
    ]
    
    # Data type descriptions
    DATA_TYPE_DESCRIPTIONS = {
        "syndicated": "Third-party market research data (Nielsen, IRI) with aggregated retail metrics",
        "pos": "Point-of-sale transaction data from retail systems",
        "product_attribute": "Product master data with attributes, hierarchies, and specifications",
        "depletion_data": "Distributor shipment and inventory depletion information",
        "margin_data": "Cost and margin analysis data for products and channels",
        "numerator_intel": "Promotional and marketing intelligence data",
        "trace_data": "Supply chain traceability and product movement data",
        "product_mapping": "Product hierarchy and cross-reference mappings",
        "geography_mapping": "Geographic territory and market boundary definitions",
        "pvp_mapping": "Price-Volume-Pack mapping and configuration data",
        "national_accounts": "Key account customer data and analytics",
        "pos_fact": "Fact table data from POS systems with detailed transactions",
        "dimension_data": "Reference dimension data for analytics (time, geography, product)"
    }
    
    # Key indicators for each data type
    DATA_TYPE_INDICATORS = {
        "syndicated": {
            "column_patterns": ["tdp", "acv", "velocity", "market_share", "total_distribution", "all_commodity"],
            "business_terms": ["nielsen", "iri", "syndicated", "market", "category", "brand"],
            "metrics": ["units", "dollars", "share", "penetration", "frequency"],
            "granularity": ["market", "category", "brand", "week", "month"]
        },
        "pos": {
            "column_patterns": ["upc", "sku", "store", "transaction", "scan", "register", "checkout"],
            "business_terms": ["pos", "point of sale", "transaction", "receipt", "basket"],
            "metrics": ["quantity", "amount", "price", "discount", "tax"],
            "granularity": ["store", "transaction", "item", "day", "hour"]
        },
        "product_attribute": {
            "column_patterns": ["product_id", "sku", "upc", "brand", "category", "description", "attribute"],
            "business_terms": ["product", "item", "brand", "category", "hierarchy", "master"],
            "metrics": ["weight", "size", "volume", "count", "pack"],
            "granularity": ["product", "variant", "package", "brand"]
        },
        "depletion_data": {
            "column_patterns": ["distributor", "depletion", "shipment", "inventory", "warehouse"],
            "business_terms": ["depletion", "distributor", "dsd", "warehouse", "inventory"],
            "metrics": ["cases", "units", "volume", "shipments", "stock"],
            "granularity": ["distributor", "warehouse", "route", "week"]
        },
        "margin_data": {
            "column_patterns": ["cost", "margin", "profit", "price", "discount", "rebate"],
            "business_terms": ["margin", "cost", "profit", "pricing", "rebate", "allowance"],
            "metrics": ["gross_margin", "net_margin", "cost", "price", "profit"],
            "granularity": ["product", "customer", "channel", "month"]
        },
        "numerator_intel": {
            "column_patterns": ["promotion", "campaign", "coupon", "display", "feature"],
            "business_terms": ["numerator", "promotion", "campaign", "marketing", "coupon"],
            "metrics": ["reach", "frequency", "lift", "roi", "redemption"],
            "granularity": ["campaign", "promotion", "customer", "week"]
        },
        "trace_data": {
            "column_patterns": ["lot", "batch", "trace", "serial", "manufacturing", "facility"],
            "business_terms": ["trace", "lot", "batch", "manufacturing", "facility", "recall"],
            "metrics": ["quantity", "batch_size", "production_date", "expiry"],
            "granularity": ["lot", "batch", "facility", "day"]
        },
        "product_mapping": {
            "column_patterns": ["mapping", "hierarchy", "parent", "child", "level", "category"],
            "business_terms": ["mapping", "hierarchy", "cross_reference", "lookup", "master"],
            "metrics": [],
            "granularity": ["category", "subcategory", "brand", "variant"]
        },
        "geography_mapping": {
            "column_patterns": ["geography", "territory", "region", "market", "area", "zone"],
            "business_terms": ["geography", "territory", "region", "market", "area"],
            "metrics": [],
            "granularity": ["region", "market", "territory", "store"]
        },
        "pvp_mapping": {
            "column_patterns": ["price", "volume", "pack", "pvp", "size", "configuration"],
            "business_terms": ["pvp", "price_volume_pack", "configuration", "pack"],
            "metrics": ["price", "volume", "pack_size", "unit_price"],
            "granularity": ["product", "pack", "size", "configuration"]
        },
        "national_accounts": {
            "column_patterns": ["account", "customer", "retailer", "chain", "banner"],
            "business_terms": ["national_account", "customer", "retailer", "chain", "key_account"],
            "metrics": ["sales", "volume", "share", "distribution", "velocity"],
            "granularity": ["account", "customer", "banner", "month"]
        },
        "pos_fact": {
            "column_patterns": ["fact", "measure", "quantity", "amount", "sales", "units"],
            "business_terms": ["fact", "measure", "sales", "transaction", "aggregated"],
            "metrics": ["sales", "units", "quantity", "amount", "transactions"],
            "granularity": ["store", "product", "day", "week", "month"]
        },
        "dimension_data": {
            "column_patterns": ["dimension", "time", "date", "calendar", "geography", "product"],
            "business_terms": ["dimension", "reference", "lookup", "calendar", "hierarchy"],
            "metrics": [],
            "granularity": ["day", "week", "month", "quarter", "year"]
        }
    }
    
    @classmethod
    def get_data_type_score(cls, data_type: str, file_features: Dict[str, Any]) -> float:
        """Calculate similarity score for a specific data type"""
        if data_type not in cls.DATA_TYPE_INDICATORS:
            return 0.0
        
        indicators = cls.DATA_TYPE_INDICATORS[data_type]
        score = 0.0
        max_score = 0.0
        
        # Check column patterns
        columns = [col.lower() for col in file_features.get('columns', [])]
        column_patterns = indicators['column_patterns']
        
        for pattern in column_patterns:
            max_score += 1.0
            if any(pattern in col for col in columns):
                score += 1.0
        
        # Check business terms
        content = file_features.get('content_summary', '').lower()
        business_terms = indicators['business_terms']
        
        for term in business_terms:
            max_score += 0.5
            if term in content:
                score += 0.5
        
        # Check granularity indicators
        granularity_terms = indicators['granularity']
        for term in granularity_terms:
            max_score += 0.3
            if any(term in col for col in columns):
                score += 0.3
        
        return score / max_score if max_score > 0 else 0.0
    
    @classmethod
    def get_all_scores(cls, file_features: Dict[str, Any]) -> Dict[str, float]:
        """Get scores for all CPG data types"""
        scores = {}
        for data_type in cls.CPG_DATA_TYPES:
            scores[data_type] = cls.get_data_type_score(data_type, file_features)
        return scores