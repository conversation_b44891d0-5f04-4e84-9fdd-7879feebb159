"""
Schema Matcher Agent
Provides multiple semantic matching approaches for source and target schema columns
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from neo4j import GraphDatabase
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from fuzzywuzzy import fuzz
import json
import re
from pathlib import Path
import sys
from collections import Counter

# Add project root to path to import ConfigManager
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from core.config_manager import ConfigManager


class MatcherAgent:
    """
    Schema matching agent that uses multiple semantic approaches to match 
    source schema columns with target schema columns stored in Neo4j
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        Initialize MatcherAgent with ConfigManager
        
        Args:
            config_manager: ConfigManager instance for accessing configurations
        """
        self.config_manager = config_manager
        self.driver = None
        self.neo4j_config = None
        self.matcher_config = self._load_matcher_config()
        self._connect_neo4j()
    
    def _load_matcher_config(self) -> Dict[str, Any]:
        """Load matcher-specific configuration with defaults"""
        default_config = {
            "weights": {
                "vector_embedding": 0.4,
                "synonym": 0.3,
                "description": 0.2,
                "column_name": 0.1
            },
            "min_confidence_threshold": 0.3,
            "max_matches_per_source": 3,
            "enable_fuzzy_matching": True,
            "synonym_similarity_threshold": 0.1
        }
        
        # Try to get matcher config from ConfigManager, fallback to defaults
        matcher_config = self.config_manager.get("matcher_agent", default_config)
        return matcher_config
    
    def _connect_neo4j(self):
        """Establish connection to Neo4j using ConfigManager"""
        try:
            self.neo4j_config = self.config_manager.get_neo4j_config()
            self.driver = GraphDatabase.driver(
                self.neo4j_config.uri,
                auth=(self.neo4j_config.username, self.neo4j_config.password)
            )
            
            # Test connection
            with self.driver.session(database=self.neo4j_config.database) as session:
                session.run("RETURN 1")
            
            print(f"Connected to Neo4j at {self.neo4j_config.uri}")
            
        except Exception as e:
            print(f"Failed to connect to Neo4j: {e}")
            raise
    
    def _get_target_columns(self, target_table_name: str) -> List[Dict[str, Any]]:
        """
        Fetch target schema columns from Neo4j
        
        Args:
            target_table_name: Name of the target table
            
        Returns:
            List of column dictionaries with properties mapped to source DataFrame format
        """
        query = """
        MATCH (t:Table {name: $table_name})-[:HAS_COLUMN]->(c:Column) 
        RETURN c.name as name,
               c.description as description, 
               c.applicable_domains as applicable_domains,
               c.category as category, 
               c.synonyms as synonyms, 
               c.sentence_transformer_embedding as sentence_transformer_embedding
        """
        
        try:
            with self.driver.session(database=self.neo4j_config.database) as session:
                result = session.run(query, table_name=target_table_name)
                columns = []
                
                for record in result:
                    # Safely extract column name from Neo4j 'name' property
                    column_name = record.get('name')
                    if column_name is None or str(column_name).strip() == '':
                        column_name = f"unknown_column_{len(columns)}"
                        print(f"Warning: Found column with missing name, assigned: {column_name}")
                    
                    # Map Neo4j properties to source DataFrame format
                    column = {
                        'column_name': str(column_name).strip(),  # Map c.name -> column_name
                        'description': record.get('description') or '',
                        'applicable_domain': record.get('applicable_domains') or '',  # Map c.applicable_domains -> applicable_domain
                        'column_category': record.get('category') or '',  # Map c.category -> column_category
                        'synonyms': record.get('synonyms') or '[]',
                        'sentence_transformer_embedding': record.get('sentence_transformer_embedding')
                    }
                    columns.append(column)
                
                if not columns:
                    raise ValueError(f"No columns found for table: {target_table_name}")
                
                print(f"Retrieved {len(columns)} target columns for table '{target_table_name}'")
                # Show first few column names
                sample_names = [col['column_name'] for col in columns[:3]]
                print(f"Sample target columns: {sample_names}")
                return columns
                
        except Exception as e:
            print(f"Error fetching target columns: {e}")
            raise
    
    def _vector_embedding_match(self, source_df: pd.DataFrame, target_columns: List[Dict]) -> np.ndarray:
        """
        Perform vector embedding matching using cosine similarity
        
        Args:
            source_df: Source schema DataFrame
            target_columns: Target column data
            
        Returns:
            Similarity matrix (source_rows x target_cols)
        """
        def parse_embedding(embedding_data) -> Optional[np.ndarray]:
            """Parse embedding from various formats and validate"""
            # Handle None first
            if embedding_data is None:
                return None
            
            # Handle numpy arrays directly (from Neo4j)
            if isinstance(embedding_data, np.ndarray):
                if embedding_data.size == 0:
                    return None
                # Check for NaN or infinity values in array
                if np.any(np.isnan(embedding_data)) or np.any(np.isinf(embedding_data)):
                    return None
                return embedding_data.astype(float)
            
            # Handle list directly (common from Neo4j)
            if isinstance(embedding_data, (list, tuple)):
                try:
                    embedding_array = np.array(embedding_data, dtype=float)
                    if embedding_array.size == 0:
                        return None
                    if np.any(np.isnan(embedding_array)) or np.any(np.isinf(embedding_array)):
                        return None
                    return embedding_array
                except (ValueError, TypeError):
                    return None
            
            # Handle pandas NA/NaN for scalar types
            try:
                if pd.isna(embedding_data):
                    return None
            except (TypeError, ValueError):
                # pd.isna might fail on some types, continue processing
                pass
            
            try:
                # Handle string format
                if isinstance(embedding_data, str):
                    # Handle empty or null strings
                    if not embedding_data.strip() or embedding_data.strip().lower() in ['none', 'null', 'nan']:
                        return None
                    
                    # Parse JSON string
                    embedding = json.loads(embedding_data)
                else:
                    # Try to convert other types
                    embedding = embedding_data
                
                # Convert to numpy array
                embedding_array = np.array(embedding, dtype=float)
                
                # Validate array
                if embedding_array.size == 0:
                    return None
                
                # Check for NaN or infinity values
                if np.any(np.isnan(embedding_array)) or np.any(np.isinf(embedding_array)):
                    return None
                
                return embedding_array
                
            except (json.JSONDecodeError, ValueError, TypeError) as e:
                print(f"Could not parse embedding (type: {type(embedding_data)}): {e}")
                return None
        
        def normalize_embedding_dimension(embeddings: List[np.ndarray], target_dim: int) -> List[np.ndarray]:
            """Normalize all embeddings to the same dimension"""
            normalized = []
            for emb in embeddings:
                if emb is None:
                    # Create zero vector for missing embeddings
                    normalized.append(np.zeros(target_dim))
                elif emb.size == target_dim:  # Use .size instead of len() for safety
                    normalized.append(emb)
                elif emb.size > target_dim:
                    # Truncate
                    normalized.append(emb[:target_dim])
                else:
                    # Pad with zeros
                    padded = np.zeros(target_dim)
                    padded[:emb.size] = emb  # Use .size instead of len()
                    normalized.append(padded)
            return normalized
        
        try:
            # Extract source embeddings
            source_embeddings = []
            for _, row in source_df.iterrows():
                embedding = parse_embedding(row.get('sentence_transformer_embedding'))
                source_embeddings.append(embedding)
            
            # Extract target embeddings
            target_embeddings = []
            for col in target_columns:
                embedding = parse_embedding(col.get('sentence_transformer_embedding'))
                target_embeddings.append(embedding)
            
            # Check if we have any valid embeddings
            valid_source = [emb for emb in source_embeddings if emb is not None]
            valid_target = [emb for emb in target_embeddings if emb is not None]
            
            if len(valid_source) == 0 or len(valid_target) == 0:
                print("No valid embeddings found for vector matching")
                return np.zeros((len(source_df), len(target_columns)))
            
            # Determine target dimension (use the most common dimension)
            all_valid = valid_source + valid_target
            dimensions = [emb.size for emb in all_valid]  # Use .size instead of len()
            
            if len(dimensions) == 0:
                print("No valid embedding dimensions found")
                return np.zeros((len(source_df), len(target_columns)))
            
            # Use the most common dimension
            target_dim = Counter(dimensions).most_common(1)[0][0]
            print(f"Using embedding dimension: {target_dim}")
            
            # Normalize dimensions
            source_embeddings = normalize_embedding_dimension(source_embeddings, target_dim)
            target_embeddings = normalize_embedding_dimension(target_embeddings, target_dim)
            
            # Convert to matrices
            source_matrix = np.array(source_embeddings)
            target_matrix = np.array(target_embeddings)
            
            # Validate matrix shapes
            if source_matrix.shape[1] != target_matrix.shape[1]:
                print(f"Dimension mismatch: source={source_matrix.shape[1]}, target={target_matrix.shape[1]}")
                return np.zeros((len(source_df), len(target_columns)))
            
            # Check for zero vectors (which would cause issues with cosine similarity)
            source_norms = np.linalg.norm(source_matrix, axis=1)
            target_norms = np.linalg.norm(target_matrix, axis=1)
            
            # Replace zero vectors with small random vectors to avoid division by zero
            zero_source_indices = np.where(source_norms == 0)[0]
            zero_target_indices = np.where(target_norms == 0)[0]
            
            if len(zero_source_indices) > 0:
                print(f"Found {len(zero_source_indices)} zero source embeddings, replacing with small random vectors")
                source_matrix[zero_source_indices] = np.random.normal(0, 0.01, (len(zero_source_indices), target_dim))
            
            if len(zero_target_indices) > 0:
                print(f"Found {len(zero_target_indices)} zero target embeddings, replacing with small random vectors")
                target_matrix[zero_target_indices] = np.random.normal(0, 0.01, (len(zero_target_indices), target_dim))
            
            # Compute cosine similarity
            similarity_matrix = cosine_similarity(source_matrix, target_matrix)
            
            # Ensure values are in valid range [0, 1] (cosine similarity can be [-1, 1])
            similarity_matrix = np.clip((similarity_matrix + 1) / 2, 0, 1)
            
            print(f"Vector embedding matching completed: {similarity_matrix.shape}")
            return similarity_matrix
            
        except Exception as e:
            print(f"Error in vector embedding matching: {e}")
            import traceback
            traceback.print_exc()
            # Return zero matrix as fallback
            return np.zeros((len(source_df), len(target_columns)))
    
    def _synonym_match(self, source_df: pd.DataFrame, target_columns: List[Dict]) -> np.ndarray:
        """
        Perform advanced synonym-based matching using multiple similarity techniques
        
        Args:
            source_df: Source schema DataFrame
            target_columns: Target column data
            
        Returns:
            Similarity matrix (source_rows x target_cols)
        """
        def parse_synonyms(synonym_data) -> set:
            """Parse synonyms from various formats"""
            if synonym_data is None:
                return set()
            
            if isinstance(synonym_data, list):
                return set(str(syn).lower().strip() for syn in synonym_data if syn is not None and str(syn).strip())
            
            try:
                if pd.isna(synonym_data):
                    return set()
            except (TypeError, ValueError):
                pass
            
            try:
                if isinstance(synonym_data, str):
                    if not synonym_data.strip() or synonym_data.strip().lower() in ['none', 'null', 'nan', '[]']:
                        return set()
                    
                    synonyms_list = json.loads(synonym_data)
                    if not isinstance(synonyms_list, list):
                        return set()
                    
                    return set(str(syn).lower().strip() for syn in synonyms_list if syn is not None and str(syn).strip())
                else:
                    syn_str = str(synonym_data).strip().lower()
                    return {syn_str} if syn_str and syn_str not in ['none', 'null', 'nan'] else set()
                    
            except (json.JSONDecodeError, TypeError, ValueError):
                return set()
        
        def normalize_text(text: str) -> str:
            """Advanced text normalization for better matching"""
            if not text:
                return ""
            
            text = text.lower().strip()
            
            # Remove common prefixes/suffixes
            text = re.sub(r'^(the|a|an)_', '', text)
            text = re.sub(r'_(id|identifier|key|code|tag|name)$', '', text)
            
            # Normalize separators
            text = re.sub(r'[_\-\s]+', '_', text)
            
            # Handle common abbreviations
            abbreviations = {
                'id': 'identifier', 'num': 'number', 'qty': 'quantity',
                'amt': 'amount', 'val': 'value', 'desc': 'description',
                'addr': 'address', 'tel': 'telephone', 'ph': 'phone',
                'dt': 'date', 'tm': 'time', 'ts': 'timestamp',
                'mkt': 'market', 'seg': 'segment', 'cat': 'category'
            }
            
            for abbr, full in abbreviations.items():
                text = re.sub(rf'\b{abbr}\b', full, text)
            
            # Handle plurals
            text = re.sub(r's$', '', text) if text.endswith('s') and len(text) > 3 else text
            
            return text
        
        def compute_synonym_similarity(source_synonyms: set, target_synonyms: set) -> float:
            """Compute advanced similarity between synonym sets"""
            if not source_synonyms or not target_synonyms:
                return 0.0
            
            # Normalize all synonyms
            norm_source = {normalize_text(syn) for syn in source_synonyms}
            norm_target = {normalize_text(syn) for syn in target_synonyms}
            
            total_score = 0.0
            max_possible_score = 0.0
            
            for source_syn in source_synonyms:
                norm_source_syn = normalize_text(source_syn)
                best_match_score = 0.0
                
                for target_syn in target_synonyms:
                    norm_target_syn = normalize_text(target_syn)
                    
                    # Level 1: Exact match (highest score)
                    if source_syn == target_syn:
                        score = 1.0
                    elif norm_source_syn == norm_target_syn:
                        score = 0.95
                    
                    # Level 2: Substring/containment match
                    elif source_syn in target_syn or target_syn in source_syn:
                        score = 0.8
                    elif norm_source_syn in norm_target_syn or norm_target_syn in norm_source_syn:
                        score = 0.75
                    
                    # Level 3: Fuzzy string similarity
                    else:
                        # Use fuzzy matching for partial similarity
                        ratio = fuzz.ratio(source_syn, target_syn) / 100.0
                        partial_ratio = fuzz.partial_ratio(source_syn, target_syn) / 100.0
                        token_ratio = fuzz.token_set_ratio(source_syn, target_syn) / 100.0
                        
                        # Weighted combination of fuzzy scores
                        score = (0.4 * ratio + 0.3 * partial_ratio + 0.3 * token_ratio)
                        
                        # Apply threshold - only consider matches above 0.6
                        score = score if score >= 0.6 else 0.0
                    
                    best_match_score = max(best_match_score, score)
                
                total_score += best_match_score
                max_possible_score += 1.0
            
            # Normalize by the number of source synonyms
            final_score = total_score / max_possible_score if max_possible_score > 0 else 0.0
            
            # Apply bonus for bidirectional matching
            if final_score > 0:
                # Check reverse direction for bonus
                reverse_score = 0.0
                reverse_max = 0.0
                
                for target_syn in target_synonyms:
                    best_reverse_score = 0.0
                    for source_syn in source_synonyms:
                        if target_syn == source_syn or normalize_text(target_syn) == normalize_text(source_syn):
                            best_reverse_score = 1.0
                            break
                        elif target_syn in source_syn or source_syn in target_syn:
                            best_reverse_score = max(best_reverse_score, 0.8)
                    
                    reverse_score += best_reverse_score
                    reverse_max += 1.0
                
                reverse_normalized = reverse_score / reverse_max if reverse_max > 0 else 0.0
                
                # Combine forward and reverse scores with higher weight on forward
                final_score = 0.7 * final_score + 0.3 * reverse_normalized
            
            return min(final_score, 1.0)  # Ensure score doesn't exceed 1.0
        
        def safe_get_column_name(data, key='column_name'):
            """Safely get column name and convert to lowercase"""
            name = data.get(key) if isinstance(data, dict) else getattr(data, key, None)
            if name is None or pd.isna(name):
                return ''
            return str(name).lower().strip()
        
        try:
            similarity_matrix = np.zeros((len(source_df), len(target_columns)))
            
            for i, (_, source_row) in enumerate(source_df.iterrows()):
                source_synonyms = parse_synonyms(source_row.get('synonyms'))
                source_col_name = safe_get_column_name(source_row)
                
                # Add column name and its normalized version to synonyms
                if source_col_name:
                    source_synonyms.add(source_col_name)
                    normalized_name = normalize_text(source_col_name)
                    if normalized_name:
                        source_synonyms.add(normalized_name)
                
                for j, target_col in enumerate(target_columns):
                    target_synonyms = parse_synonyms(target_col.get('synonyms'))
                    target_col_name = safe_get_column_name(target_col)
                    
                    # Add column name and its normalized version to synonyms
                    if target_col_name:
                        target_synonyms.add(target_col_name)
                        normalized_name = normalize_text(target_col_name)
                        if normalized_name:
                            target_synonyms.add(normalized_name)
                    
                    similarity = compute_synonym_similarity(source_synonyms, target_synonyms)
                    similarity_matrix[i, j] = similarity
            
            print(f"Synonym matching completed: {similarity_matrix.shape}")
            return similarity_matrix
            
        except Exception as e:
            print(f"Error in synonym matching: {e}")
            import traceback
            traceback.print_exc()
            return np.zeros((len(source_df), len(target_columns)))
    
    def _description_match(self, source_df: pd.DataFrame, target_columns: List[Dict]) -> np.ndarray:
        """
        Perform description-based matching using TF-IDF and cosine similarity
        
        Args:
            source_df: Source schema DataFrame
            target_columns: Target column data
            
        Returns:
            Similarity matrix (source_rows x target_cols)
        """
        def preprocess_text(text) -> str:
            """Basic text preprocessing with null handling"""
            if text is None or pd.isna(text):
                return ""
            
            # Convert to string safely
            text_str = str(text).strip()
            
            if not text_str or text_str.lower() in ['none', 'null', 'nan']:
                return ""
            
            # Convert to lowercase and remove special characters
            text_clean = re.sub(r'[^a-zA-Z0-9\s]', ' ', text_str.lower())
            # Remove extra whitespace
            text_clean = ' '.join(text_clean.split())
            return text_clean
        
        try:
            # Collect all descriptions with safe preprocessing
            source_descriptions = []
            for _, row in source_df.iterrows():
                desc = preprocess_text(row.get('description'))
                source_descriptions.append(desc)
            
            target_descriptions = []
            for col in target_columns:
                desc = preprocess_text(col.get('description'))
                target_descriptions.append(desc)
            
            # Check if we have meaningful descriptions
            meaningful_source = [desc for desc in source_descriptions if desc.strip()]
            meaningful_target = [desc for desc in target_descriptions if desc.strip()]
            
            if not meaningful_source or not meaningful_target:
                print("No meaningful descriptions found, returning zero similarity")
                return np.zeros((len(source_df), len(target_columns)))
            
            # Combine all descriptions for TF-IDF fitting
            all_descriptions = source_descriptions + target_descriptions
            
            # Filter out empty descriptions for TF-IDF (but keep indices aligned)
            non_empty_descriptions = [desc if desc.strip() else "empty_description" for desc in all_descriptions]
            
            # Create TF-IDF vectorizer
            vectorizer = TfidfVectorizer(
                stop_words='english',
                max_features=1000,
                ngram_range=(1, 2),
                min_df=1,
                token_pattern=r'\b\w+\b'  # Better token pattern
            )
            
            # Fit and transform all descriptions
            try:
                tfidf_matrix = vectorizer.fit_transform(non_empty_descriptions)
            except ValueError as e:
                print(f"TF-IDF vectorization failed: {e}")
                return np.zeros((len(source_df), len(target_columns)))
            
            # Split back into source and target matrices
            source_tfidf = tfidf_matrix[:len(source_descriptions)]
            target_tfidf = tfidf_matrix[len(source_descriptions):]
            
            # Compute cosine similarity
            similarity_matrix = cosine_similarity(source_tfidf, target_tfidf)
            
            # Set similarity to 0 for empty descriptions
            for i, desc in enumerate(source_descriptions):
                if not desc.strip():
                    similarity_matrix[i, :] = 0.0
            
            for j, desc in enumerate(target_descriptions):
                if not desc.strip():
                    similarity_matrix[:, j] = 0.0
            
            print(f"Description matching completed: {similarity_matrix.shape}")
            return similarity_matrix
            
        except Exception as e:
            print(f"Error in description matching: {e}")
            import traceback
            traceback.print_exc()
            return np.zeros((len(source_df), len(target_columns)))
    
    def _column_name_match(self, source_df: pd.DataFrame, target_columns: List[Dict]) -> np.ndarray:
        """
        Perform column name matching using multiple string similarity metrics
        
        Args:
            source_df: Source schema DataFrame
            target_columns: Target column data
            
        Returns:
            Similarity matrix (source_rows x target_cols)
        """
        def safe_get_string(value) -> str:
            """Safely convert value to string, handling None/NaN"""
            if value is None or pd.isna(value):
                return ""
            return str(value).strip()
        
        def normalize_name(name: str) -> str:
            """Normalize column name for comparison"""
            if not name:
                return ""
            # Convert to lowercase, remove underscores/spaces
            return re.sub(r'[_\s-]', '', name.lower())
        
        def string_similarity(str1: str, str2: str) -> float:
            """Compute combined string similarity score"""
            str1 = safe_get_string(str1)
            str2 = safe_get_string(str2)
            
            if not str1 or not str2:
                return 0.0
            
            # Exact match
            if str1 == str2:
                return 1.0
            
            # Normalized exact match
            norm1, norm2 = normalize_name(str1), normalize_name(str2)
            if norm1 == norm2 and norm1:  # Check norm1 is not empty
                return 0.95
            
            try:
                # Fuzzy matching scores
                ratio = fuzz.ratio(str1, str2) / 100.0
                partial_ratio = fuzz.partial_ratio(str1, str2) / 100.0
                token_sort_ratio = fuzz.token_sort_ratio(str1, str2) / 100.0
                
                # Combined score with weights
                combined_score = (
                    0.4 * ratio + 
                    0.3 * partial_ratio + 
                    0.3 * token_sort_ratio
                )
                
                return combined_score
                
            except Exception as e:
                print(f"Error in string similarity for '{str1}' vs '{str2}': {e}")
                return 0.0
        
        try:
            similarity_matrix = np.zeros((len(source_df), len(target_columns)))
            
            for i, (_, source_row) in enumerate(source_df.iterrows()):
                source_name = safe_get_string(source_row.get('column_name', ''))
                
                for j, target_col in enumerate(target_columns):
                    target_name = safe_get_string(target_col.get('column_name', ''))
                    
                    similarity = string_similarity(source_name, target_name)
                    similarity_matrix[i, j] = similarity
            
            print(f"Column name matching completed: {similarity_matrix.shape}")
            return similarity_matrix
            
        except Exception as e:
            print(f"Error in column name matching: {e}")
            import traceback
            traceback.print_exc()
            return np.zeros((len(source_df), len(target_columns)))
    
    def _combine_match_results(self, match_results: Dict[str, np.ndarray]) -> np.ndarray:
        """
        Combine results from different matching methods using weighted formula
        
        Args:
            match_results: Dictionary of method_name -> similarity_matrix
            
        Returns:
            Combined similarity matrix
        """
        weights = self.matcher_config['weights']
        
        # Initialize combined matrix
        shape = None
        for matrix in match_results.values():
            if shape is None:
                shape = matrix.shape
            elif matrix.shape != shape:
                print(f"Inconsistent matrix shapes found, using zeros for mismatched matrices")
        
        combined_matrix = np.zeros(shape)
        total_weight = 0.0
        
        # Weighted combination
        for method, matrix in match_results.items():
            weight = weights.get(method, 0.0)
            if weight > 0 and matrix.shape == shape:
                combined_matrix += weight * matrix
                total_weight += weight
        
        # Normalize by total weight
        if total_weight > 0:
            combined_matrix = combined_matrix / total_weight
        
        print(f"Combined results using weights: {weights}")
        return combined_matrix
    
    def _select_best_matches(self, similarity_matrix: np.ndarray, source_df: pd.DataFrame, 
                           target_columns: List[Dict], match_results: Dict[str, np.ndarray]) -> List[Dict]:
        """
        Select best matches for each source column above confidence threshold
        
        Args:
            similarity_matrix: Combined similarity matrix
            source_df: Source schema DataFrame
            target_columns: Target column data
            match_results: Individual method results for detailed reporting
            
        Returns:
            List of match result dictionaries
        """
        min_threshold = self.matcher_config['min_confidence_threshold']
        max_matches = self.matcher_config['max_matches_per_source']
        
        matches = []
        
        for i, (_, source_row) in enumerate(source_df.iterrows()):
            source_column = str(source_row.get('column_name', f'unknown_source_{i}')).strip()
            
            # Get similarities for this source column
            source_similarities = similarity_matrix[i, :]
            
            # Get top matches above threshold
            valid_indices = np.where(source_similarities >= min_threshold)[0]
            
            if len(valid_indices) > 0:
                # Sort by similarity score (descending)
                sorted_indices = valid_indices[np.argsort(source_similarities[valid_indices])[::-1]]
                
                # Take top N matches
                top_indices = sorted_indices[:max_matches]
                
                for rank, target_idx in enumerate(top_indices):
                    # Extract target column name using the correct mapping
                    target_column = str(target_columns[target_idx].get('column_name', f'unknown_target_{target_idx}')).strip()
                    confidence_score = source_similarities[target_idx]
                    
                    # Determine primary contributing method
                    method_scores = {}
                    for method, matrix in match_results.items():
                        method_scores[method] = matrix[i, target_idx]
                    
                    primary_method = max(method_scores, key=method_scores.get) if method_scores else 'unknown'
                    
                    match = {
                        'source_column': source_column,
                        'target_column': target_column,
                        'confidence_score': round(confidence_score, 4),
                        'match_method': primary_method,
                        'vector_score': round(match_results.get('vector_embedding', np.zeros_like(similarity_matrix))[i, target_idx], 4),
                        'synonym_score': round(match_results.get('synonym', np.zeros_like(similarity_matrix))[i, target_idx], 4),
                        'description_score': round(match_results.get('description', np.zeros_like(similarity_matrix))[i, target_idx], 4),
                        'name_score': round(match_results.get('column_name', np.zeros_like(similarity_matrix))[i, target_idx], 4),
                        'rank': rank + 1
                    }
                    
                    matches.append(match)
        
        print(f"Selected {len(matches)} matches above threshold {min_threshold}")
        return matches
    
    def match_schemas(self, source_df: pd.DataFrame, target_table_name: str, 
                     matching_methods: List[str] = ['all']) -> pd.DataFrame:
        """
        Main method to match source schema DataFrame with target schema in Neo4j
        
        Args:
            source_df: DataFrame with columns: column_name, description, applicable_domain, 
                      column_category, synonyms, sentence_transformer_embedding
            target_table_name: Name of target table in Neo4j
            matching_methods: List of methods to use ['all', 'vector', 'synonym', 'description', 'name']
            
        Returns:
            DataFrame with matching results
        """
        print(f"\nStarting schema matching for table: {target_table_name}")
        print(f"Source columns: {len(source_df)}")
        print(f"Methods: {matching_methods}")
        
        # Validate source DataFrame
        required_columns = ['column_name', 'description', 'applicable_domain', 
                          'column_category', 'synonyms', 'sentence_transformer_embedding']
        missing_columns = [col for col in required_columns if col not in source_df.columns]
        if missing_columns:
            raise ValueError(f"Source DataFrame missing required columns: {missing_columns}")
        
        # Get target columns from Neo4j
        target_columns = self._get_target_columns(target_table_name)
        
        # Determine which methods to run
        available_methods = ['vector_embedding', 'synonym', 'description', 'column_name']
        if 'all' in matching_methods:
            methods_to_run = available_methods
        else:
            # Map user-friendly names to internal names
            method_mapping = {
                'vector': 'vector_embedding',
                'synonym': 'synonym', 
                'description': 'description',
                'name': 'column_name'
            }
            methods_to_run = [method_mapping.get(m, m) for m in matching_methods if method_mapping.get(m, m) in available_methods]
        
        print(f"Running methods: {methods_to_run}")
        
        # Run individual matching methods
        match_results = {}
        
        if 'vector_embedding' in methods_to_run:
            print("\nRunning vector embedding matching...")
            match_results['vector_embedding'] = self._vector_embedding_match(source_df, target_columns)
        
        if 'synonym' in methods_to_run:
            print("\nRunning synonym matching...")
            match_results['synonym'] = self._synonym_match(source_df, target_columns)
        
        if 'description' in methods_to_run:
            print("\nRunning description matching...")
            match_results['description'] = self._description_match(source_df, target_columns)
        
        if 'column_name' in methods_to_run:
            print("\nRunning column name matching...")
            match_results['column_name'] = self._column_name_match(source_df, target_columns)
        
        # Combine results if multiple methods
        if len(match_results) > 1:
            print("\nCombining match results...")
            combined_similarity = self._combine_match_results(match_results)
        else:
            combined_similarity = list(match_results.values())[0]
        
        # Select best matches
        print("\nSelecting best matches...")
        best_matches = self._select_best_matches(combined_similarity, source_df, target_columns, match_results)
        
        # Convert to DataFrame
        if best_matches:
            result_df = pd.DataFrame(best_matches)
            print(f"\nMatching completed! Found {len(result_df)} matches")
            print(f"Average confidence: {result_df['confidence_score'].mean():.3f}")
            
            # Print highest matching pairs as formatted table
            print("\nHighest Matching Column Pairs:")
            print("-" * 50)
            print(f"{'Source Column':<25} {'Target Column':<25}")
            print("-" * 50)
            
            # Get the best match for each source column (highest confidence)
            best_matches_per_source = result_df.loc[result_df.groupby('source_column')['confidence_score'].idxmax()]
            
            # Sort by confidence score descending
            best_matches_per_source = best_matches_per_source.sort_values('confidence_score', ascending=False)
            
            for _, row in best_matches_per_source.iterrows():
                source_col = str(row['source_column'])[:24]  # Truncate if too long
                target_col = str(row['target_column'])[:24]  # Truncate if too long
                print(f"{source_col:<25} {target_col:<25}")
            
            print("-" * 50)
            
        else:
            print("\nNo matches found above confidence threshold")
            # Return empty DataFrame with correct columns
            result_df = pd.DataFrame(columns=[
                'source_column', 'target_column', 'confidence_score', 'match_method',
                'vector_score', 'synonym_score', 'description_score', 'name_score', 'rank'
            ])
        
        return result_df
    
    def close(self):
        """Close Neo4j connection"""
        if self.driver:
            self.driver.close()
            print("Neo4j connection closed")
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.close()


# Example usage and testing
if __name__ == "__main__":
    # Test the MatcherAgent
    from pathlib import Path
    
    # Initialize ConfigManager
    config_manager = ConfigManager()
    
    try:
        # Create sample source data with correct column names
        sample_source_data = {
            'column_name': ['user_id', 'email_address', 'first_name', 'created_date'],
            'description': ['Unique identifier for user', 'User email contact', 'User given name', 'Account creation timestamp'],
            'applicable_domain': ['identity', 'contact', 'personal', 'temporal'],
            'column_category': ['identifier', 'contact', 'personal', 'datetime'],
            'synonyms': ['["uid","user_identifier"]', '["email","mail"]', '["firstname","given_name"]', '["created_at","timestamp"]'],
            'sentence_transformer_embedding': [
                '[0.1, 0.2, 0.3]',  # Sample embeddings (in practice these would be longer)
                '[0.4, 0.5, 0.6]',
                '[0.7, 0.8, 0.9]',
                '[0.2, 0.4, 0.1]'
            ]
        }
        
        source_df = pd.DataFrame(sample_source_data)
        
        # Initialize MatcherAgent
        with MatcherAgent(config_manager) as matcher:
            # Perform matching
            results = matcher.match_schemas(
                source_df=source_df,
                target_table_name="users",  # Replace with actual table name
                matching_methods=['all']
            )
            
            print("\nMatching Results:")
            print(results.to_string(index=False))
            
    except Exception as e:
        print(f"Error testing MatcherAgent: {e}")