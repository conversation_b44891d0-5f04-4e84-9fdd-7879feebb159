import json
import pandas as pd
from tqdm import tqdm
from typing import List, Optional, Dict, Any
import time
import os
import sys
from pathlib import Path

project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from utils.llm_processor import AzureOpenAIProcessor
from utils.logger import Logger


class SchemaEnricherAgent:
    """Agent for enriching database schema with synonyms and applicable CPG domains"""
    
    def __init__(self, config_manager):
        """Initialize the Schema Enricher Agent"""
        self.llm_processor = AzureOpenAIProcessor(config_manager)
        self.logger = Logger.get_logger("schema_enricher_agent")
        
        # Add the new prompt methods to llm_processor
        self.llm_processor.synonym_generation_prompt = self.llm_processor._get_synonym_generation_prompt()
        self.llm_processor.domain_mapping_prompt = self.llm_processor._get_domain_mapping_prompt()
        
        # Comprehensive CPG Sub-Industries List
        self.cpg_domains = [
            "Food & Beverages",
            "Personal Care & Cosmetics", 
            "Household Products",
            "Health & Wellness",
            "Pet Care",
            "Tobacco & Alcohol",
            "Baby Care",
            "Oral Care",
            "Hair Care",
            "Skin Care",
            "Fragrances",
            "Cleaning Products",
            "Laundry & Fabric Care",
            "Paper Products",
            "Batteries & Power Solutions",
            "Electronics Accessories",
            "Automotive Care",
            "Garden & Outdoor",
            "Stationery & Office Supplies",
            "Seasonal & Holiday Products",
            "Health Supplements",
            "Over-the-Counter Pharmaceuticals",
            "Sports & Fitness",
            "Travel & Convenience",
            "Frozen Foods",
            "Snacks & Confectionery",
            "Beverages (Non-Alcoholic)",
            "Dairy Products",
            "Condiments & Sauces",
            "Baking & Cooking Ingredients"
        ]
        
        self.logger.info("Schema Enricher Agent initialized successfully")
    
    def enrich_schema(self, input_filepath: str, output_filepath: str = None) -> pd.DataFrame:
        """
        Main method to enrich schema with synonyms and applicable domains
        
        Args:
            input_filepath: Path to input CSV file
            output_filepath: Optional path to save enriched CSV
        
        Returns:
            DataFrame with enriched schema
        """
        try:
            self.logger.info(f"Starting schema enrichment for: {input_filepath}")
            
            # Load and validate CSV
            df = self._load_and_validate_csv(input_filepath)
            
            # Process dataframe with progress tracking
            enriched_df = self._process_dataframe(df)
            
            # Save results if output path provided
            if output_filepath:
                self._save_results(enriched_df, output_filepath)
                self.logger.info(f"Enriched schema saved to: {output_filepath}")
            
            self.logger.info(f"Schema enrichment completed. Processed {len(enriched_df)} rows")
            return enriched_df
            
        except Exception as e:
            self.logger.error(f"Failed to enrich schema: {str(e)}")
            raise
    
    def _load_and_validate_csv(self, filepath: str) -> pd.DataFrame:
        """Load CSV and validate required columns"""
        try:
            # Show loading progress
            print("Loading CSV file...")
            df = pd.read_csv(filepath)
            
            # Remove any unnamed columns that might exist
            unnamed_cols = [col for col in df.columns if col.startswith('Unnamed:')]
            if unnamed_cols:
                df = df.drop(columns=unnamed_cols)
                self.logger.info(f"Removed unnamed columns: {unnamed_cols}")
            
            # Validate required columns
            required_columns = ['column_name', 'description', 'column_category', 'table_type']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                raise ValueError(f"Missing required columns: {missing_columns}")
            
            # Remove any existing synonyms or applicable_domain columns
            columns_to_drop = ['synonyms', 'applicable_domain']
            df = df.drop(columns=[col for col in columns_to_drop if col in df.columns])
            
            # Clean up the dataframe - remove any empty rows and reset index
            df = df.dropna(subset=['column_name']).reset_index(drop=True)
            
            self.logger.info(f"Loaded CSV with {len(df)} rows and {len(df.columns)} columns")
            return df
            
        except Exception as e:
            self.logger.error(f"Failed to load CSV: {str(e)}")
            raise
    
    def _process_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Process dataframe with progress tracking"""
        try:
            # Create copies for processing
            enriched_df = df.copy()
            
            # Initialize new columns with empty lists
            enriched_df['synonyms'] = [[] for _ in range(len(df))]
            enriched_df['applicable_domain'] = [[] for _ in range(len(df))]
            
            # Process synonyms with progress bar
            print("\nGenerating synonyms...")
            tqdm.pandas(desc="Generating synonyms", unit="rows")
            
            for idx, row in tqdm(enriched_df.iterrows(), total=len(enriched_df), desc="Generating synonyms"):
                try:
                    synonyms = self._generate_synonyms(
                        row['column_name'], 
                        row['description'], 
                        row['column_category']
                    )
                    enriched_df.at[idx, 'synonyms'] = synonyms
                    
                    # Small delay to respect rate limits
                    time.sleep(0.1)
                    
                except Exception as e:
                    self.logger.warning(f"Failed to generate synonyms for row {idx}: {str(e)}")
                    enriched_df.at[idx, 'synonyms'] = []
            
            # Process applicable domains with progress bar
            print("\nMapping applicable domains...")
            
            for idx, row in tqdm(enriched_df.iterrows(), total=len(enriched_df), desc="Mapping domains"):
                try:
                    domains = self._identify_applicable_domains(
                        row['column_name'], 
                        row['description'], 
                        row['table_type']
                    )
                    enriched_df.at[idx, 'applicable_domain'] = domains
                    
                    # Small delay to respect rate limits
                    time.sleep(0.1)
                    
                except Exception as e:
                    self.logger.warning(f"Failed to map domains for row {idx}: {str(e)}")
                    enriched_df.at[idx, 'applicable_domain'] = []
            
            return enriched_df
            
        except Exception as e:
            self.logger.error(f"Failed to process dataframe: {str(e)}")
            raise
    
    def _generate_synonyms(self, column_name: str, description: str, category: str) -> List[str]:
        """Generate synonyms for a column using LLM"""
        try:
            # Create prompt using the template
            prompt = self.llm_processor.synonym_generation_prompt.format(
                column_name=column_name,
                description=description or "No description provided",
                category=category or "Unknown",
                table_context="CPG domain database schema"
            )
            
            # Get LLM response
            response = self.llm_processor._call_llm(prompt)
            
            # Parse JSON array response
            synonyms = self._parse_json_array_response(response)
            
            # Clean and validate synonyms
            synonyms = [syn.strip() for syn in synonyms if syn.strip() and syn.strip() != column_name]
            
            self.logger.debug(f"Generated {len(synonyms)} synonyms for column: {column_name}")
            return synonyms[:12]  # Allow up to 12 synonyms for comprehensive coverage
            
        except Exception as e:
            self.logger.error(f"Failed to generate synonyms for {column_name}: {str(e)}")
            return []
    
    def _identify_applicable_domains(self, column_name: str, description: str, table_type: str) -> List[str]:
        """Identify applicable CPG domains for a column using LLM"""
        try:
            # Create prompt using the template
            prompt = self.llm_processor.domain_mapping_prompt.format(
                column_name=column_name,
                description=description or "No description provided",
                category="",  # Not used in domain mapping
                table_type=table_type or "Unknown",
                cpg_domains="\n".join([f"- {domain}" for domain in self.cpg_domains])
            )
            
            # Get LLM response
            response = self.llm_processor._call_llm(prompt)
            
            # Parse JSON array response
            domains = self._parse_json_array_response(response)
            
            # Validate domains against predefined list
            validated_domains = self._validate_domains(domains)
            
            self.logger.debug(f"Mapped {len(validated_domains)} domains for column: {column_name}")
            return validated_domains[:8]  # Allow up to 8 domains for comprehensive coverage
            
        except Exception as e:
            self.logger.error(f"Failed to map domains for {column_name}: {str(e)}")
            return []
    
    def _parse_json_array_response(self, response: str) -> List[str]:
        """Parse LLM response to extract JSON array"""
        try:
            # Clean response
            response = response.strip()
            
            # Try to find JSON array in response
            start = response.find('[')
            end = response.rfind(']') + 1
            
            if start != -1 and end != 0:
                json_str = response[start:end]
                parsed = json.loads(json_str)
                
                if isinstance(parsed, list):
                    return [str(item) for item in parsed if item]
            
            # Fallback: try to parse entire response as JSON
            parsed = json.loads(response)
            if isinstance(parsed, list):
                return [str(item) for item in parsed if item]
            
            return []
            
        except json.JSONDecodeError:
            self.logger.warning(f"Failed to parse JSON from response: {response[:100]}...")
            return []
        except Exception as e:
            self.logger.warning(f"Unexpected error parsing response: {str(e)}")
            return []
    
    def _validate_domains(self, domains: List[str]) -> List[str]:
        """Validate domains against predefined CPG list"""
        validated = []
        
        for domain in domains:
            # Find exact match or close match
            if domain in self.cpg_domains:
                validated.append(domain)
            else:
                # Try to find close match (case insensitive)
                domain_lower = domain.lower()
                for valid_domain in self.cpg_domains:
                    if domain_lower == valid_domain.lower():
                        validated.append(valid_domain)
                        break
        
        return list(dict.fromkeys(validated))  # Remove duplicates while preserving order
    
    def _save_results(self, df: pd.DataFrame, output_path: str):
        """Save enriched dataframe to CSV with proper JSON formatting"""
        try:
            # Create output directory if it doesn't exist
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # Create a clean copy of the dataframe for saving
            df_to_save = df.copy()
            
            print("\nSaving results...")
            
            # Convert list columns to JSON strings with progress
            for idx in tqdm(range(len(df_to_save)), desc="Converting to JSON"):
                # Convert synonyms list to JSON string
                if isinstance(df_to_save.at[idx, 'synonyms'], list):
                    df_to_save.at[idx, 'synonyms'] = json.dumps(df_to_save.at[idx, 'synonyms'])
                else:
                    df_to_save.at[idx, 'synonyms'] = "[]"
                
                # Convert domains list to JSON string
                if isinstance(df_to_save.at[idx, 'applicable_domain'], list):
                    df_to_save.at[idx, 'applicable_domain'] = json.dumps(df_to_save.at[idx, 'applicable_domain'])
                else:
                    df_to_save.at[idx, 'applicable_domain'] = "[]"
            
            # Ensure only the expected columns are present in the correct order
            expected_columns = ['column_name', 'description', 'column_category', 'table_type', 'synonyms', 'applicable_domain']
            
            # Add any missing original columns that aren't in our expected list
            original_columns = [col for col in df.columns if col not in ['synonyms', 'applicable_domain']]
            final_columns = original_columns + ['synonyms', 'applicable_domain']
            
            # Select only the final columns to avoid unnamed columns
            df_final = df_to_save[final_columns].copy()
            
            # Save to CSV with explicit parameters to avoid unnamed columns
            df_final.to_csv(output_path, index=False, encoding='utf-8')
            
            self.logger.info(f"Successfully saved enriched schema with {len(df_final)} rows and {len(df_final.columns)} columns to: {output_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to save results: {str(e)}")
            raise
    
    def get_enrichment_summary(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Get summary statistics of enrichment process"""
        try:
            total_rows = len(df)
            
            # Count rows with synonyms
            rows_with_synonyms = sum(1 for _, row in df.iterrows() 
                                   if isinstance(row.get('synonyms'), list) and len(row['synonyms']) > 0)
            
            # Count rows with domains
            rows_with_domains = sum(1 for _, row in df.iterrows() 
                                  if isinstance(row.get('applicable_domain'), list) and len(row['applicable_domain']) > 0)
            
            # Average synonyms per row
            total_synonyms = sum(len(row.get('synonyms', [])) for _, row in df.iterrows() 
                               if isinstance(row.get('synonyms'), list))
            avg_synonyms = total_synonyms / total_rows if total_rows > 0 else 0
            
            # Average domains per row
            total_domains = sum(len(row.get('applicable_domain', [])) for _, row in df.iterrows() 
                              if isinstance(row.get('applicable_domain'), list))
            avg_domains = total_domains / total_rows if total_rows > 0 else 0
            
            summary = {
                'total_rows': total_rows,
                'rows_with_synonyms': rows_with_synonyms,
                'rows_with_domains': rows_with_domains,
                'synonym_coverage': round(rows_with_synonyms / total_rows * 100, 2) if total_rows > 0 else 0,
                'domain_coverage': round(rows_with_domains / total_rows * 100, 2) if total_rows > 0 else 0,
                'avg_synonyms_per_row': round(avg_synonyms, 2),
                'avg_domains_per_row': round(avg_domains, 2),
                'total_synonyms_generated': total_synonyms,
                'total_domains_mapped': total_domains
            }
            
            return summary
            
        except Exception as e:
            self.logger.error(f"Failed to generate summary: {str(e)}")
            return {}