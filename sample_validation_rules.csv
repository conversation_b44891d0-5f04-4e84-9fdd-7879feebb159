column_name,rule_type,rule_config,rule_description,is_active
customer_id,not_null,{},Customer ID cannot be null,true
age,range,"{""min"": 18, ""max"": 120}",Age must be between 18 and 120,true
email,regex,"{""pattern"": ""^[\\w\\.-]+@[\\w\\.-]+\\.[a-zA-Z]{2,}$""}",Valid email format required,true
phone,length,"{""min"": 10, ""max"": 15}",Phone number must be 10-15 characters,true
status,enum,"{""values"": [""active"", ""inactive"", ""pending""]}",Status must be one of the allowed values,true
salary,data_type,"{""type"": ""float""}",Salary must be a valid float,true
employee_id,unique,{},Employee ID must be unique,true
